name: <PERSON><PERSON> Deploy

on:
  workflow_call:
    inputs:
      spotInstance:
        description: 'Runs-on spot instance'
        required: false
        default: "true"
        type: string
      releaseEnvName:
        required: true
        type: string
      releaseEnvURL:
        required: true
        type: string
      imageTag:
        required: true
        type: string
      branchName:
        required: true
        type: string
      chartName:
        required: true
        type: string
      gkeRegion:
        required: true
        type: string
      gkeCluster:
        required: true
        type: string
      gkeDeployNamespace:
        required: true
        type: string
      workloadIdentityProvider:
        required: true
        type: string
      serviceAccount:
        required: true
        type: string
      migrationsEnabled:
        required: true
        type: string
      workingDirectory:
        required: true
        type: string

jobs:
  deploy-helm-to-gke:
    name: Deploy to Kubernetes
    environment: 
      name: ${{ inputs.releaseEnvName }}
      url: ${{ inputs.releaseEnvURL }}
    runs-on:
      - runs-on=k8s-deploy
      - cpu=2
      - ram=4
      - family=t3
      - image=ubuntu24-full-x64
      - spot=${{ inputs.spotInstance }}
      - extras=s3-cache
      - repo=be-microservices
      - action=helm-deploy-${{ inputs.releaseEnvName }}
      - private=true
      - tag=service-${{ inputs.chartName }}-${{ github.ref_name }}-${{ github.sha }}
    permissions:
        contents: 'read'
        id-token: 'write'
    steps:
    - uses: runs-on/action@v2
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 1
        sparse-checkout: |
          soenergy-charts
          soenergy-ruby-charts
          soenergy-fe-charts
        sparse-checkout-cone-mode: true

    - name: Setup gcloud auth
      uses: 'google-github-actions/auth@v1'
      with:
        workload_identity_provider: ${{ inputs.workloadIdentityProvider }}
        service_account: ${{ inputs.serviceAccount }}

    - name: Get GKE kubeconfig credentials
      uses: 'google-github-actions/get-gke-credentials@v1'
      with:
        cluster_name: ${{ inputs.gkeCluster }}
        location: ${{ inputs.gkeRegion }}

    - name: Get commit SHA
      run: |
        echo "ddGitCommitSha=$(git rev-parse HEAD)" >> $GITHUB_ENV
    
    - name: Deploy ${{ inputs.chartName }} to non-ondemand env (${{ inputs.releaseEnvName }})
      if: ${{ inputs.releaseEnvName != 'ondemand' }}
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        ./build-helm-dependencies.sh
        helm upgrade --install --wait --debug --timeout=25m --namespace=${{ inputs.gkeDeployNamespace }} --create-namespace \
        --values ${{ inputs.chartName }}/values-${{ inputs.releaseEnvName }}.yaml \
        --set global.image.tag=${{ inputs.imageTag }} \
        --set global.db.migrationsEnabled=${{ inputs.migrationsEnabled }} \
        --set global.datadog.commitSha=$ddGitCommitSha \
        ${{ inputs.chartName }}-${{ inputs.releaseEnvName }} \
        ${{ inputs.chartName }} --force

    # Careful with DB name that max to 63 characters can be used
    # DB cleanup hook deletes only databases ending with `*-so-*`
    - name: Deploy ondemand ${{ inputs.chartName }}
      if: ${{ inputs.releaseEnvName == 'ondemand' }}
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        ./build-helm-dependencies.sh
        helm upgrade --install --wait --atomic --debug --timeout=45m --namespace=${{ inputs.gkeDeployNamespace }} --create-namespace \
        --values ${{ inputs.chartName }}/values-${{ inputs.releaseEnvName }}.yaml \
        --set global.image.tag=${{ inputs.imageTag }} \
        --set global.ondemand.branchName=${{ inputs.branchName }} \
        --set db.overrideName=${{ inputs.chartName }}-${{ inputs.branchName }} \
        --set global.db.migrationsEnabled=${{ inputs.migrationsEnabled }} \
        --set global.datadog.commitSha=$ddGitCommitSha \
        ${{ inputs.chartName }}-${{ inputs.branchName }} \
        ${{ inputs.chartName }} --force

    - name: Slack Notification on failure ${{ inputs.releaseEnvName }}
      if: github.ref == 'refs/heads/main' && failure()
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_DEVELOP_ALERTS_WEBHOOK }}
        SLACK_COLOR: failure
        SLACK_LINK_NAMES: true
        SLACK_TITLE: "@here DEPLOYMENTS ARE FAILING FOR ${{ inputs.releaseEnvName }}, FIX YO STUFF"
