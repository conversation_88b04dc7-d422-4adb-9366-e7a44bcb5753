name: <PERSON><PERSON>ploy - Ruby

on:
  workflow_call:
    inputs:
      spotInstance:
        description: 'Runs-on spot instance'
        required: false
        default: "true"
        type: string
      releaseEnvName:
        required: true
        type: string
      releaseEnvURL:
        required: true
        type: string
      chartName:
        required: true
        type: string
      gkeRegion:
        required: true
        type: string
      gkeCluster:
        required: true
        type: string
      gkeDeployNamespace:
        required: true
        type: string
      workloadIdentityProvider:
        required: true
        type: string
      serviceAccount:
        required: true
        type: string

jobs:
  deploy-helm-to-gke:
    name: Deploy to Kubernetes
    environment: 
      name: ${{ inputs.releaseEnvName }}
      url: ${{ inputs.releaseEnvURL }}
    runs-on:
      - runs-on=k8s-deploy
      - cpu=2
      - ram=4
      - family=t3
      - image=ubuntu24-full-x64
      - spot=${{ inputs.spotInstance }}
      - extras=s3-cache
      - repo=be-microservices
      - action=helm-deploy-${{ inputs.releaseEnvName }}
      - gh-sha=${{ github.sha }}
      - private=true
    permissions:
        contents: 'read'
        id-token: 'write'
    steps:
    - uses: runs-on/action@v2
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 1
        sparse-checkout: |
          soenergy-ruby-charts
        sparse-checkout-cone-mode: true

    - name: Setup gcloud auth
      uses: 'google-github-actions/auth@v1'
      with:
        workload_identity_provider: ${{ inputs.workloadIdentityProvider }}
        service_account: ${{ inputs.serviceAccount }}

    - name: Get GKE kubeconfig credentials
      uses: 'google-github-actions/get-gke-credentials@v1'
      with:
        cluster_name: ${{ inputs.gkeCluster }}
        location: ${{ inputs.gkeRegion }}
    
    - name: Deploy ${{ inputs.chartName }} to non-ondemand env (${{ inputs.releaseEnvName }})
      if: ${{ inputs.releaseEnvName != 'ondemand' }}
      working-directory: soenergy-ruby-charts
      run: |
        ./build-helm-dependencies.sh
        helm upgrade --install --wait --timeout=15m --atomic --debug --namespace=${{ inputs.gkeDeployNamespace }} --create-namespace \
        --values ${{ inputs.chartName }}/values-${{ inputs.releaseEnvName }}.yaml \
        --set global.environmentName=${{ inputs.releaseEnvName }} \
        ${{ inputs.chartName }}-${{ inputs.releaseEnvName }} \
        ${{ inputs.chartName }} --force
