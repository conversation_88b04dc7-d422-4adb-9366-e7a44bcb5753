name: Build and Release Backend

on:
  push:
    branches:
      - main

jobs:
  branch-validation-checks:
    uses: ./.github/workflows/branch-validation-checks.yaml
  
  check-changed-services:
    uses: ./.github/workflows/changed-files.yaml
    needs:
      - branch-validation-checks

  find-merged-pr-labels:
    uses: ./.github/workflows/pr-label-finder.yaml
    needs:
      - branch-validation-checks

####################################
## STAGING/ PRODUCTION DEPLOYMENT ##
####################################

  prepare-release:
    if: success('check-changed-services')
    runs-on: ubuntu-latest
    needs:
      - check-changed-services
    outputs:
      releaseTag: ${{ steps.createTags.outputs.releaseTag }}
      jiraID: ${{ steps.createTags.outputs.jiraID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v4
      - name: Create release tag output
        id: createTags
        run: |
          echo "releaseTag=$(date +'%Y-%m-%d').${{ env.GITHUB_SHA_SHORT }}" >> "$GITHUB_OUTPUT"
          JIRA_ID=$(echo $GITHUB_HEAD_REF | cut -d "-" -f 1,2 | cut -d "_" -f 1 | awk '{print tolower($0)}')
          echo "jiraID=${JIRA_ID}" >> $GITHUB_OUTPUT

  build-release:
    if: success('prepare-release') && (needs.check-changed-services.outputs.kotlinChanged == 'true' || needs.check-changed-services.outputs.libraryChanged == 'true')
    needs:
      - prepare-release
    uses: ./.github/workflows/build.yaml
    secrets: inherit
    with:
      dockerImageTag: ${{ needs.prepare-release.outputs.releaseTag }}
      dockerRegistry: europe-west2-docker.pkg.dev/soe-artifact-registry/docker-release

  create-release:
    if: success('build-release')
    runs-on: ubuntu-latest
    needs:
      - prepare-release
      - build-release
    steps:
      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        with:
          prerelease: false
          name: ${{ needs.prepare-release.outputs.releaseTag }}
          tag_name: ${{ needs.prepare-release.outputs.releaseTag }}
          target_commitish: ${{ env.GITHUB_SHA }}
          generate_release_notes: true

  deploy-kotlin-staging-candidate:
    if: success('create-release') && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - check-changed-services
      - find-merged-pr-labels
      - prepare-release
      - build-release
      - create-release
    uses: ./.github/workflows/helm-deploy.yaml
    concurrency: deploy-be-staging-release-${{ github.ref_name }}
    secrets: inherit
    # The following variables are environment specific env vars
    # See: https://github.com/soenergy/be-microservices/settings/environments
    with:
      spotInstance: "false"
      releaseEnvName: staging
      releaseEnvURL: https://staging.soenergy.co/
      imageTag: ${{ needs.prepare-release.outputs.releaseTag }}
      branchName: ${{ needs.prepare-release.outputs.jiraID }}
      # migrationsEnabled: ${{ needs.check-changed-services.outputs.migrationsDetected }}
      migrationsEnabled: true
      chartName: nova-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-nonprod-core-application-staging
      gkeDeployNamespace: staging
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>
      workingDirectory: soenergy-charts
  
  check-successful-canary-staging:
    if: success('deploy-kotlin-staging-candidate') && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - find-merged-pr-labels
      - deploy-kotlin-staging-candidate
    uses: ./.github/workflows/check-canary-deployments.yaml
    concurrency: check-canary-staging-${{ github.ref_name }}
    secrets: inherit
    with:
      releaseEnvName: staging
      gkeRegion: europe-west2-a
      gkeCluster: soe-nonprod-core-application-staging
      gkeDeployNamespace: staging
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-staging-candidate-fe:
    if: needs.check-changed-services.outputs.feChanged == 'true' && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - check-changed-services
      - find-merged-pr-labels
    uses: ./.github/workflows/helm-deploy-fe.yaml
    concurrency: deploy-fe-staging
    secrets: inherit
    with:
      spotInstance: "false"
      releaseEnvName: staging
      releaseEnvURL: https://staging.soenergy.co/
      chartName: nova-fe-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-nonprod-core-application-staging
      gkeDeployNamespace: staging
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-staging-candidate-ruby:
    if: needs.check-changed-services.outputs.rubyChanged == 'true' && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - check-changed-services
      - find-merged-pr-labels
    uses: ./.github/workflows/helm-deploy-ruby.yaml
    concurrency: deploy-ruby-staging
    secrets: inherit
    with:
      spotInstance: "false"
      releaseEnvName: staging
      releaseEnvURL: https://staging.soenergy.co/
      chartName: nova-ruby-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-nonprod-core-application-staging
      gkeDeployNamespace: staging
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-kotlin-staging-candidate-terraform:
    if: success('create-release') && needs.check-changed-services.outputs.terraformChanged == 'true' && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - check-changed-services
      - find-merged-pr-labels
      - prepare-release
      - build-release
      - create-release
    uses: ./.github/workflows/terraform-deploy.yaml
    concurrency: deploy-terraform-kotlin-staging
    secrets: inherit
    with:
      service: kotlin
      releaseEnvName: staging
      releaseEnvURL: https://staging.soenergy.co/
      imageTag: ${{ needs.prepare-release.outputs.releaseTag }}
      dockerRegistry: europe-west2-docker.pkg.dev/soe-artifact-registry/docker-release
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  run-staging-regression-tests-qa-cypress:
    if: success('check-successful-canary-staging') && (!contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    needs:
      - find-merged-pr-labels
      - check-successful-canary-staging
    uses: ./.github/workflows/qa-regression.yaml
    concurrency: regression-staging-${{ github.ref_name }}
    secrets: inherit
    with:
      envName: staging

  run-staging-regression-tests-fe-nova:
    needs:
      - check-successful-canary-staging
    uses: ./.github/workflows/fe-nova-qa-e2e.yaml
    concurrency: regression-staging-fe-nova-${{ github.ref_name }}
    secrets: inherit

  export-hasura-metadata:
    name: Export hasura metadata
    runs-on:
      - runs-on=hasura
      - cpu=2
      - ram=4
      - family=t3
      - image=ubuntu24-full-x64
      - spot=false
      - extras=s3-cache
      - repo=be-microservices
      - action=hasura-export
      - gh-sha=${{ github.sha }}
      - private=true
    environment: staging
    needs:
      - find-merged-pr-labels
      - run-staging-regression-tests-qa-cypress
      - run-staging-regression-tests-fe-nova
    steps:
      - uses: runs-on/action@v2
      - name: hasura-cli install
        run: curl -L https://github.com/hasura/graphql-engine/raw/stable/cli/get.sh | bash
      - name: Init hasura-cli
        run: hasura init hasura --endpoint https://hasura.staging.soenergy.co --admin-secret ${{ secrets.HASURA_ADMIN_SECRET }} --skip-update-check
      - name: Export hasura-cli
        working-directory: hasura
        run: hasura metadata export --endpoint https://hasura.staging.soenergy.co --admin-secret ${{ secrets.HASURA_ADMIN_SECRET }} --skip-update-check
      - name: Upload hasura metadata artifact
        uses: actions/upload-artifact@v4
        with:
          name: hasura-metadata
          path: hasura

  production-approval:
    if: success('export-hasura-metadata') || (contains(needs.find-merged-pr-labels.outputs.labels, 'production-priority'))
    environment: production-approval
    needs:
      - find-merged-pr-labels
      - export-hasura-metadata
    runs-on: ubuntu-latest
    steps:
      - run: echo "Deployment to production approved."
      # - name: checkout active-workflow-checker binary
      #   uses: actions/checkout@v4
      #   with:
      #     sparse-checkout: .github/scripts/active-workflow-checker/active-workflow-checker
      # - name: Post-approval step
      #   run: .github/scripts/active-workflow-checker/active-workflow-checker
      #   env:
      #     GH_RUN_ID: ${{ github.run_id }}
      #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  deploy-production-release:
    if: success('production-approval')
    needs:
      - prepare-release
      - production-approval
    uses: ./.github/workflows/helm-deploy.yaml
    concurrency: deploy-be-production-release
    secrets: inherit
    # The following variables are environment specific env vars
    # See: https://github.com/soenergy/be-microservices/settings/environments
    with:
      spotInstance: "false"
      releaseEnvName: production
      releaseEnvURL: https://so.energy/
      imageTag: ${{ needs.prepare-release.outputs.releaseTag }}
      branchName: ${{ needs.prepare-release.outputs.jiraID }}
      migrationsEnabled: true
      chartName: nova-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-prod-core-application-gke
      gkeDeployNamespace: production
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>
      workingDirectory: soenergy-charts
  
  import-hasura-metadata:
    if: success('production-approval')
    name: Import hasura metadata
    runs-on:
      - runs-on=hasura
      - cpu=2
      - ram=4
      - family=t3
      - image=ubuntu24-full-x64
      - spot=false
      - extras=s3-cache
      - repo=be-microservices
      - action=hasura-import
      - gh-sha=${{ github.sha }}
      - private=true
    environment: production
    needs:
      - export-hasura-metadata
      - production-approval
    steps:
      - uses: runs-on/action@v2
      - name: hasura-cli install
        run: curl -L https://github.com/hasura/graphql-engine/raw/stable/cli/get.sh | bash
      - name: Download artifact of hasura metadata
        uses: actions/download-artifact@v4
        with:
          name: hasura-metadata
          path: hasura
      - name: Import hasura metadata
        working-directory: hasura
        run: |
          hasura metadata apply --endpoint https://hasura.so.energy --admin-secret ${{ secrets.HASURA_ADMIN_SECRET }} --skip-update-check

  check-successful-canary-production:
    if: success('deploy-production-release')
    needs:
      - deploy-production-release
    uses: ./.github/workflows/check-canary-deployments.yaml
    concurrency: check-canary-production-${{ github.ref_name }}
    secrets: inherit
    with:
      releaseEnvName: production
      gkeRegion: europe-west2-a
      gkeCluster: soe-prod-core-application-gke
      gkeDeployNamespace: production
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-production-release-fe:
    if: success('deploy-staging-candidate-fe')
    needs:
      - check-changed-services
      - deploy-staging-candidate-fe
    uses: ./.github/workflows/helm-deploy-fe.yaml
    concurrency: deploy-fe-production-release
    secrets: inherit
    with:
      spotInstance: "false"
      releaseEnvName: production
      releaseEnvURL: https://so.energy/
      chartName: nova-fe-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-prod-core-application-gke
      gkeDeployNamespace: production
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-production-release-ruby:
    if: success('deploy-staging-candidate-ruby')
    needs:
      - check-changed-services
      - deploy-staging-candidate-ruby
    uses: ./.github/workflows/helm-deploy-ruby.yaml
    concurrency: deploy-ruby-production-release
    secrets: inherit
    with:
      spotInstance: "false"
      releaseEnvName: production
      releaseEnvURL: https://so.energy/
      chartName: nova-ruby-parent
      gkeRegion: europe-west2-a
      gkeCluster: soe-prod-core-application-gke
      gkeDeployNamespace: production
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>

  deploy-kotlin-production-terraform:
    if: success('deploy-kotlin-staging-candidate-terraform')
    needs:
      - check-changed-services
      - prepare-release
      - build-release
      - create-release
      - deploy-kotlin-staging-candidate-terraform
    uses: ./.github/workflows/terraform-deploy.yaml
    concurrency: deploy-terraform-production-kotlin-release
    secrets: inherit
    with:
      service: kotlin
      releaseEnvName: production
      releaseEnvURL: https://so.energy/
      imageTag: ${{ needs.prepare-release.outputs.releaseTag }}
      dockerRegistry: europe-west2-docker.pkg.dev/soe-artifact-registry/docker-release
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>
