name: Regression Tests - Cypress

on:
  workflow_call:
    inputs:
      branchName:
        description: 'Branch name to run tests against if present'
        default: default
        required: false
        type: string
      envName:
        description: 'The environment that the qa-cypress tests will use to run against'
        required: true
        type: string

env:
  branchName: ${{ inputs.branchName }}
  SO_ENERGY_MOVE_OUT_SANDBOX_ACCOUNT_PASSWORD: ${{ secrets.SO_ENERGY_MOVE_OUT_SANDBOX_ACCOUNT_PASSWORD }}
  SO_ENERGY_MOVE_OUT_ACCOUNT_PASSWORD: ${{ secrets.SO_ENERGY_MOVE_OUT_ACCOUNT_PASSWORD }}
  SO_ENERGY_SPARK_ACCOUNT_PASSWORD: ${{ secrets.SO_ENERGY_SPARK_ACCOUNT_PASSWORD }}
  SO_ENERGY_ACCOUNT_EMAIL: ${{ secrets.SO_ENERGY_ACCOUNT_EMAIL }}
  SO_ENERGY_ACCOUNT_PASSWORD: ${{ secrets.SO_ENERGY_ACCOUNT_PASSWORD }}
  SO_ENERGY_CLOSED_ACCOUNT_EMAIL: ${{ secrets.SO_ENERGY_CLOSED_ACCOUNT_EMAIL }}
  SO_ENERGY_CLOSED_ACCOUNT_PASSWORD: ${{ secrets.SO_ENERGY_CLOSED_ACCOUNT_PASSWORD }}

jobs:
  trigger-regression-tests:
    runs-on:
      - runs-on=qa-testing
      - cpu=4
      - ram=8
      - family=c7i
      - image=ubuntu24-full-x64
      - extras=s3-cache
      - repo=be-microservices
      - service=qa-cypress
      - gh-sha=${{ github.sha }}
      - private=true
    outputs:
      test_outcome: ${{ steps.set-result.outputs.test_outcome }}
    steps:
      - uses: runs-on/action@v2
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: soenergy/qa-cypress
          ref: main
          token: ${{ secrets.SOE_PLATENG_SERVICEACCOUNT_TOKEN }}
          fetch-depth: 1

      - name: Cache npm dependencies
        uses: actions/cache@v4
        id: npm-cache
        with:
          path: |
            **/node_modules
            ~/.npm
            ~/.cache/Cypress
          key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Install dependencies
        run: npm ci
        if: steps.npm-cache.outputs.cache-hit != 'true'

      - name: Cypress run tests
        id: cypress-run
        uses: cypress-io/github-action@v6
        env:
          branchName: ${{ inputs.branchName }}
        with:
          command: npm run cy:run:${{ inputs.envName }}:regression:electron:testrail
          install: false

      - name: Set Result
        if: always()
        id: set-result
        run: |
          if [ "${{ steps.cypress-run.outcome }}" == "success" ]; then
            echo "test_outcome=success" >> $GITHUB_OUTPUT
          else
            echo "test_outcome=failure" >> $GITHUB_OUTPUT
          fi

      - name: Python setup
        if: always()
        uses: actions/setup-python@v3
        with:
          python-version: '3.x'

      # - name: TestRail CLI upload results
      #   if: always()
      #   env:
      #     TESTRAIL_HOST: ${{ secrets.TESTRAIL_HOST }}
      #     TESTRAIL_USERNAME: ${{ secrets.TESTRAIL_USERNAME }}
      #     TESTRAIL_PASSWORD: ${{ secrets.TESTRAIL_PASSWORD }}
      #   run: |
      #     pip install trcli
      #     junitparser merge --glob "results/e2e-*" "results/regression-report.xml"
      #     trcli -y \
      #       -h "$TESTRAIL_HOST" \
      #       -u "$TESTRAIL_USERNAME" \
      #       -p "$TESTRAIL_PASSWORD" \
      #       --project "QA-Cypress Automated Regression" \
      #       parse_junit \
      #       --title "Regression - ${{ inputs.envName }} $(date -u)" \
      #       --run-description "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
      #       -f "results/regression-report.xml"
      #     rm results/*

      - name: Save build folder
        if: success() || failure()
        uses: actions/upload-artifact@v4
        with:
          name: RegressionReports
          path: |
            cucumber-report.html
            cypress/screenshots

  notifications:
    runs-on: ubuntu-latest
    needs: [ trigger-regression-tests ]
    if: always()
    steps:
      - name: Notify Slack build Failure
        if: needs.trigger-regression-tests.outputs.test_outcome == 'failure'
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACKBOT_TOKEN_QA }}
        uses: voxmedia/github-action-slack-notify-build@v1
        with:
          channel_id: C08CP7Q4529
          status: FAILED
          color: danger
