name: Create OnDemand deployment

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review

jobs:
  draft-pull-request-check:
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Exit workflow
        id: exit-workflow
        run: |
          if [ ${{ github.event.pull_request.draft }} == 'true' ]; then
            echo "This is a draft pull request. Exiting workflow."
            exit 1
          else
            echo "This is not a draft pull request. Continuing workflow."
          fi

  branch-validation-checks:
    needs: draft-pull-request-check
    uses: ./.github/workflows/branch-validation-checks.yaml

  check-feature-flag-presence:
    uses: ./.github/workflows/feature-flag-validation.yaml
    needs:
      - branch-validation-checks

  check-env-presence:
    uses: ./.github/workflows/env-validation.yaml
    needs:
      - branch-validation-checks
    with:
      yamlParsingScript: ./.github/scripts/parse_yaml.sh

  prepare-ondemand:
    runs-on: ubuntu-latest
    needs:
      - check-feature-flag-presence
      - check-env-presence
    outputs:
      ondemandTag: ${{ steps.createTags.outputs.ondemandTag }}
      jiraID: ${{ steps.createTags.outputs.jiraID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          sparse-checkout: |
            .github
          sparse-checkout-cone-mode: true

      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v4

      - name: Create ondemand tag output
        id: createTags
        run: |
          JIRA_ID=$(echo $GITHUB_HEAD_REF | cut -d "-" -f 1,2 | cut -d "_" -f 1 | awk '{print tolower($0)}')
          echo "jiraID=${JIRA_ID}" >> $GITHUB_OUTPUT
          echo "ondemandTag=${JIRA_ID}-${{ env.GITHUB_SHA_SHORT }}" >> "$GITHUB_OUTPUT"

  identify-affected-apps:
    if: ('! github.event.pull_request.draft')
    runs-on: ubuntu-latest
    needs:
      - check-feature-flag-presence
      - prepare-ondemand
    outputs:
      detectedChanges: ${{ steps.combine-detected.outputs.finalChanges }}
      migrationsDetected: ${{ steps.change-check.outputs.migrations }}
      hasChanges: ${{ steps.combine-detected.outputs.hasChanges }}
      acServiceChanges: ${{ steps.changed-ac-services.outputs.only_changed }}
      libraryChangesOnly: ${{ steps.library-changes.outputs.only_changed }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            kotlin-services
            soenergy-charts
          sparse-checkout-cone-mode: true

      - name: Get changed services
        id: changed-services
        uses: tj-actions/changed-files@v37
        with:
          dir_names: true
          json: true
          files: |
            kotlin-services/services/**
          # Ignore services that do not have a helm chart!
          files_ignore: |
            kotlin-services/services/be-ac-junifer/**
            soenergy-charts/be-ac-esg/**
            kotlin-services/services/be-junifer-sync/**
            kotlin-services/services/be-meter-readings/**
            kotlin-services/services/be-mock-junifer/**

      - name: Get changed AC services
        id: changed-ac-services
        uses: tj-actions/changed-files@v37
        with:
          dir_names: true
          json: true
          files: |
            kotlin-services/services/be-ac-esg/**
            kotlin-services/services/be-ac-junifer/**

      - name: Get changed helm charts
        id: changed-charts
        uses: tj-actions/changed-files@v37
        with:
          dir_names: true
          json: true
          files: |
            soenergy-charts/**
          # Ignore services that do not have a helm chart!
          files_ignore: |
            soenergy-charts/README.md
            soenergy-charts/*.sh
            soenergy-charts/nova-parent/**
            soenergy-charts/0-commons/**
            soenergy-charts/nginx-junifer/**
            soenergy-charts/nginx-nova/**
            soenergy-charts/do-monitoring/**
            soenergy-charts/do-freshdesk-cache/**
            soenergy-charts/be-ac-junifer/**
            soenergy-charts/be-ac-esg/**

      - name: Get changed kotlin library files
        id: library-changes
        uses: tj-actions/changed-files@v37
        with:
          files: |
            kotlin-services/libraries/**

      - name: Combine detected changes
        id: combine-detected
        run: |
          # Process changed services with special handling for nested services
          export CHANGED_SERVICES=$(echo ${{ steps.changed-services.outputs.all_modified_files }} | jq '
            .[] | 
            split("/") | 
            if length >= 4 then
              # Check for nested structure
              if .[3] == "service-v2" then
                .[2] + "-v2"
              elif .[3] == "service" then
                .[2]
              else
                .[2]
              end
            else
              # Default to original behavior
              .[2]
            end' | jq --compact-output --slurp '.')
          
          export CHANGED_CHARTS=$(echo ${{ steps.changed-charts.outputs.all_modified_files }} | jq '.[] | split("/") | .[1]' | jq --compact-output --slurp .)
          export MERGED_UNIQUE_CHANGES=$(echo -e "$CHANGED_SERVICES\n$CHANGED_CHARTS" | jq --compact-output --slurp 'add | unique')
          echo "Detected changed services: $CHANGED_SERVICES"
          echo "Detected changed charts: $CHANGED_CHARTS"
          echo "Final list of detected changes: $MERGED_UNIQUE_CHANGES"
          echo "finalChanges=$MERGED_UNIQUE_CHANGES" >> "$GITHUB_OUTPUT"
          
          # Check if there are any changes and set hasChanges output
          CHANGES_COUNT=$(echo $MERGED_UNIQUE_CHANGES | jq 'length')
          if [ "$CHANGES_COUNT" -gt 0 ]; then
            echo "hasChanges=true" >> "$GITHUB_OUTPUT"
          else
            echo "hasChanges=false" >> "$GITHUB_OUTPUT"
          fi

      - name: check for dir changes
        uses: dorny/paths-filter@v3.0.1
        id: change-check
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          filters: |
            migrations:
              - '**/*.sql'

  build-ondemand:
    if: (needs.identify-affected-apps.outputs.hasChanges == 'true' || needs.identify-affected-apps.outputs.acServiceChanges == 'true' || needs.identify-affected-apps.outputs.libraryChangesOnly == 'true')
    needs:
      - check-feature-flag-presence
      - identify-affected-apps
      - prepare-ondemand
    uses: ./.github/workflows/build.yaml
    secrets: inherit
    with:
      dockerImageTag: ${{ needs.prepare-ondemand.outputs.ondemandTag }}
      dockerRegistry: europe-west2-docker.pkg.dev/soe-artifact-registry/docker-release
      ondemandBuild: 'true'

  deploy-ondemand-services:
    if: ('! github.event.pull_request.draft') && (needs.identify-affected-apps.outputs.acServiceChanges == 'false' || needs.identify-affected-apps.outputs.hasChanges == 'false' || needs.identify-affected-apps.outputs.libraryChangesOnly == 'false')
    needs:
      - check-feature-flag-presence
      - prepare-ondemand
      - identify-affected-apps
      - build-ondemand
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.identify-affected-apps.outputs.detectedChanges) }}
    concurrency: deploy-ond-${{ needs.prepare-ondemand.outputs.jiraID }}-${{ matrix.service }}-${{ github.ref_name }}
    uses: ./.github/workflows/helm-deploy.yaml
    secrets: inherit
    with:
      spotInstance: "false"
      releaseEnvName: ondemand
      releaseEnvURL: https://staging.soenergy.co/${{ matrix.service }}/${{ needs.prepare-ondemand.outputs.jiraID }}
      imageTag: ${{ needs.prepare-ondemand.outputs.ondemandTag }}
      branchName: ${{ needs.prepare-ondemand.outputs.jiraID }}
      migrationsEnabled: ${{ needs.identify-affected-apps.outputs.migrationsDetected }}
      chartName: ${{ matrix.service }}
      gkeRegion: europe-west2-a
      gkeCluster: soe-nonprod-core-application-staging
      gkeDeployNamespace: staging
      workloadIdentityProvider: projects/*************/locations/global/workloadIdentityPools/github/providers/github
      serviceAccount: <EMAIL>
      workingDirectory: soenergy-charts

  run-ondemand-regression-tests:
    needs:
      - prepare-ondemand
      - deploy-ondemand-services
    uses: ./.github/workflows/qa-regression.yaml
    concurrency: regression-staging--${{ needs.prepare-ondemand.outputs.jiraID }}-${{ github.ref_name }}
    secrets: inherit
    with:
      branchName: ${{ needs.prepare-ondemand.outputs.jiraID }}
      envName: staging

  run-ondemand-regression-tests-fe-nova:
    needs:
      - prepare-ondemand
      - deploy-ondemand-services
    uses: ./.github/workflows/fe-nova-qa-e2e.yaml
    concurrency: regression-staging-fe-nova-${{ needs.prepare-ondemand.outputs.jiraID }}-${{ github.ref_name }}
    secrets: inherit

  # Branch protection check job
  branch-status-check:
    if: always()
    runs-on: ubuntu-latest
    needs: 
      - identify-affected-apps
      - deploy-ondemand-services
      - check-feature-flag-presence
      - run-ondemand-regression-tests
      - run-ondemand-regression-tests-fe-nova
    steps:
      - name: Check deployment status
        run: |
          # No changes detected - this is a valid success case
          if [[ "${{ needs.identify-affected-apps.outputs.hasChanges }}" == "false" ]]; then
            echo "No application changes detected - marking workflow as successful"
            exit 0
          fi

          # Pass if AC changes are detected, and the deploy job is skipped
          if "${{ needs.identify-affected-apps.outputs.acServiceChanges }}" == "true" && "${{ needs.deploy-ondemand-services.result }}" != "success" ]]; then
            echo "Deployment not required for detected changes"
            exit 0
          fi

          # Fail if deploy job failed but changes were detected
          if [[ "${{ needs.identify-affected-apps.outputs.acServiceChanges }}" == "false" && ("${{ needs.identify-affected-apps.outputs.hasChanges }}" == "true" && "${{ needs.deploy-ondemand-services.result }}" != "success") ]]; then
            echo "Deployment failed for detected changes"
            exit 1
          fi

          # Fail if deploy job succeeded but QA testing fails
          if [[ "${{ needs.identify-affected-apps.outputs.acServiceChanges }}" == "false" && ("${{ needs.identify-affected-apps.outputs.hasChanges }}" == "true" && "${{ needs.deploy-ondemand-services.result }}" == "success") && ("${{ needs.run-ondemand-regression-tests.result }}" != "success" || "${{ needs.run-ondemand-regression-tests-fe-nova.result }}" != "success") ]]; then
            echo "QA testing failed after deployment"
            exit 1
          fi

          echo "On-Demand process completed successfully"