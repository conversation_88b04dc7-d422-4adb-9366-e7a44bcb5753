plugins {
    id("energy.so.be-commons-conventions-library")
    id("energy.so.conventions.spring")
}

dependencies {
    implementation(libs.google.cloud.pubsub)

    implementation(project(":be-commons-queues"))
    implementation(project(":be-commons-logging"))

    implementation("org.springframework.boot:spring-boot-starter-web")
}

tasks.named<org.sonarqube.gradle.SonarTask>("sonarqube") {
    enabled = false
}