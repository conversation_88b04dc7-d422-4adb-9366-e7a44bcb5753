# Pub/Sub Dev Tools

This project contains some tools to help manage pub/sub.

## Sending a message to a Pub/Sub Topic

When this tool runs, it starts up a HTTP server. You can send a message to a specific topic via:

`localhost:8080/send?topic=MyTopic&message=MessageContent&eventType=CREATED`

Using the application default credentials installed on the machine running the tool, this would send `MessageContent` to
the `MyTopic` topic, with the event type set to `CREATED`.

You can also use the Pub/Sub emulator by setting the `useEmulator` variable:

`localhost:8080/send?useEmulator=true...`

When this mode is enabled credentials are disabled. The tool will use `127.0.0.1:8085` for the host. This can be
overriden by
setting the `PUBSUB_EMULATOR_HOST` environment variable. See the README of `be-commons-queues` for more information on
how to run the pub/sub emulator.

If you are using the pub/sub emulator, you can use the following endpoint to create a topic and subscription on the
emulator:

`localhost:8080/create-topic-and-sub?topic=TOPIC&sub=SUB`

Which would create a topic called `TOPIC` with a subscription called `SUB`.