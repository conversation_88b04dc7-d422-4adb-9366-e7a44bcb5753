plugins {
    id("energy.so.be-commons-conventions-library")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":be-commons-httpclient"))
    implementation(project(":be-commons-json"))
    implementation(project(":be-commons-logging"))
    implementation(libs.ktor.client.cio)
    implementation(libs.ktor.client.content.negotiation)
    implementation(libs.ktor.client.auth)
    implementation(libs.ktor.serialization.kotlinx)
    implementation(libs.ktor.client.logging)
    implementation(libs.tika)
    implementation(project(mapOf("path" to ":be-commons-exceptions")))

    testImplementation(libs.ktor.client.mock)
    implementation(kotlin("reflect"))
    testFixturesImplementation(project(":be-commons-json"))
    testFixturesImplementation(libs.ktor.client.core)
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            listOf(
                "src/main/kotlin/energy/so/freshdesk/client/v1/dto/**",
            )
        )
    }
}
