package client

import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.http.ClientHttpException
import energy.so.freshdesk.client.FreshdeskPrecannedData.API_KEY
import energy.so.freshdesk.client.FreshdeskPrecannedData.HOST
import energy.so.freshdesk.client.FreshdeskPrecannedData.JOB_ID
import energy.so.freshdesk.client.FreshdeskPrecannedData.LONG_1
import energy.so.freshdesk.client.FreshdeskPrecannedData.PASSWORD
import energy.so.freshdesk.client.FreshdeskPrecannedData.TICKET_ID
import energy.so.freshdesk.client.FreshdeskPrecannedData.TIMEOUT
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshDeskCreateContactRequest
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshDeskCreateContactResponse
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskBulkUpdateTicketsRequestDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskCreateNoteRequestDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskCreateTicketRequestDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskUpdateTicketRequestDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.testSendReplyRequest
import energy.so.freshdesk.client.v1.config.FreshDeskConfig
import energy.so.freshdesk.client.v1.impl.DefaultFreshDeskClient
import energy.so.freshdesk.client.v1.impl.invoke
import engine.FreshDeskMockEngineFactory
import engine.FreshDeskMockErrorCaseEngineFactory
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.common.runBlocking
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.longs.shouldBeGreaterThanOrEqual
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.ktor.http.HttpStatusCode
import kotlin.system.measureTimeMillis

class DefaultFreshDeskClientTest : BehaviorSpec({
    val sut = runBlocking {
        DefaultFreshDeskClient(
            FreshDeskConfig(
                HOST,
                API_KEY,
                PASSWORD,
                TIMEOUT,
                TIMEOUT,
                LONG_1,
                LONG_1,
                LONG_1,
                LONG_1
            ),
            FreshDeskMockEngineFactory()
        )
    }

    val sutBadRequest = runBlocking {
        DefaultFreshDeskClient(
            FreshDeskConfig(
                HOST,
                API_KEY,
                PASSWORD,
                TIMEOUT,
                TIMEOUT,
                LONG_1,
                TIMEOUT,
                LONG_1,
                LONG_1,
            ),
            FreshDeskMockErrorCaseEngineFactory(HttpStatusCode.BadRequest, LONG_1)
        )
    }

    val sutInternalServerError = runBlocking {
        DefaultFreshDeskClient(
            FreshDeskConfig(
                HOST,
                API_KEY,
                PASSWORD,
                TIMEOUT,
                TIMEOUT,
                LONG_1,
                LONG_1,
                LONG_1,
                LONG_1
            ),
            FreshDeskMockErrorCaseEngineFactory(HttpStatusCode.InternalServerError, 2)
        )
    }

    val mockTooManyRequestsEngineFactory = FreshDeskMockErrorCaseEngineFactory(HttpStatusCode.TooManyRequests)
    val sutTooManyRequests = runBlocking {
        DefaultFreshDeskClient(
            FreshDeskConfig(
                HOST,
                API_KEY,
                PASSWORD,
                TIMEOUT,
                TIMEOUT,
                LONG_1,
                LONG_1,
                LONG_1,
                LONG_1,
                2
            ),
            mockTooManyRequestsEngineFactory
        )
    }

    given("::createTicket") {
        `when`("post createTicket") {
            then("response is ok on successful call") {
                sut.createTicket(freshdeskCreateTicketRequestDto).status shouldBe HttpStatusCode.OK
            }
            then("throw error on bad request failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.createTicket(freshdeskCreateTicketRequestDto)
                    }

                    exception.status shouldBe HttpStatusCode.BadRequest
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }
            then("throw error on internal server error on failed call") {
                shouldThrow<ClientHttpException> {
                    sutInternalServerError.createTicket(freshdeskCreateTicketRequestDto).status shouldBe HttpStatusCode.InternalServerError
                }
            }
            then("retry request on too many requests on failed call") {
                shouldThrow<ClientHttpException> {
                    sutTooManyRequests.createTicket(freshdeskCreateTicketRequestDto).status shouldBe HttpStatusCode.TooManyRequests
                }

                mockTooManyRequestsEngineFactory.countRequests() shouldBe 3
            }
        }
    }

    given("::createTicketWithAttachment") {
        `when`("post createTicketWithAttachment") {
            then("response is ok on successful call") {
                sut.createTicketWithAttachment(freshdeskCreateTicketRequestDto)
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.createTicketWithAttachment(freshdeskCreateTicketRequestDto)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.createTicketWithAttachment(freshdeskCreateTicketRequestDto).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.createTicketWithAttachment(freshdeskCreateTicketRequestDto).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::createNoteWithAttachment") {
        `when`("post createNoteWithAttachment") {
            then("response is ok on successful call") {
                sut.createNoteWithAttachment(freshdeskCreateNoteRequestDto, TICKET_ID).status shouldBe HttpStatusCode.OK
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.createNoteWithAttachment(freshdeskCreateNoteRequestDto, TICKET_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.createNoteWithAttachment(
                            freshdeskCreateNoteRequestDto,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.createNoteWithAttachment(
                            freshdeskCreateNoteRequestDto,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::createNote") {

        val request = freshdeskCreateNoteRequestDto.copy(attachments = null)

        `when`("post createNote") {
            then("response is ok on successful call") {
                sut.createNote(request, TICKET_ID).status shouldBe HttpStatusCode.OK
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.createNote(request, TICKET_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.createNote(
                            request,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.createNote(
                            request,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::updateTicketWithAttachment") {
        `when`("put updateTicketWithAttachment") {
            then("response is ok on successful call") {
                sut.updateTicketWithAttachment(
                    freshdeskUpdateTicketRequestDto,
                    TICKET_ID
                ).status shouldBe HttpStatusCode.OK
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.updateTicketWithAttachment(freshdeskUpdateTicketRequestDto, TICKET_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.updateTicketWithAttachment(
                            freshdeskUpdateTicketRequestDto,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.updateTicketWithAttachment(
                            freshdeskUpdateTicketRequestDto,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::updateTicket") {

        val request = freshdeskUpdateTicketRequestDto.copy(attachments = null)

        `when`("put updateTicket") {

            then("response is ok on successful call") {
                sut.updateTicket(
                    request,
                    TICKET_ID
                ).status shouldBe HttpStatusCode.OK
            }

            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.updateTicket(request, TICKET_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.updateTicket(
                            request,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.updateTicket(
                            request,
                            TICKET_ID
                        ).status shouldBe HttpStatusCode.InternalServerError
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::bulkUpdateTickets") {
        `when`("post bulkUpdateTickets") {
            then("response is ok on successful call") {
                sut.bulkUpdateTickets(freshdeskBulkUpdateTicketsRequestDto)
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.bulkUpdateTickets(freshdeskBulkUpdateTicketsRequestDto)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.bulkUpdateTickets(freshdeskBulkUpdateTicketsRequestDto)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.bulkUpdateTickets(freshdeskBulkUpdateTicketsRequestDto)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::viewJob") {
        `when`("get viewJob") {
            then("response is ok on successful call") {
                sut.viewJob(JOB_ID)
            }
            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.viewJob(JOB_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.viewJob(JOB_ID)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.viewJob(JOB_ID)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::sendReplyWithoutAttachments") {
        `when`("a reply is sent") {

            then("response is ok on successful call") {
                sut.sendReplyWithoutAttachments(testSendReplyRequest, TICKET_ID)
            }

            then("throw error on bad request on failed call") {
                val elapsedTime = measureTimeMillis {
                    val exception = shouldThrow<ClientHttpException> {
                        sutBadRequest.sendReplyWithoutAttachments(testSendReplyRequest, TICKET_ID)
                    }

                    exception.run {
                        status shouldBe HttpStatusCode.BadRequest
                        message shouldNotBe null
                        errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                        errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                    }
                }

                elapsedTime shouldBeGreaterThanOrEqual TIMEOUT
            }

            then("throw error on internal server error on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutInternalServerError.sendReplyWithoutAttachments(testSendReplyRequest, TICKET_ID)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.InternalServerError
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_SERVER
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }

            then("throw error on too many requests on failed call") {
                val exception =
                    shouldThrow<ClientHttpException> {
                        sutTooManyRequests.sendReplyWithoutAttachments(testSendReplyRequest, TICKET_ID)
                    }

                exception.run {
                    status shouldBe HttpStatusCode.TooManyRequests
                    message shouldNotBe null
                    errorCategory shouldBe ErrorCategories.HTTP_CLIENT
                    errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                }
            }
        }
    }

    given("::createContact") {

        `when`("a create contact") {
            then("it should create a new contact") {
                val response = sut.createContact(freshDeskCreateContactRequest)

                assertSoftly {
                    response.requesterId shouldBe freshDeskCreateContactResponse.requesterId
                    response.name shouldBe freshDeskCreateContactResponse.name
                    response.active shouldBe freshDeskCreateContactResponse.active
                    response.deleted shouldBe freshDeskCreateContactResponse.deleted
                    response.createdAt shouldBe freshDeskCreateContactResponse.createdAt
                    response.updatedAt shouldBe freshDeskCreateContactResponse.updatedAt
                }
            }
        }

    }
})
