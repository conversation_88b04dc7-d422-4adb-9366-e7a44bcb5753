package engine

import energy.so.freshdesk.client.FreshdeskPrecannedData.JOB_ID
import energy.so.freshdesk.client.FreshdeskPrecannedData.TICKET_ID
import energy.so.freshdesk.client.v1.impl.RATE_LIMIT_HEADER
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.engine.HttpClientEngineFactory
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockEngineConfig
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf

class FreshDeskMockErrorCaseEngineFactory(statusCode: HttpStatusCode, rateLimitHeader: Long? = null) :
    HttpClientEngineFactory<MockEngineConfig> {

    private val basePathSegments = listOf("", "api", "v2")

    /**
     * Mock engine preconfigured to return well-known data from the precanned data object as if we are calling the
     * freshdesk API. Ideally we'd run against a test container that behaved like pipedrive but such as thing doesn't
     * exist.
     */
    private val freshDeskMockErrorEngine: MockEngine = MockEngine { request ->
        when (request.method to request.url.pathSegments) {
            HttpMethod.Post to basePathSegments + "tickets",
            HttpMethod.Put to basePathSegments + "tickets" + TICKET_ID,
            HttpMethod.Post to basePathSegments + "tickets" + TICKET_ID + "notes",
            HttpMethod.Post to basePathSegments + "tickets" + "bulk_update",
            HttpMethod.Get to basePathSegments + "jobs" + JOB_ID,
            HttpMethod.Post to basePathSegments + "tickets" + TICKET_ID + "reply",
            -> mockResponse(
                statusCode,
                rateLimitHeader
            )

            else -> respond(content = "not found!", status = HttpStatusCode.NotFound)
        }
    }

    override fun create(block: MockEngineConfig.() -> Unit): HttpClientEngine = freshDeskMockErrorEngine

    fun countRequests(): Int {
        return freshDeskMockErrorEngine.requestHistory.size
    }

    private fun MockRequestHandleScope.mockResponse(
        statusCode: HttpStatusCode,
        rateLimitHeader: Long?,
    ) =
        respond(
            content = statusCode.description,
            status = statusCode,
            headers = rateLimitHeader?.let { headersOf(name = RATE_LIMIT_HEADER, value = it.toString()) } ?: headersOf()
        )
}
