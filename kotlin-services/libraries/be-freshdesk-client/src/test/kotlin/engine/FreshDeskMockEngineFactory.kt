package engine

import energy.so.freshdesk.client.FreshdeskPrecannedData.JOB_ID
import energy.so.freshdesk.client.FreshdeskPrecannedData.TICKET_ID
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshDeskCreateContactResponse
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskBulkUpdateTicketsResponseDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskCreateTicketResponseDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.freshdeskJobResponseDto
import energy.so.freshdesk.client.FreshdeskPrecannedData.testSendReplyResponse
import energy.so.freshdesk.client.v1.dto.FreshDeskCreateContactResponse
import energy.so.freshdesk.client.v1.dto.FreshdeskBulkUpdateTicketsResponseDto
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateTicketResponseDto
import energy.so.freshdesk.client.v1.dto.FreshdeskJobResponseDto
import energy.so.freshdesk.client.v1.dto.FreshdeskSendReplyResponse
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.engine.HttpClientEngineFactory
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockEngineConfig
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class FreshDeskMockEngineFactory : HttpClientEngineFactory<MockEngineConfig> {

    private val basePathSegments = listOf("", "api", "v2")

    /**
     * Mock engine preconfigured to return well-known data from the precanned data object as if we are calling the
     * freshdesk API. Ideally we'd run against a test container that behaved like pipedrive but such as thing doesn't
     * exist.
     */

    private val freshDeskMockEngine: HttpClientEngine = MockEngine { request ->
        when (request.method to request.url.pathSegments) {
            HttpMethod.Post to basePathSegments + "tickets" -> createTicket()
            HttpMethod.Put to basePathSegments + "tickets" + TICKET_ID -> createTicket()
            HttpMethod.Post to basePathSegments + "tickets" + TICKET_ID + "notes" -> createNote()
            HttpMethod.Post to basePathSegments + "tickets" + "bulk_update" -> createBulkUpdate()
            HttpMethod.Get to basePathSegments + "jobs" + JOB_ID -> viewJob()
            HttpMethod.Post to basePathSegments + "tickets" + TICKET_ID + "reply" -> sendReply()
            HttpMethod.Post to basePathSegments + "contacts" -> createContact()

            else -> respond(content = "not found!", status = HttpStatusCode.NotFound)
        }
    }

    override fun create(block: MockEngineConfig.() -> Unit): HttpClientEngine = freshDeskMockEngine

    private fun MockRequestHandleScope.createContact() =
        respond(
            content = ByteReadChannel(
                Json.encodeToString(
                    FreshDeskCreateContactResponse.serializer(),
                    freshDeskCreateContactResponse
                )
            ),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )

    private fun MockRequestHandleScope.createTicket() =
        respond(
            content = ByteReadChannel(
                Json.encodeToString(
                    FreshdeskCreateTicketResponseDto.serializer(),
                    freshdeskCreateTicketResponseDto
                )
            ),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )

    private fun MockRequestHandleScope.createNote() =
        respond(
            content = "content",
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )

    private fun MockRequestHandleScope.createBulkUpdate() =
        respond(
            content = ByteReadChannel(
                Json.encodeToString(
                    FreshdeskBulkUpdateTicketsResponseDto.serializer(),
                    freshdeskBulkUpdateTicketsResponseDto
                )
            ),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )

    private fun MockRequestHandleScope.viewJob() =
        respond(
            content = ByteReadChannel(
                Json.encodeToString(
                    FreshdeskJobResponseDto.serializer(),
                    freshdeskJobResponseDto
                )
            ),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )

    private fun MockRequestHandleScope.sendReply() =
        respond(
            content = ByteReadChannel(
                Json.encodeToString(
                    FreshdeskSendReplyResponse.serializer(),
                    testSendReplyResponse
                )
            ),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json")
        )
}
