package energy.so.dotdigital

import energy.so.dotdigital.config.DotDigitalConfig
import energy.so.dotdigital.models.ChannelProperty
import energy.so.dotdigital.models.ChannelStatus
import energy.so.dotdigital.models.CreateContactRequest
import energy.so.dotdigital.models.EmailProperties
import energy.so.dotdigital.models.EmailType
import energy.so.dotdigital.models.Identifier
import energy.so.dotdigital.models.OptInType

object DotDigitalPrecannedData {

    const val CONTACT_ID = "<EMAIL>"
    const val TEST_REGION = "test-region"

    val testConfig = DotDigitalConfig(
        region = TEST_REGION,
        apiUser = "test",
        apiPassword = "test",
        connectionTimeout = 30000,
        requestTimeout = 30000,
    )

    val createContactRequest = CreateContactRequest(
        identifiers = Identifier(
            email = CONTACT_ID
        ),
        channelProperties = ChannelProperty(
            email = EmailProperties(
                emailType = EmailType.HTML,
                optInType = OptInType.DOUBLE,
                status = ChannelStatus.SUBSCRIBED,
            )
        )
    )

    const val GET_PREFERENCES_JSON = """[
    {
        "id": 5466,
        "publicName": "Preferred contact method",
        "privateName": "Preferred contact method",
        "isPreference": true,
        "order": "0",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-09-11T15:55:38Z",
        "lastModified": "2025-03-27T11:26:10Z",
        "isOptedIn": false
    },
    {
        "id": 5994,
        "publicName": "Solar",
        "privateName": "solar",
        "isPreference": true,
        "order": "1",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-10-18T14:03:25Z",
        "lastModified": "2025-03-27T11:26:10Z",
        "isOptedIn": false
    },
    {
        "id": 5995,
        "publicName": "Electric vehicules",
        "privateName": "ev",
        "isPreference": true,
        "order": "2",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-10-18T14:03:42Z",
        "lastModified": "2025-03-27T11:26:10Z",
        "isOptedIn": false
    }
]"""

    const val GET_SUBSCRIPTIONS_JSON = """{
    "contact": {
        "id": *********,
        "email": "$CONTACT_ID",
        "optInType": "Double",
        "emailType": "Html",
        "dataFields": null,
        "status": "Subscribed"
    },
    "marketingPreferenceOptIns": []
}"""

    const val GET_PREFERENCES_EMPTY_JSON = """[]"""

    const val GET_ALL_PREFERENCES_JSON = """[
    {
        "id": 5466,
        "publicName": "Preferred contact method",
        "privateName": "Preferred contact method",
        "isPreference": true,
        "order": "0",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-09-11T15:55:38Z",
        "lastModified": "2025-03-27T11:26:10Z"
    },
    {
        "id": 5994,
        "publicName": "Solar",
        "privateName": "solar",
        "isPreference": true,
        "order": "1",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-10-18T14:03:25Z",
        "lastModified": "2025-03-27T11:26:10Z"
    },
    {
        "id": 5995,
        "publicName": "Electric vehicules",
        "privateName": "ev",
        "isPreference": true,
        "order": "2",
        "isPublic": true,
        "categoryId": 0,
        "preferenceCount": 0,
        "created": "2024-10-18T14:03:42Z",
        "lastModified": "2025-03-27T11:26:10Z"
    }
]"""

    const val V2_ERROR_JSON = """{
    "message": "Error: ERROR_CONTACT_NOT_FOUND"
}"""

    const val V3_ERROR_JSON = """{
    "errorCode": "server:badRequest",
    "description": "Bad request",
    "details": [
        {
            "item": "line 1, position 1",
            "description": "Required properties are missing from object: identifiers."
        }
    ]
}"""

    const val V3_NOT_FOUND_JSON = """{
    "errorCode": "contacts:contactNotFound",
    "description": "The following contacts could not be found.",
    "details": [
        {
            "item": "<EMAIL>",
            "description": "Not found using the identifier 'email'."
        }
    ]
}"""

    const val UNSUB_CONTACT_RESPONSE_JSON = """{
    "suppressedContact": {
        "id": *********,
        "email": "$CONTACT_ID",
        "optInType": "Double",
        "emailType": "Html",
        "dataFields": null,
        "status": "Unsubscribed"
    },
    "dateRemoved": "2025-05-19T16:37:36.0478329Z",
    "reason": "Unsubscribed"
}"""

    const val RESUB_CONTACT_RESPONSE_JSON = """{
    "contact": {
        "id": *********,
        "email": "$CONTACT_ID",
        "optInType": "Double",
        "emailType": "Html",
        "dataFields": null,
        "status": "Subscribed"
    },
    "status": "ContactAdded"
}"""

    const val CONTACT_RESPONSE_JSON = """{
    "contactId": *********,
    "status": "subscribed",
    "created": "2025-05-19T16:32:26.5639031Z",
    "updated": "2025-05-19T16:32:26.5639031Z",
    "identifiers": {
        "email": "$CONTACT_ID",
        "mobileNumber": ""
    },
    "dataFields": {
        "ACCOUNT_BALANCE": null,
        "ACCOUNT_CREATED_DATE": null,
        "ACCOUNT_CREATED_YEAR": null,
        "ACCOUNT_END_DATE": null,
        "ACCOUNT_ID": null,
        "ACCOUNT_NUMBER": null,
        "ACCOUNT_START_DATE": null,
        "ACCOUNT_STATUS": null,
        "BILL_DELIVERY": null,
        "BILLING_POSTCODE": null,
        "COMPLAINTS": null,
        "CONTACT_NUMBER": null,
        "CUSTOMER_FORENAME": null,
        "CUSTOMER_ID": null,
        "CUSTOMER_NUMBER": null,
        "DATE_OF_BIRTH": null,
        "FIRSTNAME": null,
        "FIXED_SVT_ELEC": null,
        "FIXED_SVT_GAS": null,
        "FULL_NAME": null,
        "FULLNAME": null,
        "GENDER": null,
        "GSP_NAME_ELEC": null,
        "GSP_NAME_GAS": null,
        "IS_ON_SUPPLY": null,
        "LASTNAME": null,
        "LASTSUBSCRIBED": "2025-05-19T16:32:26.563903Z",
        "MARKETING_OPT_OUT": null,
        "METER_TYPE_ELEC": null,
        "METER_TYPE_GAS": null,
        "MPXN_ELEC": null,
        "MPXN_GAS": null,
        "OCCUPIER_FLAG": null,
        "OPEN_COMPLAINTS": null,
        "PHONE_NUMBER": null,
        "POSTCODE": null,
        "PSR_END": null,
        "PSR_START": null,
        "REQUEST_REASON": null,
        "SUPPLY_POSTCODE": null,
        "TARIFF_END_ELEC": null,
        "TARIFF_END_GAS": null,
        "TARIFF_NAME_ELEC": null,
        "TARIFF_NAME_GAS": null,
        "TARIFF_START_ELEC": null,
        "TARIFF_START_GAS": null,
        "TITLE": null,
        "VULNPSRCUST_FLAG": null
    },
    "channelProperties": {
        "email": {
            "status": "subscribed",
            "emailType": "html",
            "optInType": "double"
        }
    },
    "lists": [
        {
            "id": 1949512,
            "name": "Which?",
            "status": "subscribed"
        },
        {
            "id": 2729672,
            "name": "Solar £1000 (Aug1395 customers) 19/11",
            "status": "subscribed"
        },
        {
            "id": 2814530,
            "name": "Solar £1000 (18-20th Aug1395 customers) - 1st reminder 25/11",
            "status": "subscribed"
        },
        {
            "id": 2869197,
            "name": "Solar £1000 (ALL Aug1395 customers) - 2nd reminder 28/11",
            "status": "subscribed"
        }
    ]
}"""

}