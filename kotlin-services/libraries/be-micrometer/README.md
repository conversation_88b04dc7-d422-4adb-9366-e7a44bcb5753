Inject the MicrometerHandler in the service where you want to record metrics.

Here is an example

```cookie
class DcaJourneyHandler(micrometerHandler: MicrometerHandler) {

    val dcaAssignmentTimer: WrappedTimer
    val recallCounter: WrappedCounter

    init {
        micrometerHandler.registerGauge("NumAccountsInDebt", this::getNumberOfAccountsInDebt, "Number of active accounts with a balance in debit")

        dcaAssignmentTimer = micrometerHandler.registerTimer("TimeToSubmitToDca", TimeUnit.DAYS, "Number of days between an account going into debit, and being assigned to a DCA")

        recallCounter = micrometerHandler.registerCounter("NumDcaRecalls", "Number of accounts recalled from DCAs")
    }

    fun submitAccountForDunning(accountId: Long) {

        // TODO: implement logic to send account details to <PERSON><PERSON>
    }

    fun sendAccountToDca(accountId: Long, dcaId: Long) {

        val currentDate = LocalDateTime.now()
        val firstDebitDate = getFirstDebitDate(accountId)

        dcaAssignmentTimer.recordDuration(firstDebitDate, currentDate)

        // TODO: implement logic to assign account to DCA, including writing the details to the correct CSV file
    }

    fun processAccountUpdateFromDca(accountId: Long, dcaId: Long, update: String) {

        // TODO: Register any inbound changes from the DCA to our systems, e.g. name/address updates, payments made
    }

    fun registerQueryFromDca(accountId: Long, dcaId: Long, query: String) {

        // TODO: save a record of the question asked by the DCA
    }

    fun respondToQueryFromDca(accountId: Long, dcaId: Long, response: String) {

        // TODO: write the query response to the outbound DCA file
    }

    fun recallAccountFromDca(accountId: Long, dcaId: Long, reason: String) {

        recallCounter.recordTick()

        // TODO: write the relevant CSV file to recall an account from the given DCA
    }

    fun writeOffAccountDebt(accountId: Long, dcaId: Long) {

        // TODO: we can't recover the debt, so update our systems to write it off
    }

    fun markAccountAsRepaid(accountId: Long) {

        // TODO: the debt was repaid; update our systems accordingly
    }

    fun prepareAccountForReassignment(accountId: Long) {

        // TODO: having recalled the account from a DCA, get it ready to assign to another
    }

    fun getAccountDcaStatus(accountId: Long): DcaStatus {

        return DcaStatus.AWAITING_UPDATES
    }

    private fun getNumberOfAccountsInDebt(): Double {

        return 0.0
    }

    private fun getFirstDebitDate(accountId: Long): LocalDateTime {

        return LocalDateTime.now()
    }
}
```