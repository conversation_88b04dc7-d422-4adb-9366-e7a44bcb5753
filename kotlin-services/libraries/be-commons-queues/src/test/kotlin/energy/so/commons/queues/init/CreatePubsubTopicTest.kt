package energy.so.commons.queues.init

import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.config.TopicConfiguration
import io.mockk.every
import io.mockk.just
import io.mockk.mockkObject
import io.mockk.verify
import io.mockk.Runs
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CreatePubsubTopicTest {

    private val configuration = PubSubConfiguration(
        projectName = SAMPLE_PROJECT_NAME,
        topics = setOf(
            TopicConfiguration("users", key = "key"),
            TopicConfiguration("accounts", key = "key"),
            TopicConfiguration("customers", key = "key")
        )
    )

    @BeforeEach
    fun setup() {
        mockkObject(PubSubConfiguration.Companion)
        every { PubSubConfiguration.Companion.fromApplicationConf() } returns configuration

        mockkObject(TopicManager)
    }

    @Test
    fun `for each topic on the config it tries to create a topic with the TopicManager`() {
        every {
            TopicManager.createTopicIfMissing(
                projectName = SAMPLE_PROJECT_NAME,
                topic = any()
            )
        } just Runs

        CreatePubsubTopic.main(arrayOf())

        configuration.topics.forEach {
            verify { TopicManager.createTopicIfMissing(projectName = SAMPLE_PROJECT_NAME, topic = it) }
        }
    }

    companion object {
        const val SAMPLE_PROJECT_NAME = "sample-project-name"
    }

}