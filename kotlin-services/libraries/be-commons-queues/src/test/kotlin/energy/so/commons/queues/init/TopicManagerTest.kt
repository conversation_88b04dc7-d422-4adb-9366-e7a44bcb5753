package energy.so.commons.queues.init

import com.google.api.gax.rpc.NotFoundException
import com.google.cloud.pubsub.v1.TopicAdminClient
import com.google.pubsub.v1.Topic
import com.google.pubsub.v1.TopicName
import energy.so.commons.queues.config.TopicConfiguration
import io.mockk.mockk
import io.mockk.unmockkStatic
import io.mockk.mockkStatic
import io.mockk.every
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class TopicManagerTest {

    private val topicConfiguration = TopicConfiguration(TOPIC_NAME, key = "key")
    private val topicAdminClient = mockk<TopicAdminClient>(relaxed = true)

    @BeforeEach
    fun setup() {
        unmockkStatic("com.google.cloud.pubsub.v1.TopicAdminClient")
        mockkStatic("com.google.cloud.pubsub.v1.TopicAdminClient")

        every { TopicAdminClient.create() } returns topicAdminClient
    }

    @Test
    fun `topic already exists and it is not created`() {
        val topic = mockk<Topic>()
        val topicNameCapture = slot<TopicName>()

        every { topicAdminClient.getTopic(capture(topicNameCapture)) } returns topic

        TopicManager.createTopicIfMissing(projectName = PROJECT_NAME, topic = topicConfiguration)

        verify(inverse = true) { topicAdminClient.createTopic(any<TopicName>()) }

        topicNameCapture.captured.run {
            assertThat(project).isEqualTo(PROJECT_NAME)
            assertThat(this.topic).isEqualTo(topicConfiguration.name)
        }
    }

    @Test
    fun `topic does not exist then it's created`() {
        val topic = mockk<Topic>()
        val getTopicCapture = slot<TopicName>()
        val createTopicCapture = slot<TopicName>()

        every { topicAdminClient.getTopic(capture(getTopicCapture)) } throws mockk<NotFoundException>()
        every { topicAdminClient.createTopic(capture(createTopicCapture)) } returns topic

        TopicManager.createTopicIfMissing(projectName = PROJECT_NAME, topic = topicConfiguration)

        verify { topicAdminClient.createTopic(any<TopicName>()) }

        assertThat(getTopicCapture.captured).isEqualTo(createTopicCapture.captured)
        getTopicCapture.captured.run {
            assertThat(project).isEqualTo(PROJECT_NAME)
            assertThat(this.topic).isEqualTo(topicConfiguration.name)
        }
    }

    companion object {
        const val PROJECT_NAME = "gcp-project-name"
        const val TOPIC_NAME = "some-topic"
    }

}
