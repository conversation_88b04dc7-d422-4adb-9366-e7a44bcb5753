package energy.so.commons.queues.subscribers

import com.google.api.gax.rpc.NotFoundException
import com.google.api.gax.rpc.StatusCode
import com.google.cloud.pubsub.v1.MessageReceiver
import com.google.cloud.pubsub.v1.Subscriber
import com.google.cloud.pubsub.v1.SubscriptionAdminClient
import com.google.cloud.pubsub.v1.SubscriptionAdminSettings
import com.google.cloud.pubsub.v1.TopicAdminClient
import com.google.cloud.pubsub.v1.TopicAdminSettings
import com.google.pubsub.v1.ProjectSubscriptionName
import com.google.pubsub.v1.TopicName
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Test

class AckPubsubMessageProcessorTest {

    @Test
    fun `when project name is empty should throw exception`() {
        val config = SubscriptionConfiguration(name = SAMPLE_SUBSCRIPTION_NAME, topic = "some-topic", key = "key", topicKey = "topicKey")

        val ex = shouldThrow<IllegalArgumentException> {
            TestMessageProcessor(
                projectName = "",
                config = config
            )
        }

        ex.message shouldBe "project cannot be blank"
    }

    @Test
    fun `when subscription name is empty should throw exception`() {
        val config = SubscriptionConfiguration(name = "", topic = "some-topic", key = "key", topicKey = "topicKey")

        val ex = shouldThrow<IllegalArgumentException> {
            TestMessageProcessor(
                projectName = SAMPLE_PROJECT_NAME,
                config = config
            )
        }

        ex.message shouldBe "subscription cannot be blank"
    }

    @Test
    fun `when valid subscription information provided should start message consumption`() {
        val config = SubscriptionConfiguration(name = SAMPLE_SUBSCRIPTION_NAME, topic = SAMPLE_TOPIC_NAME, key = "key", topicKey = "topicKey")

        val subscriber = TestMessageProcessor(projectName = SAMPLE_PROJECT_NAME, config = config)

        val pubsubSubscriber = mockSubscriber()
        val subscriberBuilder = mockSubscriberBuilder(pubsubSubscriber)

        mockkStatic("com.google.cloud.pubsub.v1.Subscriber")
        every {
            Subscriber.newBuilder(
                eq(ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME)),
                any<MessageReceiver>()
            )
        } returns subscriberBuilder

        subscriber.subscribe()

        verify {
            subscriberBuilder.setCredentialsProvider(any())
            subscriberBuilder.setChannelProvider(any())
            subscriberBuilder.setParallelPullCount(config.parallelPullCount)
            pubsubSubscriber.startAsync()
            pubsubSubscriber.awaitRunning()
        }
    }

    @Test
    fun `given a topic and subscription that exist when credentials are disabled then the topic and subscription should not be created`() {
        val config =
            SubscriptionConfiguration(name = SAMPLE_SUBSCRIPTION_NAME, topic = "some-topic", enableCredentials = false, key = "key", topicKey = "topicKey")
        val subscriber = TestMessageProcessor(projectName = SAMPLE_PROJECT_NAME, config = config)

        val pubsubSubscriber = mockSubscriber()
        val subscriberBuilder = mockSubscriberBuilder(pubsubSubscriber)

        val mockAdminClient = mockk<TopicAdminClient> {
            every {
                getTopic(TopicName.of(SAMPLE_PROJECT_NAME, SAMPLE_TOPIC_NAME))
            } returns mockk()
            every {
                close()
            } returns Unit
        }

        val mockSubClient = mockk<SubscriptionAdminClient> {
            every {
                getSubscription(ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME).toString())
            } returns mockk()
            every {
                close()
            } returns Unit
        }

        mockkStatic("com.google.cloud.pubsub.v1.Subscriber")
        every {
            Subscriber.newBuilder(
                eq(ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME)),
                any<MessageReceiver>()
            )
        } returns subscriberBuilder

        mockkStatic("com.google.cloud.pubsub.v1.TopicAdminClient")
        every {
            TopicAdminClient.create(any<TopicAdminSettings>())
        } returns mockAdminClient

        mockkStatic("com.google.cloud.pubsub.v1.SubscriptionAdminClient")
        every {
            SubscriptionAdminClient.create(any<SubscriptionAdminSettings>())
        } returns mockSubClient

        subscriber.subscribe()

        verify {
            subscriberBuilder.setCredentialsProvider(any())
            subscriberBuilder.setChannelProvider(any())
            subscriberBuilder.setParallelPullCount(config.parallelPullCount)
            pubsubSubscriber.startAsync()
            pubsubSubscriber.awaitRunning()
            // As the topic exists in this example, it should not create a new one
            mockAdminClient.getTopic(TopicName.of(SAMPLE_PROJECT_NAME, SAMPLE_TOPIC_NAME))
            // As the subscription exists in this example, it should not create a new one
            mockSubClient.getSubscription(
                ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME).toString()
            )
        }
    }

    @Test
    fun `given a topic and subscription that do not exist when credentials are disabled then the topic and subscription should be created`() {
        val config =
            SubscriptionConfiguration(name = SAMPLE_SUBSCRIPTION_NAME, topic = "some-topic", enableCredentials = false, key = "key", topicKey = "topicKey")
        val subscriber = TestMessageProcessor(projectName = SAMPLE_PROJECT_NAME, config = config)
        val statusCode = object : StatusCode {
            override fun getCode(): StatusCode.Code {
                return StatusCode.Code.OK
            }

            override fun getTransportCode(): Any {
                return Unit
            }
        }

        val pubsubSubscriber = mockSubscriber()
        val subscriberBuilder = mockSubscriberBuilder(pubsubSubscriber)
        val topicName = TopicName.of(SAMPLE_PROJECT_NAME, SAMPLE_TOPIC_NAME)
        val subName = ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME)

        val mockAdminClient = mockk<TopicAdminClient> {
            every {
                getTopic(topicName)
            } throws NotFoundException("Topic not found", IllegalStateException(), statusCode, false)
            every {
                createTopic(topicName)
            } returns mockk()
            every {
                close()
            } returns Unit
        }

        val mockSubClient = mockk<SubscriptionAdminClient> {
            every {
                getSubscription(subName.toString())
            } throws NotFoundException(
                "Subscription not found",
                IllegalStateException(),
                statusCode,
                false
            )
            every {
                createSubscription(any())
            } returns mockk()
            every {
                close()
            } returns Unit
        }

        mockkStatic("com.google.cloud.pubsub.v1.Subscriber")
        every {
            Subscriber.newBuilder(
                eq(ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME)),
                any<MessageReceiver>()
            )
        } returns subscriberBuilder

        mockkStatic("com.google.cloud.pubsub.v1.TopicAdminClient")
        every {
            TopicAdminClient.create(any<TopicAdminSettings>())
        } returns mockAdminClient

        mockkStatic("com.google.cloud.pubsub.v1.SubscriptionAdminClient")
        every {
            SubscriptionAdminClient.create(any<SubscriptionAdminSettings>())
        } returns mockSubClient

        subscriber.subscribe()

        verify {
            subscriberBuilder.setCredentialsProvider(any())
            subscriberBuilder.setChannelProvider(any())
            subscriberBuilder.setParallelPullCount(config.parallelPullCount)

            pubsubSubscriber.startAsync()
            pubsubSubscriber.awaitRunning()

            // getTopic throws an exception, as it does not exist, it should create a new topic
            mockAdminClient.getTopic(TopicName.of(SAMPLE_PROJECT_NAME, SAMPLE_TOPIC_NAME))
            mockAdminClient.createTopic(topicName)

            // getSubscription throws an exception, as it does not exist, it should create a new topic
            mockSubClient.getSubscription(
                ProjectSubscriptionName.of(SAMPLE_PROJECT_NAME, SAMPLE_SUBSCRIPTION_NAME).toString()
            )
            mockSubClient.createSubscription(any())
        }
    }

    private fun mockSubscriberBuilder(grpcSubscriber: Subscriber): Subscriber.Builder {
        return mockk {
            every {
                build()
            } returns grpcSubscriber
            every {
                setCredentialsProvider(any())
            } returns this
            every {
                setChannelProvider(any())
            } returns this
            every {
                setParallelPullCount(any())
            } returns this
        }
    }

    private fun mockSubscriber(): Subscriber {
        return mockk {
            every {
                startAsync()
            } returns this
            every {
                awaitRunning()
            } returns Unit
        }
    }

    class TestMessageProcessor(projectName: String, config: SubscriptionConfiguration) :
        PubsubMessageProcessor<String>(projectName, config) {
        override suspend fun processMessage(message: QueueMessage<String>) {
            println("processing")
        }
    }

    companion object {
        private const val SAMPLE_PROJECT_NAME = "sample-project-name"
        private const val SAMPLE_SUBSCRIPTION_NAME = "sample-subscription-name"
        private const val SAMPLE_TOPIC_NAME = "some-topic"
    }
}
