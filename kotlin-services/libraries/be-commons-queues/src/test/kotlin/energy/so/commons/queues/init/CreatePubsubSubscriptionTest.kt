package energy.so.commons.queues.init

import com.google.api.gax.rpc.NotFoundException
import com.google.cloud.pubsub.v1.SubscriptionAdminClient
import com.google.pubsub.v1.ProjectSubscriptionName
import com.google.pubsub.v1.Subscription
import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.config.TopicConfiguration
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.just
import io.mockk.Runs
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CreatePubsubSubscriptionTest {

    private val configuration = PubSubConfiguration(
        projectName = SAMPLE_PROJECT_NAME,
        subscriptions = setOf(
            SubscriptionConfiguration(name = "users-send-welcome-email", topic = "users",  key = "test-key", topicKey = "test-topic-key"),
            SubscriptionConfiguration(name = "users-password-reset", topic = "users",  key = "test-key", topicKey = "test-topic-key"),
            SubscriptionConfiguration(
                name = "accounts-activation-code",
                topic = "accounts",
                deadLetterQueueTopicName = "accounts-dlq",
                key = "test-key",
                topicKey = "test-topic-key"
            ),
            SubscriptionConfiguration(
                name = "customers-deleted",
                topic = "customers",
                deadLetterQueueTopicName = "customers-dlq",
                key = "test-key",
                topicKey = "test-topic-key"
            ),
        )
    )
    private val subscriptionAdminClient = mockk<SubscriptionAdminClient>(relaxed = true)

    @BeforeEach
    fun setup() {
        unmockkStatic("com.google.cloud.pubsub.v1.SubscriptionAdminClient")

        mockkObject(PubSubConfiguration)
        mockkObject(TopicManager)
        mockkStatic("com.google.cloud.pubsub.v1.SubscriptionAdminClient")

        every { SubscriptionAdminClient.create() } returns subscriptionAdminClient
        every { PubSubConfiguration.Companion.fromApplicationConf() } returns configuration
    }

    @Test
    fun `subscriptions already exists and they are not not created`() {
        val subscription = mockk<Subscription>()
        val subscriptionNamesCapture = mutableListOf<ProjectSubscriptionName>()

        every { subscriptionAdminClient.getSubscription(capture(subscriptionNamesCapture)) } returns subscription

        CreatePubsubSubscription.main(arrayOf())

        verify(inverse = true) {
            TopicManager.createTopicIfMissing(any(), any())
            subscriptionAdminClient.createSubscription(any())
        }

        subscriptionNamesCapture.forEach { subscriptionName ->
            assertThat(configuration.subscriptions).anyMatch { subscriptionName.subscription == it.name && subscriptionName.project == SAMPLE_PROJECT_NAME }
        }
    }

    @Test
    fun `for each subscription on the config it tries to create a subscription on gcp`() {
        val getSubscriptionCaptures = mutableListOf<ProjectSubscriptionName>()
        val createSubscriptionCaptures = mutableListOf<Subscription>()

        every { subscriptionAdminClient.getSubscription(capture(getSubscriptionCaptures)) } throws mockk<NotFoundException>()
        every {
            TopicManager.createTopicIfMissing(
                projectName = SAMPLE_PROJECT_NAME,
                topic = any()
            )
        } just Runs

        CreatePubsubSubscription.main(arrayOf())

        verifyTopicsCreationCalls()

        verify { subscriptionAdminClient.createSubscription(capture(createSubscriptionCaptures)) }

        getSubscriptionCaptures.forEach { subscriptionName ->
            assertThat(configuration.subscriptions).anyMatch { subscriptionName.subscription == it.name && subscriptionName.project == SAMPLE_PROJECT_NAME }
        }

        createSubscriptionCaptures.forEach { subscription ->
            assertThat(configuration.subscriptions).anyMatch {
                subscription.name.endsWith(it.name) && subscription.name.contains(
                    SAMPLE_PROJECT_NAME
                )
            }
        }
    }

    private fun verifyTopicsCreationCalls() {
        val subscriptionTopics = configuration.subscriptions.map { it.topic }.toSet()
        val subscriptionDlqTopics = configuration.subscriptions.map { it.topic }.toSet()
        val topicNameSlots = mutableListOf<TopicConfiguration>()


        verify {
            TopicManager.createTopicIfMissing(
                projectName = SAMPLE_PROJECT_NAME,
                topic = capture(topicNameSlots)
            )
        }

        (subscriptionTopics + subscriptionDlqTopics).forEach { topicName ->
            assertThat(topicNameSlots).anyMatch { topicName == it.name }
        }
    }

    companion object {
        const val SAMPLE_PROJECT_NAME = "sample-project-name"
    }
}