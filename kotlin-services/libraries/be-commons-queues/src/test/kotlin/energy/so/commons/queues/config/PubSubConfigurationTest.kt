package energy.so.commons.queues.config

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class PubSubConfigurationTest {

    private val configuration = PubSubConfiguration(
        projectName = "gcp-project",
        topics = setOf(
            TopicConfiguration("users", key = "users-key"),
            TopicConfiguration("accounts", key = "accounts-key"),
            TopicConfiguration(TOPIC_NAME_TO_SEARCH, key = "key")
        ),
        subscriptions = setOf(
            SubscriptionConfiguration("users-send-welcome-email", "users", key = "test-key", topicKey = "test-topic-key"),
            SubscriptionConfiguration(SUBSCRIPTION_NAME_TO_SEARCH, "users", key = "test-key", topicKey = "test-topic-key"),
            SubscriptionConfiguration("accounts-activation-code", "accounts",  key = "test-key", topicKey = "test-topic-key"),
            SubscriptionConfiguration("customers-deleted", TOPIC_NAME_TO_SEARCH,  key = "test-key", topicKey = "test-topic-key")
        )
    )

    @Test
    fun `finds topic by the given name`() {
        val topic = configuration.getTopic(TOPIC_NAME_TO_SEARCH)

        assertThat(topic!!.name).isEqualTo(TOPIC_NAME_TO_SEARCH)
    }

    @Test
    fun `searches a topic that does not exist, then returns null`() {
        val topic = configuration.getTopic("non-existent-topic")

        assertThat(topic).isNull()
    }

    @Test
    fun `finds subscription by the given name`() {
        val subscription = configuration.getSubscription(SUBSCRIPTION_NAME_TO_SEARCH)

        assertThat(subscription!!.name).isEqualTo(SUBSCRIPTION_NAME_TO_SEARCH)
    }

    @Test
    fun `searches a subscription that does not exist, then returns null`() {
        val subscription = configuration.getTopic("non-existent-subscription")

        assertThat(subscription).isNull()
    }

    companion object {
        const val TOPIC_NAME_TO_SEARCH = "customers"
        const val SUBSCRIPTION_NAME_TO_SEARCH = "users-password-reset"
    }

}