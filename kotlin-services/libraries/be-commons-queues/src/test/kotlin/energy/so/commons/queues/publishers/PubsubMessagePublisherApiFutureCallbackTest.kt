package energy.so.commons.queues.publishers

import com.google.api.gax.grpc.GrpcStatusCode
import com.google.api.gax.rpc.ApiException
import io.grpc.Status
import org.junit.jupiter.api.Test

class PubsubMessagePublisherApiFutureCallbackTest {

    @Test
    fun `when ApiFuture publishing the Pubsub message fails with ApiException should log error`() {
        val apiFutureCallback = MessagePublisherApiFutureCallback(SAMPLE_TOPIC)

        apiFutureCallback.onFailure(
            ApiException(IllegalArgumentException(), GrpcStatusCode.of(Status.Code.INTERNAL), true)
        )
    }

    @Test
    fun `when ApiFuture publishing the Pubsub message fails with exception other than ApiException should log error`() {
        val apiFutureCallback = MessagePublisherApiFutureCallback(SAMPLE_TOPIC)

        apiFutureCallback.onFailure(IllegalArgumentException())
    }

    @Test
    fun `when ApiFuture publishing the Pubsub message succeeds should log message id`() {
        val apiFutureCallback = MessagePublisherApiFutureCallback(SAMPLE_TOPIC)

        apiFutureCallback.onSuccess(SAMPLE_MESSAGE_ID)
    }

    companion object {
        private const val SAMPLE_TOPIC = "sample-topic"
        private const val SAMPLE_MESSAGE_ID = "sample-message-id"
    }
}