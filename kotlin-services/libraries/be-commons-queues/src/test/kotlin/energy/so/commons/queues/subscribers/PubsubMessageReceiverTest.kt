package energy.so.commons.queues.subscribers

import com.google.cloud.pubsub.v1.AckReplyConsumer
import com.google.protobuf.ByteString
import com.google.protobuf.Parser
import com.google.pubsub.v1.PubsubMessage
import energy.so.commons.queues.exceptions.MessageProcessingFailedException
import energy.so.commons.queues.util.SampleGrpcRequest
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.lang.IllegalArgumentException

class PubsubMessageReceiverTest {

    @Test
    fun `when MessageProcessingFailedException thrown should acknowledge the message to avoid infinite retries`() =
        runBlocking {
            val parser = mockk<Parser<SampleGrpcRequest>> {
                every {
                    parseFrom(any<ByteString>())
                } returns mockk()
            }
            val messageProcessor = mockk<MessageProcessor<SampleGrpcRequest>> {
                coEvery {
                    processMessage(any())
                } throws MessageProcessingFailedException()
            }

            val consumer = mockk<AckReplyConsumer>()

            val messageReceiver = PubsubMessageReceiver(messageProcessor, parser)
            messageReceiver
                .launchMessageConsumeCoroutine(PubsubMessage.getDefaultInstance(), consumer)
                .join()

            verify {
                consumer.ack()
            }
        }

    @Test
    fun `when other exception  thrown should not acknowledge the message to Pubsub to trigger retry`() =
        runBlocking {
            val parser = mockk<Parser<SampleGrpcRequest>> {
                every {
                    parseFrom(any<ByteString>())
                } returns mockk()
            }
            val messageProcessor = mockk<MessageProcessor<SampleGrpcRequest>> {
                coEvery {
                    processMessage(any())
                } throws IllegalArgumentException()
            }

            val consumer = mockk<AckReplyConsumer>()

            val messageReceiver = PubsubMessageReceiver(messageProcessor, parser)
            messageReceiver
                .launchMessageConsumeCoroutine(PubsubMessage.getDefaultInstance(), consumer)
                .join()

            verify {
                consumer.nack()
            }
        }

    @Test
    fun `when message is processed should acknowledge the message to Pubsub`() = runBlocking {
        val parser = mockk<Parser<SampleGrpcRequest>> {
            every {
                parseFrom(any<ByteString>())
            } returns mockk()
        }
        val messageProcessor = mockk<MessageProcessor<SampleGrpcRequest>> {
            coEvery {
                processMessage(any())
            } returns Unit
        }

        val consumer = mockk<AckReplyConsumer>()

        val messageReceiver = PubsubMessageReceiver(messageProcessor, parser)
        messageReceiver
            .launchMessageConsumeCoroutine(PubsubMessage.getDefaultInstance(), consumer)
            .join()

        verify {
            consumer.ack()
        }
    }

}