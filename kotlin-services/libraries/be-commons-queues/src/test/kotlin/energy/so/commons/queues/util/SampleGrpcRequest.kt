package energy.so.commons.queues.util

import com.google.protobuf.GeneratedMessageV3
import com.google.protobuf.Message

class SampleGrpcRequest : GeneratedMessageV3() {
    override fun getDefaultInstanceForType(): Message {
        TODO("Not yet implemented")
    }

    override fun newBuilderForType(parent: BuilderParent?): Message.Builder {
        TODO("Not yet implemented")
    }

    override fun newBuilderForType(): Message.Builder {
        TODO("Not yet implemented")
    }

    override fun toBuilder(): Message.Builder {
        TODO("Not yet implemented")
    }

    override fun internalGetFieldAccessorTable(): FieldAccessorTable {
        TODO("Not yet implemented")
    }

}