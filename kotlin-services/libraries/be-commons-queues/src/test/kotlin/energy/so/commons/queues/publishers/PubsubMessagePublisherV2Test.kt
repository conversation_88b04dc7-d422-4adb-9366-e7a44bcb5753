package energy.so.commons.queues.publishers

import com.google.api.core.ApiFuture
import com.google.cloud.pubsub.v1.Publisher
import com.google.pubsub.v1.TopicName
import energy.so.commons.queues.config.TopicConfiguration
import energy.so.commons.queues.exceptions.MessagePublishingFailedException
import energy.so.commons.queues.models.QueueMessage
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.verify
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeUnit.SECONDS
import org.junit.jupiter.api.Test

class PubsubMessagePublisherV2Test {

    @Test
    fun `when message publishing fails before message processing step should throw MessagePublishingFailedException`() {
        val cause = IllegalArgumentException()
        val pubsubPublisher = mockk<Publisher>(relaxed = true) {
            every { publish(any()) } throws cause
            every { shutdown() } returns Unit
            every { awaitTermination(10, SECONDS) } returns true
        }

        val mockPublisherBuilder = mockPublisherBuilder(pubsubPublisher)

        mockkStatic("com.google.cloud.pubsub.v1.Publisher")
        every { Publisher.newBuilder(any<TopicName>()) } returns mockPublisherBuilder

        val publisher =
            PubsubMessagePublisherV2<String>(projectName = PROJECT_NAME, config = PUBSUB_TOPIC_CONFIG)

        val message = QueueMessage("some message")

        val ex = shouldThrow<MessagePublishingFailedException> { publisher.publishMessage(message) }

        cause shouldBe ex.cause
    }

    @Test
    fun `when message published correctly should send it and close resources`() {

        val mockPublisher = mockPublisher()
        val mockPublisherBuilder = mockPublisherBuilder(mockPublisher)

        mockkStatic("com.google.cloud.pubsub.v1.Publisher")
        every { Publisher.newBuilder(any<TopicName>()) } returns mockPublisherBuilder

        val publisher =
            PubsubMessagePublisherV2<String>(projectName = PROJECT_NAME, config = PUBSUB_TOPIC_CONFIG)

        val message = QueueMessage(data = "some message")

        publisher.publishMessage(message)
    }

    @Test
    fun `publishMessageAsync calls publishMessage method`() {
        // Given
        val mockPublisher = mockPublisher()
        val mockPublisherBuilder = mockPublisherBuilder(mockPublisher)

        mockkStatic("com.google.cloud.pubsub.v1.Publisher")
        every { Publisher.newBuilder(any<TopicName>()) } returns mockPublisherBuilder

        // When
        val publisher =
            spyk(PubsubMessagePublisherV2<String>(projectName = PROJECT_NAME, config = PUBSUB_TOPIC_CONFIG))

        val message = QueueMessage("some message")
        publisher.publishMessageAsync(message = message, retries = RETRIES, delay = 100, delayFactor = 1)

        // Then
        verify(timeout = 100L) { publisher.publishMessage(message) }
    }

    @Test
    fun `publishMessage throws an error so it retries the calls by the given number times`() {
        // Given
        val message = QueueMessage("some message")
        val mockPublisher = mockPublisher()
        val mockPublisherBuilder = mockPublisherBuilder(mockPublisher)

        mockkStatic("com.google.cloud.pubsub.v1.Publisher")
        every { Publisher.newBuilder(any<TopicName>()) } returns mockPublisherBuilder

        // When
        val publisher = PubsubMessagePublisherV2<String>(projectName = PROJECT_NAME, config = PUBSUB_TOPIC_CONFIG)
        every { mockPublisher.publish(any()) } throws Exception("cannot publish message")

        publisher.publishMessageAsync(message = message, retries = RETRIES, delay = 100, delayFactor = 1)

        // Then
        verify(exactly = RETRIES.toInt() + 1, timeout = 5000) { mockPublisher.publish(any()) }
    }

    @Test
    fun `shutdown calls publisher shutdowns`() {
        // Given
        val duration = 3402L
        val timeUnit = TimeUnit.MICROSECONDS
        val mockPublisher = mockPublisher()
        val mockPublisherBuilder = mockPublisherBuilder(mockPublisher)

        mockkStatic("com.google.cloud.pubsub.v1.Publisher")
        every { Publisher.newBuilder(any<TopicName>()) } returns mockPublisherBuilder
        justRun { mockPublisher.shutdown() }
        every { mockPublisher.awaitTermination(duration, timeUnit) } returns true

        // When
        val publisher = PubsubMessagePublisherV2<String>(projectName = PROJECT_NAME, config = PUBSUB_TOPIC_CONFIG)
        publisher.shutdown(duration, timeUnit)

        // Then
        verify(exactly = 1) { mockPublisher.shutdown() }
        verify(exactly = 1) { mockPublisher.awaitTermination(duration, timeUnit) }
    }

    fun mockPublisher(): Publisher {
        val apiFuture = mockk<ApiFuture<String>> {
            every { addListener(any(), any()) } returns Unit
            every { get() } returns "messageID"
        }

        return mockk {
            every { publish(any()) } returns apiFuture
            every { shutdown() } returns Unit
            every { awaitTermination(PUBSUB_TOPIC_CONFIG.timeoutInSeconds, SECONDS) } returns true
        }
    }

    fun mockPublisherBuilder(publisher: Publisher): Publisher.Builder {
        return mockk {
            every {
                setCredentialsProvider(any())
            } returns this
            every {
                setChannelProvider(any())
            } returns this
            every { build() } returns publisher
        }
    }

    companion object {
        private const val RETRIES = 5L
        private const val PROJECT_NAME = "sample-project-name"
        private val PUBSUB_TOPIC_CONFIG = TopicConfiguration(
            name = "sample-topic-name",
            timeoutInSeconds = 1L,
            key = "key"
        )
    }
}
