package energy.so.commons.json.utils

import energy.so.commons.json.exceptions.UnsupportedTypeException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

fun Map<*, *>.toJsonElement(): JsonElement = this
    .filterKeys { it != null }
    .filterValues { it != null }
    .map { (key, value) -> key.toString() to value!!.toJsonElement() }
    .toMap()
    .let { JsonObject(it) }

fun Map<String, *>.toJsonObject(): JsonObject =
    mapValues { entry -> entry.value.toJsonElement() }
        .run(::JsonObject)

fun Collection<*>.toJsonElement(): JsonElement = this
    .filterNotNull()
    .map { it.toJsonElement() }
    .let { JsonArray(it) }

fun Array<*>.toJsonElement(): JsonElement = this
    .filterNotNull()
    .map { it.toJsonElement() }
    .let { JsonArray(it) }

fun Any?.toJsonElement() = when (this) {
    null -> JsonNull
    is Map<*, *> -> {
        if (any { it.key !is String }) throw UnsupportedTypeException("Maps with non-string keys are not supported")
        @Suppress("UNCHECKED_CAST")
        (this as Map<String, *>).toJsonObject()
    }
    is Collection<*> -> toJsonElement()
    is Array<*> -> toJsonElement()
    is String, is Char, is Int, is Number, is Boolean -> JsonPrimitive(toString())
    else -> throw UnsupportedTypeException(this::class)
}

/**
 * This method tries to parse any type into a string to solve the problem we have with Kotlinx Serialization where this
 * is not possible. It supports a few common scenarios we need now, but it will fail for the non supported cases.
 * Please feel free to extend it adding support for a specific type you need.
 *
 * It will remove all the null values and null keys for maps.
 */
fun Json.encodeAnyToString(value: Any) = value.toJsonElement().toString()

fun JsonObject.toStringMap(): Map<String, String> =
    mapValues {
        val value = it.value as? JsonPrimitive
            ?: throw IllegalArgumentException("Only flat json objects can be cast to Map<String, String>")
        value.content
    }
