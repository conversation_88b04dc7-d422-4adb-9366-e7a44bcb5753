package energy.so.commons.json.serializers

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.math.BigDecimal

const val MAX_LATITUDE_VALUE = 90
const val MAX_LONGITUDE_VALUE = 180
const val NUMBERS_AFTER_DECIMAL = 6

class CoordinateSerializer(private val maxValue: Int) : KSerializer<BigDecimal> {
    override fun deserialize(decoder: Decoder): BigDecimal {
        val coordinate = decoder.decodeString().toBigDecimal()
        validateCoordinate(coordinate)
        return coordinate
    }

    override fun serialize(encoder: Encoder, value: BigDecimal) {
        validateCoordinate(value)
        encoder.encodeString(value.toPlainString())
    }

    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("BigDecimal", PrimitiveKind.STRING)

    private fun validateCoordinate(coordinate: BigDecimal) {
        if (coordinate !in BigDecimal(-maxValue)..BigDecimal(maxValue)) {
            throw IllegalArgumentException("Coordinate can only be between -$maxValue and $maxValue.")
        }

        if (coordinate.scale() > NUMBERS_AFTER_DECIMAL) {
            throw IllegalArgumentException("Coordinate can have at most 4 numbers after decimal.")
        }
    }
}

object LatitudeAsBigDecimalSerializer : KSerializer<BigDecimal> by CoordinateSerializer(MAX_LATITUDE_VALUE)

object LongitudeAsBigDecimalSerializer : KSerializer<BigDecimal> by CoordinateSerializer(MAX_LONGITUDE_VALUE)
