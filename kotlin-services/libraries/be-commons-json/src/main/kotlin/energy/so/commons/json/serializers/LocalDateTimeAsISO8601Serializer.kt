package energy.so.commons.json.serializers

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind.STRING
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter.ISO_DATE_TIME

object LocalDateTimeAsISO8601Serializer : KSerializer<LocalDateTime> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("timestamp", STRING)

    override fun deserialize(decoder: Decoder): LocalDateTime = LocalDateTime.parse(
        decoder.decodeString(),
        ISO_DATE_TIME
    )

    override fun serialize(encoder: Encoder, value: LocalDateTime) =
        encoder.encodeString(value.format(ISO_DATE_TIME))
}
