package energy.so.commons.automation.services

import com.google.protobuf.kotlin.toByteStringUtf8
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldHaveSize
import org.assertj.core.api.SoftAssertions.assertSoftly

class CsvExtractionServiceTest : ShouldSpec({

    val csvExtractionService = CsvExtractionService()
    val csvFile: String = """
        SAMPLE_COL_1,SAMPLE_COL_2
        sample1,sample2
    """.trimIndent()

    context("::extract") {
        val extractedRecords = arrayListOf<SampleCsvRecord>()

        should("Parse the csv file and get the list of SampleCsvRecord objects when receiving csv file") {
            val expected = SampleCsvRecord("sample1", "sample2")
            csvExtractionService.extract<SampleCsvRecord>(
                csvFile.toByteStringUtf8().newInput(),
                onCollect = {
                    extractedRecords.add(it)
                },
                onComplete = {},
                SampleCsvRecord.keyMap()
            )

            assertSoftly {
                extractedRecords shouldHaveSize 1
                extractedRecords shouldContain expected
            }
        }
    }
})

data class SampleCsvRecord(
    val sample1: String,
    val sample2: String,
) {
    companion object
}

fun SampleCsvRecord.Companion.keyMap() = mapOf(
    "SAMPLE_COL_1" to "sample1",
    "SAMPLE_COL_2" to "sample2",
)
