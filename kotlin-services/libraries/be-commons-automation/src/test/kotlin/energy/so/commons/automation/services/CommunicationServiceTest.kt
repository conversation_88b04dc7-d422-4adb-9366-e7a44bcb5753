package energy.so.commons.automation.services

import energy.so.commons.automation.config.CommunicationConfig
import energy.so.commons.automation.dtos.CommunicationDto
import energy.so.commons.automation.v2.models.TaskResultResponseKt.taskResult
import energy.so.commons.automation.v2.models.taskResultResponse
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationMessageDto
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.random.Random
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class CommunicationServiceTest {

    private val communicationClient = mockk<CommunicationClient>()
    private val results = taskResultResponse {
        results.add(taskResult { data.add("1") })
        fields.add("ID")
    }
    private val emailList = listOf(
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"

    )
    private val cancelAccountReviewPeriodConfig = CommunicationConfig(
        communicationEmailList = emailList.joinToString(", "),
        communicationName = "automated_script_cancellation_review_period",
        communicationSubject = "Account review periods cancellation process finished running",
        communicationHtmlHeader = "some header"
    )

    private val createAccountReviewPeriodConfig = CommunicationConfig(
        communicationEmailList = emailList.joinToString(", "),
        communicationName = "automated_script_cancellation_review_period",
        communicationSubject = "Account review periods cancellation process finished running",
        communicationHtmlHeader = "some header"
    )

    private val updateFinalBillDunningTicketsConfig = CommunicationConfig(
        communicationEmailList = emailList.joinToString(", "),
        communicationName = "automated_script_update_final_bill_dunning_tickets",
        communicationSubject = "Update final bill dunning tickets process finished running",
        communicationHtmlHeader = "some header"
    )

    private val meterExchangeReviewPeriodConfig = CommunicationConfig(
        communicationEmailList = emailList.joinToString(", "),
        communicationName = "automated_script_meter_exchange_account_reviews",
        communicationSubject = "meter exchange account reviews finished running",
        communicationHtmlHeader = "some header"
    )

    private val communicationDto = CommunicationDto(
        dataset = results,
        elapsedTime = Random.nextInt()
    )

    @Test
    fun `cancelAccountReviewPeriodCommunication uses the correct emails to send communication`() = runBlocking {

        //GIVEN
        val slot = mutableListOf<CommunicationMessageDto>()
        coEvery { communicationClient.sendCommunicationMessage(capture(slot)) } returns mockk()

        //WHEN
        CommunicationService(communicationClient = communicationClient)
            .processCommunication(communicationDto, cancelAccountReviewPeriodConfig)

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
        assertThat(slot.map { it.recipient.email }).containsAll(cancelAccountReviewPeriodConfig.emailList)

        slot.forEach {
            it.communicationName shouldBe cancelAccountReviewPeriodConfig.communicationName
            it.subject shouldBe cancelAccountReviewPeriodConfig.communicationSubject
        }
    }

    @Test
    fun `sendCommunicationMessage throws exception`() = runBlocking {
        //GIVEN
        val exception = Exception("There was an error")
        coEvery { communicationClient.sendCommunicationMessage(any()) } throws exception andThenMany
                List(2) { mockk() } andThenThrows exception
        //WHEN
        shouldNotThrow<Exception> {
            CommunicationService(communicationClient = communicationClient)
                .processCommunication(communicationDto, cancelAccountReviewPeriodConfig)
        }

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
    }

    @Test
    fun `createAccountReviewPeriodCommunication uses the correct emails to send communication`() = runBlocking {

        //GIVEN
        val slot = mutableListOf<CommunicationMessageDto>()
        coEvery { communicationClient.sendCommunicationMessage(capture(slot)) } returns mockk()

        //WHEN
        CommunicationService(communicationClient = communicationClient)
            .processCommunication(communicationDto, cancelAccountReviewPeriodConfig)

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
        assertThat(slot.map { it.recipient.email }).containsAll(cancelAccountReviewPeriodConfig.emailList)

        slot.forEach {
            it.communicationName shouldBe cancelAccountReviewPeriodConfig.communicationName
            it.subject shouldBe cancelAccountReviewPeriodConfig.communicationSubject
        }
    }

    @Test
    fun `sendCommunicationMessage throws exception when creatingAccountReviewPeriods`() = runBlocking {
        //GIVEN
        val exception = Exception("There was an error")
        coEvery { communicationClient.sendCommunicationMessage(any()) } throws exception andThenMany
                List(3) { mockk() }

        //WHEN
        shouldNotThrow<Exception> {
            CommunicationService(communicationClient = communicationClient)
                .processCommunication(communicationDto, createAccountReviewPeriodConfig)
        }

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
    }

    @Test
    fun `updateFinalBillDunningTickets uses the correct emails to send communication`() = runBlocking {

        //GIVEN
        val slot = mutableListOf<CommunicationMessageDto>()
        coEvery { communicationClient.sendCommunicationMessage(capture(slot)) } returns mockk()

        //WHEN
        CommunicationService(communicationClient = communicationClient)
            .processCommunication(communicationDto, updateFinalBillDunningTicketsConfig)

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
        assertThat(slot.map { it.recipient.email }).containsAll(updateFinalBillDunningTicketsConfig.emailList)

        slot.forEach {
            it.communicationName shouldBe updateFinalBillDunningTicketsConfig.communicationName
            it.subject shouldBe updateFinalBillDunningTicketsConfig.communicationSubject
        }
    }

    @Test
    fun `sendCommunicationMessage throws exception when updateFinalBillDunningTickets`() = runBlocking {
        //GIVEN
        val exception = Exception("There was an error")
        coEvery { communicationClient.sendCommunicationMessage(any()) } throws exception andThenMany
                List(3) { mockk() }

        //WHEN
        shouldNotThrow<Exception> {
            CommunicationService(communicationClient = communicationClient)
                .processCommunication(communicationDto, updateFinalBillDunningTicketsConfig)
        }

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
    }

    @Test
    fun `meterExchangeAccountReviewPeriods uses the correct emails to send communication`() = runBlocking {

        //GIVEN
        val slot = mutableListOf<CommunicationMessageDto>()
        coEvery { communicationClient.sendCommunicationMessage(capture(slot)) } returns mockk()

        //WHEN
        CommunicationService(communicationClient = communicationClient)
            .processCommunication(communicationDto, meterExchangeReviewPeriodConfig)

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
        assertThat(slot.map { it.recipient.email }).containsAll(meterExchangeReviewPeriodConfig.emailList)

        slot.forEach {
            it.communicationName shouldBe meterExchangeReviewPeriodConfig.communicationName
            it.subject shouldBe meterExchangeReviewPeriodConfig.communicationSubject
        }
    }

    @Test
    fun `sendCommunicationMessage throws exception when meterExchangeAccountReviewPeriods`() = runBlocking {
        //GIVEN
        val exception = Exception("There was an error")
        coEvery { communicationClient.sendCommunicationMessage(any()) } throws exception andThenMany
                List(3) { mockk() }

        //WHEN
        shouldNotThrow<Exception> {
            CommunicationService(communicationClient)
                .processCommunication(communicationDto, meterExchangeReviewPeriodConfig)
        }

        //THEN
        coVerify(exactly = 4) { communicationClient.sendCommunicationMessage(any()) }
    }
}
