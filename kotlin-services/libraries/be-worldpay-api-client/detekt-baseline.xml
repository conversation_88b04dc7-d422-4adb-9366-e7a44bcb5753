<?xml version="1.0" ?>
<SmellBaseline>
  <ManuallySuppressedIssues></ManuallySuppressedIssues>
  <CurrentIssues>
    <ID>MagicNumber:WorldpayClient.kt$WorldpayClient$99</ID>
    <ID>MaxLineLength:WorldpayClient.kt$WorldpayClient$logger.error { "[order_code:$orderCode][error_code:${error.code}] Request authorisation failed: ${error.message}" }</ID>
    <ID>MaxLineLength:WorldpayClient.kt$WorldpayClient$private</ID>
    <ID>MaximumLineLength:energy.so.worldpay.client.WorldpayClient.kt:115</ID>
    <ID>MaximumLineLength:energy.so.worldpay.client.WorldpayClient.kt:74</ID>
    <ID>MaximumLineLength:energy.so.worldpay.client.WorldpayClient.kt:90</ID>
    <ID>MaximumLineLength:energy.so.worldpay.client.WorldpayClient.kt:99</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayClient.kt:16</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayClient.kt:17</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayClient.kt:18</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayClient.kt:19</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayClient.kt:20</ID>
    <ID>NoWildcardImports:energy.so.worldpay.client.WorldpayXmlMapper.kt:12</ID>
    <ID>ReturnCount:WorldpayClient.kt$WorldpayClient$private suspend fun makePaymentRequest( authoriseRequest: AuthorisePaymentServiceRequest, cookieHeader: String? = null ): CardPaymentResponse</ID>
    <ID>TooManyFunctions:WorldpayClient.kt$WorldpayClient</ID>
    <ID>UnnecessaryAbstractClass:AbstractCardPaymentRequest.kt$AbstractCardPaymentRequest</ID>
    <ID>WildcardImport:WorldpayClient.kt$import io.ktor.client.*</ID>
    <ID>WildcardImport:WorldpayClient.kt$import io.ktor.client.request.*</ID>
    <ID>WildcardImport:WorldpayClient.kt$import io.ktor.client.statement.*</ID>
    <ID>WildcardImport:WorldpayClient.kt$import io.ktor.http.*</ID>
    <ID>WildcardImport:WorldpayClient.kt$import io.ktor.http.content.*</ID>
    <ID>WildcardImport:WorldpayXmlMapper.kt$import io.ktor.client.statement.*</ID>
  </CurrentIssues>
</SmellBaseline>
