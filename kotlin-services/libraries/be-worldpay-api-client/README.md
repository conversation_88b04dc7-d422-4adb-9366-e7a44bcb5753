# Worldpay XML API  Client

This is the Direct Integration of Worldpay XML API for Java ecosystem. It covers Worldpay's payment flow.

### Scope
This integration contains Worldpay authorisation and capture calls for Direct Integration. Please see https://developer.worldpay.com/docs/wpg/reference/thepaymentprocess for Worldpay's payment process reference.

### Usage
To make a payment, create an instance of `WorldpayClient` class, create a `MakePaymentRequest` object with the necessary information and call `WorldpayClient.makeCardPayment` method.

### Example code
```kotlin
val request = CardPaymentRequest(
                "4312365",
                500,
                "GBP",
                "description",
                CustomerCard(
                        "****************",
                        "Sample name",
                        LocalDate.now().plusYears(5),
                        Customer<PERSON>ddress(
                                "123 Sample Avenue",
                                null,
                                null,
                                "AB12 3CD",
                                "Sample City",
                                "Sample State",
                                "GB")
                ),
                "<EMAIL>", null, null)

runBlocking {
    val response = WorldpayClient(WorldpayEnv.TEST, "MERCHANTCODE", "XML_PASSWORD", "INSTALLATION_ID")
                .makeCardPayment(request)
}
```

### Testing
You can test different scenarios in the `Test` mode by using the following values as <PERSON><PERSON>'s name
- `AUTHORISED` - successful authorisation response
- `REFUSED` - authorisation refused by Worldpay
- `ERROR` - error response due to f.e. ill-formed request

For more information about testing please see https://developer.worldpay.com/docs/wpg/directintegration/abouttesting

### Resources
1. https://developer.worldpay.com/docs/wpg/directintegration/quickstart