package energy.so.aes.client.v1.impl

import energy.so.aes.client.v1.AESClient
import energy.so.aes.client.v1.models.*
import energy.so.aes.client.v1.models.beboc.*
import energy.so.commons.httpclient.HttpClientProvider
import energy.so.commons.httpclient.TracingHttpClient
import energy.so.commons.httpclient.invoke
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.session.SessionManager
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json

private val logger = TraceableLogging.logger {}

class BebocAesClient internal constructor(
    private val httpClient: HttpClient,
    private val baseUrl: String = "https://aes.staging.nexus-api.reachwm.io",
) : AESClient {

    override suspend fun availability(request: BebocAvailabilityRequest): BebocAvailabilityResponse {
        return httpClient.get("$baseUrl/$CLIENT_PATH/slots/capacity") {
            url {
                parameters.append("from", request.start.toString())
                parameters.append("to", request.end.toString())
                parameters.append("postcode", request.postcode)
                parameters.append("supplierConstant", request.supplierConstant)
            }
        }.body()
    }

    override suspend fun bookJob(request: BookImmediateRequest): BookJobResponse {
        logger.info { "Beboc API book-immediate request  : $request" }
        val response = httpClient.post("$baseUrl/$CLIENT_PATH/appointments/book-immediate") {
            contentType(ContentType.Application.Json)
            setBody(request)
        }.body<String>()

        logger.info { "Beboc API book-immediate response  : $response" }
        return if (response.contains("error")) {
            val errorResponse = Json.decodeFromString<BookImmediateErrorResponse>(response)
            BookJobResponse(
                status = ResponseStatus.FAIL,
                statusCode = errorResponse.error.code,
                message = errorResponse.error.description
            )
        } else {
            val successResponse = Json {
                isLenient = true
                ignoreUnknownKeys = true
            }.decodeFromString<BookImmediateResponse>(response)
            BookJobResponse(
                status = ResponseStatus.SUCCESS,
                statusCode = 200,
                data = BookJobNumberResponse(
                    jobNumber = successResponse.result.bookingReference
                ),
                message = "Booked successfully"
            )
        }
    }

    override suspend fun cancelJob(request: CancelJobRequest): CancelJobResponse =
        httpClient.post("$baseUrl/$CLIENT_PATH/appointments/cancel") {
            contentType(ContentType.Application.Json)
            setBody(request)
        }.body<GenericBebocResponse>().toAESCancelJobResponse()

    override suspend fun updateJob(request: UpdateJobRequest): UpdateJobResponse {
        logger.info { "Beboc API updateJob reschedule request  : ${request.toBebocUpdateJobRequest()}" }
        val response = httpClient.post("$baseUrl/$CLIENT_PATH/appointments/reschedule") {
            contentType(ContentType.Application.Json)
            setBody(request.toBebocUpdateJobRequest())
        }.body<BebocUpdateJobResponse>().toUpdateJobResponse()
        logger.info { "Beboc API updateJob reschedule response  : $response" }
        return response
    }

    override suspend fun jobDetails(request: JobDetailsRequest): JobDetailsResponse {
        TODO("Not yet implemented")
    }

    override suspend fun cancellationReasons(): CancellationReasonsResponse {
        TODO("Not yet implemented")
    }

    override suspend fun getElecLocations(): GetLocationsResponse {
        TODO("Not yet implemented")
    }

    override suspend fun getGasLocations(): GetLocationsResponse {
        TODO("Not yet implemented")
    }

    override suspend fun getElecTypes(): GetElecTypesResponse {
        TODO("Not yet implemented")
    }

    override suspend fun getGasTypes(): GetGasTypesResponse {
        TODO("Not yet implemented")
    }

    override suspend fun reserveSlot(request: ReserveSlotAESRequest): ReserveSlotAESResponse {
        return httpClient.post("$baseUrl/$CLIENT_PATH/appointments/reserve") {
            contentType(ContentType.Application.Json)
            if(SessionManager.shouldMockJunifer()) {
                header("is-mock", "true")
            }
            setBody(request)
        }.body()
    }

    override suspend fun bookReservation(request: BookReservationAESRequest): BookReservationAESResponse {
        logger.info { "Sending AES book reservation request: $request" }
        return httpClient.post("$baseUrl/$CLIENT_PATH/appointments/book-reservation") {
            contentType(ContentType.Application.Json)
            setBody(request)
        }.body()
    }

    companion object {
        const val CLIENT_PATH = "v1/api/public"
        var apiToken = ""
    }
}

/**
 * Returns an instance of [BebocAesClient] with a CIO engine
 */
suspend operator fun BebocAesClient.Companion.invoke(
    authUrl: String = "https://staging.hub.reachwm.io",
    apiUrl: String = "https://aes.staging.nexus-api.reachwm.io",
    email: String,
    secret: String,
    requestTimeoutMillis: Long,
    connectionTimeoutMillis: Long,
    keepAliveTime: Long? = 5000,
    connectAttempts: Int? = 4,
): BebocAesClient = BebocAesClient(
    authUrl,
    apiUrl,
    email,
    secret,
    requestTimeoutMillis,
    connectionTimeoutMillis,
    CIO.config {
        endpoint.keepAliveTime = keepAliveTime!!
        endpoint.connectAttempts = connectAttempts!!
    }
)

/**
 * Returns an instance of [BebocAesClient] with specified engine, enabling MockEngine testing
 */
suspend operator fun <T : HttpClientEngineConfig> BebocAesClient.Companion.invoke(
    authUrl: String = "https://staging.hub.reachwm.io",
    apiUrl: String = "https://aes.staging.nexus-api.reachwm.io",
    email: String,
    secret: String,
    requestTimeout: Long,
    connectionTimeout: Long,
    engine: HttpClientEngineFactory<T>,
): BebocAesClient {
    val client = HttpClientProvider(engine) {
        install(HttpTimeout) {
            requestTimeoutMillis = requestTimeout
            connectTimeoutMillis = connectionTimeout
        }
        install(ContentNegotiation) {
            json(
                json = Json {
                    ignoreUnknownKeys = true
                    coerceInputValues = true
                }
            )
        }

        install(Auth)
        install(TracingHttpClient)
        expectSuccess = false
    }.client

    client.plugin(Auth).bearer {
        refreshTokens {
            apiToken = client.post("$authUrl/api/authenticate") {
                header("Content-Type", "application/json")
                setBody(
                    BebocAuthRequest(
                        email = email,
                        password = secret
                    )
                )
            }.body<BebocAuthResponse>().token
            BearerTokens(apiToken, apiToken)
        }
    }

    return BebocAesClient(client, apiUrl)
}

