package energy.so.commons.monitoring

import com.bugsnag.Bugsnag
import com.bugsnag.Report
import energy.so.commons.monitoring.config.MonitorConfig
import mu.KLogging

object ErrorMonitor : KLogging() {
    private val handler: Bugsnag

    init {
        try {
            MonitorConfig.fromApplicationConf().run {
                logger.info { "Initializing Bugsnag on environment $environment for API Version $apiVersion" }
                handler = Bugsnag(monitorPlatformKey)
                handler.setAppVersion(apiVersion)
                handler.setReleaseStage(environment)
                logger.info { "Bugsnag up and running" }
            }
        } catch (e: Exception) {
            logger.error(e) { "Cannot initialise Bugsnag" }
            throw e
        }
    }

    fun notify(exception: Throwable): <PERSON><PERSON><PERSON> = handler.notify(exception)

    fun setUserId(userId: String) {
        handler.addCallback { report: Report ->
            report.setUserId(userId)
        }
    }
}
