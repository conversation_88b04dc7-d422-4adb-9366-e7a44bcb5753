package energy.so.commons.monitoring.config

import com.typesafe.config.ConfigFactory
import io.github.config4k.extract
import java.io.File

data class MonitorConfig(
    val monitorPlatformKey: String,
    val environment: String,
    var apiVersion: String? = null
) {
    companion object {
        fun fromApplicationConf(): MonitorConfig {
            val env = System.getenv("ENV") ?: "dev"
            val baseline = ConfigFactory.load()

            return ConfigFactory.load("application-$env")
                .withFallback(baseline)
                .extract<MonitorConfig>("monitoring")
                .apply { apiVersion = apiVersion ?: getApiVersion() }
        }

        private fun getApiVersion() = File("VERSION").takeIf { it.exists() }?.readText()?.trim()
    }
}
