plugins {
    id("energy.so.be-commons-conventions-library")
}

dependencies {
    api(libs.ktor.client.core)

    implementation(kotlin("reflect"))
    implementation(libs.ktor.client.cio)
    implementation(libs.ktor.client.serialization)
    implementation(libs.ktor.client.mock)
    implementation(libs.ktor.client.json)
    implementation(libs.ktor.client.content.negotiation)
    implementation(libs.ktor.serialization.kotlinx)
    implementation(libs.ktor.client.logging)
    implementation(libs.opentelemetry.ktor)

    testImplementation(libs.kotest.runner.junit5)
    testImplementation(libs.kotest.assertions.core)

    implementation(project(":be-commons-logging"))
    implementation(project(":be-commons-exceptions"))
}
