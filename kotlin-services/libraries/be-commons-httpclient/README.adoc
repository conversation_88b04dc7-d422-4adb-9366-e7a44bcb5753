= Be-Commons Http Client

== How to use this?

If your project uses `be-commons-conventions`, it will be loaded as a library you can use.
Simply declare it as a dependencies:

----
dependencies {
    implementation(project(":be-commons-httpclient"))
}
----

=== Using the client

.Invoking the `+HttpClientProvider+` is done by the following:
* `+HttpClientProvider()+` will return a provider with a CIO engine and default configuration.
The default CIO configuration is found inside the `+CIOConfiguration+` files.
It uses the default argument values of the data class
`+CIOHttpClientConfiguration+`

* `+HttpClientProvider(engineConfig)+` returns an instance of
`+HttpClientProvider+` with CIO engine.
The `+engineConfig+` block configures the CIO engine


* `+HttpClientProvider(config)+` returns an instance with CIO engine configured by `+defaultCIOEngineConfig+` and `+config+` provided

* `+HttpClientProvider(config, CIOEngineConfig)+` returns an instance with CIO engine configured by
`+defaultCIOEngineConfig()+` and `+config+` settings.
Invoking `+HttpClientProvider+` this way allows you to furhter specify configuration with the `+CIOEngineConfig+` block, for example if you need to install a plugin on the engine

* `+HttpClientProvider(engine, engineConfig)+` will return an instance of `+HttpClientProvider+` with a provided engine (a mock engine used in tests for example) configured with the `+engineConfig+` block

To use the client, create the desired instance of `+HttpClientProvider+` and retrieve the client through the instance's `+client+` attribute i.e.

----
val httpClient = HttpClientProvider().client
----
