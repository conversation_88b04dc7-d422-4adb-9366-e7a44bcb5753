package energy.so.commons.search.extension

import energy.so.commons.search.model.filter.Pagination
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/** <AUTHOR> 16/02/2022
 */
class PaginationExtensionTest {

    @Test
    fun `given page number when get offset then return expected`() {
        val expected = 20
        val pagination = Pagination(pageSize = 10, pageNumber = 3)
        val offset = pagination.getOffset()
        assertThat(offset).isEqualTo(expected)
    }

    @Test
    fun `given zero page number when get offset then return zero`() {
        val pagination = Pagination(pageSize = 10, pageNumber = 0)
        val offset = pagination.getOffset()
        assertThat(offset).isZero
    }

    @Test
    fun `given negative page number when get offset then return zero`() {
        val pagination = Pagination(pageSize = 10, pageNumber = -3)
        val offset = pagination.getOffset()
        assertThat(offset).isZero
    }
}
