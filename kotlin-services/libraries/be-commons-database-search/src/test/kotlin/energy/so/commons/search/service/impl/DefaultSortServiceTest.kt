import energy.so.commons.database.test.public.tables.references.LANGUAGE
import energy.so.commons.search.exception.TableFieldNotFoundException
import energy.so.commons.search.model.filter.ConditionalSort
import energy.so.commons.search.model.filter.ConditionalSortBranch
import energy.so.commons.search.model.filter.Direction
import energy.so.commons.search.model.filter.SortAsc
import energy.so.commons.search.model.filter.SortDesc
import energy.so.commons.search.service.SortService
import energy.so.commons.search.service.impl.DefaultSortService
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.collections.shouldContainExactly
import org.jooq.Field
import org.jooq.SortOrder
import org.jooq.impl.DSL

/** <AUTHOR> 16/02/2022
 */
class DefaultSortServiceTest : ShouldSpec({
    val sut: SortService = DefaultSortService(LANGUAGE)

    context("Given a list of basic sorts") {
        should("return expected jooq sorts") {
            val sorts = listOf(
                SortAsc(LANGUAGE.DATE.name),
                SortDesc(LANGUAGE.STRING.name)
            )

            val result = sut.start(sorts)

            result.shouldContainExactly(LANGUAGE.DATE.asc(), LANGUAGE.STRING.desc())
        }
    }

    context("Given a field that doesn't exist") {
        should("throw TableFieldNotFoundException") {
            val sorts = listOf(SortAsc("nonexistent_field"))

            shouldThrow<TableFieldNotFoundException> {
                sut.start(sorts)
            }
        }
    }

    context("Given a ConditionalSort") {
        should("apply all branch sorts unconditionally") {
            val conditional = ConditionalSort(
                conditionField = LANGUAGE.STATUS.name,
                branches = listOf(
                    ConditionalSortBranch(
                        conditionFieldValue = "PENDING",
                        sortField = LANGUAGE.INT.name
                    ),
                    ConditionalSortBranch(
                        conditionFieldValue = "ACCEPTED",
                        sortField = LANGUAGE.INT.name
                    )
                ),
                direction = Direction.DESC
            )

            val result = sut.start(listOf(conditional))

            val value = LANGUAGE.field(LANGUAGE.STATUS.name) as Field<String>

            val expected = DSL.case_(value).`when`("PENDING" , LANGUAGE.INT)
                .`when`("ACCEPTED", LANGUAGE.INT).sort(
                    SortOrder.DESC
                )

            result.shouldContainExactly(
                expected
            )
        }
    }
})