package energy.so.commons.search

import energy.so.commons.search.model.filter.AndFilter
import energy.so.commons.search.model.filter.BetweenFilter
import energy.so.commons.search.model.filter.ContainsFilter
import energy.so.commons.search.model.filter.EndsWithFilter
import energy.so.commons.search.model.filter.EqFilter
import energy.so.commons.search.model.filter.GtFilter
import energy.so.commons.search.model.filter.InFilter
import energy.so.commons.search.model.filter.LtFilter
import energy.so.commons.search.model.filter.NotFilter
import energy.so.commons.search.model.filter.OrFilter
import energy.so.commons.search.model.filter.Pagination
import energy.so.commons.search.model.filter.SearchDto
import energy.so.commons.search.model.filter.SortAsc
import energy.so.commons.search.model.filter.SortDesc
import energy.so.commons.search.model.filter.StartsWithFilter
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.datatest.withData
import io.kotest.inspectors.forSingle
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.DisplayName

@DisplayName("InMemorySearchService Test")
internal class InMemorySearchServiceTest : ShouldSpec({
    val data = mutableListOf<String>()
    val subject = InMemorySearchService(data) { it }
    beforeTest {
        data.clear()
    }

    context("::search") {
        context("Given one piece of data in the search service") {
            val testData = "TestData"
            beforeTest {
                data.add(testData)
            }

            context("When that data is searched for") {
                withData(
                    nameFn = { "using ${it::class.simpleName}, then it's returned in the results" },
                    EqFilter("", testData),
                    AndFilter(listOf(EqFilter("", testData), EqFilter("", testData))),
                    OrFilter(listOf(EqFilter("", testData), EqFilter("", testData))),
                    NotFilter(EqFilter("", "notTestData")),
                    BetweenFilter("", "RestData", "VestData"),
                    ContainsFilter("", "Data"),
                    GtFilter("", "RestData"),
                    LtFilter("", "VestData"),
                    EndsWithFilter("", "Data"),
                    StartsWithFilter("", "Test"),
                    InFilter("", listOf("TestData", "RestData", "VestData"))
                ) { filter ->
                    val searchExist = SearchDto(filters = listOf(filter))

                    val result = subject.search(searchExist)

                    result.forSingle {
                        it shouldBe testData
                    }
                }
            }

            should("When something different is searched for, then nothing is returned") {
                val searchDoesntExist = SearchDto(filters = listOf(EqFilter("", "TestDataNotExisting")))

                val result = subject.search(searchDoesntExist)

                result.shouldBeEmpty()
            }

            context("And more data is added in") {
                val aTestData = "ATestData"
                beforeTest {
                    data.add(aTestData)
                }
                should("When that data is sorted ascending, then it's returned in the ascending direction") {
                    val sortAsc = SearchDto(sorting = listOf(SortAsc("")))

                    val result = subject.search(sortAsc)

                    result.shouldContainInOrder(listOf(aTestData, testData))
                }

                should("When that data is sorted descending, then it's returned in the descending direction") {
                    val sortDesc = SearchDto(sorting = listOf(SortDesc("")))

                    val result = subject.search(sortDesc)

                    result.shouldContainInOrder(listOf(testData, aTestData))
                }

                should("When that data is paged, only the request page is returned") {
                    val pagedOneResult = SearchDto(pagination = Pagination(0, 1))

                    val result = subject.search(pagedOneResult)

                    result.shouldContainExactly(aTestData)
                }
            }
        }
    }
})
