package energy.so.commons.search.service.impl

import energy.so.commons.search.InMemorySearchService
import energy.so.commons.search.service.search
import energy.so.commons.v2.search.eqFilter
import energy.so.commons.v2.search.filter
import energy.so.commons.v2.search.pagination
import energy.so.commons.v2.search.searchRequest
import energy.so.commons.v2.search.searchResponseMetadata
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe

internal class DatabaseSearchServiceExtensionsTest : ShouldSpec({
    val stringSearch = InMemorySearchService(mutableListOf("Hello")) { it }

    context("::search") {
        should("""when searching for "hello", the result should contain only "hello"""") {
            val expectedValue = "Hello"
            val expectedPageNumber = 0
            val expectedPageSize = 1

            val validCriteria = searchRequest {
                filter = filter {
                    eqFilter = eqFilter {
                        value = expectedValue
                    }
                }
                pagination = pagination {
                    pageNumber = expectedPageNumber
                    pageSize = expectedPageSize
                }
            }

            val result = stringSearch.search(
                validCriteria,
                mapper = { it },
            ) { results, metadata ->
                results to metadata
            }

            assertSoftly(result) {
                first shouldBe listOf(expectedValue)
                second shouldBe searchResponseMetadata {
                    pageSize = expectedPageSize
                    pageNumber = expectedPageNumber
                }
            }
        }
        should("""when searching for "hello", the result should contain only "hello and include totals"""") {
            val expectedValue = "Hello"
            val expectedPageNumber = 0
            val expectedPageSize = 1
            val expectedTotalElements = 1
            val expectedTotalPages = 1

            val validCriteria = searchRequest {
                filter = filter {
                    eqFilter = eqFilter {
                        value = expectedValue
                    }
                }
                pagination = pagination {
                    pageNumber = expectedPageNumber
                    pageSize = expectedPageSize
                }
                includeTotalElements = true
            }

            val result = stringSearch.search(
                validCriteria,
                mapper = { it },
            ) { results, metadata ->
                results to metadata
            }

            assertSoftly(result) {
                first shouldBe listOf(expectedValue)
                second shouldBe searchResponseMetadata {
                    pageSize = expectedPageSize
                    pageNumber = expectedPageNumber
                    totalPages = expectedTotalPages
                    totalElements = expectedTotalElements
                }
            }
        }
    }
})
