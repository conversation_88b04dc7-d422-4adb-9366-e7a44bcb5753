package energy.so.commons.search.filters

import energy.so.commons.database.test.public.tables.references.LANGUAGE
import energy.so.commons.search.model.filter.BetweenFilter
import energy.so.commons.search.model.filter.EqFilter
import energy.so.commons.search.model.filter.InFilter
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.datatest.withData
import io.kotest.matchers.shouldBe
import org.jooq.Condition
import org.jooq.Field
import java.lang.IllegalArgumentException
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset

class ParsedFilterTestParams<T>(
    val value: T,
    val field: Field<T>,
) {
    val expectedValueCondition: Condition = field.eq(value)
    val expectedRangeCondition: Condition = field.between(value, value)
    val expectedValuesCondition: Condition = field.`in`(listOf(value))
}

internal class ParsedFilterTests : ShouldSpec({
    context("Given a value that is a type we handle for searching") {

        withData(
            nameFn = { "Then value ${it.value} is handled by column of type ${it.field.dataType}" },
            ParsedFilterTestParams("A String", LANGUAGE.STRING),
            ParsedFilterTestParams(LocalDate.of(1, 1, 1), LANGUAGE.DATE),
            ParsedFilterTestParams(OffsetDateTime.ofInstant(Instant.EPOCH, ZoneOffset.UTC), LANGUAGE.TIMESTAMP),
            ParsedFilterTestParams(567L, LANGUAGE.LONG),
            ParsedFilterTestParams(5672, LANGUAGE.INT),
            ParsedFilterTestParams(5617.toShort(), LANGUAGE.SHORT),
            ParsedFilterTestParams(5767.toByte(), LANGUAGE.BYTE),
            ParsedFilterTestParams(5467.0, LANGUAGE.DOUBLE),
            ParsedFilterTestParams(5678.0f, LANGUAGE.FLOAT),
            ParsedFilterTestParams(BigInteger.TEN, LANGUAGE.BIG_INTEGER),
            ParsedFilterTestParams(BigDecimal.TEN, LANGUAGE.BIG_DECIMAL),
            ParsedFilterTestParams(false, LANGUAGE.BOOLEAN),
        ) { params ->
            val eqFilter = EqFilter(params.field.name, "${params.value}")
            val betweenFilter = BetweenFilter(params.field.name, "${params.value}", "${params.value}")
            val inFilter = InFilter(params.field.name, listOf("${params.value}"))

            val valueCondition = ParsedValueFilter(eqFilter, params.field).toCondition()
            val rangeCondition = ParsedRangeFilter(betweenFilter, params.field).toCondition()
            val valuesCondition = ParsedValuesFilter(inFilter, params.field).toCondition()

            assertSoftly {
                valueCondition shouldBe params.expectedValueCondition
                rangeCondition shouldBe params.expectedRangeCondition
                valuesCondition shouldBe params.expectedValuesCondition
            }
        }
    }

    context("Given a value we do not handle for searching") {
        should("Then throw an exception") {
            val field = LANGUAGE.JSON
            val value = "{}"
            val eqFilter = EqFilter(field.name, value)
            val betweenFilter = BetweenFilter(field.name, value, value)
            assertSoftly {
                shouldThrow<IllegalArgumentException> { ParsedValueFilter(eqFilter, field) }
                shouldThrow<IllegalArgumentException> { ParsedRangeFilter(betweenFilter, field) }
            }
        }
    }
})
