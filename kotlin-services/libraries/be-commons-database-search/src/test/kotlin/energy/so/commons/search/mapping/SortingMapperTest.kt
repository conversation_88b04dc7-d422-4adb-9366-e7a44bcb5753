package energy.so.commons.search.mapping

import energy.so.commons.search.model.filter.ConditionalSortBranch
import energy.so.commons.search.model.filter.SortAsc
import energy.so.commons.search.model.filter.SortDesc
import energy.so.commons.search.model.mapping.mapConditionalSort
import energy.so.commons.search.model.mapping.mapSimpleSort
import energy.so.commons.search.model.mapping.mapSorting
import energy.so.commons.v2.search.Direction
import energy.so.commons.v2.search.conditionBranch
import energy.so.commons.v2.search.conditionalSort
import energy.so.commons.v2.search.simpleSort
import energy.so.commons.v2.search.sort
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class SortingMapperTest : BehaviorSpec({

    given("a SimpleSort") {
        `when`("direction is Ascending") {
            then("it should map to SortAsc") {
                val simpleSort = simpleSort {
                    field = "name"
                    direction = Direction.Ascending
                }

                mapSimpleSort(simpleSort) shouldBe SortAsc("name")
            }
        }

        `when`("direction is Descending") {
            then("it should map to SortDesc") {
                val simpleSort = simpleSort {
                    field = "name"
                    direction = Direction.Descending
                }

                mapSimpleSort(simpleSort) shouldBe SortDesc("name")
            }
        }
    }

    given("a ConditionalSort") {
        then("it should map all branches and their sorts") {
            val conditionalSort = conditionalSort {
                conditionField = "status"

                branches += conditionBranch {
                    conditionFieldValue = "PENDING"
                    sortField = "createdAt"
                }

                branches += conditionBranch {
                    conditionFieldValue = "ACCEPTED"
                    sortField = "acceptedAt"
                }

                direction = Direction.Ascending
            }

            mapConditionalSort(conditionalSort) shouldBe energy.so.commons.search.model.filter.ConditionalSort(
                conditionField = "status",
                branches = listOf(
                    ConditionalSortBranch("PENDING", "createdAt"),
                    ConditionalSortBranch("ACCEPTED", "acceptedAt")
                ),
                direction = energy.so.commons.search.model.filter.Direction.ASC
            )
        }
    }

    given("a Sort proto wrapper") {

        `when`("it contains a SimpleSort") {
            then("it should map to SortAsc") {
                val sort = sort {
                    simpleSort = simpleSort {
                        field = "id"
                        direction = Direction.Ascending
                    }
                }

                mapSorting(sort) shouldBe SortAsc("id")
            }
        }

        `when`("it contains a ConditionalSort") {
            then("it should map correctly") {
                val sort = sort {
                    conditionalSort = conditionalSort {
                        conditionField = "status"
                        branches += conditionBranch {
                            conditionFieldValue = "DONE"
                            sortField = "doneAt"
                        }
                        direction = Direction.Descending
                    }
                }

                mapSorting(sort) shouldBe energy.so.commons.search.model.filter.ConditionalSort(
                    conditionField = "status",
                    branches = listOf(
                        ConditionalSortBranch("DONE", "doneAt")
                    ),
                    direction = energy.so.commons.search.model.filter.Direction.DESC
                )
            }
        }

        `when`("it contains none of the supported types") {
            then("it should throw IllegalArgumentException") {
                val sort = sort {} // empty sort

                shouldThrow<IllegalArgumentException> {
                    mapSorting(sort)
                }
            }
        }
    }
})
