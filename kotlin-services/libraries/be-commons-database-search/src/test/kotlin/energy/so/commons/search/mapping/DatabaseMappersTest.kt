package energy.so.commons.search.mapping

import energy.so.commons.search.model.filter.EqFilter
import energy.so.commons.search.model.filter.Pagination
import energy.so.commons.search.model.filter.SortAsc
import energy.so.commons.search.model.filter.SortDesc
import energy.so.commons.search.model.mapping.mapPagination
import energy.so.commons.search.model.mapping.mapSorting
import energy.so.commons.search.model.mapping.nonNullDbToConsumer
import energy.so.commons.search.model.mapping.nullableDbToConsumer
import energy.so.commons.search.model.mapping.toSearchDto
import energy.so.commons.v2.search.Direction
import energy.so.commons.v2.search.eqFilter
import energy.so.commons.v2.search.filter
import energy.so.commons.v2.search.pagination
import energy.so.commons.v2.search.searchRequest
import energy.so.commons.v2.search.simpleSort
import energy.so.commons.v2.search.sort
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe

class DatabaseMappersTest : ShouldSpec({

    context("::nonNullDbToConsumer") {
        should("return consumer with db field set") {
            // THEN
            shouldNotThrow<Exception> {
                nonNullDbToConsumer(
                    fieldName = "idField",
                    consumer = { },
                    dbSupplier = { 10L },
                )
            }
        }

        should("throw as db supplier is null") {
            // THEN
            shouldThrow<IllegalArgumentException> {
                nonNullDbToConsumer(
                    fieldName = "idField",
                    consumer = { },
                    dbSupplier = { null },
                )
            }
        }
    }

    context("::nullableDbToConsumer") {
        should("return unit as consumer has been called with value supplied by dbSupplier") {
            // THEN
            nullableDbToConsumer(
                consumer = { },
                dbSupplier = { 10L }
            ) shouldBe Unit
        }

        should("return as dbSupplier returns null") {
            // THEN
            nullableDbToConsumer(
                consumer = { },
                dbSupplier = { null }
            ) shouldBe Unit
        }
    }

    context("::mapPagination") {
        should("return Pagination with defaults 1, 1") {
            // WHEN
            val returnedPagination = mapPagination(
                pagination = pagination {
                    pageNumber = -1
                    pageSize = -1
                }
            )

            // THEN
            assertSoftly {
                returnedPagination.pageNumber shouldBe 1
                returnedPagination.pageSize shouldBe 1
            }
        }

        should("return pagination with supplied pageNumber and pageSize 1 as attribute was not supplied") {
            // GIVEN
            val pageNumber = 10

            // WHEN
            val returnedPagination = mapPagination(
                pagination = pagination {
                    this.pageNumber = pageNumber
                    pageSize = -1
                }
            )

            // THEN
            assertSoftly {
                returnedPagination.pageNumber shouldBe pageNumber
                returnedPagination.pageSize shouldBe 1
            }
        }

        should("return pagination with supplied pageNumber and pageSize") {
            // GIVEN
            val pageNumber = 10
            val pageSize = 6

            // WHEN
            val returnedPagination = mapPagination(
                pagination = pagination {
                    this.pageNumber = pageNumber
                    this.pageSize = pageSize
                }
            )

            // THEN
            assertSoftly {
                returnedPagination.pageNumber shouldBe pageNumber
                returnedPagination.pageSize shouldBe pageSize
            }
        }
    }

    context("::mapSorting") {
        val numberField = "number"
        should("return ascending sorting") {
            mapSorting(
                sort {
                    simpleSort = simpleSort {
                        field = numberField
                        direction = Direction.Ascending
                    }
                }

            ) shouldBe SortAsc(numberField)

        }

        should("return descending sorting") {
            mapSorting(
                sort {
                    simpleSort = simpleSort {
                        field = numberField
                        direction = Direction.Descending
                    }
                }

            ) shouldBe SortDesc(numberField)
        }

        should("throw IllegalArgumentException") {
            shouldThrow<IllegalArgumentException> {
                mapSorting(
                    sort {
                        simpleSort = simpleSort {
                            field = numberField
                            direction = Direction.UNRECOGNIZED
                        }
                    }
                )
            }
        }
    }

    context("::toSearchDto") {
        should("when mapping a searchRequest to searchDto, then the result is correct") {
            val expectedField = "My Field"
            val expectedValue = "My Value"
            val expectedSortField = "My Sort Field"
            val expectedPageSize = 123
            val expectedPageNumber = 321
            val searchRequest = searchRequest {
                filter = filter {
                    eqFilter = eqFilter {
                        field = expectedField
                        value = expectedValue
                    }
                }
                pagination = pagination {
                    pageNumber = expectedPageNumber
                    pageSize = expectedPageSize
                }
                sorting += sort {
                    simpleSort = simpleSort {
                        field = expectedSortField
                        direction = Direction.Ascending
                    }
                }
            }

            val result = searchRequest.toSearchDto()

            assertSoftly(result) {
                filters shouldBe listOf(EqFilter(expectedField, expectedValue))
                pagination shouldBe Pagination(expectedPageNumber, expectedPageSize)
                sorting shouldBe listOf(SortAsc(expectedSortField))
            }
        }
    }
})
