package energy.so.commons.search.service.impl

import energy.so.commons.database.test.public.tables.references.LANGUAGE
import energy.so.commons.search.exception.TableFieldNotFoundException
import energy.so.commons.search.model.filter.AndFilter
import energy.so.commons.search.model.filter.BetweenFilter
import energy.so.commons.search.model.filter.ContainsFilter
import energy.so.commons.search.model.filter.EndsWithFilter
import energy.so.commons.search.model.filter.EqFilter
import energy.so.commons.search.model.filter.GtFilter
import energy.so.commons.search.model.filter.InFilter
import energy.so.commons.search.model.filter.LtFilter
import energy.so.commons.search.model.filter.NotFilter
import energy.so.commons.search.model.filter.OrFilter
import energy.so.commons.search.model.filter.StartsWithFilter
import energy.so.commons.search.service.FilterService
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.datatest.withData
import io.kotest.inspectors.forSingle
import io.kotest.matchers.shouldBe

/**
 * <AUTHOR> 16/02/2022
 */
class DefaultFilterServiceTest : ShouldSpec({
    val sut: FilterService = DefaultFilterService(LANGUAGE)

    val field = LANGUAGE.DESCRIPTION
    val fieldName = LANGUAGE.DESCRIPTION.name
    val value = "some description here"

    context("ValueFilters produce the correct condition") {
        withData(
            nameFn = { "When using the ${it.first::class.simpleName}" },
            EqFilter(fieldName, value) to field.eq(value),
            StartsWithFilter(fieldName, value) to field.startsWith(value),
            EndsWithFilter(fieldName, value) to field.endsWith(value),
            GtFilter(fieldName, value) to field.gt(value),
            LtFilter(fieldName, value) to field.lt(value),
            ContainsFilter(fieldName, value) to field.contains(value),
            InFilter(fieldName, listOf(value, "some other value")) to field.`in`(listOf(value, "some other value")),
        ) { (filter, expectedCondition) ->
            val result = sut.start(listOf(filter))

            result.forSingle { actualCondition ->
                actualCondition shouldBe expectedCondition
            }
        }
    }

    context("RangeFilters produce the correct condition") {
        val otherValue = "some other value here"

        should("when using the BetweenFilter") {
            val filter = BetweenFilter(field.name, value, otherValue)

            val result = sut.start(listOf(filter))

            result.forSingle { actualCondition ->
                actualCondition shouldBe field.between(value, otherValue)
            }
        }
    }

    context("DelegateFilters produce the correct condition") {
        should("When using the NotFilter") {
            val eqFilter = EqFilter(field.name, value)
            val notEqFilter = NotFilter(eqFilter)

            val result = sut.start(listOf(notEqFilter))

            result.forSingle { actualCondition ->
                actualCondition shouldBe field.equal(value).not()
            }
        }
    }

    context("AggregateFilters produce the correct condition") {
        val firstFilter = StartsWithFilter(fieldName, value)
        val secondFilter = EqFilter(fieldName, value)
        val filters = listOf(firstFilter, secondFilter)

        withData(
            nameFn = { "When using the ${it.first::class.simpleName}" },
            AndFilter(filters) to field.startsWith(value).and(field.eq(value)),
            OrFilter(filters) to field.startsWith(value).or(field.eq(value)),
        ) { (filter, expectedCondition) ->
            val result = sut.start(listOf(filter))

            result.forSingle { actualCondition ->
                actualCondition shouldBe expectedCondition
            }
        }
    }

    context("Given a field doesn't exist") {
        should("Then throw an exception") {
            val filter = EqFilter("AFieldThatShouldNeverExist", "Any Variable Here")
            val filters = listOf(filter)

            shouldThrow<TableFieldNotFoundException> {
                sut.start(filters)
            }
        }
    }
})
