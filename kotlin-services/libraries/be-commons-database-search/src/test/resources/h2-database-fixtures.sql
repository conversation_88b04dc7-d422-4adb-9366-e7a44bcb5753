CREATE TYPE status_type AS ENUM ('PENDING', 'ACCEPTED');

CREATE TABLE language
(
    id          NUMBER(7) NOT NULL PRIMARY KEY,
    cd          CHAR(2) NOT NULL,
    description VARCHAR2(50),
    string      VARCHAR2(50),
    date        DATE,
    timestamp   TIMESTAMP WITH TIME ZONE,
    long        BIGINT,
    int         INT,
    short       SMALLINT,
    byte        TINYINT,
    double      DOUBLE PRECISION,
    float       REAL,
    big_integer NUMERIC,
    big_decimal NUMERIC(20, 2),
    boolean     BOOLEAN,
    json        JSON,
    status      status_type
);