# BE commons Session

Library to manage an HTTP or gRPC session.

## Why we need it ?

gRPC io has a way to manage a context where we can store data to use on different instances of our gRPC call.

The problem is that the `io.grpc.Context` has a map with a key `Key<T>`. Let's say we create the
key `Key<String>("key-01")`
on a gRPC server library and then we want to get the value on the application, if we try to get it doing the same we
won't
find anything. This is because besides both keys have the same values, they are different instances, so the internal Map
in the `io.grpc.Context` is going to take them as different values.

To solve this problem we separated the `Key<T>` in a different library that can be used on other libs or services, so we
can
be sure that we always have the same instance.