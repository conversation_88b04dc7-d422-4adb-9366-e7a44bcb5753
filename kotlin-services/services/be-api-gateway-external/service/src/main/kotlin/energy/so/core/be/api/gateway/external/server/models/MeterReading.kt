package energy.so.core.be.api.gateway.external.server.models

import energy.so.assets.meterReadings.v2.MeterReadingUnitType
import energy.so.assets.meterReadings.v2.ReadingSource
import energy.so.assets.meterReadings.v2.SubmitMeterReadingFlow
import energy.so.assets.meterReadings.v2.submitMeterReadingList
import energy.so.assets.meterReadings.v2.submitMeterReadingRequest
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.json.serializers.LocalDateTimeAsISO8601Serializer
import java.time.LocalDateTime
import kotlinx.serialization.Serializable

@Serializable
data class MeterReading(
    val meterPointId: Long,
    val meterSerialNumber: String,
    val registerId: String,
    val reads: Long,
    @Serializable(with = LocalDateTimeAsISO8601Serializer::class)
    val readAt: LocalDateTime,
    val unitType: String,
)

fun MeterReading.toSubmitMeterReadingRequest(
    billingAccountId: Long,
) = let { model ->
    submitMeterReadingList {
        this.billingAccountId = billingAccountId
        this.flow = SubmitMeterReadingFlow.ASYNC_WITH_ONE_RETRY
        meters.add(
            submitMeterReadingRequest {
                this.meterIdentifier = model.meterSerialNumber.toNullableString()
                this.meterPointId = model.meterPointId
                this.registerIdentifier = model.registerId.toNullableString()
                this.unitType = MeterReadingUnitType.valueOf(model.unitType.uppercase())
                this.units = model.reads
                this.readingAt = model.readAt.toTimestamp()
            }
        )
        this.source = ReadingSource.IVR
    }

}
