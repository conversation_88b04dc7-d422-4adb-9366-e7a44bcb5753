package energy.so.core.be.api.gateway.external.server.fixtures

import energy.so.assets.meter.v2.MeterPointType.MPAN
import energy.so.assets.meter.v2.MeterPointType.MPRN
import energy.so.assets.meter.v2.meterInfo
import energy.so.assets.meter.v2.meterInfoResponse
import energy.so.assets.meter.v2.meterReadInfo
import energy.so.assets.meter.v2.meterReadInfoResponse
import energy.so.assets.meterReadings.v2.MeterReadingUnitType
import energy.so.assets.meterReadings.v2.ReadingSource
import energy.so.assets.meterReadings.v2.SubmitMeterReadingFlow
import energy.so.assets.meterReadings.v2.submitMeterReadingList
import energy.so.assets.meterReadings.v2.submitMeterReadingRequest
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.core.be.api.gateway.external.server.models.MeterReading
import energy.so.customers.billingaccounts.v2.BillingAccountMeterPoint.OperationType
import energy.so.customers.billingaccounts.v2.MeterPointType
import energy.so.customers.billingaccounts.v2.billingAccountAddress
import energy.so.customers.billingaccounts.v2.billingAccountContactInfo
import energy.so.customers.billingaccounts.v2.billingAccountMeterPoint
import energy.so.customers.billingaccounts.v2.billingAccountName
import energy.so.customers.billingaccounts.v2.billingAccountResponse
import java.time.LocalDateTime

object MeterReadingCannedData {

    const val METER_POINT_ID = "*********"
    private const val METER_SERIAL_NUMBER = "*********"
    private const val TEST_STRING = "TEST"
    private const val TEST_JUNIFER_ACCOUNT_ID = 1L
    private const val TEST_BILLING_ACCOUNT_ID = 2L
    const val ID_1 = 1L

    val meterInfoResponse = meterInfoResponse {
        meterInfo.addAll(
            listOf(
                meterInfo {
                    identifier = METER_POINT_ID
                    meterSerialNumber = METER_SERIAL_NUMBER
                    registerId = "34567"
                    unit = "M3"
                    type = MPRN
                },
                meterInfo {
                    identifier = METER_POINT_ID
                    meterSerialNumber = METER_SERIAL_NUMBER
                    registerId = "34567"
                    unit = "KWH"
                    type = MPAN
                },
            )
        )
    }

    val meterReadInfoResponse = meterReadInfoResponse {
        meterReadInfo.addAll(
            listOf(
                meterReadInfo {
                    meterPointId = METER_POINT_ID
                    meterSerialNumber = METER_SERIAL_NUMBER
                    registerId = "1"
                    lastRead = 339.5F
                    toleranceLower = 81017.0F
                    toleranceHigher = 81251.39F
                    lastReadAt = LocalDateTime.now().toTimestamp()
                    rateName = "Standard"
                }
            )
        )
    }

    val billingAccountResponse = billingAccountResponse {
        gspGroupCode = TEST_STRING.toNullableString()
        name = billingAccountName {
            firstName = TEST_STRING
            lastName = TEST_STRING
        }
        address = billingAccountAddress {
            address1 = TEST_STRING.toNullableString()
            postcode = TEST_STRING.toNullableString()
            countryCode = TEST_STRING.toNullableString()
        }
        contactInfo = billingAccountContactInfo { }
        juniferAccountNumber = TEST_STRING
        juniferAccountId = TEST_JUNIFER_ACCOUNT_ID
        gasMeterpointData.add(billingAccountMeterPoint {
            id = 1L
            identifier = TEST_STRING
            type = MeterPointType.MPAN
            ukGspGroup = TEST_STRING
            operationType = OperationType.CREDIT
        })
        hasActiveAgreement = true
        switchingAway = true
        billingAccountId = TEST_BILLING_ACCOUNT_ID
    }

    val meterReading = MeterReading(
        meterPointId = METER_POINT_ID.toLong(),
        meterSerialNumber = "11LLN",
        registerId = "11",
        reads = 1L,
        readAt = LocalDateTime.now().minusDays(2),
        unitType = "Electricity"
    )

    val submitMeterReadingRequest = submitMeterReadingRequest {
        meterIdentifier = meterReading.meterSerialNumber.toNullableString()
        meterPointId = meterReading.meterPointId
        unitType = MeterReadingUnitType.valueOf(meterReading.unitType.uppercase())
        units = meterReading.reads
        readingAt = meterReading.readAt.toTimestamp()
        registerIdentifier = meterReading.registerId.toNullableString()
    }

    val submitMeterReadingList = submitMeterReadingList {
        flow = SubmitMeterReadingFlow.ASYNC_WITH_ONE_RETRY
        billingAccountId = ID_1
        meters.add(submitMeterReadingRequest)
        source = ReadingSource.IVR
    }

}