package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterPointElectricityEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.MeterType
import energy.so.commons.model.tables.pojos.MeterPointElectricity
import energy.so.commons.model.tables.records.MeterPointElectricityRecord
import energy.so.commons.model.tables.references.METER_POINT_ELECTRICITY
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqMeterPointElectricityEntityRepository(
    private val dslContext: DSLContext,
) : MeterPointElectricityEntityRepository {
    override fun deleteMeterPointElectricityEntity(id: Long): Long {
        dslContext.update(METER_POINT_ELECTRICITY)
            .set(METER_POINT_ELECTRICITY.DELETED, LocalDateTime.now())
            .where(METER_POINT_ELECTRICITY.ID.eq(id))
            .execute()
        return id
    }

    override fun patchMeterPointElectricityEntity(entity: MeterPointElectricityEntity): Long {
        val updateStep: UpdateSetStep<MeterPointElectricityRecord> = dslContext
            .update(METER_POINT_ELECTRICITY)
            .set(METER_POINT_ELECTRICITY.UPDATED_AT, LocalDateTime.now())

        if (entity.hasEnergised()) updateStep.set(METER_POINT_ELECTRICITY.ENERGISED, entity.energised.getValueOrNull())

        if (entity.hasLineLossFactorClass()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.MEASUREMENT_CLASS,
                entity.lineLossFactorClass.getValueOrNull()
            )
        }

        if (entity.hasMeasurementClass()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.MEASUREMENT_CLASS,
                entity.measurementClass.getValueOrNull()
            )
        }

        if (entity.hasMeterSerialNumber()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.METER_SERIAL_NUMBER,
                entity.meterSerialNumber.getValueOrNull()
            )
        }

        if (entity.hasMeterTimeSwitchCode()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.METER_TIMESWITCH_CODE,
                entity.meterTimeSwitchCode.getValueOrNull()
            )
        }

        if (entity.hasMeterType()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.METER_TYPE,
                MeterType.valueOf(entity.meterType.value)
            )
        }

        if (entity.hasSmsoMarketParticipant()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.SMSO_MARKET_PARTICIPANT_CODE,
                entity.smsoMarketParticipant.getValueOrNull()
            )
        }
        if (entity.hasStandardSettlementConfiguration()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.STANDARD_SETTLEMENT_CONFIGURATION_CODE,
                entity.standardSettlementConfiguration.getValueOrNull()
            )
        }
        if (entity.hasMopMarketParticipant()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.MOP_MARKET_PARTICIPANT,
                entity.mopMarketParticipant.getValueOrNull()
            )
        }
        if (entity.hasDistributionNetworkOperator()) {
            updateStep.set(
                METER_POINT_ELECTRICITY.DISTRIBUTION_NETWORK_OPERATOR,
                entity.distributionNetworkOperator.getValueOrNull()
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(METER_POINT_ELECTRICITY.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }

        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_POINT_ELECTRICITY.DELETED)
            } else {
                updateStep.set(
                    METER_POINT_ELECTRICITY.DELETED,
                    DSL.coalesce(METER_POINT_ELECTRICITY.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }

        (updateStep as UpdateSetMoreStep).where(METER_POINT_ELECTRICITY.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createMeterPointElectricityEntity(entity: MeterPointElectricityEntity): Long {
        return resolveSyncCreationFlow(
            METER_POINT_ELECTRICITY.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterPointElectricityEntity,
            ::patchMeterPointElectricityEntity,
            fun(e: MeterPointElectricityEntity, newId: Long): MeterPointElectricityEntity =
                e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterPointElectricityEntity(entity: MeterPointElectricityEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER_POINT_ELECTRICITY,
            MeterPointElectricity(
                id = entity.meterPointId.value,
                energised = entity.energised.getValueOrNull(),
                lineLossFactorClassId = entity.lineLossFactorClass.getValueOrNull(),
                measurementClass = entity.measurementClass.getValueOrNull(),
                meterSerialNumber = entity.meterSerialNumber.getValueOrNull(),
                meterTimeswitchCode = entity.meterTimeSwitchCode.getValueOrNull(),
                meterType = entity.meterType.getValueOrNull()?.let { MeterType.valueOf(it) },
                smsoMarketParticipantCode = entity.smsoMarketParticipant.getValueOrNull(),
                standardSettlementConfigurationCode = entity.standardSettlementConfiguration.getValueOrNull(),
                mopMarketParticipant = entity.mopMarketParticipant.getValueOrNull(),
                distributionNetworkOperator = entity.distributionNetworkOperator.getValueOrNull(),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(MeterPointElectricity()) }.id!!
}
