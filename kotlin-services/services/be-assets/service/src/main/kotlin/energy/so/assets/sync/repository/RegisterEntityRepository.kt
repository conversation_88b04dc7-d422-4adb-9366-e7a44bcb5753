package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.RegisterEntity

/**
 * A repository that handles entity synchronisation updates from the anti-corruption layer.
 * <AUTHOR>
 */
interface RegisterEntityRepository {

    fun deleteRegisterEntity(id: Long): Long

    fun patchRegisterEntity(entity: RegisterEntity): Long

    fun createRegisterEntity(entity: RegisterEntity): Long
}
