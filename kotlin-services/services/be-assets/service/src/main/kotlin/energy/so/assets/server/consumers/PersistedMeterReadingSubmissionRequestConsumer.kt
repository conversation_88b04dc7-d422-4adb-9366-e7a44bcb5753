package energy.so.assets.server.consumers

import energy.so.assets.server.services.MeterReadingSubmissionRequestService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

private val logger = TraceableLogging.logger { }

/**
 * Queue persisted submissions that failed queueing cron consumer
 *
 * Will consume events scheduled at specific point in a day, to queue persisted submissions that failed queueing
 */
class PersistedMeterReadingSubmissionRequestConsumer(
    projectName: String,
    config: SubscriptionConfiguration,
    private val meterReadingSubmissionRequestService: MeterReadingSubmissionRequestService,
) : PubsubMessageProcessor<String>(projectName, config) {

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug { "[::processMessage] Received message ($message) to queue persisted submissions that failed queueing" }
        if (message.eventType == EventType.CREATED) {
            meterReadingSubmissionRequestService.queuePersistedSubmissions()
        }
    }
}
