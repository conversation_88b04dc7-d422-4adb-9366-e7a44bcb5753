package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.meterReading
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.MeterReadingStatu
import energy.so.commons.model.enums.MeterReadingWorkflowStatu
import java.math.BigDecimal
import java.time.LocalDateTime
import energy.so.assets.meterReadings.v2.MeterReading as ProtoMeterReading
import energy.so.commons.model.tables.pojos.MeterReading as JooqMeterReading

data class MeterReading(
    val id: Long? = null,
    val meterPointId: Long,
    val meterId: Long?,
    val register: Register? = null,
    val readingDttm: LocalDateTime,
    val fromDttm: LocalDateTime? = null,
    val status: MeterReadingStatus? = null,
    val sequenceType: MeterReadingSequenceType,
    val source: MeterReadingSource,
    val quality: MeterReadingQuality,
    val cumulative: BigDecimal,
    val consumption: BigDecimal? = null,
    val unit: MeterReadingUnit? = null,
    val workflowStatus: MeterReadingWorkflowStatus? = null,
    val rateName: String? = null,
    val pendingRegisterReadingId: String? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val errorDescription: String? = null,
    val submissionProcessed: Boolean = false,
    val receivedDttm: LocalDateTime? = null,
    val meterPointIdentifier: String? = null,
    val meterIdentifier: String? = null,
    val registerIdentifier: String? = null,
    val submissionSource: MeterReadingSubmissionSource? = null,
) {
    companion object {
        fun fromJooq(jooq: JooqMeterReading, register: Register?): MeterReading = MeterReading(
            id = jooq.id,
            meterPointId = jooq.meterPointId!!,
            meterId = jooq.meterId,
            register = register,
            readingDttm = jooq.readingDttm!!,
            fromDttm = jooq.fromDttm,
            status = jooq.status?.toSiblingEnum<MeterReadingStatus>(),
            sequenceType = jooq.sequenceType!!.toSiblingEnum(),
            source = jooq.source!!.toSiblingEnum(),
            quality = jooq.quality!!.toSiblingEnum(),
            cumulative = jooq.cumulative!!,
            consumption = jooq.consumption,
            unit = jooq.unit?.toSiblingEnum<MeterReadingUnit>(),
            workflowStatus = jooq.workflowStatus?.toSiblingEnum<MeterReadingWorkflowStatus>(),
            rateName = jooq.rateName,
            pendingRegisterReadingId = jooq.pendingRegisterReadingId,
            deleted = jooq.deleted,
            createdAt = jooq.createdAt!!,
            updatedAt = jooq.updatedAt!!,
            errorDescription = jooq.errorDescription,
            submissionProcessed = jooq.submissionProcessed!!,
            submissionSource = null // TODO: Update when JOOQ classes are regenerated after migration
        )
    }
}

enum class MeterReadingStatus(val value: String) {
    ACCEPTED("Accepted"),
    PENDING("Pending"),
    UNKNOWN("Unknown"),
    REMOVED("Removed")
}

enum class MeterReadingSource {
    MMR,
    System,
    Customer,
    User,
    Virtual,
    SMR,
    Industry,
    EDMI,
    Secure,
    AMR,
    GCMDS
}

enum class MeterReadingQuality {
    NORMAL,
    MANUAL,
    ESTIMATED
}

enum class MeterReadingSequenceType {
    NORMAL,
    FIRST,
    LAST
}

enum class MeterReadingUnit {
    kWh,
    MWh,
    kVAh,
    hour,
    day,
    month,
    second,
    kVArh,
    MJ,
    therm,
    kW,
    MW,
    kVAr,
    kVA,
    m3,
    tm3,
    hm3,
    thm3,
    ft3,
    tcf,
    hcf,
    thcf,
    year,
    default,
}

enum class MeterReadingWorkflowStatus {
    VALIDATION_SCHEDULED,
    VALIDATION_SCHEDULED_WITH_OVERRIDE,
    AWAITING_INDUSTRY_RESPONSE,
    VALIDATION_SUCCESS,
    VALIDATION_FAILURE,
    ACCEPTED,
    ERROR,
    SCHEDULED_FOR_PUBLISHING,
    FLAGGED
}

fun MeterReading.toResponse(): ProtoMeterReading = let { from ->
    meterReading {
        id = from.id!!
        meterPointId = from.meterPointId
        meterId = from.meterId.toNullableInt64()
        from.register?.let { register = it.toResponse() }
        readingDttm = from.readingDttm.toTimestamp()
        fromDttm = from.fromDttm.toNullableTimestamp()
        from.status?.let { status = it.toSiblingEnum() }
        sequenceType = from.sequenceType.toSiblingEnum()
        source = from.source.toSiblingEnum()
        quality = from.quality.toSiblingEnum()
        cumulative = from.cumulative.toDouble()
        consumption = from.consumption?.toDouble().toNullableDouble()
        from.unit?.let { unit = it.toSiblingEnum() }
        from.workflowStatus?.let { workflowStatus = it.toSiblingEnum() }
        rateName = from.rateName.toNullableString()
        pendingRegisterReadingId = from.pendingRegisterReadingId.toNullableString()
        deleted = from.deleted.toNullableTimestamp()
        createdAt = from.createdAt.toNullableTimestamp()
        updatedAt = from.updatedAt.toNullableTimestamp()
        errorDescription = from.errorDescription.toNullableString()
        receivedDttm = from.receivedDttm.toNullableTimestamp()
        meterPointIdentifier = from.meterPointIdentifier.toNullableString()
        meterIdentifier = from.meterIdentifier.toNullableString()
        registerIdentifier = from.registerIdentifier.toNullableString()
    }
}

fun MeterReading.toJooq(): JooqMeterReading = JooqMeterReading(
    id = id,
    meterPointId = meterPointId,
    meterId = meterId,
    registerId = register?.id,
    readingDttm = readingDttm,
    fromDttm = fromDttm,
    status = status?.toSiblingEnum<MeterReadingStatu>(),
    sequenceType = sequenceType.toSiblingEnum<energy.so.commons.model.enums.MeterReadingSequenceType>(),
    source = source.toSiblingEnum<energy.so.commons.model.enums.MeterReadingSource>(),
    quality = quality.toSiblingEnum<energy.so.commons.model.enums.MeterReadingQuality>(),
    cumulative = cumulative,
    consumption = consumption,
    unit = unit!!.toSiblingEnum<energy.so.commons.model.enums.MeterReadingUnit>(),
    workflowStatus = workflowStatus?.toSiblingEnum<MeterReadingWorkflowStatu>(),
    rateName = rateName,
    pendingRegisterReadingId = pendingRegisterReadingId,
    deleted = deleted,
    createdAt = createdAt,
    updatedAt = updatedAt,
    errorDescription = errorDescription,
    submissionProcessed = submissionProcessed,
)

fun MeterReading.fuelType() = if (this.unit == MeterReadingUnit.m3) UnitType.Gas else UnitType.Electricity

enum class MeterReadingSubmissionSource {
    WEBSITE,
    AGENT,
    IVR,
    JUNIFER
}
