package energy.so.assets.server.extensions

import energy.so.ac.esg.v1.GetIHDsResponse
import energy.so.ac.esg.v1.GetSmartMeterDataFileUrlResponse
import energy.so.ac.esg.v1.GetUpdatedTimeResponse
import energy.so.ac.esg.v1.IHD
import energy.so.ac.esg.v1.getSmartMeterDataRequest
import energy.so.assets.smartMeters.v2.GetSmartMeterDataRequest
import energy.so.commons.grpc.extensions.toNullableTimestamp

fun GetSmartMeterDataRequest.toEsgRequest() = let {
    getSmartMeterDataRequest {
        selectedDeviceIds.addAll(it.selectedDeviceIdsList)
        selectedDeviceStatuses.addAll(it.selectedDeviceStatusesList)
        installationDateFrom = it.installationDateFrom
        installationDateTo = it.installationDateTo
        lastMeterReadDateFrom = it.lastMeterReadDateFrom
        lastMeterReadDateTo = it.lastMeterReadDateTo
        sortColumnName = it.sortColumnName
        sortDxn = it.sortDxn
        offset = it.offset
        limit = it.limit
        selectedErrors.addAll(it.selectedErrorsList)
        selectedErrorStatuses.addAll(it.selectedErrorStatusesList)
        selectedSrvs.addAll(it.selectedSrvsList)
        selectedLastOrchestrations.addAll(it.selectedLastOrchestrationsList)
        selectedOrchestrationStatuses.addAll(it.selectedOrchestrationStatusesList)
        alertDateFrom = it.alertDateFrom
        alertDateTo = it.alertDateTo
        selectedAlertCodes.addAll(it.selectedAlertCodesList)
        selectedAlertStatuses.addAll(it.selectedAlertStatusesList)
        readingsReceivedFilter = it.readingsReceivedFilter
        selectedWorkstreams.addAll(it.selectedWorkstreamsList)
        selectedRegions.addAll(it.selectedRegionsList)
        selectedFirmwareStatuses.addAll(it.selectedFirmwareStatusesList)
        selectedFirmwareVersions.addAll(it.selectedFirmwareVersionsList)
        selectedManufacturers.addAll(it.selectedManufacturersList)
        selectedModels.addAll(it.selectedModelsList)
        selectedVariants.addAll(it.selectedVariantsList)
        selectedPaymentModes.addAll(it.selectedPaymentModesList)
        selectedSmetsVersions.addAll(it.selectedSmetsVersionsList)
        selectedGbcsVersions.addAll(it.selectedGbcsVersionsList)
        ssdFrom = it.ssdFrom
        ssdTo = it.ssdTo
        selectedMeterType.addAll(it.selectedMeterTypeList)
    }
}

fun GetSmartMeterDataFileUrlResponse.toAssestsResponse() = let {
    energy.so.assets.smartMeters.v2.getSmartMeterDataFileUrlResponse {
        fileUrl = it.fileUrl
    }
}

fun GetUpdatedTimeResponse.toAssetsResponse() = energy.so.assets.smartMeters.v2.getUpdatedTimeResponse {
    updatedTime = <EMAIL>
}

fun GetIHDsResponse.toAssetsResponse() = energy.so.assets.smartMeters.v2.getIHDsResponse {
    ihds.addAll(<EMAIL> { it.toAssetsResponse() })
}

fun IHD.toAssetsResponse() = energy.so.assets.smartMeters.v2.iHD {
    deviceId = <EMAIL>
    deviceType = <EMAIL>
    deviceTypeName = <EMAIL>
    dateInstalled = <EMAIL>
    installedBy = <EMAIL>
    deviceStatus = <EMAIL>
    dateCommissioned = <EMAIL>
    responsiveStatus = <EMAIL>
    manufacturerCode = <EMAIL>
    manufacturerFullName = <EMAIL>
    manufacturerName = <EMAIL>
    model = <EMAIL>
    modelName = <EMAIL>
    smetsVersion = <EMAIL>
    firmwareVersion = <EMAIL>
    firmwareStatus = <EMAIL>
    mpan = <EMAIL>
    mprn = <EMAIL>
    elecSsd = <EMAIL>
    gasSsd = <EMAIL>
    dateLastUpdated = <EMAIL>
}
