package energy.so.assets.server.models

import energy.so.commons.grpc.extensions.toSiblingEnum
import java.time.LocalDateTime

data class MeterPointAverageHhConsumption(
    val id: Long,
    val meterPointId: Long,
    val averageConsumption: List<Double?>,
    val unit: MeterReadingUnit,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
) {
    init {
        require(averageConsumption.size == 48) {
            "Expected 48 half-hour values, but got ${averageConsumption.size}"
        }
    }

    companion object {
        fun fromJooq(
            jooq: energy.so.commons.model.tables.pojos.MeterPointAverageHhConsumption
        ): MeterPointAverageHhConsumption =
            MeterPointAverageHhConsumption(
                id = checkNotNull(jooq.id) { "id must not be null"},
                meterPointId = checkNotNull(jooq.meterPointId) { "meterPointId must not be null" },
                averageConsumption = jooq.averageConsumption?.toList()
                    ?: error("averageConsumption must not be null"),
                unit = jooq.unit?.toSiblingEnum<MeterReadingUnit>()
                    ?: error("unit must not be null"),
                deleted = jooq.deleted,
                createdAt = checkNotNull(jooq.createdAt) { "createdAt must not be null" },
                updatedAt = checkNotNull(jooq.updatedAt) { "updatedAt must not be null" }
            )
    }
}