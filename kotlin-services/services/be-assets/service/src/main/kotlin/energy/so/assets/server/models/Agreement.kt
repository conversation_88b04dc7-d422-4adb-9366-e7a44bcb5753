package energy.so.assets.server.models

import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.customers.productaccounts.v2.ProductAccount
import java.time.LocalDateTime
import energy.so.customers.agreements.v2.Agreement as ProtoAgreement

data class Agreement(
    val id: Long? = null,
    val productType: ProductType,
    val fromDate: LocalDateTime,
    val toDate: LocalDateTime?,
    val meterPointsIds: List<Long>,
)

fun ProtoAgreement.toAgreementModel(type: ProductAccount.Type): Agreement {
    return Agreement(
        id = id,
        productType = type.toSiblingEnum(),
        fromDate = fromDate.toLocalDateTime(),
        toDate = if (toDate.hasValue()) toDate.value.toLocalDateTime() else null,
        meterPointsIds = meterPointsList.map { it.id }
    )
}

fun ProductAccount.getActiveAgreements(): List<Agreement> =
    this.activeAgreementsList.filter {
        it.toDate.hasNull() || !it.toDate.value.toLocalDateTime().isBefore(LocalDateTime.now())
    }
        .map { it.toAgreementModel(this.type) }
