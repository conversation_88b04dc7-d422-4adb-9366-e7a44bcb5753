package energy.so.assets.server.models

import energy.so.ac.junifer.v1.smartreads.BulkXRITMResponse
import energy.so.ac.junifer.v1.smartreads.XRITMError
import energy.so.ac.junifer.v1.smartreads.bulkXRITMRequest
import energy.so.ac.junifer.v1.smartreads.xRITMMeter
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus
import energy.so.assets.meterPoints.v2.mPXN
import energy.so.assets.meterPoints.v2.meterPoint
import energy.so.assets.meterReadings.v2.bulkXRITMResponse
import energy.so.assets.meterReadings.v2.xRITMError
import energy.so.assets.server.mapping.SimplifiedSupplyStatusMapper
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.tables.records.Junifer_MeterpointRecord
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.assets.meterPoints.v2.MeterPoint as MeterPointProto
import energy.so.commons.model.tables.pojos.MeterPoint as JooqMeterPoint

data class MeterPoint(
    val id: Long? = null,
    val property: Property? = null,
    val identifier: String,
    val type: MeterPointType,
    val supplyStartDate: LocalDate? = null,
    val changeOfTenancyFl: Boolean,
    val ukProfileClass: UkProfileClass? = null,
    val ukGspGroup: String? = null,
    val operationType: OperationType?,
    val readingFrequencyCode: String? = null,
    val serviceType: String?,
    val measurementType: MeasurementType? = null,
    val dccServiceStatus: DccServiceStatus? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val meters: List<Meter>,
    val meterPointGas: MeterPointGas? = null,
    val meterPointElectricity: MeterPointElectricity? = null,
    val meterPointHistory: MeterPointHistory? = null,
    val fromDt: LocalDate? = null,
    val toDt: LocalDate? = null,
    val simplifiedSupplyStatus: SimplifiedSupplyStatus? = null,
) {

    fun toMPXNProto() = let { from ->
        mPXN {
            id = from.id!!
            identifier = from.identifier
            type = from.type.toSiblingEnum()
            from.ukGspGroup?.let { ukGspGroup = it.toNullableString() }
            if (from.property != null) {
                property = from.property.toProtobuf()
            }
        }
    }

    companion object {
        fun fromJooq(
            jooq: JooqMeterPoint,
            property: Property? = null,
            meters: List<Meter>? = null,
            meterPointGas: MeterPointGas? = null,
            meterPointElectricity: MeterPointElectricity? = null,
            meterPointHistory: MeterPointHistory? = null,
        ): MeterPoint {
            val type = jooq.type!!.toSiblingEnum<MeterPointType>()

            return MeterPoint(
                id = jooq.id,
                property = property,
                identifier = jooq.identifier!!,
                type = type,
                supplyStartDate = jooq.supplyStartDate,
                changeOfTenancyFl = jooq.changeOfTenancyFl!!,
                ukProfileClass = jooq.ukProfileClass?.toSiblingEnum<UkProfileClass>(),
                ukGspGroup = jooq.ukGspGroup,
                operationType = jooq.operationType?.toSiblingEnum<OperationType>(),
                readingFrequencyCode = jooq.readingFrequencyCode,
                serviceType = jooq.serviceType,
                measurementType = jooq.measurementType?.toSiblingEnum<MeasurementType>(),
                dccServiceStatus = jooq.dccServiceStatus?.toSiblingEnum<DccServiceStatus>(),
                deleted = jooq.deleted,
                createdAt = jooq.createdAt,
                updatedAt = jooq.updatedAt,
                meters = meters ?: emptyList(),
                meterPointGas = meterPointGas,
                meterPointElectricity = meterPointElectricity,
                meterPointHistory = meterPointHistory,
                fromDt = jooq.fromDt,
                toDt = jooq.toDt,
                simplifiedSupplyStatus = SimplifiedSupplyStatusMapper.computeSimplifiedSupplyStatus(
                    jooq.id,
                    type,
                    meterPointHistory
                )
            )
        }
    }
}

enum class MeterPointType {
    MPAN, MPRN
}

enum class UkProfileClass {
    DOMESTIC_UNRESTRICTED,
    DOMESTIC_ECONOMY_7,
    NONDOMESTIC_UNRESTRICTED,
    NONDOMESTIC_ECONOMY_7,
    NONDOMESTIC_MD_LOAD_FACTOR_0_20,
    NONDOMESTIC_MD_LOAD_FACTOR_20_30,
    NONDOMESTIC_MD_LOAD_FACTOR_30_40,
    NONDOMESTIC_MD_LOAD_FACTOR_40,
    AUTOGENERATED_HALFHOURLY_PROFILE_CLASS,
}

enum class OperationType {
    CREDIT,
    PREPAY,
    UNKNOWN,
    SMARTPAYG,
}

enum class MeasurementType {
    IMPORT,
    EXPORT,
}

enum class DccServiceStatus {
    ACTIVE,
    INSTALLED_NOT_COMMISSIONED,
    NON_ACTIVE,
    WITHDRAWN,
    SUSPENDED
}

fun MeterPoint.toProtobuf(
    meterReadings: List<MeterReading>,
    registersToMeterIdMap: Map<Long, List<Register>>,
): MeterPointProto = let { from ->
    val meters = from.meters.toProtobuf(registersToMeterIdMap)
    val meterPoint = meterPoint {
        id = from.id!!
        from.property?.let { property = it.toProtobuf() }
        identifier = from.identifier
        type = from.type.toSiblingEnum()
        from.supplyStartDate?.let { supplyStartDate = it.toTimestamp() }
        changeOfTenancyFl = from.changeOfTenancyFl
        from.ukProfileClass?.let { ukProfileClass = it.toSiblingEnum() }
        from.ukGspGroup?.let { ukGspGroup = it }
        from.operationType?.let { operationType = it.toSiblingEnum() }
        readingFrequencyCode = from.readingFrequencyCode.toString().toNullableString()
        serviceType = from.serviceType.toNullableString()
        from.measurementType?.let { measurementType = it.toSiblingEnum() }
        from.dccServiceStatus?.let { dccServiceStatus = it.toSiblingEnum() }
        createdAt = from.createdAt!!.toTimestamp()
        updatedAt = from.updatedAt!!.toTimestamp()
        from.deleted?.let { deleted = it.toNullableTimestamp() }
        from.meterPointElectricity?.let { meterPointElectricity = it.toProtobuf() }
        from.meterPointGas?.let { meterPointGas = it.toProtobuf() }
        from.meterPointHistory?.let { meterPointHistory = it.toResponse() }
        readings.addAll(meterReadings.map { it.toResponse() })
        from.fromDt?.let { fromDt = it.toTimestamp() }
        from.toDt?.let { toDt = it.toTimestamp() }
        from.simplifiedSupplyStatus?.let { simplifiedSupplyStatus = it.toSiblingEnum() }
    }
    meterPoint.toBuilder().addAllMeters(meters).build()
}

fun List<MeterPoint>.toProtobuf(
    readingsToMeterPointId: Map<Long, List<MeterReading>> = emptyMap(),
    registersToMeterId: Map<Long, List<Register>> = emptyMap(),
): List<MeterPointProto> = map { meterPoint ->
    meterPoint.toProtobuf(readingsToMeterPointId[meterPoint.id] ?: emptyList(), registersToMeterId)
}

fun List<Junifer_MeterpointRecord>.toBulkXRITMRequest() = bulkXRITMRequest {
    meters.addAll(<EMAIL> { it.toGprc() })
}

fun Junifer_MeterpointRecord.toGprc() = xRITMMeter {
    meterpointId = <EMAIL>()
    mpxn = <EMAIL>()
}

fun BulkXRITMResponse.toAssetsResponse() = bulkXRITMResponse {
    errors.addAll(<EMAIL> { it.toAssetsResponse() })
}

fun XRITMError.toAssetsResponse() = xRITMError {
    mpxn = <EMAIL>
    meterpointId = <EMAIL>
    error = <EMAIL>
}

fun MeterPoint.isDccActive() = dccServiceStatus == DccServiceStatus.ACTIVE

fun MeterPoint.isEco7() = (ukProfileClass == UkProfileClass.DOMESTIC_ECONOMY_7) ||
        (ukProfileClass == UkProfileClass.NONDOMESTIC_ECONOMY_7)

fun MeterPoint.hasSmartMeter() = meters.any { m -> m.isSmartMeter() }

fun MeterPoint.hasSmartPayAsYouGo() = meters.any { it.isSmartPayAsYouGo() }

fun MeterPoint.isExportType() = (measurementType == MeasurementType.EXPORT)