package energy.so.assets.server.consumers

import energy.so.assets.server.services.OutboundPhotoRequestService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

class OutboundPhotoRequestConsumer(
    private val outboundPhotoRequestService: OutboundPhotoRequestService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }
    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.info("Received message: ${message.data}")

        val outboundPhotoRequestId = message.data.toLong()
        logger.info {
            "[outboundPhotoRequestId:$outboundPhotoRequestId] forward reading to OutboundPhotoRequestService"
        }

        outboundPhotoRequestService.processOutboundPhotoRequest(outboundPhotoRequestId)
    }
}
