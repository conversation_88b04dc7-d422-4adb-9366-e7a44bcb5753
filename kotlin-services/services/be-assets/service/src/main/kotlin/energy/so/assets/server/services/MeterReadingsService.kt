package energy.so.assets.server.services

import energy.so.assets.meterReadings.v2.BulkXRITMResponse
import energy.so.assets.meterReadings.v2.CreateMeterReadingRequest
import energy.so.assets.meterReadings.v2.MeterReading
import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.SubmitMeterReadingsResult
import java.time.LocalDateTime

interface MeterReadingsService {

    /**
     * Create a new meter reading request, saving it to a repository and returning a [MeterReading] object.
     * @param request CreateMeterReadingRequest
     * @return MeterReading
     */
    fun createMeterReading(request: CreateMeterReadingRequest): MeterReading

    /**
     * Persists a series of meter readings.
     * @param meterReadingsRequestDto MeterReadingsRequestDto
     * @param submitMeterReadingsResult SubmitMeterReadingsResult
     */
    suspend fun createMeterReading(
        meterReadingsRequestDto: MeterReadingsRequestDto,
        submitMeterReadingsResult: SubmitMeterReadingsResult,
    )

    /**
     * Submits a series of meter readings to be-ac-junifer. These can be with or without meter technical details.
     * @param meterReadingsRequestDto MeterReadingsRequestDto
     * @return SubmitMeterReadingsResult
     */
    suspend fun submitMeterReadingToJunifer(meterReadingsRequestDto: MeterReadingsRequestDto): SubmitMeterReadingsResult

    /**
     * Search meter readings
     *
     * @param searchRequest the filters to search by
     * @return container of readings found
     */
    suspend fun searchMeterReadings(searchRequest: MeterReadingsSearchRequest): List<energy.so.assets.server.models.MeterReading>

    /**
     * Get the latest electricity meter reading
     * @param meterPointId Long
     * @return MeterReading?
     */
    suspend fun getLastElectricityMeterReading(meterPointId: Long): energy.so.assets.server.models.MeterReading?

    /**
     * Get the first electricity meter reading
     * @param meterPointId Long
     * @return MeterReading?
     */
    suspend fun getFirstElectricityMeterReading(meterPointId: Long): energy.so.assets.server.models.MeterReading?

    /**
     * Get electricity meter readings by date
     * @param meterPointId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @return List<MeterReading>
     */
    suspend fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<energy.so.assets.server.models.MeterReading>

    fun bulkXRITM(
        mpxns: List<String>
    ): BulkXRITMResponse

    suspend fun computeSequenceTypeForReadings(meterReadingsRequestDto: MeterReadingsRequestDto): MeterReadingsRequestDto
}
