package energy.so.assets.server.models

import energy.so.assets.v2.asset
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Asset as JooqAsset

data class Asset(
    val id: Long? = null,
    val type: AssetType? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(pojo: JooqAsset) = Asset(
            id = pojo.id,
            type = pojo.type?.toSiblingEnum<AssetType>(),
            deleted = pojo.deleted,
            createdAt = pojo.createdAt,
            updatedAt = pojo.updatedAt
        )
    }
}

enum class AssetType {
    METER,
    EV_CHARGER,
    BATTERY,
}

fun Asset.toProto(meter: Meter? = null) = let { model ->
    asset {
        model.id?.let { id = it }
        model.type?.let { type = it.toSiblingEnum() }
        model.deleted?.let { deleted = it.toNullableTimestamp() }
        model.createdAt?.let { createdAt = it.toTimestamp() }
        model.updatedAt?.let { updatedAt = it.toTimestamp() }
        meter?.let { this.meter = it.toProtobuf() }
    }
}
