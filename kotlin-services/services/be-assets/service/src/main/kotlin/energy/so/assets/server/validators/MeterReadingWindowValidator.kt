package energy.so.assets.server.validators

import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.models.MeterReadings
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.assets.server.models.Read
import energy.so.assets.server.models.UnitType
import energy.so.assets.server.services.FeatureService
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.logging.TraceableLogging
import energy.so.customers.calendarholiday.v2.calendarHolidaysRequest
import energy.so.customers.calendarholiday.v2.holidayDateOrNull
import energy.so.customers.client.v2.calendarholiday.BlockingCalendarHolidayClient
import energy.so.users.v2.FeatureName
import org.koin.core.annotation.Single
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val logger = TraceableLogging.logger {}

@Single(createdAtStart = true)
class MeterReadingWindowValidator(
    private val meterReadingConfig: MeterReadingConfig,
    private val featureService: FeatureService,
    private val calendarHolidayClient: BlockingCalendarHolidayClient,
) : MeterReadingValidator {

    companion object {
        private const val ELECTRICITY_FIRST_READING = "Electricity first reading"
        private const val GAS_FIRST_READING = "Gas first reading"
        const val WINDOW_DATE_FORMAT = "MMMM dd, yyyy"
    }

    override suspend fun validate(subject: MeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> {
        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_16936_WINDOW_VALIDATOR)) {
            val reads = when (subject) {
                is MeterReadingsWithoutTechnicalDetailsDto -> subject.registerReads.map {
                    it.getValues(subject)
                }

                is MeterReadingsWithTechnicalDetailsDto -> subject.readings.map {
                    it.getValues(subject.meterPointId)
                }
            }

            val errorMessage = reads.mapNotNull { reading -> validateMeterReading(reading) }
                .joinToString(" | ")

            return if (errorMessage != "") {
                MeterReadingWindowInvalidError(errorMessage).invalidNel()
            } else {
                validNel()
            }
        } else {
            return validNel()
        }
    }

    private fun Read.getValues(
        readingWithoutTechDetails: MeterReadingsWithoutTechnicalDetailsDto,
    ): MeterReadData {
        return MeterReadData(
            readingAt = readingWithoutTechDetails.readingDate.atStartOfDay(),
            meterPointId = readingWithoutTechDetails.meterPointId,
            unitType = this.unitType,
            agreementFromDate = readingWithoutTechDetails.agreementFromDate!!,
            sequenceType = readingWithoutTechDetails.sequenceType
        )
    }

    private fun MeterReadings.getValues(meterPointId: Long): MeterReadData {
        return MeterReadData(
            readingAt = this.readingDateTime,
            meterPointId = meterPointId,
            unitType = this.unitType,
            agreementFromDate = this.agreementFromDate!!,
            sequenceType = this.sequenceType!!
        )
    }

    data class MeterReadData(
        val readingAt: LocalDateTime,
        val meterPointId: Long,
        val unitType: UnitType,
        val agreementFromDate: LocalDate,
        val sequenceType: energy.so.assets.server.models.SequenceType,
    )

    private fun validateMeterReading(
        reading: MeterReadData,
    ): String? {
        val isElectricityMeter = isElectricityMeter(reading)

        val readingDate = reading.readingAt
        val agreementFromDate = reading.agreementFromDate

        logger.debug(
            "[::validateMeterReading] sequence type: Fisrt for " +
                "[meterPointId - ${reading.meterPointId} unitType - ${reading.unitType} readingTime - ${reading.readingAt}]"
        )

        if (isFirstSequenceType(reading.sequenceType)) {
            return validateReadingWindowWithin(
                readingDate.toLocalDate(),
                agreementFromDate,
                isElectricityMeter
            )
        }

        return null
    }

    private fun validateReadingWindowWithin(
        readingDate: LocalDate,
        agreementFromDate: LocalDate,
        isElectricityMeter: Boolean,
    ): String? {
        val type = if (isElectricityMeter) ELECTRICITY_FIRST_READING else GAS_FIRST_READING
        val holidays = getHolidaysForReadingDate(readingDate)
        val lowerBound = if (isElectricityMeter) computeLowerBound(agreementFromDate, holidays) else agreementFromDate
        val upperBoundDays = if (isElectricityMeter) meterReadingConfig.firstReadingPeriodDaysElec else meterReadingConfig.firstReadingMaxPeriodDaysGas
        val upperBound = computeUpperBound(agreementFromDate, holidays, upperBoundDays)

        val errorMessage =
            "$type should be within the window [${formatDate(lowerBound)}] to [${formatDate(upperBound)}]."

        if (!readingWithinWindow(readingDate, lowerBound, upperBound)) {
            logger.error("[::validateReadingWindowWithin] $readingDate should be within $lowerBound - $upperBound")
            return errorMessage
        }

        return null
    }

    private fun computeLowerBound(supplyStartDate: LocalDate, publicHoliday: List<LocalDate>): LocalDate {
        var workDays = 0
        var lowerBound = supplyStartDate
        while (workDays < meterReadingConfig.firstReadingPeriodDaysElec) {
            lowerBound = lowerBound.minusDays(1)
            if (isWorkingDay(lowerBound, publicHoliday)) {
                workDays++
            }
        }

        if (!isWorkingDay(supplyStartDate, publicHoliday)) {
            while (!isWorkingDay(lowerBound.minusDays(1), publicHoliday)) {
                lowerBound = lowerBound.minusDays(1)
            }
        }

        return lowerBound
    }

    private fun computeUpperBound(supplyStartDate: LocalDate, publicHoliday: List<LocalDate>, days: Int): LocalDate {
        var workDays = 0
        var upperBound = supplyStartDate
        while (workDays < days) {
            if (isWorkingDay(upperBound, publicHoliday)) {
                workDays++
            }
            if (workDays < days) {
                upperBound = upperBound.plusDays(1)
            }
        }

        while (!isWorkingDay(upperBound.plusDays(1), publicHoliday)) {
            upperBound = upperBound.plusDays(1)
        }

        return upperBound
    }

    private fun isWorkingDay(
        currentDay: LocalDate,
        publicHoliday: List<LocalDate>,
    ) =
        !(currentDay.dayOfWeek == DayOfWeek.SATURDAY || currentDay.dayOfWeek == DayOfWeek.SUNDAY || publicHoliday.any {
            currentDay == it
        })

    private fun getHolidaysForReadingDate(readingDate: LocalDate): List<LocalDate> =
        calendarHolidayClient.getCalendarHolidaysForYears(
            calendarHolidaysRequest {
                year.addAll(listOf(readingDate.year - 1, readingDate.year, readingDate.year + 1))
            }
        ).calendarHolidaysList
            .mapNotNull { it.holidayDateOrNull }
            .map { it.toLocalDate() }

    private fun formatDate(date: LocalDate): String {
        val dateFormatter = DateTimeFormatter.ofPattern(WINDOW_DATE_FORMAT)

        return dateFormatter.format(date)
    }

    private fun readingWithinWindow(readingDate: LocalDate, lowerBound: LocalDate, upperBound: LocalDate): Boolean {
        return !lowerBound.isAfter(readingDate) && !upperBound.isBefore(readingDate)
    }

    private fun isElectricityMeter(subject: MeterReadData): Boolean {
        return UnitType.Gas != subject.unitType
    }

    private fun isFirstSequenceType(sequenceType: energy.so.assets.server.models.SequenceType): Boolean {
        return energy.so.assets.server.models.SequenceType.First == sequenceType
    }
}
