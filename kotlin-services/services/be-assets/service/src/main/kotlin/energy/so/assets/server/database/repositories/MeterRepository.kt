package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Meter

interface MeterRepository {

    /**
     *
     * @param id Long
     * @return Meter?
     */
    fun findById(id: Long): Meter?

    /**
     *
     * @param id Long
     * @param daysBeforeSupplyStartDate Int
     * @return Meter?
     */
    fun findByMeterPointId(id: Long, daysBeforeSupplyStartDate: Int): List<Meter>

    /**
     *
     * @param id Long
     * @return Meter?
     */
    fun findByIdentifier(identifier: String): Meter?

    /**
     *
     * @param ids List <Long>
     * @return List<Meter>
     */
    fun findByIds(ids: List<Long>): List<Meter>

    /**
     * Retrieves all meters by meter point ids of electricity type.
     * @param ids: List<Long>
     * @return List<Meter>
     */
    fun findElectricityMetersByMeterPointIds(ids: List<Long>): List<Meter>

    /**
     * Retrieves all meters by meter point ids of gas type.
     * @param ids: List<Long>
     * @return List<Meter>
     */
    fun findGasMetersByMeterPointIds(ids: List<Long>): List<Meter>

    /**
     * Retrieves all meters by meter point ids.
     * @param ids: List<Long> meter point ids
     * @return List<Meter>
     */
    fun findByMeterPointIds(ids: List<Long>): List<Meter>
}
