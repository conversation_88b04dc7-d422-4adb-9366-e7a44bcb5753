package energy.so.assets.server.services

import energy.so.assets.server.models.Register

interface RegisterService {

    /**
     * returns registers by set of meterIds
     * @param meterIds Set<Long>
     * @return List<Register>
     */
    fun getRegistersByMeterIds(meterIds: Set<Long>): List<Register>

    /**
     * returns active registers by set of meterIds
     * @param meterIds Set<Long>
     * @param daysBeforeSupplyStartDate Int in which an account can submit opening reading
     * @return List<Register>
     */
    fun getActiveRegistersByMeterIds(meterIds: Set<Long>, daysBeforeSupplyStartDate: Int): List<Register>
}
