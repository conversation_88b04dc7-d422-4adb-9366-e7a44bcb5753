package energy.so.assets.server.consumers

import energy.so.assets.server.services.MeterReadingSubmissionRequestService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

class UpdateMeterReadSubmissionStatusConsumer(
    projectName: String,
    config: SubscriptionConfiguration,
    private val meterReadingSubmissionRequestService: MeterReadingSubmissionRequestService,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug("Received message: ${message.data}")

        if (message.eventType == EventType.CREATED) {
            logger.debug { "Updating Contacted reads status" }

            meterReadingSubmissionRequestService.updateContactedReadsMap()

            logger.debug { "Contacted reads status updated" }
        }
    }
}
