package energy.so.assets.server.modules

import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.StorageConfig
import energy.so.assets.server.consumers.PhotoEvidenceFreshdeskOperationConsumer
import energy.so.assets.server.database.repositories.JooqMeterReadingPhotoEvidenceRepository
import energy.so.assets.server.database.repositories.MeterReadingPhotoEvidenceRepository
import energy.so.assets.server.services.DefaultFeatureService
import energy.so.assets.server.services.DefaultMeterReadingPhotoEvidenceService
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.MeterReadingPhotoEvidenceService
import energy.so.awsconnect.client.v1.client.CustomerSupportCenterClient
import energy.so.awsconnect.client.v1.client.impl.AwsConnectClient
import energy.so.awsconnect.client.v1.config.CustomerSupportCenterConfig
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.queues.publishers.PubsubMessagePublisher
import energy.so.freshdesk.client.v1.FreshDeskClient
import energy.so.freshdesk.client.v1.config.FreshDeskConfig
import energy.so.freshdesk.client.v1.impl.DefaultFreshDeskClient
import energy.so.freshdesk.client.v1.impl.invoke
import energy.so.users.client.v2.FeatureClient
import kotlinx.coroutines.runBlocking
import org.koin.core.qualifier.StringQualifier
import org.koin.dsl.module

object MeterReadingPhotoEvidenceModule {
    private val storageConfig = Constants.get<StorageConfig>("googleStorage")
    private val freshdeskConfig = Constants.get<FreshDeskConfig>("freshdesk")
    private val pubsubConfig = PubSubConfiguration.fromApplicationConf()
    private val freshDeskTicketCreationQualifier = StringQualifier("meter-photo-evidence-freshdesk-ticket-creation")
    private val identityConfig = Constants.get<GrpcServiceConfig>("be-identity")
    private val customerSupportCenterConfig = Constants.get<CustomerSupportCenterConfig>("awsConnect")

    val module = module {
        single { FeatureClient(identityConfig) }

        single<FeatureService> {
            DefaultFeatureService(
                featureClient = get(),
            )
        }

        single<MeterReadingPhotoEvidenceService> {
            DefaultMeterReadingPhotoEvidenceService(
                meterReadingPhotoRepository = get(),
                outboundPhotoRequestRepository = get(),
                customersClient = get(),
                storageClient = get(),
                freshdeskClient = get(),
                juniferAccountsClient = get(),
                freshDeskTicketOperationPublisher = get(qualifier = freshDeskTicketCreationQualifier),
                closeOutboundPhotoRequestHandler = get(),
                storageConfig = storageConfig,
                freshDeskConfig = freshdeskConfig,
                customerSupportCenterClient = get(),
                customerSupportCenterConfig = customerSupportCenterConfig,
                featureService = get()
            )
        }

        single<MeterReadingPhotoEvidenceRepository> {
            JooqMeterReadingPhotoEvidenceRepository(
                dslContext = get()
            )
        }

        single<MessagePublisher<String>>(createdAtStart = true, qualifier = freshDeskTicketCreationQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-photo-evidence-freshdesk-ticket")!!
            )
        }
        single {
            PhotoEvidenceFreshdeskOperationConsumer(
                meterReadingPhotoEvidenceService = get(),
                featureService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-photo-evidence-freshdesk-ticket-subs")!!,
            )
        }

        single<FreshDeskClient> {
            runBlocking {
                DefaultFreshDeskClient.Companion.invoke(
                    freshdeskConfig
                )
            }
        }

        single<CustomerSupportCenterClient> {
            AwsConnectClient(customerSupportCenterConfig)
        }
    }
}
