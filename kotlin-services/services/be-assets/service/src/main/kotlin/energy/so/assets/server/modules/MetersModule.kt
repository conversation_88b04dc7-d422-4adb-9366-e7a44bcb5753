package energy.so.assets.server.modules

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.generic.GenericSyncClient
import energy.so.assets.server.config.Constants
import energy.so.assets.server.controllers.MeterController
import energy.so.assets.server.database.repositories.JooqMeterRepository
import energy.so.assets.server.database.repositories.MeterRepository
import energy.so.assets.server.services.DefaultMeterService
import energy.so.assets.server.services.MeterService
import energy.so.assets.sync.repository.JooqMeterEntityRepository
import energy.so.assets.sync.repository.MeterEntityRepository
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.grpc.clients.junifer.JuniferClient
import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.queues.publishers.PubsubMessagePublisher
import energy.so.junifer.client.v1.JuniferAccountClient
import energy.so.junifer.client.v1.JuniferMeterClient
import energy.so.users.client.v2.FeatureClient
import org.koin.core.qualifier.StringQualifier
import org.koin.dsl.module

object MetersModule {
    private val BE_JUNIFER_QUALIFIER = StringQualifier("be-junifer-config")
    private val pubsubConfig = PubSubConfiguration.fromApplicationConf()
    private val submissionQualifier = StringQualifier("meter-readings-submission")
    private val acJuniferConfig = Constants.get<GrpcServiceConfig>("be-ac-junifer")
    private val identityConfig = Constants.get<GrpcServiceConfig>("be-identity")

    val module = module {
        single(qualifier = BE_JUNIFER_QUALIFIER) {
            Constants.get<GrpcServiceConfig>("be-junifer")
        }

        single {
            JuniferAccountClient(config = get(qualifier = BE_JUNIFER_QUALIFIER))
        }

        single {
            JuniferMeterClient(config = get(qualifier = BE_JUNIFER_QUALIFIER))
        }
        single {
            JuniferClient(config = get(qualifier = BE_JUNIFER_QUALIFIER))
        }

        single {
            AssetsClient(config = acJuniferConfig)
        }

        single {
            FeatureClient(config = identityConfig)
        }

        single {
            GenericSyncClient(config = acJuniferConfig)
        }

        single {
            MeterController(
                meterService = get(),
                featureClient = get(),
                genericSyncClient = get(),
            )
        }

        single<MessagePublisher<String>>(createdAtStart = true, qualifier = submissionQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-meter-readings-submissions")!!
            )
        }

        single<MeterService> {
            DefaultMeterService(
                meterRepository = get(),
                accountsClient = get(),
                juniferAccountClient = get(),
                juniferMeterClient = get(),
                juniferClient = get(),
                messagePublisher = get(submissionQualifier),
                assetsClient = get(),
                featureClient = get(),
            )
        }

        single<MeterRepository> {
            JooqMeterRepository(dslContext = get())
        }

        single<MeterEntityRepository> {
            JooqMeterEntityRepository(dslContext = get())
        }
    }
}
