package energy.so.assets.server.models

import energy.so.assets.meterPoints.v2.meterPointElectricity
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime
import energy.so.assets.meterPoints.v2.MeterPointElectricity as GrpcMeterPointElectricity
import energy.so.commons.model.tables.pojos.MeterPointElectricity as JooqMeterPointElectricity

/**
 * Model class representing MPAN information about meter point.
 */
data class MeterPointElectricity(
    val id: Long? = null,
    val energised: Boolean? = null,
    val lineLossFactorClassId: String? = null,
    val measurementClass: String? = null,
    val meterSerialNumber: String? = null,
    val meterTimeswitchCode: String? = null,
    val meterType: MeterType? = null,
    val smsoMarketParticipantCode: String? = null,
    val standardSettlementConfigurationCode: String? = null,
    val mopMarketParticipant: String? = null,
    val distributionNetworkOperator: String? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {

    companion object {
        fun fromJooq(
            jooq: JooqMeterPointElectricity,
        ): MeterPointElectricity = MeterPointElectricity(
            id = jooq.id,
            energised = jooq.energised,
            lineLossFactorClassId = jooq.lineLossFactorClassId,
            measurementClass = jooq.measurementClass,
            meterSerialNumber = jooq.meterSerialNumber,
            meterTimeswitchCode = jooq.meterTimeswitchCode,
            meterType = jooq.meterType?.toSiblingEnum<MeterType>(),
            smsoMarketParticipantCode = jooq.smsoMarketParticipantCode,
            standardSettlementConfigurationCode = jooq.standardSettlementConfigurationCode,
            mopMarketParticipant = jooq.mopMarketParticipant,
            distributionNetworkOperator = jooq.distributionNetworkOperator,
            deleted = jooq.deleted,
            createdAt = jooq.createdAt,
            updatedAt = jooq.updatedAt,
        )
    }
}

fun MeterPointElectricity.toProtobuf(): GrpcMeterPointElectricity = let { from ->
    meterPointElectricity {
        id = from.id!!
        from.energised?.let { energised = it }
        from.lineLossFactorClassId?.let { lineLossFactorClassId = it }
        from.meterType?.let { meterType = it.toSiblingEnum() }
        createdAt = from.createdAt!!.toTimestamp()
        updatedAt = from.updatedAt!!.toTimestamp()
        from.measurementClass?.let { measurementClass = it }
        from.meterSerialNumber?.let { meterSerialNumber = it.toNullableString() }
        from.meterTimeswitchCode?.let { meterTimeSwitchCode = it }
        from.smsoMarketParticipantCode?.let { smsoMarketParticipantCode = it.toNullableString() }
        from.standardSettlementConfigurationCode?.let { standardSettlementConfigurationCode = it.toNullableString() }
        from.mopMarketParticipant?.let { mopMarketParticipant = it.toNullableString() }
        from.distributionNetworkOperator?.let { distributionNetworkOperator = it.toNullableString() }
        from.deleted?.let { deleted = it.toNullableTimestamp() }
    }
}
