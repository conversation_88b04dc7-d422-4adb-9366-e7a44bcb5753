package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPoint
import energy.so.commons.model.tables.records.Junifer_MeterpointRecord


interface MeterPointsRepository {

    /**
     * @param id
     * @return Boolean
     * Checks  whether meter point exists or not by given id
     */
    fun existsById(id: Long): Boolean

    /**
     * @param id
     * @return Boolean
     * Checks if meter point has a valid dcc service status for submit operation
     * Dcc service status is valid if it's not NON_ACTIVE or WITHDRAWN
     */
    fun hasValidDccServiceStatusForSubmit(id: Long): Boolean

    /**
     * Retrieves meter points by given asset ids.
     *
     * @param assetIds The asset id list.
     * @return List of [MeterPoint].
     */
    fun findAllByAssetIds(assetIds: List<Long>): List<MeterPoint>

    /**
     * Retrieves a list of meter points by address id.
     *
     * @param addressIds Address ids.
     * @return List of[MeterPoint].
     */
    fun findMeterPointsByAddressIds(addressIds: List<Long>): List<MeterPoint>

    /**
     * Retrieves a list of meter points by ids.
     *
     * @param ids [MeterPoint] ids.
     * @return List of[MeterPoint].
     */
    fun findByIds(ids: List<Long>): List<MeterPoint>

    /**
     * Retrieves a list of meter points by meter ids.
     *
     * @param ids [MeterPoint] ids.
     * @return List of[MeterPoint].
     */
    fun findByMeterIds(ids: List<Long>): List<MeterPoint>

    fun findById(id: Long): MeterPoint

    fun bulkFindByMPXNs(mpxns: List<String>): List<Junifer_MeterpointRecord>
    
    fun findByMPXN(mpxn: String): MeterPoint?

    fun findByMPXNs(mpxns: List<String>): List<MeterPoint>

    fun findByIdentifier(identifier: String): MeterPoint?
}
