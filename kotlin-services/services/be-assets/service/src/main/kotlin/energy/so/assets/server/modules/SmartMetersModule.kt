package energy.so.assets.server.modules

import energy.so.ac.esg.api.v1.SmartMetersDashboardClient
import energy.so.assets.server.config.Constants
import energy.so.assets.server.controllers.SmartMeterController
import energy.so.assets.server.services.DefaultSmartMetersDashboardService
import energy.so.assets.server.services.SmartMetersDashboardService
import energy.so.commons.grpc.clients.GrpcServiceConfig
import org.koin.dsl.module

object SmartMetersModule {
    private val beEsgConfig = Constants.get<GrpcServiceConfig>("be-ac-esg")

    val module = module {
        single {
            SmartMeterController(
                dashboardService = get(),
            )
        }

        single { SmartMetersDashboardClient(config = beEsgConfig) }

        single<SmartMetersDashboardService> {
            DefaultSmartMetersDashboardService(
                acEsgClient = get(),
            )
        }
    }
}
