package energy.so.assets.server.consumers

import energy.so.assets.server.services.OutboundPhotoRequestService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

class AutomaticCloseOutboundRequestsConsumer(
    private val outboundPhotoRequestService: OutboundPhotoRequestService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug { "[::processMessage] Received message ($message) to automatic close outbound requests" }
        if (message.eventType == EventType.CREATED) {
            outboundPhotoRequestService.automaticCancelOutboundRequest()
        }
    }
}
