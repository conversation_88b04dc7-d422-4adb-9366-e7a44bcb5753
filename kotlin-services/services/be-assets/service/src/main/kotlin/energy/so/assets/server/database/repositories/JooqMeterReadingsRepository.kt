package energy.so.assets.server.database.repositories

import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.server.models.*
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.MeterReadingStatu
import energy.so.commons.model.tables.records.MeterReadingRecord
import energy.so.commons.model.tables.references.METER_READING
import energy.so.commons.search.extension.getOffset
import energy.so.commons.search.model.mapping.mapPagination
import org.jooq.DSLContext
import org.jooq.SelectConditionStep
import org.jooq.SelectForUpdateStep
import org.jooq.impl.DSL
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.commons.model.enums.MeterReadingQuality as JooqMeterReadingQuality
import energy.so.commons.model.enums.MeterReadingSource as JooqMeterReadingSource
import energy.so.commons.model.tables.pojos.MeterReading as JooqMeterReading

class JooqMeterReadingsRepository(
    private val dslContext: DSLContext,
    private val registerRepository: RegisterRepository,
) : MeterReadingsRepository {
    override fun search(searchRequest: MeterReadingsSearchRequest): List<MeterReading> =
        dslContext
            .selectFrom(METER_READING)
            .where(getSearchConditions(searchRequest))
            .let {
                addPagination(searchRequest, it)
            }
            .fetchInto(JooqMeterReading::class.java)
            .let { readings ->
                val registers =
                    registerRepository.findByIds(readings.mapNotNull { it.registerId }.toSet()).associateBy { it.id }

                readings.map {
                    MeterReading.fromJooq(it, registers[it.registerId])
                }
            }

    private fun getSearchConditions(searchRequest: MeterReadingsSearchRequest) = listOf(
        if (searchRequest.meterPointIdList.isNotEmpty()) {
            METER_READING.METER_POINT_ID.`in`(searchRequest.meterPointIdList)
        } else DSL.trueCondition(),

        if (searchRequest.meterIdList.isNotEmpty()) {
            METER_READING.METER_ID.`in`(searchRequest.meterIdList)
        } else DSL.trueCondition(),

        if (searchRequest.sourceList.isNotEmpty()) {
            METER_READING.SOURCE.`in`(searchRequest.sourceList.map { it.toSiblingEnum<JooqMeterReadingSource>() })
        } else DSL.trueCondition(),

        if (searchRequest.qualityList.isNotEmpty()) {
            METER_READING.QUALITY.`in`(searchRequest.qualityList.map { it.toSiblingEnum<JooqMeterReadingQuality>() })
        } else DSL.trueCondition(),

        if (searchRequest.hasStartDate()) {
            METER_READING.READING_DTTM.ge(searchRequest.startDate.toLocalDateTime())
        } else DSL.trueCondition(),

        if (searchRequest.hasEndDate()) {
            METER_READING.READING_DTTM.le(searchRequest.endDate.toLocalDateTime())
        } else DSL.trueCondition(),

        if (searchRequest.hasSubmissionProcessed()) {
            METER_READING.SUBMISSION_PROCESSED.equal(searchRequest.submissionProcessed)
        } else DSL.trueCondition(),

        if (searchRequest.hasDeleted()) {
            METER_READING.DELETED.isNotNull
        } else METER_READING.DELETED.isNull,

        if (searchRequest.statusList.isNotEmpty()) {
            METER_READING.STATUS.`in`(searchRequest.statusList)
        } else DSL.trueCondition()
    )

    private fun addPagination(
        searchRequest: MeterReadingsSearchRequest,
        it: SelectConditionStep<MeterReadingRecord>,
    ): SelectForUpdateStep<MeterReadingRecord> {
        val pagination = mapPagination(searchRequest.pagination)

        return if (searchRequest.onlyLast) {
            it.orderBy(METER_READING.FROM_DTTM.desc())
                .limit(1)
        } else it
            .offset(pagination.getOffset())
            .limit(pagination.pageSize)
    }

    override fun save(meterReading: MeterReading): MeterReading {
        return meterReading.toJooq()
            .let {
                dslContext.newRecord(METER_READING, it)
                    .apply { this.save() }
                    .run { into(JooqMeterReading()) }
            }
            .let { MeterReading.fromJooq(it, getRegister(it.registerId)) }
    }

    override fun saveAll(meterReadings: List<MeterReading>) {
        meterReadings.map { it.toJooq() }
            .map { dslContext.newRecord(METER_READING, it) }
            .run(dslContext::batchInsert)
            .execute()
    }

    override fun getLastElectricityMeterReading(meterPointId: Long): MeterReading? {
        return dslContext.selectFrom(METER_READING)
            .where(METER_READING.METER_POINT_ID.equal(meterPointId))
            .and(METER_READING.QUALITY.notEqual(MeterReadingQuality.ESTIMATED.toJooqEnum()))
            .and(METER_READING.STATUS.equal(MeterReadingStatu.ACCEPTED))
            .and(METER_READING.DELETED.isNull)
            .orderBy(METER_READING.READING_DTTM.desc())
            .limit(1)
            .fetchOneInto(JooqMeterReading::class.java)
            ?.let { MeterReading.fromJooq(it, getRegister(it.registerId)) }
    }

    override fun getFirstElectricityMeterReading(meterPointId: Long): MeterReading? {
        return dslContext.selectFrom(METER_READING)
            .where(METER_READING.METER_POINT_ID.equal(meterPointId))
            .and(METER_READING.QUALITY.notEqual(MeterReadingQuality.ESTIMATED.toJooqEnum()))
            .and(METER_READING.STATUS.equal(MeterReadingStatu.ACCEPTED))
            .and(METER_READING.DELETED.isNull)
            .orderBy(METER_READING.READING_DTTM)
            .limit(1)
            .fetchOneInto(JooqMeterReading::class.java)
            ?.let { MeterReading.fromJooq(it, getRegister(it.registerId)) }
    }

    override fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<MeterReading> {
        return dslContext.selectFrom(METER_READING)
            .where(
                METER_READING.METER_POINT_ID.equal(meterPointId)
                    .and(METER_READING.READING_DTTM.greaterThan(startDate))
                    .and(METER_READING.READING_DTTM.lessThan(endDate))
                    .and(METER_READING.QUALITY.notEqual(MeterReadingQuality.ESTIMATED.toJooqEnum()))
                    .and(METER_READING.STATUS.equal(MeterReadingStatu.ACCEPTED))
                    .and(METER_READING.DELETED.isNull)
            )
            .orderBy(METER_READING.READING_DTTM)
            .fetchInto(JooqMeterReading::class.java)
            .let { readings -> readings.map { MeterReading.fromJooq(it, getRegister(it.registerId)) } }
    }

    override fun getLastSmartReadingByMeterIdAndSourceInAndQualityIn(
        meterId: Long,
        sources: Set<MeterReadingSource>,
        qualities: Set<MeterReadingQuality>,
    ): MeterReading? {
        return dslContext.selectFrom(METER_READING)
            .where(
                METER_READING.SOURCE.`in`(sources.map { it.toJooqEnum() }),
                METER_READING.QUALITY.`in`(qualities.map { it.toJooqEnum() }),
                METER_READING.DELETED.isNull
            )
            .orderBy(METER_READING.FROM_DTTM.desc())
            .limit(1)
            .fetchOneInto(JooqMeterReading::class.java)
            ?.let { MeterReading.fromJooq(it, getRegister(it.registerId)) }
    }

    private fun getRegister(registerId: Long?): Register? {
        return registerId?.let { registerRepository.findByIds(setOf(registerId)) }?.firstOrNull()
    }

    override fun existsForMeterPointIdInLastYear(meterPointId: Long): Boolean {
        return dslContext.selectCount()
            .from(METER_READING)
            .where(
                METER_READING.METER_POINT_ID.equal(meterPointId),
                METER_READING.DELETED.isNull
            )
            .and(
                DSL.field("DATE({0})", LocalDate::class.java, METER_READING.READING_DTTM)
                    .greaterOrEqual(LocalDate.now().minusYears(1))
                    .and(
                        DSL.field("DATE({0})", LocalDate::class.java, METER_READING.READING_DTTM)
                            .lessOrEqual(LocalDate.now())
                    )
            )
            .fetchOneInto(Long::class.java)
            ?.let { count -> count > 0 } ?: false
    }

    override fun existsForMeterIdsAndSubmissionProcessed(
        meterIds: List<Long>,
        submissionProcessed: Boolean,
    ): Boolean {
        return dslContext.selectCount()
            .from(METER_READING)
            .where(
                METER_READING.DELETED.isNull,
                METER_READING.SUBMISSION_PROCESSED.equal(submissionProcessed),
                METER_READING.METER_ID.`in`(meterIds)
            )
            .fetchOneInto(Long::class.java)
            ?.let { count -> count > 0 } ?: false
    }

    override fun markSubmissionsProcessed(ids: List<Long>) {
        dslContext.update(METER_READING)
            .set(METER_READING.SUBMISSION_PROCESSED, true)
            .where(METER_READING.ID.`in`(ids))
            .execute()
    }
}

fun MeterReadingSource.toJooqEnum(): JooqMeterReadingSource {
    return JooqMeterReadingSource.valueOf(name)
}

fun MeterReadingQuality.toJooqEnum(): JooqMeterReadingQuality {
    return JooqMeterReadingQuality.valueOf(name)
}
