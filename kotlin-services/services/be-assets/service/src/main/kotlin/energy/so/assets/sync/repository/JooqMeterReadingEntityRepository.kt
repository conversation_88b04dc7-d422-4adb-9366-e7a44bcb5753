package energy.so.assets.sync.repository

import energy.so.assets.sync.service.getEnumOrNullWhenEmpty
import energy.so.assets.sync.service.searchEnum
import energy.so.assets.sync.v2.MeterReadingEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.MeterReadingQuality
import energy.so.commons.model.enums.MeterReadingSequenceType
import energy.so.commons.model.enums.MeterReadingSource
import energy.so.commons.model.enums.MeterReadingStatu
import energy.so.commons.model.enums.MeterReadingUnit
import energy.so.commons.model.enums.MeterReadingWorkflowStatu
import energy.so.commons.model.tables.pojos.MeterReading
import energy.so.commons.model.tables.records.MeterReadingRecord
import energy.so.commons.model.tables.references.METER_READING
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqMeterReadingEntityRepository(
    private val dslContext: DSLContext,
) : MeterReadingEntityRepository {

    private val logger = TraceableLogging.logger { }

    override fun deleteMeterReadingEntity(id: Long): Long {
        val numberOfDeletedRecords = dslContext.update(METER_READING)
            .set(METER_READING.DELETED, LocalDateTime.now())
            .where(METER_READING.ID.eq(id))
            .execute()

        if (numberOfDeletedRecords == 0) logger.warn("no MeterReading record found with id $id for delete sync")
        return id
    }

    override fun patchMeterReadingEntity(entity: MeterReadingEntity): Long {
        val updateStep: UpdateSetStep<MeterReadingRecord> = dslContext
            .update(METER_READING)
            .set(METER_READING.UPDATED_AT, LocalDateTime.now())

        if (entity.hasMeterId()) updateStep.set(METER_READING.METER_ID, entity.meterId.value)
        if (entity.hasMeterPointId()) updateStep.set(METER_READING.METER_POINT_ID, entity.meterPointId.value)
        if (entity.hasRegisterId()) updateStep.set(METER_READING.REGISTER_ID, entity.registerId.value)
        if (entity.hasReadingDttm()) {
            updateStep.set(
                METER_READING.READING_DTTM,
                entity.readingDttm.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasStatus()) {
            updateStep.set(
                METER_READING.STATUS,
                searchEnum<MeterReadingStatu>(entity.status.value)
            )
        }
        if (entity.hasSequenceType()) {
            updateStep.set(
                METER_READING.SEQUENCE_TYPE,
                searchEnum<MeterReadingSequenceType>(entity.sequenceType.value)
            )
        }
        if (entity.hasSource()) {
            updateStep.set(
                METER_READING.SOURCE,
                searchEnum<MeterReadingSource>(entity.source.value)
            )
        }
        if (entity.hasQuality()) {
            updateStep.set(
                METER_READING.QUALITY,
                searchEnum<MeterReadingQuality>(entity.quality.value)
            )
        }
        if (entity.hasCumulative()) updateStep.set(METER_READING.CUMULATIVE, entity.cumulative.value.toBigDecimal())
        if (entity.hasConsumption()) updateStep.set(METER_READING.CONSUMPTION, entity.consumption.value.toBigDecimal())
        if (entity.hasUnit()) {
            updateStep.set(
                METER_READING.UNIT,
                searchEnum<MeterReadingUnit>(entity.unit.value)
            )
        }
        if (entity.hasWorkflowStatus()) {
            updateStep.set(
                METER_READING.WORKFLOW_STATUS,
                searchEnum<MeterReadingWorkflowStatu>(entity.workflowStatus.value)
            )
        }
        if (entity.hasRateName()) updateStep.set(METER_READING.RATE_NAME, entity.rateName.value)
        if (entity.hasPendingRegisterReadingId()) {
            updateStep.set(
                METER_READING.PENDING_REGISTER_READING_ID,
                entity.pendingRegisterReadingId.value
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(METER_READING.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_READING.DELETED)
            } else {
                updateStep.set(
                    METER_READING.DELETED,
                    DSL.coalesce(METER_READING.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }
        val numUpdated = (updateStep as UpdateSetMoreStep).where(METER_READING.ID.eq(entity.id.value)).execute()
        if (numUpdated == 0) logger.warn("no MeterReading record found with id ${entity.id.value} for patch sync")

        return entity.id.value
    }

    override fun createMeterReadingEntity(entity: MeterReadingEntity): Long {
        return resolveSyncCreationFlow(
            METER_READING.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterReadingEntity,
            ::patchMeterReadingEntity,
            fun(e: MeterReadingEntity, newId: Long): MeterReadingEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterReadingEntity(entity: MeterReadingEntity, dslContext: DSLContext) = dslContext.newRecord(
        METER_READING,
        MeterReading(
            submissionProcessed = true,
            meterPointId = entity.meterPointId.value,
            meterId = entity.meterId.value,
            registerId = entity.registerId.value,
            readingDttm = entity.readingDttm.getValueOrNull()?.toLocalDateTime(),
            fromDttm = entity.fromDttm.getValueOrNull()?.toLocalDateTime(),
            status = searchEnum<MeterReadingStatu>(entity.status.value),
            sequenceType = searchEnum<MeterReadingSequenceType>(entity.sequenceType.value),
            source = searchEnum<MeterReadingSource>(entity.source.value),
            quality = searchEnum<MeterReadingQuality>(entity.quality.value),
            cumulative = entity.cumulative.value.toBigDecimal(),
            consumption = entity.consumption.value.toBigDecimal(),
            unit = getEnumOrNullWhenEmpty<MeterReadingUnit>(entity.unit.value),
            workflowStatus = getEnumOrNullWhenEmpty<MeterReadingWorkflowStatu>(entity.workflowStatus.value),
            rateName = entity.rateName.value,
            pendingRegisterReadingId = entity.pendingRegisterReadingId.value,
            createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
            deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
        )
    )
        .apply { save() }
        .run { into(MeterReading()) }
        .let { it.id!! }
}
