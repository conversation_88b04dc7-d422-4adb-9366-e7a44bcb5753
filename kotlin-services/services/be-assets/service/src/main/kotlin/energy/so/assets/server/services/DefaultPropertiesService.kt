package energy.so.assets.server.services

import energy.so.assets.server.database.repositories.PropertyRepository
import energy.so.assets.server.models.CreatePropertyDto
import energy.so.assets.server.models.PropertyDto
import energy.so.assets.server.models.toProperty
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.logging.TraceableLogging

private val logger = TraceableLogging.logger { }

class DefaultPropertiesService(
    private val propertyRepository: PropertyRepository,
) : PropertiesService {
    override fun createProperty(createPropertyDto: CreatePropertyDto): PropertyDto {
        return createPropertyDto.toProperty()
            .let {
                // TODO update propertyRepository.save to receive and return a model instead
                TODO("Will be implemented")
            }
    }

    override fun getProperty(id: Long): PropertyDto {
        return propertyRepository.findById(id)
            ?.let {
                TODO("Will be implemented")
            }
            ?: throw EntityNotFoundException("Property with $id does not exist")
    }
}
