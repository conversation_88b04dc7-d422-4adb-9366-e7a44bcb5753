package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.EmailType
import energy.so.assets.server.models.OutboundPhotoRequest
import energy.so.assets.server.models.OutboundPhotoRequestFilter
import energy.so.assets.server.models.OutboundPhotoRequestStatus

interface OutboundPhotoRequestRepository {

    /**
     * Creates a new [OutboundPhotoRequest] into db
     *
     * @param outboundPhotoRequest The [OutboundPhotoRequest] data to be saved.
     * @return Saved [OutboundPhotoRequest]
     */
    fun save(outboundPhotoRequest: OutboundPhotoRequest): OutboundPhotoRequest

    /**
     * Verifies if there is an open request for the given accountNumber
     *
     * @param accountNumber The account number.
     * @return [Boolean] true if open request exists, false otherwise
     */
    fun accountNumberHasOpenRequest(accountNumber: String): Boolean

    /**
     * Searches for an [OutboundPhotoRequest] that fits the filters
     *
     * @param outboundPhotoRequestFilter the filters to apply to the search
     * @return A nullable [OutboundPhotoRequest] that matches the criteria in the filters
     */
    fun findById(outboundPhotoRequestFilter: OutboundPhotoRequestFilter): OutboundPhotoRequest?

    /**
     * Update status of [OutboundPhotoRequest] with given id
     *
     * @param id The id of the outbound photo request
     * @param newStatus The new status to be set
     * @param freshdeskTicketId The id of freshdesk ticket to be set
     * @param emailType The type of the email that was sent for the request
     * @return The updated [OutboundPhotoRequest]
     */
    fun updateStatus(
        id: Long,
        newStatus: OutboundPhotoRequestStatus,
        freshdeskTicketId: Long? = null,
        caseId: String? = null,
        emailMessageId: Long? = null,
        emailType: EmailType? = null,
    ): OutboundPhotoRequest?

    /**
     * Retrieves a list of active outbound requests for given account number or empty list if there is no active request.
     * @param accountNumber
     * @return [OutboundPhotoRequest]
     */
    fun getActiveOutboundRequests(accountNumber: String): List<OutboundPhotoRequest>

    /**
     * Retrieves list of all cancelable outbound requests.
     * An outbound request is cancelable if it's in SECOND_FOLLOW_UP_SENT status and second follow up was sent maxActivityPeriodDays days ago
     * @param maxActivityPeriodDays
     * @return [OutboundPhotoRequest] or empty list if no such request
     */
    fun getCancelableOutboundRequest(maxActivityPeriodDays: Long): List<OutboundPhotoRequest>

    /**
     * Cancels an outbound request.
     * @param id of outbound request
     */
    fun cancelOutboundRequest(id: Long)
}
