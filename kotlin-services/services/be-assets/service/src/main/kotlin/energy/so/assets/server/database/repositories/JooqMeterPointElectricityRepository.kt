package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointElectricity
import energy.so.commons.model.tables.references.METER_POINT_ELECTRICITY
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.MeterPointElectricity as JooqMeterPointElectricity

class JooqMeterPointElectricityRepository(
    private val dslContext: DSLContext,
) : MeterPointElectricityRepository {

    override fun findById(id: Long): MeterPointElectricity? =
        dslContext.selectFrom(METER_POINT_ELECTRICITY)
            .where(METER_POINT_ELECTRICITY.ID.eq(id), METER_POINT_ELECTRICITY.DELETED.isNull)
            .fetchOneInto(JooqMeterPointElectricity::class.java)
            ?.let(MeterPointElectricity::fromJooq)
}
