package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.AddressEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.tables.records.AddressRecord
import energy.so.commons.model.tables.references.ADDRESS
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Address as JooqAddress

class JooqAddressEntityRepository(private val dslContext: DSLContext) : AddressEntityRepository {

    override fun deleteAddressEntity(id: Long): Long {
        dslContext.update(ADDRESS)
            .set(ADDRESS.DELETED, LocalDateTime.now())
            .where(ADDRESS.ID.eq(id))
            .execute()
        return id
    }

    override fun patchAddressEntity(entity: AddressEntity): Long {
        val updateStep: UpdateSetStep<AddressRecord> = dslContext
            .update(ADDRESS)
            .set(ADDRESS.UPDATED_AT, LocalDateTime.now())

        if (entity.hasAddress1()) updateStep.set(ADDRESS.ADDRESS1, entity.address1.value)
        if (entity.hasAddress2()) updateStep.set(ADDRESS.ADDRESS2, entity.address2.value)
        if (entity.hasAddress3()) updateStep.set(ADDRESS.ADDRESS3, entity.address3.value)
        if (entity.hasAddress4()) updateStep.set(ADDRESS.ADDRESS4, entity.address4.value)
        if (entity.hasAddress5()) updateStep.set(ADDRESS.ADDRESS5, entity.address5.value)
        if (entity.hasAddress6()) updateStep.set(ADDRESS.ADDRESS6, entity.address6.value)
        if (entity.hasAddress7()) updateStep.set(ADDRESS.ADDRESS7, entity.address7.value)
        if (entity.hasAddress8()) updateStep.set(ADDRESS.ADDRESS8, entity.address8.value)
        if (entity.hasAddress9()) updateStep.set(ADDRESS.ADDRESS9, entity.address9.value)
        if (entity.hasPostcode()) updateStep.set(ADDRESS.POSTCODE, entity.postcode.value)
        if (entity.hasCountryCode()) updateStep.set(ADDRESS.COUNTRY_CODE, entity.countryCode.value)
        if (entity.hasType()) updateStep.set(ADDRESS.TYPE, entity.type.value)
        if (entity.hasCreatedAt()) {
            updateStep.set(ADDRESS.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(ADDRESS.DELETED)
            } else {
                updateStep.set(ADDRESS.DELETED, DSL.coalesce(ADDRESS.DELETED, DSL.value(it.toLocalDateTime())))
            }
        }

        (updateStep as UpdateSetMoreStep).where(ADDRESS.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createAddressEntity(entity: AddressEntity): Long {
        return resolveSyncCreationFlow(
            ADDRESS.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateAddressEntity,
            ::patchAddressEntity,
            fun(e: AddressEntity, newId: Long): AddressEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateAddressEntity(entity: AddressEntity, dslContext: DSLContext): Long = dslContext.newRecord(
        ADDRESS,
        JooqAddress(
            type = entity.type.value,
            address1 = entity.address1.value,
            address2 = entity.address2.value,
            address3 = entity.address3.value,
            address4 = entity.address4.value,
            address5 = entity.address5.value,
            address6 = entity.address6.value,
            address7 = entity.address7.value,
            address8 = entity.address8.value,
            address9 = entity.address9.value,
            postcode = entity.postcode.value,
            countryCode = entity.countryCode.value,
            createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
            deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
        )
    )
        .apply { save() }
        .run { into(JooqAddress()) }.id!!
}
