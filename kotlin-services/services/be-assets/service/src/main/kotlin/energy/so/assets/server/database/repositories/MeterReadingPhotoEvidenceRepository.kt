package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterReadingPhotoEvidence

/**
 * Repository interface exposing all database operations done on [MeterReadingPhotoEvidence]
 */
interface MeterReadingPhotoEvidenceRepository {

    /**
     * Creates a new [MeterReadingPhotoEvidence] into local db.
     *
     * @param meterReadingPhotoEvidence The [MeterReadingPhotoEvidence] data to be saved.
     * @return Saved [MeterReadingPhotoEvidence].
     */
    fun save(meterReadingPhotoEvidence: MeterReadingPhotoEvidence): MeterReadingPhotoEvidence?

    /**
     * Returns a list of [MeterReadingPhotoEvidence] for given account number.
     *
     * @param accountNumberParam The account number.
     * @return List of [MeterReadingPhotoEvidence] matching the given account number.
     */
    fun getByAccountNumber(accountNumberParam: String): List<MeterReadingPhotoEvidence>

    /**
     * Returns a MeterReadingPhotoEvidence for given imageIdentifier.
     *
     * @param imageIdentifier .
     * @return [MeterReadingPhotoEvidence] matching the given imageIdentifier.
     */
    fun getByImageIdentifier(imageIdentifier: String): MeterReadingPhotoEvidence?

    /**
     * Returns a MeterReadingPhotoEvidence for given outboundRequestId.
     *
     * @param outboundRequestId .
     * @return [MeterReadingPhotoEvidence] matching the given outboundRequestId.
     */
    fun getByOutboundRequestId(outboundRequestId: Long): List<MeterReadingPhotoEvidence>

    /**
     * Delete photo evidence for given image identifier.
     *
     * @param imageIdentifier The image identifier.
     */
    fun deleteByImageIdentifier(imageIdentifier: String)

    /**
     * Set status to DONE for photo evidence by its identifier, and set its type and freshdesk ticket id.
     * @param imageIdentifier
     * @param imageType
     * @param freshDeskTicketId
     */
    fun markAsDoneAndSetFreshdeskTicket(
        imageIdentifier: String,
        imageType: String,
        freshDeskTicketId: Long?,
        caseId: String?,
    )
}
