package energy.so.assets.server.modules

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.ShadowTrafficConfig
import energy.so.assets.server.controllers.PostcodeGspController
import energy.so.assets.server.database.repositories.JooqPostcodeGspCacheRepository
import energy.so.assets.server.database.repositories.PostcodeGspCacheRepository
import energy.so.assets.server.services.DefaultPostcodeGspService
import energy.so.assets.server.services.PostcodeGspService
import energy.so.commons.grpc.clients.GrpcServiceConfig
import org.koin.dsl.module

object PostcodeGspModule {

    private val acConfig = Constants.get<GrpcServiceConfig>("be-ac-junifer")
    private val shadowTraffic = Constants.get<ShadowTrafficConfig>("shadowTraffic")

    val module = module {
        single {
            PostcodeGspController(
                postcodeGspService = get(),
            )
        }

        single { AssetsClient(config = acConfig) }

        single<PostcodeGspService> {
            DefaultPostcodeGspService(
                postcodeGspCacheRepository = get(),
                assetsClient = get(),
                shadowTrafficConfig = shadowTraffic,
            )
        }

        single<PostcodeGspCacheRepository> {
            JooqPostcodeGspCacheRepository(
                dslContext = get(),
            )
        }
    }
}
