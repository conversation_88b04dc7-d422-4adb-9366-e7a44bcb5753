package energy.so.assets.server.extensions

import energy.so.assets.meter.v2.MeterInfoAcc
import energy.so.assets.meter.v2.MeterInfoResponse
import energy.so.assets.meter.v2.meterInfoAcc

fun MeterInfoResponse.toMeterInfoAccList(accNum: String): List<MeterInfoAcc> = let {
    it.meterInfoList.map { meterInfo ->
        meterInfoAcc {
            identifier = meterInfo.identifier
            meterSerialNumber = meterInfo.meterSerialNumber
            registerId = meterInfo.registerId
            unit = meterInfo.unit
            type = meterInfo.type
            meterPointId = meterInfo.meterPointId
            meterType = meterInfo.meterType
            accountNumber = accNum
        }
    }
}
