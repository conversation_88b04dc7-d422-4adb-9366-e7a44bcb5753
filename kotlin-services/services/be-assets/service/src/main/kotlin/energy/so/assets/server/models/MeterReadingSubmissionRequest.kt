package energy.so.assets.server.models

import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.model.enums.MeterReadingStatu
import energy.so.commons.model.enums.MeterReadingSubmissionRequestStatu
import java.time.LocalDate
import java.time.LocalDateTime
import kotlinx.serialization.builtins.ListSerializer
import org.jooq.JSON
import energy.so.commons.model.tables.pojos.MeterReadingSubmissionRequest as JooqMeterReadingSubmissionRequest

data class MeterReadingSubmissionRequest(
    val id: Long? = null,
    val email: String,
    val accountNumber: String,
    val submissionData: MeterReadingsRequestDto,
    val status: MeterReadingSubmissionRequestStatus,
    val debugData: JSON? = null,
    val lastAttempt: LocalDateTime? = null,
    val failedAttempts: Int = 0,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val submissionValues: List<SubmissionValue> = emptyList(),
    val meterPointIdentifier: String? = null,
    val juniferStatus: MeterReadingStatus? = null,
) {
    companion object {
        fun fromJooq(pojo: JooqMeterReadingSubmissionRequest): MeterReadingSubmissionRequest {
            val submissionData = pojo.submissionData!!.data()
                .let { jsonFormat.decodeFromString(MeterReadingsRequestDto.serializer(), it) }

            val submissionValues = pojo.submissionValues!!.data()
                .let { jsonFormat.decodeFromString(ListSerializer(SubmissionValue.serializer()), it) }

            return MeterReadingSubmissionRequest(
                id = pojo.id,
                email = pojo.userEmail ?: "",
                accountNumber = pojo.accountNumber ?: "",
                submissionData = submissionData,
                status = pojo.status!!.toSiblingEnum(),
                debugData = pojo.debugData,
                lastAttempt = pojo.lastAttempt,
                failedAttempts = pojo.failedAttempts ?: 0,
                deleted = pojo.deleted,
                createdAt = pojo.createdAt,
                updatedAt = pojo.updatedAt,
                submissionValues = submissionValues,
                meterPointIdentifier = pojo.meterPointIdentifier,
                juniferStatus = pojo.juniferStatus?.toSiblingEnum<MeterReadingStatus>()
            )
        }
    }
}

enum class MeterReadingSubmissionRequestStatus {
    PERSISTED,
    QUEUED,
    SUBMITTED,
    ERRORED,
    CONTACTED,
    MANUAL_SUBMISSION,
    NO_ACTION_REQUIRED;

    companion object {
        fun toMeterReadingSubmissionStatus(meterReadingSubmissionRequestStatus: MeterReadingSubmissionRequestStatus): MeterReadingSubmissionStatus {
            return when (meterReadingSubmissionRequestStatus) {
                QUEUED, PERSISTED -> MeterReadingSubmissionStatus.PENDING
                ERRORED -> MeterReadingSubmissionStatus.ERROR
                CONTACTED -> MeterReadingSubmissionStatus.CONTACTED
                SUBMITTED -> MeterReadingSubmissionStatus.SUBMITTED
                MANUAL_SUBMISSION -> MeterReadingSubmissionStatus.MANUAL_SUBMISSION
                NO_ACTION_REQUIRED -> MeterReadingSubmissionStatus.NO_ACTION_REQUIRED
            }
        }
    }
}

fun MeterReadingSubmissionRequest.toJooq() = JooqMeterReadingSubmissionRequest(
    id = id,
    submissionData = JSON.valueOf(
        jsonFormat.encodeToString(
            MeterReadingsRequestDto.serializer(),
            submissionData
        )
    ),
    meterpointId = submissionData.meterPointId,
    status = status.toSiblingEnum<MeterReadingSubmissionRequestStatu>(),
    debugData = debugData,
    lastAttempt = lastAttempt,
    userEmail = email,
    accountNumber = accountNumber,
    deleted = deleted,
    createdAt = createdAt,
    updatedAt = updatedAt,
    failedAttempts = failedAttempts,
    submissionValues = JSON.valueOf(
        jsonFormat.encodeToString(ListSerializer(SubmissionValue.serializer()), submissionValues)
    ),
    meterPointIdentifier = meterPointIdentifier,
    juniferStatus = juniferStatus?.toSiblingEnum<MeterReadingStatu>()
)

fun MeterReadingSubmissionRequest.toMeterReadingStatusResponse() = MeterReadingStatusResponse(
    submissionTime = createdAt!!,
    submissionValues = submissionValues,
    meterReadingStatusType = MeterReadingSubmissionRequestStatus.toMeterReadingSubmissionStatus(status),
    id = id,
    email = email,
    accountNumber = accountNumber,
    submissionErrors = debugData?.data()?.let { jsonFormat.decodeFromString<List<SubmissionError>>(it) }
)

fun MeterReadingsRequestDto.toPersistedMeterReadingSubmissionRequest(
    userEmail: String,
    accountNumber: String,
    meterPointIdentifier: String?,
) =
    MeterReadingSubmissionRequest(
        submissionData = this,
        email = userEmail,
        accountNumber = accountNumber,
        status = MeterReadingSubmissionRequestStatus.PERSISTED,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        submissionValues = when (this) {
            is MeterReadingsWithoutTechnicalDetailsDto -> this.registerReads.map {
                it.getSubmissionValue(
                    this.readingDate
                )
            }

            is MeterReadingsWithTechnicalDetailsDto -> {
                val isElecMultiRate = this.readings.any { read -> read.unitType == UnitType.Electricity_Night }

                this.readings.map {
                    it.getSubmissionValue(isElecMultiRate)
                }
            }
        },
        meterPointIdentifier = meterPointIdentifier,
        juniferStatus = MeterReadingStatus.PENDING
    )

private fun Read.getSubmissionValue(readingDate: LocalDate): SubmissionValue {
    return SubmissionValue(
        value = this.reading,
        type = this.readingType,
        date = readingDate,
        meterType = this.unitType
    )
}

private fun MeterReadings.getSubmissionValue(isElecMultiRate: Boolean): SubmissionValue {
    return SubmissionValue(
        registerId = this.registerId,
        value = this.cumulative,
        type = getReadingType(isElecMultiRate),
        date = this.readingDateTime.toLocalDate(),
        meterType = this.unitType
    )
}

private fun MeterReadings.getReadingType(isMultiRateMeter: Boolean): ReadingType {
    return when (isMultiRateMeter) {
        true -> if (unitType == UnitType.Electricity_Night) {
            ReadingType.Night
        } else ReadingType.Day

        false -> ReadingType.Standard
    }
}
