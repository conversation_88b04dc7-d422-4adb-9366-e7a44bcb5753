package energy.so.assets.server.controllers

import energy.so.ac.junifer.v1.generic.EntityIdentifier
import energy.so.ac.junifer.v1.generic.GenericSyncClient
import energy.so.ac.junifer.v1.generic.getCoreMappingRequest
import energy.so.assets.meter.v2.MeterInfoListRequest
import energy.so.assets.meter.v2.MeterInfoListResponse
import energy.so.assets.meter.v2.MeterInfoRequest
import energy.so.assets.meter.v2.MeterInfoResponse
import energy.so.assets.meter.v2.MeterReadInfoResponse
import energy.so.assets.meter.v2.MetersGrpcKt
import energy.so.assets.meter.v2.MetersResponse
import energy.so.assets.meter.v2.meterInfoListResponse
import energy.so.assets.meter.v2.metersResponse
import energy.so.assets.server.extensions.toMeterInfoAccList
import energy.so.assets.server.models.MeterInfoResponseDto
import energy.so.assets.server.models.toMeterInfoResponse
import energy.so.assets.server.models.toMeterReadInfoResponse
import energy.so.assets.server.models.toProtobuf
import energy.so.assets.server.services.MeterService
import energy.so.commons.exceptions.grpc.UnsupportedGrpcException
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.dtos.IdRequestList
import energy.so.users.client.v2.FeatureClient
import energy.so.users.v2.FeatureName
import energy.so.users.v2.featureNameRequest
import io.opentelemetry.instrumentation.annotations.WithSpan

private val logger = TraceableLogging.logger {}

class MeterController(
    private val meterService: MeterService,
    private val featureClient: FeatureClient,
    private val genericSyncClient: GenericSyncClient,
) : MetersGrpcKt.MetersCoroutineImplBase() {

    @WithSpan
    override suspend fun getMeterInfo(meterInfoRequest: MeterInfoRequest): MeterInfoResponse = try {
        meterService.getMeterInfo(meterInfoRequest.accountNumber).toMeterInfoResponse()
    } catch (e: IllegalStateException) {
        throw UnsupportedGrpcException(message = e.message, cause = e)
    }

    @WithSpan
    override suspend fun getMeterInfoList(request: MeterInfoListRequest): MeterInfoListResponse {

        val meterInfoResponseDtosByAccNo = request.accountNumbersList.map { eachAccNo ->
            try {
                meterService.getMeterInfo(eachAccNo) to eachAccNo
            } catch(e: Exception) {
                logger.warn("Unable to get meter details for account number $eachAccNo - ${e.message}")
                MeterInfoResponseDto(meterInfo = listOf()) to eachAccNo
            }
        }

        val meterInfos = meterInfoResponseDtosByAccNo.map { (dto, accNo) ->
            dto.toMeterInfoResponse().toMeterInfoAccList(accNo)
        }.flatten()

        return meterInfoListResponse { meterInfoList.addAll(meterInfos) }
    }

    @WithSpan
    override suspend fun getMeterReadInfo(request: IdRequest): MeterReadInfoResponse {
        try {
            val useAcJuniferCallEnabled = featureClient.getFeature(
                featureNameRequest { name = FeatureName.TMP_SO_22512_USE_AC_JUNIFER_READINGS }
            ).enabled

            val juniferMeterPointId = request.id

            return if (useAcJuniferCallEnabled) {
                val coreMeterPointId = genericSyncClient.getCoreMapping(
                    getCoreMappingRequest {
                        juniferId = juniferMeterPointId.toString()
                        entityType = EntityIdentifier.METER_POINT
                    }
                ).id.value.toLong()

                // set the junifer id on the response to avoid modifying the expected output
                val modifiedReadInfo = meterService.getMeterReadInfo(coreMeterPointId)
                    .meterReadInfo
                    .map { it.copy(meterPointId = juniferMeterPointId.toString()) }

                energy.so.assets.server.models.MeterReadInfoResponse(modifiedReadInfo).toMeterReadInfoResponse()
            } else {
                meterService.getMeterReadInfo(juniferMeterPointId).toMeterReadInfoResponse()
            }
        } catch (e: IllegalStateException) {
            throw UnsupportedGrpcException(message = e.message, cause = e)
        }
    }

    @WithSpan
    override suspend fun getMeters(request: IdRequestList): MetersResponse {
        return meterService.getMetersByIds(request.idRequestsList)
            .map { meter -> meter.toProtobuf() }
            .let { metersResponse { meter.addAll(it) } }
    }

    @WithSpan
    override suspend fun getMetersByMeterPointIds(request: IdRequestList): MetersResponse {
        return meterService.getMetersByMeterPointIds(request.idRequestsList)
            .map { meter -> meter.toProtobuf() }
            .let { metersResponse { meter.addAll(it) } }
    }
}
