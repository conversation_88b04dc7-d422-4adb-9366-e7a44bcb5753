package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Asset

interface AssetRepository {

    /**
     * Find [Asset] by id
     *
     * @param id
     * @return the [Asset] found or null
     */
    fun findById(id: Long): Asset?

    /**
     * Find [Asset] by ids
     *
     * @param ids
     * @return
     */
    fun findByIds(ids: List<Long>): List<Asset>
}
