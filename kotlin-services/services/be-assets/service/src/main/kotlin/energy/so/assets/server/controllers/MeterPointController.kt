package energy.so.assets.server.controllers

import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.getCustomerConsentsRequest
import energy.so.assets.meterPoints.v2.EstimatedUsageResponse
import energy.so.assets.meterPoints.v2.GetCurrentSmartMeterReadingFrequencyRequest
import energy.so.assets.meterPoints.v2.GetCurrentSmartMeterReadingFrequencyResponse
import energy.so.assets.meterPoints.v2.GetMPXNsByMeterPointIdsRequest
import energy.so.assets.meterPoints.v2.GetMPXNsByMeterPointIdsResponse
import energy.so.assets.meterPoints.v2.GetMeterPointsRequest
import energy.so.assets.meterPoints.v2.MeterIdToRegistersResponse
import energy.so.assets.meterPoints.v2.MeterPointResponse
import energy.so.assets.meterPoints.v2.MeterPointsGrpcKt
import energy.so.assets.meterPoints.v2.MeterPointsRequest
import energy.so.assets.meterPoints.v2.MeterPointsResponse
import energy.so.assets.meterPoints.v2.MpxnSmartPayAsYouGoEligibilityRequest
import energy.so.assets.meterPoints.v2.MpxnSmartPayAsYouGoEligibilityResponse
import energy.so.assets.meterPoints.v2.SmartMeterEvTariffEligibilityRequest
import energy.so.assets.meterPoints.v2.SmartMeterEvTariffEligibilityResponse
import energy.so.assets.meterPoints.v2.UpdateSmartMeterReadingFrequencyRequest
import energy.so.assets.meterPoints.v2.UpdateSmartMeterReadingFrequencyResponse
import energy.so.assets.meterPoints.v2.estimatedUsageResponse
import energy.so.assets.meterPoints.v2.getCurrentSmartMeterReadingFrequencyResponse
import energy.so.assets.meterPoints.v2.getMPXNsByMeterPointIdsResponse
import energy.so.assets.meterPoints.v2.meterPointResponse
import energy.so.assets.meterPoints.v2.meterPointsResponse
import energy.so.assets.meterPoints.v2.updateSmartMeterReadingFrequencyError
import energy.so.assets.meterPoints.v2.updateSmartMeterReadingFrequencyResponse
import energy.so.assets.meterReadings.v2.MeterReadingStatusMessage
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.models.FrequencyConsentUpdateDto.Companion.fromProto
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.MeterPointType
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.Register
import energy.so.assets.server.models.isSmartMeter
import energy.so.assets.server.models.toProto
import energy.so.assets.server.models.toProtobuf
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.MeterReadingsService
import energy.so.assets.server.services.RegisterService
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.dtos.IdRequestList
import energy.so.commons.v2.dtos.IdRequestStr
import energy.so.commons.v2.search.pagination
import energy.so.users.v2.FeatureName
import io.opentelemetry.instrumentation.annotations.WithSpan

const val DAILY_CONSENT = "Daily"
private const val PAGE_SIZE = 10_000
private val logger = TraceableLogging.logger {}

class MeterPointController(
    private val meterPointService: MeterPointService,
    private val meterReadingsService: MeterReadingsService,
    private val registerService: RegisterService,
    private val meterReadingConfig: MeterReadingConfig,
    private val customerClient: CustomersSyncClient,
    private val featureService: FeatureService,
) : MeterPointsGrpcKt.MeterPointsCoroutineImplBase() {

    @WithSpan
    override suspend fun getMeterPointsByAssetIds(
        request: IdRequestList,
    ): MeterPointsResponse {
        return meterPointService.getMeterPointsByAssetIds(request.idRequestsList)
            .let {
                meterPointsResponse {
                    val readingsToMeterPointId = getMeterReadingToMeterPointId(it)
                    meterPoints.addAll(
                        it.toProtobuf(readingsToMeterPointId)
                    )
                }
            }
    }

    @WithSpan
    override suspend fun getEstimatedUsage(request: IdRequest): EstimatedUsageResponse {
        logger.debug { "[::getEstimatedUsage] Getting estimated usage for meterPointId: ${request.id}" }
        return meterPointService.getEstimatedUsage(request.id)
            .let {
                estimatedUsageResponse {
                    it.map { e -> e.toResponse() }
                        .forEach { e -> estimates.add(e) }
                }
            }
    }

    @WithSpan
    override suspend fun getMeterPointsByAddressIds(
        request: IdRequestList,
    ): MeterPointsResponse =
        meterPointService.getMeterPointsByAddressIds(request.idRequestsList)
            .let {
                meterPointsResponse { meterPoints.addAll(it.toProtobuf()) }
            }

    @WithSpan
    override suspend fun getMeterPointsByIds(request: GetMeterPointsRequest): MeterPointsResponse {
        return meterPointService.getMeterPointsByIds(request.idsList)
            .let {
                val readingsToMeterPointId = retrieveReadingMap(request, it)
                val registersToMeterId = getRegisterToMeterId(it)

                logger.debug(
                    "[::getMeterPointsByIds]" +
                            "Preparing response for meter point ids ${it.map { meterPoint -> meterPoint.id }}"
                )
                meterPointsResponse {
                    meterPoints.addAll(
                        it.toProtobuf(
                            readingsToMeterPointId,
                            registersToMeterId
                        )
                    )
                }
            }
    }

    @WithSpan
    override suspend fun getMPXNsByMeterPointIds(request: GetMPXNsByMeterPointIdsRequest): GetMPXNsByMeterPointIdsResponse {
        try {
            logger.debug { "[::getMxnsByIds] Getting MPXNs for meterPointIds: ${request.idsList}" }
            val meterPointList = meterPointService.getMeterPointsByIds(request.idsList).map { it.toMPXNProto() }
            return getMPXNsByMeterPointIdsResponse {
                mPXNList.addAll(meterPointList)
            }
        } catch (e: Exception) {
            logger.error(e) { "[::getMxnsByIds] Failed to retrieve MPXNs for for meterPointIds: ${request.idsList}" }

            throw UnknownGrpcException(
                message = e.message ?: "Failed to retrieve MPXNs for for meterPointIds: ${request.idsList}",
                errorCategory = ErrorCategories.COMMONS,
                errorCode = ErrorCodes.COMMONS_UNHANDLED_ERROR,
                cause = e.cause
            )
        }
    }

    override suspend fun getMeterIdToRegisters(request: IdRequest): MeterIdToRegistersResponse {
        return meterPointService.getMeterIdToRegisters(request.id)
    }

    @WithSpan
    override suspend fun getMeterPointByMPXN(request: IdRequestStr): MeterPointResponse {
        val mpxn = meterPointService.getMeterPointByMPXN(request.id)
        return if (mpxn == null) {
            meterPointResponse { }
        } else {
            meterPointResponse {
                meterPoint = meterPointService.getMeterPointByMPXN(request.id)?.toProtobuf(listOf(), mapOf())!!
            }
        }
    }

    @WithSpan
    override suspend fun getMeterPointsByMPXNs(request: MeterPointsRequest): MeterPointsResponse {
        return meterPointService.getMeterPointsByMPXNs(request.mpxnIdentifierList)
    }

    @WithSpan
    override suspend fun checkSmartMeterEvTariffEligibility(request: SmartMeterEvTariffEligibilityRequest): SmartMeterEvTariffEligibilityResponse {
        logger.debug {
            "[::checkSmartMeterEvTariffEligibility] Checking SmartMeterEvTariffEligibility for meterPoint Identifier: ${request.meterPointIdentifier}"
        }
        return meterPointService.checkSmartMeterEvTariffEligibility(request.meterPointIdentifier)
    }

    @WithSpan
    override suspend fun checkMpxnSmartPayAsYouGoEligibility(
        request: MpxnSmartPayAsYouGoEligibilityRequest,
    ): MpxnSmartPayAsYouGoEligibilityResponse {
        return meterPointService.checkMpxnSmartPayAsYouGoEligibility(request.meterPointIdsList)
    }

    @WithSpan
    override suspend fun getCurrentSmartMeterReadingFrequency(
        request: GetCurrentSmartMeterReadingFrequencyRequest,
    ): GetCurrentSmartMeterReadingFrequencyResponse {
        return getCurrentSmartMeterReadingFrequencyResponse {
            consents.addAll(
                meterPointService.getCurrentSmartMeterReadingFrequency(
                    request.mpxnList,
                    request.validDt.getValueOrNull()?.toLocalDate()
                ).toProto()
            )
        }
    }

    @WithSpan
    override suspend fun updateSmartMeterReadingFrequency(
        request: UpdateSmartMeterReadingFrequencyRequest,
    ): UpdateSmartMeterReadingFrequencyResponse {
        try {
            meterPointService.updateSmartMeterReadingFrequency(
                request.consentsList.map { fromProto(it) }
            )
            return updateSmartMeterReadingFrequencyResponse { success = true }
        } catch (e: Exception) {
            logger.error(e) {
                "[::updateSmartMeterReadingFrequency] Failed to update smart meter reading frequency for requested " +
                        "mpxns: {${request.consentsList.map { it.mpxn }}, message: ${e.message}"
            }

            return updateSmartMeterReadingFrequencyResponse {
                success = false
                error = updateSmartMeterReadingFrequencyError {
                    code = ErrorCodes.COMMONS_UNHANDLED_ERROR
                    category = ErrorCategories.ASSETS
                    message = e.message.toString()
                }
            }
        }
    }

    private suspend fun retrieveReadingMap(
        request: GetMeterPointsRequest,
        meterPointList: List<MeterPoint>,
    ): Map<Long, List<MeterReading>> =
        // Always include readings, unless requested not to
        if (request.hasIncludeMeterReadings() && !request.includeMeterReadings) {
            emptyMap()
        } else {
            getMeterReadingToMeterPointId(
                meterPointList = meterPointList,
                enableAllHistoricalReadings = request.enableAllHistoricalReadings,
                includeCancelledAndDeletedAgreements = if (request.hasIncludeCancelledAndDeletedAgreements()) {
                    request.includeCancelledAndDeletedAgreements
                } else {
                    false
                },
                productAccountId = if (request.hasProductAccountId()) request.productAccountId else null
            )
        }

    private suspend fun getMeterReadingToMeterPointId(
        meterPointList: List<MeterPoint>,
        enableAllHistoricalReadings: Boolean = false,
        includeCancelledAndDeletedAgreements: Boolean = false,
        productAccountId: Long? = null,
    ): Map<Long, List<MeterReading>> {

        val juniferReads = searchReads(
            meterPointList,
            enableAllHistoricalReadings,
            includeCancelledAndDeletedAgreements,
            productAccountId,
        )

        val dailyReads =
            if (featureService.isFeatureEnabled(FeatureName.TMP_SO_23911_DAILY_READINGS_FROM_METER_POINT)) {

                // Check whether any meterpoint has ever consented to daily
                val dailyConsent = checkConsent(meterPointList)

                if (dailyConsent) {
                    searchReads(
                        meterPointList,
                        enableAllHistoricalReadings,
                        includeCancelledAndDeletedAgreements,
                        productAccountId,
                        energy.so.assets.meterReadings.v2.ReadFrequency.DAILY_FREQUENCY,
                    )
                } else emptyList()
            } else emptyList()

        return (juniferReads + dailyReads).groupBy { it.meterPointId }
    }


    private fun getRegisterToMeterId(meterPointList: List<MeterPoint>): Map<Long, List<Register>> {
        val meterPointsByType = meterPointList.partition { MeterPointType.MPAN == it.type }
        val elecMeterPoints = meterPointsByType.first
        val gasMeterPoints = meterPointsByType.second

        val elecMeterIdSet = fetchMeterIdsSet(elecMeterPoints)
        val gasMeterIdSet = fetchMeterIdsSet(gasMeterPoints)

        return (
                registerService.getActiveRegistersByMeterIds(
                    elecMeterIdSet,
                    meterReadingConfig.firstReadingPeriodDaysElec
                ) + registerService.getActiveRegistersByMeterIds(
                    gasMeterIdSet,
                    meterReadingConfig.firstReadingMinPeriodDaysGas
                )
                )
            .groupBy { it.meterId }
    }

    private fun fetchMeterIdsSet(meterPointList: List<MeterPoint>): Set<Long> =
        meterPointList.map { meterPoint -> meterPoint.meters.map { it.id } }
            .flatten()
            .filterNotNull()
            .toSet()

    private suspend fun searchReads(
        meterPointList: List<MeterPoint>,
        enableAllHistoricalReadings: Boolean = false,
        includeCancelledAndDeletedAgreements: Boolean = false,
        productAccountId: Long? = null,
        frequency: energy.so.assets.meterReadings.v2.ReadFrequency? = null,
    ) = meterReadingsService.searchMeterReadings(
        meterReadingsSearchRequest {
            meterPointId.addAll(
                meterPointList.mapNotNull { it.id }
            )
            pagination = pagination {
                pageNumber = 1
                pageSize = PAGE_SIZE
            }
            if (enableAllHistoricalReadings) {
                status.add(MeterReadingStatusMessage.MeterReadingStatus.PENDING)
            }
            status.add(MeterReadingStatusMessage.MeterReadingStatus.ACCEPTED)
            this.enableAllHistoricalReadings = enableAllHistoricalReadings
            this.includeCancelledAndDeletedAgreements = includeCancelledAndDeletedAgreements
            productAccountId?.let { this.productAccountId = productAccountId }
            frequency?.let { this.readFrequency = frequency }
        }
    )

    private suspend fun checkConsent(meterPointList: List<MeterPoint>) =
        meterPointList.filter { it.meters.any { meter -> meter.isSmartMeter() } }.any {
            customerClient.getMeterPointConsentHistory(getCustomerConsentsRequest {
                meterPointId = it.id!!
            }).consentsList.any { it.setting == DAILY_CONSENT }
        }
}
