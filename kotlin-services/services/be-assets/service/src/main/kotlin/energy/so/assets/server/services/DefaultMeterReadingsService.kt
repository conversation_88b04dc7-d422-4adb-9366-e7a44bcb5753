package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.meterPointStructureRequest
import energy.so.ac.junifer.v1.smartreads.BlockingSmartReadsClient
import energy.so.assets.meterReadings.v2.BulkXRITMResponse
import energy.so.assets.meterReadings.v2.CreateMeterReadingRequest
import energy.so.assets.meterReadings.v2.MeterReadingStatusMessage
import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.MeterReadingsRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.extensions.toMeterReading
import energy.so.assets.server.extensions.toSubmitMeterReadingErrorResult
import energy.so.assets.server.models.*
import energy.so.assets.server.validators.MeterReadingError
import energy.so.commons.exceptions.grpc.GrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.search.pagination
import energy.so.commons.validations.validator.Validator
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import energy.so.customers.productaccounts.v2.ProductAccountsResponse
import energy.so.users.v2.FeatureName
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import energy.so.assets.meterReadings.v2.MeterReading as ProtoMeterReading

class DefaultMeterReadingsService(
    private val meterReadingsRepository: MeterReadingsRepository,
    private val meterPointsRepository: MeterPointsRepository,
    private val registerRepository: RegisterRepository,
    private val acAssetsClient: AssetsClient,
    private val meterReadingApiService: MeterReadingApiService,
    private val validator: Validator<MeterReadingError, MeterReadingsRequestDto>,
    private val meterReadingConfig: MeterReadingConfig,
    private val featureService: FeatureService,
    private val productAccountsClient: ProductAccountsClient,
    private val smartReadsClient: BlockingSmartReadsClient,
) : MeterReadingsService {

    private val logger = TraceableLogging.logger { }

    companion object {
        private const val FIRST_READ_TOO_FAR_FROM_SSD = "first_read_too_far_from_ssd"
    }

    override fun createMeterReading(request: CreateMeterReadingRequest): ProtoMeterReading {
        // TODO drop or implement when is confirmed that CreateMeterReading method is used
        return request.toMeterReading()
            .let { meterReadingsRepository.save(it) }
            .toResponse()
    }

    override suspend fun createMeterReading(
        meterReadingsRequestDto: MeterReadingsRequestDto,
        submitMeterReadingsResult: SubmitMeterReadingsResult,
    ) {
        logger.debug("::createMeterReading  Start to persist meter reading to be-asset database")
        val meterPointId = meterReadingsRequestDto.meterPointId
        validateMeterPointExistence(meterPointId)

        when (meterReadingsRequestDto) {
            is MeterReadingsWithoutTechnicalDetailsDto -> {
                val isGas = UnitType.Gas == meterReadingsRequestDto.registerReads[0].unitType
                val daysBeforeSupplyStartDate = meterReadingConfig.getDaysBeforeSupplyStartDate(isGas)

                val register = fetchRegisterFromMeterPoint(meterPointId, daysBeforeSupplyStartDate)

                val meterReadings: List<MeterReading> =
                    meterReadingsRequestDto.registerReads
                        .stream()
                        .map {
                            MeterReading(
                                meterPointId = meterPointId,
                                register = register,
                                meterId = register?.meterId,
                                readingDttm = LocalDateTime.now(),
                                sequenceType = meterReadingsRequestDto.sequenceType.toSiblingEnum(),
                                source = MeterReadingSource.Customer,
                                quality = MeterReadingQuality.MANUAL,
                                cumulative = it.reading.toBigDecimal(),
                                unit = getMeterReadingUnit(isGas),
                                errorDescription = submitMeterReadingsResult.getErrorDescriptionIfFailed()
                            )
                        }
                        .toList()
                meterReadingsRepository.saveAll(meterReadings)
            }

            is MeterReadingsWithTechnicalDetailsDto -> {
                val isGas = UnitType.Gas == meterReadingsRequestDto.readings[0].unitType
                val daysBeforeSupplyStartDate = meterReadingConfig.getDaysBeforeSupplyStartDate(isGas)

                val meterReadings: List<MeterReading> =
                    meterReadingsRequestDto.readings
                        .stream()
                        .map {
                            val registerIdentifier = it.registerIdentifier

                            val register = when (registerIdentifier.isNullOrBlank()) {
                                true -> fetchRegisterFromMeterPoint(meterPointId, daysBeforeSupplyStartDate)
                                false -> registerRepository.findByMeterPointIdAndIdentifiers(
                                    meterPointId,
                                    it.meterIdentifier,
                                    registerIdentifier,
                                )
                                    ?: throw EntityNotFoundException(
                                        "There is no register for params: meterPointId: $meterPointId," +
                                                " registerIdentifier: $registerIdentifier, meterIdentifier: ${it.meterIdentifier}"
                                    )
                            }

                            MeterReading(
                                meterPointId = meterPointId,
                                meterId = register?.meterId,
                                register = register,
                                sequenceType = it.sequenceType!!.toSiblingEnum(),
                                readingDttm = it.readingDateTime,
                                source = MeterReadingSource.valueOf(it.source),
                                quality = MeterReadingQuality.valueOf(it.quality),
                                cumulative = it.cumulative.toBigDecimal(),
                                unit = getMeterReadingUnit(isGas),
                                errorDescription = submitMeterReadingsResult.getErrorDescriptionIfFailed()
                            )
                        }
                        .toList()
                meterReadingsRepository.saveAll(meterReadings)
            }
        }
    }

    override suspend fun submitMeterReadingToJunifer(meterReadingsRequestDto: MeterReadingsRequestDto): SubmitMeterReadingsResult {
        val meterPointId = meterReadingsRequestDto.meterPointId
        val submissionTime = OffsetDateTime.now(ZoneOffset.UTC)

        /**
         * TMP_SO_21610_COMPUTE_SEQUENCE_TYPE_NPE_FIX has moved this logic to [DefaultMeterReadingSubmissionService] so
         * it can be re-used. Once this feature is removed, the computeSequenceType call can be removed entirely from here.
         */
        val meterReadingsToSubmit: MeterReadingsRequestDto = if (!featureService.isFeatureEnabled(
                FeatureName.TMP_SO_21610_COMPUTE_SEQUENCE_TYPE_NPE_FIX
            )
        ) {
            try {
                meterReadingsRequestDto.computeSequenceType()
            } catch (ex: Exception) {
                logger.error(ex) {
                    "[::submitMeterReadingToJunifer] Unexpected exception when computing sequence type for meterPointId: $meterPointId"
                }

                return mapGrpcException(ex, meterPointId, submissionTime)
            }
        } else {
            meterReadingsRequestDto
        }

        return validator.validate(meterReadingsToSubmit).fold(
            { errors ->
                logger.error { errors }

                errors.all.map { error ->
                    SubmitMeterReadingError(
                        error.validationErrorCode.lowercase(),
                        error.toString()
                    )
                }.onEach {
                    logger.error(
                        "[::submitMeterReadingToJunifer] service validations failed: error ${it.errorMessage}"
                    )
                }.let { SubmitMeterReadingsResult(meterPointId, submissionTime, false, it) }
            },
            {
                val juniferResponse = sendToJunifer(meterReadingsToSubmit, meterPointId, submissionTime)

                if (isFirstReadTooFarFromSSD(juniferResponse)) {
                    val requestWithNormalSequenceType = rebuildReadingUsingNormalSequenceType(meterReadingsToSubmit)

                    logger.debug(
                        "[::submitMeterReadingToJunifer] Junifer FIRST reading failed with $FIRST_READ_TOO_FAR_FROM_SSD. Trying to resubmit it as NORMAL"
                    )
                    sendToJunifer(
                        requestWithNormalSequenceType,
                        meterPointId,
                        submissionTime
                    ).also {
                        when (it.success) {
                            true -> logger.info(
                                "[::submitMeterReadingToJunifer] Junifer FIRST read successfully submitted as NORMAL"
                            )

                            false -> logger.info(
                                "[::submitMeterReadingToJunifer] Junifer FIRST read cannot be submitted as NORMAL for request $requestWithNormalSequenceType"
                            )
                        }
                    }
                } else {
                    juniferResponse
                }
            }
        )
    }

    private fun isFirstReadTooFarFromSSD(result: SubmitMeterReadingsResult): Boolean {
        return result.errors?.any { it.errorCode == FIRST_READ_TOO_FAR_FROM_SSD } ?: false
    }

    private fun rebuildReadingUsingNormalSequenceType(meterReadingsToSubmit: MeterReadingsRequestDto): MeterReadingsRequestDto {
        return when (meterReadingsToSubmit) {
            is MeterReadingsWithTechnicalDetailsDto -> meterReadingsToSubmit.copy(
                readings = meterReadingsToSubmit.readings.map {
                    it.copy(
                        sequenceType = SequenceType.Normal
                    )
                }
            )

            is MeterReadingsWithoutTechnicalDetailsDto -> meterReadingsToSubmit.copy(
                sequenceType = SequenceType.Normal
            )
        }
    }

    override suspend fun searchMeterReadings(searchRequest: MeterReadingsSearchRequest): List<MeterReading> =
        meterReadingApiService.search(searchRequest)

    override suspend fun getLastElectricityMeterReading(meterPointId: Long): MeterReading? {
        return meterReadingApiService.getLastElectricityMeterReading(meterPointId)
    }

    override suspend fun getFirstElectricityMeterReading(meterPointId: Long): MeterReading? {
        return meterReadingApiService.getFirstElectricityMeterReading(meterPointId)
    }

    override suspend fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<MeterReading> {
        return meterReadingApiService.getElectricityMeterReadingsByDate(meterPointId, startDate, endDate)
    }

    override fun bulkXRITM(mpxns: List<String>): BulkXRITMResponse {
        val meterPoints = meterPointsRepository.bulkFindByMPXNs(mpxns)
        var results = mutableListOf<energy.so.ac.junifer.v1.smartreads.XRITMError>()
        meterPoints.chunked(500) { meterPoints ->
            val grpcRequest = meterPoints.toBulkXRITMRequest()
            val response = smartReadsClient.bulkXRITM(grpcRequest)
            results.addAll(response.errorsList)
        }
        return energy.so.ac.junifer.v1.smartreads.bulkXRITMResponse { errors.addAll(results) }.toAssetsResponse()
    }

    private suspend fun sendToJunifer(
        request: MeterReadingsRequestDto,
        meterPointId: Long,
        submissionTime: OffsetDateTime,
    ): SubmitMeterReadingsResult =
        try {
            logger.debug(
                "[::sendToJunifer] Start sending meter reading to junifer for meterpoint id $meterPointId having request $request"
            )

            acAssetsClient.submitMeterReadings(request.toProtobuf()).let {
                when (it.validationErrorsCount) {
                    0 -> SubmitMeterReadingsResult(
                        meterPointId,
                        submissionTime,
                        true,
                        readingDates = request.getReadingDates()
                    )

                    else -> it.toSubmitMeterReadingErrorResult(meterPointId, submissionTime)
                }
            }
        } catch (exception: Exception) {
            logger.error(exception) { "::sendToJunifer Unexpected exception when submitting meter reading: $meterPointId" }
            mapGrpcException(exception, meterPointId, submissionTime)
        }

    private fun mapGrpcException(
        exception: Exception,
        meterPointId: Long,
        submissionTime: OffsetDateTime,
    ) = when (exception) {
        is GrpcException -> {
            SubmitMeterReadingsResult(
                meterPointId,
                submissionTime,
                false,
                exception.errorCode,
                exception.message
            )
        }

        else -> {
            SubmitMeterReadingsResult(
                meterPointId,
                submissionTime,
                false,
                "999", // Unknown exception error code
                exception.message
            )
        }
    }

    private fun getMeterReadingUnit(isGas: Boolean) =
        if (isGas) MeterReadingUnit.m3 else MeterReadingUnit.kWh

    private suspend fun getSequenceTypeForReadingWithTechnicalDetails(meterPointId: Long) =
        if (meterReadingApiService.existsForMeterPointIdInLastYear(meterPointId)) {
            MeterReadingSequenceType.NORMAL
        } else MeterReadingSequenceType.FIRST

    private fun validateMeterPointExistence(meterPointId: Long) {
        if (!meterPointsRepository.existsById(meterPointId)) {
            throw EntityNotFoundException("There is no meterPoint for id: $meterPointId")
        }
    }

    private fun fetchRegisterFromMeterPoint(meterPointId: Long, daysBeforeSupplyStartDate: Int): Register? {
        return registerRepository.findByMeterPointId(meterPointId, daysBeforeSupplyStartDate).let {
            if (it.size == 1) it[0] else null
        }
    }

//    private fun fetchMeterOrThrowError(meterIdentifier: String): Long {
//        return meterRepository.findByIdentifier(meterIdentifier)?.id
//            ?: throw EntityNotFoundException("There is no meter for identifier: $meterIdentifier")
//    }

    override suspend fun computeSequenceTypeForReadings(meterReadingsRequestDto: MeterReadingsRequestDto): MeterReadingsRequestDto {
        return meterReadingsRequestDto.computeSequenceType()
    }

    private suspend fun MeterReadingsRequestDto.computeSequenceType(): MeterReadingsRequestDto {
        if (this.billingAccountId != null) {
            val productAccounts = getCustomerProducts(this.billingAccountId!!)

            logger.debug("[::computeSequenceType] product account - agreements retrieved")
            productAccounts.productAccountsList.flatMap { it.agreementsList }.forEach {
                logger.debug(
                    "[::computeSequenceType] agreement-${it.id} meterpoints ${
                        it.meterPointsList.map { mp -> mp.id.toString() }.reduce { acc, s -> "$acc $s" }
                    }"
                )
            }
        }

        val supplyStartDate =
            acAssetsClient.getMeterPointStructure(
                meterPointStructureRequest {
                    id = meterPointId
                    effectiveDt = LocalDate.now().toTimestamp()
                }
            ).supplyStartDate.value.toLocalDate()

        return when (this) {
            is MeterReadingsWithTechnicalDetailsDto -> this.putSequenceType(supplyStartDate)
            is MeterReadingsWithoutTechnicalDetailsDto -> this.putSequenceType(supplyStartDate)
        }
    }

    private suspend fun getCustomerProducts(billingAccountId: Long): ProductAccountsResponse {
        return productAccountsClient.getProductAccountsByBillingAccountId(idRequest { id = billingAccountId })
    }

    private suspend fun MeterReadingsWithoutTechnicalDetailsDto.putSequenceType(
        supplyStartDate: LocalDate,
    ): MeterReadingsWithoutTechnicalDetailsDto {
        val sequenceType =
            if (getMeterpointReadings(meterPointId, supplyStartDate, this.readingDate).isNotEmpty()) {
                SequenceType.Normal
            } else SequenceType.First

        return this.copy(
            sequenceType = sequenceType,
            agreementFromDate = supplyStartDate
        )
    }

    private suspend fun MeterReadingsWithTechnicalDetailsDto.putSequenceType(
        supplyStartDate: LocalDate,
    ): MeterReadingsWithTechnicalDetailsDto {
        val readsWithSequenceTypeAndAgreement = this.readings.map { read ->
            val sequenceType =
                if (getMeterpointReadings(
                        meterPointId,
                        supplyStartDate,
                        read.readingDateTime.toLocalDate()
                    ).isNotEmpty()
                ) {
                    SequenceType.Normal
                } else SequenceType.First

            read.copy(
                sequenceType = sequenceType,
                agreementFromDate = supplyStartDate
            )
        }

        return this.copy(readings = readsWithSequenceTypeAndAgreement)
    }

    private suspend fun getMeterpointReadings(
        meterPointId: Long,
        agreementFromDate: LocalDate,
        readingDate: LocalDate,
    ): List<MeterReading> {
        val toDate = maxOf(readingDate, agreementFromDate)

        return searchMeterReadings(
            meterReadingsSearchRequest {
                this.meterPointId.add(meterPointId)
                status.add(MeterReadingStatusMessage.MeterReadingStatus.ACCEPTED)
                startDate = agreementFromDate.minusDays(1).toTimestamp()
                endDate = toDate.toTimestamp()
                pagination = pagination {
                    pageNumber = 1
                    pageSize = 1
                }
            }
        )
    }
}
