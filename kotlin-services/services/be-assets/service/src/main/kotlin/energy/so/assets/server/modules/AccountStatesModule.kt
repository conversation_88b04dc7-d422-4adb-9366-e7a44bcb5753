package energy.so.assets.server.modules

import energy.so.assets.server.config.Constants
import energy.so.assets.server.controllers.AssetsAccountStatesController
import energy.so.assets.server.services.accountState.OpeningReadingStateService
import energy.so.assets.server.services.accountState.ReadStateService
import energy.so.assets.server.services.accountState.SupplyStateService
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.customers.client.v2.changeoftenancy.ChangeOfTenancyClient
import org.koin.dsl.module

object AccountStatesModule {
    private val customersConfig = Constants.get<GrpcServiceConfig>("be-customers")

    val module = module {
        single { ChangeOfTenancyClient(config = customersConfig) }
        single { BillingAccountsClient(config = customersConfig) }

        single {
            AssetsAccountStatesController(
                openingReadingStateService = get(),
                supplyStateService = get(),
                readStateService = get()
            )
        }

        single {
            OpeningReadingStateService(
                productAccountClient = get(),
                meterPointService = get(),
                meterReadingsApiService = get(),
                meterReadingConfig = get()
            )
        }

        single {
            ReadStateService(
                productAccountClient = get(),
                meterPointService = get(),
                meterReadingsApiService = get(),
            )
        }

        single {
            SupplyStateService(
                productAccountClient = get(),
                meterPointService = get(),
                cotClient = get(),
                billingAccountsClient = get()
            )
        }
    }
}
