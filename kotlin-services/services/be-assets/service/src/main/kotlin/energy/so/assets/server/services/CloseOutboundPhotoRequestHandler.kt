package energy.so.assets.server.services

import energy.so.ac.junifer.v1.accounts.AccountsClient
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.assets.server.config.getNovaMeterPhotoHistoryModalUrl
import energy.so.assets.server.database.repositories.OutboundPhotoRequestRepository
import energy.so.assets.server.models.OutboundPhotoRequestStatus.CANCELLED
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.JUNIFER_NOTE_SUBJECT
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.communicationTemplate
import energy.so.commons.logging.TraceableLogging

private val logger = TraceableLogging.logger {}

class CloseOutboundPhotoRequestHandler(
    private val outboundPhotoRequestRepository: OutboundPhotoRequestRepository,
    private val juniferAccountsClient: AccountsClient,
) {
    suspend fun closeOutboundRequest(
        outboundRequestId: Long,
        juniferNoteSummary: String,
    ) {
        val cancelledOutboundRequest = outboundPhotoRequestRepository.updateStatus(outboundRequestId, CANCELLED)

        if (cancelledOutboundRequest == null) {
            logger.error("[closeOutboundRequest] no outboundRequest found with id $outboundRequestId to be cancelled")
            return
        }

        juniferAccountsClient.createAccountNote(
            createAccountNoteRequest {
                useJuniferId = false
                accountId = cancelledOutboundRequest.accountId.toString()
                subject = JUNIFER_NOTE_SUBJECT
                type = NoteType.Note
                summary = juniferNoteSummary
                content = "FD ${cancelledOutboundRequest.freshdeskTicketId} - " +
                        communicationTemplate.getNovaMeterPhotoHistoryModalUrl(cancelledOutboundRequest.accountNumber)
            }
        )
    }
}
