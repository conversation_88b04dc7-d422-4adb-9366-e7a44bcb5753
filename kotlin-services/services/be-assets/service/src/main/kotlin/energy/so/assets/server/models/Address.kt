package energy.so.assets.server.models

import energy.so.assets.properties.v2.address
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Address as JooqAddress

data class Address(
    val id: Long? = null,
    val address1: String? = null,
    val address2: String? = null,
    val address3: String? = null,
    val address4: String? = null,
    val address5: String? = null,
    val address6: String? = null,
    val address7: String? = null,
    val address8: String? = null,
    val address9: String? = null,
    val postcode: String? = null,
    val countryCode: String? = null,
    val type: String,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
) {
    companion object {
        fun fromJooq(pojo: JooqAddress) = Address(
            id = pojo.id,
            address1 = pojo.address1,
            address2 = pojo.address2,
            address3 = pojo.address3,
            address4 = pojo.address4,
            address5 = pojo.address5,
            address6 = pojo.address6,
            address7 = pojo.address7,
            address8 = pojo.address8,
            address9 = pojo.address9,
            postcode = pojo.postcode,
            countryCode = pojo.countryCode,
            type = pojo.type!!,
            deleted = pojo.deleted,
            createdAt = pojo.createdAt!!,
            updatedAt = pojo.updatedAt!!,
        )
    }
}

fun Address.toProtobuf() = let { from ->
    address {
        id = from.id!!
        address1 = from.address1.toNullableString()
        address2 = from.address2.toNullableString()
        address3 = from.address3.toNullableString()
        address4 = from.address4.toNullableString()
        address5 = from.address5.toNullableString()
        address6 = from.address6.toNullableString()
        address7 = from.address7.toNullableString()
        address8 = from.address8.toNullableString()
        address9 = from.address9.toNullableString()
        postcode = from.postcode.toNullableString()
        countryCode = from.countryCode.toNullableString()
        type = from.type
        deleted = from.deleted.toNullableTimestamp()
        createdAt = from.createdAt.toTimestamp()
        updatedAt = from.updatedAt.toTimestamp()
    }
}
