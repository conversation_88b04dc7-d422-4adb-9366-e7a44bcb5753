package energy.so.assets.server.database.repositories

import org.jooq.DSLContext

class JooqEstimatedUsageRepository(private val dslContext: DSLContext) : EstimatedUsageRepository {
//    override fun findByMeterPointId(meterPointId: Long): List<EstimatedUsage> = dslContext
//        .select(ESTIMATED_USAGE.asterisk())
//        .distinctOn(ESTIMATED_USAGE.RATE_NAME)
//        .from(ESTIMATED_USAGE)
//        .where(
//            ESTIMATED_USAGE.METER_POINT_ID.eq(meterPointId)
//                .and(ESTIMATED_USAGE.DELETED.isNull)
//                .and(ESTIMATED_USAGE.CANCELFL.eq(false))
//        )
//        .orderBy(
//            ESTIMATED_USAGE.RATE_NAME,
//            ESTIMATED_USAGE.CREATED_AT.desc()
//        )
//        .fetchInto(energy.so.commons.model.tables.pojos.EstimatedUsage::class.java)
//        .map { EstimatedUsage.fromJooq(it) }
}
