package energy.so.assets.server.services

import energy.so.users.client.v2.FeatureClient
import energy.so.users.v2.FeatureName
import energy.so.users.v2.featureNameRequest

class DefaultFeatureService(
    private val featureClient: FeatureClient,
) : FeatureService {
    override suspend fun meterReadPubsubFeatureEnabled(): Boolean =
        featureClient.getFeature(featureNameRequest { name = FeatureName.METER_READ_PUBSUB }).enabled

    override suspend fun isFeatureEnabled(featureName: FeatureName): Boolean =
        featureClient.getFeature(featureNameRequest { name = featureName }).enabled
}
