package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterMeterPointRelEntity

/**
 * A repository that handles entity synchronisation updates from the anti-corruption layer.
 * <AUTHOR>
 */
interface MeterMeterPointRelEntityRepository {

    fun deleteMeterMeterPointRelEntity(id: Long): Long

    fun patchMeterMeterPointRelEntity(entity: MeterMeterPointRelEntity): Long

    fun createMeterMeterPointRelEntity(entity: MeterMeterPointRelEntity): Long
}
