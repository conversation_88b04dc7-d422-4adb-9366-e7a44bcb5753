package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.MeterType
import energy.so.commons.model.tables.pojos.Meter
import energy.so.commons.model.tables.records.MeterRecord
import energy.so.commons.model.tables.references.METER
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqMeterEntityRepository(
    private val dslContext: DSLContext,
) : MeterEntityRepository {
    private val logger = TraceableLogging.logger {}

    override fun deleteMeterEntity(id: Long): Long {
        dslContext.update(METER)
            .set(METER.DELETED, LocalDateTime.now())
            .where(METER.ID.eq(id))
            .execute()
        return id
    }

    override fun patchMeterEntity(entity: MeterEntity): Long {
        logger.info("[patchMeterEntity] entity: $entity")
        val updateStep: UpdateSetStep<MeterRecord> = dslContext
            .update(METER)
            .set(METER.UPDATED_AT, LocalDateTime.now())

        if (entity.hasType()) updateStep.set(METER.TYPE, MeterType.valueOf(entity.type.value.uppercase()))
        if (entity.hasDescription()) updateStep.set(METER.DESCRIPTION, entity.description.getValueOrNull())
        if (entity.hasIdentifier()) updateStep.set(METER.IDENTIFIER, entity.identifier.value)
        if (entity.hasCreatedAt()) {
            updateStep.set(METER.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER.DELETED)
            } else {
                updateStep.set(METER.DELETED, DSL.coalesce(METER.DELETED, DSL.value(it.toLocalDateTime())))
            }
        }
        (updateStep as UpdateSetMoreStep).where(METER.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createMeterEntity(entity: MeterEntity): Long {
        return resolveSyncCreationFlow(
            METER.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterEntity,
            ::patchMeterEntity,
            fun(e: MeterEntity, newId: Long): MeterEntity =
                e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterEntity(entity: MeterEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER,
            Meter(
                id = entity.assetId.value,
                type = MeterType.valueOf(entity.type.value.uppercase()),
                description = entity.description.getValueOrNull(),
                identifier = entity.identifier.value,
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(Meter()) }.id!!
}
