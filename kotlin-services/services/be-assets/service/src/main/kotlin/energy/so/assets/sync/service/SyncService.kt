package energy.so.assets.sync.service

import energy.so.assets.sync.v2.AddressEntityRequest
import energy.so.assets.sync.v2.AssetEntityRequest
import energy.so.assets.sync.v2.EstimatedUsageEntityRequest
import energy.so.assets.sync.v2.MeterEntityRequest
import energy.so.assets.sync.v2.MeterMeterPointRelEntityRequest
import energy.so.assets.sync.v2.MeterPointElectricityEntityRequest
import energy.so.assets.sync.v2.MeterPointEntityRequest
import energy.so.assets.sync.v2.MeterPointGasEntityRequest
import energy.so.assets.sync.v2.MeterPointHistoryEntityRequest
import energy.so.assets.sync.v2.MeterReadingEntityRequest
import energy.so.assets.sync.v2.PropertyEntityRequest
import energy.so.assets.sync.v2.RegisterEntityRequest
import energy.so.commons.v2.sync.SyncResponse

/**
 * A service responsible for synchronising entity updates from the anti-corruption layer.
 * <AUTHOR>
 */
interface SyncService {

    /**
     * Patch, Create or Delete an Asset.
     *
     * @param request
     * @return
     */
    fun syncAssetEntity(request: AssetEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Register.
     *
     * @param request
     * @return
     */
    fun syncRegisterEntity(request: RegisterEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter.
     *
     * @param request
     * @return
     */
    fun syncMeterEntity(request: MeterEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Property.
     *
     * @param request
     * @return
     */
    fun syncPropertyEntity(request: PropertyEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete an Address.
     *
     * @param request
     * @return
     */
    fun syncAddressEntity(request: AddressEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Point.
     *
     * @param request
     * @return
     */
    fun syncMeterPointEntity(request: MeterPointEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Point History.
     *
     * @param request
     * @return
     */
    fun syncMeterPointHistoryEntity(request: MeterPointHistoryEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete an Estimated Usage.
     *
     * @param request
     * @return
     */
    fun syncEstimatedUsageEntity(request: EstimatedUsageEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Reading.
     *
     * @param request
     * @return
     */
    fun syncMeterReadEntity(request: MeterReadingEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Meter Point Relationship.
     *
     * @param request
     * @return
     */
    fun syncMeterMeterPointRelEntity(request: MeterMeterPointRelEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Point Electricity.
     *
     * @param request
     * @return
     */
    fun syncMeterPointElectricityEntity(request: MeterPointElectricityEntityRequest): SyncResponse

    /**
     * Patch, Create or Delete a Meter Point Gas.
     *
     * @param request
     * @return
     */
    fun syncMeterPointGasEntity(request: MeterPointGasEntityRequest): SyncResponse
}
