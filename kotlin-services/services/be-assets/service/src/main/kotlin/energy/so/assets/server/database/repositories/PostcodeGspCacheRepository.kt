package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.PostcodeGsp
import energy.so.commons.model.tables.pojos.PostcodeGspCache as JoopPostcodeGsp

interface PostcodeGspCacheRepository {

    /**
     *
     * @param postcode String
     * @return PostcodeGsp?
     */
    fun findByPostcode(postcode: String): PostcodeGsp?

    /**
     *
     * @param property PostcodeGspCache
     * @return PostcodeGspCache
     */
    fun save(property: JoopPostcodeGsp): JoopPostcodeGsp
}
