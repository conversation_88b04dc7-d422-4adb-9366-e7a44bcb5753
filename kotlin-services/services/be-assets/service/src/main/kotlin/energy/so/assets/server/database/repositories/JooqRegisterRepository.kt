package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Register
import energy.so.commons.model.tables.references.METER
import energy.so.commons.model.tables.references.METER_METER_POINT_REL
import energy.so.commons.model.tables.references.REGISTER
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Register as JooqRegister

class JooqRegisterRepository(private val dslContext: DSLContext) : RegisterRepository {

    override fun findByMeterPointIdAndIdentifiers(
        meterPointId: Long,
        meterIdentifier: String,
        registerIdentifier: String,
    ): Register? = dslContext
        .select(REGISTER.asterisk())
        .from(METER_METER_POINT_REL)
        .innerJoin(METER)
        .on(METER.ID.eq(METER_METER_POINT_REL.METER_ID))
        .innerJoin(REGISTER)
        .on(REGISTER.METER_ID.eq(METER.ID))
        .where(
            METER_METER_POINT_REL.METER_POINT_ID.eq(meterPointId),
            REGISTER.IDENTIFIER.eq(registerIdentifier),
            DSL.field("DATE({0})", LocalDate::class.java, REGISTER.FROM_DTTM)
                .lessOrEqual(LocalDate.now()),
            DSL.field("DATE({0})", LocalDate::class.java, REGISTER.TO_DTTM)
                .isNull.or(
                    DSL.field("DATE({0})", LocalDate::class.java, REGISTER.TO_DTTM)
                        .greaterOrEqual(LocalDate.now())
                ),
            METER.IDENTIFIER.eq(meterIdentifier),
            METER_METER_POINT_REL.DELETED.isNull,
            METER.DELETED.isNull,
            REGISTER.DELETED.isNull,
            METER_METER_POINT_REL.FROM_DT.lessOrEqual(LocalDateTime.now()),
            METER_METER_POINT_REL.TO_DT.isNull.or(METER_METER_POINT_REL.TO_DT.greaterOrEqual(LocalDateTime.now())),
        )
        .orderBy(REGISTER.FROM_DTTM.desc())
        .limit(1)
        .fetchOneInto(JooqRegister::class.java)
        ?.let { Register.fromJooq(it) }

    override fun findByMeterPointId(
        meterPointId: Long,
        daysBeforeSupplyStartDate: Int,
    ): List<Register> = dslContext
        .select(REGISTER.asterisk())
        .from(METER_METER_POINT_REL)
        .innerJoin(METER)
        .on(METER.ID.eq(METER_METER_POINT_REL.METER_ID))
        .innerJoin(REGISTER)
        .on(REGISTER.METER_ID.eq(METER.ID))
        .where(
            METER_METER_POINT_REL.METER_POINT_ID.eq(meterPointId),
            METER.DELETED.isNull,
            getActiveConditions(daysBeforeSupplyStartDate)
        )
        .fetchInto(JooqRegister::class.java)
        .map { Register.fromJooq(it) }

    override fun findByIds(id: Set<Long>) = dslContext
        .selectFrom(REGISTER)
        .where(REGISTER.ID.`in`(id), REGISTER.DELETED.isNull)
        .fetchInto(JooqRegister::class.java)
        .map { Register.fromJooq(it) }

    override fun getById(id: Long): Register? = dslContext
        .selectFrom(REGISTER)
        .where(REGISTER.ID.eq(id), REGISTER.DELETED.isNull)
        .fetchOneInto(JooqRegister::class.java)
        ?.let { Register.fromJooq(it) }

    override fun findByMeterIds(meterIds: Set<Long>): List<Register> =
        dslContext
            .selectFrom(REGISTER)
            .where(
                REGISTER.METER_ID.`in`(meterIds),
                REGISTER.DELETED.isNull,
                DSL.field("DATE({0})", LocalDate::class.java, REGISTER.FROM_DTTM)
                    .lessOrEqual(LocalDate.now()),
                REGISTER.TO_DTTM.isNull.or(
                    DSL.field("DATE({0})", LocalDate::class.java, REGISTER.TO_DTTM)
                        .greaterOrEqual(LocalDate.now())
                ),
            )
            .fetchInto(JooqRegister::class.java)
            .map { Register.fromJooq(it) }

    override fun findActiveByMeterIds(meterIds: Set<Long>, daysBeforeSupplyStartDate: Int): List<Register> =
        dslContext
            .selectDistinct(REGISTER)
            .from(METER_METER_POINT_REL)
            .innerJoin(REGISTER).on(METER_METER_POINT_REL.REGISTER_ID.eq(REGISTER.ID))
            .where(
                METER_METER_POINT_REL.METER_ID.`in`(meterIds),
                getActiveConditions(daysBeforeSupplyStartDate)
            )
            .fetchInto(JooqRegister::class.java)
            .map { Register.fromJooq(it) }

    private fun getActiveConditions(daysBeforeSupplyStartDate: Int) =
        DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
            .lessOrEqual(LocalDate.now().plusDays(daysBeforeSupplyStartDate.toLong()))
            .and(
                METER_METER_POINT_REL.TO_DT.isNull.or(
                    DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                        .greaterOrEqual(LocalDate.now()),
                )
            ).and(
                METER_METER_POINT_REL.DELETED.isNull
            ).and(
                REGISTER.DELETED.isNull
            )
}
