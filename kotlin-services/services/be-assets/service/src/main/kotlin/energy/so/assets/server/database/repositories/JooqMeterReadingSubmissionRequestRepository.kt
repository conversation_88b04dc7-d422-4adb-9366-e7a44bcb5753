package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterReadingStatus
import energy.so.assets.server.models.MeterReadingSubmissionFilter
import energy.so.assets.server.models.MeterReadingSubmissionIdFilter
import energy.so.assets.server.models.MeterReadingSubmissionRequest
import energy.so.assets.server.models.MeterReadingSubmissionRequestStatus
import energy.so.assets.server.models.toJooq
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.MeterReadingSubmissionRequestStatu
import energy.so.commons.model.tables.references.METER_READING_SUBMISSION_REQUEST
import java.time.LocalDateTime
import org.jooq.DSLContext
import org.jooq.impl.DSL
import energy.so.commons.model.tables.pojos.MeterReadingSubmissionRequest as JooqMeterReadingSubmissionRequest

private val logger = TraceableLogging.logger {}

class JooqMeterReadingSubmissionRequestRepository(
    private val dslContext: DSLContext,
) : MeterReadingSubmissionRequestRepository {

    override fun save(meterReadingSubmissionRequest: MeterReadingSubmissionRequest): MeterReadingSubmissionRequest =
        meterReadingSubmissionRequest.toJooq()
            .let {
                dslContext.newRecord(METER_READING_SUBMISSION_REQUEST, it)
                    .apply { this.save() }
                    .run { into(JooqMeterReadingSubmissionRequest()) }
            }
            .let { MeterReadingSubmissionRequest.fromJooq(it) }

    override fun search(meterReadingSubmissionFilter: MeterReadingSubmissionFilter): List<MeterReadingSubmissionRequest> =
        dslContext.selectFrom(METER_READING_SUBMISSION_REQUEST)
            .where(getSearchConditions(meterReadingSubmissionFilter))
            .fetchInto(JooqMeterReadingSubmissionRequest::class.java)
            .map { MeterReadingSubmissionRequest.fromJooq(it) }

    override fun searchById(meterReadingSubmissionIdFilter: MeterReadingSubmissionIdFilter): MeterReadingSubmissionRequest? =
        dslContext.selectFrom(METER_READING_SUBMISSION_REQUEST)
            .where(getSearchByIdConditions(meterReadingSubmissionIdFilter))
            .fetchOneInto(JooqMeterReadingSubmissionRequest::class.java)
            ?.let { MeterReadingSubmissionRequest.fromJooq(it) }

    override fun updateStatus(id: Long, newStatus: MeterReadingSubmissionRequestStatus) {
        val updatedRequests = dslContext.update(METER_READING_SUBMISSION_REQUEST)
            .set(METER_READING_SUBMISSION_REQUEST.STATUS, newStatus.toSiblingEnum<MeterReadingSubmissionRequestStatu>())
            .where(METER_READING_SUBMISSION_REQUEST.ID.eq(id))
            .execute()

        if (updatedRequests == 0) {
            logger.warn(
                "[::updateStatus] no METER_READING_SUBMISSION_REQUEST record found with id $id to update."
            )
        }
    }

    override fun updateStatusIfNotAlreadySubmitted(id: Long, newStatus: MeterReadingSubmissionRequestStatus) {
        val updatedRequests = dslContext.update(METER_READING_SUBMISSION_REQUEST)
            .set(METER_READING_SUBMISSION_REQUEST.STATUS, newStatus.toSiblingEnum<MeterReadingSubmissionRequestStatu>())
            .where(METER_READING_SUBMISSION_REQUEST.ID.eq(id))
            .and(METER_READING_SUBMISSION_REQUEST.STATUS.notEqual(MeterReadingSubmissionRequestStatu.SUBMITTED))
            .execute()

        if (updatedRequests == 0) {
            logger.warn(
                "[::updateStatus] no METER_READING_SUBMISSION_REQUEST record found with id $id to update."
            )
        }
    }

    override fun resolveSubmission(id: Long) {
        val updatedRequests = dslContext.update(METER_READING_SUBMISSION_REQUEST)
            .set(METER_READING_SUBMISSION_REQUEST.STATUS, MeterReadingSubmissionRequestStatu.SUBMITTED)
            .set(METER_READING_SUBMISSION_REQUEST.DELETED, LocalDateTime.now())
            .where(METER_READING_SUBMISSION_REQUEST.ID.eq(id))
            .execute()

        if (updatedRequests == 0) {
            logger.warn(
                "[::updateErroredStatus] no record found with id $id to update."
            )
        }
    }

    override fun resolveMeterPointContactedSubmission(meterPointId: List<Long>) {
        val updatedRequests = dslContext.update(METER_READING_SUBMISSION_REQUEST)
            .set(METER_READING_SUBMISSION_REQUEST.STATUS, MeterReadingSubmissionRequestStatu.MANUAL_SUBMISSION)
            .set(METER_READING_SUBMISSION_REQUEST.DELETED, LocalDateTime.now())
            .where(METER_READING_SUBMISSION_REQUEST.METERPOINT_ID.`in`(meterPointId))
            .and(METER_READING_SUBMISSION_REQUEST.STATUS.eq(MeterReadingSubmissionRequestStatu.CONTACTED))
            .execute()

        if (updatedRequests == 0) {
            logger.warn(
                "[::updateErroredStatus] no record found for meterPoint $meterPointId to update."
            )
        }
    }

    override fun getErroredSubmissions(): List<MeterReadingSubmissionRequest> {
        val subquery = dslContext.select(
            METER_READING_SUBMISSION_REQUEST.METERPOINT_ID,
            DSL.max(METER_READING_SUBMISSION_REQUEST.CREATED_AT).`as`("maxCreatedAt")
        )
            .from(METER_READING_SUBMISSION_REQUEST)
            .groupBy(METER_READING_SUBMISSION_REQUEST.METERPOINT_ID)

        return dslContext.select(METER_READING_SUBMISSION_REQUEST.asterisk())
            .from(METER_READING_SUBMISSION_REQUEST)
            .join(subquery)
            .on(
                METER_READING_SUBMISSION_REQUEST.METERPOINT_ID.eq(
                    subquery.field(METER_READING_SUBMISSION_REQUEST.METERPOINT_ID)
                )
            )
            .and(
                METER_READING_SUBMISSION_REQUEST.CREATED_AT.eq(
                    subquery.field(
                        "maxCreatedAt",
                        LocalDateTime::class.java
                    )
                )
            )
            .and(METER_READING_SUBMISSION_REQUEST.STATUS.eq(MeterReadingSubmissionRequestStatu.ERRORED))
            .fetchInto(JooqMeterReadingSubmissionRequest::class.java)
            .map { MeterReadingSubmissionRequest.fromJooq(it) }
    }

    private fun getSearchConditions(searchRequest: MeterReadingSubmissionFilter) = listOf(
        if (searchRequest.meterPointId != null) {
            METER_READING_SUBMISSION_REQUEST.METERPOINT_ID.eq(searchRequest.meterPointId)
        } else DSL.trueCondition(),
        when (searchRequest.deleted) {
            true -> METER_READING_SUBMISSION_REQUEST.DELETED.isNotNull
            false -> METER_READING_SUBMISSION_REQUEST.DELETED.isNull
            else -> DSL.trueCondition()
        },
        if (searchRequest.statusesList.isNotEmpty()) {
            METER_READING_SUBMISSION_REQUEST.STATUS.`in`(
                searchRequest.statusesList.map { it.toSiblingEnum<MeterReadingSubmissionRequestStatu>() }
            )
        } else DSL.trueCondition(),
        if (searchRequest.createdAt != null) {
            METER_READING_SUBMISSION_REQUEST.CREATED_AT.greaterOrEqual(searchRequest.createdAt)
        } else DSL.trueCondition(),
        if (searchRequest.juniferStatus.isNotEmpty()) {
            METER_READING_SUBMISSION_REQUEST.JUNIFER_STATUS
                .`in`(searchRequest.juniferStatus.map { it.toSiblingEnum<MeterReadingStatus>() })
        } else {
            DSL.trueCondition()
        }
    )

    private fun getSearchByIdConditions(searchRequest: MeterReadingSubmissionIdFilter) =
        getSearchConditions(searchRequest) + METER_READING_SUBMISSION_REQUEST.ID.eq(searchRequest.id)
}
