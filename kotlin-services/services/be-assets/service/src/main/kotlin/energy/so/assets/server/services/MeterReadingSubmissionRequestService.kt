package energy.so.assets.server.services

import energy.so.assets.server.models.MeterReadingStatusResponse
import energy.so.assets.server.models.MeterReadingSubmissionIdFilter
import energy.so.assets.server.models.MeterReadingSubmissionRequest
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.SubmitMeterReadingsResult

interface MeterReadingSubmissionRequestService {
    suspend fun processMeterReadingsAsyncSubmissions(reading: MeterReadingsRequestDto)

    suspend fun processMoveInMeterReadingsAsyncSubmissions(
        accountNumber: String,
        email: String,
        reading: MeterReadingsRequestDto,
    )

    fun queuePersistedSubmissions()

    /**
     * Returns meter reading status unless is submitted
     * @param meterPointId
     * @return List<MeterReadingStatusResponse>
     */
    suspend fun getMeterReadingsPendingStatus(meterPointId: Long): List<MeterReadingStatusResponse>

    /**
     * Returns last meter reading status
     * @param meterPointId
     * @return MeterReadingStatusResponse
     */
    suspend fun getLastMeterReadingStatus(meterPointId: Long): MeterReadingStatusResponse?

    suspend fun resolveReadingSubmissionError(submissionId: Long)

    suspend fun handleJuniferSubmissionResponse(
        submissionRequest: MeterReadingSubmissionRequest,
        response: SubmitMeterReadingsResult,
    )

    suspend fun findMeterReadingSubmissionsByIdFilter(
        filter: MeterReadingSubmissionIdFilter,
    ): MeterReadingSubmissionRequest

    /**
     * Returns errored meter reading submissions only if the last reading for each meter point is errored.
     * @return List<MeterReadingStatusResponse>
     */
    suspend fun getErroredSubmissions(): List<MeterReadingStatusResponse>

    /**
     * Updates the CONTACTED reads status to MANUAL_SUBMISSION status, whenever a new reading submit appears
     * from other sources
     */
    suspend fun updateContactedReadsMap()

    /**
     * Updates the junifer status of a meter reading submission taken from the related Junifer meter reading
     * at fixed intervals.
     */
    suspend fun updateMeterReadingJuniferStatus()
}
