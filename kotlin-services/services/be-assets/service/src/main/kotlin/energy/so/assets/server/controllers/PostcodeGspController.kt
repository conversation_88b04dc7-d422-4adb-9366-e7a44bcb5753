package energy.so.assets.server.controllers

import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeRequest
import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeResponse
import energy.so.assets.postcodeGsp.v2.PostcodeGspServiceGrpcKt
import energy.so.assets.server.services.PostcodeGspService
import io.opentelemetry.instrumentation.annotations.WithSpan

class PostcodeGspController(
    private val postcodeGspService: PostcodeGspService,
) : PostcodeGspServiceGrpcKt.PostcodeGspServiceCoroutineImplBase() {
    @WithSpan
    override suspend fun getGspIdGroupsByPostcode(
        request: GspGroupIdsByPostcodeRequest,
    ): GspGroupIdsByPostcodeResponse {
        return postcodeGspService.getGspIdGroupsByPostcode(request)
    }
}
