package energy.so.assets.server.extensions

import energy.so.assets.meterReadings.v2.CreateMeterReadingRequest
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingQuality
import energy.so.assets.server.models.MeterReadingSequenceType
import energy.so.assets.server.models.MeterReadingSource
import energy.so.assets.server.models.MeterReadingStatus
import energy.so.assets.server.models.MeterReadingUnit
import energy.so.assets.server.models.MeterReadingWorkflowStatus
import java.time.LocalDateTime

fun CreateMeterReadingRequest.toMeterReading(): MeterReading = let { request ->
    MeterReading(
        // TODO this is a dummy implementation. drop or implement when is confirmed that CreateMeterReading method is used
        meterPointId = 1L,
        meterId = 1L,
        register = null,
        readingDttm = LocalDateTime.now(),
        fromDttm = LocalDateTime.now(),
        status = MeterReadingStatus.ACCEPTED,
        sequenceType = MeterReadingSequenceType.FIRST,
        source = MeterReadingSource.Customer,
        quality = MeterReadingQuality.MANUAL,
        cumulative = cumulative.toBigDecimal(),
        consumption = cumulative.toBigDecimal(),
        unit = MeterReadingUnit.kWh,
        workflowStatus = MeterReadingWorkflowStatus.ACCEPTED,
        rateName = "rateName",
        pendingRegisterReadingId = "1",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        deleted = LocalDateTime.now(),
        submissionProcessed = true
    )
}
