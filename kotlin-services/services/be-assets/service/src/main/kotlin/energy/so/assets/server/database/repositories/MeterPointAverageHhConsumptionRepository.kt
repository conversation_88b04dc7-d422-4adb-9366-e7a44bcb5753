package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointAverageHhConsumption

interface MeterPointAverageHhConsumptionRepository {
    /**
     * Retrieves the average half-hourly consumption for a given meter point.
     *
     * @param meterPointId The ID of the meter point.
     * @return Average half-hourly consumption for the meter point, or null if not found.
     */
    fun findByMeterPointId(meterPointId: Long): MeterPointAverageHhConsumption?
}