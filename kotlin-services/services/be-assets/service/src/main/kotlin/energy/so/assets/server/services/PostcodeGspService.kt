package energy.so.assets.server.services

import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeRequest
import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeResponse

interface PostcodeGspService {

    /**
     * Returns gsp groupIds associated with specified postcode
     * @param request GspGroupIdsByPostcodeRequest
     * @return GspGroupIdsByPostcodeResponse
     */
    suspend fun getGspIdGroupsByPostcode(request: GspGroupIdsByPostcodeRequest): GspGroupIdsByPostcodeResponse
}
