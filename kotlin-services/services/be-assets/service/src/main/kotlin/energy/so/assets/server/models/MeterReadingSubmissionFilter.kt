package energy.so.assets.server.models

import java.time.LocalDateTime

open class MeterReadingSubmissionFilter(
    open val meterPointId: Long? = null,
    open val deleted: Boolean? = false,
    open val statusesList: List<MeterReadingSubmissionRequestStatus> = emptyList(),
    open val createdAt: LocalDateTime? = null,
    open val juniferStatus: List<MeterReadingStatus> = emptyList(),
)

class MeterReadingSubmissionIdFilter(
    override val meterPointId: Long? = null,
    override val deleted: Boolean? = null,
    override val statusesList: List<MeterReadingSubmissionRequestStatus> = emptyList(),
    override val createdAt: LocalDateTime? = null,
    override val juniferStatus: List<MeterReadingStatus> = emptyList(),
    val id: Long,
) : MeterReadingSubmissionFilter(meterPointId, deleted, statusesList, createdAt, juniferStatus)
