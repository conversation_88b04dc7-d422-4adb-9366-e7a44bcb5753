package energy.so.assets.server.modules

import energy.so.assets.server.config.Constants
import energy.so.assets.server.consumers.AutomaticCloseOutboundRequestsConsumer
import energy.so.assets.server.consumers.OutboundPhotoRequestConsumer
import energy.so.assets.server.database.repositories.JooqOutboundPhotoRequestRepository
import energy.so.assets.server.database.repositories.OutboundPhotoRequestRepository
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService
import energy.so.assets.server.services.OutboundPhotoRequestService
import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.queues.publishers.PubsubMessagePublisher
import energy.so.email.client.v1.config.EmailClientConfig
import energy.so.email.client.v1.impl.DefaultEmailClient
import org.koin.core.qualifier.StringQualifier
import org.koin.dsl.module

object OutboundPhotoRequestModule {
    private val outboundPhotoRequestQualifier = StringQualifier("outbound-photo-request")
    private val pubsubConfig = PubSubConfiguration.fromApplicationConf()
    private val emailClientConfig = Constants.get<EmailClientConfig>("emailClientConfig")

    val module = module {
        single<OutboundPhotoRequestRepository> {
            JooqOutboundPhotoRequestRepository(
                dslContext = get()
            )
        }

        single<OutboundPhotoRequestService> {
            DefaultOutboundPhotoRequestService(
                outboundPhotoRequestRepository = get(),
                meterReadingPhotoRepository = get(),
                processOutboundPhotoRequestPublisher = get(qualifier = outboundPhotoRequestQualifier),
                customersClient = get(),
                billingAccountsClient = get(),
                communicationClient = get(),
                freshDeskClient = get(),
                juniferAccountsClient = get(),
                closeOutboundPhotoRequestHandler = get(),
                featureService = get(),
                emailClient = get(),
            )
        }

        single<MessagePublisher<String>>(createdAtStart = true, qualifier = outboundPhotoRequestQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-outbound-photo-requests")!!
            )
        }

        single {
            OutboundPhotoRequestConsumer(
                outboundPhotoRequestService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-outbound-photo-requests-subs")!!
            )
        }

        single {
            AutomaticCloseOutboundRequestsConsumer(
                outboundPhotoRequestService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-automate-close-outbound-req-subs")!!
            )
        }

        single {
            DefaultEmailClient(
                EmailClientConfig(
                    bootstrapServer = emailClientConfig.bootstrapServer,
                    schemaRegistryUrl = emailClientConfig.schemaRegistryUrl,
                    usernamePassword = emailClientConfig.usernamePassword,
                    jaasConfig = emailClientConfig.jaasConfig,
                    dotdigitalTopic = emailClientConfig.dotdigitalTopic
                )
            )
        }
    }
}
