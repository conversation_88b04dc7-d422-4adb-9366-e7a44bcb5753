package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Address
import energy.so.commons.model.tables.references.ADDRESS
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.Address as JooqAddress

class JooqAddressRepository(private val dslContext: DSLContext) : AddressRepository {

    override fun findById(id: Long): Address? =
        dslContext.selectFrom(ADDRESS)
            .where(ADDRESS.ID.eq(id))
            .fetchOneInto(JooqAddress::class.java)
            ?.let { Address.fromJooq(it) }

    override fun findAllByPostcode(postcode: String): List<Address> =
        dslContext.selectFrom(ADDRESS)
            .where(ADDRESS.POSTCODE.eq(postcode), ADDRESS.DELETED.isNull)
            .fetchInto(JooqAddress::class.java)
            .map { Address.fromJooq(it) }
}
