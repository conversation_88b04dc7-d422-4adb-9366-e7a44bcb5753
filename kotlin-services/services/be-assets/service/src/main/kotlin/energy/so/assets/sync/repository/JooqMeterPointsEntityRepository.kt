package energy.so.assets.sync.repository

import energy.so.assets.sync.service.getEnumOrNullWhenEmpty
import energy.so.assets.sync.service.searchEnum
import energy.so.assets.sync.v2.MeterPointEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.DccServiceStatu
import energy.so.commons.model.enums.MeterPointMeasurementType
import energy.so.commons.model.enums.MeterPointType
import energy.so.commons.model.enums.OperationType
import energy.so.commons.model.enums.UkProfileClass
import energy.so.commons.model.tables.records.MeterPointRecord
import energy.so.commons.model.tables.references.METER_POINT
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.MeterPoint as JooqMeterPoint

class JooqMeterPointsEntityRepository(
    private val dslContext: DSLContext,
) : MeterPointsEntityRepository {

    val YEAR_9999: LocalDate = LocalDate.of(9999, 1, 1)

    override fun deleteMeterPointEntity(id: Long): Long {
        dslContext.update(METER_POINT)
            .set(METER_POINT.DELETED, LocalDateTime.now())
            .where(METER_POINT.ID.eq(id))
            .execute()
        return id
    }

    override fun patchMeterPointEntity(entity: MeterPointEntity): Long {
        val updateStep: UpdateSetStep<MeterPointRecord> = dslContext
            .update(METER_POINT)
            .set(METER_POINT.UPDATED_AT, LocalDateTime.now())

        if (entity.hasServiceType()) updateStep.set(METER_POINT.SERVICE_TYPE, entity.serviceType.getValueOrNull())
        if (entity.hasType()) updateStep.set(METER_POINT.TYPE, searchEnum<MeterPointType>(entity.type.value))
        if (entity.hasOperationType()) {
            updateStep.set(
                METER_POINT.OPERATION_TYPE,
                getEnumOrNullWhenEmpty<OperationType>(entity.operationType.getValueOrNull())
            )
        }
        if (entity.hasIdentifier()) updateStep.set(METER_POINT.IDENTIFIER, entity.identifier.getValueOrNull())
        if (entity.hasUkGspGroup()) updateStep.set(METER_POINT.UK_GSP_GROUP, entity.ukGspGroup.getValueOrNull())
        updateStep.set(METER_POINT.PROPERTY_ID, entity.propertyId.getValueOrNull())
        if (entity.hasChangeOfTenancyFl()) {
            updateStep.set(
                METER_POINT.CHANGE_OF_TENANCY_FL,
                entity.changeOfTenancyFl.getValueOrNull()
            )
        }
        if (entity.hasReadingFrequencyCode()) {
            updateStep.set(
                METER_POINT.READING_FREQUENCY_CODE,
                entity.readingFrequencyCode.getValueOrNull()
            )
        }
        if (entity.hasUkProfileClass()) {
            updateStep.set(
                METER_POINT.UK_PROFILE_CLASS,
                getEnumOrNullWhenEmpty<UkProfileClass>(entity.ukProfileClass.getValueOrNull())
            )
        }
        if (entity.hasSupplyStartDate() && entity.supplyStartDate.hasValue()) {
            updateStep.set(
                METER_POINT.SUPPLY_START_DATE,
                entity.supplyStartDate.getValueOrNull()?.toLocalDate()
            )
        }

        if (entity.hasSupplyStartDate() && entity.supplyStartDate.hasNull() && entity.existsSupplyStartDate.value) {
            updateStep.set(
                METER_POINT.SUPPLY_START_DATE,
                YEAR_9999
            )
        }

        if (entity.hasSupplyStartDate() && entity.supplyStartDate.hasNull() && !entity.existsSupplyStartDate.value) {
            updateStep.setNull(METER_POINT.SUPPLY_START_DATE)
        }

        if (entity.hasDccServiceStatus()) {
            updateStep.set(
                METER_POINT.DCC_SERVICE_STATUS,
                getEnumOrNullWhenEmpty<DccServiceStatu>(entity.dccServiceStatus.getValueOrNull())
            )
        }
        if (entity.hasMeasurementType()) {
            updateStep.set(
                METER_POINT.MEASUREMENT_TYPE,
                getEnumOrNullWhenEmpty<MeterPointMeasurementType>(entity.measurementType.getValueOrNull())
            )
        }
        if (entity.hasFromDt()) {
            updateStep.set(
                METER_POINT.FROM_DT,
                entity.fromDt.getValueOrNull()?.toLocalDate()
            )
        }
        if (entity.hasToDt()) {
            updateStep.set(
                METER_POINT.TO_DT,
                entity.toDt.getValueOrNull()?.toLocalDate()
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(METER_POINT.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_POINT.DELETED)
            } else {
                updateStep.set(METER_POINT.DELETED, DSL.coalesce(METER_POINT.DELETED, DSL.value(it.toLocalDateTime())))
            }
        }
        (updateStep as UpdateSetMoreStep).where(METER_POINT.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createMeterPointEntity(entity: MeterPointEntity): Long {
        return resolveSyncCreationFlow(
            METER_POINT.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterPointEntity,
            ::patchMeterPointEntity,
            fun(e: MeterPointEntity, newId: Long): MeterPointEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterPointEntity(entity: MeterPointEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER_POINT,
            JooqMeterPoint(
                propertyId = entity.propertyId.getValueOrNull(),
                serviceType = entity.serviceType.getValueOrNull(),
                identifier = entity.identifier.getValueOrNull(),
                type = getEnumOrNullWhenEmpty<MeterPointType>(entity.type.getValueOrNull()),
                operationType = getEnumOrNullWhenEmpty<OperationType>(entity.operationType.getValueOrNull()),
                ukGspGroup = entity.ukGspGroup.getValueOrNull(),
                changeOfTenancyFl = entity.changeOfTenancyFl.getValueOrNull(),
                readingFrequencyCode = entity.readingFrequencyCode.getValueOrNull(), // todo(SO-15156) not sent by processor
                ukProfileClass = getEnumOrNullWhenEmpty<UkProfileClass>(entity.ukProfileClass.getValueOrNull()),
                supplyStartDate = if (entity.hasSupplyStartDate() && entity.supplyStartDate.hasValue()) {
                    entity.supplyStartDate.getValueOrNull()?.toLocalDate()
                } else if (entity.hasSupplyStartDate() && entity.supplyStartDate.hasNull() && entity.existsSupplyStartDate.value) {
                    YEAR_9999
                } else {
                    null
                },
                dccServiceStatus = getEnumOrNullWhenEmpty<DccServiceStatu>(entity.dccServiceStatus.getValueOrNull()),
                measurementType = getEnumOrNullWhenEmpty<MeterPointMeasurementType>(
                    entity.measurementType.getValueOrNull()
                ),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime(),
                fromDt = entity.fromDt.getValueOrNull()?.toLocalDate(),
                toDt = entity.toDt.getValueOrNull()?.toLocalDate(),
            )
        )
            .apply { save() }
            .run { into(JooqMeterPoint()) }.id!!
}
