package energy.so.assets.server.services

import energy.so.assets.server.database.repositories.RegisterRepository

class DefaultRegisterService(
    private val registerRepository: RegisterRepository,
) : RegisterService {

    override fun getRegistersByMeterIds(meterIds: Set<Long>) =
        registerRepository.findByMeterIds(meterIds)

    override fun getActiveRegistersByMeterIds(meterIds: Set<Long>, daysBeforeSupplyStartDate: Int) =
        registerRepository.findActiveByMeterIds(meterIds, daysBeforeSupplyStartDate)
}
