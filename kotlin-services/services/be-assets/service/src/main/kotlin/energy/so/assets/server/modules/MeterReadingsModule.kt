package energy.so.assets.server.modules

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.smartreads.BlockingSmartReadsClient
import energy.so.assets.server.config.AsyncMeterReadingSubmissionRequestConfig
import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.config.RedisConfig
import energy.so.assets.server.consumers.MeterReadSubmissionRequestConsumer
import energy.so.assets.server.consumers.MeterReadingStatusUpdateConsumer
import energy.so.assets.server.consumers.MeterReadingSubmissionConsumer
import energy.so.assets.server.consumers.PersistedMeterReadingSubmissionRequestConsumer
import energy.so.assets.server.consumers.UpdateMeterReadSubmissionStatusConsumer
import energy.so.assets.server.controllers.MeterReadingsController
import energy.so.assets.server.database.listeners.MeterReadingsDatabaseListener
import energy.so.assets.server.database.repositories.JooqMeterPointElectricityRepository
import energy.so.assets.server.database.repositories.JooqMeterPointGasRepository
import energy.so.assets.server.database.repositories.JooqMeterPointHistoryRepository
import energy.so.assets.server.database.repositories.JooqMeterPointsRepository
import energy.so.assets.server.database.repositories.JooqMeterReadingSubmissionRequestRepository
import energy.so.assets.server.database.repositories.JooqMeterReadingsRepository
import energy.so.assets.server.database.repositories.JooqMeterRepository
import energy.so.assets.server.database.repositories.JooqPropertyRepository
import energy.so.assets.server.database.repositories.JooqRegisterRepository
import energy.so.assets.server.database.repositories.MeterPointElectricityRepository
import energy.so.assets.server.database.repositories.MeterPointGasRepository
import energy.so.assets.server.database.repositories.MeterPointHistoryRepository
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.MeterReadingSubmissionRequestRepository
import energy.so.assets.server.database.repositories.MeterReadingsRepository
import energy.so.assets.server.database.repositories.MeterRepository
import energy.so.assets.server.database.repositories.PropertyRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.publishers.redis.RedisEventPublisher
import energy.so.assets.server.services.DefaultFeatureService
import energy.so.assets.server.services.DefaultMeterReadingSubmissionRequestService
import energy.so.assets.server.services.DefaultMeterReadingSubmissionService
import energy.so.assets.server.services.DefaultMeterReadingsService
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.JuniferMeterReadingApiService
import energy.so.assets.server.services.MeterReadingApiService
import energy.so.assets.server.services.MeterReadingSubmissionRequestService
import energy.so.assets.server.services.MeterReadingSubmissionService
import energy.so.assets.server.services.MeterReadingsService
import energy.so.assets.server.validators.MeterReadBoundValidator
import energy.so.assets.server.validators.MeterReadingValidator
import energy.so.assets.sync.repository.JooqMeterReadingEntityRepository
import energy.so.assets.sync.repository.MeterReadingEntityRepository
import energy.so.commons.database.DatabaseConfigurator
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.queues.config.PubSubConfiguration
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.queues.publishers.PubsubMessagePublisher
import energy.so.commons.redis.pubsub.JedisClusterPubSub
import energy.so.commons.redis.pubsub.JedisPoolPubSub
import energy.so.commons.validations.validator.aggregate
import energy.so.commons.validations.validator.toNonEmptyListOrNull
import energy.so.communications.v1.CommunicationClient
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.accounts.AccountsClient
import energy.so.customers.client.v2.accounts.RestrictToUserAccounts
import energy.so.customers.client.v2.agreements.AgreementsClient
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import energy.so.users.client.v2.FeatureClient
import energy.so.users.client.v2.UsersClient
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.qualifier.StringQualifier
import org.koin.dsl.module

@Module
@ComponentScan("energy.so.assets.server.validators")
object MeterReadingsModule {

    private val customersQualifier = StringQualifier("be-customers-config")
    private val submissionQualifier = StringQualifier("meter-readings-submission")
    private val submissionRequestQualifier = StringQualifier("meter-readings-submission-request")
    private val eventsQualifier = StringQualifier("meter-readings-events")
    private val serviceValidatorsQualifier = StringQualifier("aggregate-meter-reading-service-validators")

    private val juniferConfig = Constants.get<GrpcServiceConfig>("be-ac-junifer")
    private val identityConfig = Constants.get<GrpcServiceConfig>("be-identity")
    private val communicationsConfig = Constants.get<GrpcServiceConfig>("be-communications")
    private val customersConfig = Constants.get<GrpcServiceConfig>("be-customers")
    private val pubsubConfig = PubSubConfiguration.fromApplicationConf()
    private val asyncMeterReadingSubmissionRequestConfig = Constants.get<AsyncMeterReadingSubmissionRequestConfig>(
        "asyncMeterReadingSubmissionRequest"
    )

    private val redisConfig = Constants.get<RedisConfig>("redis")

    val manualModule = module {
        single {
            MeterReadingsController(
                get(),
                get(),
                get(),
                get(qualifier = submissionQualifier),
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
            )
        }
        // GRPC
        // Validators will automatically be scanned from the validation.validators package
        single(qualifier = serviceValidatorsQualifier) {
            getAll<MeterReadingValidator>().toNonEmptyListOrNull()!!.aggregate()
        }
        single {
            MeterReadBoundValidator(
                meterPointService = get(),
                meterPointsRepository = get(),
                registerRepository = get(),
                meterReadingsApiService = get(),
                featureService = get(),
            )
        }

        // Services
        single { AssetsClient(juniferConfig) }
        single { UsersClient(identityConfig) }
        single { BillingAccountsClient(customersConfig) }
        single { CustomersClient(customersConfig) }
        single { ProductAccountsClient(customersConfig) }
        single { CommunicationClient(communicationsConfig) }
        single { FeatureClient(identityConfig) }
        single { AgreementsClient(customersConfig) }
        single { BlockingSmartReadsClient(juniferConfig) }
        single<MeterReadingApiService> {
            JuniferMeterReadingApiService(
                assetsClient = get(),
                agreementsClient = get(),
                registerRepository = get(),
                meterPointRepository = get(),
                juniferEntityMapper = get(),
                featureService = get(),
            )
        }
        single<MeterReadingsService> {
            DefaultMeterReadingsService(
                meterReadingsRepository = get(),
                meterPointsRepository = get(),
                registerRepository = get(),
                acAssetsClient = get(),
                validator = get(qualifier = serviceValidatorsQualifier),
                meterReadingApiService = get(),
                meterReadingConfig = get(),
                featureService = get(),
                productAccountsClient = get(),
                smartReadsClient = get()

            )
        }
        single<MeterReadingSubmissionService> {
            DefaultMeterReadingSubmissionService(
                meterReadingsService = get(),
                meterReadingsRepository = get(),
                usersClient = get(),
                customersClient = get(),
                billingAccountsClient = get(),
                productAccountsClient = get(),
                communicationsClient = get(),
                assetsClient = get(),
                juniferEntityMapper = get(),
                meterReadingApiService = get(),
                meterReadingSubmissionRequestService = get(),
                meterRepository = get(),
                registerRepository = get(),
                meterReadBoundValidator = get(),
                featureService = get(),
                validator = get(qualifier = serviceValidatorsQualifier),
            )
        }
        single<MeterReadingSubmissionRequestService> {
            DefaultMeterReadingSubmissionRequestService(
                meterReadingPublisher = get(qualifier = submissionRequestQualifier),
                meterReadingSubmissionRequestRepository = get(),
                userClient = get(),
                billingAccountClient = get(),
                maxFailedAttempts = asyncMeterReadingSubmissionRequestConfig.maxFailureAttempts,
                assetsClient = get(),
                meterPointsRepository = get(),
                featureService = get(),
                redisEventPublisher = get(),
                meterReadingService = get(),
            )
        }
        single<FeatureService> {
            DefaultFeatureService(
                featureClient = get(),
            )
        }

        // Database
        single<MeterPointsRepository> {
            JooqMeterPointsRepository(
                dslContext = get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get(),
                get()
            )
        }
        single<MeterRepository> { JooqMeterRepository(dslContext = get()) }
        single<PropertyRepository> { JooqPropertyRepository(dslContext = get(), get()) }
        single<MeterPointElectricityRepository> { JooqMeterPointElectricityRepository(dslContext = get()) }
        single<MeterPointGasRepository> { JooqMeterPointGasRepository(dslContext = get()) }
        single<MeterReadingsRepository> { JooqMeterReadingsRepository(dslContext = get(), registerRepository = get()) }
        single<RegisterRepository> { JooqRegisterRepository(dslContext = get()) }
        single<MeterPointHistoryRepository> { JooqMeterPointHistoryRepository(dslContext = get()) }
        single<MeterReadingEntityRepository> { JooqMeterReadingEntityRepository(dslContext = get()) }
        single<MeterReadingSubmissionRequestRepository> {
            JooqMeterReadingSubmissionRequestRepository(
                dslContext = get()
            )
        }

        single(createdAtStart = true) {
            DatabaseConfigurator.startContext(
                listeners = listOf(
                    MeterReadingsDatabaseListener(messagePublisher = get(qualifier = eventsQualifier))
                )
            )
        }

        single {
            RestrictToUserAccounts(accountsClient = get())
        }

        // Pub/Sub
        single<MessagePublisher<String>>(createdAtStart = true, qualifier = eventsQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-meter-readings-events")!!
            )
        }

        single<MessagePublisher<String>>(createdAtStart = true, qualifier = submissionQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-meter-readings-submissions")!!
            )
        }

        single<MessagePublisher<String>>(createdAtStart = true, qualifier = submissionRequestQualifier) {
            PubsubMessagePublisher(
                pubsubConfig.projectName,
                pubsubConfig.getTopicByKey("be-assets-meter-read-submission-requests")!!
            )
        }

        single {
            MeterReadingSubmissionConsumer(
                meterReadingSubmissionService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-meter-readings-submissions-subscription")!!,
            )
        }

        single {
            PersistedMeterReadingSubmissionRequestConsumer(
                meterReadingSubmissionRequestService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey(
                    "be-assets-queue-persisted-meter-read-submission-requests-subscription"
                )!!,
            )
        }

        single {
            MeterReadSubmissionRequestConsumer(
                meterReadingSubmissionService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-meter-read-submission-requests-subscription")!!,
            )
        }

        single {
            UpdateMeterReadSubmissionStatusConsumer(
                meterReadingSubmissionRequestService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-update-contacted-reads-status-subs")!!,
            )
        }

        single {
            MeterReadingStatusUpdateConsumer(
                meterReadingSubmissionRequestService = get(),
                projectName = pubsubConfig.projectName,
                config = pubsubConfig.getSubscriptionByKey("be-assets-meter-reading-status-updates-subs")!!,
            )
        }

        single(qualifier = customersQualifier) {
            Constants.get<GrpcServiceConfig>(
                "be-customers"
            )
        }
        single {
            Constants.get<MeterReadingConfig>("meter-reading")
        }

        single { AccountsClient(config = get(qualifier = customersQualifier)) }
        single {
            if (redisConfig.cluster) {
                JedisClusterPubSub(config = get())
            } else {
                JedisPoolPubSub(config = get())
            }
        }
        single(createdAtStart = true) {
            RedisEventPublisher(
                redisTopics = redisConfig.topics,
                redisPubSub = get()
            )
        }
    }
}
