package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterReadingPhotoEvidence
import energy.so.assets.server.models.toJooq
import energy.so.commons.model.enums.MeterReadingPhotoEvidenceStatu.DONE
import energy.so.commons.model.enums.MeterReadingPhotoEvidenceStatu.PENDING
import energy.so.commons.model.tables.references.METER_READING_PHOTO_EVIDENCE
import energy.so.commons.model.tables.references.OUTBOUND_PHOTO_REQUEST
import org.jooq.DSLContext
import org.jooq.Records
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.MeterReadingPhotoEvidence as JooqMeterReadingPhotoEvidence

class JooqMeterReadingPhotoEvidenceRepository(
    private val dslContext: DSLContext,
) : MeterReadingPhotoEvidenceRepository {

    override fun save(
        meterReadingPhotoEvidence: MeterReadingPhotoEvidence,
    ): MeterReadingPhotoEvidence? =
        dslContext.insertInto(METER_READING_PHOTO_EVIDENCE)
            .set(dslContext.newRecord(METER_READING_PHOTO_EVIDENCE, meterReadingPhotoEvidence.toJooq()))
            .onDuplicateKeyIgnore().returning().fetchOne()?.into(JooqMeterReadingPhotoEvidence())
            ?.let { MeterReadingPhotoEvidence.fromJooq(it) }

    override fun getByAccountNumber(accountNumberParam: String): List<MeterReadingPhotoEvidence> {
        val mrpe = METER_READING_PHOTO_EVIDENCE
        val opr = OUTBOUND_PHOTO_REQUEST

        return dslContext.select(
            mrpe.ID.`as`("id"),
            mrpe.ACCOUNT_NUMBER.`as`("account_number"),
            mrpe.METER_POINT_ID.`as`("meter_point_id"),
            mrpe.SUBMISSION_DATE.`as`("submission_date"),
            mrpe.IMAGE_IDENTIFIER.`as`("image_identifier"),
            mrpe.ERROR_CODE.`as`("error_code"),
            mrpe.DELETED.`as`("deleted"),
            mrpe.CREATED_AT.`as`("created_at"),
            mrpe.UPDATED_AT.`as`("updated_at"),
            mrpe.STANDARD_READ.`as`("standard_read"),
            mrpe.DAY_READ.`as`("day_read"),
            mrpe.NIGHT_READ.`as`("night_read"),
            mrpe.METER_POINT_IDENTIFIER.`as`("meter_point_identifier"),
            mrpe.STATUS.`as`("status"),
            mrpe.IMAGE_TYPE.`as`("image_type"),
            mrpe.FUEL.`as`("fuel"),
            mrpe.FRESHDESK_TICKET_ID.`as`("freshdesk_ticket_id"),
            mrpe.OUTBOUND_REQUEST_ID.`as`("outbound_request_id"),
            opr.REQUEST_REASON.`as`("request_reason"),
        ).from(mrpe)
            .leftJoin(opr).on(mrpe.OUTBOUND_REQUEST_ID.eq(opr.ID))
            .where(mrpe.ACCOUNT_NUMBER.eq(accountNumberParam))
            .and(mrpe.DELETED.isNull).and(mrpe.STATUS.eq(DONE))
            .fetch(Records.mapping {
                    id, accountNumber, meterPointId, submissionDate, imageIdentifier, errorCode,
                    deleted, createdAt, updatedAt, standardRead, dayRead, nightRead, meterPointIdentifier, status,
                    imageType, fuel, freshdeskTicketId, outboundRequestId, requestReason,
                ->
                MeterReadingPhotoEvidence.fromJooq(
                    JooqMeterReadingPhotoEvidence(
                        id, accountNumber, meterPointId, submissionDate, imageIdentifier, errorCode,
                        deleted, createdAt, updatedAt, standardRead, dayRead, nightRead, meterPointIdentifier, status,
                        imageType, fuel, freshdeskTicketId, outboundRequestId
                    ), requestReason
                )
            })
    }

    override fun getByImageIdentifier(imageIdentifier: String): MeterReadingPhotoEvidence? =
        dslContext.selectFrom(METER_READING_PHOTO_EVIDENCE)
            .where(METER_READING_PHOTO_EVIDENCE.IMAGE_IDENTIFIER.eq(imageIdentifier))
            .and(METER_READING_PHOTO_EVIDENCE.DELETED.isNull)
            .and(METER_READING_PHOTO_EVIDENCE.STATUS.eq(PENDING))
            .fetchOneInto(JooqMeterReadingPhotoEvidence::class.java)
            ?.let { MeterReadingPhotoEvidence.fromJooq(it) }

    override fun getByOutboundRequestId(outboundRequestId: Long): List<MeterReadingPhotoEvidence> =
        dslContext.selectFrom(METER_READING_PHOTO_EVIDENCE)
            .where(METER_READING_PHOTO_EVIDENCE.OUTBOUND_REQUEST_ID.eq(outboundRequestId))
            .and(METER_READING_PHOTO_EVIDENCE.DELETED.isNull)
            .fetchInto(JooqMeterReadingPhotoEvidence::class.java)
            .map { MeterReadingPhotoEvidence.fromJooq(it) }

    override fun deleteByImageIdentifier(imageIdentifier: String) {
        dslContext.update(METER_READING_PHOTO_EVIDENCE)
            .set(METER_READING_PHOTO_EVIDENCE.DELETED, LocalDateTime.now())
            .where(METER_READING_PHOTO_EVIDENCE.IMAGE_IDENTIFIER.eq(imageIdentifier)).execute()
    }

    override fun markAsDoneAndSetFreshdeskTicket(
        imageIdentifier: String,
        imageType: String,
        freshDeskTicketId: Long?,
        caseId: String?,
    ) {
        dslContext.update(METER_READING_PHOTO_EVIDENCE).set(METER_READING_PHOTO_EVIDENCE.STATUS, DONE)
            .set(METER_READING_PHOTO_EVIDENCE.IMAGE_TYPE, imageType)
            .set(METER_READING_PHOTO_EVIDENCE.FRESHDESK_TICKET_ID, freshDeskTicketId)
            .set(METER_READING_PHOTO_EVIDENCE.CASE_ID, caseId)
            .where(METER_READING_PHOTO_EVIDENCE.IMAGE_IDENTIFIER.eq(imageIdentifier)).execute()
    }
}
