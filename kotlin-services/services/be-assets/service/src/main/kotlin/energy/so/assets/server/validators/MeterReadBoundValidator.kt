package energy.so.assets.server.validators

import arrow.core.Validated
import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.sequence
import arrow.core.validNel
import energy.so.assets.meterReadings.v2.MeterReadingStatusMessage
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.models.AccountMeterReadingsRequestDto
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithMTD
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithoutMTD
import energy.so.assets.server.models.MeterPointType
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingQuality
import energy.so.assets.server.models.MeterReadingSequenceType
import energy.so.assets.server.models.MeterReadingUnit
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.assets.server.models.Register
import energy.so.assets.server.models.UnitType
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.MeterReadingApiService
import energy.so.commons.v2.search.pagination
import energy.so.users.v2.FeatureName
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import org.koin.core.annotation.Single
import kotlin.math.ceil
import kotlin.math.pow

const val DAYS_IN_YEAR = 365
const val PAGE_SIZE = 10_000

const val DEFAULT_KWH_VALUE = 1.0
const val MFAC_FT = 0.031412
const val MFAC_M = 0.089348

const val MULTIPLIER_GAS = 6
const val MULTIPLIER_ELEC = 4

const val MIN_BOUND_GAS = 300.0
const val MIN_BOUND_ELEC = 900.0
const val DEFAULT_ESTIMATION = 3100.0
const val DEFAULT_MAX_REGISTER_COUNT = 999_999_999

const val STANDARD_RATE_NAME = "standard"

@Single(createdAtStart = true)
class MeterReadBoundValidator(
    private val meterPointService: MeterPointService,
    private val meterPointsRepository: MeterPointsRepository,
    private val registerRepository: RegisterRepository,
    private val meterReadingsApiService: MeterReadingApiService,
    private val featureService: FeatureService,
) : MeterReadingValidator {

    override suspend fun validate(subject: MeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> {
        return if (featureService.isFeatureEnabled(FeatureName.TMP_SO_21630_READ_SUBMISSION_TOLERANCE_CHECK)) {
            when (subject) {
                is MeterReadingsWithoutTechnicalDetailsDto -> validateMeterReadingsRequestWithoutMTD(subject)
                is MeterReadingsWithTechnicalDetailsDto -> validateMeterReadingsRequestWithMTD(subject)
            }
        } else {
            validNel()
        }
    }

    suspend fun validateAccountMeterReadingsRequestDto(subject: AccountMeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> {
        return when (subject) {
            is AccountMeterReadingsRequestDtoWithMTD -> {
                val meterPoint = meterPointsRepository.findByMeterIds(listOf(subject.meterId)).firstOrNull()
                    ?: return Validated.validNel()
                val meterPointId = meterPoint.id!!
                val isGas = meterPoint.type == MeterPointType.MPRN
                validateAccountMeterReadings(subject, meterPointId, isGas)
            }

            is AccountMeterReadingsRequestDtoWithoutMTD -> {
                val meterPoint = meterPointsRepository.findByIds(listOf(subject.meterPointId)).firstOrNull()
                    ?: return Validated.validNel()
                val meterPointId = meterPoint.id!!
                val isGas = meterPoint.type == MeterPointType.MPRN
                validateAccountMeterReadings(subject, meterPointId, isGas)
            }
        }
    }

    private suspend fun validateMeterReadingsRequestWithMTD(
        dto: MeterReadingsWithTechnicalDetailsDto,
    ): ValidatedNel<MeterReadingError, *> {
        return dto.readings.map { reading ->
            val register = reading.registerId?.let {
                registerRepository.getById(it)
            } ?: return@map validNel()

            val estimatedUsage = estimateUsage(dto.meterPointId, register.rateName)
            val lastRead = getLastMeterRead(
                dto.meterPointId,
                reading.readingDateTime.toLocalDate()
            ) ?: return@map validNel()

            val upperBound = calculateUpperBound(lastRead, estimatedUsage, register, reading.unitType == UnitType.Gas)
            val rolloverPossible = lastRead.cumulative.toDouble() > upperBound

            if (rolloverPossible) {
                if (reading.cumulative < lastRead.cumulative.toDouble() && reading.cumulative > upperBound) {
                    return MeterReadingoOutOfBoundsError(
                        registerId = register.id,
                        error = "Reading out of range with rollover"
                    ).invalidNel()
                }
            } else {
                if (reading.cumulative <= lastRead.cumulative.toDouble()) {
                    return MeterReadingoOutOfBoundsError(
                        registerId = register.id,
                        error = "Reading too low"
                    ).invalidNel()
                }
                if (reading.cumulative > upperBound) {
                    return MeterReadingoOutOfBoundsError(
                        registerId = register.id,
                        error = "Reading too high"
                    ).invalidNel()
                }
            }
            validNel()
        }.sequence()
    }

    private suspend fun validateMeterReadingsRequestWithoutMTD(
        dto: MeterReadingsWithoutTechnicalDetailsDto,
    ): ValidatedNel<MeterReadingError, *> {
        return dto.registerReads.map { reading ->
            val estimatedUsage = estimateUsage(dto.meterPointId, reading.readingType.name)
            val lastRead = getLastMeterRead(
                dto.meterPointId,
                dto.readingDate
            ) ?: return@map validNel()

            val upperBound = calculateUpperBound(lastRead, estimatedUsage, null, reading.unitType == UnitType.Gas)
            val rolloverPossible = lastRead.cumulative.toDouble() > upperBound

            if (rolloverPossible) {
                if (reading.reading < lastRead.cumulative.toDouble() && reading.reading > upperBound) {
                    return MeterReadingoOutOfBoundsError(
                        error = "Reading out of range with rollover"
                    ).invalidNel()
                }
            } else {
                if (reading.reading <= lastRead.cumulative.toDouble()) {
                    return MeterReadingoOutOfBoundsError(
                        error = "Reading too low"
                    ).invalidNel()
                }
                if (reading.reading > upperBound) {
                    return MeterReadingoOutOfBoundsError(
                        error = "Reading too high"
                    ).invalidNel()
                }
            }
            validNel()
        }.sequence()
    }

    private suspend fun validateAccountMeterReadings(
        reading: AccountMeterReadingsRequestDtoWithMTD,
        meterPointId: Long,
        isGas: Boolean,
    ): ValidatedNel<MeterReadingError, *> {
        val register = registerRepository.findByIds(setOf(reading.registerId)).firstOrNull() ?: return validNel()
        val estimatedUsage = estimateUsage(meterPointId, register.rateName)
        val lastRead = getLastMeterRead(meterPointId, reading.readingDate) ?: return validNel()

        val upperBound = calculateUpperBound(lastRead, estimatedUsage, register, isGas)
        val rolloverPossible = lastRead.cumulative.toDouble() > upperBound

        if (rolloverPossible) {
            if (reading.value < lastRead.cumulative.toDouble() && reading.value > upperBound) {
                return MeterReadingoOutOfBoundsError(
                    registerId = register.id,
                    error = "Reading out of range with rollover"
                ).invalidNel()
            }
        } else {
            if (reading.value <= lastRead.cumulative.toDouble()) {
                return MeterReadingoOutOfBoundsError(
                    registerId = register.id,
                    error = "Reading too low"
                ).invalidNel()
            }
            if (reading.value > upperBound) {
                return MeterReadingoOutOfBoundsError(
                    registerId = register.id,
                    error = "Reading too high"
                ).invalidNel()
            }
        }
        return validNel()
    }

    private suspend fun validateAccountMeterReadings(
        reading: AccountMeterReadingsRequestDtoWithoutMTD,
        meterPointId: Long,
        isGas: Boolean,
    ): ValidatedNel<MeterReadingError, *> {
        val estimatedUsage = estimateUsage(meterPointId, reading.rateName)
        val lastRead = getLastMeterRead(meterPointId, reading.readingDate) ?: return validNel()

        val upperBound = calculateUpperBound(lastRead, estimatedUsage, null, isGas)
        val rolloverPossible = lastRead.cumulative.toDouble() > upperBound

        if (rolloverPossible) {
            if (reading.value < lastRead.cumulative.toDouble() && reading.value > upperBound) {
                return MeterReadingoOutOfBoundsError(
                    error = "Reading out of range with rollover"
                ).invalidNel()
            }
        } else {
            if (reading.value < lastRead.cumulative.toDouble()) {
                return MeterReadingoOutOfBoundsError(
                    error = "Reading too low"
                ).invalidNel()
            }
            if (reading.value > upperBound) {
                return MeterReadingoOutOfBoundsError(
                    error = "Reading too high"
                ).invalidNel()
            }
        }
        return validNel()
    }

    private suspend fun getLastMeterRead(meterPointId: Long, readingDate: LocalDate) =
        meterReadingsApiService.search(
            meterReadingsSearchRequest {
                this.meterPointId.add(meterPointId)
                status.add(MeterReadingStatusMessage.MeterReadingStatus.ACCEPTED)
                pagination = pagination {
                    pageNumber = 1
                    pageSize = PAGE_SIZE
                }
            }
        ).filter { it.readingDttm.isBefore(readingDate.atStartOfDay()) && it.quality != MeterReadingQuality.ESTIMATED }
            .sortedByDescending { it.readingDttm }
            .firstOrNull { it.sequenceType != MeterReadingSequenceType.LAST }

    private suspend fun estimateUsage(meterPointId: Long, rateName: String?): Double {
        val estimatedUsage = meterPointService.getEstimatedUsage(meterPointId)
            .filter { it.rateName.name.lowercase() == (rateName?.lowercase() ?: STANDARD_RATE_NAME) }
        return if (estimatedUsage.isEmpty()) {
            DEFAULT_ESTIMATION
        } else {
            estimatedUsage.map { it.usage }.reduce(Double::plus)
        }
    }

    private fun calculateUpperBound(
        lastRead: MeterReading,
        estimatedUsage: Double,
        register: Register?,
        isGas: Boolean,
    ): Int {
        val rawUpperBound = computeRawUpperBound(lastRead, estimatedUsage, isGas)
        val maxRegisterCount = register?.let { 10.0.pow(it.digits) } ?: DEFAULT_MAX_REGISTER_COUNT.toDouble()

        return if (rawUpperBound >= maxRegisterCount) {
            ceil(rawUpperBound - maxRegisterCount).toInt()
        } else {
            ceil(rawUpperBound).toInt()
        }
    }

    private fun computeRawUpperBound(lastRead: MeterReading, estimatedUsage: Double, isGas: Boolean): Double =
        maxOf(if (isGas) MIN_BOUND_GAS else MIN_BOUND_ELEC, computeTolerance(lastRead, estimatedUsage, isGas)).plus(
            lastRead.cumulative.toDouble()
        )

    private fun computeTolerance(lastRead: MeterReading, estimatedUsage: Double, isGas: Boolean): Double =
        estimatedUsage.times(ChronoUnit.DAYS.between(lastRead.readingDttm, LocalDateTime.now()))
            .times(if (isGas) MULTIPLIER_GAS else MULTIPLIER_ELEC).times(
                if (isGas) {
                    if (lastRead.unit == MeterReadingUnit.ft3) MFAC_FT else MFAC_M
                } else {
                    DEFAULT_KWH_VALUE
                }
            ).div(DAYS_IN_YEAR)
}
