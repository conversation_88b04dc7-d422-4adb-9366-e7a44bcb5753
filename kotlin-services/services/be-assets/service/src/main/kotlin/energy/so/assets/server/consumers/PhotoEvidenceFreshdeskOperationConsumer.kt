package energy.so.assets.server.consumers

import energy.so.assets.server.models.MeterPhotoEvidenceIdentifiers
import energy.so.assets.server.models.jsonFormat
import energy.so.assets.server.services.DefaultMeterReadingPhotoEvidenceService.Companion.OUTBOUND_REQUEST_ID_PROPERTY
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.MeterReadingPhotoEvidenceService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor
import energy.so.users.v2.FeatureName

class PhotoEvidenceFreshdeskOperationConsumer(
    private val meterReadingPhotoEvidenceService: MeterReadingPhotoEvidenceService,
    private val featureService: FeatureService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug("Received message: ${message.data}")

        val meterPhotoEvidenceIdentifiers = jsonFormat.decodeFromString<MeterPhotoEvidenceIdentifiers>(message.data)
        logger.debug {
            "[meterPhotoEvidenceIdentifier:$meterPhotoEvidenceIdentifiers] forward reading to MeterReadingPhotoEvidenceService"
        }

        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_19346_OUTBOUND_PHOTO_REQUEST)) {
            if (message.isOutboundRequest()) {
                logger.info { "[photoEvidenceFreshdeskOperationConsumer] Updating outbound photo evidence freshdesk ticket" }

                meterReadingPhotoEvidenceService.updatePhotoEvidenceFreshDeskTicket(
                    meterPhotoEvidenceIdentifiers.identifiers,
                    message.properties[OUTBOUND_REQUEST_ID_PROPERTY]!!.toLong()
                )
            } else {
                logger.info { "[photoEvidenceFreshdeskOperationConsumer] Creating meter reading photo evidence freshdesk ticket" }

                meterReadingPhotoEvidenceService.createPhotoEvidenceFreshDeskTicket(meterPhotoEvidenceIdentifiers.identifiers)
            }
        } else {
            logger.info { "[photoEvidenceFreshdeskOperationConsumer] Creating meter reading photo evidence freshdesk ticket" }

            meterReadingPhotoEvidenceService.createPhotoEvidenceFreshDeskTicket(meterPhotoEvidenceIdentifiers.identifiers)
        }
    }

    private fun QueueMessage<String>.isOutboundRequest() = properties[OUTBOUND_REQUEST_ID_PROPERTY] != null
}
