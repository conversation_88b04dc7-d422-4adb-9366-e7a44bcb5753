package energy.so.assets.server.modules

import energy.so.ac.junifer.v1.generic.GenericSyncClient
import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.RedisConfig
import energy.so.assets.server.mapping.AcJuniferEntityMapper
import energy.so.assets.server.mapping.JuniferEntityMapper
import energy.so.assets.server.mapping.RedisJuniferEntityMapper
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.redis.JedisClusterClient
import energy.so.commons.redis.JedisRedisClient
import energy.so.commons.redis.RedisConnectionConfig
import org.koin.dsl.module

object EntityMappingModule {
    private val redisConfig = Constants.get<RedisConfig>("redis")

    val module = module {

        single { GenericSyncClient(Constants.get<GrpcServiceConfig>("be-ac-junifer")) }
        single { AcJuniferEntityMapper(genericSyncClient = get()) }
        single {
            if (redisConfig.disabledForJuniferEntityMapper) {
                JuniferEntityMapper(
                    listOf(
                        get<AcJuniferEntityMapper>()
                    )
                )
            } else {
                JuniferEntityMapper(
                    listOf(
                        RedisJuniferEntityMapper(redisClient = get()),
                        get<AcJuniferEntityMapper>()
                    )
                )
            }
        }

        single {
            if (redisConfig.cluster) {
                JedisClusterClient(config = get())
            } else {
                JedisRedisClient(config = get())
            }
        }
        single { RedisConnectionConfig(host = redisConfig.host, authString = redisConfig.authString) }
    }
}
