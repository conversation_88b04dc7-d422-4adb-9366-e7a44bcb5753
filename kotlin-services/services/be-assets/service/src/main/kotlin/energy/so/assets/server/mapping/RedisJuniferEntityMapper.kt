package energy.so.assets.server.mapping

import energy.so.commons.logging.TraceableLogging
import energy.so.commons.redis.BiDirectionalMapper

const val ENTITY_JUNIFER_PREFIX = "JUNIFER_"

private val logger = TraceableLogging.logger { }

class RedisJuniferEntityMapper(
    private val redisClient: BiDirectionalMapper<String, String>,
) : EntityMapper {
    override suspend fun getCoreId(entityType: EntityIdentifier, externalId: String): String? {
        val result = redisClient.inverseGet(ENTITY_JUNIFER_PREFIX + entityType.name, externalId)
        if (result.isNullOrBlank()) {
            logger.warn("Could not find coreId for entity type $entityType and externalId $externalId")
        }
        return result
    }
}
