package energy.so.assets.server.controllers

import energy.so.assets.properties.v2.CreatePropertyRequest
import energy.so.assets.properties.v2.PropertiesGrpcKt
import energy.so.assets.properties.v2.PropertiesSearchResponse
import energy.so.assets.properties.v2.PropertyResponse
import energy.so.assets.properties.v2.propertiesSearchResponse
import energy.so.assets.server.extensions.toProtobufModel
import energy.so.assets.server.models.toCreatePropertyDto
import energy.so.assets.server.models.toPropertyResponse
import energy.so.assets.server.services.PropertiesService
import energy.so.commons.exceptions.dto.ErrorCategories.ASSETS
import energy.so.commons.exceptions.dto.ErrorCodes.COMMONS_UNHANDLED_ERROR
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnauthenticatedGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.tables.records.PropertyRecord
import energy.so.commons.search.service.SearchService
import energy.so.commons.search.service.search
import energy.so.commons.session.SessionManager
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.search.SearchRequest
import energy.so.commons.validations.ValidationException
import energy.so.customers.client.v2.accounts.AccountsClient
import energy.so.customers.client.v2.accounts.findAccountIdsForSession
import energy.so.customers.client.v2.accounts.toUsersWithAccountsOrThrow
import io.opentelemetry.instrumentation.annotations.WithSpan

private val logger = TraceableLogging.logger { }

class PropertiesController(
    private val propertiesService: PropertiesService,
    private val searchService: SearchService<PropertyRecord>,
    private val accountsClient: AccountsClient,
) : PropertiesGrpcKt.PropertiesCoroutineImplBase() {

    @WithSpan
    override suspend fun createProperty(request: CreatePropertyRequest): PropertyResponse = try {
        logger.debug { "[::createProperty CreatePropertyRequest: $request] Creating a property" }

        request.toCreatePropertyDto()
            .run(propertiesService::createProperty)
            .toPropertyResponse()
    } catch (validationException: ValidationException) {
        throw InvalidArgumentGrpcException(
            message = validationException.message,
            errorCategory = ASSETS,
            errorCode = "001",
            cause = validationException,
        )
    } catch (exception: Exception) {
        throw exception.toUnknownGrpcException(
            nullMessageReplacement = "Unexpected error occurred while trying to create a property",
        )
    }

    @WithSpan
    override suspend fun searchProperties(request: SearchRequest): PropertiesSearchResponse {
        return searchService.search(
            request,
            PropertyRecord::toProtobufModel
        ) { properties, searchMetadata ->
            propertiesSearchResponse {
                results += properties
                metadata = searchMetadata
            }
        }
    }

    @WithSpan
    override suspend fun getProperty(request: IdRequest): PropertyResponse {
        val (userId, accountIds) = accountsClient.findAccountIdsForSession(SessionManager.getSession())
            .toUsersWithAccountsOrThrow()
        val propertyResponse = propertiesService.getProperty(request.id)
            .toPropertyResponse()

        if (!accountIds.contains(propertyResponse.accountId)) {
            throw UnauthenticatedGrpcException("User [$userId] cannot access account [${propertyResponse.accountId}]")
        }

        return propertyResponse
    }

    private fun Exception.toUnknownGrpcException(nullMessageReplacement: String) = UnknownGrpcException(
        message = message ?: nullMessageReplacement,
        errorCategory = ASSETS,
        errorCode = COMMONS_UNHANDLED_ERROR,
        cause = this,
    )
}
