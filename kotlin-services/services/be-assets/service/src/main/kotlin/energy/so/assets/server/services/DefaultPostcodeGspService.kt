package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.gspGroupIdsByPostcodeRequest
import energy.so.ac.junifer.v1.assets.gspGroupIdsByPostcodeResponse
import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeRequest
import energy.so.assets.postcodeGsp.v2.GspGroupIdsByPostcodeResponse
import energy.so.assets.server.config.ShadowTrafficConfig
import energy.so.assets.server.database.repositories.PostcodeGspCacheRepository
import energy.so.assets.server.models.PostcodeGsp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.tables.pojos.PostcodeGspCache
import java.time.LocalDateTime

private val logger = TraceableLogging.logger { }
private const val GSP_GROUP_ID = "_A"

class DefaultPostcodeGspService(
    private val postcodeGspCacheRepository: PostcodeGspCacheRepository,
    private val assetsClient: AssetsClient,
    private val shadowTrafficConfig: ShadowTrafficConfig,
) : PostcodeGspService {

    companion object {
        var defaultGspGroupIdsByPostcodeResponse = gspGroupIdsByPostcodeResponse {
            gspGroupIds.add(GSP_GROUP_ID)
        }
    }

    override suspend fun getGspIdGroupsByPostcode(request: GspGroupIdsByPostcodeRequest): GspGroupIdsByPostcodeResponse {
        logger.debug { "[::getGspIdGroupsByPostcode] Request get gsp group id by postcode ${request.postcode}" }

        return if (shadowTrafficConfig.enabled) {
            val postcodeGspFromCache = postcodeGspCacheRepository.findByPostcode(request.postcode)

            if (postcodeGspFromCache != null) {
                createResponse(postcodeGspFromCache.gspGroupIds?.split(","))
            } else {
                logger.debug(
                    "[::getGspIdGroupsByPostcode] Cannot retrieve gsp id groups by postcode ${request.postcode} from database"
                )

                try {
                    val gspGroupIdsByPostcode =
                        assetsClient.getGspGroupIdsByPostcode(toBeAcRequest(request.postcode))

                    if (gspGroupIdsByPostcode.gspGroupIdsList.isNotEmpty()) {
                        saveToCache(request.postcode, gspGroupIdsByPostcode.gspGroupIdsList.joinToString())
                    }
                    createResponse(gspGroupIdsByPostcode.gspGroupIdsList)
                } catch (e: Exception) {
                    logger.error(
                        "[::getGspIdGroupsByPostcode] Cannot retrieve gsp id groups by postcode ${request.postcode} from shadow cache"
                    )

                    createResponse(defaultGspGroupIdsByPostcodeResponse.gspGroupIdsList)
                }
            }
        } else {
            val postcodeGspFromCache = postcodeGspCacheRepository.findByPostcode(request.postcode)

            return if (postcodeGspFromCache == null || cacheMoreThan7DaysOld(postcodeGspFromCache)) {
                logger.debug(
                    "[::getGspIdGroupsByPostcode] Cannot retrieve gsp id groups by postcode ${request.postcode} from database or cache too old"
                )

                val gspGroupIdsByPostcode = assetsClient.getGspGroupIdsByPostcode(toBeAcRequest(request.postcode))

                if (gspGroupIdsByPostcode.gspGroupIdsList.isNotEmpty()) {
                    logger.debug(
                        "[::getGspIdGroupsByPostcode] Saving gsp id groups to cache for postcode ${request.postcode}"
                    )

                    saveToCache(request.postcode, gspGroupIdsByPostcode.gspGroupIdsList.joinToString())
                }
                createResponse(gspGroupIdsByPostcode.gspGroupIdsList)
            } else {
                createResponse(postcodeGspFromCache.gspGroupIds?.split(","))
            }
        }
    }

    private fun saveToCache(postcode: String, gspGroupIds: String) {
        postcodeGspCacheRepository.save(
            PostcodeGspCache(
                postcode = postcode,
                gspGroupIds = gspGroupIds,
                updatedAt = LocalDateTime.now()
            )
        )
    }

    private fun createResponse(gspGroupIds: List<String>?): GspGroupIdsByPostcodeResponse {
        return GspGroupIdsByPostcodeResponse.newBuilder()
            .addAllGspGroupIds(gspGroupIds)
            .build()
    }

    private fun toBeAcRequest(postcode: String) =
        gspGroupIdsByPostcodeRequest {
            this.postcode = postcode
            this.formatAddress = false
        }

    private fun cacheMoreThan7DaysOld(postcodeGspFromCache: PostcodeGsp) =
        if (postcodeGspFromCache.updatedAt == null) {
            true
        } else
            postcodeGspFromCache.updatedAt.isBefore(LocalDateTime.now().minusDays(7))
}
