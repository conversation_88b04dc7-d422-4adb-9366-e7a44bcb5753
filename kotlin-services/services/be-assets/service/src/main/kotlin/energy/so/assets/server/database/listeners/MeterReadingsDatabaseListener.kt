package energy.so.assets.server.database.listeners

import energy.so.commons.database.DatabaseRecordListener
import energy.so.commons.model.tables.records.MeterReadingRecord
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import org.jooq.RecordContext

class MeterReadingsDatabaseListener(
    private val messagePublisher: MessagePublisher<String>,
) : DatabaseRecordListener {

    override fun insertEnd(ctx: RecordContext?) {
        val record = ctx?.record() as? MeterReadingRecord ?: return
        messagePublisher.publishMessageAsync(
            message = QueueMessage(record.get("id").toString(), EventType.CREATED),
            retries = 3
        )
    }
}
