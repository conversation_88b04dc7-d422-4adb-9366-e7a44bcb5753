package energy.so.assets.server.database.repositories

import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.models.MeterPoint
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.enums.DccServiceStatu
import energy.so.commons.model.enums.MeterPointType
import energy.so.commons.model.tables.records.Junifer_MeterpointRecord
import energy.so.commons.model.tables.references.ADDRESS
import energy.so.commons.model.tables.references.JUNIFER__METERPOINT
import energy.so.commons.model.tables.references.METER
import energy.so.commons.model.tables.references.METER_METER_POINT_REL
import energy.so.commons.model.tables.references.METER_POINT
import energy.so.commons.model.tables.references.PROPERTY
import java.time.LocalDateTime
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.MeterPoint as JooqMeterPoint

private const val DCC_SERVICE_TYPE = "DCC"

class JooqMeterPointsRepository(
    private val dslContext: DSLContext,
    private val meterRepository: MeterRepository,
    private val propertyRepository: PropertyRepository,
    private val meterPointGasRepository: MeterPointGasRepository,
    private val meterPointElectricityRepository: MeterPointElectricityRepository,
    private val meterPointHistoryRepository: MeterPointHistoryRepository,
    private val estimatedUsageRepository: EstimatedUsageRepository,
    private val meterReadingConfig: MeterReadingConfig,
) : MeterPointsRepository {

    override fun existsById(id: Long): Boolean {
        return dslContext.fetchExists(
            dslContext.select().from(METER_POINT)
                .where(
                    METER_POINT.ID.eq(id),
                    METER_POINT.DELETED.isNull
                )
        )
    }

    override fun hasValidDccServiceStatusForSubmit(id: Long): Boolean {
        return dslContext.fetchExists(
            dslContext.select().from(METER_POINT)
                .where(
                    METER_POINT.ID.eq(id),
                    METER_POINT.DELETED.isNull,
                    METER_POINT.SERVICE_TYPE.notEqual(DCC_SERVICE_TYPE)
                        .or(
                            METER_POINT.DCC_SERVICE_STATUS.isNull.or(
                                METER_POINT.DCC_SERVICE_STATUS.notIn(
                                    DccServiceStatu.NON_ACTIVE,
                                    DccServiceStatu.WITHDRAWN
                                )
                            )
                        )
                )
        )
    }

    override fun findAllByAssetIds(assetIds: List<Long>): List<MeterPoint> = dslContext
        .select(METER_POINT.asterisk())
        .from(METER_POINT)
        .innerJoin(METER_METER_POINT_REL)
        .on(METER_POINT.ID.eq(METER_METER_POINT_REL.METER_POINT_ID))
        .and(METER_METER_POINT_REL.FROM_DT.lessOrEqual(LocalDateTime.now()))
        .and(METER_METER_POINT_REL.TO_DT.isNull.or(METER_METER_POINT_REL.TO_DT.greaterOrEqual(LocalDateTime.now())))
        .innerJoin(METER)
        .on(METER_METER_POINT_REL.METER_ID.eq(METER.ID))
        .where(
            METER.ID.`in`(assetIds),
            METER_POINT.DELETED.isNull,
            METER_METER_POINT_REL.DELETED.isNull,
            METER.DELETED.isNull
        )
        .fetchInto(JooqMeterPoint::class.java)
        .mapNotNull { mapMeterPointToModel(it) }

    override fun findMeterPointsByAddressIds(addressIds: List<Long>): List<MeterPoint> =
        dslContext.select(METER_POINT.asterisk())
            .from(METER_POINT)
            .join(PROPERTY)
            .on(METER_POINT.PROPERTY_ID.eq(PROPERTY.ID))
            .join(ADDRESS)
            .on(PROPERTY.ADDRESS_ID.eq(ADDRESS.ID))
            .where(
                PROPERTY.ADDRESS_ID.`in`(addressIds),
                ADDRESS.DELETED.isNull,
                PROPERTY.DELETED.isNull,
                METER_POINT.DELETED.isNull
            )
            .fetchInto(JooqMeterPoint::class.java)
            .mapNotNull { mapMeterPointToModel(it) }

    override fun findByIds(ids: List<Long>): List<MeterPoint> =
        dslContext.select(METER_POINT.asterisk())
            .from(METER_POINT)
            .where(METER_POINT.ID.`in`(ids), METER_POINT.DELETED.isNull)
            .fetchInto(JooqMeterPoint::class.java)
            .mapNotNull { mapMeterPointToModel(it) }

    override fun findById(id: Long): MeterPoint = dslContext.select(METER_POINT.asterisk())
        .from(METER_POINT)
        .where(METER_POINT.ID.eq(id), METER_POINT.DELETED.isNull)
        .fetchOneInto(JooqMeterPoint::class.java)
        ?.let { mapMeterPointToModel(it) }
        ?: throw EntityNotFoundException("MeterPoint not found")

    override fun findByMeterIds(ids: List<Long>): List<MeterPoint> = dslContext
        .select(METER_POINT.asterisk())
        .from(METER_METER_POINT_REL)
        .innerJoin(METER_POINT)
        .on(METER_METER_POINT_REL.METER_POINT_ID.eq(METER_POINT.ID))
        .and(METER_METER_POINT_REL.FROM_DT.lessOrEqual(LocalDateTime.now()))
        .and(METER_METER_POINT_REL.TO_DT.isNull.or(METER_METER_POINT_REL.TO_DT.greaterOrEqual(LocalDateTime.now())))
        .where(
            METER_METER_POINT_REL.METER_ID.`in`(ids),
            METER_POINT.DELETED.isNull,
            METER_METER_POINT_REL.DELETED.isNull
        )
        .fetchInto(JooqMeterPoint::class.java)
        .mapNotNull { mapMeterPointToModel(it) }

    override fun findByMPXN(mpxn: String): MeterPoint? = dslContext.select(METER_POINT.asterisk())
        .from(METER_POINT)
        .where(METER_POINT.IDENTIFIER.eq(mpxn), METER_POINT.DELETED.isNull)
        .fetchOneInto(JooqMeterPoint::class.java)
        ?.let { meterPt ->
            mapMeterPointToModel(meterPt)
        }

    override fun findByMPXNs(mpxns: List<String>): List<MeterPoint> = dslContext.select(METER_POINT.asterisk())
        .from(METER_POINT)
        .where(METER_POINT.IDENTIFIER.`in`(mpxns), METER_POINT.DELETED.isNull)
        .fetchInto(JooqMeterPoint::class.java)
        .mapNotNull {
            mapMeterPointToModel(it)
        }

    override fun bulkFindByMPXNs(mpxns: List<String>): List<Junifer_MeterpointRecord> {
        return dslContext.select(JUNIFER__METERPOINT.asterisk())
            .from(JUNIFER__METERPOINT)
            .where(JUNIFER__METERPOINT.IDENTIFIER.`in`(mpxns))
            .fetchInto(Junifer_MeterpointRecord::class.java)
    }

    override fun findByIdentifier(identifier: String): MeterPoint? = dslContext
        .selectFrom(METER_POINT)
        .where(METER_POINT.IDENTIFIER.eq(identifier).and(METER_POINT.DELETED.isNull))
        .fetchOneInto(JooqMeterPoint::class.java)?.let {
            MeterPoint.fromJooq(
                jooq = it,
                meters = getMeters(it)
            )
        }

    private fun getMeters(jooqMeterPoint: JooqMeterPoint) =
        meterRepository.findByMeterPointId(
            jooqMeterPoint.id!!,
            meterReadingConfig.getDaysBeforeSupplyStartDate(!jooqMeterPoint.isElectricityType())
        )


    private fun mapMeterPointToModel(meterPoint: JooqMeterPoint): MeterPoint {
        val property = if (meterPoint.propertyId != null) propertyRepository.findById(meterPoint.propertyId) else null
        val daysBeforeSupplyStartDate = meterReadingConfig.getDaysBeforeSupplyStartDate(!meterPoint.isElectricityType())

        return meterRepository.findByMeterPointId(meterPoint.id!!, daysBeforeSupplyStartDate).let { meter ->
            MeterPoint.fromJooq(
                jooq = meterPoint,
                property = property,
                meters = meter,
                meterPointGas =
                if (meterPoint.isGasType()) meterPointGasRepository.findById(meterPoint.id) else null,
                meterPointElectricity =
                if (meterPoint.isElectricityType()) {
                    meterPointElectricityRepository.findById(
                        meterPoint.id
                    )
                } else {
                    null
                },
                meterPointHistory = meterPointHistoryRepository.findLastByMeterPointId(meterPoint.id)
            )
        }
    }
}

fun JooqMeterPoint.isGasType(): Boolean = MeterPointType.MPRN == this.type

fun JooqMeterPoint.isElectricityType(): Boolean = MeterPointType.MPAN == this.type
