package energy.so.assets.sync.service

import energy.so.assets.sync.repository.AddressEntityRepository
import energy.so.assets.sync.repository.AssetEntityRepository
import energy.so.assets.sync.repository.EstimatedUsageEntityRepository
import energy.so.assets.sync.repository.MeterEntityRepository
import energy.so.assets.sync.repository.MeterMeterPointRelEntityRepository
import energy.so.assets.sync.repository.MeterPointElectricityEntityRepository
import energy.so.assets.sync.repository.MeterPointGasEntityRepository
import energy.so.assets.sync.repository.MeterPointHistoryEntityRepository
import energy.so.assets.sync.repository.MeterPointsEntityRepository
import energy.so.assets.sync.repository.MeterReadingEntityRepository
import energy.so.assets.sync.repository.PropertyEntityRepository
import energy.so.assets.sync.repository.RegisterEntityRepository
import energy.so.assets.sync.v2.AddressEntityRequest
import energy.so.assets.sync.v2.AssetEntityRequest
import energy.so.assets.sync.v2.EstimatedUsageEntityRequest
import energy.so.assets.sync.v2.MeterEntityRequest
import energy.so.assets.sync.v2.MeterMeterPointRelEntityRequest
import energy.so.assets.sync.v2.MeterPointElectricityEntityRequest
import energy.so.assets.sync.v2.MeterPointEntityRequest
import energy.so.assets.sync.v2.MeterPointGasEntityRequest
import energy.so.assets.sync.v2.MeterPointHistoryEntityRequest
import energy.so.assets.sync.v2.MeterReadingEntityRequest
import energy.so.assets.sync.v2.PropertyEntityRequest
import energy.so.assets.sync.v2.RegisterEntityRequest
import energy.so.commons.grpc.exceptions.SyncValidationFailedException
import energy.so.commons.grpc.sync.toSyncResponse
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.MeterReadingQuality
import energy.so.commons.model.enums.MeterReadingSequenceType
import energy.so.commons.model.enums.MeterReadingSource
import energy.so.commons.model.enums.MeterReadingStatu
import energy.so.commons.model.enums.MeterReadingUnit
import energy.so.commons.model.enums.MeterReadingWorkflowStatu
import energy.so.commons.v2.sync.OperationType
import energy.so.commons.v2.sync.SyncResponse

private val logger = TraceableLogging.logger {}

class DefaultSyncService(
    private val assetRepo: AssetEntityRepository,
    private val meterRepo: MeterEntityRepository,
    private val registerRepo: RegisterEntityRepository,
    private val propertyRepo: PropertyEntityRepository,
    private val addressRepo: AddressEntityRepository,
    private val meterPointRepo: MeterPointsEntityRepository,
    private val meterPointElectricityRepo: MeterPointElectricityEntityRepository,
    private val meterPointGasRepo: MeterPointGasEntityRepository,
    private val estimatedUsageRepo: EstimatedUsageEntityRepository,
    private val meterPointHistoryRepo: MeterPointHistoryEntityRepository,
    private val meterReadingRepo: MeterReadingEntityRepository,
    private val meterMeterPointRelRepo: MeterMeterPointRelEntityRepository,
) : SyncService {

    override fun syncAssetEntity(request: AssetEntityRequest): SyncResponse {
        validateAssetEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(assetRepo.createAssetEntity(request.assetEntity))
            OperationType.DELETE -> toSyncResponse(assetRepo.deleteAssetEntity(request.assetEntity.id.value))
            OperationType.PATCH -> toSyncResponse(assetRepo.patchAssetEntity(request.assetEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncRegisterEntity(request: RegisterEntityRequest): SyncResponse {
        validateRegisterEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(registerRepo.createRegisterEntity(request.registerEntity))
            OperationType.DELETE -> toSyncResponse(registerRepo.deleteRegisterEntity(request.registerEntity.id.value))
            OperationType.PATCH -> toSyncResponse(registerRepo.patchRegisterEntity(request.registerEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterEntity(request: MeterEntityRequest): SyncResponse {
        validateMeterEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(meterRepo.createMeterEntity(request.meterEntity))
            OperationType.DELETE -> toSyncResponse(meterRepo.deleteMeterEntity(request.meterEntity.id.value))
            OperationType.PATCH -> toSyncResponse(meterRepo.patchMeterEntity(request.meterEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterReadEntity(request: MeterReadingEntityRequest): SyncResponse {
        validateMeterReadEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                meterReadingRepo.createMeterReadingEntity(request.meterReadingEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                meterReadingRepo.deleteMeterReadingEntity(request.meterReadingEntity.id.value)
            )

            OperationType.PATCH -> toSyncResponse(meterReadingRepo.patchMeterReadingEntity(request.meterReadingEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncPropertyEntity(request: PropertyEntityRequest): SyncResponse {
        validatePropertyEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(propertyRepo.createPropertyEntity(request.propertyEntity))
            OperationType.DELETE -> toSyncResponse(propertyRepo.deletePropertyEntity(request.propertyEntity.id.value))
            OperationType.PATCH -> toSyncResponse(propertyRepo.patchPropertyEntity(request.propertyEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncAddressEntity(request: AddressEntityRequest): SyncResponse {
        validateAddressEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(addressRepo.createAddressEntity(request.addressEntity))
            OperationType.DELETE -> toSyncResponse(addressRepo.deleteAddressEntity(request.addressEntity.id.value))
            OperationType.PATCH -> toSyncResponse(addressRepo.patchAddressEntity(request.addressEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterPointEntity(request: MeterPointEntityRequest): SyncResponse {
        validateMeterPointEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(meterPointRepo.createMeterPointEntity(request.meterPointEntity))
            OperationType.DELETE -> toSyncResponse(
                meterPointRepo.deleteMeterPointEntity(request.meterPointEntity.id.value)
            )

            OperationType.PATCH -> toSyncResponse(meterPointRepo.patchMeterPointEntity(request.meterPointEntity))
            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterPointHistoryEntity(request: MeterPointHistoryEntityRequest): SyncResponse {
        validateMeterPointHistoryEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                meterPointHistoryRepo.createMeterPointHistoryEntity(request.meterPointHistoryEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                meterPointHistoryRepo.deleteMeterPointHistoryEntity(request.meterPointHistoryEntity.id.value)
            )

            OperationType.PATCH -> toSyncResponse(
                meterPointHistoryRepo.patchMeterPointHistoryEntity(request.meterPointHistoryEntity)
            )

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncEstimatedUsageEntity(request: EstimatedUsageEntityRequest): SyncResponse {
        validateEstimatedUsageEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                estimatedUsageRepo.createEstimatedUsageEntity(request.estimatedUsageEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                estimatedUsageRepo.deleteEstimatedUsageEntity(request.estimatedUsageEntity.id.value)
            )

            OperationType.PATCH -> toSyncResponse(
                estimatedUsageRepo.patchEstimatedUsageEntity(request.estimatedUsageEntity)
            )

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterMeterPointRelEntity(request: MeterMeterPointRelEntityRequest): SyncResponse {
        validateMeterMeterPointRelEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                meterMeterPointRelRepo.createMeterMeterPointRelEntity(request.meterMeterPointRelEntity)
            )

            OperationType.PATCH -> toSyncResponse(
                meterMeterPointRelRepo.patchMeterMeterPointRelEntity(request.meterMeterPointRelEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                meterMeterPointRelRepo.deleteMeterMeterPointRelEntity(request.meterMeterPointRelEntity.id.value)
            )

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterPointElectricityEntity(request: MeterPointElectricityEntityRequest): SyncResponse {
        validateMeterPointElectricityEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                meterPointElectricityRepo.createMeterPointElectricityEntity(request.meterPointElectricityEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                meterPointElectricityRepo.deleteMeterPointElectricityEntity(
                    request.meterPointElectricityEntity.id.value
                )
            )

            OperationType.PATCH -> toSyncResponse(
                meterPointElectricityRepo.patchMeterPointElectricityEntity(request.meterPointElectricityEntity)
            )

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    override fun syncMeterPointGasEntity(request: MeterPointGasEntityRequest): SyncResponse {
        validateMeterPointGasEntity(request)
        return when (request.operationType) {
            OperationType.CREATE -> toSyncResponse(
                meterPointGasRepo.createMeterPointGasEntity(request.meterPointGasEntity)
            )

            OperationType.DELETE -> toSyncResponse(
                meterPointGasRepo.deleteMeterPointGasEntity(request.meterPointGasEntity.id.value)
            )

            OperationType.PATCH -> toSyncResponse(
                meterPointGasRepo.patchMeterPointGasEntity(request.meterPointGasEntity)
            )

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterMeterPointRelEntity(request: MeterMeterPointRelEntityRequest) {
        when (request.operationType) {
            OperationType.PATCH, OperationType.CREATE -> {
                if (!request.meterMeterPointRelEntity.hasMeterPointId() || request.meterMeterPointRelEntity.meterPointId.hasNull() ||
                    !request.meterMeterPointRelEntity.hasMeterId() || request.meterMeterPointRelEntity.meterId.hasNull() ||
                    !request.meterMeterPointRelEntity.hasRegisterId() || request.meterMeterPointRelEntity.registerId.hasNull() ||
                    !request.meterMeterPointRelEntity.hasFromDttm() || request.meterMeterPointRelEntity.fromDttm.hasNull() ||
                    !request.meterMeterPointRelEntity.hasSyncTransactionId() || request.meterMeterPointRelEntity.syncTransactionId.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterMeterPointRelEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on meterMeterPointRel entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterMeterPointRelEntity.hasId() || request.meterMeterPointRelEntity.id.hasNull()) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterMeterPointRelEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on meterMeterPointRel entity, missing mandatory fields"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateAssetEntity(request: AssetEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.assetEntity.hasType() || request.assetEntity.type.hasNull() ||
                    !request.assetEntity.hasSyncTransactionId() || request.assetEntity.syncTransactionId.hasNull()
                ) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on asset entity, missing mandatory field"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.assetEntity.hasId() || request.assetEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on asset entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.assetEntity.hasId() || request.assetEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on asset entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterEntity(request: MeterEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.meterEntity.hasType() || request.meterEntity.type.hasNull() ||
                    !request.meterEntity.hasIdentifier() || request.meterEntity.identifier.hasNull() ||
                    !request.meterEntity.hasSyncTransactionId() || request.meterEntity.syncTransactionId.hasNull() ||
                    !request.meterEntity.hasAssetId() || request.meterEntity.assetId.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on meter entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterEntity.hasId() || request.meterEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on meter entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.meterEntity.hasId() || request.meterEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on meter entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateRegisterEntity(request: RegisterEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.registerEntity.hasMeterId() || request.registerEntity.meterId.hasNull() ||
                    !request.registerEntity.hasSyncTransactionId() || request.registerEntity.syncTransactionId.hasNull() ||
                    !request.registerEntity.hasDigits() || request.registerEntity.digits.hasNull() ||
                    !request.registerEntity.hasDecimalPlaces() || request.registerEntity.decimalPlaces.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on RegisterEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on register entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.registerEntity.hasId() || request.registerEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on register entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.registerEntity.hasId() || request.registerEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on register entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validatePropertyEntity(request: PropertyEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.propertyEntity.hasType() || request.propertyEntity.type.hasNull() ||
                    !request.propertyEntity.hasAddressId() || request.propertyEntity.addressId.hasNull() ||
                    !request.propertyEntity.hasSyncTransactionId() || request.propertyEntity.syncTransactionId.hasNull() ||
                    !request.propertyEntity.hasType() || request.propertyEntity.type.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on PropertyEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on property entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.propertyEntity.hasId() || request.propertyEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on property entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.propertyEntity.hasId() || request.propertyEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on property entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateAddressEntity(request: AddressEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.addressEntity.hasType() || request.addressEntity.type.hasNull() ||
                    !request.addressEntity.hasSyncTransactionId() || request.addressEntity.syncTransactionId.hasNull()
                ) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on Address entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.addressEntity.hasId() || request.addressEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on Address entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.addressEntity.hasId() || request.addressEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on Address entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterPointEntity(request: MeterPointEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.meterPointEntity.hasType() || request.meterPointEntity.type.hasNull() ||
                    !request.meterPointEntity.hasSyncTransactionId() || request.meterPointEntity.syncTransactionId.hasNull() ||
                    !request.meterPointEntity.hasChangeOfTenancyFl() || request.meterPointEntity.changeOfTenancyFl.hasNull() ||
                    !request.meterPointEntity.hasIdentifier() || request.meterPointEntity.identifier.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterPointEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPoint entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterPointEntity.hasId() || request.meterPointEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPoint entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.meterPointEntity.hasId() || request.meterPointEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on property entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterPointElectricityEntity(request: MeterPointElectricityEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (
                    !request.meterPointElectricityEntity.hasSyncTransactionId() || request.meterPointElectricityEntity.syncTransactionId.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterPointElectricityEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointElectricity entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterPointElectricityEntity.hasId() || request.meterPointElectricityEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointElectricity entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.meterPointElectricityEntity.hasId() || request.meterPointElectricityEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointElectricity entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterPointGasEntity(request: MeterPointGasEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.meterPointGasEntity.hasSyncTransactionId() || request.meterPointGasEntity.syncTransactionId.hasNull()) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterPointGasEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointGas entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterPointGasEntity.hasId() || request.meterPointGasEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointGas entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.meterPointGasEntity.hasId() || request.meterPointGasEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointGas entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterPointHistoryEntity(request: MeterPointHistoryEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.meterPointHistoryEntity.hasMeterPointId() || request.meterPointHistoryEntity.meterPointId.hasNull() ||
                    !request.meterPointHistoryEntity.hasSyncTransactionId() || request.meterPointHistoryEntity.syncTransactionId.hasNull() ||
                    !request.meterPointHistoryEntity.hasEventType() || request.meterPointHistoryEntity.eventType.hasNull() ||
                    !request.meterPointHistoryEntity.hasStatus() || request.meterPointHistoryEntity.status.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on MeterPointHistoryEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointHistory entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.meterPointHistoryEntity.hasId() || request.meterPointHistoryEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointHistory entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.meterPointHistoryEntity.hasId() || request.meterPointHistoryEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterPointHistory entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateEstimatedUsageEntity(request: EstimatedUsageEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.estimatedUsageEntity.hasMeterPointId() || request.estimatedUsageEntity.meterPointId.hasNull() ||
                    !request.estimatedUsageEntity.hasSyncTransactionId() || request.estimatedUsageEntity.syncTransactionId.hasNull() ||
                    !request.estimatedUsageEntity.hasUsageType() || request.estimatedUsageEntity.usageType.hasNull() ||
                    !request.estimatedUsageEntity.hasUnits() || request.estimatedUsageEntity.units.hasNull() ||
                    !request.estimatedUsageEntity.hasUsage() || request.estimatedUsageEntity.usage.hasNull() ||
                    !request.estimatedUsageEntity.hasRateName() || request.estimatedUsageEntity.rateName.hasNull()
                ) {
                    logger.error(
                        "Validation failed for operation ${request.operationType} on EstimatedUsageEntityRequest [$request], missing mandatory fields"
                    )
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on EstimatedUsage entity, missing mandatory fields"
                    )
                }
            }

            OperationType.DELETE -> {
                if (!request.estimatedUsageEntity.hasId() || request.estimatedUsageEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on EstimatedUsage entity, missing ID field"
                    )
                }
            }

            OperationType.PATCH -> {
                if (!request.estimatedUsageEntity.hasId() || request.estimatedUsageEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on EstimatedUsage entity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }

    private fun validateMeterReadEntity(request: MeterReadingEntityRequest) {
        when (request.operationType) {
            OperationType.CREATE -> {
                if (!request.meterReadingEntity.hasMeterPointId() || request.meterReadingEntity.meterPointId.hasNull() ||
                    !request.meterReadingEntity.hasSyncTransactionId() || request.meterReadingEntity.syncTransactionId.hasNull()
                ) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing meterPointId field"
                    )
                }
                if (!request.meterReadingEntity.hasMeterId() || request.meterReadingEntity.meterId.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing meter field"
                    )
                }
                if (!request.meterReadingEntity.hasRegisterId() || request.meterReadingEntity.registerId.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing registerId field"
                    )
                }
                if (!request.meterReadingEntity.hasReadingDttm() || request.meterReadingEntity.readingDttm.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing readingDttm field"
                    )
                }
                if (!request.meterReadingEntity.hasStatus() || request.meterReadingEntity.status.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing status field"
                    )
                }
                if (!request.meterReadingEntity.hasSequenceType() || request.meterReadingEntity.sequenceType.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing sequenceType field"
                    )
                }
                if (!request.meterReadingEntity.hasSource() || request.meterReadingEntity.source.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing source field"
                    )
                }
                if (!request.meterReadingEntity.hasQuality() || request.meterReadingEntity.quality.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing quality field"
                    )
                }
                if (!request.meterReadingEntity.hasCumulative() || request.meterReadingEntity.cumulative.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing cumulative field"
                    )
                }
                if (!request.meterReadingEntity.hasConsumption() || request.meterReadingEntity.consumption.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing consumption field"
                    )
                }
                if (!request.meterReadingEntity.hasUnit() || request.meterReadingEntity.unit.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing unit field"
                    )
                }

                searchEnum<MeterReadingStatu>(request.meterReadingEntity.status.value)
                searchEnum<MeterReadingSequenceType>(request.meterReadingEntity.sequenceType.value)
                searchEnum<MeterReadingSource>(request.meterReadingEntity.source.value)
                searchEnum<MeterReadingQuality>(request.meterReadingEntity.quality.value)
                searchEnum<MeterReadingUnit>(request.meterReadingEntity.unit.value)
                if (request.meterReadingEntity.hasWorkflowStatus() && !request.meterReadingEntity.workflowStatus.hasNull()) {
                    searchEnum<MeterReadingWorkflowStatu>(request.meterReadingEntity.workflowStatus.value)
                }
            }

            OperationType.PATCH -> {
                if (!request.meterReadingEntity.hasId() || request.meterReadingEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing ID field"
                    )
                }

                if (request.meterReadingEntity.hasStatus() && !request.meterReadingEntity.status.hasNull()) {
                    searchEnum<MeterReadingStatu>(request.meterReadingEntity.status.value)
                }
                if (request.meterReadingEntity.hasSequenceType() && !request.meterReadingEntity.sequenceType.hasNull()) {
                    searchEnum<MeterReadingSequenceType>(request.meterReadingEntity.sequenceType.value)
                }
                if (request.meterReadingEntity.hasSource() && !request.meterReadingEntity.source.hasNull()) {
                    searchEnum<MeterReadingSource>(request.meterReadingEntity.source.value)
                }
                if (request.meterReadingEntity.hasQuality() && !request.meterReadingEntity.quality.hasNull()) {
                    searchEnum<MeterReadingQuality>(request.meterReadingEntity.quality.value)
                }
                if (request.meterReadingEntity.hasUnit() && !request.meterReadingEntity.unit.hasNull()) {
                    searchEnum<MeterReadingUnit>(request.meterReadingEntity.unit.value)
                }
                if (request.meterReadingEntity.hasWorkflowStatus() && !request.meterReadingEntity.workflowStatus.hasNull()) {
                    searchEnum<MeterReadingWorkflowStatu>(request.meterReadingEntity.workflowStatus.value)
                }
            }

            OperationType.DELETE -> {
                if (!request.meterReadingEntity.hasId() || request.meterReadingEntity.id.hasNull()) {
                    throw SyncValidationFailedException(
                        request.operationType,
                        "Validation failed for operation ${request.operationType} on MeterReadingEntity, missing ID field"
                    )
                }
            }

            else -> throw UnsupportedOperationException("Unsupported Sync Operation: ${request.operationType}")
        }
    }
}

inline fun <reified E : Enum<E>> searchEnum(search: String): E {
    val regex = "\\s+".toRegex()
    val trimmed = search.trim()

    val searchedEnum = regex.replace(trimmed, "-")
    val searchedEnum2 = regex.replace(trimmed, "_")

    val found = E::class.java.enumConstants.firstOrNull {
        it.name.equals(searchedEnum, true) ||
            it.name.equals(searchedEnum2, true)
    }
    if (found != null) {
        return found
    }
    throw IllegalStateException("Cannot find an enum constant $search in ${E::class.java}")
}

inline fun <reified E : Enum<E>> getEnumOrNullWhenEmpty(search: String?): E? {
    if (search.isNullOrBlank()) {
        return null
    }
    return searchEnum<E>(search)
}
