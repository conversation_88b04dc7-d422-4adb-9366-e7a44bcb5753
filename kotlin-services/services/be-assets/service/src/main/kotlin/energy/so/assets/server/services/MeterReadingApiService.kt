package energy.so.assets.server.services

import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import java.time.LocalDate
import java.time.LocalDateTime

interface MeterReadingApiService {
    /**
     * Search meter readings
     *
     * @param searchRequest the filters to search by
     * @return container of readings found
     */
    suspend fun search(searchRequest: MeterReadingsSearchRequest): List<energy.so.assets.server.models.MeterReading>

    /**
     * Get the latest electricity meter reading
     * @param meterPointId Long
     * @return MeterReading?
     */
    suspend fun getLastElectricityMeterReading(meterPointId: Long): energy.so.assets.server.models.MeterReading?

    /**
     * Get the first electricity meter reading
     * @param meterPointId Long
     * @return MeterReading?
     */
    suspend fun getFirstElectricityMeterReading(meterPointId: Long): energy.so.assets.server.models.MeterReading?

    /**
     * Get electricity meter readings by date
     * @param meterPointId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @return List<MeterReading>
     */
    suspend fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<energy.so.assets.server.models.MeterReading>

    /**
     * Check if there is at least one reading for a given list of meters and not in readingDates
     * @param meterIds: List<Long>
     * @param readingDates: List<LocalDate>
     * @return Boolean
     */
    suspend fun existsForMeterIdsAndSubmissionProcessed(meterIds: List<Long>, readingDates: List<LocalDate>): Boolean

    /**
     * Check if there is at least one reading associated with given meterpoint
     * @param meterPointId: Long
     * @return Boolean
     */
    suspend fun existsForMeterPointIdInLastYear(meterPointId: Long): Boolean
}
