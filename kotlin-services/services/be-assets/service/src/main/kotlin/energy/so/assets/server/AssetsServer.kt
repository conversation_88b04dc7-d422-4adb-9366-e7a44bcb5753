package energy.so.assets.server

import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.EnvironmentConfig
import energy.so.assets.server.config.OnDemandConfig
import energy.so.assets.server.consumers.AutomaticCloseOutboundRequestsConsumer
import energy.so.assets.server.consumers.MeterReadSubmissionRequestConsumer
import energy.so.assets.server.consumers.MeterReadingStatusUpdateConsumer
import energy.so.assets.server.consumers.MeterReadingSubmissionConsumer
import energy.so.assets.server.consumers.OutboundPhotoRequestConsumer
import energy.so.assets.server.consumers.PersistedMeterReadingSubmissionRequestConsumer
import energy.so.assets.server.consumers.PhotoEvidenceFreshdeskOperationConsumer
import energy.so.assets.server.consumers.UpdateMeterReadSubmissionStatusConsumer
import energy.so.assets.server.controllers.AddressController
import energy.so.assets.server.controllers.AssetController
import energy.so.assets.server.controllers.AssetsAccountStatesController
import energy.so.assets.server.controllers.MeterController
import energy.so.assets.server.controllers.MeterPointController
import energy.so.assets.server.controllers.MeterReadingsController
import energy.so.assets.server.controllers.PostcodeGspController
import energy.so.assets.server.controllers.PropertiesController
import energy.so.assets.server.controllers.RegisterController
import energy.so.assets.server.controllers.SmartMeterController
import energy.so.assets.server.modules.AccountStatesModule
import energy.so.assets.server.modules.AddressModule
import energy.so.assets.server.modules.AssetsModule
import energy.so.assets.server.modules.CommonModule
import energy.so.assets.server.modules.EnergyUsagesModule
import energy.so.assets.server.modules.EntityMappingModule
import energy.so.assets.server.modules.GoogleCloudModule
import energy.so.assets.server.modules.MeterPointsModule
import energy.so.assets.server.modules.MeterReadingPhotoEvidenceModule
import energy.so.assets.server.modules.MeterReadingsModule
import energy.so.assets.server.modules.MetersModule
import energy.so.assets.server.modules.OutboundPhotoRequestModule
import energy.so.assets.server.modules.PostcodeGspModule
import energy.so.assets.server.modules.PropertiesModule
import energy.so.assets.server.modules.RegistersModule
import energy.so.assets.server.modules.SmartMetersModule
import energy.so.assets.sync.controller.SyncController
import energy.so.commons.database.ConnectionValidator.databaseReadinessCheck
import energy.so.commons.grpc.server.GrpcServer
import energy.so.commons.grpc.service.HealthService
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import org.koin.core.component.KoinComponent
import org.koin.core.context.GlobalContext
import org.koin.core.context.startKoin
import org.koin.ksp.generated.module

class AssetsGrpcServer(
    private val meterReadingsController: MeterReadingsController,
    private val meterPointController: MeterPointController,
    private val propertiesController: PropertiesController,
    private val postcodeGspController: PostcodeGspController,
    private val meterController: MeterController,
    private val assetController: AssetController,
    private val addressController: AddressController,
    private val syncController: SyncController,
    private val registerController: RegisterController,
    private val smartMeterController: SmartMeterController,
    private val accountStatesController: AssetsAccountStatesController,
    private val meterReadingSubmissionConsumer: MeterReadingSubmissionConsumer,
    private val meterReadSubmissionRequestConsumer: MeterReadSubmissionRequestConsumer,
    private val updateMeterReadSubmissionStatusConsumer: UpdateMeterReadSubmissionStatusConsumer,
    private val persistedMeterReadingSubmissionRequestConsumer: PersistedMeterReadingSubmissionRequestConsumer,
    private val photoEvidenceFreshdeskOperationConsumer: PhotoEvidenceFreshdeskOperationConsumer,
    private val meterReadingStatusUpdateConsumer: MeterReadingStatusUpdateConsumer,
    private val outboundPhotoRequestConsumer: OutboundPhotoRequestConsumer,
    private val automaticCloseOutboundRequestsConsumer: AutomaticCloseOutboundRequestsConsumer,
) : KoinComponent {

    private val dslContext = GlobalContext.get().get<DSLContext>()

    fun start(port: Int) {
        meterReadingSubmissionConsumer.subscribe()
        meterReadSubmissionRequestConsumer.subscribe()
        persistedMeterReadingSubmissionRequestConsumer.subscribe()
        updateMeterReadSubmissionStatusConsumer.subscribe()
        photoEvidenceFreshdeskOperationConsumer.subscribe()
        meterReadingStatusUpdateConsumer.subscribe()
        outboundPhotoRequestConsumer.subscribe()
        automaticCloseOutboundRequestsConsumer.subscribe()

        GrpcServer.services(
            meterReadingsController,
            meterPointController,
            propertiesController,
            postcodeGspController,
            meterController,
            assetController,
            addressController,
            syncController,
            registerController,
            smartMeterController,
            accountStatesController,
            HealthService(readinessChecks = setOf(databaseReadinessCheck(dslContext))),
        )

        // Initialise jooq
        dslContext.selectOne().fetch()
        val onDemandConfig = Constants.get<OnDemandConfig>("on-demand")
        GrpcServer.startServer(port, onDemandConfig.onDemandBranchName)
    }
}

// Define all modules for the server here
val koinModules = listOf(
    CommonModule.module,
    MeterPointsModule.module,
    MetersModule.module,
    PropertiesModule.module,
    MeterReadingsModule.manualModule,
    MeterReadingsModule.module,
    EnergyUsagesModule.module,
    PostcodeGspModule.module,
    AssetsModule.module,
    AddressModule.module,
    RegistersModule.module,
    EntityMappingModule.module,
    SmartMetersModule.module,
    GoogleCloudModule.module,
    MeterReadingPhotoEvidenceModule.module,
    OutboundPhotoRequestModule.module,
    AccountStatesModule.module
)

object AssetsServer {

    @JvmStatic
    fun main(args: Array<String>): Unit = runBlocking {
        val environmentConfig = Constants.get<EnvironmentConfig>("environment")

        startKoin {
            modules(*koinModules.toTypedArray())
        }.koin.get<AssetsGrpcServer>()
            .start(environmentConfig.port)
    }
}
