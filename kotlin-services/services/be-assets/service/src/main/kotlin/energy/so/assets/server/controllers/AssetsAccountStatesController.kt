package energy.so.assets.server.controllers

import energy.so.assets.accountStates.v2.AssetsAccountState
import energy.so.assets.accountStates.v2.GetAssetsAccountStateRequest
import energy.so.assets.accountStates.v2.GetAssetsAccountStateResponse
import energy.so.assets.accountStates.v2.getAssetsAccountStateResponse
import energy.so.assets.server.services.accountState.OpeningReadingStateService
import energy.so.assets.server.services.accountState.ReadStateService
import energy.so.assets.server.services.accountState.SupplyStateService
import energy.so.assets.v2.accountStates.AssetsAccountStatesServiceGrpcKt
import energy.so.commons.grpc.extensions.toSiblingEnum
import io.opentelemetry.instrumentation.annotations.WithSpan

class AssetsAccountStatesController(
    private val openingReadingStateService: OpeningReadingStateService,
    private val supplyStateService: SupplyStateService,
    private val readStateService: ReadStateService,
) : AssetsAccountStatesServiceGrpcKt.AssetsAccountStatesServiceCoroutineImplBase() {

    @WithSpan
    override suspend fun getAccountStates(request: GetAssetsAccountStateRequest): GetAssetsAccountStateResponse {
        return getAssetsAccountStateResponse {
            if (request.accountStateList.contains(AssetsAccountState.OPENING_METER_READING)) {
                openingMeterReadingState =
                    openingReadingStateService.computeState(request.accountId).toSiblingEnum()
            }
            if (request.accountStateList.contains(AssetsAccountState.SUPPLY)) {
                supplyState = supplyStateService.computeState(request.accountId).toSiblingEnum()
            }
            if (request.accountStateList.contains(AssetsAccountState.READ)) {
                readState = readStateService.computeState(request.accountId).toSiblingEnum()
            }
        }
    }
}
