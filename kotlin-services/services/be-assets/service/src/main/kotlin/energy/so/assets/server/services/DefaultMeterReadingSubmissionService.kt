package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.MeterPointStructure
import energy.so.ac.junifer.v1.assets.meterPointStructureRequest
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.config.CommunicationTemplate
import energy.so.assets.server.config.Constants
import energy.so.assets.server.database.repositories.MeterReadingsRepository
import energy.so.assets.server.database.repositories.MeterRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.mapping.EntityIdentifier
import energy.so.assets.server.mapping.JuniferEntityMapper
import energy.so.assets.server.models.AccountMeterReadingsRequestDto
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithMTD
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithoutMTD
import energy.so.assets.server.models.Agreement
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingQuality
import energy.so.assets.server.models.MeterReadingSource
import energy.so.assets.server.models.MeterReadingSubmissionIdFilter
import energy.so.assets.server.models.MeterReadings
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.assets.server.models.ProductType
import energy.so.assets.server.models.Read
import energy.so.assets.server.models.ReadingType
import energy.so.assets.server.models.SequenceType
import energy.so.assets.server.models.SubmitMeterReadingError
import energy.so.assets.server.models.SubmitMeterReadingsErrorWithRegisterId
import energy.so.assets.server.models.SubmitMeterReadingsErrorWithoutRegisterId
import energy.so.assets.server.models.SubmitMeterReadingsResult
import energy.so.assets.server.models.SubmitMeterReadingsResultWithRegisterId
import energy.so.assets.server.models.SubmitMeterReadingsResultWithoutRegisterId
import energy.so.assets.server.models.UnitType
import energy.so.assets.server.models.fuelType
import energy.so.assets.server.models.getActiveAgreements
import energy.so.assets.server.services.DefaultMeterReadingSubmissionRequestService.Companion.METER_READING_SUBMISSION_REQUEST_PENDING_STATUSES
import energy.so.assets.server.validators.MeterReadBoundValidator
import energy.so.assets.server.validators.MeterReadingError
import energy.so.assets.server.validators.MeterReadingoOutOfBoundsError
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.search.pagination
import energy.so.commons.validations.validator.Validator
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.communications.v1.dtos.RecipientDto
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import energy.so.users.client.v2.UsersClient
import energy.so.users.v2.FeatureName
import energy.so.users.v2.UserResponse
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

private val logger = TraceableLogging.logger {}

class DefaultMeterReadingSubmissionService(
    private val meterReadingsService: MeterReadingsService,
    private val meterReadingsRepository: MeterReadingsRepository,
    private val meterRepository: MeterRepository,
    private val registerRepository: RegisterRepository,
    private val usersClient: UsersClient,
    private val customersClient: CustomersClient,
    private val billingAccountsClient: BillingAccountsClient,
    private val productAccountsClient: ProductAccountsClient,
    private val communicationsClient: CommunicationClient,
    private val assetsClient: AssetsClient,
    private val juniferEntityMapper: JuniferEntityMapper,
    private val meterReadingApiService: MeterReadingApiService,
    private val meterReadBoundValidator: MeterReadBoundValidator,
    private val meterReadingSubmissionRequestService: MeterReadingSubmissionRequestService,
    private val featureService: FeatureService,
    private val validator: Validator<MeterReadingError, MeterReadingsRequestDto>,
) : MeterReadingSubmissionService {

    companion object {
        const val METER_READINGS_PAGINATION_SIZE = 10000
        const val GAS = "Gas"
        const val ELECTRICITY = "Electricity"
        const val ACCOUNT_NUMBER = "account_number"
        const val EMAIL = "email"
        const val FIRST_NAME = "first_name"
        const val FUEL = "fuel"
        const val HAS_MULTIPLE_SUBMISSIONS = "multiple"
        const val HAS_BOTH_FUELS = "has_both_fuels"
        const val SUBMISSIONS = "submissions"
        val communicationTemplate = Constants.get<CommunicationTemplate>("communication-template")
    }

    override suspend fun submitMeterReadings(meterReadings: MeterReadingsRequestDto): SubmitMeterReadingsResult {
        val processedReadings = computeSequenceType(meterReadings)
        val response = meterReadingsService.submitMeterReadingToJunifer(processedReadings)
        logger.debug { "[::submitMeterReadings  meterPointId:${meterReadings.meterPointId}]" }
        logger.debug { "[::submitMeterReadings] Submit meter reading result: $response" }
        try {
            meterReadingsService.createMeterReading(processedReadings, response)
        } catch (exp: Exception) {
            logger.error(exp) {
                "[::submitMeterReadings] Unable to save meter readings in DB for meterPointId ${processedReadings.meterPointId}"
            }
            return response
        }

        handleSubmissions(response, processedReadings.userId!!)

        return response
    }

    override suspend fun submitAccountMeterReadings(
        accountMeterReadings: List<AccountMeterReadingsRequestDtoWithMTD>,
        ignoreMeterWarnings: Boolean,
    ): SubmitMeterReadingsResultWithRegisterId {
        val meterReadingRequestDto = accountMeterReadings.map {
            val meterIdentifier = meterRepository.findById(it.meterId)?.identifier
                ?: return unsuccessfulSubmitWithRegisterId(it.registerId, "No meter identifier for meter id")
            val register = registerRepository.getById(it.registerId)
                ?: return unsuccessfulSubmitWithRegisterId(it.registerId, "No register for register id")

            MeterReadings(
                registerId = register.id,
                meterIdentifier = meterIdentifier,
                registerIdentifier = register.identifier,
                readingDateTime = it.readingDate.atStartOfDay(),
                source = "Customer",
                quality = "NORMAL",
                cumulative = it.value,
                unitType = it.unitType
            )
        }.let {
            MeterReadingsWithTechnicalDetailsDto(
                meterPointId = accountMeterReadings[0].meterPointId,
                userId = accountMeterReadings[0].userId,
                billingAccountId = accountMeterReadings[0].billingAccountId,
                ignoreWarnings = ignoreMeterWarnings,
                readings = it
            )
        }

        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_25244_ASYNC_NEW_SUBMIT_METER_READINGS)) {
            val errorValidations = validateAccountReadingsV2(meterReadingRequestDto)
            val response = if (errorValidations != null) {
                errorValidations
            } else {
                meterReadingSubmissionRequestService.processMeterReadingsAsyncSubmissions(meterReadingRequestDto)
                createSuccessReadingResult(meterReadingRequestDto)
            }
            return getAccountMeterResult(response, meterReadingRequestDto)
        } else {
            val processedSubmission = computeSequenceType(meterReadingRequestDto)
            val errors = validateAccountReadings(accountMeterReadings)

            val response = if (errors.isEmpty()) {
                meterReadingsService.submitMeterReadingToJunifer(processedSubmission)
            } else {
                SubmitMeterReadingsResult(
                    processedSubmission.meterPointId,
                    OffsetDateTime.now(ZoneOffset.UTC),
                    false,
                    errors
                )
            }
            val submitAccountMeterResultFromErrors =
                getAccountMeterResult(response, processedSubmission as MeterReadingsWithTechnicalDetailsDto)
            logger.debug { "[::submitAccountMeterReadings  meterPointId:${meterReadingRequestDto.meterPointId}]" }
            logger.debug { "[::submitAccountMeterReadings] Submit meter reading result: $response" }
            try {
                meterReadingsService.createMeterReading(processedSubmission, response)
            } catch (exp: Exception) {
                logger.error(exp) {
                    "[::submitAccountMeterReadings] Unable to save meter readings in DB for meterPointId ${meterReadingRequestDto.meterPointId}"
                }
                return submitAccountMeterResultFromErrors
            }
            handleSubmissions(response, processedSubmission.userId!!)

            return submitAccountMeterResultFromErrors
        }
    }

    override suspend fun submitAccountMeterReadingsWithoutMTD(
        accountMeterReadings: List<AccountMeterReadingsRequestDtoWithoutMTD>,
        ignoreMeterWarnings: Boolean,
    ): SubmitMeterReadingsResultWithoutRegisterId {
        val meterReadingRequestDto = accountMeterReadings.map {
            Read(
                reading = it.value,
                readingType = when {
                    it.rateName.equals(ReadingType.Day.name, ignoreCase = true) -> ReadingType.Day
                    it.rateName.equals(ReadingType.Night.name, ignoreCase = true) -> ReadingType.Night
                    else -> ReadingType.Standard
                },
                unitType = it.unitType
            )
        }.let {
            val meterpointHasPreviousReadings = meterReadingsService.searchMeterReadings(
                meterReadingsSearchRequest {
                    meterPointId.add(accountMeterReadings[0].meterPointId)
                    pagination = pagination {
                        pageNumber = 1
                        pageSize = 1
                    }
                    enableAllHistoricalReadings = true
                    includeCancelledAndDeletedAgreements = true
                }
            ).isNotEmpty()

            MeterReadingsWithoutTechnicalDetailsDto(
                meterPointId = accountMeterReadings[0].meterPointId,
                userId = accountMeterReadings[0].userId,
                billingAccountId = accountMeterReadings[0].billingAccountId,
                ignoreWarnings = ignoreMeterWarnings,
                registerReads = it,
                readingDate = accountMeterReadings[0].readingDate,
                quality = MeterReadingQuality.NORMAL.name,
                sequenceType = if (meterpointHasPreviousReadings) SequenceType.Normal else SequenceType.First,
                source = MeterReadingSource.Customer.name,
            )
        }

        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_25244_ASYNC_NEW_SUBMIT_METER_READINGS)) {
            val errorValidations = validateAccountReadingsV2(meterReadingRequestDto)
            val response = if (errorValidations != null) {
                errorValidations
            } else {
                meterReadingSubmissionRequestService.processMeterReadingsAsyncSubmissions(meterReadingRequestDto)
                createSuccessReadingResult(meterReadingRequestDto)
            }
            return getAccountMeterResult(response)
        } else {
            val processedSubmission = computeSequenceType(meterReadingRequestDto)

            val errors = validateAccountReadings(accountMeterReadings)

            val response = if (errors.isEmpty()) {
                meterReadingsService.submitMeterReadingToJunifer(processedSubmission)
            } else {
                SubmitMeterReadingsResult(
                    processedSubmission.meterPointId,
                    OffsetDateTime.now(ZoneOffset.UTC),
                    false,
                    errors
                )
            }
            val submitAccountMeterResultFromErrors = getAccountMeterResult(response)
            logger.debug { "[::submitAccountMeterReadingsWithoutMTD  meterPointId:${meterReadingRequestDto.meterPointId}]" }
            logger.debug { "[::submitAccountMeterReadingsWithoutMTD] Submit meter reading result: $response" }
            try {
                meterReadingsService.createMeterReading(processedSubmission, response)
            } catch (exp: Exception) {
                logger.error(exp) {
                    "[::submitAccountMeterReadingsWithoutMTD] Unable to save meter readings in DB for meterPointId ${meterReadingRequestDto.meterPointId}"
                }
                return submitAccountMeterResultFromErrors
            }

            handleSubmissions(response, processedSubmission.userId!!)

            return submitAccountMeterResultFromErrors
        }
    }

    override suspend fun submitMeterReadingsSubmissionRequest(id: Long): SubmitMeterReadingsResult {
        logger.debug { "[::submitMeterReadingsSubmissionRequest][meterReadingSubmissionRequestId:$id]" }

        val submissionRequest =
            meterReadingSubmissionRequestService.findMeterReadingSubmissionsByIdFilter(
                MeterReadingSubmissionIdFilter(
                    id = id,
                    statusesList = METER_READING_SUBMISSION_REQUEST_PENDING_STATUSES
                )
            )

        val processedSubmission = computeSequenceType(submissionRequest.submissionData)
        val response = meterReadingsService.submitMeterReadingToJunifer(processedSubmission)
        logger.debug {
            "[::submitMeterReadingsSubmissionRequest  meterPointId:${processedSubmission.meterPointId}]"
        }
        logger.debug { "[::submitMeterReadingsSubmissionRequest] Submit meter reading result: $response" }

        meterReadingSubmissionRequestService.handleJuniferSubmissionResponse(
            submissionRequest,
            response
        )

        try {
            handleSubmissions(response, processedSubmission.userId!!)
        } catch (ex: Exception) {
            logger.error(ex) {
                "[::submitMeterReadings][PostProcessingException] exception when applying post-processing for submissionId ${submissionRequest.id}"
            }
        }

        return response
    }

    private fun createSuccessReadingResult(processedSubmission: MeterReadingsRequestDto): SubmitMeterReadingsResult =
        SubmitMeterReadingsResult(
            processedSubmission.meterPointId,
            submissionTime = OffsetDateTime.now(ZoneOffset.UTC),
            success = true
        )

    private suspend fun computeSequenceType(requestDto: MeterReadingsRequestDto) =
        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_21610_COMPUTE_SEQUENCE_TYPE_NPE_FIX)) {
            meterReadingsService.computeSequenceTypeForReadings(requestDto)
        } else {
            requestDto
        }

    private suspend fun handleSubmissions(response: SubmitMeterReadingsResult, userId: Long) {
        val submissions = meterReadingApiService.search(
            meterReadingsSearchRequest {
                this.meterPointId.add(response.meterPointId)
                pagination = pagination {
                    pageNumber = 0
                    pageSize = METER_READINGS_PAGINATION_SIZE
                }
            }
        )

        logger.debug {
            "[::handleSubmissions][meterpointId: ${response.meterPointId}] submission size: ${submissions.size}. Submissions ids: ${submissions.map { it.id }}."
        }
        if (response.success) {
            handleSubmissionsResponse(
                userId,
                submissions,
                response.readingDates?.map { it.readingDate } ?: listOf(LocalDate.now())
            )
        }

//        markSubmissionsProcessed(submissions) todo(SO-15972): investigate if this logic is needed anymore if we decide to retrieve readings from db instead of junifer api
    }

    private suspend fun handleSubmissionsResponse(
        userId: Long,
        submissions: List<MeterReading>,
        readingDates: List<LocalDate>,
    ) {
        val user = usersClient.getUserById(idRequest { id = userId })

        val productAccountsResponse =
            productAccountsClient.getProductAccountsByBillingAccountId(idRequest { id = user.currentAccountId })
        logger.debug {
            "[::handleSubmissionsResponse][userId: ${user.id}] productAccounts ids: ${productAccountsResponse.productAccountsList.map { it.id }}"
        }

        val activeAgreements = productAccountsResponse.productAccountsList
            .flatMap { it.getActiveAgreements() }
        logger.debug { "[::handleSubmissionsResponse] activeAgreements ids ${activeAgreements.map { it.id }}" }

        val agreementsMap = activeAgreements.groupBy(Agreement::productType).mapValues { it.value.distinct() }
        logger.debug(
            "[::handleSubmissionsResponse][userId: ${user.id}] agreements ids: ${agreementsMap.flatMap { it.value.map { agreement -> agreement.id } }}"
        )

        val electricityMeterPoints = fetchMeterPointStructureFromJunifer(agreementsMap, ProductType.ELECTRICITY)
        val gasMeterPoints = fetchMeterPointStructureFromJunifer(agreementsMap, ProductType.GAS)
        logger.debug {
            "[::handleSubmissionsResponse][userId: ${user.id}] electricityMeterPoints ids ${electricityMeterPoints.map { it.id }}"
        }
        logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] gasMeterPoints ids ${gasMeterPoints.map { it.id }}" }

        val coreElectricityMeterPointIds = electricityMeterPoints
            .map { it.id }
            .map {
                juniferEntityMapper.getCoreId(EntityIdentifier.METER_POINT, it.toString())!!.toLong()
            }
        logger.debug {
            "[::handleSubmissionsResponse][userId: ${user.id}] coreElectricityMeterPointIds is $coreElectricityMeterPointIds"
        }
        val coreGasMeterPointIds = gasMeterPoints
            .map { it.id }
            .map {
                juniferEntityMapper.getCoreId(EntityIdentifier.METER_POINT, it.toString())!!.toLong()
            }
        logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] coreGasMeterPointIds is $coreGasMeterPointIds" }
        val coreElectricityMeterIds =
            electricityMeterPoints
                .flatMap { it.metersList }
                .distinctBy { it.id }
                .map { it.id }
                .map { juniferEntityMapper.getCoreId(EntityIdentifier.METER, it.toString())!!.toLong() }
        logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] coreElectricityMeterIds is $coreElectricityMeterIds" }
        val coreGasMeterIds =
            gasMeterPoints
                .flatMap { it.metersList }
                .distinctBy { it.id }
                .map { it.id }
                .map { juniferEntityMapper.getCoreId(EntityIdentifier.METER, it.toString())!!.toLong() }
        logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] coreGasMeterIds is $coreGasMeterIds" }

        logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] before checking customerHasOpeningReadings" }

        if (!customerHasOpeningReadings(
                agreementsMap,
                coreElectricityMeterIds,
                coreGasMeterIds,
                coreElectricityMeterPointIds,
                coreGasMeterPointIds,
                readingDates
            )
        ) {
            logger.debug { "[::handleSubmissionsResponse][userId: ${user.id}] checking customerHasOpeningReadings" }
            sendOpeningReadingEmail(
                agreementsMap,
                user,
                submissions,
                coreElectricityMeterPointIds,
                coreGasMeterPointIds,
                coreElectricityMeterIds,
                coreGasMeterIds,
            )
        }
    }

    private fun markSubmissionsProcessed(submissions: List<MeterReading>) {
        meterReadingsRepository.markSubmissionsProcessed(submissions.map { it.id!! })
    }

    private suspend fun customerHasOpeningReadings(
        agreementsMap: Map<ProductType, List<Agreement>>,
        electricityMeterIds: List<Long>,
        gasMeterIds: List<Long>,
        electricityMeterPointIds: List<Long>,
        gasMeterPointIds: List<Long>,
        readingDates: List<LocalDate>,
    ): Boolean {
        return hasSubmittedInitialReadings(electricityMeterIds, gasMeterIds, readingDates) ||
                accountHasOpenMeterReadings(
                    agreementsMap,
                    electricityMeterPointIds,
                    gasMeterPointIds,
                    electricityMeterIds,
                    gasMeterIds
                ) ||
                accountHasOpenMeterPointReadings(agreementsMap, electricityMeterPointIds, gasMeterPointIds)
    }

    private suspend fun accountHasOpenMeterReadings(
        agreementsMap: Map<ProductType, List<Agreement>>,
        electricityMeterPointIds: List<Long>,
        gasMeterPointIds: List<Long>,
        electricityMeterIds: List<Long>,
        gasMeterIds: List<Long>,
    ): Boolean {
        return hasOpeningMeterReadings(
            agreementsMap,
            electricityMeterPointIds,
            electricityMeterIds,
            ProductType.ELECTRICITY
        ) &&
                hasOpeningMeterReadings(agreementsMap, gasMeterPointIds, gasMeterIds, ProductType.GAS)
    }

    private suspend fun accountHasOpenMeterPointReadings(
        agreementsMap: Map<ProductType, List<Agreement>>,
        electricityMeterPointIds: List<Long>,
        gasMeterPointIds: List<Long>,
    ): Boolean {
        return hasOpeningMeterPointReadings(agreementsMap, electricityMeterPointIds, ProductType.ELECTRICITY) &&
                hasOpeningMeterPointReadings(agreementsMap, gasMeterPointIds, ProductType.GAS)
    }

    private suspend fun hasOpeningMeterReadings(
        agreementsMap: Map<ProductType, List<Agreement>>,
        meterpointIds: List<Long>,
        meterIds: List<Long>,
        productType: ProductType,
    ): Boolean {
        if (meterIds.isEmpty()) {
            return false
        }

        val readingsInterval: List<LocalDateTime> = getReadingsIntervalForProductType(agreementsMap, productType)

        val readingStartDate = readingsInterval.minOf { it }
        val readingEndDate = readingsInterval.maxOf { it }

        return meterHasFirstReadings(meterpointIds, meterIds, readingStartDate, readingEndDate)
    }

    private suspend fun hasOpeningMeterPointReadings(
        agreementsMap: Map<ProductType, List<Agreement>>,
        meterPointIds: List<Long>,
        productType: ProductType,
    ): Boolean {
        if (meterPointIds.isEmpty()) {
            return false
        }

        val readingsInterval: List<LocalDateTime> = getReadingsIntervalForProductType(agreementsMap, productType)

        val readingStartDate = readingsInterval.minOf { it }
        val readingEndDate = readingsInterval.maxOf { it }

        return meterPointHasFirstReadings(meterPointIds, readingStartDate, readingEndDate)
    }

    private suspend fun meterHasFirstReadings(
        meterpointIds: List<Long>,
        meterIds: List<Long>,
        readingStartDate: LocalDateTime,
        readingEndDate: LocalDateTime,
    ): Boolean {
        return meterReadingApiService.search(
            meterReadingsSearchRequest {
                meterPointId.addAll(meterpointIds)
                meterId.addAll(meterIds)
                startDate = readingStartDate.toTimestamp()
                endDate = readingEndDate.toTimestamp()
                pagination = pagination {
                    pageNumber = 0
                    pageSize = METER_READINGS_PAGINATION_SIZE
                }
            }
        ).map { it.meterId }
            .distinct()
            .containsAll(meterIds)
    }

    private suspend fun meterPointHasFirstReadings(
        meterPointIds: List<Long>,
        readingStartDate: LocalDateTime,
        readingEndDate: LocalDateTime,
    ): Boolean {
        return meterReadingApiService.search(
            meterReadingsSearchRequest {
                meterPointId.addAll(meterPointIds)
                startDate = readingStartDate.toTimestamp()
                endDate = readingEndDate.toTimestamp()
                pagination = pagination {
                    pageNumber = 0
                    pageSize = METER_READINGS_PAGINATION_SIZE
                }
            }
        ).map { it.meterPointId }
            .distinct()
            .containsAll(meterPointIds)
    }

    private fun getProductStartDate(
        activeAgreements: Map<ProductType, List<Agreement>>,
        productType: ProductType,
    ): LocalDateTime? {
        return activeAgreements[productType]?.first { it.productType == productType }
            ?.fromDate
    }

    private suspend fun hasSubmittedInitialReadings(
        electricityAssetIds: List<Long>,
        gasAssetIds: List<Long>,
        readingDates: List<LocalDate>,
    ): Boolean {
        val hasInitialElectricityReading =
            if (electricityAssetIds.isNotEmpty()) {
                hasSubmittedInitialReadingsForMeters(electricityAssetIds, readingDates)
            } else {
                true
            }
        logger.debug {
            "[::hasSubmittedInitialReadings][electricityAssetIds:$electricityAssetIds] hasInitialElectricityReading $hasInitialElectricityReading"
        }
        val hasInitialGasReading =
            if (gasAssetIds.isNotEmpty()) {
                hasSubmittedInitialReadingsForMeters(gasAssetIds, readingDates)
            } else {
                true
            }
        logger.debug { "[::hasSubmittedInitialReadings][gasAssetIds:$gasAssetIds] hasInitialGasReading $hasInitialGasReading" }

        return hasInitialElectricityReading && hasInitialGasReading
    }

    private suspend fun hasSubmittedInitialReadingsForMeters(
        meterIds: List<Long>,
        readingDates: List<LocalDate>,
    ): Boolean {
        return meterReadingApiService.existsForMeterIdsAndSubmissionProcessed(
            meterIds,
            readingDates
        )
    }

    private suspend fun fetchMeterPointStructureFromJunifer(
        agreementsMap: Map<ProductType, List<Agreement>>,
        productType: ProductType,
    ): List<MeterPointStructure> {
        return agreementsMap[productType]
            ?.flatMap {
                it.meterPointsIds.map { meterPoint ->
                    assetsClient.getMeterPointStructure(
                        meterPointStructureRequest {
                            id = meterPoint
                            effectiveDt = computeEffectiveDate(it)
                        }
                    )
                }
            } ?: emptyList()
    }

    private fun computeEffectiveDate(it: Agreement) =
        if (LocalDate.now() > it.fromDate.toLocalDate()) {
            LocalDate.now().toTimestamp()
        } else
            it.fromDate.toLocalDate().toTimestamp()

    private suspend fun sendOpeningReadingEmail(
        agreementsMap: Map<ProductType, List<Agreement>>,
        user: UserResponse,
        submissions: List<MeterReading>,
        electricityMeterPointIds: List<Long>,
        gasMeterPointIds: List<Long>,
        electricityAssetIds: List<Long>,
        gasAssetIds: List<Long>,
    ) {
        val userId = user.id
        logger.debug { "[::sendOpeningReadingEmail][userId: ${user.id}] submissions: $submissions" }
        if (submissions.isEmpty()) {
            logger.debug("[::sendOpeningReadingEmail][userId: ${user.id}] No submissions passed.")
            return
        }

        val filteredSubmissionsGas =
            filterSubmissions(agreementsMap, submissions, gasMeterPointIds, gasAssetIds, ProductType.GAS)
        val filteredSubmissionsElec =
            filterSubmissions(
                agreementsMap,
                submissions,
                electricityMeterPointIds,
                electricityAssetIds,
                ProductType.ELECTRICITY,
            )

        val filteredSubmissions = filteredSubmissionsElec + filteredSubmissionsGas
        if (filteredSubmissions.isEmpty()) {
            logger.debug("[::sendOpeningReadingEmail][userId: ${user.id}] There are no submissions to email.")
            return
        }
        logger.debug { "[::sendOpeningReadingEmail][userId: ${user.id}] filteredSubmissionsElec $filteredSubmissionsElec" }
        logger.debug { "[::sendOpeningReadingEmail][userId: ${user.id}] filteredSubmissionsGas $filteredSubmissionsGas" }

        val billingAccountId = user.currentAccountId
        val billingAccountNumber =
            billingAccountsClient.getBillingAccountById(idRequest { id = billingAccountId }).number
        val customer = customersClient.getCustomerByBillingAccountId(idRequest { id = billingAccountId })

        val firstName = customer.firstName.capitalizeName()
        val lastName = customer.lastName.capitalizeName()
        val email = user.email
        val fuel = getSubmittedFuel(submissions) ?: ""
        val electE7 = getElectE7(filteredSubmissions)

        val hasBothFuels = hasBothFuels(electricityAssetIds, gasAssetIds)
        val hasMultipleSubmissions = filteredSubmissions.size > 1

        logger.debug("[::sendOpeningReadingEmail][userId: ${user.id}] Sending opening meter read email.")
        communicationsClient.sendCommunicationTemplate(
            CommunicationTemplateDto(
                communicationName = communicationTemplate.openingMeterReadNotifierEmail,
                recipient = RecipientDto(
                    name = "$firstName $lastName",
                    email = email,
                ),
                customAttributes = mapOf(
                    ACCOUNT_NUMBER to billingAccountNumber,
                    EMAIL to email,
                    FIRST_NAME to firstName,
                    FUEL to fuel,
                    HAS_MULTIPLE_SUBMISSIONS to hasMultipleSubmissions.toString(),
                    HAS_BOTH_FUELS to hasBothFuels.toString(),
                    SUBMISSIONS to getSubmissionsString(filteredSubmissions, electE7)
                )
            )
        )
    }

    private fun getElectE7(filteredSubmissions: List<MeterReading>) = filteredSubmissions
        .map { it.fuelType() }
        .filter { it == UnitType.Electricity }
        .size > 1

    private fun getSubmissionsString(filteredSubmissions: List<MeterReading>, elecE7: Boolean): String {
        val submissionsString = StringBuilder()
        val size = filteredSubmissions.size
        filteredSubmissions.forEachIndexed { index, meterReading ->
            submissionsString.append(
                "${
                    getSubmissionFuelTypeTitle(
                        meterReading,
                        index,
                        elecE7
                    )
                } : ${meterReading.cumulative}"
            ).appendIf(index != size - 1, "\\n")
        }

        return submissionsString.toString()
    }

    private fun getSubmissionFuelTypeTitle(meterReading: MeterReading, index: Int, elecE7: Boolean): String {
        return when (meterReading.fuelType()) {
            UnitType.Gas -> GAS
            else -> {
                val identifier = meterReading.register?.id ?: (index + 1)
                if (elecE7) "$ELECTRICITY $identifier" else ELECTRICITY
            }
        }
    }

    private fun hasBothFuels(electricityAssetIds: List<Long>, gasAssetIds: List<Long>): Boolean =
        electricityAssetIds.isNotEmpty() && gasAssetIds.isNotEmpty()

    private fun getSubmittedFuel(submissions: List<MeterReading>): String? {
        val fuels = submissions.map { it.fuelType() }.distinct()

        return if (fuels.size > 1) null else fuels[0].name
    }

    private suspend fun filterSubmissions(
        agreementsMap: Map<ProductType, List<Agreement>>,
        submissions: List<MeterReading>,
        meterPointIds: List<Long>,
        meterIds: List<Long>,
        productType: ProductType,
    ): List<MeterReading> {
        val readingsInterval: List<LocalDateTime> = getReadingsIntervalForProductType(agreementsMap, productType)

        val readingStartDate = readingsInterval.minOf { it }
        val readingEndDate = readingsInterval.maxOf { it }

        logger.debug(
            "[::filterSubmissions][meterpointIds: $meterPointIds][meterIds: $meterIds] readingStartDate:$readingEndDate, readingEndDate:$readingEndDate."
        )

        val allUserSubmissions = meterReadingApiService.search(
            meterReadingsSearchRequest {
                meterPointId.addAll(meterPointIds)
                meterId.addAll(meterIds)
                startDate = readingStartDate.toTimestamp()
                endDate = readingEndDate.toTimestamp()
                pagination = pagination {
                    pageNumber = 0
                    pageSize = METER_READINGS_PAGINATION_SIZE
                }
            }
        )

        logger.debug(
            "[::filterSubmissions][meterpointIds: $meterPointIds][meterIds: $meterIds] all user submissions $allUserSubmissions"
        )
        val submissionFuelTypes = allUserSubmissions.map { it.fuelType() }
        return submissions.filter { submission -> submissionFuelTypes.contains(submission.fuelType()) }
    }

    private fun getReadingsIntervalForProductType(
        agreementsMap: Map<ProductType, List<Agreement>>,
        productType: ProductType,
    ): List<LocalDateTime> {
        val today = LocalDateTime.now()
        val productStartDate = getProductStartDate(agreementsMap, productType)
        val readingsInterval: List<LocalDateTime> = listOfNotNull(today, productStartDate)
        return readingsInterval
    }

    private fun String.capitalizeName() = this.replaceFirstChar(Char::titlecase)
    private fun StringBuilder.appendIf(condition: Boolean, value: String): StringBuilder =
        if (condition) {
            this.append(value)
            this
        } else {
            this
        }

    // TODO remove  when TMP_SO_25244_ASYNC_NEW_SUBMIT_METER_READINGS FF will be removed
    private suspend fun validateAccountReadings(
        accountMeterReadings: List<AccountMeterReadingsRequestDto>,
    ) =
        accountMeterReadings.map { meterReadBoundValidator.validateAccountMeterReadingsRequestDto(it) }
            .filter { it.isInvalid }
            .flatMap {
                it.fold({ errors ->
                    logger.error { errors }
                    errors.all.map { error ->
                        (error as MeterReadingoOutOfBoundsError).toSubmitMeterReadingError()
                    }.onEach {
                        logger.error(
                            "[::validateAccountReadings] service validations failed: error ${it.errorMessage}"
                        )
                    }
                }, { emptyList() })
            }

    suspend fun validateAccountReadingsV2(
        meterReadingsRequestDto: MeterReadingsRequestDto,
    ): SubmitMeterReadingsResult? = validator.validate(meterReadingsRequestDto).fold({ errors ->
        logger.error { errors }
        errors.all.map { error ->
            when (error) {
                is MeterReadingoOutOfBoundsError -> error.toSubmitMeterReadingError()
                else -> SubmitMeterReadingError(
                    errorCode = error.validationErrorCode.lowercase(),
                    errorMessage = error.toString()
                )
            }
        }.onEach {
            logger.error(
                "[::validateAccountReadingsV2] service validations failed: error ${it.errorMessage}"
            )
        }.let {
            SubmitMeterReadingsResult(
                meterReadingsRequestDto.meterPointId,
                OffsetDateTime.now(ZoneOffset.UTC),
                false,
                it
            )
        }
    }, { null })

    private fun MeterReadingoOutOfBoundsError.toSubmitMeterReadingError() =
        SubmitMeterReadingError(
            errorCode = validationErrorCode.lowercase(),
            errorMessage = toString(),
            registerId = registerId
        )

    private fun getAccountMeterResult(
        response: SubmitMeterReadingsResult,
        meterReadingRequestDto: MeterReadingsWithTechnicalDetailsDto,
    ) = SubmitMeterReadingsResultWithRegisterId(
        success = response.success,
        errors = response.errors?.map {
            val registerId = if (it.meterIdentifier != null && it.registerIdentifier != null) {
                registerRepository.findByMeterPointIdAndIdentifiers(
                    meterReadingRequestDto.meterPointId,
                    it.meterIdentifier,
                    it.registerIdentifier
                )?.id
            } else {
                null
            }

            SubmitMeterReadingsErrorWithRegisterId(
                errorCode = it.errorCode,
                errorMessage = it.errorMessage,
                registerId = registerId ?: it.registerId
            )
        }
    )

    private fun getAccountMeterResult(response: SubmitMeterReadingsResult) =
        SubmitMeterReadingsResultWithoutRegisterId(
            success = response.success,
            errors = response.errors?.map {
                SubmitMeterReadingsErrorWithoutRegisterId(
                    errorCode = it.errorCode,
                    errorMessage = it.errorMessage
                )
            }
        )

    private fun unsuccessfulSubmitWithRegisterId(registerId: Long, errorMessage: String) =
        SubmitMeterReadingsResultWithRegisterId(
            success = false,
            errors = listOf(
                SubmitMeterReadingsErrorWithRegisterId(
                    errorMessage = errorMessage,
                    errorCode = "entityNotFound",
                    registerId = registerId
                )
            )
        )
}
