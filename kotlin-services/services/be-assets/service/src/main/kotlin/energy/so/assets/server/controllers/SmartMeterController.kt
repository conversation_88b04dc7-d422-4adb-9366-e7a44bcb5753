package energy.so.assets.server.controllers

import com.google.protobuf.Empty
import energy.so.assets.server.services.SmartMetersDashboardService
import energy.so.assets.smartMeters.v2.GetIHDsRequest
import energy.so.assets.smartMeters.v2.GetIHDsResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataFileUrlResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataRequest
import energy.so.assets.smartMeters.v2.GetUpdatedTimeResponse
import energy.so.assets.smartMeters.v2.SmartMeterDashboardServiceGrpcKt

class SmartMeterController(
    private val dashboardService: SmartMetersDashboardService,
) : SmartMeterDashboardServiceGrpcKt.SmartMeterDashboardServiceCoroutineImplBase() {

    override suspend fun getSmartMeterDataFileUrl(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse {
        return dashboardService.getSmartMeterDataFileUrl(request)
    }

    override suspend fun getSmartMeterDataFileUrlParallelProcessing(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse {
        return dashboardService.getSmartMeterDataFileUrlParallelProcessing(request)
    }

    override suspend fun getUpdatedTime(request: Empty): GetUpdatedTimeResponse {
        return dashboardService.getUpdatedTime(request)
    }

    override suspend fun getIHDsByMPXNs(request: GetIHDsRequest): GetIHDsResponse {
        return dashboardService.getIHDsByMPXNs(request.mpxnsList)
    }
}
