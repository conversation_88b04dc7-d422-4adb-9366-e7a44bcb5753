package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.PropertyEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.PropertyType
import energy.so.commons.model.tables.records.PropertyRecord
import energy.so.commons.model.tables.references.PROPERTY
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Property as JooqProperty

class JooqPropertyEntityRepository(private val dslContext: DSLContext) : PropertyEntityRepository {

    override fun deletePropertyEntity(id: Long): Long {
        dslContext.update(PROPERTY)
            .set(PROPERTY.DELETED, LocalDateTime.now())
            .where(PROPERTY.ID.eq(id))
            .execute()
        return id
    }

    override fun patchPropertyEntity(entity: PropertyEntity): Long {
        val updateStep: UpdateSetStep<PropertyRecord> = dslContext
            .update(PROPERTY)
            .set(PROPERTY.UPDATED_AT, LocalDateTime.now())

        if (entity.hasType()) updateStep.set(PROPERTY.TYPE, PropertyType.valueOf(entity.type.value.uppercase()))
        if (entity.hasAddressId()) updateStep.set(PROPERTY.ADDRESS_ID, entity.addressId.value)
        if (entity.hasIdentifier()) updateStep.set(PROPERTY.IDENTIFIER, entity.identifier.value)
        if (entity.hasCreatedAt()) {
            updateStep.set(PROPERTY.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(PROPERTY.DELETED)
            } else {
                updateStep.set(PROPERTY.DELETED, DSL.coalesce(PROPERTY.DELETED, DSL.value(it.toLocalDateTime())))
            }
        }
        (updateStep as UpdateSetMoreStep).where(PROPERTY.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createPropertyEntity(entity: PropertyEntity): Long {
        return resolveSyncCreationFlow(
            PROPERTY.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreatePropertyEntity,
            ::patchPropertyEntity,
            fun(e: PropertyEntity, newId: Long): PropertyEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreatePropertyEntity(entity: PropertyEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            PROPERTY,
            JooqProperty(
                type = PropertyType.valueOf(entity.type.value.uppercase()),
                addressId = entity.addressId.value,
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                identifier = entity.identifier.getValueOrNull(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(JooqProperty()) }.id!!
}
