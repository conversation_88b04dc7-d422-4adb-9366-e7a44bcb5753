package energy.so.assets.server.models

import energy.so.assets.meter.v2.meter
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime

data class Meter(
    val id: Long? = null,
    val identifier: String,
    val type: MeterType,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val description: String? = null
) {
    companion object {
        fun fromJooq(pojo: energy.so.commons.model.tables.pojos.Meter) = Meter(
            id = pojo.id,
            identifier = pojo.identifier!!,
            type = pojo.type!!.toSiblingEnum(),
            deleted = pojo.deleted,
            createdAt = pojo.createdAt,
            updatedAt = pojo.updatedAt,
            description = pojo.description
        )
    }
}

enum class MeterType {
    CHECK, H, K, LAG_, LEAD_, MAIN_, N, NCAMR, NSS, RCAMR, RCAMY, S, SPECL, T, CM, CR, ET, MT, PP, TH, U, S1, S2, S2A, NS, S2C, S2AD, S2BD, S2CD, S2ADE, S2BDE, S2CDE, S2B, CONV, UNKNOWN
}

fun Meter.toProtobuf(
    registers: List<Register> = emptyList(),
): energy.so.assets.meter.v2.Meter =
    let { model ->
        meter {
            id = model.id!!
            identifier = model.identifier
            type = model.type.toSiblingEnum()
            deleted = model.deleted.toNullableTimestamp()
            updatedAt = model.updatedAt!!.toTimestamp()
            createdAt = model.createdAt!!.toTimestamp()
            isSmartMeter = model.isSmartMeter()
            isSmetsVersion2 = model.isSmetsVersion2()
            registers.map { it.toResponse() }.let { this.registers.addAll(it) }
            description = model.description.toNullableString()
        }
    }

fun List<Meter>.toProtobuf(
    registerToMeterId: Map<Long, List<Register>>,
): List<energy.so.assets.meter.v2.Meter> =
    map { meter -> meter.toProtobuf(registerToMeterId[meter.id] ?: emptyList()) }

fun Meter.isSmartMeter(): Boolean {
    if (type == MeterType.S || type == MeterType.SPECL) {
        return false
    }

    return type.name.startsWith("S")
}

fun Meter.isSmartPayAsYouGo(): Boolean {
    return type.name.startsWith("S2")
}

private val smets1Types = listOf(MeterType.S1, MeterType.S2, MeterType.S2A)

fun Meter.isSmetsVersion2(): Boolean {
    if (!isSmartMeter() || smets1Types.contains(type)) {
        return false
    }

    return type.name.startsWith("S")
}
