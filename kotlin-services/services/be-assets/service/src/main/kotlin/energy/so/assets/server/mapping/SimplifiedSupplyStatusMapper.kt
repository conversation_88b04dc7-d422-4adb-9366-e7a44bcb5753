package energy.so.assets.server.mapping

import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.LOSS_COMPLETED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.LOSS_INITIATED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.LOSS_OBJECTED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.ONBOARDING
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.ON_SUPPLY
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.PRE_ONBOARDING
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_CANCELLED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_OBJECTED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_REJECTED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.STATUS_UNKNOWN
import energy.so.assets.server.models.MeterPointEventType
import energy.so.assets.server.models.MeterPointEventType.Created
import energy.so.assets.server.models.MeterPointEventType.LossCancelled
import energy.so.assets.server.models.MeterPointEventType.LossCompleted
import energy.so.assets.server.models.MeterPointEventType.LossInitiated
import energy.so.assets.server.models.MeterPointEventType.LossObjectionRaised
import energy.so.assets.server.models.MeterPointEventType.LossObjectionRemoved
import energy.so.assets.server.models.MeterPointEventType.LossObjectionUpheld
import energy.so.assets.server.models.MeterPointEventType.ManualUpdate
import energy.so.assets.server.models.MeterPointEventType.RegistrationCancelled
import energy.so.assets.server.models.MeterPointEventType.RegistrationCompleted
import energy.so.assets.server.models.MeterPointEventType.RegistrationObjectionRaised
import energy.so.assets.server.models.MeterPointEventType.RegistrationObjectionRemoved
import energy.so.assets.server.models.MeterPointEventType.RegistrationObjectionUpheld
import energy.so.assets.server.models.MeterPointEventType.RegistrationRejected
import energy.so.assets.server.models.MeterPointEventType.RegistrationStarted
import energy.so.assets.server.models.MeterPointEventType.RegistrationWithdrawRequested
import energy.so.assets.server.models.MeterPointEventType.RegistrationWithdrawn
import energy.so.assets.server.models.MeterPointEventType.RegistrationWithdrawnByNewSupplier
import energy.so.assets.server.models.MeterPointHistory
import energy.so.assets.server.models.MeterPointSupplyStatus
import energy.so.assets.server.models.MeterPointSupplyStatus.LossNotified
import energy.so.assets.server.models.MeterPointSupplyStatus.LossObjected
import energy.so.assets.server.models.MeterPointSupplyStatus.NotSupplied
import energy.so.assets.server.models.MeterPointSupplyStatus.Registered
import energy.so.assets.server.models.MeterPointSupplyStatus.RegistrationConfirmed
import energy.so.assets.server.models.MeterPointSupplyStatus.RegistrationObjected
import energy.so.assets.server.models.MeterPointSupplyStatus.RegistrationRequested
import energy.so.assets.server.models.MeterPointSupplyStatus.RegistrationWithdrawing
import energy.so.assets.server.models.MeterPointType
import energy.so.assets.server.models.MeterPointType.MPAN
import energy.so.assets.server.models.MeterPointType.MPRN
import energy.so.commons.logging.TraceableLogging

private val logger = TraceableLogging.logger { }

object SimplifiedSupplyStatusMapper {

    /**
     * Uses the meterpoint type, Junifer supply status and Junifer last event type to derive a simplified
     * supply status which aligns more closely with our business needs and the states we need to account for.
     */
    fun computeSimplifiedSupplyStatus(
        meterPointId: Long?,
        meterPointType: MeterPointType,
        meterPointHistory: MeterPointHistory?
    ): SimplifiedSupplyStatus {
        val meterPointIdString = "${meterPointId ?: "no id"}"

        val status = simplifiedSupplyStatusMappingList
            .filter { it.meterPointType == meterPointType }
            .filter { it.juniferSupplyStatus == meterPointHistory?.status }
            .firstOrNull { it.juniferEventType == meterPointHistory?.eventType }
            ?.simplifiedSupplyStatus

        return when {
            meterPointHistory == null -> {
                logger.debug("SimplifiedSupplyStatus: No meterpoint history for mp with id [$meterPointIdString}]")
                return STATUS_UNKNOWN
            }
            status == null -> {
                logger.debug(
                    "SimplifiedSupplyStatus: Couldn't map meterpoint history for meterpoint id [$meterPointIdString]"
                )
                return STATUS_UNKNOWN
            }
            else -> status
        }
    }

    /**
     * Defines a mapping FROM: meterpoint type, junifer supply status and junifer event type
     * TO: a simplified supply status.
     */
    private data class SimplifiedStatusMapping(
        val meterPointType: MeterPointType,
        val juniferSupplyStatus: MeterPointSupplyStatus,
        val juniferEventType: MeterPointEventType,
        val simplifiedSupplyStatus: SimplifiedSupplyStatus
    )

    private val simplifiedSupplyStatusMappingList = listOf(
        SimplifiedStatusMapping(MPAN, NotSupplied, Created, PRE_ONBOARDING),
        SimplifiedStatusMapping(MPAN, NotSupplied, RegistrationRejected, REGISTRATION_REJECTED),
        SimplifiedStatusMapping(MPAN, NotSupplied, RegistrationWithdrawRequested, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(MPAN, NotSupplied, MeterPointEventType.RegistrationWithdrawRejected, ONBOARDING),
        SimplifiedStatusMapping(MPAN, NotSupplied, RegistrationWithdrawn, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(MPAN, NotSupplied, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, NotSupplied, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, NotSupplied, ManualUpdate, PRE_ONBOARDING),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationRejected, REGISTRATION_REJECTED),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationWithdrawRequested, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationRequested,
            MeterPointEventType.RegistrationWithdrawRejected,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationWithdrawn, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, MeterPointEventType.RegistrationAccepted, ONBOARDING),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationObjectionRemoved, ONBOARDING),
        SimplifiedStatusMapping(MPAN, RegistrationRequested, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, MeterPointSupplyStatus.RegistrationAccepted, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(
            MPAN,
            MeterPointSupplyStatus.RegistrationAccepted,
            MeterPointEventType.RegistrationAccepted,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPAN,
            MeterPointSupplyStatus.RegistrationAccepted,
            MeterPointEventType.RegistrationWithdrawRejected,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPAN,
            MeterPointSupplyStatus.RegistrationAccepted,
            RegistrationObjectionRemoved,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPAN, RegistrationObjected, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, RegistrationObjected, RegistrationObjectionRemoved, ONBOARDING),
        SimplifiedStatusMapping(MPAN, RegistrationObjected, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPAN, RegistrationWithdrawing, RegistrationWithdrawRequested, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationWithdrawing,
            MeterPointEventType.RegistrationWithdrawRejected,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPAN, RegistrationWithdrawing, RegistrationWithdrawnByNewSupplier, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, RegistrationWithdrawing, RegistrationWithdrawn, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(
            MPAN,
            MeterPointSupplyStatus.RegistrationWithdrawRejected,
            MeterPointEventType.RegistrationWithdrawRejected,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPAN, RegistrationConfirmed, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationConfirmed,
            MeterPointEventType.RegistrationWithdrawRejected,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationConfirmed,
            MeterPointEventType.RegistrationAccepted,
            ON_SUPPLY
        ),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationConfirmed,
            RegistrationObjectionRemoved,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPAN,
            RegistrationConfirmed,
            MeterPointEventType.RegistrationConfirmed,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPAN, Registered, RegistrationStarted, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, MeterPointEventType.RegistrationWithdrawRejected, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, MeterPointEventType.RegistrationAccepted, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, RegistrationObjectionRemoved, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, RegistrationWithdrawnByNewSupplier, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, RegistrationCompleted, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, Registered, LossObjectionUpheld, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, LossNotified, LossInitiated, LOSS_INITIATED),
        SimplifiedStatusMapping(MPAN, LossNotified, RegistrationWithdrawnByNewSupplier, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, LossNotified, LossObjectionRemoved, LOSS_INITIATED),
        SimplifiedStatusMapping(MPAN, LossObjected, LossObjectionRaised, ON_SUPPLY),
        SimplifiedStatusMapping(MPAN, LossObjected, LossObjectionRemoved, LOSS_INITIATED),
        SimplifiedStatusMapping(MPAN, LossObjected, LossObjectionUpheld, ON_SUPPLY),
        SimplifiedStatusMapping(
            MPAN,
            MeterPointSupplyStatus.LossConfirmed,
            MeterPointEventType.LossConfirmed,
            LOSS_INITIATED
        ),
        SimplifiedStatusMapping(MPAN, MeterPointSupplyStatus.LossConfirmed, LossCompleted, LOSS_COMPLETED),
        SimplifiedStatusMapping(MPAN, NotSupplied, LossCompleted, LOSS_COMPLETED),
        SimplifiedStatusMapping(MPAN, MeterPointSupplyStatus.LossConfirmed, LossObjectionRemoved, LOSS_COMPLETED),
        SimplifiedStatusMapping(MPRN, NotSupplied, Created, PRE_ONBOARDING),
        SimplifiedStatusMapping(MPRN, NotSupplied, ManualUpdate, PRE_ONBOARDING),
        SimplifiedStatusMapping(MPRN, NotSupplied, RegistrationCancelled, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(MPRN, NotSupplied, RegistrationRejected, REGISTRATION_REJECTED),
        SimplifiedStatusMapping(MPRN, NotSupplied, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, NotSupplied, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationRejected, REGISTRATION_REJECTED),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationCancelled, REGISTRATION_CANCELLED),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, MeterPointEventType.RegistrationAccepted, ONBOARDING),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationObjectionRemoved, ONBOARDING),
        SimplifiedStatusMapping(MPRN, RegistrationRequested, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, MeterPointSupplyStatus.RegistrationAccepted, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(
            MPRN,
            MeterPointSupplyStatus.RegistrationAccepted,
            MeterPointEventType.RegistrationAccepted,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPRN,
            MeterPointSupplyStatus.RegistrationAccepted,
            RegistrationObjectionRemoved,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPRN, RegistrationObjected, RegistrationObjectionRaised, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, RegistrationObjected, RegistrationObjectionRemoved, ONBOARDING),
        SimplifiedStatusMapping(MPRN, RegistrationObjected, RegistrationObjectionUpheld, REGISTRATION_OBJECTED),
        SimplifiedStatusMapping(MPRN, RegistrationConfirmed, RegistrationStarted, ONBOARDING),
        SimplifiedStatusMapping(
            MPRN,
            RegistrationConfirmed,
            MeterPointEventType.RegistrationAccepted,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPRN,
            RegistrationConfirmed,
            RegistrationObjectionRemoved,
            ONBOARDING
        ),
        SimplifiedStatusMapping(
            MPRN,
            RegistrationConfirmed,
            MeterPointEventType.RegistrationConfirmed,
            ONBOARDING
        ),
        SimplifiedStatusMapping(MPRN, Registered, RegistrationStarted, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, Registered, MeterPointEventType.RegistrationAccepted, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, Registered, RegistrationObjectionRemoved, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, Registered, MeterPointEventType.RegistrationConfirmed, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, Registered, LossObjectionUpheld, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, Registered, RegistrationCompleted, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, LossNotified, LossInitiated, LOSS_INITIATED),
        SimplifiedStatusMapping(MPRN, LossNotified, LossObjectionRemoved, LOSS_INITIATED),
        SimplifiedStatusMapping(MPRN, LossNotified, LossCancelled, ON_SUPPLY),
        SimplifiedStatusMapping(MPRN, LossObjected, LossObjectionRaised, LOSS_OBJECTED),
        SimplifiedStatusMapping(MPRN, LossObjected, LossObjectionRemoved, LOSS_INITIATED),
        SimplifiedStatusMapping(MPRN, LossObjected, LossObjectionUpheld, LOSS_OBJECTED),
        SimplifiedStatusMapping(MPRN, MeterPointSupplyStatus.LossConfirmed, LossInitiated, LOSS_INITIATED),
        SimplifiedStatusMapping(
            MPRN,
            MeterPointSupplyStatus.LossConfirmed,
            MeterPointEventType.LossConfirmed,
            LOSS_INITIATED
        ),
        SimplifiedStatusMapping(MPRN, MeterPointSupplyStatus.LossConfirmed, LossObjectionRemoved, LOSS_INITIATED),
        SimplifiedStatusMapping(MPRN, MeterPointSupplyStatus.LossConfirmed, LossCompleted, LOSS_COMPLETED),
        SimplifiedStatusMapping(MPRN, NotSupplied, LossCompleted, LOSS_COMPLETED)
    )
}
