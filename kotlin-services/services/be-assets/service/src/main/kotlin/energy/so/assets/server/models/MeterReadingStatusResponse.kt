package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.MeterReadingSubmissionError
import energy.so.assets.meterReadings.v2.MeterReadingSubmissionValue
import energy.so.assets.meterReadings.v2.ReadingSubmissions
import energy.so.assets.meterReadings.v2.meterReadingSubmissionError
import energy.so.assets.meterReadings.v2.meterReadingSubmissionValue
import energy.so.assets.meterReadings.v2.readingSubmissions
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.json.serializers.LocalDateAsISO8601Serializer
import energy.so.commons.json.serializers.LocalDateTimeAsISO8601Serializer
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.LocalDateTime

@Serializable
data class MeterReadingStatusResponse(
    @Serializable(with = LocalDateTimeAsISO8601Serializer::class)
    val submissionTime: LocalDateTime,
    val submissionValues: List<SubmissionValue>,
    val meterReadingStatusType: MeterReadingSubmissionStatus,
    val id: Long? = null,
    val email: String? = null,
    val accountNumber: String? = null,
    val submissionErrors: List<SubmissionError>? = null,
)

@Serializable
data class SubmissionValue(
    val registerId: Long? = null,
    val value: Long,
    val type: ReadingType,
    @Serializable(LocalDateAsISO8601Serializer::class)
    val date: LocalDate,
    val meterType: UnitType? = null
)

@Serializable
data class SubmissionError(
    val errorCode: String,
    val errorMessage: String,
    val meterIdentifier: String? = null,
    val registerIdentifier: String? = null,
)

enum class MeterReadingSubmissionStatus {
    PENDING,
    ERROR,
    CONTACTED,
    SUBMITTED,
    MANUAL_SUBMISSION,
    NO_ACTION_REQUIRED
}

fun MeterReadingStatusResponse.toResponse(): ReadingSubmissions = let { from ->
    readingSubmissions {
        submissionTime = from.submissionTime.toTimestamp()
        this.meterReadingSubmissionValues.addAll(from.submissionValues.map { it.toProto() })
        meterReadingSubmissionStatus = from.meterReadingStatusType.toSiblingEnum()
        from.id?.let { this.id = it }
        from.email?.let { this.email = it }
        from.accountNumber?.let { this.accountNumber = it }
        from.submissionErrors
            ?.map { error -> error.toProto() }
            ?.let { mapped -> error.addAll(mapped) }
    }
}

private fun SubmissionValue.toProto(): MeterReadingSubmissionValue = let { from ->
    meterReadingSubmissionValue {
        value = from.value
        type = from.type.toSiblingEnum()
        submissionDate = from.date.toTimestamp()
        from.meterType?.let { meterType = it.toSiblingEnum() }
    }
}

private fun SubmissionError.toProto(): MeterReadingSubmissionError = let { from ->
    meterReadingSubmissionError {
        errorCode = from.errorCode
        errorDescription = from.errorMessage
    }
}
