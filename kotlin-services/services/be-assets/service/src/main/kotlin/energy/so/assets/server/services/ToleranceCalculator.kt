package energy.so.assets.server.services

import energy.so.assets.server.models.MeterReadDto
import java.time.LocalDateTime.now
import java.time.temporal.ChronoUnit.DAYS

const val DAYS_IN_YEAR = 365
const val DEFAULT_KWH_VALUE = 1.0F
const val CORRECTION_FACTOR = 1.02264F
const val KWH_CONVERSION_FACTOR = 3.6F
const val CALORIFIC_VALUE = 39.2F
const val HCF_TO_KWH_CONVERSION_FACTOR = 2.83F
fun MeterReadDto.calculateTolerance(usage: Float): Pair<Float, Float> {
    val upper: Float = 100.00F.coerceAtLeast(
        (
            this.cumulative + (
                (usage / kwhConversionFactor(this.unit)) *
                    DAYS.between(this.readingDateTime, now()) * 4 / DAYS_IN_YEAR
                )
            )
    )
    return Pair(this.cumulative, upper)
}

fun kwhConversionFactor(unit: String): Float {
    val m3ToKwhConversion = (CORRECTION_FACTOR * CALORIFIC_VALUE) / KWH_CONVERSION_FACTOR
    return when (unit) {
        "m3" -> m3ToKwhConversion
        "hcf" -> m3ToKwhConversion * HCF_TO_KWH_CONVERSION_FACTOR
        else -> DEFAULT_KWH_VALUE
    }
}
