package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointAverageHhConsumption
import energy.so.commons.model.tables.references.METER_POINT_AVERAGE_HH_CONSUMPTION
import org.jooq.DSLContext

class JooqMeterPointAverageHhConsumptionRepository(private val dslContext: DSLContext) :
    MeterPointAverageHhConsumptionRepository {

    override fun findByMeterPointId(meterPointId: Long): MeterPointAverageHhConsumption? {
        return dslContext.selectFrom(METER_POINT_AVERAGE_HH_CONSUMPTION)
            .where(METER_POINT_AVERAGE_HH_CONSUMPTION.METER_POINT_ID.eq(meterPointId),
                METER_POINT_AVERAGE_HH_CONSUMPTION.DELETED.isNull)
            .orderBy(METER_POINT_AVERAGE_HH_CONSUMPTION.CREATED_AT.desc())
            .limit(1)
            .fetchOneInto(energy.so.commons.model.tables.pojos.MeterPointAverageHhConsumption::class.java)
            ?.let(MeterPointAverageHhConsumption::fromJooq)
    }
}