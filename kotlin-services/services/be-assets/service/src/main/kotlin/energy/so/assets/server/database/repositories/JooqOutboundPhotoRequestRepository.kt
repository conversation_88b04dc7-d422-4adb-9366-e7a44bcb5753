package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.EmailType
import energy.so.assets.server.models.EmailType.FIRST_FOLLOW_UP
import energy.so.assets.server.models.EmailType.INITIAL_EMAIL
import energy.so.assets.server.models.EmailType.SECOND_FOLLOW_UP
import energy.so.assets.server.models.OutboundPhotoRequest
import energy.so.assets.server.models.OutboundPhotoRequestFilter
import energy.so.assets.server.models.OutboundPhotoRequestStatus
import energy.so.assets.server.models.jsonFormat
import energy.so.assets.server.models.toJooq
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.OutboundPhotoRequestStatu
import energy.so.commons.model.enums.OutboundPhotoRequestStatu.CANCELLED
import energy.so.commons.model.enums.OutboundPhotoRequestStatu.UPLOADED
import energy.so.commons.model.tables.records.OutboundPhotoRequestRecord
import energy.so.commons.model.tables.references.ACTIVE_OUTBOUND_PHOTO_REQUEST
import energy.so.commons.model.tables.references.OUTBOUND_PHOTO_REQUEST
import java.time.LocalDate
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import org.jooq.impl.DSL.field
import energy.so.commons.model.tables.pojos.OutboundPhotoRequest as JooqOutboundPhotoRequest

private val logger = TraceableLogging.logger {}

class JooqOutboundPhotoRequestRepository(
    private val dslContext: DSLContext,
) : OutboundPhotoRequestRepository {
    override fun save(outboundPhotoRequest: OutboundPhotoRequest): OutboundPhotoRequest =
        dslContext.newRecord(OUTBOUND_PHOTO_REQUEST, outboundPhotoRequest.toJooq())
            .apply { save() }
            .run { into(JooqOutboundPhotoRequest()) }
            .let { OutboundPhotoRequest.fromJooq(it) }

    override fun accountNumberHasOpenRequest(accountNumber: String): Boolean =
        dslContext.fetchExists(
            dslContext.selectFrom(OUTBOUND_PHOTO_REQUEST)
                .where(OUTBOUND_PHOTO_REQUEST.ACCOUNT_NUMBER.eq(accountNumber))
                .and(OUTBOUND_PHOTO_REQUEST.STATUS.notIn(CANCELLED, UPLOADED))
                .and(OUTBOUND_PHOTO_REQUEST.DELETED.isNull)
        )

    override fun findById(outboundPhotoRequestFilter: OutboundPhotoRequestFilter): OutboundPhotoRequest? =
        dslContext.selectFrom(OUTBOUND_PHOTO_REQUEST)
            .where(getSearchConditions(outboundPhotoRequestFilter))
            .fetchOneInto(JooqOutboundPhotoRequest::class.java)
            ?.let { OutboundPhotoRequest.fromJooq(it) }

    override fun updateStatus(
        id: Long,
        newStatus: OutboundPhotoRequestStatus,
        freshdeskTicketId: Long?,
        caseId: String?,
        emailMessageId: Long?,
        emailType: EmailType?,
    ): OutboundPhotoRequest? {
        val updateStep: UpdateSetStep<OutboundPhotoRequestRecord> = dslContext.update(OUTBOUND_PHOTO_REQUEST)
            .set(OUTBOUND_PHOTO_REQUEST.STATUS, newStatus.toSiblingEnum<OutboundPhotoRequestStatu>())

        if (freshdeskTicketId != null) {
            updateStep.set(OUTBOUND_PHOTO_REQUEST.FRESHDESK_TICKET_ID, freshdeskTicketId)
        }
        if (caseId != null) {
            updateStep.set(OUTBOUND_PHOTO_REQUEST.CASE_ID, caseId)
        }
        if (emailMessageId != null) {
            updateStep.set(OUTBOUND_PHOTO_REQUEST.EMAIL_MESSAGE_ID, emailMessageId)
        }
        if (emailType != null) {
            when (emailType) {
                FIRST_FOLLOW_UP -> {
                    updateStep.set(
                        OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT,
                        field(
                            "coalesce(emails_sent_at, '{}') || jsonb_build_object('first_follow_up_sent_at', now())",
                            OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT.dataType
                        )
                    )
                }

                SECOND_FOLLOW_UP -> {
                    updateStep.set(
                        OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT,
                        field(
                            "coalesce(emails_sent_at, '{}') || jsonb_build_object('second_follow_up_sent_at', now())",
                            OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT.dataType
                        )
                    )
                }

                INITIAL_EMAIL -> {
                    updateStep.set(
                        OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT,
                        field(
                            "coalesce(emails_sent_at, '{}') || jsonb_build_object('initial_email_sent_at', now())",
                            OUTBOUND_PHOTO_REQUEST.EMAILS_SENT_AT.dataType
                        )
                    )
                }
            }
        }

        return (updateStep as UpdateSetMoreStep)
            .where(OUTBOUND_PHOTO_REQUEST.ID.eq(id))
            .returningResult(OUTBOUND_PHOTO_REQUEST)
            .fetchOneInto(JooqOutboundPhotoRequest::class.java)
            .apply {
                if (this == null) {
                    logger.warn(
                        "[::updateStatus] no OUTBOUND_PHOTO_REQUEST record found with id $id to update with status: $newStatus."
                    )
                }
            }
            ?.let { OutboundPhotoRequest.fromJooq(it) }
    }

    override fun getActiveOutboundRequests(accountNumber: String): List<OutboundPhotoRequest> =
        dslContext.selectFrom(ACTIVE_OUTBOUND_PHOTO_REQUEST)
            .where(ACTIVE_OUTBOUND_PHOTO_REQUEST.ACCOUNT_NUMBER.eq(accountNumber))
            .fetchInto(JooqOutboundPhotoRequest::class.java)
            .map { OutboundPhotoRequest.fromJooq(it) }

    override fun getCancelableOutboundRequest(maxActivityPeriodDays: Long): List<OutboundPhotoRequest> =
        dslContext.selectFrom(ACTIVE_OUTBOUND_PHOTO_REQUEST)
            .where(ACTIVE_OUTBOUND_PHOTO_REQUEST.STATUS.eq(OutboundPhotoRequestStatu.SECOND_FOLLOW_UP_SENT))
            .and(
                field("DATE({0})", LocalDate::class.java, ACTIVE_OUTBOUND_PHOTO_REQUEST.LAST_EMAIL_SENT_AT).eq(
                    LocalDate.now().minusDays(maxActivityPeriodDays)
                )
            )
            .and(ACTIVE_OUTBOUND_PHOTO_REQUEST.DELETED.isNull)
            .fetchInto(JooqOutboundPhotoRequest::class.java)
            .map { OutboundPhotoRequest.fromJooq(it) }

    override fun cancelOutboundRequest(id: Long) {
        val updatedRequests = dslContext.update(OUTBOUND_PHOTO_REQUEST)
            .set(OUTBOUND_PHOTO_REQUEST.STATUS, CANCELLED)
            .where(OUTBOUND_PHOTO_REQUEST.ID.eq(id))
            .execute()

        if (updatedRequests == 0) {
            logger.warn(
                "[::cancelOutboundRequest] no OUTBOUND_PHOTO_REQUEST record found with id $id to update."
            )
        }
    }

    private fun getSearchConditions(searchRequest: OutboundPhotoRequestFilter) = listOf(
        OUTBOUND_PHOTO_REQUEST.ID.eq(searchRequest.id),
        if (searchRequest.accountNumber != null) {
            OUTBOUND_PHOTO_REQUEST.ACCOUNT_NUMBER.eq(searchRequest.accountNumber)
        } else DSL.trueCondition(),
        if (searchRequest.statusesList.isNotEmpty()) {
            OUTBOUND_PHOTO_REQUEST.STATUS.`in`(
                searchRequest.statusesList.map { it.toSiblingEnum<OutboundPhotoRequestStatu>() }
            )
        } else DSL.trueCondition(),
        when (searchRequest.deleted) {
            true -> OUTBOUND_PHOTO_REQUEST.DELETED.isNotNull
            false -> OUTBOUND_PHOTO_REQUEST.DELETED.isNull
            else -> DSL.trueCondition()
        }
    )
}
