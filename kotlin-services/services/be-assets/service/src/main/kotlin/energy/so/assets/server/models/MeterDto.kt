package energy.so.assets.server.models

import energy.so.ac.junifer.v1.assets.Reading
import energy.so.assets.meter.v2.MeterInfo
import energy.so.assets.meter.v2.MeterInfoResponse
import energy.so.assets.meter.v2.MeterPointType.valueOf
import energy.so.assets.meter.v2.meterInfo
import energy.so.assets.meter.v2.meterInfoResponse
import energy.so.assets.meter.v2.meterReadInfo
import energy.so.assets.meter.v2.meterReadInfoResponse
import energy.so.assets.server.services.calculateTolerance
import energy.so.commons.grpc.clients.junifer.dtos.EnergyUsageUnit
import energy.so.commons.grpc.clients.junifer.dtos.MeterPointType
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.junifer.v2.MeterReadingByMeterpoint
import java.time.LocalDate
import java.time.LocalDateTime

data class MeterReadDto(
    val source: String,
    val meterIdentifier: Long,
    val readingDateTime: LocalDateTime,
    val rateName: String,
    val meterSerialNumber: String,
    val registerIdentifier: String,
    val cumulative: Float,
    val unit: String,
)

data class MeterReadInfo(
    val meterPointId: String,
    val meterSerialNumber: String,
    val registerId: String,
    val lastRead: Float,
    val toleranceLower: Float,
    val toleranceHigher: Float,
    val lastReadAt: LocalDate,
    val rateName: String,
)

data class MeterReadInfoResponse(
    val meterReadInfo: List<MeterReadInfo>,
)

data class MeterInfoDto(
    val meterPointId: Long,
    val identifier: String,
    val meterSerialNumber: String,
    val registerId: String,
    val unit: EnergyUsageUnit,
    val type: MeterPointType,
    val meterType: MeterType,
)

data class MeterInfoResponseDto(
    val meterInfo: List<MeterInfoDto>,
)

fun List<MeterReadingByMeterpoint>.toMeterReadDtoList() = this.map {
    MeterReadDto(
        source = it.source,
        meterIdentifier = it.meterIdentifier,
        readingDateTime = it.readingDateTime.toLocalDateTime(),
        rateName = it.rateName,
        meterSerialNumber = it.meterSerialNumber,
        registerIdentifier = it.registerIdentifier,
        cumulative = it.cumulative,
        unit = it.unit,
    )
}

fun List<Reading>.toMeterReadDtos() = this.map {
    MeterReadDto(
        source = it.source,
        meterIdentifier = it.meterId,
        readingDateTime = it.readingDttm.toLocalDateTime(),
        rateName = it.rateName ?: "",
        meterSerialNumber = it.meter,
        registerIdentifier = it.register ?: "",
        cumulative = it.cumulative.toFloat(),
        unit = it.unit,
    )
}

fun MeterReadDto.toMeterReadInfo(meterPointId: Long, usage: Float): MeterReadInfo {
    val tolerance = this.calculateTolerance(usage)
    return MeterReadInfo(
        meterPointId = meterPointId.toString(),
        meterSerialNumber = this.meterSerialNumber,
        registerId = this.registerIdentifier,
        lastRead = this.cumulative,
        toleranceLower = tolerance.first,
        toleranceHigher = tolerance.second,
        lastReadAt = this.readingDateTime.toLocalDate(),
        rateName = this.rateName
    )
}

fun List<MeterReadDto>.addMeterReadInfo(
    meterPointId: Long,
    rateName: String,
    meterReadInfo: MutableList<MeterReadInfo>,
    usage: Float,
) {
    this.firstOrNull { it.rateName.lowercase() == rateName }?.let { meterReading ->
        meterReadInfo.add(meterReading.toMeterReadInfo(meterPointId, usage))
    }
}

fun MeterReadInfo.toMeterReadInfo(): energy.so.assets.meter.v2.MeterReadInfo = let { dto ->
    meterReadInfo {
        meterPointId = dto.meterPointId
        meterSerialNumber = dto.meterSerialNumber
        registerId = dto.registerId
        lastRead = dto.lastRead
        toleranceLower = dto.toleranceLower
        toleranceHigher = dto.toleranceHigher
        lastReadAt = dto.lastReadAt.toTimestamp()
        rateName = dto.rateName
    }
}

fun MeterReadInfoResponse.toMeterReadInfoResponse(): energy.so.assets.meter.v2.MeterReadInfoResponse = let { dto ->
    meterReadInfoResponse {
        meterReadInfo.addAll(dto.meterReadInfo.map { it.toMeterReadInfo() })
    }
}

fun MeterInfoDto.toMeterInfo(): MeterInfo = let { dto ->
    meterInfo {
        identifier = dto.identifier
        meterSerialNumber = dto.meterSerialNumber
        registerId = dto.registerId
        unit = dto.unit.code
        type = valueOf(dto.type.name)
        meterPointId = dto.meterPointId
        meterType = energy.so.assets.meter.v2.MeterType.valueOf(dto.meterType.name)
    }
}

fun MeterInfoResponseDto.toMeterInfoResponse(): MeterInfoResponse = let { dto ->
    meterInfoResponse {
        meterInfo.addAll(dto.meterInfo.map { it.toMeterInfo() })
    }
}
