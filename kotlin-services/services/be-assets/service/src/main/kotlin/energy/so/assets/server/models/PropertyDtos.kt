package energy.so.assets.server.models

import energy.so.assets.properties.v2.CreatePropertyRequest
import energy.so.assets.properties.v2.propertyResponse
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.model.tables.pojos.Property
import energy.so.commons.validations.Validator
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.PositiveOrZero

data class CreatePropertyDto(
    val address1: String,
    val address2: String? = null,
    val address3: String? = null,
    val postcode: String,
    val accountId: Long,
    val accountOwnsProperty: Boolean,
    val conservationArea: Boolean,
    @field:PositiveOrZero
    @field:Max(100)
    val timeAtProperty: Int,
) {
    init {
        Validator.validate(this)
    }
}

data class PropertyDto(
    val id: Long,
    val address1: String,
    val address2: String? = null,
    val address3: String? = null,
    val postcode: String,
)

fun CreatePropertyDto.toProperty() = Property(
    // TODO
)

// TODO
// fun Property.toPropertyDto() = PropertyDto()

fun CreatePropertyRequest.toCreatePropertyDto() = CreatePropertyDto(
    address1 = address1,
    address2 = address2.getValueOrNull(),
    address3 = address3.getValueOrNull(),
    postcode = postcode,
    accountId = accountId,
    accountOwnsProperty = accountOwnsProperty,
    conservationArea = conservationArea,
    timeAtProperty = timeAtProperty,
)

fun PropertyDto.toPropertyResponse() = let { propertyDto ->
    propertyResponse {
        id = propertyDto.id
        address1 = propertyDto.address1
        address2 = propertyDto.address2.toNullableString()
        address3 = propertyDto.address3.toNullableString()
        postcode = propertyDto.postcode
    }
}
