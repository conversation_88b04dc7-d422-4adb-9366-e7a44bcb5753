package energy.so.assets.server.services

import energy.so.assets.meterReadings.v2.*
import energy.so.assets.server.models.MeterReadingPhotoEvidence

interface MeterReadingPhotoEvidenceService {
    /**
     * Adds meter reading photo evidence as metadata stored in db.
     * Then triggers creation of freshdesk ticket via a pubsub topic.
     */
    suspend fun addMeterReadingPhotoEvidence(request: AddMeterReadingPhotoEvidenceRequest)

    /**
     * Adds freshdesk photo evidence as metadata stored in db.
     * If there is an active outbound request for account in cause, it will be cancelled.
     */
    suspend fun addFreshdeskPhotoEvidence(request: FreshdeskMeterReadingPhotoEvidenceRequest)

    /**
     * Adds meter reading outbound photo as metadata stored in db.
     * Then triggers update of freshdesk ticket via a pubsub topic.
     */
    suspend fun addMeterReadingOutboundPhoto(request: AddMeterReadingOutboundPhotoRequest)

    fun generateMeterReadingPhotoEvidenceGetUrl(request: MeterReadingPhotoEvidenceGetUrlRequest): String

    suspend fun generateMeterReadingPhotoEvidenceUploadUrls(request: MeterReadingPhotoEvidenceUploadUrlRequest): Map<String, String>

    fun getMeterReadingPhotoEvidenceByAccountNumber(request: GetMeterReadingPhotoEvidenceRequest): List<MeterReadingPhotoEvidence>

    fun deleteMeterReadingPhotoEvidence(imageIdentifier: String)

    suspend fun createPhotoEvidenceFreshDeskTicket(photoEvidenceIdentifiers: List<String>)

    suspend fun updatePhotoEvidenceFreshDeskTicket(photoEvidenceIdentifiers: List<String>, outboundRequestId: Long)
}
