package energy.so.assets.server.validators

import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.assets.server.models.UnitType
import energy.so.assets.server.services.FeatureService
import org.koin.core.annotation.Single

/**
 * A validator that is responsible for:
 * - Ensuring the meterpoint ID is valid.
 * - Ensuring the meterpoint is linked to a valid register.
 * - Ensuring that both meterpoint and register are active.
 * <AUTHOR>
 */
@Single(createdAtStart = true)
class MeterPointValidator(
    private val registerRepository: RegisterRepository,
    private val meterReadingConfig: MeterReadingConfig,
    private val featureService: FeatureService,
) : MeterReadingValidator {

    override suspend fun validate(subject: MeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> {
        if (subject is MeterReadingsWithoutTechnicalDetailsDto) {
            return validNel()
        }

        val meterPointId = subject.meterPointId
        val isGasMeterPoint = UnitType.Gas == (subject as MeterReadingsWithTechnicalDetailsDto).readings[0].unitType
        val daysBeforeSupplyStartDate = meterReadingConfig.getDaysBeforeSupplyStartDate(isGasMeterPoint)

        if (registerRepository.findByMeterPointId(meterPointId, daysBeforeSupplyStartDate)
                .isEmpty()
        ) {
            return MisconfiguredMeter(meterPointId).invalidNel()
        }

        return validNel()
    }
}
