package energy.so.assets.server.services.accountState

import energy.so.assets.meterReadings.v2.MeterReadingStatusMessage
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.MeterPointType
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingSequenceType
import energy.so.assets.server.models.OpeningMeterReadingState
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.MeterReadingApiService
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.search.pagination
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import java.time.LocalDate

private const val METER_READING_PAGE_SIZE = 10000

class OpeningReadingStateService(
    private val productAccountClient: ProductAccountsClient,
    private val meterPointService: MeterPointService,
    private val meterReadingsApiService: MeterReadingApiService,
    private val meterReadingConfig: MeterReadingConfig,
) : AccountStateService<OpeningMeterReadingState> {

    override suspend fun computeState(accountId: Long): OpeningMeterReadingState {
        val meterPoints = getMeterPointsByAccount(accountId)
        val meterPointsInOMRWindow = getMeterPointsInOMRWindow(meterPoints).toSet()
        val meterPointsBeforeOMRWindow = getMeterPointsBeforeOMRWindow(meterPoints).toSet()
        val meterPointsAfterOMRWindow =
            meterPoints.map { it.id }.toSet() - meterPointsBeforeOMRWindow - meterPointsInOMRWindow

        val meterReadings = getMeterReadings(meterPoints)

        val elecMeterPointsWithoutOMR = getMeterPointsWithoutOMR(meterPoints, meterReadings, MeterPointType.MPAN)
        val gasMeterPointsWithoutOMR = getMeterPointsWithoutOMR(meterPoints, meterReadings, MeterPointType.MPRN)

        return when {
            meterPointsInOMRWindow.intersect(elecMeterPointsWithoutOMR).isNotEmpty() &&
                    meterPointsInOMRWindow.intersect(gasMeterPointsWithoutOMR).isNotEmpty() ->
                OpeningMeterReadingState.AWAITING_ELEC_AND_GAS_READINGS

            meterPointsInOMRWindow.intersect(elecMeterPointsWithoutOMR).isNotEmpty() ->
                OpeningMeterReadingState.AWAITING_ELEC_READING

            meterPointsInOMRWindow.intersect(gasMeterPointsWithoutOMR).isNotEmpty() ->
                OpeningMeterReadingState.AWAITING_GAS_READING

            elecMeterPointsWithoutOMR.intersect(meterPointsAfterOMRWindow).isNotEmpty() ||
                    gasMeterPointsWithoutOMR.intersect(meterPointsAfterOMRWindow).isNotEmpty() ->
                OpeningMeterReadingState.MISSED_SUBMISSION_WINDOW

            meterPointsBeforeOMRWindow.isNotEmpty() -> OpeningMeterReadingState.NOT_READY_FOR_OPENING_READINGS

            else -> OpeningMeterReadingState.ALL_SUBMITTED
        }
    }

    private suspend fun getMeterPointsByAccount(accountId: Long) =
        productAccountClient.getProductAccountsByBillingAccountId(idRequest { id = accountId })
            .productAccountsList
            .flatMap { it.agreementsList }
            .flatMap { it.meterPointsList }
            .map { it.id }
            .let { meterPointService.getMeterPointsByIds(it) }

    private fun getMeterPointsInOMRWindow(
        meterPoints: List<MeterPoint>,
    ): List<Long> {
        val today = LocalDate.now()

        return meterPoints
            .filter { meterPoint ->
                meterPoint.supplyStartDate?.let { ssd ->
                    when (meterPoint.type) {
                        MeterPointType.MPAN -> {
                            val days = meterReadingConfig.firstReadingPeriodDaysElec.toLong()
                            !today.minusDays(days).isAfter(ssd) && !today.plusDays(days).isBefore(ssd)
                        }

                        MeterPointType.MPRN -> !today.isBefore(ssd) &&
                                !today.isAfter(ssd.plusDays(meterReadingConfig.firstReadingMaxPeriodDaysGas.toLong()))
                    }
                } ?: false
            }
            .mapNotNull { it.id }
    }

    private fun getMeterPointsBeforeOMRWindow(
        meterPoints: List<MeterPoint>,
    ): List<Long> {
        val today = LocalDate.now()

        return meterPoints
            .filter { meterPoint ->
                meterPoint.supplyStartDate?.let { ssd ->
                    when (meterPoint.type) {
                        MeterPointType.MPAN -> {
                            today.minusDays(meterReadingConfig.firstReadingPeriodDaysElec.toLong()).isBefore(ssd)
                        }

                        MeterPointType.MPRN -> today.isBefore(ssd)
                    }
                } ?: false
            }
            .mapNotNull { it.id }
    }

    private suspend fun getMeterReadings(meterPoints: List<MeterPoint>) =
        meterReadingsApiService.search(
            meterReadingsSearchRequest {
                this.status.add(MeterReadingStatusMessage.MeterReadingStatus.ACCEPTED)
                this.meterPointId.addAll(meterPoints.mapNotNull { it.id })
                this.pagination = pagination {
                    pageNumber = 1
                    pageSize = METER_READING_PAGE_SIZE
                }
            }
        )

    private fun getMeterPointsWithoutOMR(
        meterPoints: List<MeterPoint>,
        meterReadings: List<MeterReading>,
        searchedType: MeterPointType,
    ) = meterPoints
        .filter { it.type == searchedType }
        .filter { meterPoint ->
            meterReadings.none {
                it.meterPointId == meterPoint.id && it.sequenceType == MeterReadingSequenceType.FIRST
            }
        }
        .mapNotNull { it.id }
        .toSet()
}
