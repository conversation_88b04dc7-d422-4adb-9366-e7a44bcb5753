package energy.so.assets.server.validators

import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.assets.server.database.repositories.MeterPointHistoryRepository
import energy.so.assets.server.models.MeterPointEventType
import energy.so.assets.server.models.MeterPointHistory
import energy.so.commons.validations.validator.Validator
import org.koin.core.annotation.Single
import java.time.LocalDate

/**
 * A validator that is responsible for:
 * - Ensuring the given meterpoint accept reads
 */
@Single(createdAtStart = true)
class MeterPointAcceptsReadsValidator(private val repository: MeterPointHistoryRepository) :
    Validator<MeterReadingError, Long> {

    override suspend fun validate(subject: Long): ValidatedNel<MeterReadingError, *> {
        return repository.findLastByMeterPointId(subject)
            ?.let { isSupplyStatusEnding(it) }
            .let { bool -> if (bool == true) validNel() else MeterPointNotAcceptReads(subject).invalidNel() }
    }

    private fun isSupplyStatusEnding(meterPointHistory: MeterPointHistory): Boolean {
        val eventType = meterPointHistory.eventType

        return (
            eventType != MeterPointEventType.LossInitiated ||
                meterPointHistory.createdAt.toLocalDate() >= LocalDate.now()
                    .minusDays(SUPPLY_STATUS_DAYS_BEFORE)
            ) && eventType != MeterPointEventType.LossConfirmed
    }

    companion object {
        const val SUPPLY_STATUS_DAYS_BEFORE: Long = 15
    }
}
