package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.*
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.MeterReadingPhotoEvidenceStatu
import java.time.LocalDateTime
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidence as ProtoMeterReadingPhotoEvidence
import energy.so.assets.meterReadings.v2.PhotoEvidenceMetadata.MeterReadingFuel as ProtoMeterReadingFuel
import energy.so.commons.model.enums.MeterReadingFuel as JooqMeterReadingFuel
import energy.so.commons.model.tables.pojos.MeterReadingPhotoEvidence as JooqMeterReadingPhotoEvidence

data class MeterReadingPhotoEvidence(
    val id: Long? = null,
    val accountNumber: String? = null,
    val meterPointId: Long? = null,
    val meterPointIdentifier: String? = null,
    val submissionDate: LocalDateTime? = null,
    val standardRead: Long? = null,
    val dayRead: Long? = null,
    val nightRead: Long? = null,
    val imageIdentifier: String? = null,
    val errorCode: String? = null,
    val status: MeterReadingPhotoEvidenceStatus? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val imageType: String? = null,
    val fuel: MeterReadingFuel? = null,
    val freshdeskTicketId: Long? = null,
    val caseId: String? = null,
    val outboundRequestId: Long? = null,
    val requestReason: String? = null,
) {
    companion object {
        fun fromJooq(jooq: JooqMeterReadingPhotoEvidence): MeterReadingPhotoEvidence =
            MeterReadingPhotoEvidence(
                id = jooq.id,
                accountNumber = jooq.accountNumber,
                meterPointId = jooq.meterPointId,
                meterPointIdentifier = jooq.meterPointIdentifier,
                submissionDate = jooq.submissionDate,
                standardRead = jooq.standardRead?.toLong(),
                dayRead = jooq.dayRead?.toLong(),
                nightRead = jooq.nightRead?.toLong(),
                imageIdentifier = jooq.imageIdentifier,
                errorCode = jooq.errorCode,
                status = jooq.status?.toSiblingEnum<MeterReadingPhotoEvidenceStatus>(),
                deleted = jooq.deleted,
                createdAt = jooq.createdAt,
                updatedAt = jooq.updatedAt,
                imageType = jooq.imageType,
                fuel = jooq.fuel?.toSiblingEnum<MeterReadingFuel>(),
                freshdeskTicketId = jooq.freshdeskTicketId,
                caseId = jooq.caseId,
                outboundRequestId = jooq.outboundRequestId
            )

        fun fromJooq(jooq: JooqMeterReadingPhotoEvidence, requestReason: String?): MeterReadingPhotoEvidence =
            MeterReadingPhotoEvidence(
                id = jooq.id,
                accountNumber = jooq.accountNumber,
                meterPointId = jooq.meterPointId,
                meterPointIdentifier = jooq.meterPointIdentifier,
                submissionDate = jooq.submissionDate,
                standardRead = jooq.standardRead?.toLong(),
                dayRead = jooq.dayRead?.toLong(),
                nightRead = jooq.nightRead?.toLong(),
                imageIdentifier = jooq.imageIdentifier,
                errorCode = jooq.errorCode,
                status = jooq.status?.toSiblingEnum<MeterReadingPhotoEvidenceStatus>(),
                deleted = jooq.deleted,
                createdAt = jooq.createdAt,
                updatedAt = jooq.updatedAt,
                imageType = jooq.imageType,
                fuel = jooq.fuel?.toSiblingEnum<MeterReadingFuel>(),
                freshdeskTicketId = jooq.freshdeskTicketId,
                caseId = jooq.caseId,
                outboundRequestId = jooq.outboundRequestId,
                requestReason = requestReason,
            )
    }
}

enum class MeterReadingPhotoEvidenceStatus(val value: String) {
    PENDING("Pending"),
    DONE("Done"),
    ERRORED("Errored")
}

enum class MeterReadingFuel {
    ELEC,
    GAS,
    DUAL,
}

fun MeterReadingPhotoEvidence.toJooq(): JooqMeterReadingPhotoEvidence =
    JooqMeterReadingPhotoEvidence(
        id = id,
        meterPointId = meterPointId,
        meterPointIdentifier = meterPointIdentifier,
        accountNumber = accountNumber,
        submissionDate = submissionDate,
        standardRead = standardRead?.toBigDecimal(),
        dayRead = dayRead?.toBigDecimal(),
        nightRead = nightRead?.toBigDecimal(),
        imageIdentifier = imageIdentifier,
        errorCode = errorCode,
        status = status?.toSiblingEnum<MeterReadingPhotoEvidenceStatu>(),
        deleted = deleted,
        createdAt = createdAt,
        updatedAt = updatedAt,
        imageType = imageType,
        fuel = fuel?.toSiblingEnum<JooqMeterReadingFuel>(),
        freshdeskTicketId = freshdeskTicketId,
        caseId = caseId,
        outboundRequestId = outboundRequestId
    )

fun List<MeterReadingPhotoEvidence>.toGetMeterReadingPhotoEvidenceResponse(): GetMeterReadingPhotoEvidenceResponse {
    val meterPointToMeterReadingPhotoEvidenceList =
        this.sortedByDescending { it.createdAt }
            .groupBy { it.freshdeskTicketId }

    val meterReadingPhotoEvidenceResponseBuilder = GetMeterReadingPhotoEvidenceResponse.newBuilder()

    meterPointToMeterReadingPhotoEvidenceList.forEach { (freshdeskTicketId, evidenceList) ->
        val meterReadingPhotoEvidenceByFreshdeskTicketIdBuilder =
            MeterReadingPhotoEvidenceByFreshdeskTicketId.newBuilder()
                .setFreshdeskTicketId(freshdeskTicketId!!)
                .addAllMeterReadingPhotoEvidences(evidenceList.map { it.toProtoMeterReadingPhotoEvidence() })

        meterReadingPhotoEvidenceResponseBuilder.addMeterReadingPhotoEvidences(
            meterReadingPhotoEvidenceByFreshdeskTicketIdBuilder.build()
        )
    }

    return meterReadingPhotoEvidenceResponseBuilder.build()
}

fun MeterReadingPhotoEvidence.toProtoMeterReadingPhotoEvidence(): ProtoMeterReadingPhotoEvidence = let { from ->
    return meterReadingPhotoEvidence {
        meterPointIdentifier = from.meterPointIdentifier.toNullableString()
        submissionDate = from.submissionDate.toNullableTimestamp()
        standardRead = from.standardRead.toNullableInt64()
        dayRead = from.dayRead.toNullableInt64()
        nightRead = from.nightRead.toNullableInt64()
        errorCode = from.errorCode.toNullableString()
        imageIdentifier = from.imageIdentifier!!
        if(from.fuel != null) fuel = from.fuel.toSiblingEnum<ProtoMeterReadingFuel>()
        requestReason = from.requestReason.toNullableString()
        createdAt = from.createdAt!!.toTimestamp()
    }
}

fun PhotoEvidenceMetadata.toMeterReadingPhotoEvidence(
    status: MeterReadingPhotoEvidenceStatus,
    outboundFuel: MeterReadingFuel? = null,
    outboundRequestId: Long? = null,
): MeterReadingPhotoEvidence =
    let { proto ->
        val nowLocalDateTime = LocalDateTime.now()
        MeterReadingPhotoEvidence(
            meterPointId = proto.meterPointId.getValueOrNull(),
            meterPointIdentifier = proto.meterPointIdentifier.getValueOrNull(),
            accountNumber = proto.accountNumber,
            submissionDate = proto.submissionDate.getValueOrNull()?.toLocalDateTime(),
            standardRead = proto.standardRead.getValueOrNull(),
            dayRead = proto.dayRead.getValueOrNull(),
            nightRead = proto.nightRead.getValueOrNull(),
            imageIdentifier = proto.imageIdentifier,
            errorCode = proto.errorCode.getValueOrNull(),
            status = status,
            createdAt = nowLocalDateTime,
            updatedAt = nowLocalDateTime,
            fuel = outboundFuel ?: if (proto.hasFuel()) proto.fuel.toSiblingEnum<MeterReadingFuel>() else null,
            outboundRequestId = outboundRequestId
        )
    }

fun FreshdeskMeterReadingPhotoEvidence.toMeterReadingPhotoEvidence(
    status: MeterReadingPhotoEvidenceStatus,
    accountNumber: String,
    outboundRequestId: Long? = null,
): MeterReadingPhotoEvidence =
    let { proto ->
        val nowLocalDateTime = LocalDateTime.now()
        MeterReadingPhotoEvidence(
            accountNumber = accountNumber,
            freshdeskTicketId = proto.freshdeskTicketId.getValueOrNull(),
            imageIdentifier = proto.imageIdentifier,
            status = status,
            createdAt = nowLocalDateTime,
            updatedAt = nowLocalDateTime,
            outboundRequestId = outboundRequestId
        )
    }
