package energy.so.assets.server.models

import energy.so.commons.json.serializers.LocalDateAsISO8601Serializer
import java.time.LocalDate
import kotlinx.serialization.Serializable

@Serializable
sealed interface AccountMeterReadingsRequestDto {
    val value: Long
    val readingDate: LocalDate
    val meterPointId: Long
    val userId: Long
    val billingAccountId: Long
    val unitType: UnitType
}

@Serializable
data class AccountMeterReadingsRequestDtoWithMTD(
    override val value: Long,
    @Serializable(LocalDateAsISO8601Serializer::class)
    override val readingDate: LocalDate,
    override val meterPointId: Long,
    override val userId: Long,
    override val billingAccountId: Long,
    override val unitType: UnitType,
    val meterId: Long,
    val registerId: Long,
) : AccountMeterReadingsRequestDto

@Serializable
data class AccountMeterReadingsRequestDtoWithoutMTD(
    override val value: Long,
    @Serializable(LocalDateAsISO8601Serializer::class)
    override val readingDate: LocalDate,
    override val meterPointId: Long,
    override val userId: Long,
    override val billingAccountId: Long,
    override val unitType: UnitType,
    val rateName: String,
) : AccountMeterReadingsRequestDto
