package energy.so.assets.server.models

import energy.so.assets.meterPoints.v2.meterPointHistory
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.MeterPointHistory as JooqMeterPointHistory

data class MeterPointHistory(
    val id: Long,
    val meterPointId: Long,
    val eventType: MeterPointEventType,
    val status: MeterPointSupplyStatus,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
) {

    companion object {
        fun fromJooq(pojo: JooqMeterPointHistory) = MeterPointHistory(
            id = pojo.id!!,
            meterPointId = pojo.meterPointId!!,
            eventType = pojo.eventType!!.toSiblingEnum(),
            status = pojo.status!!.toSiblingEnum(),
            deleted = pojo.deleted,
            createdAt = pojo.createdAt!!,
            updatedAt = pojo.updatedAt!!,
        )
    }

    fun toResponse() = let { from ->
        meterPointHistory {
            id = from.id
            meterPointId = from.meterPointId
            eventType = from.eventType.toSiblingEnum()
            supplyStatus = from.status.toSiblingEnum()
            deleted = from.deleted.toNullableTimestamp()
            createdAt = from.createdAt.toTimestamp()
            updatedAt = from.updatedAt.toTimestamp()
        }
    }
}

enum class MeterPointEventType {
    Created,
    ManualUpdate,
    RegistrationStarted,
    RegistrationRejected,
    RegistrationWithdrawRequested,
    RegistrationWithdrawRejected,
    RegistrationWithdrawn,
    RegistrationAccepted,
    RegistrationObjectionRaised,
    RegistrationObjectionRemoved,
    RegistrationObjectionUpheld,
    LossInitiated,
    LossObjectionRaised,
    LossObjectionRemoved,
    LossObjectionUpheld,
    RegistrationWithdrawnByNewSupplier,
    LossConfirmed,
    LossCompleted,
    RegistrationCancelled,
    RegistrationConfirmed,
    RegistrationCompleted,
    LossCancelled,
    RegistrationCancellationRequested,
    RegistrationCancellationRejected,
    LossObjectionRejected
}

enum class MeterPointSupplyStatus {
    NotSupplied,
    RegistrationRequested,
    RegistrationAccepted,
    RegistrationObjected,
    RegistrationWithdrawing,
    RegistrationWithdrawRejected,
    RegistrationConfirmed,
    Registered,
    LossNotified,
    LossObjected,
    LossConfirmed,
    RegistrationCancelling,
    RegistrationCancellationRejected
}
