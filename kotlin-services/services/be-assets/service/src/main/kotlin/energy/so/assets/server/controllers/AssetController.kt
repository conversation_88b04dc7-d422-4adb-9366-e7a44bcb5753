package energy.so.assets.server.controllers

import com.google.protobuf.Empty
import energy.so.assets.server.models.AssetType
import energy.so.assets.server.models.toProto
import energy.so.assets.server.services.AssetService
import energy.so.assets.server.services.MeterService
import energy.so.assets.v2.Asset
import energy.so.assets.v2.AssetsGrpcKt
import energy.so.assets.v2.AssetsResponse
import energy.so.assets.v2.CreateAssetDto
import energy.so.assets.v2.assetsResponse
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.dtos.IdRequestList
import io.opentelemetry.instrumentation.annotations.WithSpan

class AssetController(private val assetsService: AssetService, private val meterService: MeterService) :
    AssetsGrpcKt.AssetsCoroutineImplBase() {
    @WithSpan
    override suspend fun getAssets(request: IdRequestList): AssetsResponse {
        val metersMap = meterService.getMetersByIds(request.idRequestsList).associateBy { it.id }
        return assetsService.getAssetsByIds(request.idRequestsList)
            .map {
                when (it.type) {
                    AssetType.METER -> it.toProto(metersMap[it.id!!])
                    else -> it.toProto()
                }
            }
            .let { assetsResponse { assets.addAll(it) } }
    }

    @WithSpan
    override suspend fun getAsset(request: IdRequest): Asset =
        assetsService.getAssetById(request.id)
            ?.let {
                when (it.type) {
                    AssetType.METER -> it.toProto(meterService.getMeterById(it.id!!))
                    else -> it.toProto()
                }
            } ?: throw EntityNotFoundGrpcException(id = request.id, clazz = Asset::class)

    @WithSpan
    override suspend fun createAsset(request: CreateAssetDto): Asset {
        // TODO: Implement
        return Asset.newBuilder().setId(1).setType(energy.so.assets.v2.AssetType.METER).build()
    }

    @WithSpan
    override suspend fun deleteAsset(request: IdRequest): Empty {
        // TODO: Implement
        return Empty.getDefaultInstance()
    }
}
