package energy.so.assets.server.modules

import energy.so.assets.server.controllers.EnergyUsageController
import energy.so.assets.server.services.EnergyUsageService
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.session.SessionManager
import energy.so.customers.client.v2.accounts.AccountsClient
import org.koin.core.annotation.Module
import org.koin.dsl.module

@Module
object EnergyUsagesModule {

    private val customerServiceConfig = energy.so.assets.server.config.Constants.get<GrpcServiceConfig>("be-customers")

    val module = module {

        single { AccountsClient(config = customerServiceConfig) }

        single {
            EnergyUsageController(
                energyUsageService = get()
            )
        }

        // Services
        single {
            EnergyUsageService(
                accountsClient = get(),
                sessionManager = SessionManager,
            )
        }
    }
}
