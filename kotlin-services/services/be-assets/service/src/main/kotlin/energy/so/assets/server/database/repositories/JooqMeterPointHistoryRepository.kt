package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointHistory
import energy.so.commons.model.tables.references.METER_POINT_HISTORY
import org.jooq.DSLContext
import org.jooq.impl.DSL.asterisk
import energy.so.commons.model.tables.pojos.MeterPointHistory as JooqMeterPointHistory

class JooqMeterPointHistoryRepository(private val dslContext: DSLContext) : MeterPointHistoryRepository {
    override fun findLastByMeterPointId(meterPointId: Long): MeterPointHistory? {
        return dslContext.select(asterisk())
            .from(METER_POINT_HISTORY)
            .where(
                METER_POINT_HISTORY.METER_POINT_ID.eq(meterPointId),
                METER_POINT_HISTORY.DELETED.isNull
            )
            .orderBy(METER_POINT_HISTORY.CREATED_AT.desc())
            .limit(1)
            .fetchOneInto(JooqMeterPointHistory::class.java)
            ?.let { MeterPointHistory.fromJooq(it) }
    }
}
