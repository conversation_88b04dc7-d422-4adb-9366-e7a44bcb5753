package energy.so.assets.server.validators

import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.traverse
import arrow.core.validNel
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import org.koin.core.annotation.Single

/**
 * A validator that is responsible for:
 * - Ensuring the read does not have multiple decimal points.
 * - Ensuring the read is not a negative number.
 * - Ensuring the read is a number.
 *
 * <AUTHOR>
 */
@Single(createdAtStart = true)
class ReadFormatValidator : MeterReadingValidator {

    override suspend fun validate(subject: MeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> =
        when (subject) {
            is MeterReadingsWithoutTechnicalDetailsDto -> subject.registerReads.map { it.reading }
            is MeterReadingsWithTechnicalDetailsDto -> subject.readings.map { it.cumulative }
        }.traverse {
            if (it < 0) NegativeRead(subject.meterPointId, it).invalidNel() else validNel()
        }
}
