package energy.so.assets.server.services.accountState

import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.models.Meter
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingQuality
import energy.so.assets.server.models.MeterReadingSource
import energy.so.assets.server.models.ReadState
import energy.so.assets.server.models.isSmartMeter
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.MeterReadingApiService
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.search.pagination
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import java.time.LocalDate

private const val METER_READS_FILTER_DAYS = 31L
private const val DEFAULT_METER_READING_PAGE_SIZE = 10000

class ReadStateService(
    private val productAccountClient: ProductAccountsClient,
    private val meterPointService: MeterPointService,
    private val meterReadingsApiService: MeterReadingApiService,
) : AccountStateService<ReadState> {

    override suspend fun computeState(accountId: Long): ReadState {
        val meterPoints = getMeterPointsByAccount(accountId)
        val meterIdToReadsMap = getMeterReadsFromLastMonth(meterPoints)

        // this list contains active meters only
        val meters = meterPoints.map { it.meters }.flatten()

        if (allMetersHaveNonEstimatedReads(meters, meterIdToReadsMap)) {
            return ReadState.SUBMITTED_RECENTLY
        }

        val (smartMeters, manualMeters) = meters.partition { it.isSmartMeter() }

        val smartMeterIds = smartMeters.map { it.id!! }
        val manualMetersIds = manualMeters.map { it.id!! }

        // manual meter which has no non-estimated read
        // no smart meter or (smart meter without SMR read)
        return when {
            nonEstimatedReadIsMissing(meterIdToReadsMap, manualMetersIds) && !smartReadIsMissing(
                meterIdToReadsMap,
                smartMeterIds
            ) -> ReadState.MISSING_MANUAL_READING

            smartReadIsMissing(meterIdToReadsMap, smartMeterIds) -> ReadState.MISSING_SMART_READING

            else -> ReadState.MISSING_SMART_READING
        }
    }

    private suspend fun getMeterPointsByAccount(accountId: Long) =
        productAccountClient.getProductAccountsByBillingAccountId(idRequest { id = accountId })
            .productAccountsList
            .flatMap { it.agreementsList }
            .flatMap { it.meterPointsList }
            .map { it.id }
            .let { meterPointService.getMeterPointsByIds(it) }

    private fun List<MeterReading>.getNonEstimatedReads(): List<MeterReading> =
        this.filter { it.quality != MeterReadingQuality.ESTIMATED }

    private fun List<MeterReading>.getSMRReads(): List<MeterReading> =
        this.filter { it.source == MeterReadingSource.SMR }

    private fun allMetersHaveNonEstimatedReads(
        meters: List<Meter>,
        meterIdToReadsMap: Map<Long, List<MeterReading>>,
    ): Boolean {
        val meterIdToNonEstimatedReadsMap = getMeterIdToNonEstimatedReadsMap(meterIdToReadsMap)

        return meterIdToNonEstimatedReadsMap.keys.containsAll(meters.map { it.id })
    }

    private suspend fun getMeterReadsFromLastMonth(
        meterPoints: List<MeterPoint>,
    ): Map<Long, List<MeterReading>> {
        val now = LocalDate.now()

        return meterReadingsApiService.search(
            meterReadingsSearchRequest {
                meterPointId.addAll(meterPoints.map { it.id!! })
                startDate = now.minusDays(METER_READS_FILTER_DAYS).toTimestamp()
                endDate = now.toTimestamp()
                pagination = pagination {
                    pageSize = DEFAULT_METER_READING_PAGE_SIZE
                    pageNumber = 1
                }
            }
        )
            .groupBy { it.meterId!! }
    }

    private fun smartReadIsMissing(
        meterIdToReadsMap: Map<Long, List<MeterReading>>,
        smartMeterIds: List<Long>,
    ): Boolean {
        return smartMeterIds.isNotEmpty() && !getMeterToSMRReads(meterIdToReadsMap).keys.containsAll(smartMeterIds)
    }

    private fun nonEstimatedReadIsMissing(
        meterIdToReadsMap: Map<Long, List<MeterReading>>,
        meterIds: List<Long>,
    ) = meterIds.isNotEmpty() && !getMeterIdToNonEstimatedReadsMap(meterIdToReadsMap).keys.containsAll(meterIds)

    private fun getMeterIdToNonEstimatedReadsMap(meterIdToReadsMap: Map<Long, List<MeterReading>>) =
        meterIdToReadsMap.mapValues { it.value.getNonEstimatedReads() }.filter { it.value.isNotEmpty() }

    private fun getMeterToSMRReads(meterIdToReadsMap: Map<Long, List<MeterReading>>) =
        meterIdToReadsMap.mapValues { it.value.getSMRReads() }.filter { it.value.isNotEmpty() }
}
