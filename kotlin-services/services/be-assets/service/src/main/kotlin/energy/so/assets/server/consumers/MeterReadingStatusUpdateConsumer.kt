package energy.so.assets.server.consumers

import energy.so.assets.server.services.MeterReadingSubmissionRequestService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

class MeterReadingStatusUpdateConsumer(
    private val meterReadingSubmissionRequestService: MeterReadingSubmissionRequestService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug { "Received message: ${message.data}" }

        meterReadingSubmissionRequestService.updateMeterReadingJuniferStatus()

        logger.debug { "Processed meter reading status update" }
    }
}
