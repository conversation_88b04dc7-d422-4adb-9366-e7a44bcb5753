package energy.so.assets.server.extensions

import energy.so.ac.junifer.v1.assets.JuniferSubmitMeterReadingResponse
import energy.so.ac.junifer.v1.assets.SubmitMeterReadingValidationError
import energy.so.assets.meterReadings.v2.AccountReadingWithTechnicalDetails
import energy.so.assets.meterReadings.v2.AccountReadingWithoutTechnicalDetails
import energy.so.assets.meterReadings.v2.MeterReading
import energy.so.assets.meterReadings.v2.MeterReadingQuality.Quality
import energy.so.assets.meterReadings.v2.MeterReadingSequenceType
import energy.so.assets.meterReadings.v2.MeterReadingSource
import energy.so.assets.meterReadings.v2.MeterReadingStatusMessage
import energy.so.assets.meterReadings.v2.MeterReadingUnit
import energy.so.assets.meterReadings.v2.MeterReadingUnitType
import energy.so.assets.meterReadings.v2.MeterReadingWorkflowStatusMessage
import energy.so.assets.meterReadings.v2.SubmitAccountMeterReadingError
import energy.so.assets.meterReadings.v2.SubmitAccountReadingWithTechnicalDetailsRequest
import energy.so.assets.meterReadings.v2.SubmitAccountReadingWithoutTechnicalDetailsRequest
import energy.so.assets.meterReadings.v2.SubmitMeterReadingError
import energy.so.assets.meterReadings.v2.SubmitMeterReadingList
import energy.so.assets.meterReadings.v2.SubmitMeterReadingRequest
import energy.so.assets.meterReadings.v2.SubmitMoveInMeterReadingList
import energy.so.assets.meterReadings.v2.meterReading
import energy.so.assets.meterReadings.v2.submitAccountMeterReadingError
import energy.so.assets.meterReadings.v2.submitAccountMeterReadingResponse
import energy.so.assets.meterReadings.v2.submitMeterReadingError
import energy.so.assets.meterReadings.v2.submitMeterReadingResponse
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithMTD
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithoutMTD
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.MeterPointType
import energy.so.assets.server.models.MeterReadings
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.MeterReadingsWithTechnicalDetailsDto
import energy.so.assets.server.models.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.assets.server.models.Read
import energy.so.assets.server.models.ReadingType
import energy.so.assets.server.models.Register
import energy.so.assets.server.models.SequenceType
import energy.so.assets.server.models.SubmitMeterReadingsResult
import energy.so.assets.server.models.UnitType
import energy.so.assets.server.models.toResponse
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toNullableTimestamp
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toOffsetDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.tables.interfaces.IMeterReading
import energy.so.commons.search.model.mapping.nonNullDbToConsumer
import energy.so.commons.search.model.mapping.nullableDbToConsumer
import java.time.OffsetDateTime

fun IMeterReading.toProtobufModel(register: Register): MeterReading = let { recordPojo ->
    meterReading {
        nonNullDbToConsumer("id", ::id::set, recordPojo::id)
        nonNullDbToConsumer("meterPointId", ::meterPointId::set) { recordPojo.meterPointId }
        nonNullDbToConsumer("meterId", ::meterId::set) { recordPojo.meterId.toNullableInt64() }
        nonNullDbToConsumer("register", ::register::set) { register.toResponse() }
        nonNullDbToConsumer("readingDttm", ::readingDttm::set) { recordPojo.readingDttm?.toTimestamp() }
        nonNullDbToConsumer("fromDttm", ::fromDttm::set) { recordPojo.fromDttm?.toTimestamp().toNullableTimestamp() }
        nonNullDbToConsumer("quality", ::quality::set) { recordPojo.quality?.toSiblingEnum<Quality>() }
        nonNullDbToConsumer(
            "sequenceType",
            ::sequenceType::set
        ) { recordPojo.sequenceType?.toSiblingEnum<MeterReadingSequenceType>() }
        nonNullDbToConsumer("source", ::source::set) { recordPojo.source?.toSiblingEnum<MeterReadingSource>() }
        nonNullDbToConsumer(
            "status",
            ::status::set
        ) { recordPojo.status?.toSiblingEnum<MeterReadingStatusMessage.MeterReadingStatus>() }
        nonNullDbToConsumer("unit", ::unit::set) { recordPojo.unit?.toSiblingEnum<MeterReadingUnit>() }
        nonNullDbToConsumer(
            "workflowStatus",
            ::workflowStatus::set
        ) { recordPojo.workflowStatus?.toSiblingEnum<MeterReadingWorkflowStatusMessage.MeterReadingWorkflowStatus>() }
        nonNullDbToConsumer("cumulative", ::cumulative::set) { recordPojo.cumulative!!.toDouble() }
        nonNullDbToConsumer("consumption", ::consumption::set) { recordPojo.consumption?.toDouble().toNullableDouble() }
        nonNullDbToConsumer("rateName", ::rateName::set) { recordPojo.rateName.toNullableString() }
        nonNullDbToConsumer(
            "pendingRegisterReadingId",
            ::pendingRegisterReadingId::set
        ) { recordPojo.pendingRegisterReadingId.toNullableString() }
        nullableDbToConsumer(::deleted::set) { recordPojo.deleted?.toNullableTimestamp() }
        nonNullDbToConsumer("createdAt", ::createdAt::set) { recordPojo.createdAt?.toNullableTimestamp() }
        nonNullDbToConsumer("updatedAt", ::updatedAt::set) { recordPojo.updatedAt?.toNullableTimestamp() }
    }
}

fun SubmitMeterReadingList.toMeterReadingsMap(ignoreMeterWarnings: Boolean): Map<Long, MeterReadingsRequestDto> =
    let { submitRequest ->
        submitRequest.metersList.groupBy { it.meterPointId }
            .mapValues { (_, v) ->
                when (v.first().hasReceivedTechnicalDetails()) {
                    true -> v.toMeterReadingsWithTechnicalDetails(userId, billingAccountId, ignoreMeterWarnings)
                    false -> v.toMeterReadingsWithoutTechnicalDetails(userId, billingAccountId, ignoreMeterWarnings)
                }
            }
    }

fun SubmitMoveInMeterReadingList.toMeterReadingsMap(ignoreMeterWarnings: Boolean): Map<Long, MeterReadingsRequestDto> =
    let { submitRequest ->
        submitRequest.metersList.groupBy { it.meterPointId }
            .mapValues { (_, v) ->
                v.toMoveInMeterReadings(ignoreMeterWarnings)
            }
    }

fun SubmitMeterReadingList.toJuniferMeterReadingWithTechnicalDetails(): energy.so.commons.grpc.clients.junifer.dtos.MeterReadingsWithTechnicalDetailsDto =
    let { submitMeterReadingList ->
        submitMeterReadingList.metersList.first().let {
            energy.so.commons.grpc.clients.junifer.dtos.MeterReadingsWithTechnicalDetailsDto(
                meterPointId = it.meterPointId,
                ignoreWarnings = true,
                readings = listOf(
                    energy.so.commons.grpc.clients.junifer.dtos.MeterReadings(
                        meterIdentifier = it.meterIdentifier.value,
                        registerIdentifier = it.registerIdentifier.getValueOrNull(),
                        readingDateTime = it.readingAt.toOffsetDateTime(),
                        sequenceType = energy.so.commons.grpc.clients.junifer.dtos.SequenceType.Normal,
                        source = "Customer",
                        quality = "MANUAL",
                        cumulative = it.units,
                        unitType = it.unitType.toSiblingEnum(),
                        validateOnly = it.validateOnly
                    )
                )
            )
        }
    }

private fun SubmitMeterReadingRequest.hasReceivedTechnicalDetails() =
    !this.meterIdentifier.hasNull()

private fun List<SubmitMeterReadingRequest>.toMoveInMeterReadings(
    ignoreMeterWarnings: Boolean,
) =
    let { readings ->
        MeterReadingsWithTechnicalDetailsDto(
            meterPointId = readings[0].meterPointId,
            ignoreWarnings = ignoreMeterWarnings,
            validateOnly = readings[0].validateOnly,
            readings = readings.map { it.toMeterReadings() }
        )
    }

private fun List<SubmitMeterReadingRequest>.toMeterReadingsWithTechnicalDetails(
    userId: Long,
    billingAccountId: Long,
    ignoreMeterWarnings: Boolean,
) =
    let { readings ->
        MeterReadingsWithTechnicalDetailsDto(
            meterPointId = readings[0].meterPointId,
            userId = userId,
            billingAccountId = billingAccountId,
            ignoreWarnings = ignoreMeterWarnings,
            validateOnly = readings[0].validateOnly,
            readings = readings.map { it.toMeterReadings() }
        )
    }

private fun List<SubmitMeterReadingRequest>.toMeterReadingsWithoutTechnicalDetails(
    userId: Long,
    billingAccountId: Long,
    ignoreMeterWarnings: Boolean,
) =
    let { readings ->
        val hasNightReading = readings.any { it.unitType == MeterReadingUnitType.ELECTRICITY_NIGHT }

        MeterReadingsWithoutTechnicalDetailsDto(
            meterPointId = readings[0].meterPointId,
            userId = userId,
            billingAccountId = billingAccountId,
            ignoreWarnings = ignoreMeterWarnings,
            readingDate = readings[0].readingAt.toLocalDate(),
            source = "Customer",
            quality = "NORMAL",
            sequenceType = SequenceType.First,
            registerReads = readings.map { it.toRead(hasNightReading) }
        )
    }

private fun SubmitMeterReadingRequest.toMeterReadings() = let { request ->
    MeterReadings(
        registerId = request.registerId.getValueOrNull(),
        meterIdentifier = request.meterIdentifier.value,
        registerIdentifier = if (request.hasRegisterIdentifier()) request.registerIdentifier.value else "",
        readingDateTime = this.readingAt.toLocalDateTime(),
        source = "Customer",
        quality = "MANUAL",
        cumulative = request.units,
        unitType = request.unitType.toSiblingEnum(),
    )
}

private fun SubmitMeterReadingRequest.toRead(isMultiRateMeter: Boolean) = let { request ->
    val unitType = request.unitType.toSiblingEnum<UnitType>()

    val readType = when (isMultiRateMeter) {
        true -> if (unitType == UnitType.Electricity_Night) {
            ReadingType.Night
        } else ReadingType.Day

        false -> ReadingType.Standard
    }

    Read(
        reading = request.units,
        readingType = readType,
        unitType = unitType
    )
}

fun SubmitAccountReadingWithTechnicalDetailsRequest.toAccountMeterReadingsRequestDtoWithMTD(
    meterPointToMeter: List<Pair<MeterPoint, List<Long>>>,
): List<AccountMeterReadingsRequestDtoWithMTD> =
    readingsList.map { it.toAccountMeterReadingsRequestDtoWithMTD(meterPointToMeter, userId, billingAccountId) }

fun SubmitAccountReadingWithoutTechnicalDetailsRequest.toAccountMeterReadingsRequestDtoWithoutMTD(
    meterPoints: List<MeterPoint>,
): List<AccountMeterReadingsRequestDtoWithoutMTD> =
    readingsList.map { it.toAccountMeterReadingsRequestDtoWithoutMTD(meterPoints, userId, billingAccountId) }

fun AccountReadingWithTechnicalDetails.toAccountMeterReadingsRequestDtoWithMTD(
    meterPointToMeter: List<Pair<MeterPoint, List<Long>>>,
    userId: Long,
    billingAccountId: Long,
): AccountMeterReadingsRequestDtoWithMTD {
    val meterPoint = meterPointToMeter.first { it.second.contains(meterId) }.first

    return AccountMeterReadingsRequestDtoWithMTD(
        value = value,
        readingDate = readingDate.toLocalDate(),
        meterPointId = meterPoint.id!!,
        userId = userId,
        billingAccountId = billingAccountId,
        meterId = meterId,
        registerId = registerId,
        unitType = if (meterPoint.type == MeterPointType.MPRN) UnitType.Gas else UnitType.Electricity
    )
}

fun AccountReadingWithoutTechnicalDetails.toAccountMeterReadingsRequestDtoWithoutMTD(
    meterPoints: List<MeterPoint>,
    userId: Long,
    billingAccountId: Long,
): AccountMeterReadingsRequestDtoWithoutMTD {
    val meterPoint = meterPoints.first { it.id == meterpointId }

    return AccountMeterReadingsRequestDtoWithoutMTD(
        value = value,
        readingDate = readingDate.toLocalDate(),
        meterPointId = meterPoint.id!!,
        userId = userId,
        billingAccountId = billingAccountId,
        rateName = readingType.name,
        unitType = if (meterPoint.type == MeterPointType.MPRN) UnitType.Gas else UnitType.Electricity
    )
}

fun SubmitMeterReadingRequest.toResponseProtobuf(errorMessage: String) = let { request ->
    submitMeterReadingError {
        code = ErrorCodes.COMMONS_UNHANDLED_ERROR // unknown exception error code
        meterIdentifier = request.meterIdentifier
        registerIdentifier = request.registerIdentifier
        message = errorMessage
    }
}

fun AccountReadingWithTechnicalDetails.toSubmitAccountMeterReadingErrorProtobuf(
    errorCode: String,
    errorMessage: String,
) = let { request ->
    submitAccountMeterReadingError {
        this.errorCode = errorCode
        message = errorMessage
        registerId = request.registerId.toNullableInt64()
    }
}

val submitMeterReadingSuccessResponse = submitMeterReadingResponse {
    success = true
}

val submitAccountMeterReadingSuccessResponse = submitAccountMeterReadingResponse {
    success = true
}

fun List<SubmitMeterReadingError>.toErrorResponseProtobuf() = let { errors ->
    submitMeterReadingResponse {
        success = errors.isEmpty()
        error.addAll(errors)
    }
}

fun List<SubmitAccountMeterReadingError>.toErrorResponseProtobuf() = let { errors ->
    submitAccountMeterReadingResponse {
        success = errors.isEmpty()
        error.addAll(errors)
    }
}

fun JuniferSubmitMeterReadingResponse.toSubmitMeterReadingErrorResult(
    meterPointId: Long,
    submissionTime: OffsetDateTime,
) = SubmitMeterReadingsResult(
    meterPointId,
    submissionTime,
    false,
    this.validationErrorsList.map { it.toSubmitMeterReadingError() }
)

private fun SubmitMeterReadingValidationError.toSubmitMeterReadingError() =
    energy.so.assets.server.models.SubmitMeterReadingError(
        mapErrorCode(this.errorDetail.errorCode),
        this.errorDetail.errorDescription.getValueOrNull(),
        this.meterIdentifier,
        this.registerIdentifier
    )

private fun mapErrorCode(juniferErrorCode: String): String = when (juniferErrorCode) {
    // TODO(SO-12203)
    "ReadValidation.MeterRegisterConfigPeriodMissing" -> "meter_register_config_period_missing"
    "ReadValidation.EarlierReadingExists" -> "earlier_reading_exists"
    "ReadValidation.ReadingInFuture" -> "reading_in_future"
    "ReadValidation.MultipleWarnings" -> "multiple_warnings"
    "ReadValidation.NotSupplier" -> "not_on_supply"
    "ReadValidation.NoMeterPointTimeSeries" -> "no_meterpoint_time_series"
    "ReadValidation.FirstReadTooFarFromSSD" -> "first_read_too_far_from_ssd"
    "ReadValidation.LastReadTooFarFromSED" -> "last_read_too_far_from_sed"
    "ReadValidation.SEDNotConfirmed" -> "sed_not_confirmed"
    "ReadValidation.DuplicateReadingDate" -> "duplicate_reading_date"
    "ReadValidation.ReadingDttmHasTime" -> "reading_date_has_time"
    "ReadValidation.ReadingDttmNotValid" -> "reading_date_not_valid"
    "ReadValidation.ReadingDttmNotAligned" -> "reading_date_not_aligned"
    "ReadValidation.Negative" -> "reading_date_not_aligned"
    "ReadValidation.TooLarge" -> "reading_too_large"
    "ReadValidation.InvalidNumberFormat" -> "invalid_number"
    "ReadValidation.InvalidDecimalPlaces" -> "invalid_decimal_places"
    "ReadValidation.NoPreviousReading" -> "no_previous_reading"
    "ReadValidation.ZeroConsumption" -> "zero_consumption"
    "ReadValidation.ReadingLowerThanLastCustomer" -> "reading_lower_than_last_customer"
    "ReadValidation.ReadingLowerThanOpeningRead" -> "reading_lower_than_opening_read"
    "ReadValidation.ReadingLowerThanLastActual" -> "reading_lower_than_last_actual"
    "ReadValidation.ConsumptionLessThanLowerThreshold" -> "consumption_less_than_lower_threshold"
    "ReadValidation.ConsumptionHigherThanUpperThreshold" -> "consumption_higher_than_upper_threshold"
    "ReadValidation.GeneralReadingError" -> "general_read_error"
    "ReadingDtBeforeSSD" -> "reading_date_before_ssd"
    "InvalidQuality" -> "invalid_quality"
    "InvalidSource" -> "invalid_source"
    "MTDsHaveArrived" -> "meter_technical_details_have_arrived"
    "MeterNotAttachedToMeterPoint" -> "meter_not_attached_to_meter_point"
    "MeterMissingEACAQ" -> "meter_missing_EAC_or_AQ"
    "MeterMissingEUC" -> "meter_point_missing_EUC"
    "ReadingMismatch" -> "reading_details_mismatch"
    "MeterHasNoRegisters" -> "meter_has_no_register_configured"
    "RequiredRegisterIdentifierMissing" -> "register_identifier_missing_for_multiple_registers"
    "InvalidRegisterIdentifier" -> "invalid_register_identifier"
    "RegisterNotConnectedToMeterPoint" -> "register_not_connected_to_meter_point"

    else -> "unknown_junifer_validation_error"
}
