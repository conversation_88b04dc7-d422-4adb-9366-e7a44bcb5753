package energy.so.assets.server.modules

import energy.so.assets.server.controllers.PropertiesController
import energy.so.assets.server.database.repositories.AddressRepository
import energy.so.assets.server.database.repositories.JooqAddressRepository
import energy.so.assets.server.database.repositories.JooqPropertyRepository
import energy.so.assets.server.database.repositories.PropertyRepository
import energy.so.assets.server.services.DefaultPropertiesService
import energy.so.assets.server.services.PropertiesService
import energy.so.assets.sync.repository.JooqPropertyEntityRepository
import energy.so.assets.sync.repository.PropertyEntityRepository
import energy.so.commons.model.tables.records.PropertyRecord
import energy.so.commons.model.tables.references.PROPERTY
import energy.so.commons.search.service.SearchService
import energy.so.commons.search.service.impl.DatabaseSearchService
import energy.so.customers.client.v2.accounts.RestrictToUserAccounts
import org.koin.dsl.module

object PropertiesModule {
    val module = module {
        single {
            PropertiesController(
                propertiesService = get(),
                searchService = get(),
                accountsClient = get(),
            )
        }

        single<PropertiesService> {
            DefaultPropertiesService(propertyRepository = get())
        }

        single<PropertyRepository> {
            JooqPropertyRepository(
                dslContext = get(),
                addressRepository = get()
            )
        }

        single<AddressRepository> {
            JooqAddressRepository(dslContext = get())
        }

        single {
            RestrictToUserAccounts(accountsClient = get())
        }

        single<SearchService<PropertyRecord>> {
            DatabaseSearchService(
                context = get(),
                table = PROPERTY,
                securityFilterFn = get<RestrictToUserAccounts>()
            )
        }

        single<PropertyEntityRepository> {
            JooqPropertyEntityRepository(
                dslContext = get()
            )
        }
    }
}
