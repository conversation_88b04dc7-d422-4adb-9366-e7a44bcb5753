package energy.so.assets.server.services

import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.getCustomerConsentsRequest
import energy.so.ac.junifer.v1.customers.updateCustomerConsentRequestV2
import energy.so.assets.meterPoints.v2.MeterIdToRegistersResponse
import energy.so.assets.meterPoints.v2.MeterPointsResponse
import energy.so.assets.meterPoints.v2.MpxnSmartPayAsYouGoEligibility
import energy.so.assets.meterPoints.v2.MpxnSmartPayAsYouGoEligibilityResponse
import energy.so.assets.meterPoints.v2.SmartMeterEvTariffEligibilityResponse
import energy.so.assets.meterPoints.v2.meterIdToRegister
import energy.so.assets.meterPoints.v2.meterIdToRegistersResponse
import energy.so.assets.meterPoints.v2.meterPointStructureRegister
import energy.so.assets.meterPoints.v2.meterPointsResponse
import energy.so.assets.meterPoints.v2.mpxnSmartPayAsYouGoEligibility
import energy.so.assets.meterPoints.v2.mpxnSmartPayAsYouGoEligibilityResponse
import energy.so.assets.meterPoints.v2.smartMeterEvTariffEligibilityResponse
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.models.ConsentMeterType
import energy.so.assets.server.models.DccServiceStatus
import energy.so.assets.server.models.EstimatedUsage
import energy.so.assets.server.models.FrequencyConsentDto
import energy.so.assets.server.models.FrequencyConsentUpdateDto
import energy.so.assets.server.models.FrequencyDTO
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.UkProfileClass
import energy.so.assets.server.models.hasSmartPayAsYouGo
import energy.so.assets.server.models.isExportType
import energy.so.assets.server.models.isSmartMeter
import energy.so.assets.server.models.toFrequencyEnum
import energy.so.assets.server.models.toJuniferFormattedString
import energy.so.assets.server.models.toModelList
import energy.so.assets.server.models.toProtobuf
import energy.so.assets.server.services.SmartMeterEligibilityCheckError.ECO_7_METER
import energy.so.assets.server.services.SmartMeterEligibilityCheckError.NOT_DCC_ACTIVE
import energy.so.assets.server.services.SmartMeterEligibilityCheckError.NO_METER_POINT_FOUND
import energy.so.assets.server.services.SmartMeterEligibilityCheckError.NO_SMART_METER
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.NotFoundGrpcException
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.logging.TraceableLogging
import java.time.LocalDate
import kotlin.streams.toList

private val logger = TraceableLogging.logger {}

private enum class SmartMeterEligibilityCheckError {
    NO_METER_POINT_FOUND,
    NO_SMART_METER,
    NOT_DCC_ACTIVE,
    ECO_7_METER
}

class DefaultMeterPointService(
    private val meterPointsRepository: MeterPointsRepository,
    private val registerRepository: RegisterRepository,
    private val acAssetsSyncClient: AcAssetsClient,
    private val customersSyncClient: CustomersSyncClient,
) : MeterPointService {

    override suspend fun getMeterPointsByAssetIds(assetIds: List<Long>) =
        meterPointsRepository.findAllByAssetIds(assetIds)

    override suspend fun getEstimatedUsage(meterPointId: Long): List<EstimatedUsage> =
        acAssetsSyncClient.getMeterPointEstimatedUsage(
            meterPointId
        ).toModelList(meterPointId)

    override suspend fun getMeterPointsByAddressIds(addressIds: List<Long>): List<MeterPoint> =
        meterPointsRepository.findMeterPointsByAddressIds(addressIds)

    override suspend fun getMeterPointsByIds(ids: List<Long>): List<MeterPoint> {
        return meterPointsRepository.findByIds(ids)
    }

    override suspend fun getMeterPointsByMeterIds(ids: List<Long>): List<MeterPoint> =
        meterPointsRepository.findByMeterIds(ids)

    override suspend fun getMeterIdToRegisters(meterPointId: Long): MeterIdToRegistersResponse {
        val registers = meterPointsRepository.findById(meterPointId).meters.stream()
            .mapToLong { it.id!! }
            .toList()
            .let { registerRepository.findByMeterIds(it.toSet()) }

        val meterIdToRegisters = registers.groupBy { it.meterId }
            .map { entry ->
                meterIdToRegister {
                    this.meterId = entry.key
                    this.registers.addAll(
                        entry.value
                            .map {
                                meterPointStructureRegister {
                                    id = it.id
                                    identifier = it.identifier.toNullableString()
                                    digits = it.digits
                                    decimalPlaces = it.decimalPlaces
                                    registerType = it.type.toNullableString()
                                    rateName = it.rateName.toNullableString()
                                }
                            }
                            .toList()
                    )
                }
            }

        return meterIdToRegistersResponse {
            response.addAll(meterIdToRegisters)
        }
    }

    override suspend fun getMeterPointByMPXN(mpxn: String): MeterPoint? {
        return meterPointsRepository.findByMPXN(mpxn)
    }

    override suspend fun checkSmartMeterEvTariffEligibility(identifier: String): SmartMeterEvTariffEligibilityResponse {
        val meterPoint = meterPointsRepository.findByIdentifier(identifier)

        return smartMeterEvTariffEligibilityResponse {
            when {
                meterPoint == null -> {
                    isEligible = false
                    errorCode = NO_METER_POINT_FOUND.name.toNullableString()
                }

                !meterPoint.hasSmartMeter() -> {
                    isEligible = false
                    errorCode = NO_SMART_METER.name.toNullableString()
                }

                !meterPoint.isDccActive() -> {
                    isEligible = false
                    errorCode = NOT_DCC_ACTIVE.name.toNullableString()
                }

                meterPoint.isEco7() -> {
                    isEligible = false
                    errorCode = ECO_7_METER.name.toNullableString()
                }

                else -> {
                    isEligible = true
                }
            }
        }
    }

    override suspend fun getMeterPointsByMPXNs(mpxnIdentifiers: List<String>): MeterPointsResponse {
        return meterPointsResponse {
            meterPoints.addAll(meterPointsRepository.findByMPXNs(mpxnIdentifiers).toProtobuf())
        }
    }

    override suspend fun checkMpxnSmartPayAsYouGoEligibility(
        meterPointIds: List<Long>,
    ): MpxnSmartPayAsYouGoEligibilityResponse {
        val meterPoints = meterPointsRepository.findByIds(meterPointIds)

        val checkedMpxns = meterPointIds.map { meterPointId ->
            meterPoints.find { it.id == meterPointId }?.let { evaluateSmartPasAsYouGoEligibility(it) }
                ?: createMpxnSmartPayAsYouGoEligibility(
                    reason = "MeterPoint id: $meterPointId is not found",
                    isEligible = false
                )
        }

        return mpxnSmartPayAsYouGoEligibilityResponse {
            mpxnSmartPayAsYouGoEligibility.addAll(checkedMpxns)
        }
    }

    private suspend fun evaluateSmartPasAsYouGoEligibility(meterPoint: MeterPoint): MpxnSmartPayAsYouGoEligibility {
        return when {
            !meterPoint.isDccActive() -> createMpxnSmartPayAsYouGoEligibility(
                identifier = meterPoint.identifier,
                reason = "DCC is not enrolled",
                isEligible = false
            )

            !meterPoint.hasSmartPayAsYouGo() -> createMpxnSmartPayAsYouGoEligibility(
                identifier = meterPoint.identifier,
                reason = "Smart Meter do not support PAYG",
                isEligible = false
            )

            !acAssetsSyncClient.hasSmartMeterSentReadingsSinceLastMonth(
                meterPoint.id!!
            ) -> createMpxnSmartPayAsYouGoEligibility(
                identifier = meterPoint.identifier,
                reason = "Smart Meter has not sent any meter readings since last month",
                isEligible = false
            )

            meterPoint.isExportType() -> createMpxnSmartPayAsYouGoEligibility(
                identifier = meterPoint.identifier,
                reason = "Export meterpoints are not eligible for Smart Pay As You Go",
                isEligible = false
            )

            else -> createMpxnSmartPayAsYouGoEligibility(identifier = meterPoint.identifier, isEligible = true)
        }
    }

    private fun createMpxnSmartPayAsYouGoEligibility(
        identifier: String? = null,
        reason: String? = null,
        isEligible: Boolean,
    ) = mpxnSmartPayAsYouGoEligibility {
        identifier?.let { mpxnIdentifier = it.toNullableString() }
        this.isEligible = isEligible
        reason?.let { ineligibleReason = reason.toNullableString() }
    }

    override suspend fun updateSmartMeterReadingFrequency(frequencyConsents: List<FrequencyConsentUpdateDto>) {
        frequencyConsents.map { consent ->
            val meterPoint = meterPointsRepository.findByIdentifier(consent.mpxn)

            if (meterPoint != null && meterPoint.id.toString().isNotEmpty()) {
                val currentConsent = getCurrentConsent(meterPoint)
                if (!isFrequencyAlreadySet(currentConsent.frequency, consent.frequency)) {
                    updateCustomerConsentsV2(
                        meterPoint.id.toString(),
                        consent.frequency,
                        consent.type,
                        consent.fromDt,
                        currentConsent.frequency,
                    )
                }
            } else {
                logger.error { "Unable to get meterPoint for mpxn : ${consent.mpxn}" }
                throw NotFoundGrpcException(
                    errorCode = ErrorCodes.COMMONS_ENTITY_NOT_FOUND,
                    errorCategory = ErrorCategories.ASSETS,
                    message = "Sorry, we couldn't find the meterPoint associated with mpxn ${consent.mpxn}"
                )
            }
        }
    }

    override suspend fun getCurrentSmartMeterReadingFrequency(
        mpxns: List<String>,
        validDt: LocalDate?,
    ): List<FrequencyConsentDto> {
        val consentList = mpxns.map { mpxn ->
            val meterPoint = meterPointsRepository.findByIdentifier(mpxn)
            if (meterPoint?.id != null) {
                getCurrentConsent(meterPoint, validDt)
            } else {
                logger.error("Unable to get meterPoint for mpxn : $mpxn")
                throw NotFoundGrpcException(
                    errorCode = ErrorCodes.COMMONS_ENTITY_NOT_FOUND,
                    errorCategory = ErrorCategories.ASSETS,
                    message = "Sorry, we couldn't find the meterPoint associated with mpxn $mpxn"
                )
            }
        }

        return consentList
    }

    private suspend fun getCurrentConsent(
        meterPoint: MeterPoint,
        validDt: LocalDate? = null,
    ): FrequencyConsentDto {
        requireNotNull(meterPoint.id)

        val consents = customersSyncClient.getCustomerConsentsV2(
            getCustomerConsentsRequest {
                this.meterPointId = meterPoint.id
                if (validDt != null) this.validDt = validDt.toNullableTimestamp()
            }
        ).consentsList.filter { cd ->
            "MHHS Billing" == cd.consentDefinition || "MPRN Billing" == cd.consentDefinition
        }.toList()
        return if (consents.isEmpty()) {
            defaultFrequencyConsentResponse(meterPointId = meterPoint.id.toString(), mpxn = meterPoint.identifier)
        } else {
            frequencyConsentResponse(
                mpxn = meterPoint.identifier,
                meterPoint = meterPoint.id.toString(),
                fromDt = consents[0].fromDt.toLocalDate(),
                freq = consents[0].setting.toFrequencyEnum()
            )
        }
    }

    private fun defaultFrequencyConsentResponse(mpxn: String, meterPointId: String) = FrequencyConsentDto(
        meterPointId = meterPointId,
        frequency = FrequencyDTO.MONTHLY,
        mpxn = mpxn,
        fromDt = null,
    )

    private fun frequencyConsentResponse(mpxn: String, meterPoint: String, fromDt: LocalDate?, freq: FrequencyDTO) =
        FrequencyConsentDto(
            meterPointId = meterPoint,
            frequency = freq,
            mpxn = mpxn,
            fromDt = fromDt,
        )

    private fun isFrequencyAlreadySet(currentFrequency: FrequencyDTO, frequency: FrequencyDTO): Boolean {
        return currentFrequency == frequency
    }

    private suspend fun updateCustomerConsentsV2(
        meterPt: String,
        frequency: FrequencyDTO,
        type: ConsentMeterType,
        fromDate: LocalDate?,
        currentFrequency: FrequencyDTO,
    ) {
        customersSyncClient.updateCustomerConsentsV2(
            updateCustomerConsentRequestV2 {
                meterPoint = meterPt
                setting = frequency.toJuniferFormattedString()
                meterType = type.name
                fromDt = fromDate.toNullableTimestamp()
                currentSetting = currentFrequency.toJuniferFormattedString()
            }
        )
    }
}

private fun MeterPoint.isDccActive() = dccServiceStatus == DccServiceStatus.ACTIVE

private fun MeterPoint.isEco7() =
    (ukProfileClass == UkProfileClass.DOMESTIC_ECONOMY_7) || (ukProfileClass == UkProfileClass.NONDOMESTIC_ECONOMY_7)

private fun MeterPoint.hasSmartMeter() = meters.any { m -> m.isSmartMeter() }
