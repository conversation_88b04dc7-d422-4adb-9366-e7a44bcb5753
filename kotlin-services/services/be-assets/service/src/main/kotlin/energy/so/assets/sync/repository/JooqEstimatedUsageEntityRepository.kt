package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.EstimatedUsageEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.EstimatedRateName
import energy.so.commons.model.enums.EstimatedUsageType
import energy.so.commons.model.tables.records.EstimatedUsageRecord
import energy.so.commons.model.tables.references.ESTIMATED_USAGE
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqEstimatedUsageEntityRepository(private val dslContext: DSLContext) : EstimatedUsageEntityRepository {

    override fun deleteEstimatedUsageEntity(id: Long): Long {
        dslContext.update(ESTIMATED_USAGE)
            .set(ESTIMATED_USAGE.DELETED, LocalDateTime.now())
            .where(ESTIMATED_USAGE.ID.eq(id))
            .execute()
        return id
    }

    override fun patchEstimatedUsageEntity(entity: EstimatedUsageEntity): Long {
        val updateStep: UpdateSetStep<EstimatedUsageRecord> = dslContext
            .update(ESTIMATED_USAGE)
            .set(ESTIMATED_USAGE.UPDATED_AT, LocalDateTime.now())

        if (entity.hasMeterPointId()) updateStep.set(ESTIMATED_USAGE.METER_POINT_ID, entity.meterPointId.value)
        if (entity.hasUsageType()) {
            updateStep.set(
                ESTIMATED_USAGE.TYPE,
                EstimatedUsageType.valueOf(entity.usageType.value)
            )
        }
        if (entity.hasRateName()) {
            updateStep.set(
                ESTIMATED_USAGE.RATE_NAME,
                EstimatedRateName.valueOf(entity.rateName.value)
            )
        }
        if (entity.hasUnits()) updateStep.set(ESTIMATED_USAGE.UNITS, entity.units.value)
        if (entity.hasUsage()) updateStep.set(ESTIMATED_USAGE.USAGE, entity.usage.value.toBigDecimal())
        if (entity.hasMeterPointId()) updateStep.set(ESTIMATED_USAGE.METER_POINT_ID, entity.meterPointId.value)
        if (entity.hasCreatedAt()) {
            updateStep.set(
                ESTIMATED_USAGE.CREATED_AT,
                entity.createdAt.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasUpdatedAt()) {
            updateStep.set(
                ESTIMATED_USAGE.UPDATED_AT,
                entity.updatedAt.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasConsumptionFromDt()) {
            updateStep.set(
                ESTIMATED_USAGE.CONSUMPTION_FROM_DT,
                entity.consumptionFromDt.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasConsumptionToDt()) {
            updateStep.set(
                ESTIMATED_USAGE.CONSUMPTION_TO_DT,
                entity.consumptionToDt.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasSource()) updateStep.set(ESTIMATED_USAGE.SOURCE, entity.source.value)
        if (entity.hasCancelFl()) updateStep.set(ESTIMATED_USAGE.CANCELFL, entity.cancelFl.value)
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(ESTIMATED_USAGE.DELETED)
            } else {
                updateStep.set(
                    ESTIMATED_USAGE.DELETED,
                    DSL.coalesce(ESTIMATED_USAGE.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }

        (updateStep as UpdateSetMoreStep).where(ESTIMATED_USAGE.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createEstimatedUsageEntity(entity: EstimatedUsageEntity): Long {
        return resolveSyncCreationFlow(
            ESTIMATED_USAGE.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateEstimatedUsageEntity,
            ::patchEstimatedUsageEntity,
            fun(e: EstimatedUsageEntity, newId: Long): EstimatedUsageEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateEstimatedUsageEntity(entity: EstimatedUsageEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            ESTIMATED_USAGE,
            energy.so.commons.model.tables.pojos.EstimatedUsage(
                meterPointId = entity.meterPointId.value,
                type = EstimatedUsageType.valueOf(entity.usageType.value),
                rateName = EstimatedRateName.valueOf(entity.rateName.value),
                units = entity.units.value,
                usage = entity.usage.value.toBigDecimal(),
                consumptionFromDt = entity.consumptionFromDt.getValueOrNull()?.toLocalDateTime(),
                consumptionToDt = entity.consumptionToDt.getValueOrNull()?.toLocalDateTime(),
                source = entity.source.getValueOrNull(),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime(),
                cancelfl = entity.cancelFl.getValueOrNull(),
            )
        )
            .apply { save() }
            .run { into(energy.so.commons.model.tables.pojos.EstimatedUsage()) }.id!!
}
