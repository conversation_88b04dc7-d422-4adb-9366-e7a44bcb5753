package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.AssetEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.enums.AssetType
import energy.so.commons.model.tables.pojos.Asset
import energy.so.commons.model.tables.records.AssetRecord
import energy.so.commons.model.tables.references.ASSET
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL.coalesce
import org.jooq.impl.DSL.value
import java.time.LocalDateTime

class JooqAssetEntityRepository(
    private val dslContext: DSLContext,
) : AssetEntityRepository {
    private val logger = TraceableLogging.logger {}

    override fun deleteAssetEntity(id: Long): Long {
        dslContext.update(ASSET)
            .set(ASSET.DELETED, LocalDateTime.now())
            .where(ASSET.ID.eq(id))
            .execute()
        return id
    }

    override fun patchAssetEntity(entity: AssetEntity): Long {
        logger.info("[patchAssetEntity] entity: $entity")
        val updateStep: UpdateSetStep<AssetRecord> = dslContext
            .update(ASSET)
            .set(ASSET.UPDATED_AT, LocalDateTime.now())

        if (entity.hasType()) updateStep.set(ASSET.TYPE, AssetType.valueOf(entity.type.value.uppercase()))
        if (entity.hasCreatedAt()) {
            updateStep.set(ASSET.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }

        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(ASSET.DELETED)
            } else {
                updateStep.set(ASSET.DELETED, coalesce(ASSET.DELETED, value(it.toLocalDateTime())))
            }
        }

        (updateStep as UpdateSetMoreStep).where(ASSET.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createAssetEntity(entity: AssetEntity): Long {
        return resolveSyncCreationFlow(
            ASSET.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateAssetEntity,
            ::patchAssetEntity,
            fun(e: AssetEntity, newId: Long): AssetEntity =
                e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateAssetEntity(entity: AssetEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            ASSET,
            Asset(
                type = AssetType.valueOf(entity.type.value.uppercase()),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(Asset()) }
            .let { it.id!! }
}
