package energy.so.assets.server.extensions

import energy.so.assets.properties.v2.PropertyResponse
import energy.so.assets.properties.v2.propertyResponse
import energy.so.commons.model.tables.interfaces.IProperty
import energy.so.commons.search.model.mapping.nonNullDbToConsumer

fun IProperty.toProtobufModel(): PropertyResponse = let { propertyPojo ->
    propertyResponse {
        nonNullDbToConsumer("id", ::id::set, propertyPojo::id)
    }
}
