package energy.so.assets.server.models

import energy.so.assets.properties.v2.property
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime

data class Property(
    val id: Long? = null,
    val address: Address,
    val type: PropertyType,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(jooq: energy.so.commons.model.tables.pojos.Property, address: Address): Property = Property(
            id = jooq.id,
            address = address,
            type = jooq.type!!.toSiblingEnum(),
            deleted = jooq.deleted,
            createdAt = jooq.createdAt,
            updatedAt = jooq.updatedAt
        )
    }
}

enum class PropertyType {
    FLAT,
    HOUSE,
    SITE
}

fun Property.toProtobuf(): energy.so.assets.properties.v2.Property = let { from ->
    property {
        id = from.id!!
        type = from.type.toSiblingEnum()
        address = from.address.toProtobuf()
        deleted = from.deleted.toNullableTimestamp()
        createdAt = from.createdAt!!.toTimestamp()
        updatedAt = from.updatedAt!!.toTimestamp()
    }
}
