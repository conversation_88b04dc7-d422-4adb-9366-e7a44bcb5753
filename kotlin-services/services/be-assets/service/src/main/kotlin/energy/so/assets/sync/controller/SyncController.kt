package energy.so.assets.sync.controller

import energy.so.assets.sync.service.SyncService
import energy.so.assets.sync.v2.AddressEntityRequest
import energy.so.assets.sync.v2.AssetEntityRequest
import energy.so.assets.sync.v2.EstimatedUsageEntityRequest
import energy.so.assets.sync.v2.MeterEntityRequest
import energy.so.assets.sync.v2.MeterMeterPointRelEntityRequest
import energy.so.assets.sync.v2.MeterPointElectricityEntityRequest
import energy.so.assets.sync.v2.MeterPointGasEntityRequest
import energy.so.assets.sync.v2.MeterPointHistoryEntityRequest
import energy.so.assets.sync.v2.MeterReadingEntityRequest
import energy.so.assets.sync.v2.PropertyEntityRequest
import energy.so.assets.sync.v2.RegisterEntityRequest
import energy.so.assets.sync.v2.SyncGrpcKt
import energy.so.commons.v2.sync.SyncResponse
import io.opentelemetry.instrumentation.annotations.WithSpan

class SyncController(
    private val syncService: SyncService,
) : SyncGrpcKt.SyncCoroutineImplBase() {

    @WithSpan
    override suspend fun syncAssetEntity(request: AssetEntityRequest): SyncResponse {
        return syncService.syncAssetEntity(request)
    }

    @WithSpan
    override suspend fun syncRegisterEntity(request: RegisterEntityRequest): SyncResponse {
        return syncService.syncRegisterEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterEntity(request: MeterEntityRequest): SyncResponse {
        return syncService.syncMeterEntity(request)
    }

    @WithSpan
    override suspend fun syncPropertyEntity(request: PropertyEntityRequest): SyncResponse {
        return syncService.syncPropertyEntity(request)
    }

    @WithSpan
    override suspend fun syncAddressEntity(request: AddressEntityRequest): SyncResponse {
        return syncService.syncAddressEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterPointEntity(request: energy.so.assets.sync.v2.MeterPointEntityRequest): SyncResponse {
        return syncService.syncMeterPointEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterPointElectricityEntity(request: MeterPointElectricityEntityRequest): SyncResponse {
        return syncService.syncMeterPointElectricityEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterPointGasEntity(request: MeterPointGasEntityRequest): SyncResponse {
        return syncService.syncMeterPointGasEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterPointHistoryEntity(request: MeterPointHistoryEntityRequest): SyncResponse {
        return syncService.syncMeterPointHistoryEntity(request)
    }

    @WithSpan
    override suspend fun syncEstimatedUsageEntity(request: EstimatedUsageEntityRequest): SyncResponse {
        return syncService.syncEstimatedUsageEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterReadingEntity(request: MeterReadingEntityRequest): SyncResponse {
        return syncService.syncMeterReadEntity(request)
    }

    @WithSpan
    override suspend fun syncMeterMeterPointRelEntity(request: MeterMeterPointRelEntityRequest): SyncResponse {
        return syncService.syncMeterMeterPointRelEntity(request)
    }
}
