package energy.so.assets.server.database.repositories

import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingQuality
import energy.so.assets.server.models.MeterReadingSource
import java.time.LocalDateTime

interface MeterReadingsRepository {

    /**
     * Search meter readings by given filters
     *
     * @param searchRequest
     * @return the meter readings found
     */
    fun search(searchRequest: MeterReadingsSearchRequest): List<MeterReading>

    /**
     * @param meterReading MeterReading
     * @return MeterReading
     */
    fun save(meterReading: MeterReading): MeterReading

    /**
     *
     * @param meterReadings List<MeterReading>
     */
    fun saveAll(meterReadings: List<MeterReading>)

    /**
     * Get that latest electricity meter reading by meter point
     * @param meterPointId Long
     * @return MeterReading?
     */
    fun getLastElectricityMeterReading(meterPointId: Long): MeterReading?

    /**
     * Get the first electricity meter reading by meter point
     * @param meterPointId Long
     * @return MeterReading?
     */
    fun getFirstElectricityMeterReading(meterPointId: Long): MeterReading?

    /**
     * Get meter readings by start and end date
     * @param meterPointId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @return List<MeterReading>
     */
    fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<MeterReading>

    /**
     * Get last smart reading by meter id
     * read.source == "SMR" AND read.quality != "Estimated"
     * @param meterId
     * @return MeterReading?
     */
    fun getLastSmartReadingByMeterIdAndSourceInAndQualityIn(
        meterId: Long,
        sources: Set<MeterReadingSource>,
        qualities: Set<MeterReadingQuality>,
    ): MeterReading?

    /**
     * Check if there is at least one reading associated with given meterpoint
     * @param meterPointId: Long
     * @return Boolean
     */
    fun existsForMeterPointIdInLastYear(meterPointId: Long): Boolean

    /**
     * Check if there is at least one reading for a given list of meters and a submission processed
     * @param meterIds: List<Long>
     * @param submissionProcessed: bool
     * @return Boolean
     */
    fun existsForMeterIdsAndSubmissionProcessed(meterIds: List<Long>, submissionProcessed: Boolean): Boolean

    /**
     * Set submission processed true for all readings by given ids
     * @param ids: List<Long>
     */
    fun markSubmissionsProcessed(ids: List<Long>)
}
