package energy.so.assets.server.services

import com.google.cloud.storage.StorageException
import energy.so.ac.junifer.v1.accounts.AccountsClient
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.assets.meterReadings.v2.AddMeterReadingOutboundPhotoRequest
import energy.so.assets.meterReadings.v2.AddMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.FreshdeskMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.GetMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceGetUrlRequest
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceUploadUrlRequest
import energy.so.assets.meterReadings.v2.PhotoEvidenceMetadata
import energy.so.assets.server.config.StorageConfig
import energy.so.assets.server.config.getNovaMeterPhotoHistoryModalUrl
import energy.so.assets.server.database.repositories.MeterReadingPhotoEvidenceRepository
import energy.so.assets.server.database.repositories.OutboundPhotoRequestRepository
import energy.so.assets.server.exceptions.FreshDeskPhotoEvidenceException
import energy.so.assets.server.exceptions.OutboundPhotoRequestException
import energy.so.assets.server.models.MeterPhotoEvidenceIdentifiers
import energy.so.assets.server.models.MeterReadingFuel
import energy.so.assets.server.models.MeterReadingPhotoEvidence
import energy.so.assets.server.models.MeterReadingPhotoEvidenceStatus.PENDING
import energy.so.assets.server.models.OutboundPhotoRequest
import energy.so.assets.server.models.OutboundPhotoRequestFilter
import energy.so.assets.server.models.OutboundPhotoRequestStatus.CANCELLED
import energy.so.assets.server.models.OutboundPhotoRequestStatus.FIRST_FOLLOW_UP_SENT
import energy.so.assets.server.models.OutboundPhotoRequestStatus.FRESHDESK_TICKET_CREATED
import energy.so.assets.server.models.OutboundPhotoRequestStatus.SECOND_FOLLOW_UP_SENT
import energy.so.assets.server.models.OutboundPhotoRequestStatus.UPLOADED
import energy.so.assets.server.models.toMeterReadingPhotoEvidence
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.FRESHDESK_ALTERNATE_PHOTO_UPLOAD_NOTE
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.JUNIFER_NOTE_AUTOMATIC_CLOSE_OUTBOUND_REQUEST_SUMMARY
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.JUNIFER_NOTE_PHOTOS_UPLOADED_SUMMARY
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.JUNIFER_NOTE_SUBJECT
import energy.so.assets.server.services.DefaultOutboundPhotoRequestService.Companion.communicationTemplate
import energy.so.awsconnect.client.v1.client.CustomerSupportCenterClient
import energy.so.awsconnect.client.v1.config.CustomerSupportCenterConfig
import energy.so.awsconnect.client.v1.models.AttachmentDto
import energy.so.awsconnect.client.v1.models.SendEmailRequest
import energy.so.awsconnect.client.v1.models.cases.CaseSearchRequest
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.storage.StorageClient
import energy.so.commons.v2.dtos.idRequestStr
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.v2.customers.Customer
import energy.so.freshdesk.client.v1.FreshDeskClient
import energy.so.freshdesk.client.v1.config.FreshDeskConfig
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateNoteRequestDto
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateTicketAttachmentDto
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateTicketRequestDto
import energy.so.freshdesk.client.v1.dto.FreshdeskUpdateTicketRequestDto
import energy.so.freshdesk.client.v1.dto.OCTET_STREAM_MIME_TYPE
import energy.so.users.v2.FeatureName
import java.io.InputStream
import java.net.URLConnection
import kotlinx.serialization.json.Json
import energy.so.commons.storage.StorageException as CommonStorageException

private val logger = TraceableLogging.logger {}

class DefaultMeterReadingPhotoEvidenceService(
    private val meterReadingPhotoRepository: MeterReadingPhotoEvidenceRepository,
    private val outboundPhotoRequestRepository: OutboundPhotoRequestRepository,
    private val customersClient: CustomersClient,
    private val storageClient: StorageClient,
    private val freshdeskClient: FreshDeskClient,
    private val juniferAccountsClient: AccountsClient,
    private val freshDeskTicketOperationPublisher: MessagePublisher<String>,
    private val closeOutboundPhotoRequestHandler: CloseOutboundPhotoRequestHandler,
    private val storageConfig: StorageConfig,
    private val freshDeskConfig: FreshDeskConfig,
    private val customerSupportCenterClient: CustomerSupportCenterClient,
    private val customerSupportCenterConfig: CustomerSupportCenterConfig,
    private val featureService: FeatureService,
) : MeterReadingPhotoEvidenceService {

    companion object {
        const val BILLING = "Billing"
        const val FRESHDESK_PHOTO_EVIDENCE_SUBJECT = "Meter read photo submission"
        const val MAX_FILE_SIGNATURE_LENGTH_IN_BYTES = 16
        const val OUTBOUND_REQUEST_ID_PROPERTY = "outboundRequestId"
        const val FRESHDESK_TICKET_DEFAULT_STATUS = 2L// Status = 2 means 'Open'
        const val FRESHDESK_TICKET_DEFAULT_SOURCE = 2L// Source = 2 means 'Portal'
        const val FRESHDESK_TICKET_DEFAULT_PRIORITY =
            1L // As long as this isn't sent from FE in input, should stay at Low priority, which is 1.
    }

    override suspend fun addMeterReadingPhotoEvidence(request: AddMeterReadingPhotoEvidenceRequest) {
        val metadata = request.metadataList

        if (!validMetadataForPhotoEvidenceCreation(metadata)) return

        persistPhotoMetadataAndPublishAsyncOperation(metadata)
    }

    override suspend fun addFreshdeskPhotoEvidence(request: FreshdeskMeterReadingPhotoEvidenceRequest) {
        val metadata = request.photoEvidencesList

        if (metadata.isEmpty()) {
            logger.warn("[::addFreshdeskPhotoEvidence] no metadata found so no photo evidence will be added")
            return
        }

        val accountNumber = request.accountNumber

        val outboundRequest = outboundPhotoRequestRepository.getActiveOutboundRequests(
            accountNumber
        ).let { if (it.isNotEmpty()) it[0] else null }

        metadata.forEach {
            meterReadingPhotoRepository.save(
                it.toMeterReadingPhotoEvidence(
                    PENDING,
                    accountNumber,
                    outboundRequest?.id
                )
            )
        }

        if (outboundRequest?.id != null) {
            logger.info { "[::addFreshdeskPhotoEvidence] Existing outbound request found with id: ${outboundRequest.id}" }

            try {
                closeOutboundPhotoRequestHandler.closeOutboundRequest(
                    outboundRequest.id,
                    JUNIFER_NOTE_AUTOMATIC_CLOSE_OUTBOUND_REQUEST_SUMMARY
                )
            } catch (ex: Exception) {
                logger.error(ex) { "[::addFreshdeskPhotoEvidence] An error occurred while closing outbound request" }

                throw ex
            }
            try {
                if (featureService.isFeatureEnabled(FeatureName.TMP_SO_24691_SWITCH_FROM_FRESHDESK_TO_AWS_CONNECT)) {
                    getCaseIdForUpdate(outboundRequest)?.let {
                        customerSupportCenterClient.addComment(
                            it,
                            FRESHDESK_ALTERNATE_PHOTO_UPLOAD_NOTE
                        )
                    }
                } else {
                    freshdeskClient.createNote(
                        FreshdeskCreateNoteRequestDto(
                            body = FRESHDESK_ALTERNATE_PHOTO_UPLOAD_NOTE,
                            private = true,
                        ),
                        "${outboundRequest.freshdeskTicketId}",
                    )
                }
            } catch (ex: Exception) {
                logger.error(ex) { "[::addFreshdeskPhotoEvidence] An error occurred while creating freshdesk note" }

                throw ex
            }
        }

        try {
            val photosToBeMoved = metadata.map {
                FreshdeskCreateTicketAttachmentDto(
                    fileName = it.imageIdentifier,
                    mimeType = getTmpBucketImageMimeType(it.imageIdentifier)
                )
            }
            movePhotosToLongTermBucket(
                photosToBeMoved,
                metadata[0].freshdeskTicketId.getValueOrNull(),
                metadata[0].caseId.getValueOrNull(),
            )
        } catch (ex: Exception) {
            when (ex) {
                is StorageException, is CommonStorageException -> {
                    logger.error(ex) { "[::addFreshdeskPhotoEvidence] Error while moving photos to long term bucket" }
                }

                else -> logger.error(ex) { "[::addFreshdeskPhotoEvidence] Error when executing markAsDoneAndSetFreshdeskTicket repo method" }
            }

            throw ex
        }
    }

    override suspend fun addMeterReadingOutboundPhoto(request: AddMeterReadingOutboundPhotoRequest) {
        val outboundRequestId = request.outboundRequestId

        val metadata = request.metadataList
        if (!validMetadataForPhotoEvidenceCreation(metadata)) return

        val outboundFuel = getOutboundRequest(outboundRequestId, metadata[0].accountNumber.toString()).fuel

        if (meterReadingPhotoRepository.getByOutboundRequestId(outboundRequestId).isNotEmpty()) {
            throw OutboundPhotoRequestException("There are already photo evidences persisted for outbound request with id $outboundRequestId")
        }

        persistPhotoMetadataAndPublishAsyncOperation(metadata, outboundFuel, outboundRequestId)
    }

    override fun generateMeterReadingPhotoEvidenceGetUrl(request: MeterReadingPhotoEvidenceGetUrlRequest): String {
        logger.debug(
            "[::generateMeterReadingPhotoEvidenceGetUrl] Generating signed up get URL for image '${request.imageIdentifier}'"
        )
        return storageClient.generateSignedGetUrl(
            bucketName = storageConfig.bucketName,
            fileIdentifier = computePhotoEvidenceFilePath(request.imageIdentifier),
            expiryInSeconds = storageConfig.getExpiryInSeconds,
        )
    }

    override suspend fun generateMeterReadingPhotoEvidenceUploadUrls(request: MeterReadingPhotoEvidenceUploadUrlRequest): Map<String, String> {
        logger.debug(
            "[::generateMeterReadingPhotoEvidenceUploadUrl] Generating signed up upload URL for images '${request.imageNameList}'"
        )
        val uploadUrls = mutableMapOf<String, String>()

        //todo(so=19629) remove suspend from method after ff is removed
        request.imageNameList.forEach {
            val url = storageClient.generateSignedUploadUrl(
                bucketName = storageConfig.tmpBucketName,
                fileIdentifier = computePhotoEvidenceFilePath(it),
                expiryInSeconds = storageConfig.uploadExpiryInSeconds,
                maxFileSize = if (featureService.isFeatureEnabled(FeatureName.TMP_SO_19629_PHOTO_EVIDENCE_UPLOAD_URL_MAX_SIZE)) storageConfig.uploadMaxFileSize else null
            )
            uploadUrls[it] = url
        }

        return uploadUrls
    }

    override fun getMeterReadingPhotoEvidenceByAccountNumber(
        request: GetMeterReadingPhotoEvidenceRequest,
    ): List<MeterReadingPhotoEvidence> {
        logger.debug(
            "[::getMeterReadingPhotoEvidenceByAccountNumber] Get meter reading photo evidence by account number '${request.accountNumber}'"
        )
        return meterReadingPhotoRepository.getByAccountNumber(request.accountNumber)
    }

    override fun deleteMeterReadingPhotoEvidence(imageIdentifier: String) {
        logger.debug { "[::deleteMeterReadingPhotoEvidence] Image identifier: $imageIdentifier" }
        storageClient.removeFile(
            bucketName = storageConfig.bucketName,
            fileIdentifier = computePhotoEvidenceFilePath(imageIdentifier)
        )
        logger.debug { "[::deleteMeterReadingPhotoEvidence] Image deleted from bucket: $imageIdentifier" }
        meterReadingPhotoRepository.deleteByImageIdentifier(imageIdentifier)
    }

    override suspend fun createPhotoEvidenceFreshDeskTicket(photoEvidenceIdentifiers: List<String>) {
        val metadata = fetchMeterPhotoEvidenceMetadata(photoEvidenceIdentifiers, "createPhotoEvidenceFreshDeskTicket")

        if (metadata.isEmpty()) {
            logger.warn("[::createPhotoEvidenceFreshDeskTicket] no metadata found so no FD ticket will be created")
            return
        }

        val customer: Customer
        val billingAccountNumber = metadata[0].accountNumber!!
        try {
            customer = billingAccountNumber.let {
                customersClient.getCustomerDetailsByAccountNumber(
                    idRequestStr { id = it }
                )
            }
        } catch (ex: EntityNotFoundGrpcException) {
            logger.error(ex) {
                "[::createPhotoEvidenceFreshDeskTicket] Customer details for billing account number $billingAccountNumber not found"
            }

            return
        }

        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_24691_SWITCH_FROM_FRESHDESK_TO_AWS_CONNECT)) {
            sendEmailToCustomerSupportCenter(metadata, customer)
        } else {
            createFreshdeskTicketWithAttachment(metadata, customer)
        }
    }

    private suspend fun sendEmailToCustomerSupportCenter(
        metadata: List<MeterReadingPhotoEvidence>,
        customer: Customer,
    ) {
        if (customer.primaryContact == null) {
            logger.warn("[::sendEmailToCustomerSupportCenter without primary contact, skipping ticket creation")
            return
        }
        val emailRequest = SendEmailRequest(
            destinationEmailAddress = customerSupportCenterConfig.emailAddresses.helpAddress,
            sourceEmailAddress = customer.primaryContact.email,
            subject = FRESHDESK_PHOTO_EVIDENCE_SUBJECT,
            body = generateTicketDescription(metadata),
            attachments = metadata.map { buildImageAttachmentForEmail(it) }
        )

        try {
            customerSupportCenterClient.sendInboundEmail(emailRequest)
        } catch (ex: Exception) {
            logger.error(ex) { "[::createPhotoEvidenceFreshDeskTicket] Error while creating Amazon Connect case from photo evidence" }

            // this will trigger pubsub retry mechanism
            throw FreshDeskPhotoEvidenceException(
                "Error while sending inbound email to Amazon Connect case from photo evidence: ${ex.message} with payload: $emailRequest",
            )
        }

        try {
            val attachments = metadata.map { buildImageAttachmentForUpload(it) }
            movePhotosToLongTermBucket(attachments, 0)
        } catch (ex: Exception) {
            logger.error(ex) { "[::createPhotoEvidenceFreshDeskTicket] Error while moving photos to long term bucket" }

            // we don't want to let pubsub retry this flow since it will duplicate freshdesk ticket
            // TODO(so-17931) scheduler to cleanup these situations
        }
    }

    private suspend fun createFreshdeskTicketWithAttachment(
        metadata: List<MeterReadingPhotoEvidence>,
        customer: Customer,
    ) {
        val (attachments, freshDeskPayload) = buildFreshDeskCreateTicketPayload(metadata, customer)
        val freshdeskTicketId: Long

        try {
            freshdeskTicketId = freshdeskClient.createTicketWithAttachment(freshDeskPayload).id
        } catch (ex: Exception) {
            logger.error(ex) { "[::createPhotoEvidenceFreshDeskTicket] Error while creating FreshDesk ticket from photo evidence" }

            // this will trigger pubsub retry mechanism
            throw FreshDeskPhotoEvidenceException(
                "Error while creating FreshDesk ticket from photo evidence: ${ex.message} with payload: $freshDeskPayload",
            )
        }

        try {
            movePhotosToLongTermBucket(attachments, freshdeskTicketId)
        } catch (ex: Exception) {
            logger.error(ex) { "[::createPhotoEvidenceFreshDeskTicket] Error while moving photos to long term bucket" }

            // we don't want to let pubsub retry this flow since it will duplicate freshdesk ticket
            // TODO(so-17931) scheduler to cleanup these situations
        }
    }

    override suspend fun updatePhotoEvidenceFreshDeskTicket(
        photoEvidenceIdentifiers: List<String>,
        outboundRequestId: Long,
    ) {
        val metadata = fetchMeterPhotoEvidenceMetadata(photoEvidenceIdentifiers, "updatePhotoEvidenceFreshDeskTicket")

        if (metadata.isEmpty()) {
            logger.warn("[::updatePhotoEvidenceFreshDeskTicket] no metadata found so no FD ticket will be updated")
            return
        }

        val outboundRequest = outboundPhotoRequestRepository.findById(
            OutboundPhotoRequestFilter(
                outboundRequestId,
                statusesList = listOf(FRESHDESK_TICKET_CREATED, FIRST_FOLLOW_UP_SENT, SECOND_FOLLOW_UP_SENT, CANCELLED)
            )
        )

        if (outboundRequest == null) {
            logger.error("[::updatePhotoEvidenceFreshDeskTicket] no outbound request found with status FRESHDESK_TICKET_CREATED for id: $outboundRequestId")

            return
        }

        var juniferNoteContent: String?
        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_24691_SWITCH_FROM_FRESHDESK_TO_AWS_CONNECT)) {
            updateCaseWithAttachments(outboundRequest, metadata)
        } else {
            updateFreshdeskTicketWithAttachment(outboundRequest, metadata)
            juniferNoteContent = "FD ${outboundRequest.freshdeskTicketId} - " +
                    communicationTemplate.getNovaMeterPhotoHistoryModalUrl(outboundRequest.accountNumber)
        }
        juniferNoteContent = if (outboundRequest.freshdeskTicketId != null) {
            "FD ${outboundRequest.freshdeskTicketId} - " +
                    communicationTemplate.getNovaMeterPhotoHistoryModalUrl(outboundRequest.accountNumber)
        } else {
            "Case ${outboundRequest.caseId} - " +
                    communicationTemplate.getNovaMeterPhotoHistoryModalUrl(outboundRequest.accountNumber)
        }
        outboundPhotoRequestRepository.updateStatus(outboundRequestId, UPLOADED)

        juniferAccountsClient.createAccountNote(
            createAccountNoteRequest {
                useJuniferId = false
                accountId = outboundRequest.accountId.toString()
                subject = JUNIFER_NOTE_SUBJECT
                type = NoteType.Note
                summary = JUNIFER_NOTE_PHOTOS_UPLOADED_SUMMARY
                content = juniferNoteContent
            }
        )
    }

    private suspend fun updateFreshdeskTicketWithAttachment(
        outboundRequest: OutboundPhotoRequest,
        metadata: List<MeterReadingPhotoEvidence>
    ) {
        if (outboundRequest.freshdeskTicketId != null) {
            val (attachments, freshDeskPayload) = buildFreshDeskUpdateTicketPayload(metadata)
            val freshdeskTicketId = outboundRequest.freshdeskTicketId
            try {
                freshdeskClient.updateTicketWithAttachment(freshDeskPayload, freshdeskTicketId.toString())
            } catch (ex: Exception) {
                logger.error(ex) { "[::updatePhotoEvidenceFreshDeskTicket] Error while updating FreshDesk ticket with id $freshdeskTicketId from photo evidence" }

                // this will trigger pubsub retry mechanism
                throw FreshDeskPhotoEvidenceException(
                    "Error while updating FreshDesk ticket from photo evidence: ${ex.message} with payload: $freshDeskPayload"
                )
            }

            try {
                movePhotosToLongTermBucket(attachments, freshdeskTicketId)
            } catch (ex: Exception) {
                logger.error(ex) { "[::updatePhotoEvidenceFreshDeskTicket] Error while moving photos to long term bucket" }

                // we don't want to let pubsub retry this flow since it will duplicate freshdesk ticket updates
                // TODO(so-17931) scheduler to cleanup these situations
            }
        }
    }

    private suspend fun updateCaseWithAttachments(
        outboundRequest: OutboundPhotoRequest,
        metadata: List<MeterReadingPhotoEvidence>
    ) {
        val caseId = getCaseIdForUpdate(outboundRequest)
        if (caseId != null) {
            try {
                val attachments = metadata.map { buildImageAttachmentForEmail(it) }
                customerSupportCenterClient.addAttachments(caseId, attachments)
            } catch (ex: Exception) {
                logger.error(ex) { "[::updatePhotoEvidenceCase] Error while updating Case with id $caseId from photo evidence" }

                // this will trigger pubsub retry mechanism
                throw FreshDeskPhotoEvidenceException(
                    "Error while updating FreshDesk ticket from photo evidence: ${ex.message} with payload:"
                )
            }

            try {
                val attachments = metadata.map { buildImageAttachmentForUpload(it) }
                movePhotosToLongTermBucket(attachments, 0)
            } catch (ex: Exception) {
                logger.error(ex) { "[::updatePhotoEvidenceFreshDeskTicket] Error while moving photos to long term bucket" }

                // we don't want to let pubsub retry this flow since it will duplicate freshdesk ticket updates
                // TODO(so-17931) scheduler to cleanup these situations
            }
        }
    }

    private suspend fun getCaseIdForUpdate(
        outboundRequest: OutboundPhotoRequest
    ) = outboundRequest.caseId ?: let {
        val caseSearchRequest = CaseSearchRequest(
            outboundRequest.freshdeskTicketId!!.toString(),
            fields = listOf(customerSupportCenterConfig.customFields.freshdeskId)
        )
        customerSupportCenterClient.searchCases(caseSearchRequest).firstOrNull()
    }

    private fun validMetadataForPhotoEvidenceCreation(metadata: MutableList<PhotoEvidenceMetadata>): Boolean {
        logger.debug {
            "[::addMeterReadingPhotoEvidence] MeterPointId: ${metadata.firstOrNull()?.meterPointId} and metadataSize: ${metadata.size}"
        }

        if (metadata.isEmpty()) {
            logger.warn("[::addMeterReadingPhotoEvidence] empty metadata list received")
            return false
        }
        return true
    }

    private fun fetchMeterPhotoEvidenceMetadata(
        photoEvidenceIdentifiers: List<String>,
        operation: String,
    ): List<MeterReadingPhotoEvidence> {
        val metadata = photoEvidenceIdentifiers.mapNotNull {
            val maybeMetadata = meterReadingPhotoRepository.getByImageIdentifier(it)

            if (maybeMetadata == null) {
                logger.error("[::$operation] no metadata found for image identifier: $it")
            }

            maybeMetadata
        }
        return metadata
    }

    private fun persistPhotoMetadataAndPublishAsyncOperation(
        metadata: MutableList<PhotoEvidenceMetadata>,
        fuel: MeterReadingFuel? = null,
        outboundRequestId: Long? = null,
    ) {
        metadata.map { it.toMeterReadingPhotoEvidence(PENDING, fuel, outboundRequestId) }
            .mapNotNull { meterReadingPhotoRepository.save(it) }
            .mapNotNull { it.imageIdentifier }
            .apply {
                if (this.isNotEmpty()) {
                    freshDeskTicketOperationPublisher.publishMessage(
                        QueueMessage(
                            Json.encodeToString(
                                MeterPhotoEvidenceIdentifiers.serializer(),
                                MeterPhotoEvidenceIdentifiers(this)
                            ),
                            properties = if (outboundRequestId != null) mapOf(
                                OUTBOUND_REQUEST_ID_PROPERTY to outboundRequestId.toString()
                            ) else mapOf()
                        )
                    )
                }
            }
    }

    private fun getOutboundRequest(
        outboundRequestId: Long,
        accountNumber: String,
    ): OutboundPhotoRequest {
        val outboundPhotoRequest = outboundPhotoRequestRepository.findById(
            OutboundPhotoRequestFilter(
                id = outboundRequestId,
                accountNumber = accountNumber,
                statusesList = listOf(FRESHDESK_TICKET_CREATED, FIRST_FOLLOW_UP_SENT, SECOND_FOLLOW_UP_SENT, CANCELLED)
            )
        )

        if (outboundPhotoRequest == null) {
            logger.error("[::updatePhotoEvidenceFreshDeskTicket] No outbound photo request found for id $outboundRequestId and account number $accountNumber")

            throw OutboundPhotoRequestException("No outbound photo request found for id $outboundRequestId and account number $accountNumber")
        }

        return outboundPhotoRequest
    }

    private fun movePhotosToLongTermBucket(
        attachments: List<FreshdeskCreateTicketAttachmentDto>,
        freshDeskTicketId: Long?,
        caseId: String? = null
    ) {
        attachments.forEach {
            storageClient.moveFile(
                storageConfig.tmpBucketName,
                storageConfig.bucketName,
                computePhotoEvidenceFilePath(it.fileName)
            )
        }

        attachments.forEach {
            meterReadingPhotoRepository.markAsDoneAndSetFreshdeskTicket(
                imageIdentifier = it.fileName,
                imageType = it.mimeType,
                freshDeskTicketId = freshDeskTicketId,
                caseId = caseId
            )
        }
    }

    private fun computePhotoEvidenceFilePath(imageIdentifier: String) = String.format(
        "%s/%s",
        storageConfig.readingsEvidenceFolder,
        imageIdentifier
    )

    private fun buildFreshDeskCreateTicketPayload(
        metadata: List<MeterReadingPhotoEvidence>,
        customer: Customer,
    ): Pair<List<FreshdeskCreateTicketAttachmentDto>, FreshdeskCreateTicketRequestDto> {
        val attachments = metadata.map { buildImageAttachmentForUpload(it) }

        val freshDeskPayload = FreshdeskCreateTicketRequestDto(
            name = "${customer.firstName} ${customer.lastName}",
            email = "${customer.primaryContact?.email}",
            priority = FRESHDESK_TICKET_DEFAULT_PRIORITY,
            status = FRESHDESK_TICKET_DEFAULT_STATUS,
            source = FRESHDESK_TICKET_DEFAULT_SOURCE,
            description = generateTicketDescription(metadata),
            tags = listOf(BILLING, FRESHDESK_PHOTO_EVIDENCE_SUBJECT),
            group_id = freshDeskConfig.groupId,
            custom_fields = generateFreshDeskCustomFields(),
            subject = FRESHDESK_PHOTO_EVIDENCE_SUBJECT,
            attachments = attachments
        )
        return Pair(attachments, freshDeskPayload)
    }

    private fun buildFreshDeskUpdateTicketPayload(metadata: List<MeterReadingPhotoEvidence>):
            Pair<List<FreshdeskCreateTicketAttachmentDto>, FreshdeskUpdateTicketRequestDto> {
        val attachments = metadata.map { buildImageAttachmentForUpload(it) }

        val freshDeskPayload = FreshdeskUpdateTicketRequestDto(
            status = FRESHDESK_TICKET_DEFAULT_STATUS,
            attachments = attachments
        )
        return Pair(attachments, freshDeskPayload)
    }

    private fun buildImageAttachmentForUpload(photoEvidence: MeterReadingPhotoEvidence): FreshdeskCreateTicketAttachmentDto {
        val mimeType = getTmpBucketImageMimeType(photoEvidence.imageIdentifier!!)

        return FreshdeskCreateTicketAttachmentDto(
            fileName = photoEvidence.imageIdentifier,
            attachmentProvider = this::photoEvidenceAttachmentProvider,
            mimeType = mimeType
        )
    }

    private fun getTmpBucketImageMimeType(imageIdentifier: String): String {
        val fileSignatureStream =
            storageClient.retrieveFile(
                storageConfig.tmpBucketName,
                computePhotoEvidenceFilePath(imageIdentifier),
                MAX_FILE_SIGNATURE_LENGTH_IN_BYTES
            )

        return URLConnection.guessContentTypeFromStream(fileSignatureStream) ?: OCTET_STREAM_MIME_TYPE
    }

    private fun photoEvidenceAttachmentProvider(imageIdentifier: String): InputStream {
        return storageClient.retrieveFileStream(
            storageConfig.tmpBucketName,
            computePhotoEvidenceFilePath(imageIdentifier)
        )
    }

    private fun generateTicketDescription(metadata: List<MeterReadingPhotoEvidence>): String {
        val firstMetadata = metadata.getOrNull(0)
        val firstStandardRead = metadata.firstOrNull { it.standardRead != null }?.standardRead
        val firstDayRead = metadata.firstOrNull { it.dayRead != null }?.dayRead
        val firstNightRead = metadata.firstOrNull { it.nightRead != null }?.nightRead

        val description = StringBuilder()

        val brTag = "</br>"

        description.appendLine("<div>")

        if (firstMetadata != null) {
            description.appendLine("account number: ${firstMetadata.accountNumber}$brTag")
            description.appendLine("meterpoint identifier: ${firstMetadata.meterPointIdentifier}$brTag")
            description.appendLine("error code: ${firstMetadata.errorCode}$brTag")
            description.appendLine("submission date: ${firstMetadata.submissionDate}$brTag")
            firstStandardRead?.let {
                description.appendLine("standard read: $it$brTag")
            }
            firstDayRead?.let {
                description.appendLine("day read: $it$brTag")
            }
            firstNightRead?.let {
                description.appendLine("night read: $it$brTag")
            }
        }

        description.appendLine("</div>")


        return description.toString()
    }

    private fun generateCaseDescription(metadata: List<MeterReadingPhotoEvidence>): String {
        val firstMetadata = metadata.getOrNull(0)
        val firstStandardRead = metadata.firstOrNull { it.standardRead != null }?.standardRead
        val firstDayRead = metadata.firstOrNull { it.dayRead != null }?.dayRead
        val firstNightRead = metadata.firstOrNull { it.nightRead != null }?.nightRead

        val description = StringBuilder()

        if (firstMetadata != null) {
            description.appendLine("account number: ${firstMetadata.accountNumber}")
            description.appendLine("meterpoint identifier: ${firstMetadata.meterPointIdentifier}")
            description.appendLine("error code: ${firstMetadata.errorCode}")
            description.appendLine("submission date: ${firstMetadata.submissionDate}")
            firstStandardRead?.let {
                description.appendLine("standard read: $it")
            }
            firstDayRead?.let {
                description.appendLine("day read: $it")
            }
            firstNightRead?.let {
                description.appendLine("night read: $it")
            }
        }

        return description.toString()
    }

    private fun generateFreshDeskCustomFields(): Map<String, String> {
        return mapOf(
            "cf_new_level_1248554" to BILLING,
            "cf_new_level_2" to DefaultOutboundPhotoRequestService.METER_READING_SUBMISSION,
            "cf_new_level_3" to DefaultOutboundPhotoRequestService.METER_READING_SUBMISSION_ERROR_INVESTIGATING,
        )
    }

    private fun buildImageAttachmentForEmail(photoEvidence: MeterReadingPhotoEvidence): AttachmentDto {
        val byteArray = storageClient.retrieveFile(
            storageConfig.tmpBucketName,
            computePhotoEvidenceFilePath(photoEvidence.imageIdentifier!!)
        )

        return AttachmentDto(
            fileName = photoEvidence.imageIdentifier,
            content = byteArray
        )
    }
}
