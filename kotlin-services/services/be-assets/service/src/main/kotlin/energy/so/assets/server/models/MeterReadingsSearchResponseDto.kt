package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.meterReadingsSearchResponse

data class MeterReadingsSearchResponseDto(
    val meterReading: List<MeterReading>,
    val searchResponseMetadata: SearchResponseMetadataDto,
)

fun MeterReadingsSearchResponseDto.toProtobuf() = let { dto ->
    meterReadingsSearchResponse {
        results.addAll(dto.meterReading.map { r -> r.toResponse() })
        metadata = dto.searchResponseMetadata.toProtobuf()
    }
}
