package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterPointGasEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.tables.pojos.MeterPointGa
import energy.so.commons.model.tables.records.MeterPointGasRecord
import energy.so.commons.model.tables.references.METER_POINT_GAS
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqMeterPointGasEntityRepository(
    private val dslContext: DSLContext,
) : MeterPointGasEntityRepository {
    override fun deleteMeterPointGasEntity(id: Long): Long {
        dslContext.update(METER_POINT_GAS)
            .set(METER_POINT_GAS.DELETED, LocalDateTime.now())
            .where(METER_POINT_GAS.ID.eq(id))
            .execute()
        return id
    }

    override fun patchMeterPointGasEntity(entity: MeterPointGasEntity): Long {
        val updateStep: UpdateSetStep<MeterPointGasRecord> = dslContext
            .update(METER_POINT_GAS)
            .set(METER_POINT_GAS.UPDATED_AT, LocalDateTime.now())

        if (entity.hasDesignation()) updateStep.set(METER_POINT_GAS.DESIGNATION, entity.designation.getValueOrNull())

        if (entity.hasMeterSerialNumber()) {
            updateStep.set(
                METER_POINT_GAS.METER_SERIAL_NUMBER,
                entity.meterSerialNumber.getValueOrNull()
            )
        }

        if (entity.hasPrepayDetected()) {
            updateStep.set(
                METER_POINT_GAS.PREPAY_DETECTED,
                entity.prepayDetected.getValueOrNull()
            )
        }

        if (entity.hasIgtIdentifier()) {
            updateStep.set(
                METER_POINT_GAS.IGT_IDENTIFIER,
                entity.igtIdentifier.getValueOrNull()
            )
        }

        if (entity.hasMamMarketParticipant()) {
            updateStep.set(
                METER_POINT_GAS.MAM_MARKET_PARTICIPANT,
                entity.mamMarketParticipant.getValueOrNull()
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(METER_POINT_GAS.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }

        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_POINT_GAS.DELETED)
            } else {
                updateStep.set(
                    METER_POINT_GAS.DELETED,
                    DSL.coalesce(METER_POINT_GAS.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }

        (updateStep as UpdateSetMoreStep).where(METER_POINT_GAS.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createMeterPointGasEntity(entity: MeterPointGasEntity): Long {
        return resolveSyncCreationFlow(
            METER_POINT_GAS.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterPointGasEntity,
            ::patchMeterPointGasEntity,
            fun(e: MeterPointGasEntity, newId: Long): MeterPointGasEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterPointGasEntity(entity: MeterPointGasEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER_POINT_GAS,
            MeterPointGa(
                id = entity.meterPointId.value,
                designation = entity.designation.getValueOrNull(),
                meterSerialNumber = entity.meterSerialNumber.getValueOrNull(),
                prepayDetected = entity.prepayDetected.getValueOrNull(),
                igtIdentifier = entity.igtIdentifier.getValueOrNull(),
                mamMarketParticipant = entity.mamMarketParticipant.getValueOrNull(),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(MeterPointGa()) }.id!!
}
