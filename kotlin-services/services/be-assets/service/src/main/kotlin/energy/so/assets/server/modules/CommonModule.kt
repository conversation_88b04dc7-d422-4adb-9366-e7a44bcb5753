package energy.so.assets.server.modules

import energy.so.assets.server.AssetsGrpcServer
import energy.so.assets.server.config.Constants
import energy.so.assets.server.controllers.RegisterController
import energy.so.assets.server.services.CloseOutboundPhotoRequestHandler
import energy.so.assets.sync.controller.SyncController
import energy.so.assets.sync.service.DefaultSyncService
import energy.so.assets.sync.service.SyncService
import energy.so.commons.database.DatabaseConfigurator
import energy.so.commons.grpc.clients.GrpcServiceConfig
import energy.so.commons.grpc.search.FilterHandler
import energy.so.commons.search.filters.JooqFilterHandler
import energy.so.commons.search.model.filter.Filter
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.accounts.AccountsClient
import energy.so.customers.client.v2.billingaccounts.BlockingBillingAccountsClient
import energy.so.customers.client.v2.calendarholiday.BlockingCalendarHolidayClient
import org.koin.dsl.module
import energy.so.ac.junifer.v1.accounts.AccountsClient as JuniferAccountsClient

object CommonModule {
    private val customersConfig = Constants.get<GrpcServiceConfig>("be-customers")
    private val juniferConfig = Constants.get<GrpcServiceConfig>("be-ac-junifer")

    val module = module {
        single {
            AssetsGrpcServer(
                meterReadingsController = get(),
                propertiesController = get(),
                meterPointController = get(),
                meterReadingSubmissionConsumer = get(),
                updateMeterReadSubmissionStatusConsumer = get(),
                meterController = get(),
                assetController = get(),
                addressController = get(),
                syncController = get(),
                registerController = get(),
                postcodeGspController = get(),
                smartMeterController = get(),
                accountStatesController = get(),
                meterReadSubmissionRequestConsumer = get(),
                persistedMeterReadingSubmissionRequestConsumer = get(),
                photoEvidenceFreshdeskOperationConsumer = get(),
                meterReadingStatusUpdateConsumer = get(),
                outboundPhotoRequestConsumer = get(),
                automaticCloseOutboundRequestsConsumer = get()
            )
        }

        single<SyncService> {
            DefaultSyncService(
                assetRepo = get(),
                meterRepo = get(),
                registerRepo = get(),
                propertyRepo = get(),
                addressRepo = get(),
                meterPointHistoryRepo = get(),
                meterPointRepo = get(),
                estimatedUsageRepo = get(),
                meterReadingRepo = get(),
                meterMeterPointRelRepo = get(),
                meterPointElectricityRepo = get(),
                meterPointGasRepo = get(),
            )
        }

        single { RegisterController() }
        single { SyncController(get()) }

        single { DatabaseConfigurator.startContext() }

        single<FilterHandler<Filter>> { JooqFilterHandler() }

        single { CustomersClient(config = customersConfig) }
        single { BlockingBillingAccountsClient(config = customersConfig) }
        single { AccountsClient(config = customersConfig) }
        single { BlockingCalendarHolidayClient(config = customersConfig) }

        single { JuniferAccountsClient(config = juniferConfig) }

        single {
            CloseOutboundPhotoRequestHandler(
                outboundPhotoRequestRepository = get(),
                juniferAccountsClient = get()
            )
        }
    }
}
