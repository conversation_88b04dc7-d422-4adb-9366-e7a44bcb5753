package energy.so.assets.server.mapping

import energy.so.ac.junifer.v1.generic.GenericSyncClient
import energy.so.ac.junifer.v1.generic.getCoreMappingRequest
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.logging.TraceableLogging

private val logger = TraceableLogging.logger { }

class AcJuniferEntityMapper(
    private val genericSyncClient: GenericSyncClient,
) : EntityMapper {
    override suspend fun getCoreId(entityType: EntityIdentifier, externalId: String): String? =
        genericSyncClient.getCoreMapping(
            getCoreMappingRequest {
                this.juniferId = externalId
                this.entityType = entityType.toSiblingEnum()
            }
        ).id.getValueOrNull()
}
