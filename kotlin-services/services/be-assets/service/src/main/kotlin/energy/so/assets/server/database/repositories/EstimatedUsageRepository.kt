package energy.so.assets.server.database.repositories

/**
 * Temporary not used as of SO-15235
 */
interface EstimatedUsageRepository {
    /**
     * Get the EstimatedUsage list corresponding to the given meter point id.
     * @param meterPointId Long
     * @return List<EstimatedUsage>
     *
     * Temporary not used as of SO-15235
     */
//    fun findByMeterPointId(meterPointId: Long): List<EstimatedUsage>
}
