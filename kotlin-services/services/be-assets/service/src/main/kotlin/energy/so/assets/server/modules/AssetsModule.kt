package energy.so.assets.server.modules

import energy.so.assets.server.controllers.AssetController
import energy.so.assets.server.database.repositories.AssetRepository
import energy.so.assets.server.database.repositories.JooqAssetRepository
import energy.so.assets.server.services.AssetService
import energy.so.assets.sync.repository.AssetEntityRepository
import energy.so.assets.sync.repository.JooqAssetEntityRepository
import org.koin.dsl.module

object AssetsModule {
    val module = module {
        single {
            AssetController(
                assetsService = get(),
                meterService = get()
            )
        }

        single {
            AssetService(
                assetRepository = get()
            )
        }

        single<AssetRepository> {
            JooqAssetRepository(dslContext = get())
        }

        single<AssetEntityRepository> {
            JooqAssetEntityRepository(dslContext = get())
        }
    }
}
