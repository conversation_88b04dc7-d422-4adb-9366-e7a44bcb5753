package energy.so.assets.server.modules

import energy.so.assets.server.controllers.AddressController
import energy.so.assets.server.database.repositories.AddressRepository
import energy.so.assets.server.database.repositories.JooqAddressRepository
import energy.so.assets.server.services.AddressService
import energy.so.assets.sync.repository.AddressEntityRepository
import energy.so.assets.sync.repository.JooqAddressEntityRepository
import org.koin.dsl.module

object AddressModule {
    val module = module {
        single {
            AddressController(
                addressService = get()
            )
        }

        single {
            AddressService(
                addressRepository = get()
            )
        }

        single<AddressRepository> {
            JooqAddressRepository(dslContext = get())
        }

        single<AddressEntityRepository> {
            JooqAddressEntityRepository(
                dslContext = get()
            )
        }
    }
}
