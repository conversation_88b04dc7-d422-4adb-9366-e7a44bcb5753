package energy.so.assets.server.services

import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithMTD
import energy.so.assets.server.models.AccountMeterReadingsRequestDtoWithoutMTD
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.SubmitMeterReadingsResult
import energy.so.assets.server.models.SubmitMeterReadingsResultWithRegisterId
import energy.so.assets.server.models.SubmitMeterReadingsResultWithoutRegisterId

interface MeterReadingSubmissionService {

    /**
     * Submits a series of meter readings.
     * @param meterReadings readings to be submitted
     * @return submitMeterReadingsResult
     */
    suspend fun submitMeterReadings(meterReadings: MeterReadingsRequestDto): SubmitMeterReadingsResult

    /**
     * Submits a series of meter readings for async flow.
     * @param id of the meter reading submission request to be submitted
     * @return submitMeterReadingsResult
     */
    suspend fun submitMeterReadingsSubmissionRequest(id: Long): SubmitMeterReadingsResult

    /**
     * Submits a series of meter readings.
     * @param accountMeterReadings readings to be submitted
     * @return submitMeterReadingsResult
     */
    suspend fun submitAccountMeterReadings(
        accountMeterReadings: List<AccountMeterReadingsRequestDtoWithMTD>,
        ignoreMeterWarnings: Boolean,
    ): SubmitMeterReadingsResultWithRegisterId

    /**
     * Submits a series of meter readings.
     * @param accountMeterReadings readings to be submitted
     * @return submitMeterReadingsResult
     */
    suspend fun submitAccountMeterReadingsWithoutMTD(
        accountMeterReadings: List<AccountMeterReadingsRequestDtoWithoutMTD>,
        ignoreMeterWarnings: Boolean,
    ): SubmitMeterReadingsResultWithoutRegisterId
}
