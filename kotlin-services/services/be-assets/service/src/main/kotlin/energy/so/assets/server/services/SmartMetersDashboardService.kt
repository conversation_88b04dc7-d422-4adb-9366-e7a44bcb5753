package energy.so.assets.server.services

import com.google.protobuf.Empty
import energy.so.assets.smartMeters.v2.GetIHDsResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataFileUrlResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataRequest
import energy.so.assets.smartMeters.v2.GetUpdatedTimeResponse

interface SmartMetersDashboardService {

    suspend fun getUpdatedTime(request: Empty): GetUpdatedTimeResponse
    suspend fun getSmartMeterDataFileUrl(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse
    suspend fun getSmartMeterDataFileUrlParallelProcessing(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse
    suspend fun getIHDsByMPXNs(mpxnList: List<String>): GetIHDsResponse
}
