package energy.so.assets.server.controllers

import energy.so.assets.meterReadings.v2.EnergyUsageServiceGrpcKt
import energy.so.assets.meterReadings.v2.GetEnergyUsageRequest
import energy.so.assets.meterReadings.v2.GetEnergyUsageResponse
import energy.so.assets.server.services.EnergyUsageService
import energy.so.commons.logging.TraceableLogging
import io.opentelemetry.instrumentation.annotations.WithSpan

private val logger = TraceableLogging.logger {}

class EnergyUsageController(
    private val energyUsageService: EnergyUsageService,
) : EnergyUsageServiceGrpcKt.EnergyUsageServiceCoroutineImplBase() {

    @WithSpan
    override suspend fun getEnergyUsage(request: GetEnergyUsageRequest): GetEnergyUsageResponse {
        logger.debug { "[::getEnergyUsage request:$request] getEnergyUsage with supplied data" }
        return energyUsageService.getEnergyUsage(request)
    }
}
