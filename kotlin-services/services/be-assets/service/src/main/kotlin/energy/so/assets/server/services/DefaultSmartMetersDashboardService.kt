package energy.so.assets.server.services

import com.google.protobuf.Empty
import energy.so.ac.esg.api.v1.SmartMetersDashboardClient
import energy.so.ac.esg.v1.getIHDsRequest
import energy.so.assets.server.extensions.toAssestsResponse
import energy.so.assets.server.extensions.toAssetsResponse
import energy.so.assets.server.extensions.toEsgRequest
import energy.so.assets.smartMeters.v2.GetIHDsResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataFileUrlResponse
import energy.so.assets.smartMeters.v2.GetSmartMeterDataRequest
import energy.so.assets.smartMeters.v2.GetUpdatedTimeResponse

class DefaultSmartMetersDashboardService(
    private val acEsgClient: SmartMetersDashboardClient
) : SmartMetersDashboardService {

    override suspend fun getSmartMeterDataFileUrl(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse {
        return acEsgClient.getSmartMeterDataFileUrl(request.toEsgRequest()).toAssestsResponse()
    }

    override suspend fun getSmartMeterDataFileUrlParallelProcessing(request: GetSmartMeterDataRequest): GetSmartMeterDataFileUrlResponse {
        return acEsgClient.getSmartMeterDataFileUrlParallelProcessing(request.toEsgRequest()).toAssestsResponse()
    }

    override suspend fun getIHDsByMPXNs(mpxnList: List<String>): GetIHDsResponse {
        return acEsgClient.getIHDsByMPXNs(
            getIHDsRequest {
                mpxns.addAll(mpxnList)
            }
        ).toAssetsResponse()
    }

    override suspend fun getUpdatedTime(request: Empty): GetUpdatedTimeResponse {
        return acEsgClient.getUpdatedTime(request).toAssetsResponse()
    }
}
