package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Property
import energy.so.commons.model.tables.references.PROPERTY
import org.jooq.DSLContext
import org.jooq.Records
import energy.so.commons.model.tables.pojos.Property as JooqProperty

class JooqPropertyRepository(private val dslContext: DSLContext, private val addressRepository: AddressRepository) :
    PropertyRepository {
    override fun save(property: JooqProperty): JooqProperty {
        val propertyRecord = dslContext.newRecord(PROPERTY, property)
        propertyRecord.store()
        return propertyRecord.into(JooqProperty())
    }

    override fun findById(id: Long): Property? {
        return dslContext.selectFrom(PROPERTY)
            .where(PROPERTY.ID.eq(id), PROPERTY.DELETED.isNull)
            .fetchOne(Records.mapping(::JooqProperty))
            ?.let { propertyRecord ->
                propertyRecord.addressId
                    ?.let { addressId -> addressRepository.findById(addressId) }
                    ?.let { address -> Property.fromJooq(propertyRecord, address) }
            }
    }
}
