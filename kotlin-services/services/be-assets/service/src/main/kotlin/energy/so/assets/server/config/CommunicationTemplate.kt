package energy.so.assets.server.config

data class CommunicationTemplate(
    val openingMeterReadNotifierEmail: String,
    val outboundPhotoRequestEmail: String,
    val outboundPhotoRequestFollowUpEmail: String,
    val uploadOutboundPhotosUrl: String,
    val genericNovaMeterPhotoHistoryModalUrl: String,
)

private const val ACCOUNT_NUMBER_PLACEHOLDER = "{{accountNumber}}"

fun CommunicationTemplate.getNovaMeterPhotoHistoryModalUrl(accountNumber: String?): String =
    this.genericNovaMeterPhotoHistoryModalUrl.replace(ACCOUNT_NUMBER_PLACEHOLDER, "$accountNumber")
