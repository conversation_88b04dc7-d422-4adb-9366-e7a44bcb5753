package energy.so.assets.server.models

import java.time.LocalDateTime

data class PostcodeGsp(
    val id: Long? = null,
    val postcode: String? = null,
    val gspGroupIds: String? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(pojo: energy.so.commons.model.tables.pojos.PostcodeGspCache) = PostcodeGsp(
            id = pojo.id,
            postcode = pojo.postcode,
            gspGroupIds = pojo.gspGroupIds,
            createdAt = pojo.createdAt,
            updatedAt = pojo.updatedAt
        )
    }
}
