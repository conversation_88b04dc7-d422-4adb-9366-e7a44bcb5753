package energy.so.assets.server.controllers

import com.google.cloud.storage.StorageException
import com.google.protobuf.Empty
import energy.so.assets.meterReadings.v2.AddMeterReadingOutboundPhotoRequest
import energy.so.assets.meterReadings.v2.AddMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.BulkXRITMRequest
import energy.so.assets.meterReadings.v2.BulkXRITMResponse
import energy.so.assets.meterReadings.v2.CreateMeterReadingRequest
import energy.so.assets.meterReadings.v2.DeleteMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.ElectricityMeterReadingsByDateResponse
import energy.so.assets.meterReadings.v2.FreshdeskMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.GetElectricityMeterReadingsByDateRequest
import energy.so.assets.meterReadings.v2.GetFirstElectricityMeterReadingRequest
import energy.so.assets.meterReadings.v2.GetLastElectricityMeterReadingRequest
import energy.so.assets.meterReadings.v2.GetMeterReadingPhotoEvidenceRequest
import energy.so.assets.meterReadings.v2.GetMeterReadingPhotoEvidenceResponse
import energy.so.assets.meterReadings.v2.MeterReading
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceGetUrlRequest
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceSignedUpGetUrl
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceUploadUrlRequest
import energy.so.assets.meterReadings.v2.MeterReadingPhotoEvidenceUploadUrlResponse
import energy.so.assets.meterReadings.v2.MeterReadingStatusRequest
import energy.so.assets.meterReadings.v2.MeterReadingStatusResponse
import energy.so.assets.meterReadings.v2.MeterReadingsGrpcKt
import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.meterReadings.v2.MeterReadingsSearchResponse
import energy.so.assets.meterReadings.v2.OutboundMeterReadingPhotoRequest
import energy.so.assets.meterReadings.v2.OutboundRequestsResponse
import energy.so.assets.meterReadings.v2.ReadingSubmissions
import energy.so.assets.meterReadings.v2.ResolveReadingSubmissionErrorRequest
import energy.so.assets.meterReadings.v2.SendOutboundFollowUpEmailRequest
import energy.so.assets.meterReadings.v2.SubmitAccountMeterReadingResponse
import energy.so.assets.meterReadings.v2.SubmitAccountReadingWithTechnicalDetailsRequest
import energy.so.assets.meterReadings.v2.SubmitAccountReadingWithoutTechnicalDetailsRequest
import energy.so.assets.meterReadings.v2.SubmitMeterReadingFlow
import energy.so.assets.meterReadings.v2.SubmitMeterReadingList
import energy.so.assets.meterReadings.v2.SubmitMeterReadingResponse
import energy.so.assets.meterReadings.v2.SubmitMoveInMeterReadingList
import energy.so.assets.meterReadings.v2.electricityMeterReadingsByDateResponse
import energy.so.assets.meterReadings.v2.meterReading
import energy.so.assets.meterReadings.v2.meterReadingPhotoEvidenceSignedUpGetUrl
import energy.so.assets.meterReadings.v2.meterReadingPhotoEvidenceUploadUrl
import energy.so.assets.meterReadings.v2.meterReadingStatusResponse
import energy.so.assets.meterReadings.v2.meterReadingsSearchResponse
import energy.so.assets.meterReadings.v2.outboundRequestsResponse
import energy.so.assets.meterReadings.v2.submitAccountMeterReadingError
import energy.so.assets.meterReadings.v2.submitAccountMeterReadingResponse
import energy.so.assets.meterReadings.v2.submitMeterReadingError
import energy.so.assets.meterReadings.v2.submitMeterReadingResponse
import energy.so.assets.server.config.MeterReadingConfig
import energy.so.assets.server.exceptions.OutboundPhotoRequestException
import energy.so.assets.server.exceptions.OutboundRequestAlreadyExistsForBillingAccountException
import energy.so.assets.server.extensions.submitAccountMeterReadingSuccessResponse
import energy.so.assets.server.extensions.submitMeterReadingSuccessResponse
import energy.so.assets.server.extensions.toAccountMeterReadingsRequestDtoWithMTD
import energy.so.assets.server.extensions.toAccountMeterReadingsRequestDtoWithoutMTD
import energy.so.assets.server.extensions.toErrorResponseProtobuf
import energy.so.assets.server.extensions.toJuniferMeterReadingWithTechnicalDetails
import energy.so.assets.server.extensions.toMeterReadingsMap
import energy.so.assets.server.extensions.toResponseProtobuf
import energy.so.assets.server.extensions.toSubmitAccountMeterReadingErrorProtobuf
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.toAccountErrorResponseProtobuf
import energy.so.assets.server.models.toErrorResponseProtobuf
import energy.so.assets.server.models.toGetMeterReadingPhotoEvidenceResponse
import energy.so.assets.server.models.toProto
import energy.so.assets.server.models.toResponse
import energy.so.assets.server.services.FeatureService
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.MeterReadingPhotoEvidenceService
import energy.so.assets.server.services.MeterReadingSubmissionRequestService
import energy.so.assets.server.services.MeterReadingSubmissionService
import energy.so.assets.server.services.MeterReadingsService
import energy.so.assets.server.services.MeterService
import energy.so.assets.server.services.OutboundPhotoRequestService
import energy.so.assets.server.validators.MeterPointAcceptsReadsValidator
import energy.so.assets.server.validators.MeterReadingError
import energy.so.commons.exceptions.dto.ErrorCategories.ASSETS
import energy.so.commons.exceptions.dto.ErrorCodes.COMMONS_DUPLICATE_REQUEST
import energy.so.commons.exceptions.dto.ErrorCodes.COMMONS_ENTITY_NOT_FOUND
import energy.so.commons.exceptions.dto.ErrorCodes.COMMONS_UNHANDLED_ERROR
import energy.so.commons.exceptions.grpc.AlreadyExistsGrpcException
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.GrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.StorageGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.search.model.mapping.mapPagination
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.dtos.IdRequestStr
import energy.so.commons.v2.search.searchResponseMetadata
import energy.so.commons.validations.validator.Validator
import io.ktor.server.plugins.BadRequestException
import io.opentelemetry.instrumentation.annotations.WithSpan
import kotlinx.serialization.json.Json
import energy.so.commons.storage.StorageException as CommonStorageException

private val logger = TraceableLogging.logger {}

class MeterReadingsController(
    private val meterReadingsService: MeterReadingsService,
    private val meterReadingSubmissionService: MeterReadingSubmissionService,
    private val meterReadingSubmissionRequestService: MeterReadingSubmissionRequestService,
    private val meterReadingPublisher: MessagePublisher<String>,
    private val meterPointAcceptsReadsValidator: MeterPointAcceptsReadsValidator,
    private val meterReadingConfig: MeterReadingConfig,
    private val meterService: MeterService,
    private val meterPointService: MeterPointService,
    private val meterReadingPhotoEvidenceService: MeterReadingPhotoEvidenceService,
    private val outboundPhotoRequestService: OutboundPhotoRequestService,
    private val featureService: FeatureService,
) : MeterReadingsGrpcKt.MeterReadingsCoroutineImplBase() {

    @WithSpan
    override suspend fun createMeterReading(request: CreateMeterReadingRequest): MeterReading =
        meterReadingsService.createMeterReading(request)

    @WithSpan
    override suspend fun getFirstElectricityMeterReading(request: GetFirstElectricityMeterReadingRequest): MeterReading {
        return meterReadingsService.getFirstElectricityMeterReading(request.meterPointId)?.toResponse()
            ?: meterReading { }
    }

    @WithSpan
    override suspend fun getLastElectricityMeterReading(request: GetLastElectricityMeterReadingRequest): MeterReading {
        return meterReadingsService.getLastElectricityMeterReading(request.meterPointId)?.toResponse()
            ?: meterReading { }
    }

    @WithSpan
    override suspend fun getElectricityMeterReadingsByDate(request: GetElectricityMeterReadingsByDateRequest): ElectricityMeterReadingsByDateResponse {
        val readings = meterReadingsService.getElectricityMeterReadingsByDate(
            request.meterPointId,
            request.startDate.toLocalDateTime(),
            request.endDate.toLocalDateTime(),
        )
        return electricityMeterReadingsByDateResponse {
            results.addAll(readings.map { it.toResponse() }.toList())
        }
    }

    @WithSpan
    override suspend fun searchMeterReadings(request: MeterReadingsSearchRequest): MeterReadingsSearchResponse {
        val pagination = mapPagination(request.pagination)
        return meterReadingsService.searchMeterReadings(request)
            .let { list ->
                meterReadingsSearchResponse {
                    results.addAll(list.map { r -> r.toResponse() })
                    metadata = searchResponseMetadata {
                        pagination.pageNumber.run(this::pageNumber::set)
                        pageSize = list.size
                    }
                }
            }
    }

    @WithSpan
    override suspend fun getMeterReadingsPendingStatus(request: MeterReadingStatusRequest): MeterReadingStatusResponse {
        return meterReadingSubmissionRequestService.getMeterReadingsPendingStatus(request.meterPointId).let { from ->
            meterReadingStatusResponse {
                readingSubmissions.addAll(from.map { it.toResponse() })
            }
        }
    }

    @WithSpan
    override suspend fun getLastMeterReadingStatus(request: MeterReadingStatusRequest): ReadingSubmissions {
        return meterReadingSubmissionRequestService.getLastMeterReadingStatus(request.meterPointId).let { from ->
            from?.toResponse()
                ?: throw EntityNotFoundGrpcException(
                    "No meter read submission found by meter point id ${request.meterPointId}"
                )
        }
    }

    @WithSpan
    override suspend fun submitMoveInMeterReadings(request: SubmitMoveInMeterReadingList): SubmitMeterReadingResponse {
        return handleMoveInMeterReadingsAsyncSubmission(
            accountNumber = request.accountNumber,
            email = request.userEmail,
            meterReadings = request.toMeterReadingsMap(meterReadingConfig.ignoreMeterWarnings).values
        )
    }

    @WithSpan
    override suspend fun submitMeterReadings(request: SubmitMeterReadingList): SubmitMeterReadingResponse {
        /*  TODO(10289)
         For now we will apply these validators at controller level for both endpoints.
         To decide if we want to move that validation at service level or leave it like this
         */
        logger.debug("[::submitMeterReadings : userId] ${request.userId}")
        logger.debug("[::submitMeterReadings : billingAccountId] ${request.billingAccountId}")
        val validationResponse = request.validateReadings(meterPointAcceptsReadsValidator)

        if (!validationResponse.success) {
            return validationResponse
        }

        return when (request.flow) {
            SubmitMeterReadingFlow.ASYNC_WITH_MULTIPLE_RETRIES -> {
                val meterReadings = request.toMeterReadingsMap(meterReadingConfig.ignoreMeterWarnings)
                    .values
                handleMeterReadingsAsyncSubmission(meterReadings)
            }

            // IVR flow only, we have just one reading with MTDs
            SubmitMeterReadingFlow.ASYNC_WITH_ONE_RETRY -> {
                meterService.submitMeterReadInfo(request.toJuniferMeterReadingWithTechnicalDetails())
                return submitMeterReadingResponse { }
            }

            SubmitMeterReadingFlow.SYNC -> {
                val meterReadings = request.toMeterReadingsMap(meterReadingConfig.ignoreMeterWarnings)
                    .values
                handleMeterReadingsSyncSubmission(meterReadings)
            }

            else -> throw IllegalStateException("Illegal flow parameter: ${request.flow}")
        }
    }

    @WithSpan
    override suspend fun submitAccountReadingWithTechnicalDetails(
        request: SubmitAccountReadingWithTechnicalDetailsRequest,
    ): SubmitAccountMeterReadingResponse {
        logger.debug("[::submitAccountReadingWithTechnicalDetails : request] $request")

        val meterIds = request.readingsList.map { it.meterId }
        val meterPointToMeter = meterPointService.getMeterPointsByMeterIds(meterIds)
            .map { meterPoint ->
                meterPoint to meterPoint.meters
                    .map { it.id!! }
                    .filter { meterIds.contains(it) }
            }

        val validationResponse = request.validateReadings(meterPointAcceptsReadsValidator, meterPointToMeter)
        if (!validationResponse.success) {
            return validationResponse
        }

        return request.toAccountMeterReadingsRequestDtoWithMTD(meterPointToMeter)
            .groupBy { it.meterPointId }
            .map {
                meterReadingSubmissionService.submitAccountMeterReadings(
                    it.value,
                    meterReadingConfig.ignoreMeterWarnings
                )
            }
            .filter { !it.success }
            .let { errors ->
                when (errors.size) {
                    0 -> submitAccountMeterReadingSuccessResponse
                    else -> submitAccountMeterReadingResponse {
                        success = false
                        errors.flatMap { errorResult ->
                            errorResult.errors.orEmpty()
                                .map { error -> error.toAccountErrorResponseProtobuf() }
                        }.let { mapped -> error.addAll(mapped) }
                    }
                }
            }
    }

    @WithSpan
    override suspend fun submitAccountReadingWithoutTechnicalDetails(
        request: SubmitAccountReadingWithoutTechnicalDetailsRequest,
    ): SubmitAccountMeterReadingResponse {
        logger.debug("[::submitAccountReadingWithoutTechnicalDetails : request] ${request}")

        val meterpointIds = request.readingsList.map { it.meterpointId }
        val meterPoints = meterPointService.getMeterPointsByIds(meterpointIds)

        val validationResponse = request.validateReadings(meterPointAcceptsReadsValidator, meterPoints)
        if (!validationResponse.success) {
            return validationResponse
        }

        return request.toAccountMeterReadingsRequestDtoWithoutMTD(meterPoints)
            .groupBy { it.meterPointId }
            .map {
                meterReadingSubmissionService.submitAccountMeterReadingsWithoutMTD(
                    it.value,
                    meterReadingConfig.ignoreMeterWarnings
                )
            }
            .filter { !it.success }
            .let { errors ->
                when (errors.size) {
                    0 -> submitAccountMeterReadingSuccessResponse
                    else -> submitAccountMeterReadingResponse {
                        success = false
                        errors.flatMap { errorResult ->
                            errorResult.errors.orEmpty()
                                .map { error -> error.toAccountErrorResponseProtobuf() }
                        }.let { mapped -> error.addAll(mapped) }
                    }
                }
            }
    }

    @WithSpan
    override suspend fun resolveReadingSubmissionError(request: ResolveReadingSubmissionErrorRequest): Empty {
        try {
            meterReadingSubmissionRequestService.resolveReadingSubmissionError(request.submissionId)

            return Empty.getDefaultInstance()
        } catch (ex: Exception) {
            when (ex) {
                is FailedPreconditionGrpcException -> throw ex
                is BadRequestException -> throw InvalidArgumentGrpcException(
                    message = ex.localizedMessage,
                    errorCode = COMMONS_ENTITY_NOT_FOUND,
                    errorCategory = ASSETS,
                    cause = ex
                )

                else -> throw UnknownGrpcException(
                    message = ex.message ?: "Unknown error occurred: ${ex.cause}",
                    errorCode = COMMONS_ENTITY_NOT_FOUND,
                    errorCategory = ASSETS,
                    cause = ex.cause
                )
            }
        }
    }

    @WithSpan
    override suspend fun addMeterReadingPhotoEvidence(request: AddMeterReadingPhotoEvidenceRequest): Empty {
        logger.debug { "[::addMeterReadingPhotoEvidence] Received request: $request" }
        try {
            meterReadingPhotoEvidenceService.addMeterReadingPhotoEvidence(request)
        } catch (ex: Exception) {
            throw UnknownGrpcException(
                message = ex.message ?: "Unknown error occurred: ${ex.cause}",
                errorCode = COMMONS_UNHANDLED_ERROR,
                errorCategory = ASSETS,
                cause = ex.cause
            )
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun addMeterReadingOutboundPhoto(request: AddMeterReadingOutboundPhotoRequest): Empty {
        logger.debug { "[::addMeterReadingOutboundPhoto] Received request: $request" }
        try {
            meterReadingPhotoEvidenceService.addMeterReadingOutboundPhoto(request)
        } catch (ex: Exception) {
            when (ex) {
                is OutboundPhotoRequestException -> throw InvalidArgumentGrpcException(
                    message = ex.localizedMessage,
                    errorCode = COMMONS_ENTITY_NOT_FOUND,
                    errorCategory = ASSETS,
                    cause = ex
                )

                else -> throw UnknownGrpcException(
                    message = ex.message ?: "Unknown error occurred: ${ex.cause}",
                    errorCode = COMMONS_UNHANDLED_ERROR,
                    errorCategory = ASSETS,
                    cause = ex.cause
                )
            }
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun deleteMeterReadingPhotoEvidence(request: DeleteMeterReadingPhotoEvidenceRequest): Empty {
        logger.debug { "[::deleteMeterReadingPhotoEvidence] Received request for image: ${request.imageIdentifier}" }
        try {
            meterReadingPhotoEvidenceService.deleteMeterReadingPhotoEvidence(request.imageIdentifier)
        } catch (e: Exception) {
            logger.error(e) { "[::deleteMeterReadingPhotoEvidence] An error occurred: ${e.message}" }
            when (e) {
                is StorageException -> throw StorageGrpcException(
                    message = e.message,
                    cause = e.cause,
                    errorCategory = ASSETS,
                )

                else -> throw UnknownGrpcException(
                    message = e.message ?: "Unknown error occurred: ${e.cause}",
                    errorCode = COMMONS_UNHANDLED_ERROR,
                    errorCategory = ASSETS,
                    cause = e.cause
                )
            }
        }

        return Empty.getDefaultInstance()
    }

    override suspend fun getErroredReadingSubmissions(request: Empty): MeterReadingStatusResponse {
        return meterReadingSubmissionRequestService.getErroredSubmissions().let { from ->
            meterReadingStatusResponse {
                readingSubmissions.addAll(from.map { it.toResponse() })
            }
        }
    }

    @WithSpan
    override suspend fun generateMeterReadingPhotoEvidenceGetUrl(
        request: MeterReadingPhotoEvidenceGetUrlRequest,
    ): MeterReadingPhotoEvidenceSignedUpGetUrl {
        return meterReadingPhotoEvidenceService.generateMeterReadingPhotoEvidenceGetUrl(request).let { url ->
            meterReadingPhotoEvidenceSignedUpGetUrl {
                this.url = url
            }
        }
    }

    @WithSpan
    override suspend fun generateMeterReadingPhotoEvidenceUploadUrls(request: MeterReadingPhotoEvidenceUploadUrlRequest): MeterReadingPhotoEvidenceUploadUrlResponse {
        val responseBuilder = MeterReadingPhotoEvidenceUploadUrlResponse.newBuilder()
        for ((key, value) in meterReadingPhotoEvidenceService.generateMeterReadingPhotoEvidenceUploadUrls(request)) {
            val urlMessage = meterReadingPhotoEvidenceUploadUrl {
                imageName = key
                url = value
            }
            responseBuilder.addImageToUrl(urlMessage)
        }
        return responseBuilder.build()
    }

    @WithSpan
    override suspend fun getMeterReadingPhotoEvidences(request: GetMeterReadingPhotoEvidenceRequest): GetMeterReadingPhotoEvidenceResponse {
        return meterReadingPhotoEvidenceService.getMeterReadingPhotoEvidenceByAccountNumber(request)
            .toGetMeterReadingPhotoEvidenceResponse()
    }

    @WithSpan
    override suspend fun requestOutboundMeterReadingPhoto(request: OutboundMeterReadingPhotoRequest): Empty {
        logger.info("[::requestOutboundMeterReadingPhoto] Received request for account number ${request.accountNumber}")
        try {
            outboundPhotoRequestService.requestOutboundMeterReadingPhoto(request)
        } catch (e: Exception) {
            logger.error(e) { "[::requestOutboundMeterReadingPhoto] An error occurred: ${e.message}" }

            throw handleOutboundException(e)
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun getActiveOutboundRequests(request: IdRequestStr): OutboundRequestsResponse {
        logger.debug("[::getActiveOutboundRequests] get active outbound requests for account number: ${request.id}")

        return outboundPhotoRequestService.getActiveOutboundRequests(request.id)
            .map { it.toProto() }
            .let {
                outboundRequestsResponse { outboundRequests.addAll(it) }
            }
    }

    @WithSpan
    override suspend fun sendOutboundFollowUpEmail(request: SendOutboundFollowUpEmailRequest): Empty {
        logger.debug(
            "[::sendOutboundFollowUpEmail][currentStatus: ${request.outboundRequestCurrentStatus}] Received request for outbound request id ${request.outboundRequestId}"
        )
        try {
            outboundPhotoRequestService.sendFollowUpEmail(request)
        } catch (ex: Exception) {
            logger.error(ex) { "[::sendOutboundFollowUpEmail] An error occurred: ${ex.message}" }
            throw handleOutboundException(ex)
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun cancelOutboundRequest(request: IdRequest): Empty {
        logger.debug("[::cancelOutboundRequest] Cancel outbound request with id: ${request.id}")

        try {
            outboundPhotoRequestService.manualCancelOutboundRequest(request.id)
        } catch (ex: Exception) {
            logger.error(ex) { "[::cancelOutboundRequest] An error occurred: ${ex.message}" }
            throw handleOutboundException(ex)
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun addFreshdeskMeterReadingPhotoEvidences(request: FreshdeskMeterReadingPhotoEvidenceRequest): Empty {
        logger.info("[::addFreshdeskMeterReadingPhotoEvidences] Add freshdesk photo evidences: $request")

        try {
            meterReadingPhotoEvidenceService.addFreshdeskPhotoEvidence(request)
        } catch (ex: Exception) {
            logger.error(ex) { "[::addFreshdeskMeterReadingPhotoEvidences] An error occurred: ${ex.message}" }
            throw handleOutboundException(ex)
        }

        return Empty.getDefaultInstance()
    }

    @WithSpan
    override suspend fun bulkXRITM(request: BulkXRITMRequest): BulkXRITMResponse {
        return meterReadingsService.bulkXRITM(request.mpxnsList)
    }

    private suspend fun handleMeterReadingsSyncSubmission(
        meterReadings: Collection<MeterReadingsRequestDto>,
    ) =
        meterReadings.map { meterReadingSubmissionService.submitMeterReadings(it) }
            .filter { !it.success }
            .let {
                when (it.size) {
                    0 -> submitMeterReadingSuccessResponse
                    else -> it.toErrorResponseProtobuf()
                }
            }

    private fun forwardMeterReadingToPubsub(readings: Collection<MeterReadingsRequestDto>) =
        readings
            .map { QueueMessage(Json.encodeToString(MeterReadingsRequestDto.serializer(), it)) }
            .map { meterReadingPublisher.publishMessage(it) }
            .let { submitMeterReadingSuccessResponse }

    private suspend fun handleMoveInMeterReadingsAsyncSubmission(
        accountNumber: String,
        email: String,
        meterReadings: Collection<MeterReadingsRequestDto>,
    ) = meterReadings
        .map {
            meterReadingSubmissionRequestService.processMoveInMeterReadingsAsyncSubmissions(
                accountNumber,
                email,
                it
            )
        }
        .let { submitMeterReadingSuccessResponse }

    private suspend fun handleMeterReadingsAsyncSubmission(
        meterReadings: Collection<MeterReadingsRequestDto>,
    ) =
        meterReadings
            .map { meterReadingSubmissionRequestService.processMeterReadingsAsyncSubmissions(it) }
            .let { submitMeterReadingSuccessResponse }
}

suspend fun SubmitAccountReadingWithTechnicalDetailsRequest.validateReadings(
    validator: Validator<MeterReadingError, Long>,
    meterPointToMeter: List<Pair<MeterPoint, List<Long>>>,
): SubmitAccountMeterReadingResponse =
    meterPointToMeter.map { it to validator.validate(it.first.id!!) }
        .filter { pair -> pair.second.isInvalid }
        .map { (meterPointToMeterIds, invalidNel) ->
            invalidNel.fold({ error ->
                val errorCode = error[0].validationErrorCode
                val errorMessage = error[0].toString()

                logger.error("[validateReadings] Validation error: $errorMessage")
                readingsList.first { meterPointToMeterIds.second.contains(it.meterId) }
                    .toSubmitAccountMeterReadingErrorProtobuf(errorCode.lowercase(), errorMessage)
            }, {
                submitAccountMeterReadingError { }
            })
        }.toErrorResponseProtobuf()

suspend fun SubmitAccountReadingWithoutTechnicalDetailsRequest.validateReadings(
    validator: Validator<MeterReadingError, Long>,
    meterPoints: List<MeterPoint>,
): SubmitAccountMeterReadingResponse =
    meterPoints.map { validator.validate(it.id!!) }
        .filter { it.isInvalid }
        .map { invalidNel ->
            invalidNel.fold({ error ->
                val errorMessage = error[0].toString()
                val errorCode = error[0].validationErrorCode

                logger.error("[validateReadings] Validation error: $errorMessage")
                submitAccountMeterReadingError {
                    this.errorCode = errorCode.lowercase()
                    message = errorMessage
                }
            }, {
                submitAccountMeterReadingError { }
            })
        }.toErrorResponseProtobuf()

suspend fun SubmitMeterReadingList.validateReadings(validator: Validator<MeterReadingError, Long>) =
    metersList.map { it to validator.validate(it.meterPointId) }
        .filter { pair -> pair.second.isInvalid }
        .map { (requestMeter, invalidNel) ->
            invalidNel.fold({ error ->
                val errorMessage = error[0].toString()

                logger.error("[validateReadings] Validation error: $errorMessage")
                requestMeter.toResponseProtobuf(errorMessage)
            }, {
                submitMeterReadingError { }
            })
        }.toErrorResponseProtobuf()

fun handleOutboundException(e: Throwable): GrpcException {
    when (e) {
        is OutboundRequestAlreadyExistsForBillingAccountException -> throw AlreadyExistsGrpcException(
            message = e.message,
            errorCode = COMMONS_DUPLICATE_REQUEST,
            errorCategory = ASSETS,
            cause = e.cause,
        )

        is EntityNotFoundException -> throw EntityNotFoundGrpcException(e.message ?: e.localizedMessage)
        is OutboundPhotoRequestException -> throw FailedPreconditionGrpcException(
            message = e.localizedMessage,
            errorCode = COMMONS_UNHANDLED_ERROR,
            errorCategory = ASSETS,
            cause = e
        )

        is StorageException, is CommonStorageException -> throw StorageGrpcException(
            message = e.message,
            cause = e.cause,
            errorCategory = ASSETS,
        )

        else -> throw UnknownGrpcException(
            message = e.message ?: "Unknown error occurred: ${e.cause}",
            errorCode = COMMONS_UNHANDLED_ERROR,
            errorCategory = ASSETS,
            cause = e.cause
        )
    }
}
