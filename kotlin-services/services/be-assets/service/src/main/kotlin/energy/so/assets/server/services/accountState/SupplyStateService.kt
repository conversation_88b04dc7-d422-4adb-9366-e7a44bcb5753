package energy.so.assets.server.services.accountState

import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.LOSS_COMPLETED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.LOSS_INITIATED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.ONBOARDING
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.ON_SUPPLY
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.PRE_ONBOARDING
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_CANCELLED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_OBJECTED
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus.REGISTRATION_REJECTED
import energy.so.assets.server.models.MeterPoint
import energy.so.assets.server.models.SupplyState
import energy.so.assets.server.services.MeterPointService
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.v2.dtos.idRequest
import energy.so.customers.changeoftenancy.v2.MoveInChangeOfTenancyDataResponse
import energy.so.customers.changeoftenancy.v2.MoveOutChangeOfTenancyDataResponse
import energy.so.customers.changeoftenancy.v2.moveInChangeOfTenancyDataRequest
import energy.so.customers.changeoftenancy.v2.moveOutChangeOfTenancyDataRequest
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.customers.client.v2.changeoftenancy.ChangeOfTenancyClient
import energy.so.customers.client.v2.productaccounts.ProductAccountsClient
import java.time.LocalDate
import java.time.LocalDate.now

private const val SSD_DAYS_BEFORE = 31L

class SupplyStateService(
    private val productAccountClient: ProductAccountsClient,
    private val meterPointService: MeterPointService,
    private val cotClient: ChangeOfTenancyClient,
    private val billingAccountsClient: BillingAccountsClient
) : AccountStateService<SupplyState> {

    // Helper class to encapsulate ChangeOfTenancy data
    private data class ChangeOfTenancyData(
        val moveOutData: MoveOutChangeOfTenancyDataResponse,
        val futureMoveInData: MoveOutChangeOfTenancyDataResponse,
        val incomingMoveInData: MoveInChangeOfTenancyDataResponse
    )

    override suspend fun computeState(accountId: Long): SupplyState {
        val meterPoints = getMeterPointsByAccount(accountId)
        val cotData = getChangeOfTenancyData(accountId)

        val accountStartDate = billingAccountsClient.getBillingAccountById(idRequest { id = accountId })
            .from.toLocalDate()

        return when {
            cotData.moveOutData.hasData() -> {
                computeStateFromMoveOut(cotData)
            }

            cotData.futureMoveInData.isFutureMoveIn() || cotData.incomingMoveInData.isFutureMoveIn() -> SupplyState.FUTURE_MOVE_IN

            meterPoints.allHaveSimplifiedStatus(PRE_ONBOARDING) &&
                meterPoints.all { meterPoint -> meterPoint.supplyStartDate?.isEqualOrAfter(now()) ?: true } &&
                accountStartDate.isEqualOrAfter(now()) -> SupplyState.PRE_ONBOARDING

            meterPoints.allHaveSimplifiedStatus(ONBOARDING) -> SupplyState.ONBOARDING
            meterPoints.allHaveSimplifiedStatus(REGISTRATION_REJECTED) -> SupplyState.ENROLMENT_REJECTED
            meterPoints.allHaveSimplifiedStatus(REGISTRATION_OBJECTED) -> SupplyState.ENROLMENT_OBJECTED
            meterPoints.allHaveSimplifiedStatus(REGISTRATION_CANCELLED) -> SupplyState.ENROLMENT_CANCELLED

            meterPoints.anyHaveSimplifiedStatuses(ON_SUPPLY) && meterPoints.anyHaveSimplifiedStatuses(
                REGISTRATION_REJECTED,
                REGISTRATION_OBJECTED,
                REGISTRATION_CANCELLED
            ) -> SupplyState.ONBOARDING_ADDITIONAL_METERPOINT_FAILED

            meterPoints.anyHaveSimplifiedStatuses(REGISTRATION_REJECTED) -> SupplyState.ONBOARDING_PARTIALLY_REJECTED

            meterPoints.anyHaveSimplifiedStatuses(REGISTRATION_OBJECTED) -> SupplyState.ONBOARDING_PARTIALLY_OBJECTED

            meterPoints.anyHaveSimplifiedStatuses(REGISTRATION_CANCELLED) -> SupplyState.ONBOARDING_PARTIALLY_CANCELLED

            meterPoints.any {
                val isOnSupply = it.simplifiedSupplyStatus == ON_SUPPLY
                val isLessThan31DaysAfterSSD =
                    it.supplyStartDate?.let { ssd -> !ssd.isBefore(now().minusDays(SSD_DAYS_BEFORE)) } ?: false

                isOnSupply && isLessThan31DaysAfterSSD
            } && !meterPoints.anyHaveSimplifiedStatuses(
                ONBOARDING,
                REGISTRATION_REJECTED,
                REGISTRATION_OBJECTED,
                REGISTRATION_CANCELLED
            ) -> SupplyState.ONBOARDED_RECENTLY

            meterPoints.any {
                it.simplifiedSupplyStatus == ON_SUPPLY &&
                        it.supplyStartDate?.let { ssd -> !ssd.isAfter(now().minusDays(SSD_DAYS_BEFORE)) } ?: false
            } &&
                !meterPoints.anyHaveSimplifiedStatuses(ONBOARDING) &&
                !meterPoints.anyHaveSimplifiedStatuses(LOSS_INITIATED) -> SupplyState.ON_SUPPLY

            meterPoints.anyHaveSimplifiedStatuses(ON_SUPPLY) &&
                meterPoints.anyHaveSimplifiedStatuses(ONBOARDING) -> SupplyState.ONBOARDING_ADDITIONAL_METERPOINT

            meterPoints.anyHaveSimplifiedStatuses(ON_SUPPLY) &&
                meterPoints.anyHaveSimplifiedStatuses(LOSS_INITIATED) -> SupplyState.PARTIAL_LOSS_INITIATED

            meterPoints.allHaveSimplifiedStatus(LOSS_INITIATED, LOSS_COMPLETED) &&
                meterPoints.anyHaveSimplifiedStatuses(LOSS_INITIATED) -> SupplyState.LOSS_INITIATED

            meterPoints.allHaveSimplifiedStatus(LOSS_COMPLETED) -> SupplyState.LOSS_COMPLETE

            else -> SupplyState.UNKNOWN
        }
    }

    private fun computeStateFromMoveOut(cotData: ChangeOfTenancyData): SupplyState {
        val ldor = cotData.moveOutData.data.lastDateOfResponsibility.toLocalDate()
        return when {
            ldor.plusDays(1).isAfter(now()) -> SupplyState.FUTURE_MOVE_OUT
            ldor.isBefore(now()) -> SupplyState.MOVED_OUT
            else -> SupplyState.UNKNOWN
        }
    }

    private suspend fun getChangeOfTenancyData(accountId: Long): ChangeOfTenancyData {
        val moveOutRequest = moveOutChangeOfTenancyDataRequest { billingAccountId = accountId }
        val moveInRequest = moveInChangeOfTenancyDataRequest { billingAccountId = accountId }

        val moveOutData = cotClient.getMoveOutChangeOfTenancyData(moveOutRequest)
        val futureMoveInData = cotClient.getMoveOutChangeOfTenancyDataByNewBillingAccountId(moveOutRequest)
        val incomingMoveInData = cotClient.getMoveInChangeOfTenancyDataByNewBillingAccountId(moveInRequest)

        return ChangeOfTenancyData(moveOutData, futureMoveInData, incomingMoveInData)
    }

    private fun MoveOutChangeOfTenancyDataResponse.isFutureMoveIn() =
        this.hasData() && this.data.lastDateOfResponsibility.toLocalDate().isAfter(now())

    private fun MoveInChangeOfTenancyDataResponse.isFutureMoveIn() =
        this.hasData() && this.data.firstDateOfResponsibility.toLocalDate().isAfter(now())

    private fun List<MeterPoint>.anyHaveSimplifiedStatuses(
        vararg simplifiedStatuses: SimplifiedSupplyStatus,
    ) = any { it.simplifiedSupplyStatus in simplifiedStatuses }

    private fun List<MeterPoint>.allHaveSimplifiedStatus(
        vararg simplifiedStatus: SimplifiedSupplyStatus,
    ) = all { it.simplifiedSupplyStatus in simplifiedStatus }

    private suspend fun getMeterPointsByAccount(accountId: Long) =
        productAccountClient.getProductAccountsByBillingAccountId(idRequest { id = accountId })
            .productAccountsList
            .flatMap { it.agreementsList }
            .flatMap { it.meterPointsList }
            .map { it.id }
            .let { meterPointService.getMeterPointsByIds(it) }

    private fun LocalDate.isEqualOrAfter(other: LocalDate): Boolean {
        return this.isEqual(other) || this.isAfter(other)
    }
}
