package energy.so.assets.server.consumers

import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.jsonFormat
import energy.so.assets.server.services.MeterReadingSubmissionService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

/**
 * A consumer which forwards meter readings it receives to <PERSON><PERSON>, then submits a follow-up message onto another
 * queue which denotes whether the reading was successful, or a failure.
 *
 * @property meterReadingsService
 * @property meterReadingSubmissionService
 * @param projectName
 * @param config
 */
class MeterReadingSubmissionConsumer(
    private val meterReadingSubmissionService: MeterReadingSubmissionService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug("Received message: ${message.data}")
        val meterReading = jsonFormat.decodeFromString(MeterReadingsRequestDto.serializer(), message.data)
        logger.debug {
            "[meterPointId:${meterReading.meterPointId}] " +
                "forward reading to MeterReadingSubmissionService"
        }

        meterReadingSubmissionService.submitMeterReadings(meterReading)
        logger.debug {
            "[meterPointId:${meterReading.meterPointId}] " +
                "Reading forwarded successfully to MeterReadingSubmissionService"
        }
    }
}
