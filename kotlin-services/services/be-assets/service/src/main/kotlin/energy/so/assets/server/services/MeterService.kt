package energy.so.assets.server.services

import energy.so.assets.server.models.Meter
import energy.so.assets.server.models.MeterInfoResponseDto
import energy.so.assets.server.models.MeterReadInfoResponse
import energy.so.commons.grpc.clients.junifer.dtos.MeterReadingsWithTechnicalDetailsDto

interface MeterService {

    /**
     *
     * @param id Long
     * @return MeterDto
     */
    suspend fun getMeterById(id: Long): Meter?

    /**
     *
     * @param id Long
     * @return MeterInfoResponseDto
     */
    suspend fun getMeterInfo(accountNumber: String): MeterInfoResponseDto

    /**
     *
     * @param meterPointId Long
     * @return MeterInfoResponseDto
     */
    suspend fun getMeterReadInfo(meterPointId: Long): MeterReadInfoResponse

    /**
     *
     * @param meterpointId Long
     * @return MeterInfoResponseDto
     */
    suspend fun submitMeterReadInfo(meterReading: MeterReadingsWithTechnicalDetailsDto)

    /**
     * Retrieves a list of meter by ids.
     *
     * @param ids The list of ids.
     * @return List of [Meter].
     */
    suspend fun getMetersByIds(ids: List<Long>): List<Meter>

    /**
     * Retrieves a list of meter by meter point ids.
     *
     * @param ids The list of meter point ids.
     * @return List of [Meter].
     */
    suspend fun getMetersByMeterPointIds(ids: List<Long>): List<Meter>
}
