package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.PostcodeGsp
import energy.so.commons.extension.save
import energy.so.commons.model.tables.pojos.PostcodeGspCache
import energy.so.commons.model.tables.references.POSTCODE_GSP_CACHE
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.PostcodeGspCache as JooqPostcodeGspCache

class JooqPostcodeGspCacheRepository(private val dslContext: DSLContext) : PostcodeGspCacheRepository {

    override fun findByPostcode(postcode: String): PostcodeGsp? =
        dslContext.selectFrom(POSTCODE_GSP_CACHE)
            .where(POSTCODE_GSP_CACHE.POSTCODE.eq(postcode))
            .fetchOneInto(JooqPostcodeGspCache::class.java)
            ?.let { PostcodeGsp.fromJooq(it) }

    override fun save(property: PostcodeGspCache): PostcodeGspCache {
        return dslContext.newRecord(POSTCODE_GSP_CACHE, property)
            .apply { this.save() }
            .run { into(PostcodeGspCache()) }
    }
}
