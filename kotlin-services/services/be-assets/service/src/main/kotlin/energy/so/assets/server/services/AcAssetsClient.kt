package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.EstimatedUsage
import energy.so.ac.junifer.v1.assets.estimatedUsageRequest
import energy.so.ac.junifer.v1.assets.getMeterReadingRequest
import energy.so.assets.server.config.Environment
import energy.so.assets.server.models.MeterReadingStatus
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDate
import java.time.LocalTime

/**
 * This class serves as a wrapper around [AssetsClient] to handle inconsistencies
 * observed in the staging environment. It ensures that certain calls behave as expected
 * in staging without affecting production logic.
 */
class AcAssetsClient(
    private val acAssetsClient: AssetsClient,
    private val environment: Environment,
) {
    suspend fun getMeterPointEstimatedUsage(meterPointId: Long): EstimatedUsage {
        return acAssetsClient.getMeterPointEstimatedUsage(
            estimatedUsageRequest {
                this.meterPointId = meterPointId
                this.queryDate = LocalDate.now().atTime(LocalTime.MIN).toTimestamp()
                this.useHistoricalReadings = true.toNullableBoolean()
            }
        )
    }

    suspend fun hasSmartMeterSentReadingsSinceLastMonth(meterPointId: Long): Boolean {
        val readingsCount = acAssetsClient.getMeterReadings(
            getMeterReadingRequest {
                meterpointId = meterPointId
                fromDt = LocalDate.now().minusMonths(1).toTimestamp()
                toDt = LocalDate.now().toTimestamp()
                status.add(MeterReadingStatus.ACCEPTED.value)
            }
        ).readingsCount

        // Bypassing check in staging due to smart meter doesnt send reading
        return if (environment == Environment.STAGING) true else readingsCount > 0
    }

}
