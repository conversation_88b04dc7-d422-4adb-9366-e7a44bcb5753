package energy.so.assets.server.models

import energy.so.assets.meterPoints.v2.FrequencyConsent
import energy.so.assets.meterPoints.v2.FrequencyConsentUpdate
import energy.so.assets.meterPoints.v2.frequencyConsent
import energy.so.commons.grpc.extensions.StringExtension.asEnum
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toNullableTimestamp
import java.time.LocalDate

data class FrequencyConsentUpdateDto(
    val frequency: FrequencyDTO,
    val mpxn: String,
    val type: ConsentMeterType,
    val fromDt: LocalDate?
) {
    companion object {
        fun fromProto(from: FrequencyConsentUpdate): FrequencyConsentUpdateDto {
            return FrequencyConsentUpdateDto(
                frequency = from.frequency.toSiblingEnum(),
                mpxn = from.mpxn,
                type = from.type.toSiblingEnum(),
                fromDt = from.fromDt.getValueOrNull()?.toLocalDate(),
            )
        }
    }
}

data class FrequencyConsentDto(
    val frequency: FrequencyDTO,
    val mpxn: String,
    val meterPointId: String,
    val fromDt: LocalDate?,
)

fun FrequencyConsentDto.toProto(): FrequencyConsent {
    return frequencyConsent {
        mpxn = <EMAIL>
        meterPointId = <EMAIL>
        frequency = <EMAIL>()
        fromDt = <EMAIL>()
    }
}

fun List<FrequencyConsentDto>.toProto(): List<FrequencyConsent> = map { it.toProto() }

enum class FrequencyDTO {
    MONTHLY,
    DAILY,
    HALF_HOURLY,
}

fun FrequencyDTO.toJuniferFormattedString(): String = when (this) {
    FrequencyDTO.MONTHLY -> "Monthly"
    FrequencyDTO.DAILY -> "Daily"
    FrequencyDTO.HALF_HOURLY -> "Half Hourly"
    else -> throw IllegalArgumentException("Requested frequency is invalid: $this")
}

// Maps Junifer Consent's setting field to FrequencyDTO enum
fun String.toFrequencyEnum(): FrequencyDTO {
    return this.uppercase().replace(" ", "_").asEnum()
}

enum class ConsentMeterType {
    ELEC,
    GAS,
}
