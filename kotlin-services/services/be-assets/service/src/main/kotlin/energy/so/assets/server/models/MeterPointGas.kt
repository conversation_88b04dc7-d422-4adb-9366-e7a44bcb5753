package energy.so.assets.server.models

import energy.so.assets.meterPoints.v2.meterPointGas
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.time.LocalDateTime
import energy.so.assets.meterPoints.v2.MeterPointGas as GrpcMeterPointGas
import energy.so.commons.model.tables.pojos.MeterPointGa as JooqMeterPointGas

/**
 * Model class representing MPRN information about meter point.
 */
data class MeterPointGas(
    val id: Long? = null,
    val designation: String? = null,
    val meterSerialNumber: String? = null,
    val prepayDetected: Boolean? = null,
    val igtIdentifier: String? = null,
    val mamMarketParticipant: String? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(
            jooq: JooqMeterPointGas,
        ): MeterPointGas = MeterPointGas(
            id = jooq.id,
            designation = jooq.designation,
            meterSerialNumber = jooq.meterSerialNumber,
            prepayDetected = jooq.prepayDetected,
            igtIdentifier = jooq.igtIdentifier,
            mamMarketParticipant = jooq.mamMarketParticipant,
            deleted = jooq.deleted,
            createdAt = jooq.createdAt,
            updatedAt = jooq.updatedAt,
        )
    }
}

fun MeterPointGas.toProtobuf(): GrpcMeterPointGas = let { from ->
    meterPointGas {
        id = from.id!!
        createdAt = from.createdAt!!.toTimestamp()
        updatedAt = from.updatedAt!!.toTimestamp()
        from.designation?.let { designation = it.toNullableString() }
        from.meterSerialNumber?.let { meterSerialNumber = it.toNullableString() }
        from.prepayDetected?.let { prepayDetected = it }
        from.igtIdentifier?.let { igtIdentifier = it.toNullableString() }
        from.mamMarketParticipant?.let { mamMarketParticipant = it.toNullableString() }
        from.deleted?.let { deleted = it.toNullableTimestamp() }
    }
}
