package energy.so.assets.server.mapping

import energy.so.commons.logging.TraceableLogging

private val logger = TraceableLogging.logger { }

class JuniferEntityMapper(
    val juniferEntityMappers: List<EntityMapper>,
) : EntityMapper {
    override suspend fun getCoreId(entityType: EntityIdentifier, externalId: String): String? =
        juniferEntityMappers
            .firstNotNullOfOrNull {
                try {
                    it.getCoreId(entityType, externalId)
                } catch (e: Exception) {
                    logger.warn(e) {
                        "Could not get core mapping for entity $entityType and " +
                            "id $externalId from entity mapper ${it.javaClass} because of exception"
                    }
                    null
                }
            }
}
