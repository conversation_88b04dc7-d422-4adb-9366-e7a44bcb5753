package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointGas
import energy.so.commons.model.tables.references.METER_POINT_GAS
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.MeterPointGa as JooqMeterPointGas

class JooqMeterPointGasRepository(
    private val dslContext: DSLContext,
) : MeterPointGasRepository {

    override fun findById(id: Long): MeterPointGas? =
        dslContext.selectFrom(METER_POINT_GAS)
            .where(METER_POINT_GAS.ID.eq(id), METER_POINT_GAS.DELETED.isNull)
            .fetchOneInto(JooqMeterPointGas::class.java)
            ?.let(MeterPointGas::fromJooq)
}
