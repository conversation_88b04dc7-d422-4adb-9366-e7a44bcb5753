package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Property
import energy.so.commons.model.tables.pojos.Property as JooqProperty

interface PropertyRepository {

    /**
     *
     * @param property Property
     * @return Property
     */
    fun save(property: JooqProperty): JooqProperty

    /**
     *
     * @param id Long
     * @return Property?
     */
    fun findById(id: Long): Property?
}
