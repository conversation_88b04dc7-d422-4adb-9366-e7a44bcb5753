package energy.so.assets.server.services

import energy.so.assets.meterReadings.v2.OutboundMeterReadingPhotoRequest
import energy.so.assets.meterReadings.v2.SendOutboundFollowUpEmailRequest
import energy.so.assets.server.models.OutboundPhotoRequest

interface OutboundPhotoRequestService {
    suspend fun requestOutboundMeterReadingPhoto(request: OutboundMeterReadingPhotoRequest)

    /**
     * Processes an outbound request based on its status
     * @param id of the outbound photo request to be processed
     */
    suspend fun processOutboundPhotoRequest(id: Long)

    /**
     * Sends follow-up email of specified type for given outbound request id
     * @param request holding id of the outbound request and the type of email to be sent
     */
    suspend fun sendFollowUpEmail(request: SendOutboundFollowUpEmailRequest)

    /**
     * Retrieves active outbound request for given account number or empty list.
     * @param: accountNumber
     */
    suspend fun getActiveOutboundRequests(accountNumber: String): List<OutboundPhotoRequest>

    /**
     * Cancels the outbound request with given id
     * @param outboundRequestId
     */
    suspend fun manualCancelOutboundRequest(outboundRequestId: Long)

    /**
     * Automatically cancels all outbound requests for which second follow up was sent 7 days ago
     */
    suspend fun automaticCancelOutboundRequest()

}
