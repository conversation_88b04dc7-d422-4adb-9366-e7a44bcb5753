package energy.so.assets.sync.repository

import energy.so.commons.model.tables.references.SYNC_STATUS
import org.jooq.DSLContext

fun getEntityIdForSyncTransactionId(
    syncTransactionId: String,
    tableName: String,
    dslContext: DSLContext,
): Long? {
    return dslContext.select(SYNC_STATUS.ENTITY_ID).from(SYNC_STATUS)
        .where(SYNC_STATUS.TABLE_NAME.eq(tableName), SYNC_STATUS.TRANSACTION_ID.eq(syncTransactionId))
        .fetchOne(SYNC_STATUS.ENTITY_ID)
}

fun insertSyncStatusRecord(
    tableName: String,
    syncTransactionId: String,
    entityId: Long,
    dslContext: DSLContext,
) {
    dslContext.insertInto(SYNC_STATUS)
        .columns(SYNC_STATUS.TABLE_NAME, SYNC_STATUS.TRANSACTION_ID, SYNC_STATUS.ENTITY_ID)
        .values(tableName, syncTransactionId, entityId).execute()
}
