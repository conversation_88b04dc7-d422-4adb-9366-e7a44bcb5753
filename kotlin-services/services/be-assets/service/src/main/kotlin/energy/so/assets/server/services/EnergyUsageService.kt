package energy.so.assets.server.services

import energy.so.assets.meterReadings.v2.FuelType
import energy.so.assets.meterReadings.v2.GetEnergyUsageRequest
import energy.so.assets.meterReadings.v2.GetEnergyUsageResponse
import energy.so.assets.meterReadings.v2.UnitOfMeasurement
import energy.so.assets.meterReadings.v2.energyUsage
import energy.so.assets.meterReadings.v2.getEnergyUsageResponse
import energy.so.assets.meterReadings.v2.usageDetails
import energy.so.commons.session.SessionManager
import energy.so.customers.client.v2.accounts.AccountsClient
import energy.so.customers.client.v2.accounts.findAccountIdsForSession
import energy.so.customers.client.v2.accounts.toUsersWithAccountsOrThrow
import kotlinx.coroutines.runBlocking

class EnergyUsageService(
    private val accountsClient: AccountsClient,
    private val sessionManager: SessionManager,
) {

    suspend fun getEnergyUsage(request: GetEnergyUsageRequest): GetEnergyUsageResponse {
        // check user has access to this account
        val eligibleAccountIds = runBlocking {
            accountsClient.findAccountIdsForSession(sessionManager.getSession())
                .toUsersWithAccountsOrThrow()
                .accountsIds
        }

        if (request.identifier.toLong() !in eligibleAccountIds) {
            throw Exception()
        }

        // todo Stub implementation, SO-8036 will fetch the real data
        return getEnergyUsageResponse {
            energyUsage += stubJanElectricityUsage
            energyUsage += stubJanGasUsage
            energyUsage += stubJanDualUsage
        }
    }

    internal companion object {
        val stubJanElectricityUsage = energyUsage {
            fuelType = FuelType.electricity
            usageDetails += usageDetails {
                durationLabel = "Jan"
                monetaryAmount = 500.77
                kwHUsage = 125.21
                unitOfMeasurement = UnitOfMeasurement.kwH
            }
        }

        val stubJanGasUsage = energyUsage {
            fuelType = FuelType.gas
            usageDetails += usageDetails {
                durationLabel = "Jan"
                monetaryAmount = 254.99
                kwHUsage = 400.23
                unitOfMeasurement = UnitOfMeasurement.kwH
            }
        }

        val stubJanDualUsage = energyUsage {
            fuelType = FuelType.dual
            usageDetails += usageDetails {
                durationLabel = "Jan"
                monetaryAmount = 755.76
                kwHUsage = 525.44
                unitOfMeasurement = UnitOfMeasurement.kwH
            }
        }
    }
}
