package energy.so.assets.server.models

import energy.so.assets.register.v2.register
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import java.math.BigDecimal
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Register as JooqRegister

data class Register(
    val id: Long,
    val meterId: Long,
    val identifier: String?,
    val type: String?,
    val digits: Int,
    val decimalPlaces: Int,
    val rateName: String? = null,
    val usage: BigDecimal,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val fromDttm: LocalDateTime? = null,
    val toDttm: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(pojo: JooqRegister) = Register(
            id = pojo.id!!,
            meterId = pojo.meterId!!,
            identifier = pojo.identifier,
            type = pojo.type,
            digits = pojo.digits!!,
            decimalPlaces = pojo.decimalPlaces!!,
            rateName = pojo.rateName,
            usage = pojo.usage!!,
            deleted = pojo.deleted,
            createdAt = pojo.createdAt!!,
            updatedAt = pojo.updatedAt!!,
            fromDttm = pojo.fromDttm,
            toDttm = pojo.toDttm
        )
    }
}

fun Register.toResponse() = let { from ->
    register {
        id = from.id
        meterId = from.meterId
        identifier = from.identifier.toNullableString()
        type = from.type.toNullableString()
        digits = from.digits
        decimalPlaces = from.decimalPlaces
        rateName = from.rateName.toNullableString()
        usage = from.usage.toLong()
        deleted = from.deleted.toNullableTimestamp()
        createdAt = from.createdAt.toTimestamp()
        updatedAt = from.updatedAt.toTimestamp()
        fromDttm = from.fromDttm.toNullableTimestamp()
        toDttm = from.toDttm.toNullableTimestamp()
    }
}
