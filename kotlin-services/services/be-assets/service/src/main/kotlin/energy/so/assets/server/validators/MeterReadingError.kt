package energy.so.assets.server.validators

import energy.so.assets.server.validators.ValidationErrorCode.FIRST_READ_OUTSIDE_OF_OMR_INTERVAL
import energy.so.assets.server.validators.ValidationErrorCode.INVALID_DCC_SERVICE_STATUS
import energy.so.assets.server.validators.ValidationErrorCode.MISCONFIGURED_METER
import energy.so.assets.server.validators.ValidationErrorCode.NEGATIVE_READ
import energy.so.assets.server.validators.ValidationErrorCode.NO_REGISTER_FOR_METER_POINT
import energy.so.assets.server.validators.ValidationErrorCode.READ_DATE_IN_THE_FUTURE
import energy.so.assets.server.validators.ValidationErrorCode.READ_EXISTS_FOR_DATE
import energy.so.assets.server.validators.ValidationErrorCode.READ_VALUE_OUT_OF_BOUNDS

enum class ValidationErrorCode {
    NEGATIVE_READ,
    MISCONFIGURED_METER,
    INVALID_DCC_SERVICE_STATUS,
    NO_REGISTER_FOR_METER_POINT,
    FIRST_READ_OUTSIDE_OF_OMR_INTERVAL,
    READ_DATE_IN_THE_FUTURE,
    READ_EXISTS_FOR_DATE,
    READ_VALUE_OUT_OF_BOUNDS;

    fun lowercase(): String {
        return this.toString().lowercase()
    }
}

sealed class MeterReadingError(
    val validationErrorCode: ValidationErrorCode,
    open val meterPointId: Long? = null,
)

/**
 * Represents a meter reading with a negative reading.
 *
 * @property meterPointId The meter point the read occurred on.
 * @property read The value of the negative read
 */
data class NegativeRead(
    override val meterPointId: Long,
    val read: Long,
) : MeterReadingError(NEGATIVE_READ, meterPointId) {
    override fun toString(): String {
        return "Cannot have a negative read [$read] on meter point $meterPointId"
    }
}

/**
 * Represents an error that occurs when a meter reading was submitted for a misconfigured meter.
 *
 * @property meterPointId The meter point the read occurred on.
 */
data class MisconfiguredMeter(
    override val meterPointId: Long,
) : MeterReadingError(MISCONFIGURED_METER, meterPointId) {
    override fun toString(): String {
        return "There is no register associated with meter point $meterPointId"
    }
}

data class MeterPointInvalidDccServiceStatus(
    override val meterPointId: Long,
) : MeterReadingError(INVALID_DCC_SERVICE_STATUS, meterPointId) {
    override fun toString(): String {
        return "Invalid dcc service status for meter point $meterPointId"
    }
}

/**
 * Represents an error that occurs when a meter point does not accept reads.
 *
 * @property meterPointId The meter point the read occurred on.
 */
data class MeterPointNotAcceptReads(
    override val meterPointId: Long,
) : MeterReadingError(NO_REGISTER_FOR_METER_POINT, meterPointId) {
    override fun toString(): String {
        return "Meterpoint with id $meterPointId is not accepting reads"
    }
}

data class MeterReadingWindowInvalidError(
    val error: String,
) : MeterReadingError(FIRST_READ_OUTSIDE_OF_OMR_INTERVAL) {
    override fun toString(): String {
        return error
    }
}

data class MeterReadingInvalidReadingDateError(
    override val meterPointId: Long,
) : MeterReadingError(READ_DATE_IN_THE_FUTURE) {
    override fun toString(): String {
        return "Read request for meterpoint with id $meterPointId has reading date in the future"
    }
}

data class MeterReadingAlreadyExistingReadForDateError(
    override val meterPointId: Long,
) : MeterReadingError(READ_EXISTS_FOR_DATE) {
    override fun toString(): String {
        return "Meterpoint with id $meterPointId already has a reading submitted for date"
    }
}

data class MeterReadingoOutOfBoundsError(
    val error: String,
    val registerId: Long? = null
) : MeterReadingError(READ_VALUE_OUT_OF_BOUNDS) {
    override fun toString() = error
}
