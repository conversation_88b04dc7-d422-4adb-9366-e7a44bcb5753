package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointElectricity

/**
 * Repository interface exposing all database operations done on [MeterPointElectricity]
 */
interface MeterPointElectricityRepository {

    /**
     * Returns a [MeterPointElectricity] for given meter point id.
     *
     * @param id The id of the entity.
     * @return A [MeterPointElectricity] matching the id or null otherwise.
     */
    fun findById(id: Long): MeterPointElectricity?
}
