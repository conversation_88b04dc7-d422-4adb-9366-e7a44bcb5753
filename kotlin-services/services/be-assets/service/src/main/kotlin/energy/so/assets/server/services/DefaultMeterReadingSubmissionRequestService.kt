package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.getMeterReadingRequest
import energy.so.assets.meterReadings.v2.meterReadingsSearchRequest
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.MeterReadingSubmissionRequestRepository
import energy.so.assets.server.exceptions.FailedMeterReadingSubmissionRequestException
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingStatus
import energy.so.assets.server.models.MeterReadingStatusResponse
import energy.so.assets.server.models.MeterReadingSubmissionFilter
import energy.so.assets.server.models.MeterReadingSubmissionIdFilter
import energy.so.assets.server.models.MeterReadingSubmissionRequest
import energy.so.assets.server.models.MeterReadingSubmissionRequestStatus
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.SubmitMeterReadingsResult
import energy.so.assets.server.models.jsonFormat
import energy.so.assets.server.models.toMeterReadingStatusResponse
import energy.so.assets.server.models.toPersistedMeterReadingSubmissionRequest
import energy.so.assets.server.publishers.redis.RedisEventPublisher
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.exceptions.MessagePublishingFailedException
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.redis.pubsub.events.meterReadingStatus.MeterReadingEventStatus
import energy.so.commons.redis.pubsub.events.meterReadingStatus.MeterReadingStatusChangedEvent
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.search.pagination
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.users.client.v2.UsersClient
import io.ktor.server.plugins.BadRequestException
import java.time.LocalDate
import java.time.LocalDateTime
import kotlinx.serialization.encodeToString
import org.jooq.JSON

private val logger = TraceableLogging.logger {}

class DefaultMeterReadingSubmissionRequestService(
    private val meterReadingSubmissionRequestRepository: MeterReadingSubmissionRequestRepository,
    private val meterReadingPublisher: MessagePublisher<String>,
    private val userClient: UsersClient,
    private val billingAccountClient: BillingAccountsClient,
    private val maxFailedAttempts: Int,
    private val assetsClient: AssetsClient,
    private val meterPointsRepository: MeterPointsRepository,
    private val meterReadingService: MeterReadingsService,
    private val featureService: FeatureService,
    private val redisEventPublisher: RedisEventPublisher,
) : MeterReadingSubmissionRequestService {

    companion object {
        val METER_READING_SUBMISSION_REQUEST_PENDING_STATUSES =
            MeterReadingSubmissionRequestStatus.values()
                .filterNot {
                    it == MeterReadingSubmissionRequestStatus.SUBMITTED ||
                            it == MeterReadingSubmissionRequestStatus.MANUAL_SUBMISSION
                }

        const val DAYS = 1L
        private const val DEFAULT_METER_READING_PAGE_SIZE = 2000
    }

    override suspend fun processMeterReadingsAsyncSubmissions(reading: MeterReadingsRequestDto) {
        logger.debug("[::processMeterReadingsAsyncSubmissions: reading] $reading")
        val user = userClient.getUserById(idRequest { this.id = reading.userId!! })
        logger.debug("[::processMeterReadingsAsyncSubmissions: user] $user")
        val accountNumber =
            billingAccountClient.getBillingAccountById(idRequest { this.id = user.currentAccountId }).number
        logger.debug("[::processMeterReadingsAsyncSubmissions: accountNumber] $accountNumber")
        val meterPointIdentifier =
            meterPointsRepository.findByIds(
                listOf(reading.meterPointId)
            ).firstOrNull()?.identifier
        logger.debug("[::processMeterReadingsAsyncSubmissions: meterPointIdentifier] $meterPointIdentifier")
        val persistedRequest =
            meterReadingSubmissionRequestRepository.save(
                reading.toPersistedMeterReadingSubmissionRequest(user.email, accountNumber, meterPointIdentifier)
            )
        queueRequest(persistedRequest)
    }


    override suspend fun processMoveInMeterReadingsAsyncSubmissions(
        accountNumber: String,
        email: String,
        reading: MeterReadingsRequestDto,
    ) {
        logger.info("[::processMoveInMeterReadingsAsyncSubmissions: reading] $reading")
        logger.info("[::processMoveInMeterReadingsAsyncSubmissions: accountNumber] $accountNumber")
        val meterPointIdentifier =
            meterPointsRepository.findById(
                reading.meterPointId
            ).identifier
        logger.info("[::processMoveInMeterReadingsAsyncSubmissions: meterPointIdentifier] $meterPointIdentifier")

        val persistedRequest =
            meterReadingSubmissionRequestRepository.save(
                reading.toPersistedMeterReadingSubmissionRequest(email, accountNumber, meterPointIdentifier)
            )
        queueRequest(persistedRequest)
    }

    override fun queuePersistedSubmissions() {
        val persistedRequests =
            meterReadingSubmissionRequestRepository.search(
                MeterReadingSubmissionFilter(statusesList = listOf(MeterReadingSubmissionRequestStatus.PERSISTED))
            )
        persistedRequests.forEach { queueRequest(it) }
    }

    override suspend fun handleJuniferSubmissionResponse(
        submissionRequest: MeterReadingSubmissionRequest,
        response: SubmitMeterReadingsResult,
    ) {
        when (response.success) {
            true -> {
                logger.debug(
                    "[::handleJuniferSubmissionResponse][submissionRequestId: ${submissionRequest.id}] Marking submission reading as submitted and deleting it."
                )
                meterReadingSubmissionRequestRepository.save(
                    submissionRequest.copy(
                        status = MeterReadingSubmissionRequestStatus.SUBMITTED,
                        deleted = LocalDateTime.now()
                    )
                )
            }

            false -> {
                val currentFailedAttempts = submissionRequest.failedAttempts + 1
                if (currentFailedAttempts >= maxFailedAttempts) {
                    logger.debug(
                        "[::handleJuniferSubmissionResponse][submissionRequestId: ${submissionRequest.id}] Reached max attempts. Mark as errored."
                    )
                    meterReadingSubmissionRequestRepository.save(
                        submissionRequest.copy(
                            status = MeterReadingSubmissionRequestStatus.ERRORED,
                            failedAttempts = currentFailedAttempts,
                            lastAttempt = LocalDateTime.now(),
                            debugData = JSON.json(jsonFormat.encodeToString(response.errors)),
                            juniferStatus = null
                        )
                    )
                    publishMeterReadingStatusChangedEvent(
                        submissionRequest.submissionData.billingAccountId,
                        MeterReadingEventStatus.REJECTED
                    )
                } else {
                    logger.debug(
                        "[::handleJuniferSubmissionResponse][submissionRequestId: ${submissionRequest.id}] Tried to process for $currentFailedAttempts times. Retrying."
                    )
                    meterReadingSubmissionRequestRepository.save(
                        submissionRequest.copy(
                            failedAttempts = currentFailedAttempts,
                            lastAttempt = LocalDateTime.now(),
                            debugData = JSON.json(jsonFormat.encodeToString(response.errors))
                        )
                    )
                    throw FailedMeterReadingSubmissionRequestException(
                        "Failed to submit submissionRequest with id ${submissionRequest.id}. Will try again."
                    )
                }
            }
        }
    }

    override suspend fun findMeterReadingSubmissionsByIdFilter(
        filter: MeterReadingSubmissionIdFilter,
    ): MeterReadingSubmissionRequest =
        meterReadingSubmissionRequestRepository.searchById(filter) ?: throw EntityNotFoundException(
            clazz = MeterReadingSubmissionRequest::class,
            parameters = mapOf("meterReadingSubmissionId" to filter.id),
        )

    override suspend fun getErroredSubmissions(): List<MeterReadingStatusResponse> {
        return meterReadingSubmissionRequestRepository.getErroredSubmissions()
            .map { it.toMeterReadingStatusResponse() }
    }

    override suspend fun updateContactedReadsMap() {
        val now = LocalDate.now().toTimestamp()
        val yesterday = LocalDate.now().minusDays(1).toTimestamp()

        val meterReadingSubmissionRequestMap = meterReadingSubmissionRequestRepository.search(
            MeterReadingSubmissionFilter(
                statusesList = listOf(MeterReadingSubmissionRequestStatus.CONTACTED),
                createdAt = LocalDateTime.now().minusDays(DAYS)
            )
        ).groupBy { it.submissionData.meterPointId }

        meterReadingSubmissionRequestMap.map { (meterPointId, submissions) ->
            val latestSubmissionDate = submissions.maxOf { it.createdAt!! }

            val latestJuniferReadDate = assetsClient.getMeterReadings(
                getMeterReadingRequest {
                    meterpointId = meterPointId
                    fromDt = yesterday
                    toDt = now
                }
            ).readingsList.filter { reading -> reading.status == MeterReadingStatus.ACCEPTED.value }
                .maxOfOrNull { it.receivedDttm.toLocalDateTime() }

            if (latestJuniferReadDate != null && latestJuniferReadDate.isAfter(latestSubmissionDate)) {
                meterReadingSubmissionRequestRepository.resolveMeterPointContactedSubmission(
                    submissions.map { it.submissionData.meterPointId }
                )
            }
        }
    }

    override suspend fun updateMeterReadingJuniferStatus() {
        val meterReadingFilter = MeterReadingSubmissionFilter(
            createdAt = LocalDateTime.now().minusHours(2),
            juniferStatus = listOf(MeterReadingStatus.PENDING),
            deleted = null,
        )
        val meterReadingSubmissions = meterReadingSubmissionRequestRepository.search(meterReadingFilter)
        logger.debug {
            "[::updateMeterReadingJuniferStatus] found [database readings] " +
                    "$meterReadingSubmissions in status [PENDING]"
        }

        val meterReadingSearchRequest = meterReadingsSearchRequest {
            this.meterPointId.addAll(meterReadingSubmissions.map { it.submissionData.meterPointId })
            this.status.addAll(MeterReadingStatus.values().map { it.toSiblingEnum() })
            this.startDate = LocalDateTime.now().minusHours(2).toTimestamp()
            this.pagination = pagination {
                pageSize = DEFAULT_METER_READING_PAGE_SIZE
                pageNumber = 1
            }
        }

        val juniferReadings = meterReadingService.searchMeterReadings(meterReadingSearchRequest)
        logger.debug { "[::updateMeterReadingJuniferStatus] found [junifer readings]: $juniferReadings " }

        meterReadingSubmissions.forEach { dbReading ->
            juniferReadings.getMatchingJuniferReading(dbReading)
                ?.let { reading ->
                    logger.debug {
                        "[::updateMeterReadingJuniferStatus] found [matching reading] $reading"
                    }

                    if (reading.status != null) {
                        meterReadingSubmissionRequestRepository.save(dbReading.copy(juniferStatus = reading.status))
                        publishMeterReadingStatusChangedEvent(
                            dbReading.submissionData.billingAccountId,
                            reading.status.toEventStatus()
                        )
                    }
                }
                ?: let {
                    if (dbReading.status == MeterReadingSubmissionRequestStatus.ERRORED) {
                        logger.debug {
                            "[::updateMeterReadingJuniferStatus] no matching Junifer reading found," +
                                    "submission errored, resetting the junifer status in database"
                        }
                        meterReadingSubmissionRequestRepository.save(dbReading.copy(juniferStatus = null))
                    }
                }
        }
    }

    private fun MeterReadingStatus.toEventStatus() =
        when (this) {
            MeterReadingStatus.ACCEPTED -> MeterReadingEventStatus.ACCEPTED
            else -> MeterReadingEventStatus.REJECTED
        }

    private fun List<MeterReading>.getMatchingJuniferReading(
        meterReadingSubmission: MeterReadingSubmissionRequest,
    ) = this
        .filter { it.status != MeterReadingStatus.PENDING && it.status != MeterReadingStatus.UNKNOWN }
        .filter {
            it.meterPointId == meterReadingSubmission.submissionData.meterPointId
        }
        .firstOrNull {
            meterReadingSubmission.submissionValues
                .any { submissionValue -> submissionValue.value == it.cumulative.toLong() }
        }

    private fun publishMeterReadingStatusChangedEvent(
        accountId: Long?,
        status: MeterReadingEventStatus,
    ) {
        accountId?.also {
            redisEventPublisher.publishMeterReadingStatusChangedEvent(
                MeterReadingStatusChangedEvent(it, status)
            )
        }
    }

    private fun queueRequest(request: MeterReadingSubmissionRequest) {
        logger.debug("[::queueRequest: persistedRequest] $request")

        try {
            QueueMessage(request.id.toString())
                .let { meterReadingPublisher.publishMessage(it) }

            logger.debug("[::queueRequest] Message published")
            meterReadingSubmissionRequestRepository.updateStatusIfNotAlreadySubmitted(
                request.id!!,
                MeterReadingSubmissionRequestStatus.QUEUED
            )
        } catch (ex: MessagePublishingFailedException) {
            logger.error("[::queueRequest] Failed to publish message to pubsub: $request")
        }
    }

    @Deprecated("FE use Hasura mutation to do this")
    override suspend fun getMeterReadingsPendingStatus(meterPointId: Long): List<MeterReadingStatusResponse> {
        return meterReadingSubmissionRequestRepository.search(
            MeterReadingSubmissionFilter(
                meterPointId = meterPointId,
                statusesList = METER_READING_SUBMISSION_REQUEST_PENDING_STATUSES
            ),
        ).map { it.toMeterReadingStatusResponse() }
    }

    override suspend fun getLastMeterReadingStatus(meterPointId: Long): MeterReadingStatusResponse? {
        return meterReadingSubmissionRequestRepository.search(
            MeterReadingSubmissionFilter(
                meterPointId = meterPointId,
                deleted = null
            ),
        ).sortedByDescending { it.createdAt }.firstOrNull()?.toMeterReadingStatusResponse()
    }

    @Deprecated("FE use Hasura mutation to do this")
    override suspend fun resolveReadingSubmissionError(submissionId: Long) {
        val meterReadingSubmissionRequest =
            meterReadingSubmissionRequestRepository.searchById(
                MeterReadingSubmissionIdFilter(
                    id = submissionId,
                    statusesList = listOf(
                        MeterReadingSubmissionRequestStatus.ERRORED,
                        MeterReadingSubmissionRequestStatus.CONTACTED
                    )
                )
            ) ?: throw BadRequestException("There is no submission with ERRORED or CONTACT_NEEDED status")

        meterReadingSubmissionRequest.submissionValues.let {
            it.map {
                val readings = assetsClient.getMeterReadings(
                    getMeterReadingRequest {
                        meterpointId = meterReadingSubmissionRequest.submissionData.meterPointId
                        fromDt = it.date.toTimestamp()
                        toDt = LocalDate.now().plusDays(1).toTimestamp()
                    }
                )

                if (readings.readingsList.isEmpty()) {
                    throw FailedPreconditionGrpcException(
                        message = "The reading submission couldn't be resolved due to no reading data being in Junifer, after the submission date",
                        errorCategory = ErrorCategories.ASSETS,
                        errorCode = ErrorCodes.COMMONS_UNHANDLED_ERROR,
                    )
                } else {
                    meterReadingSubmissionRequestRepository.resolveSubmission(submissionId)
                }
            }
        }
    }
}
