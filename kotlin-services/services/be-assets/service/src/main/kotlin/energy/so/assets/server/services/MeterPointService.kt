package energy.so.assets.server.services

import energy.so.assets.meterPoints.v2.MeterIdToRegistersResponse
import energy.so.assets.meterPoints.v2.MeterPointsResponse
import energy.so.assets.meterPoints.v2.MpxnSmartPayAsYouGoEligibilityResponse
import energy.so.assets.meterPoints.v2.SmartMeterEvTariffEligibilityResponse
import energy.so.assets.server.models.EstimatedUsage
import energy.so.assets.server.models.FrequencyConsentDto
import energy.so.assets.server.models.FrequencyConsentUpdateDto
import energy.so.assets.server.models.MeterPoint
import java.time.LocalDate

interface MeterPointService {

    /**
     * Returns meter points for given asset ids.
     *
     * @param assetIds The asset ids
     * @return [MeterPointsResponse] containing list with retrieved meter point data
     */
    suspend fun getMeterPointsByAssetIds(assetIds: List<Long>): List<MeterPoint>

    /**
     * Returns estimated usages for given meter point id.
     *
     * @param meterPointId The meter point id
     * @return [EstimatedUsage] containing list with retrieves estimated usages data
     */
    suspend fun getEstimatedUsage(meterPointId: Long): List<EstimatedUsage>

    /**
     * Returns meter points for given address ids.
     *
     * @param addressIds The request containing address ids
     * @return List of [MeterPoint]: containing list with retrieved meter point data
     */
    suspend fun getMeterPointsByAddressIds(addressIds: List<Long>): List<MeterPoint>

    /**
     * Returns meter points for given ids.
     *
     * @param ids The request containing ids
     * @return List of [MeterPoint]: containing list with retrieved meter point data
     */
    suspend fun getMeterPointsByIds(ids: List<Long>): List<MeterPoint>

    /**
     * Returns meter points for given meter ids.
     *
     * @param ids The request containing meter ids
     * @return List of [MeterPoint]: containing list with retrieved meter point data
     */
    suspend fun getMeterPointsByMeterIds(ids: List<Long>): List<MeterPoint>

    /**
     * Returns meter ID to meter registers for given ids.
     *
     * @param ids The request containing ids
     * @return List of [MeterPoint]: containing list with retrieved meter point data
     */
    suspend fun getMeterIdToRegisters(meterPointId: Long): MeterIdToRegistersResponse

    suspend fun getMeterPointByMPXN(mpxn: String): MeterPoint?

    suspend fun checkSmartMeterEvTariffEligibility(identifier: String): SmartMeterEvTariffEligibilityResponse

    suspend fun getMeterPointsByMPXNs(mpxnIdentifiers: List<String>): MeterPointsResponse

    suspend fun checkMpxnSmartPayAsYouGoEligibility(meterPointIds: List<Long>): MpxnSmartPayAsYouGoEligibilityResponse

    suspend fun updateSmartMeterReadingFrequency(frequencyConsents: List<FrequencyConsentUpdateDto>)

    suspend fun getCurrentSmartMeterReadingFrequency(
        mpxns: List<String>, validDt: LocalDate? = null,
    ): List<FrequencyConsentDto>
}
