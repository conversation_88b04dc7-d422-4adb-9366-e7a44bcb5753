package energy.so.assets.server.modules

import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.EnvironmentConfig
import energy.so.assets.server.controllers.MeterPointController
import energy.so.assets.server.database.repositories.EstimatedUsageRepository
import energy.so.assets.server.database.repositories.JooqEstimatedUsageRepository
import energy.so.assets.server.database.repositories.JooqMeterPointsRepository
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.services.AcAssetsClient
import energy.so.assets.server.services.DefaultMeterPointService
import energy.so.assets.server.services.DefaultRegisterService
import energy.so.assets.server.services.MeterPointService
import energy.so.assets.server.services.RegisterService
import energy.so.assets.sync.repository.EstimatedUsageEntityRepository
import energy.so.assets.sync.repository.JooqEstimatedUsageEntityRepository
import energy.so.assets.sync.repository.JooqMeterMeterPointRelEntityRepository
import energy.so.assets.sync.repository.JooqMeterPointElectricityEntityRepository
import energy.so.assets.sync.repository.JooqMeterPointGasEntityRepository
import energy.so.assets.sync.repository.JooqMeterPointHistoryEntityRepository
import energy.so.assets.sync.repository.JooqMeterPointsEntityRepository
import energy.so.assets.sync.repository.MeterMeterPointRelEntityRepository
import energy.so.assets.sync.repository.MeterPointElectricityEntityRepository
import energy.so.assets.sync.repository.MeterPointGasEntityRepository
import energy.so.assets.sync.repository.MeterPointHistoryEntityRepository
import energy.so.assets.sync.repository.MeterPointsEntityRepository
import energy.so.commons.grpc.clients.GrpcServiceConfig
import org.koin.dsl.module

object MeterPointsModule {
    private val juniferConfig = Constants.get<GrpcServiceConfig>("be-ac-junifer")


    private val environmentConfig = Constants.get<EnvironmentConfig>("environment")

    val module = module {
        single {
            CustomersSyncClient(juniferConfig)
        }

        single {
            MeterPointController(
                meterPointService = get(),
                meterReadingsService = get(),
                registerService = get(),
                meterReadingConfig = get(),
                customerClient = get(),
                featureService = get(),
            )
        }

        single<RegisterService> {
            DefaultRegisterService(
                registerRepository = get()
            )
        }

        single {
            AcAssetsClient(
                acAssetsClient = get(),
                environment = environmentConfig.env
            )
        }

        single<MeterPointService> {
            DefaultMeterPointService(
                meterPointsRepository = get(),
                registerRepository = get(),
                acAssetsSyncClient = get(),
                customersSyncClient = get(),
            )
        }

        single<MeterPointsRepository> {
            JooqMeterPointsRepository(
                dslContext = get(),
                meterRepository = get(),
                propertyRepository = get(),
                meterPointGasRepository = get(),
                meterPointElectricityRepository = get(),
                meterPointHistoryRepository = get(),
                estimatedUsageRepository = get(),
                meterReadingConfig = get()
            )
        }

        single<EstimatedUsageRepository> {
            JooqEstimatedUsageRepository(dslContext = get())
        }

        single<MeterPointsEntityRepository> {
            JooqMeterPointsEntityRepository(
                dslContext = get()
            )
        }

        single<MeterPointElectricityEntityRepository> {
            JooqMeterPointElectricityEntityRepository(
                dslContext = get()
            )
        }

        single<MeterPointGasEntityRepository> {
            JooqMeterPointGasEntityRepository(
                dslContext = get()
            )
        }

        single<MeterPointHistoryEntityRepository> {
            JooqMeterPointHistoryEntityRepository(
                dslContext = get()
            )
        }

        single<EstimatedUsageEntityRepository> {
            JooqEstimatedUsageEntityRepository(
                dslContext = get()
            )
        }

        single<MeterMeterPointRelEntityRepository> {
            JooqMeterMeterPointRelEntityRepository(
                dslContext = get()
            )
        }
    }
}
