package energy.so.assets.server.controllers

import energy.so.assets.addresses.v2.AddressesResponse
import energy.so.assets.addresses.v2.Postcode
import energy.so.assets.addresses.v2.addressesResponse
import energy.so.assets.server.models.toProtobuf
import energy.so.assets.server.services.AddressService
import energy.so.assets.v2.addresses.AddressServiceGrpcKt
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import io.opentelemetry.instrumentation.annotations.WithSpan

class AddressController(private val addressService: AddressService) :
    AddressServiceGrpcKt.AddressServiceCoroutineImplBase() {

    @WithSpan
    override suspend fun getAddressByPostcode(request: Postcode): AddressesResponse =
        addressService.getAddressesByPostcode(request.value)
            .takeIf { it.isNotEmpty() }
            ?.let { from ->
                addressesResponse {
                    addresses.addAll(from.map { it.toProtobuf() })
                }
            } ?: throw EntityNotFoundGrpcException("Could not find any address with postcode: ${request.value}")
}
