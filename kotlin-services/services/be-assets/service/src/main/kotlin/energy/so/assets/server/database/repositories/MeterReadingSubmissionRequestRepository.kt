package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterReadingSubmissionFilter
import energy.so.assets.server.models.MeterReadingSubmissionIdFilter
import energy.so.assets.server.models.MeterReadingSubmissionRequest
import energy.so.assets.server.models.MeterReadingSubmissionRequestStatus

interface MeterReadingSubmissionRequestRepository {
    fun save(meterReadingSubmissionRequest: MeterReadingSubmissionRequest): MeterReadingSubmissionRequest

    fun search(meterReadingSubmissionFilter: MeterReadingSubmissionFilter): List<MeterReadingSubmissionRequest>

    fun searchById(meterReadingSubmissionIdFilter: MeterReadingSubmissionIdFilter): MeterReadingSubmissionRequest?

    fun updateStatus(id: Long, newStatus: MeterReadingSubmissionRequestStatus)

    fun updateStatusIfNotAlreadySubmitted(id: Long, newStatus: MeterReadingSubmissionRequestStatus)

    fun resolveSubmission(id: Long)

    fun resolveMeterPointContactedSubmission(meterPointId: List<Long>)

    fun getErroredSubmissions(): List<MeterReadingSubmissionRequest>
}
