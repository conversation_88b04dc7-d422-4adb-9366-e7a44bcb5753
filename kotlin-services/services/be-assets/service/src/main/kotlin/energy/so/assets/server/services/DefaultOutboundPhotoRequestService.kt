package energy.so.assets.server.services

import energy.so.ac.junifer.v1.accounts.AccountsClient
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.assets.meterReadings.v2.OutboundMeterReadingPhotoRequest
import energy.so.assets.meterReadings.v2.OutboundRequestStatus
import energy.so.assets.meterReadings.v2.SendOutboundFollowUpEmailRequest
import energy.so.assets.server.config.CommunicationTemplate
import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.OutboundPhotoConfig
import energy.so.assets.server.config.getNovaMeterPhotoHistoryModalUrl
import energy.so.assets.server.database.repositories.MeterReadingPhotoEvidenceRepository
import energy.so.assets.server.database.repositories.OutboundPhotoRequestRepository
import energy.so.assets.server.exceptions.OutboundPhotoRequestException
import energy.so.assets.server.exceptions.OutboundRequestAlreadyExistsForBillingAccountException
import energy.so.assets.server.models.EmailType
import energy.so.assets.server.models.OutboundPhotoRequest
import energy.so.assets.server.models.OutboundPhotoRequestFilter
import energy.so.assets.server.models.OutboundPhotoRequestStatus
import energy.so.assets.server.models.OutboundPhotoRequestStatus.CANCELLED
import energy.so.assets.server.models.OutboundPhotoRequestStatus.EMAIL_SENT
import energy.so.assets.server.models.OutboundPhotoRequestStatus.FIRST_FOLLOW_UP_SENT
import energy.so.assets.server.models.OutboundPhotoRequestStatus.FRESHDESK_TICKET_CREATED
import energy.so.assets.server.models.OutboundPhotoRequestStatus.REQUESTED
import energy.so.assets.server.models.OutboundPhotoRequestStatus.SECOND_FOLLOW_UP_SENT
import energy.so.assets.server.models.toOutboundPhotoRequest
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.v2.dtos.idRequestStr
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.communications.v1.dtos.RecipientDto
import energy.so.communications.v1.dtos.SenderDto
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.billingaccounts.BlockingBillingAccountsClient
import energy.so.customers.v2.customers.Customer
import energy.so.email.client.v1.dto.EmailDto
import energy.so.email.client.v1.enums.TemplateName
import energy.so.email.client.v1.impl.DefaultEmailClient
import energy.so.freshdesk.client.v1.FreshDeskClient
import energy.so.freshdesk.client.v1.dto.FreshdeskBulkUpdateTicketsPropertiesDto
import energy.so.freshdesk.client.v1.dto.FreshdeskBulkUpdateTicketsRequestDto
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateNoteRequestDto
import energy.so.freshdesk.client.v1.dto.FreshdeskCreateTicketRequestDto
import energy.so.freshdesk.client.v1.dto.FreshdeskJobResponseDto
import energy.so.users.v2.FeatureName

private val logger = TraceableLogging.logger {}

class DefaultOutboundPhotoRequestService(
    private val outboundPhotoRequestRepository: OutboundPhotoRequestRepository,
    private val meterReadingPhotoRepository: MeterReadingPhotoEvidenceRepository,
    private val processOutboundPhotoRequestPublisher: MessagePublisher<String>,
    private val customersClient: CustomersClient,
    private val billingAccountsClient: BlockingBillingAccountsClient,
    private val communicationClient: CommunicationClient,
    private val freshDeskClient: FreshDeskClient,
    private val juniferAccountsClient: AccountsClient,
    private val closeOutboundPhotoRequestHandler: CloseOutboundPhotoRequestHandler,
    private val featureService: FeatureService,
    private val emailClient: DefaultEmailClient,
) : OutboundPhotoRequestService {

    companion object {
        const val OUTBOUND_REQUEST_FRESHDESK_TICKET_TYPE = "Customer"
        const val METER_READING_SUBMISSION = "Meter Reading Submission"
        const val METER_READING_SUBMISSION_ERROR_INVESTIGATING = "Meter Read Submission Error - Investigating"
        const val PHOTO_REQUEST_PREFIX = "Photo Request - "
        const val FIRST_FOLLOW_UP_EMAIL_SENT = "First follow-up email sent"
        const val SECOND_FOLLOW_UP_EMAIL_SENT = "Second follow-up email sent"
        val FRESHDESK_PENDING_JOB_STATUSES = listOf("PENDING", "IN_PROGRESS")
        const val FRESHDESK_AUTOMATICALLY_CANCEL_TICKET_NOTE =
            "This photo request was automatically cancelled due to no response from the customer after the final follow-up email was sent"
        const val FRESHDESK_ALTERNATE_PHOTO_UPLOAD_NOTE =
            "The customer has sent us photos outside of the photo request journey and they've been uploaded on their Nova account."
        const val JUNIFER_NOTE_SUBJECT = "General Note"
        const val JUNIFER_NOTE_INITIAL_EMAIL_SUMMARY = "Photo requested via Nova"
        const val JUNIFER_NOTE_FIRST_FOLLOW_UP_SUMMARY = "Photo request - first follow-up email sent"
        const val JUNIFER_NOTE_SECOND_FOLLOW_UP_SUMMARY = "Photo request - second follow-up email sent"
        const val JUNIFER_NOTE_PHOTOS_UPLOADED_SUMMARY = "Photo request - customer uploaded photos"
        const val JUNIFER_NOTE_MANUAL_CLOSE_OUTBOUND_REQUEST_SUMMARY = "Photo request - manually cancelled"
        const val JUNIFER_NOTE_AUTOMATIC_CLOSE_OUTBOUND_REQUEST_SUMMARY = "Photo request - automatically cancelled"
        const val SHADOW_EMAIL_ADDRESS = "<EMAIL>"
        val communicationTemplate = Constants.get<CommunicationTemplate>("communication-template")
        val outboundPhotoConfig = Constants.get<OutboundPhotoConfig>("outboundPhoto")
    }

    override suspend fun requestOutboundMeterReadingPhoto(request: OutboundMeterReadingPhotoRequest) {
        logger.debug { "[::requestOutboundMeterReadingPhoto] Requesting outbound meter reading photo for account number '${request.accountNumber}'" }

        if (!featureService.isFeatureEnabled(FeatureName.TMP_SO_19346_OUTBOUND_PHOTO_REQUEST)) {
            return
        }

        if (outboundPhotoRequestRepository.accountNumberHasOpenRequest(request.accountNumber))
            throw OutboundRequestAlreadyExistsForBillingAccountException(request.accountNumber)

        val accountId = billingAccountsClient.getBillingAccountByAccountNumber(
            idRequestStr {
                id = request.accountNumber
            }
        ).id

        logger.info { "[::requestOutboundMeterReadingPhoto][${request.accountNumber}] Saving request" }
        outboundPhotoRequestRepository.save(request.toOutboundPhotoRequest(accountId, REQUESTED))
            .also {
                logger.info { "[::requestOutboundMeterReadingPhoto][${it.id}] Publishing message" }
                processOutboundPhotoRequestPublisher.publishMessage(
                    QueueMessage(it.id.toString())
                )
            }

        logger.info { "[::requestOutboundMeterReadingPhoto] Message published" }
    }

    override suspend fun processOutboundPhotoRequest(id: Long) {
        logger.debug { "[::processOutboundPhotoRequest][outboundPhotoRequestId:$id]" }

        var outboundPhotoRequest = outboundPhotoRequestRepository.findById(
            OutboundPhotoRequestFilter(
                id = id,
                statusesList = listOf(REQUESTED, EMAIL_SENT),
            )
        )

        if (outboundPhotoRequest == null) {
            logger.error("[::processOutboundPhotoRequest] no active outbound photo request found with id: $id")
            return
        }

        logger.debug { "[::processOutboundPhotoRequest] Getting customer with account number ${outboundPhotoRequest!!.accountNumber!!}" }
        val customer = try {
            getCustomer(outboundPhotoRequest)
        } catch (ex: EntityNotFoundGrpcException) {
            logger.error(ex) {
                "[::processOutboundPhotoRequest] Customer details for outbound request with id $id not found"
            }

            return
        }

        var emailMessageId: Long? = null
        if (outboundPhotoRequest.status == REQUESTED) {
            val customerEmail = customer.primaryContact.email

            logger.debug { "[::processOutboundPhotoRequest][email=$customerEmail] Sending Email" }

            if (!customer.primaryContact.hasEmail()) {
                logger.error("[::processOutboundPhotoRequest] There is no email for contact with id ${customer.primaryContact.id}. Skipping")
                return
            }

            emailMessageId = sendEmailToCustomer(
                customer,
                communicationTemplate.outboundPhotoRequestEmail,
                mutableMapOf(
                    "account_number" to outboundPhotoRequest.accountNumber!!,
                    "upload_url" to communicationTemplate.uploadOutboundPhotosUrl,
                    "customer_forename" to customer.firstName,
                    "fuel_type" to (outboundPhotoRequest.fuel?.name ?: ""),
                    "request_reason" to (outboundPhotoRequest.requestReason ?: ""),
                    "delivery_email_address" to customerEmail,
                ), outboundPhotoConfig
            )

            if (featureService.isFeatureEnabled(FeatureName.TMP_SO_19496_SHADOW_SEND_VIA_DOTDIGITAL)) {
                try {
                    logger.info(
                        "[::processOutboundPhotoRequest] [SHADOW] Sending email to queue for OutboundPhotoRequest with id $id"
                    )
                    emailClient.sendEmail(
                        EmailDto(
                            toAddresses = listOf(SHADOW_EMAIL_ADDRESS),
                            templateName = TemplateName.OUTBOUND_PHOTO_REQUEST,
                            customAttributes = mutableMapOf(
                                "account_number" to outboundPhotoRequest.accountNumber!!,
                                "upload_url" to communicationTemplate.uploadOutboundPhotosUrl,
                                "customer_forename" to customer.firstName,
                                "fuel_type" to (outboundPhotoRequest.fuel?.name ?: ""),
                                "request_reason" to (outboundPhotoRequest.requestReason ?: ""),
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.info(
                        "[::processOutboundPhotoRequest] [SHADOW] Something went wrong when sending email to queue for OutboundPhotoRequest with id $id...${e.message}"
                    )
                }
            }

            logger.debug { "[::processOutboundPhotoRequest][email=$customerEmail] Updating status" }
            outboundPhotoRequest =
                outboundPhotoRequestRepository.updateStatus(
                    id,
                    EMAIL_SENT,
                    emailMessageId = emailMessageId,
                    emailType = EmailType.INITIAL_EMAIL
                )

            logger.info { "[::processOutboundPhotoRequest] Email was sent to $customerEmail" }
        }

        if (outboundPhotoRequest != null && outboundPhotoRequest.status == EMAIL_SENT) {
            val htmlEmailTemplate = getEmailContent(outboundPhotoRequest.emailMessageId) ?: return

            val freshDeskTicketId = createFreshdeskTicket(customer, outboundPhotoRequest, htmlEmailTemplate)

            logger.info { "[::processOutboundPhotoRequest] Freshdesk ticket created" }

            outboundPhotoRequestRepository.updateStatus(id, FRESHDESK_TICKET_CREATED, freshDeskTicketId)

            createOutboundJuniferNote(
                outboundPhotoRequest,
                JUNIFER_NOTE_INITIAL_EMAIL_SUMMARY
            )
        }
    }

    override suspend fun sendFollowUpEmail(request: SendOutboundFollowUpEmailRequest) {
        val outboundPhotoRequestId = request.outboundRequestId

        val followUpEmailType =
            when (request.outboundRequestCurrentStatus) {
                OutboundRequestStatus.FRESHDESK_TICKET_CREATED -> EmailType.FIRST_FOLLOW_UP
                OutboundRequestStatus.FIRST_FOLLOW_UP_SENT -> EmailType.SECOND_FOLLOW_UP
                else -> {
                    logger.error("[::sendFollowUpEmail] Outbound request with id $outboundPhotoRequestId is in invalid status")
                    throw OutboundPhotoRequestException("Outbound request with id $outboundPhotoRequestId is in invalid status")
                }
            }

        logger.debug { "[::sendFollowUpEmail] Sending follow-up email of type $followUpEmailType for outbound request id $outboundPhotoRequestId" }

        val outboundPhotoRequest = outboundPhotoRequestRepository.findById(
            OutboundPhotoRequestFilter(
                id = outboundPhotoRequestId,
                statusesList = listOf(FRESHDESK_TICKET_CREATED, FIRST_FOLLOW_UP_SENT, SECOND_FOLLOW_UP_SENT),
                deleted = false
            )
        ) ?: throw EntityNotFoundException(clazz = OutboundPhotoRequest::class, id = outboundPhotoRequestId)

        if (isFollowUpEmailAlreadySent(outboundPhotoRequest.status!!, followUpEmailType)) {
            logger.error("[::sendFollowUpEmail] Follow-Up of type $followUpEmailType already sent for outbound request id $outboundPhotoRequestId")
            throw OutboundPhotoRequestException("Follow-Up of type $followUpEmailType already sent for outbound request id $outboundPhotoRequestId")
        }

        if (meterReadingPhotoRepository.getByOutboundRequestId(outboundPhotoRequestId).isNotEmpty()) {
            logger.error("[::sendFollowUpEmail] Customer already submitted photos for outbound request id $outboundPhotoRequestId")
            throw OutboundPhotoRequestException("Customer already submitted photos for outbound request id $outboundPhotoRequestId")
        }

        val customer = try {
            getCustomer(outboundPhotoRequest)
        } catch (ex: EntityNotFoundGrpcException) {
            logger.error(ex) {
                "[::sendFollowUpEmail] Customer details for outbound request with id $outboundPhotoRequestId not found"
            }
            throw EntityNotFoundException("There are no customer details for outbound request with id $outboundPhotoRequestId")
        }

        val followUpEmailMessageId = try {
            sendEmailToCustomer(
                customer,
                communicationTemplate.outboundPhotoRequestFollowUpEmail,
                mutableMapOf(
                    "account_number" to outboundPhotoRequest.accountNumber!!,
                    "upload_url" to communicationTemplate.uploadOutboundPhotosUrl,
                    "customer_forename" to customer.firstName,
                    "fuel_type" to (outboundPhotoRequest.fuel?.name ?: ""),
                    "request_reason" to (outboundPhotoRequest.requestReason ?: ""),
                    "delivery_email_address" to customer.primaryContact.email,
                    "outbound_request_status" to outboundPhotoRequest.status.name
                ), outboundPhotoConfig
            )
        } catch (ex: Exception) {
            logger.error(ex) {
                "[::sendFollowUpEmail] Failed to send follow up email of type $followUpEmailType for outbound request id $outboundPhotoRequestId"
            }
            throw OutboundPhotoRequestException("Failed to send follow-up email of type $followUpEmailType for outbound request id $outboundPhotoRequestId")
        }

        val updatedRequest = outboundPhotoRequestRepository.updateStatus(
            outboundPhotoRequestId,
            newStatus = if (followUpEmailType == EmailType.FIRST_FOLLOW_UP) FIRST_FOLLOW_UP_SENT else SECOND_FOLLOW_UP_SENT,
            emailType = followUpEmailType
        ) ?: throw EntityNotFoundException(clazz = OutboundPhotoRequest::class, id = outboundPhotoRequestId)

        val htmlEmailTemplate = getEmailContent(followUpEmailMessageId)
            ?: throw EntityNotFoundException("Failed to fetch email content for email message id $followUpEmailMessageId")

        val freshdeskPayload = buildFreshDeskNotePayload(updatedRequest, followUpEmailType, htmlEmailTemplate)
        try {
            freshDeskClient.createNoteWithAttachment(
                freshdeskPayload,
                outboundPhotoRequest.freshdeskTicketId.toString()
            )
        } catch (ex: Exception) {
            logger.error(ex) { "[::sendFollowUpEmail] Error while creating note for FreshDesk ticket with id ${outboundPhotoRequest.freshdeskTicketId}" }
            throw OutboundPhotoRequestException(
                "Error while creating note in FreshDesk ${ex.message}"
            )
        }

        createOutboundJuniferNote(
            outboundPhotoRequest,
            if (followUpEmailType == EmailType.FIRST_FOLLOW_UP)
                JUNIFER_NOTE_FIRST_FOLLOW_UP_SUMMARY else
                JUNIFER_NOTE_SECOND_FOLLOW_UP_SUMMARY
        )
    }

    override suspend fun getActiveOutboundRequests(accountNumber: String): List<OutboundPhotoRequest> {
        if (!featureService.isFeatureEnabled(FeatureName.TMP_SO_19346_OUTBOUND_PHOTO_REQUEST)) {
            return emptyList()
        }

        return outboundPhotoRequestRepository.getActiveOutboundRequests(accountNumber)
    }

    override suspend fun manualCancelOutboundRequest(outboundRequestId: Long) {
        closeOutboundPhotoRequestHandler.closeOutboundRequest(
            outboundRequestId,
            JUNIFER_NOTE_MANUAL_CLOSE_OUTBOUND_REQUEST_SUMMARY
        )
    }

    override suspend fun automaticCancelOutboundRequest() {
        if (!featureService.isFeatureEnabled(FeatureName.TMP_SO_19091_AUTOMATE_CANCEL_OUTBOUND_REQUESTS)) {
            return
        }

        logger.info("[automaticCancelOutboundRequest] Starting..")

        val cancelableRequests =
            outboundPhotoRequestRepository.getCancelableOutboundRequest(outboundPhotoConfig.maxActivityPeriodDays)
                .partition { it.freshdeskTicketId != null }
                .also { pair ->
                    if (pair.second.isNotEmpty())
                        logger.info("[automaticCancelOutboundRequest] Cannot cancel outbound requests with ids ${pair.second} as they were not linked to a valid freshdesk ticket")
                }
                .first

        if (cancelableRequests.isEmpty()) {
            logger.info("[automaticCancelOutboundRequest] No outbound requests to be closed, exiting...")
            return
        }

        val freshdeskTicketToOutboundRequestId = cancelableRequests
            .groupBy({ it.freshdeskTicketId!! }, { it.id!! })
            .mapValues { it.value[0] }

        val freshdeskJobId = freshDeskClient.bulkUpdateTickets(
            FreshdeskBulkUpdateTicketsRequestDto(
                ids = freshdeskTicketToOutboundRequestId.keys.toList(),
                FreshdeskBulkUpdateTicketsPropertiesDto(
                    status = 5 // CLOSED
                )
            )
        ).job_id

        var jobResult: FreshdeskJobResponseDto

        //cycling until freshdesk job is done
        do {
            jobResult = freshDeskClient.viewJob(freshdeskJobId)
        } while (FRESHDESK_PENDING_JOB_STATUSES.contains(jobResult.status))

        logger.info("[automaticCancelOutboundRequest] freshdesk job data: id - ${jobResult.id} , status - ${jobResult.status}")

        val jobResultMap = jobResult.data?.groupBy({ it.success }, { it.id })

        jobResultMap?.get(false)
            ?.also { logger.error("[automaticCancelOutboundRequest] could not cancel freshdesk tickets with ids: $it") }

        jobResultMap?.get(true)?.forEach { ticketId ->
            try {
                val updatedOutboundRequest = outboundPhotoRequestRepository.updateStatus(
                    freshdeskTicketToOutboundRequestId[ticketId]!!,
                    CANCELLED
                )

                freshDeskClient.createNote(
                    FreshdeskCreateNoteRequestDto(
                        body = FRESHDESK_AUTOMATICALLY_CANCEL_TICKET_NOTE,
                        private = true,
                    ),
                    ticketId.toString(),
                )

                updatedOutboundRequest?.let {
                    createOutboundJuniferNote(it, JUNIFER_NOTE_AUTOMATIC_CLOSE_OUTBOUND_REQUEST_SUMMARY)
                }
            } catch (ex: Exception) {
                logger.error(ex) {
                    "[automaticCancelOutboundRequest] Error while canceling outbound request or creating note for FreshDesk ticket with id $ticketId"
                }

                //exception is not propagated so that we process as many requests as possible
            }

            logger.info("[automaticCancelOutboundRequest] Finishing..")
        }
    }

    private suspend fun createOutboundJuniferNote(
        outboundPhotoRequest: OutboundPhotoRequest,
        noteSummary: String,
    ) = juniferAccountsClient.createAccountNote(
        createAccountNoteRequest {
            useJuniferId = false
            accountId = outboundPhotoRequest.accountId.toString()
            subject = JUNIFER_NOTE_SUBJECT
            type = NoteType.Note
            summary = noteSummary
            content = "FD ${outboundPhotoRequest.freshdeskTicketId} - " +
                    communicationTemplate.getNovaMeterPhotoHistoryModalUrl(outboundPhotoRequest.accountNumber)
        }
    )

    private suspend fun getCustomer(outboundPhotoRequest: OutboundPhotoRequest): Customer =
        outboundPhotoRequest.accountNumber!!.let {
            customersClient.getCustomerDetailsByAccountNumber(idRequestStr { this.id = it })
        }

    private suspend fun sendEmailToCustomer(
        customer: Customer,
        template: String,
        customAttributes: MutableMap<String, String>,
        outboundPhotoConfig: OutboundPhotoConfig,
    ): Long = communicationClient.sendCommunicationTemplate(
        CommunicationTemplateDto(
            communicationName = template,
            recipient = RecipientDto(
                name = "${customer.firstName} ${customer.lastName}", email = customer.primaryContact.email
            ),
            customAttributes = customAttributes,
            sendersEmail = SenderDto(outboundPhotoConfig.emailName, outboundPhotoConfig.email)
        )
    ).emailMessageId

    private suspend fun getEmailContent(emailMessageId: Long?): String? {
        if (emailMessageId == null) {
            logger.error("[::processOutboundPhotoRequest] No email message id provided")
            return null
        }

        val htmlEmailTemplate = try {
            communicationClient.getEmailContent("email-messages/$emailMessageId/html_content")
        } catch (e: EntityNotFoundGrpcException) {
            logger.error("[::processOutboundPhotoRequest] Email content not found for email message id $emailMessageId")
            return null
        }

        if (htmlEmailTemplate == null) {
            logger.error("[::processOutboundPhotoRequest] Email content not found for email message id $emailMessageId")
            return null
        }

        return htmlEmailTemplate
    }

    private suspend fun createFreshdeskTicket(
        customer: Customer,
        outboundPhotoRequest: OutboundPhotoRequest,
        emailSent: String,
    ): Long {
        val payload = buildFreshDeskTicketPayload(customer, outboundPhotoRequest, emailSent)

        return freshDeskClient.createTicketWithAttachment(payload).id
    }

    private fun buildFreshDeskTicketPayload(
        customer: Customer,
        outboundPhotoRequest: OutboundPhotoRequest,
        email: String,
    ): FreshdeskCreateTicketRequestDto {
        return FreshdeskCreateTicketRequestDto(
            name = "${customer.firstName} ${customer.lastName}",
            email = "${customer.primaryContact?.email}",
            type = OUTBOUND_REQUEST_FRESHDESK_TICKET_TYPE,
            priority = 1, // As long as this isn't sent from FE in input, should stay at Low priority, which is 1.
            status = 15, // Status = 15 means 'Waiting on Customer'
            source = 100, // Source = 100 means 'Ticket'
            description = email,
            group_id = outboundPhotoRequest.freshdeskGroupId!!,
            custom_fields = mapOf(
                "cf_new_level_1248554" to DefaultMeterReadingPhotoEvidenceService.BILLING,
                "cf_new_level_2" to METER_READING_SUBMISSION,
                "cf_new_level_3" to METER_READING_SUBMISSION_ERROR_INVESTIGATING,
            ),
            subject = outboundPhotoRequest.requestReason!!.toFreshdeskTicketSubject(),
        )
    }

    private fun buildFreshDeskNotePayload(
        request: OutboundPhotoRequest,
        followUpType: EmailType,
        emailTemplate: String,
    ): FreshdeskCreateNoteRequestDto {
        val noteDescription = if (followUpType == EmailType.FIRST_FOLLOW_UP) {
            "$FIRST_FOLLOW_UP_EMAIL_SENT [${request.getEmailsSentAt()?.firstFollowUpSentAt}] <br>"
        } else {
            "$SECOND_FOLLOW_UP_EMAIL_SENT [${request.getEmailsSentAt()?.secondFollowUpSentAt}] <br>"
        }

        return FreshdeskCreateNoteRequestDto(
            body = noteDescription + emailTemplate,
            private = false,
        )
    }

    private fun String.toFreshdeskTicketSubject() = PHOTO_REQUEST_PREFIX + this

    private fun isFollowUpEmailAlreadySent(
        outboundRequestStatus: OutboundPhotoRequestStatus,
        requestType: EmailType,
    ) =
        outboundRequestStatus == FIRST_FOLLOW_UP_SENT && requestType == EmailType.FIRST_FOLLOW_UP || outboundRequestStatus == SECOND_FOLLOW_UP_SENT && requestType == EmailType.SECOND_FOLLOW_UP
}
