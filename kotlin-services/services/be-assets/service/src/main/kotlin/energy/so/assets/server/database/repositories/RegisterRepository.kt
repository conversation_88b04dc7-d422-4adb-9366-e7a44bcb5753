package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Register

interface RegisterRepository {
    /**
     * Return a register by meterpoint id, meter identifier and register identifier if exists
     * @param meterIdentifier String
     * @return Register?
     */
    fun findByMeterPointIdAndIdentifiers(
        meterPointId: Long,
        meterIdentifier: String,
        registerIdentifier: String,
    ): Register?

    /**
     * Return all registers by meterpoint id
     * @param meterPointId Long
     * @param daysBeforeSupplyStartDate Int
     * @return List<Register>
     */
    fun findByMeterPointId(meterPointId: Long, daysBeforeSupplyStartDate: Int): List<Register>

    /**
     * Return register by ids
     * @param ids Long
     * @return List<Register>
     */
    fun findByIds(id: Set<Long>): List<Register>

    /**
     * Return register by id
     * @param id Long
     * @return Register
     */
    fun getById(id: Long): Register?

    /**
     * Return registers by meter id
     * @param meterIds List<Long>
     * @return List<Register>
     */
    fun findByMeterIds(meterIds: Set<Long>): List<Register>

    /**
     * Return active registers by meter id
     * @param meterIds List<Long>
     * @param daysBeforeSupplyStartDate Long
     * @return List<Register>
     */
    fun findActiveByMeterIds(meterIds: Set<Long>, daysBeforeSupplyStartDate: Int): List<Register>
}
