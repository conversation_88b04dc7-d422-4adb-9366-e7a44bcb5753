package energy.so.assets.server.consumers

import energy.so.assets.server.services.MeterReadingSubmissionService
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.subscribers.PubsubMessageProcessor

class MeterReadSubmissionRequestConsumer(
    private val meterReadingSubmissionService: MeterReadingSubmissionService,
    projectName: String,
    config: SubscriptionConfiguration,
) : PubsubMessageProcessor<String>(projectName, config) {

    private val logger = TraceableLogging.logger { }

    override suspend fun processMessage(message: QueueMessage<String>) {
        logger.debug("Received message: ${message.data}")
        val meterReadingSubmissionRequestId = message.data.toLong()
        logger.debug {
            "[meterReadingSubmissionRequestId:$meterReadingSubmissionRequestId] " +
                "forward reading to MeterReadingSubmissionService"
        }

        meterReadingSubmissionService.submitMeterReadingsSubmissionRequest(meterReadingSubmissionRequestId)
        logger.debug {
            "[meterReadingSubmissionRequestId:$meterReadingSubmissionRequestId] " +
                "Reading forwarded successfully to MeterReadingSubmissionService"
        }
    }
}
