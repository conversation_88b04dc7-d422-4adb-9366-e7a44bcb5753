package energy.so.assets.server.services

import energy.so.assets.server.models.CreatePropertyDto
import energy.so.assets.server.models.PropertyDto

interface PropertiesService {

    /**
     *
     * @param createPropertyDto CreatePropertyDto
     * @return PropertyDto
     */
    fun createProperty(createPropertyDto: CreatePropertyDto): PropertyDto

    /**
     *
     * @param id Long
     * @return PropertyDto
     */
    fun getProperty(id: Long): PropertyDto
}
