package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Asset
import energy.so.commons.model.tables.references.ASSET
import org.jooq.DSLContext
import energy.so.commons.model.tables.pojos.Asset as JooqAsset

class JooqAssetRepository(private val dslContext: DSLContext) : AssetRepository {
    override fun findById(id: Long): Asset? =
        dslContext.selectFrom(ASSET)
            .where(ASSET.ID.eq(id).and(ASSET.DELETED.isNull))
            .fetchOneInto(JooqAsset::class.java)
            ?.let { Asset.fromJooq(it) }

    override fun findByIds(ids: List<Long>): List<Asset> =
        dslContext.selectFrom(ASSET)
            .where(ASSET.ID.`in`(ids).and(ASSET.DELETED.isNull))
            .fetchInto(JooqAsset::class.java)
            .map { Asset.fromJooq(it) }
}
