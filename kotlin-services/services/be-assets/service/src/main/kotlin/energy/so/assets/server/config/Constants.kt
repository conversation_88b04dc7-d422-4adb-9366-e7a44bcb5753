package energy.so.assets.server.config

import com.typesafe.config.ConfigFactory
import io.github.config4k.extract

object Constants {
    // development, staging, production
    val env = System.getenv("BE_ASSETS_ENV") ?: "development"

    inline fun <reified T> get(key: String): T {
        val baseline = ConfigFactory.load()
        return ConfigFactory.load("application-$env").withFallback(baseline)
            .extract(
                key
            )
    }
}
