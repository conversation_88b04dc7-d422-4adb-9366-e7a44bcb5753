package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.OutboundMeterReadingPhotoRequest
import energy.so.assets.meterReadings.v2.OutboundRequest
import energy.so.assets.meterReadings.v2.outboundRequest
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.json.serializers.LocalDateTimeAsISO8601Serializer
import energy.so.commons.model.enums.OutboundPhotoRequestStatu
import java.time.LocalDateTime
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.jooq.JSONB
import energy.so.commons.model.enums.MeterReadingFuel as JooqMeterReadingFuel
import energy.so.commons.model.tables.pojos.OutboundPhotoRequest as JooqOutboundPhotoRequest

data class OutboundPhotoRequest(
    val id: Long? = null,
    val accountNumber: String? = null,
    val accountId: Long? = null,
    val fuel: MeterReadingFuel? = null,
    val requestReason: String? = null,
    val status: OutboundPhotoRequestStatus? = null,
    val freshdeskGroupId: Long? = null,
    val freshdeskGroupName: String? = null,
    val freshdeskTicketId: Long? = null,
    val caseId: String? = null,
    val emailMessageId: Long? = null,
    val emailsSentAt: JSONB? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    companion object {
        fun fromJooq(jooq: JooqOutboundPhotoRequest): OutboundPhotoRequest =
            OutboundPhotoRequest(
                id = jooq.id,
                accountNumber = jooq.accountNumber,
                accountId = jooq.accountId,
                fuel = jooq.fuel?.toSiblingEnum<MeterReadingFuel>(),
                requestReason = jooq.requestReason,
                status = jooq.status?.toSiblingEnum<OutboundPhotoRequestStatus>(),
                freshdeskGroupId = jooq.freshdeskGroupId,
                caseId = jooq.caseId,
                freshdeskGroupName = jooq.freshdeskGroupName,
                freshdeskTicketId = jooq.freshdeskTicketId,
                emailMessageId = jooq.emailMessageId,
                emailsSentAt = jooq.emailsSentAt,
                deleted = jooq.deleted,
                createdAt = jooq.createdAt,
                updatedAt = jooq.updatedAt
            )
    }

    fun getEmailsSentAt(): EmailsSentAt? = emailsSentAt?.data()
        ?.takeUnless { data -> data == "{}" || data.isEmpty() }
        ?.let { Json.decodeFromString<EmailsSentAt>(it) }
}

@Serializable
data class EmailsSentAt(
    @SerialName("initial_email_sent_at")
    @Serializable(LocalDateTimeAsISO8601Serializer::class)
    val initialEmailSentAt: LocalDateTime? = null,
    @SerialName("first_follow_up_sent_at")
    @Serializable(LocalDateTimeAsISO8601Serializer::class)
    val firstFollowUpSentAt: LocalDateTime? = null,
    @SerialName("second_follow_up_sent_at")
    @Serializable(LocalDateTimeAsISO8601Serializer::class)
    val secondFollowUpSentAt: LocalDateTime? = null,
)

enum class OutboundPhotoRequestStatus(val value: String) {
    REQUESTED("Requested"),
    UPLOADED("Uploaded"),
    FRESHDESK_TICKET_CREATED("Freshdesk Ticket Created"),
    EMAIL_SENT("Email Sent"),
    CANCELLED("Cancelled"),
    FIRST_FOLLOW_UP_SENT("First Follow-Up Sent"),
    SECOND_FOLLOW_UP_SENT("Second Follow-Up Sent"),
}

enum class EmailType {
    INITIAL_EMAIL,
    FIRST_FOLLOW_UP,
    SECOND_FOLLOW_UP
}

fun OutboundPhotoRequest.toJooq(): JooqOutboundPhotoRequest =
    JooqOutboundPhotoRequest(
        id = id,
        accountNumber = accountNumber,
        accountId = accountId,
        fuel = fuel?.toSiblingEnum<JooqMeterReadingFuel>(),
        requestReason = requestReason,
        status = status?.toSiblingEnum<OutboundPhotoRequestStatu>(),
        freshdeskGroupId = freshdeskGroupId,
        freshdeskGroupName = freshdeskGroupName,
        freshdeskTicketId = freshdeskTicketId,
        emailMessageId = emailMessageId,
        emailsSentAt = emailsSentAt,
        deleted = deleted,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

fun OutboundMeterReadingPhotoRequest.toOutboundPhotoRequest(billingAccountId: Long, status: OutboundPhotoRequestStatus): OutboundPhotoRequest =
    let { proto ->
        OutboundPhotoRequest(
            accountNumber = proto.accountNumber,
            accountId = billingAccountId,
            fuel = proto.meterPointType.toSiblingEnum<MeterReadingFuel>(),
            requestReason = proto.requestReason,
            freshdeskGroupId = proto.freshdeskGroupId,
            freshdeskGroupName = proto.freshdeskGroupName,
            status = status
        )
    }

fun OutboundPhotoRequest.toProto(): OutboundRequest = let { model ->
    outboundRequest {
        id = model.id!!
        accountNumber = model.accountNumber!!
        fuel = model.fuel!!.toSiblingEnum()
        requestReason = model.requestReason!!
        status = model.status!!.toSiblingEnum()
        freshdeskGroupId = model.freshdeskGroupId!!
        freshdeskTicketId = model.freshdeskTicketId.toNullableInt64()
        emailMessageId = model.emailMessageId.toNullableInt64()
        freshdeskGroupName = model.freshdeskGroupName!!
        createdAt = model.createdAt!!.toTimestamp()
        updatedAt = model.updatedAt.toNullableTimestamp()
        deleted = model.deleted.toNullableTimestamp()
    }
}
