package energy.so.assets.server.publishers.redis

import energy.so.assets.server.config.RedisTopics
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.redis.pubsub.JedisPubSub
import energy.so.commons.redis.pubsub.events.AccountEvent

class RedisEventPublisher(
    private val redisTopics: RedisTopics,
    private val redisPubSub: JedisPubSub,
) {
    private val logger = TraceableLogging.logger {}

    fun publishMeterReadingStatusChangedEvent(event: AccountEvent) {
        try {
            logger.debug(
                "[RedisEventPublisher] Publishing redis message for [accountId] ${event.accountId} " +
                        "to topic ${redisTopics.meterReadingStatus}"
            )
            redisPubSub.publishMessage(redisTopics.meterReadingStatus, event)
        } catch (e: Exception) {
            logger.error(e, "[RedisEventPublisher] Failed to publish event to topic ${redisTopics.meterReadingStatus}")
        }
    }
}