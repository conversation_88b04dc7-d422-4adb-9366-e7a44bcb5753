package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.Meter
import energy.so.commons.model.tables.references.METER
import energy.so.commons.model.tables.references.METER_METER_POINT_REL
import energy.so.commons.model.tables.references.METER_POINT
import energy.so.commons.model.tables.references.METER_POINT_ELECTRICITY
import energy.so.commons.model.tables.references.METER_POINT_GAS
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.SelectConditionStep
import org.jooq.SelectOnConditionStep
import org.jooq.impl.DSL
import java.time.LocalDate
import energy.so.commons.model.tables.pojos.Meter as JooqMeter

class JooqMeterRepository(private val dslContext: DSLContext) : MeterRepository {

    override fun findById(id: Long): Meter? = dslContext
        .select(METER.asterisk())
        .from(METER)
        .where(METER.ID.eq(id).and(METER.DELETED.isNull))
        .fetchOne()
        ?.let {
            Meter.fromJooq(pojo = it.into(JooqMeter::class.java))
        }

    override fun findByIds(ids: List<Long>): List<Meter> = dslContext
        .select(METER.asterisk())
        .from(METER)
        .where(METER.ID.`in`(ids), METER.DELETED.isNull)
        .fetchInto(JooqMeter::class.java)
        .mapNotNull {
            Meter.fromJooq(it)
        }

    override fun findByMeterPointId(
        id: Long,
        daysBeforeSupplyStartDate: Int,
    ): List<Meter> = dslContext
        .selectFrom(METER)
        .where(
            METER.DELETED.isNull,
            METER.ID.`in`(
                dslContext.select(METER_METER_POINT_REL.METER_ID)
                    .from(METER_METER_POINT_REL)
                    .where(
                        METER_METER_POINT_REL.METER_POINT_ID.eq(id),
                        METER_METER_POINT_REL.DELETED.isNull,
                        DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                            .lessOrEqual(LocalDate.now().plusDays(daysBeforeSupplyStartDate.toLong())),
                        METER_METER_POINT_REL.TO_DT.isNull.or(
                            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                                .greaterOrEqual(LocalDate.now())
                        ),
                    )
                    .fetch()
            )
        )
        .fetchInto(JooqMeter::class.java)
        .mapNotNull {
            Meter.fromJooq(it)
        }

    override fun findByIdentifier(identifier: String): Meter? = dslContext
        .select(METER.asterisk())
        .from(METER)
        .innerJoin(METER_METER_POINT_REL)
        .on(METER_METER_POINT_REL.METER_ID.eq(METER.ID))
        .and(
            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                .lessOrEqual(LocalDate.now())
        )
        .and(
            METER_METER_POINT_REL.TO_DT.isNull.or(
                DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                    .greaterOrEqual(LocalDate.now())
            )
        )
        .where(METER.IDENTIFIER.eq(identifier).and(METER.DELETED.isNull))
        .and(METER_METER_POINT_REL.DELETED.isNull)
        .fetchOne()
        ?.let {
            Meter.fromJooq(
                pojo = it.into(JooqMeter::class.java)
            )
        }

    override fun findElectricityMetersByMeterPointIds(ids: List<Long>): List<Meter> = dslContext
        .selectDistinct(METER.asterisk())
        .from(METER)
        .innerJoin(METER_METER_POINT_REL)
        .on(METER.ID.eq(METER_METER_POINT_REL.METER_ID))
        .and(
            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                .lessOrEqual(LocalDate.now())
        )
        .and(
            METER_METER_POINT_REL.TO_DT.isNull.or(
                DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                    .greaterOrEqual(LocalDate.now())
            )
        )
        .innerJoin(METER_POINT_ELECTRICITY).on(
            METER_POINT_ELECTRICITY.ID.`in`(ids),
            METER_METER_POINT_REL.METER_POINT_ID.eq(METER_POINT_ELECTRICITY.ID)
        )
        .where(
            METER.DELETED.isNull,
            METER_METER_POINT_REL.DELETED.isNull,
            METER_POINT_ELECTRICITY.DELETED.isNull,
        )
        .fetchInto(JooqMeter::class.java)
        .mapNotNull {
            Meter.fromJooq(it)
        }

    override fun findGasMetersByMeterPointIds(ids: List<Long>): List<Meter> = dslContext
        .selectDistinct(METER.asterisk())
        .from(METER)
        .join(METER_METER_POINT_REL)
        .on(METER.ID.eq(METER_METER_POINT_REL.METER_ID))
        .and(
            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                .lessOrEqual(LocalDate.now())
        )
        .and(
            METER_METER_POINT_REL.TO_DT.isNull.or(
                DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                    .greaterOrEqual(LocalDate.now())
            )
        )
        .join(METER_POINT_GAS).on(
            METER_POINT_GAS.ID.`in`(ids),
            METER_METER_POINT_REL.METER_POINT_ID.eq(METER_POINT_GAS.ID)
        )
        .where(
            METER.DELETED.isNull,
            METER_METER_POINT_REL.DELETED.isNull,
            METER_POINT_GAS.DELETED.isNull,
        )
        .fetchInto(JooqMeter::class.java)
        .mapNotNull {
            Meter.fromJooq(it)
        }

    override fun findByMeterPointIds(ids: List<Long>): List<Meter> =
        selectOnMeterMeterPointCondition(ids)
            .let { whereMeterMeterPointRelationValid(it) }
            .fetchInto(JooqMeter::class.java)
            .mapNotNull {
                Meter.fromJooq(it)
            }

    private fun selectOnMeterMeterPointCondition(ids: List<Long>) = dslContext
        .selectDistinct(METER.asterisk())
        .from(METER)
        .innerJoin(METER_METER_POINT_REL)
        .on(METER.ID.eq(METER_METER_POINT_REL.METER_ID))
        .and(
            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                .lessOrEqual(LocalDate.now())
        )
        .and(
            METER_METER_POINT_REL.TO_DT.isNull.or(
                DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                    .greaterOrEqual(LocalDate.now())
            )
        )
        .innerJoin(METER_POINT)
        .on(METER_POINT.ID.`in`(ids), METER_METER_POINT_REL.METER_POINT_ID.eq(METER_POINT.ID))

    private fun whereMeterMeterPointRelationValid(selectClause: SelectOnConditionStep<Record>): SelectConditionStep<Record> =
        selectClause.where(
            METER.DELETED.isNull,
            METER_POINT.DELETED.isNull,
            METER_METER_POINT_REL.DELETED.isNull,
            DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.FROM_DT)
                .lessOrEqual(LocalDate.now()),
            METER_METER_POINT_REL.TO_DT.isNull.or(
                DSL.field("DATE({0})", LocalDate::class.java, METER_METER_POINT_REL.TO_DT)
                    .greaterOrEqual(LocalDate.now())
            ),
        )
}
