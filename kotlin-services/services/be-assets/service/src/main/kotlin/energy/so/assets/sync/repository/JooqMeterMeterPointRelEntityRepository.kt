package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterMeterPointRelEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.tables.pojos.MeterMeterPointRel
import energy.so.commons.model.tables.records.MeterMeterPointRelRecord
import energy.so.commons.model.tables.references.METER_METER_POINT_REL
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqMeterMeterPointRelEntityRepository(
    private val dslContext: DSLContext,
) : MeterMeterPointRelEntityRepository {

    override fun deleteMeterMeterPointRelEntity(id: Long): Long {
        dslContext.update(METER_METER_POINT_REL)
            .set(METER_METER_POINT_REL.DELETED, LocalDateTime.now())
            .where(METER_METER_POINT_REL.ID.eq(id))
            .execute()
        return id
    }

    override fun createMeterMeterPointRelEntity(entity: MeterMeterPointRelEntity): Long {
        return resolveSyncCreationFlow(
            METER_METER_POINT_REL.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterMeterpointRelEntity,
            ::patchMeterMeterPointRelEntity,
            fun(e: MeterMeterPointRelEntity, newId: Long): MeterMeterPointRelEntity =
                e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    override fun patchMeterMeterPointRelEntity(entity: MeterMeterPointRelEntity): Long {
        val updateStep: UpdateSetStep<MeterMeterPointRelRecord> = dslContext
            .update(METER_METER_POINT_REL)
            .set(METER_METER_POINT_REL.UPDATED_AT, LocalDateTime.now())
            .set(METER_METER_POINT_REL.METER_ID, entity.meterId.value)
            .set(METER_METER_POINT_REL.METER_POINT_ID, entity.meterPointId.value)
            .set(METER_METER_POINT_REL.REGISTER_ID, entity.registerId.value)
            .set(METER_METER_POINT_REL.FROM_DT, entity.fromDttm.value.toLocalDateTime())

        if (entity.hasToDttm()) {
            updateStep.set(
                METER_METER_POINT_REL.TO_DT,
                entity.toDttm.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(METER_METER_POINT_REL.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }

        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_METER_POINT_REL.DELETED)
            } else {
                updateStep.set(
                    METER_METER_POINT_REL.DELETED,
                    DSL.coalesce(METER_METER_POINT_REL.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }
        (updateStep as UpdateSetMoreStep).where(METER_METER_POINT_REL.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    private fun doCreateMeterMeterpointRelEntity(entity: MeterMeterPointRelEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER_METER_POINT_REL,
            MeterMeterPointRel(
                meterId = entity.meterId.value,
                meterPointId = entity.meterPointId.value,
                registerId = entity.registerId.value,
                fromDt = entity.fromDttm.value.toLocalDateTime(),
                toDt = entity.toDttm.getValueOrNull()?.toLocalDateTime(),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(MeterMeterPointRel()) }.id!!
}
