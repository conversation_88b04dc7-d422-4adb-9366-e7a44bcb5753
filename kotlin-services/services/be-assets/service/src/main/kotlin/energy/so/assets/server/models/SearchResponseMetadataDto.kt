package energy.so.assets.server.models

import energy.so.commons.v2.search.searchResponseMetadata

data class SearchResponseMetadataDto(
    val pageNumber: Int,
    val pageSize: Int,
    val totalElements: Int? = null,
    val totalPages: Int? = null,
)

fun SearchResponseMetadataDto.toProtobuf() = let { dto ->
    searchResponseMetadata {
        pageNumber = dto.pageNumber
        pageSize = dto.pageSize
        dto.totalElements?.let { totalElements = it }
        dto.totalPages?.let { totalPages = it }
    }
}
