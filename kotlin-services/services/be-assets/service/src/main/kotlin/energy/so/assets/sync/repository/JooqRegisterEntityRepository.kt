package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.RegisterEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.tables.pojos.Register
import energy.so.commons.model.tables.records.RegisterRecord
import energy.so.commons.model.tables.references.REGISTER
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.math.BigDecimal
import java.time.LocalDateTime

class JooqRegisterEntityRepository(
    private val dslContext: DSLContext,
) : RegisterEntityRepository {

    override fun deleteRegisterEntity(id: Long): Long {
        dslContext.update(REGISTER)
            .set(REGISTER.DELETED, LocalDateTime.now())
            .where(REGISTER.ID.eq(id))
            .execute()
        return id
    }

    override fun patchRegisterEntity(entity: RegisterEntity): Long {
        val updateStep: UpdateSetStep<RegisterRecord> = dslContext
            .update(REGISTER)
            .set(REGISTER.UPDATED_AT, LocalDateTime.now())

        if (entity.hasType()) updateStep.set(REGISTER.TYPE, entity.type.getValueOrNull())
        if (entity.hasIdentifier()) updateStep.set(REGISTER.IDENTIFIER, entity.identifier.getValueOrNull())
        if (entity.hasDigits()) updateStep.set(REGISTER.DIGITS, entity.digits.value)
        if (entity.hasDecimalPlaces()) updateStep.set(REGISTER.DECIMAL_PLACES, entity.decimalPlaces.value)
        if (entity.hasRateName()) updateStep.set(REGISTER.RATE_NAME, entity.rateName.value)
        if (entity.hasUsage()) updateStep.set(REGISTER.USAGE, entity.usage.value.toBigDecimal())
        if (entity.hasRegisterFromDttm()) {
            updateStep.set(
                REGISTER.FROM_DTTM,
                entity.registerFromDttm.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasRegisterToDttm()) {
            updateStep.set(
                REGISTER.TO_DTTM,
                entity.registerToDttm.getValueOrNull()?.toLocalDateTime()
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(REGISTER.CREATED_AT, entity.createdAt.getValueOrNull()?.toLocalDateTime())
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(REGISTER.DELETED)
            } else {
                updateStep.set(REGISTER.DELETED, DSL.coalesce(REGISTER.DELETED, DSL.value(it.toLocalDateTime())))
            }
        }

        (updateStep as UpdateSetMoreStep).where(REGISTER.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createRegisterEntity(entity: RegisterEntity): Long {
        return resolveSyncCreationFlow(
            REGISTER.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateRegisterEntity,
            ::patchRegisterEntity,
            fun(e: RegisterEntity, newId: Long): RegisterEntity = e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateRegisterEntity(entity: RegisterEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            REGISTER,
            Register(
                meterId = entity.meterId.value,
                type = entity.type.getValueOrNull(),
                identifier = entity.identifier.getValueOrNull(),
                digits = entity.digits.value,
                decimalPlaces = entity.decimalPlaces.value,
                usage = if (entity.hasUsage()) entity.usage.value.toBigDecimal() else BigDecimal.ZERO,
                rateName = if (entity.hasRateName()) entity.rateName.value else null,
                fromDttm = entity.registerFromDttm.getValueOrNull()?.toLocalDateTime(),
                toDttm = entity.registerToDttm.getValueOrNull()?.toLocalDateTime(),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(Register()) }.id!!
}
