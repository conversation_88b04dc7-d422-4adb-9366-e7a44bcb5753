package energy.so.assets.server.modules

import energy.so.assets.server.config.Constants
import energy.so.assets.server.config.Environment
import energy.so.assets.server.config.EnvironmentConfig
import energy.so.commons.storage.GoogleCloudStorageClient
import energy.so.commons.storage.GoogleCloudStorageConfig
import energy.so.commons.storage.InMemoryStorageClient
import energy.so.commons.storage.StorageClient
import org.koin.dsl.module

object GoogleCloudModule {

    private val storageConfig = Constants.get<GoogleCloudStorageConfig>("googleStorage")
    private val environmentConfig = Constants.get<EnvironmentConfig>("environment")

    val module = module {

        single<StorageClient> {

            if (environmentConfig.env == Environment.DEVELOPMENT && storageConfig.credentialsPath.isNullOrEmpty()) {
                InMemoryStorageClient()
            } else {
                // If we have set a path to the credentials, configure this, else use the default credentials
                if (!storageConfig.credentialsPath.isNullOrEmpty()) {
                    storageConfig.credentialsStream =
                        GoogleCloudModule::class.java.getResourceAsStream(storageConfig.credentialsPath!!)
                }
                GoogleCloudStorageClient(
                    config = storageConfig,
                    externalClientsEnabled = true,
                )
            }
        }
    }
}
