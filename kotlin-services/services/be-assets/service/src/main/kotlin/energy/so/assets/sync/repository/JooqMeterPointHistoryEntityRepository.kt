package energy.so.assets.sync.repository

import energy.so.assets.sync.v2.MeterPointHistoryEntity
import energy.so.assets.sync.v2.copy
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.MeterPointEventType
import energy.so.commons.model.enums.MeterPointSupplyStatu
import energy.so.commons.model.tables.records.MeterPointHistoryRecord
import energy.so.commons.model.tables.references.METER_POINT_HISTORY
import energy.so.commons.sync.resolveSyncCreationFlow
import org.jooq.DSLContext
import org.jooq.UpdateSetMoreStep
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.MeterPointHistory as JooqMeterPointHistory

class JooqMeterPointHistoryEntityRepository(private val dslContext: DSLContext) : MeterPointHistoryEntityRepository {

    override fun deleteMeterPointHistoryEntity(id: Long): Long {
        dslContext.update(METER_POINT_HISTORY)
            .set(METER_POINT_HISTORY.DELETED, LocalDateTime.now())
            .where(METER_POINT_HISTORY.ID.eq(id))
            .execute()
        return id
    }

    override fun patchMeterPointHistoryEntity(entity: MeterPointHistoryEntity): Long {
        val updateStep: UpdateSetStep<MeterPointHistoryRecord> = dslContext
            .update(METER_POINT_HISTORY)
            .set(METER_POINT_HISTORY.UPDATED_AT, LocalDateTime.now())

        if (entity.hasMeterPointId()) updateStep.set(METER_POINT_HISTORY.METER_POINT_ID, entity.meterPointId.value)
        if (entity.hasEventType()) {
            updateStep.set(
                METER_POINT_HISTORY.EVENT_TYPE,
                MeterPointEventType.valueOf(entity.eventType.value)
            )
        }
        if (entity.hasStatus()) {
            updateStep.set(
                METER_POINT_HISTORY.STATUS,
                MeterPointSupplyStatu.valueOf(entity.status.value)
            )
        }
        if (entity.hasCreatedAt()) {
            updateStep.set(
                METER_POINT_HISTORY.CREATED_AT,
                entity.createdAt.getValueOrNull()?.toLocalDateTime()
            )
        }
        entity.deleted.getValueOrNull().let {
            if (it == null) {
                updateStep.setNull(METER_POINT_HISTORY.DELETED)
            } else {
                updateStep.set(
                    METER_POINT_HISTORY.DELETED,
                    DSL.coalesce(METER_POINT_HISTORY.DELETED, DSL.value(it.toLocalDateTime()))
                )
            }
        }

        (updateStep as UpdateSetMoreStep).where(METER_POINT_HISTORY.ID.eq(entity.id.value)).execute()
        return entity.id.value
    }

    override fun createMeterPointHistoryEntity(entity: MeterPointHistoryEntity): Long {
        return resolveSyncCreationFlow(
            METER_POINT_HISTORY.name,
            entity.syncTransactionId.value,
            entity,
            dslContext,
            ::doCreateMeterPointHistoryEntity,
            ::patchMeterPointHistoryEntity,
            fun(e: MeterPointHistoryEntity, newId: Long): MeterPointHistoryEntity =
                e.copy { id = newId.toNullableInt64() },
            ::getEntityIdForSyncTransactionId,
            ::insertSyncStatusRecord
        )
    }

    private fun doCreateMeterPointHistoryEntity(entity: MeterPointHistoryEntity, dslContext: DSLContext): Long =
        dslContext.newRecord(
            METER_POINT_HISTORY,
            JooqMeterPointHistory(
                meterPointId = entity.meterPointId.value,
                eventType = MeterPointEventType.valueOf(entity.eventType.value),
                status = MeterPointSupplyStatu.valueOf(entity.status.value),
                createdAt = entity.createdAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                updatedAt = entity.updatedAt.getValueOrNull()?.toLocalDateTime() ?: LocalDateTime.now(),
                deleted = entity.deleted.getValueOrNull()?.toLocalDateTime()
            )
        )
            .apply { save() }
            .run { into(JooqMeterPointHistory()) }.id!!
}
