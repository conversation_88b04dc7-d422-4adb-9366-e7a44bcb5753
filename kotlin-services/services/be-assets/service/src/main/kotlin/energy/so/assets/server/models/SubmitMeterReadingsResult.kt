package energy.so.assets.server.models

import energy.so.assets.meterReadings.v2.SubmitAccountMeterReadingError
import energy.so.assets.meterReadings.v2.submitAccountMeterReadingError
import energy.so.assets.meterReadings.v2.submitMeterReadingError
import energy.so.assets.meterReadings.v2.submitMeterReadingResponse
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.json.serializers.LocalDateAsISO8601Serializer
import energy.so.commons.json.serializers.OffsetDateTimeAsISO8601Serializer
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import energy.so.assets.meterReadings.v2.SubmitMeterReadingError as SubmitMeterReadingErrorProto

// Prevents an exception being thrown if a key is included that doesn't match the model.
val jsonFormat = Json { ignoreUnknownKeys = true }

@Serializable
data class SubmitMeterReadingError(
    val errorCode: String,
    val errorMessage: String? = null,
    val meterIdentifier: String? = null,
    val registerIdentifier: String? = null,
    val registerId: Long? = null,
)

@Serializable
data class SubmitMeterReadingsResult(
    val meterPointId: Long,
    @Serializable(OffsetDateTimeAsISO8601Serializer::class)
    val submissionTime: OffsetDateTime,
    val success: Boolean,
    val errors: List<SubmitMeterReadingError>? = null,
    val readingDates: List<ReadingDate>? = null,
) {

    constructor(
        meterPointId: Long,
        submissionTime: OffsetDateTime,
        success: Boolean,
        errorCode: String,
        message: String?,
    ) : this(meterPointId, submissionTime, success, listOf(SubmitMeterReadingError(errorCode, message)))

    fun getErrorDescriptionIfFailed() =
        if (!success) errors?.get(0)?.errorMessage else null
}

data class SubmitMeterReadingsResultWithRegisterId(
    val success: Boolean,
    val errors: List<SubmitMeterReadingsErrorWithRegisterId>? = null,
)

data class SubmitMeterReadingsResultWithoutRegisterId(
    val success: Boolean,
    val errors: List<SubmitMeterReadingsErrorWithoutRegisterId>? = null,
)

data class SubmitMeterReadingsErrorWithRegisterId(
    val errorMessage: String?,
    val errorCode: String,
    val registerId: Long?,
)

data class SubmitMeterReadingsErrorWithoutRegisterId(
    val errorMessage: String?,
    val errorCode: String,
)

@Serializable
data class ReadingDate(
    @Serializable(LocalDateAsISO8601Serializer::class)
    val readingDate: LocalDate,
)

fun List<SubmitMeterReadingsResult>.toErrorResponseProtobuf() = let {
    submitMeterReadingResponse {
        success = false
        it.flatMap { errorResult -> errorResult.errors.orEmpty() }
            .map { error -> error.toErrorResponseProtobuf() }
            .let { mapped -> error.addAll(mapped) }
    }
}

private fun SubmitMeterReadingError.toErrorResponseProtobuf(): SubmitMeterReadingErrorProto = let { result ->
    submitMeterReadingError {
        code = result.errorCode
        result.errorMessage?.let { message = it }
        meterIdentifier = result.meterIdentifier.toNullableString()
        registerIdentifier = result.registerIdentifier.toNullableString()
    }
}

fun SubmitMeterReadingsErrorWithRegisterId.toAccountErrorResponseProtobuf(): SubmitAccountMeterReadingError =
    let { result ->
        submitAccountMeterReadingError {
            errorCode = result.errorCode
            message = result.errorMessage ?: result.errorCode
            registerId = result.registerId.toNullableInt64()
        }
    }

fun SubmitMeterReadingsErrorWithoutRegisterId.toAccountErrorResponseProtobuf(): SubmitAccountMeterReadingError =
    let { result ->
        submitAccountMeterReadingError {
            errorCode = result.errorCode
            message = result.errorMessage ?: result.errorCode
        }
    }
