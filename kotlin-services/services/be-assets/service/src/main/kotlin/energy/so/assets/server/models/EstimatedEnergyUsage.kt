package energy.so.assets.server.models

import energy.so.assets.meterPoints.v2.estimatedUsage
import energy.so.commons.grpc.extensions.toSiblingEnum
import java.time.LocalDateTime
import energy.so.ac.junifer.v1.assets.EstimatedUsage as EstimatedUsageAcProto
import energy.so.assets.meterPoints.v2.EstimatedUsage as EstimatedUsageGrpc
import energy.so.commons.model.tables.pojos.EstimatedUsage as JooqEstimatedUsage

data class EstimatedUsage(
    val id: Long? = null,
    val meterPointId: Long,
    val meterPointType: String? = null,
    val usageType: EstimatedUsageType,
    val rateName: EstimatedRateName,
    val units: String,
    val usage: Double,
    val consumptionFromDt: LocalDateTime? = null,
    val consumptionToDt: LocalDateTime? = null,
    val source: String? = null,
    val cancelFl: Boolean? = null,
    val deleted: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
) {
    fun toResponse(): EstimatedUsageGrpc = let { from ->
        estimatedUsage {
            meterPointId = from.meterPointId
            from.meterPointType?.let { meterPointType = from.meterPointType }
            usageType = from.usageType.toSiblingEnum()
            rateName = from.rateName.toSiblingEnum()
            units = from.units
            usage = from.usage
        }
    }

    companion object {
        fun fromJooq(from: JooqEstimatedUsage): EstimatedUsage = EstimatedUsage(
            id = from.id!!,
            meterPointId = from.meterPointId!!,
            meterPointType = from.meterPointType,
            usageType = from.type!!.toSiblingEnum(),
            rateName = from.rateName!!.toSiblingEnum(),
            units = from.units!!,
            usage = from.usage!!.toDouble(),
            consumptionFromDt = from.consumptionFromDt,
            consumptionToDt = from.consumptionToDt,
            source = from.source,
            cancelFl = from.cancelfl,
            deleted = from.deleted,
            createdAt = from.createdAt!!,
            updatedAt = from.updatedAt!!,
        )
    }
}

data class Rates(
    val usage: Double,
    val rateName: EstimatedRateName,
)

enum class EstimatedUsageType {
    ANNUAL_QUANTITY,
    ESTIMATED_ANNUAL_CONSUMPTION,
    ANNUAL_CONSUMPTION_DETAILS,
}

enum class EstimatedRateName {
    Standard,
    Day,
    Night,
}

fun EstimatedUsageAcProto.toModelList(meterPointId: Long) = this.ratesList
    .map {
        EstimatedUsage(
            meterPointId = meterPointId,
            meterPointType = this.meterPointType,
            usageType = EstimatedUsageType.valueOf(this.usageType),
            rateName = EstimatedRateName.valueOf(it.rateName),
            units = this.units,
            usage = it.usage,
        )
    }
