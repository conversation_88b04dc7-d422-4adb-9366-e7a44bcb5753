package energy.so.assets.server.services

import com.google.protobuf.Timestamp
import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.ReadFrequency
import energy.so.ac.junifer.v1.assets.Reading
import energy.so.ac.junifer.v1.assets.copy
import energy.so.ac.junifer.v1.assets.getMeterReadingRequest
import energy.so.ac.junifer.v1.assets.meterReadings
import energy.so.assets.meterReadings.v2.MeterReadingGrouping
import energy.so.assets.meterReadings.v2.MeterReadingsSearchRequest
import energy.so.assets.server.database.repositories.MeterPointsRepository
import energy.so.assets.server.database.repositories.RegisterRepository
import energy.so.assets.server.mapping.EntityIdentifier
import energy.so.assets.server.mapping.JuniferEntityMapper
import energy.so.assets.server.models.MeterReading
import energy.so.assets.server.models.MeterReadingStatus
import energy.so.assets.server.models.MeterReadingUnit
import energy.so.assets.server.models.MeterReadingWorkflowStatus
import energy.so.assets.server.models.Register
import energy.so.assets.sync.service.searchEnum
import energy.so.commons.grpc.extensions.StringExtension.asEnum
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.customers.agreements.v2.getAgreementByMeterpointIdRequest
import energy.so.customers.client.v2.agreements.AgreementsClient
import energy.so.users.v2.FeatureName
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Locale

private const val ESTIMATED = "ESTIMATED"
private const val ACCEPTED = "ACCEPTED"

private val logger = TraceableLogging.logger { }

class JuniferMeterReadingApiService(
    private val assetsClient: AssetsClient,
    private val agreementsClient: AgreementsClient,
    private val registerRepository: RegisterRepository,
    private val meterPointRepository: MeterPointsRepository,
    private val juniferEntityMapper: JuniferEntityMapper,
    private val featureService: FeatureService,
) : MeterReadingApiService {

    override suspend fun search(searchRequest: MeterReadingsSearchRequest): List<MeterReading> {
        var meterPointIds = searchRequest.meterPointIdList
        logger.debug { "[::search] meterPointIds $meterPointIds" }
        if (meterPointIds.isEmpty() && searchRequest.meterIdList.isNotEmpty()) {
            meterPointIds = meterPointRepository.findByMeterIds(searchRequest.meterIdList)
                .map { it.id }
        }

        logger.debug { "[::search] meterPointIds is $meterPointIds" }
        val meterReadings = meterPointIds
            .map { meterPointId -> getMeterReadings(meterPointId, searchRequest) }
        logger.debug {
            "[::search][meterpointIds: $meterPointIds] meterReadings size " +
                    "is ${meterReadings.flatMap { it.readingsList }.size}. " +
                    "meter point ids to readings ids: ${
                        meterReadings.flatMap { it.readingsList }
                            .associateBy { it.meterpointId }
                            .mapValues { it.value.id }
                    }."
        }
        val filteredReadings = meterReadings
            .flatMap { it.readingsList }
            .putCoreIds()
            .filter { respectsCriteria(it, searchRequest) }
            .let { readings ->
                if (searchRequest.hasGrouping() &&
                    searchRequest.grouping != null &&
                    searchRequest.grouping != MeterReadingGrouping.NO_GROUPING
                ) {
                    readings
                        .groupBy { it.meterpointId }
                        .flatMap { readingsByMeterpointId ->
                            readingsByMeterpointId.value
                                .groupBy {
                                    LocalDate.of(
                                        it.readingDttm.toLocalDateTime().year,
                                        it.readingDttm.toLocalDate().monthValue,
                                        if (searchRequest.grouping == MeterReadingGrouping.MONTHLY) {
                                            1
                                        } else {
                                            it.readingDttm.toLocalDate().dayOfMonth
                                        }
                                    )
                                }
                                .toSortedMap()
                                .mapNotNull { mapEntry ->
                                    mapEntry.value
                                        .sortedByDescending { it.readingDttm.toLocalDateTime() }
                                        .getOrNull(0)
                                }
                        }
                } else {
                    readings
                }
            }

        val registers = getAssociatedRegisters(filteredReadings)

        logger.debug {
            "[::search][searchRequest: $searchRequest][meterpointIds: $meterPointIds] filteredReadings " +
                    "size: ${filteredReadings.size}. " +
                    "meter point ids to readings ids: ${
                        filteredReadings.associateBy { it.meterpointId }.mapValues { it.value.id }
                    }."
        }
        val mappedMeterReading = filteredReadings.map { it.toMeterReading(registers[it.meterRegisterId]) }
        logger.debug {
            "[::search][meterpointIds: $meterPointIds] mapped meter readings."
        }

        if (searchRequest.hasOnlyLast() && searchRequest.onlyLast) {
            return listOf(mappedMeterReading.sortedByDescending { it.fromDttm }[0])
        }

        return mappedMeterReading.returnPage(searchRequest.pagination.pageNumber, searchRequest.pagination.pageSize)
    }

    private fun getAssociatedRegisters(readings: List<Reading>): Map<Long, Register> =
        readings.ifEmpty { return emptyMap() }
            .map { it.meterRegisterId }
            .distinct()
            .toSet()
            .let { registerRepository.findByIds(it).associateBy { register -> register.id } }

    private suspend fun List<Reading>.putCoreIds(): List<Reading> {
        val meterIdCache = mutableMapOf<Long, Long>()
        val registerIdCache = mutableMapOf<Long, Long>()

        return this.map {
            if (it.status.uppercase() == ACCEPTED) {
                if (!meterIdCache.containsKey(it.meterId)) {
                    meterIdCache[it.meterId] = juniferEntityMapper.getCoreId(
                        EntityIdentifier.METER,
                        it.meterId.toString()
                    )!!.toLong()
                }

                if (!registerIdCache.containsKey(it.meterRegisterId)) {
                    registerIdCache[it.meterRegisterId] =
                        juniferEntityMapper.getCoreId(EntityIdentifier.REGISTER, it.meterRegisterId.toString())!!
                            .toLong()
                }
                it.copy {
                    meterId = meterIdCache[this.meterId]!!
                    meterRegisterId = registerIdCache[this.meterRegisterId]!!
                }
            } else {
                it
            }
        }
    }

    override suspend fun getLastElectricityMeterReading(meterPointId: Long): MeterReading? =
        getMeterReadings(meterPointId)
            .readingsList
            .putCoreIds()
            .asSequence()
            .filter { reading -> reading.quality.uppercase() != ESTIMATED }
            .filter { reading -> reading.status.uppercase() == ACCEPTED }
            .sortedByDescending { it.readingDttm.toLocalDateTime() }
            .toList()
            .getOrNull(0)?.toMeterReading()

    override suspend fun getFirstElectricityMeterReading(meterPointId: Long): MeterReading? =
        getMeterReadings(meterPointId)
            .readingsList
            .putCoreIds()
            .asSequence()
            .filter { reading -> reading.quality.uppercase() != ESTIMATED }
            .filter { reading -> reading.status.uppercase() == ACCEPTED }
            .sortedBy { it.readingDttm.toLocalDateTime() }
            .toList().getOrNull(0)?.toMeterReading()

    override suspend fun getElectricityMeterReadingsByDate(
        meterPointId: Long,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
    ): List<MeterReading> {
        val readings = getMeterReadings(meterPointId)
            .readingsList
            .putCoreIds()
            .asSequence()
            .filter { reading -> reading.readingDttm.toLocalDateTime().isAfter(startDate) }
            .filter { reading -> reading.readingDttm.toLocalDateTime().isBefore(endDate) }
            .filter { reading -> reading.quality.uppercase() != ESTIMATED }
            .filter { reading -> reading.status.uppercase() == ACCEPTED }
            .sortedBy { it.readingDttm.toLocalDateTime() }
            .toList()

        val registers = getAssociatedRegisters(readings)

        return readings.map { it.toMeterReading(registers[it.meterRegisterId]) }
    }

    override suspend fun existsForMeterIdsAndSubmissionProcessed(
        meterIds: List<Long>,
        readingDates: List<LocalDate>,
    ): Boolean {
        val meterPointIds = meterPointRepository.findByMeterIds(meterIds)
            .mapNotNull { it.id }
        logger.debug { "[::existsForMeterIdsAndSubmissionProcessed] meterPointIds $meterPointIds" }
        logger.debug { "[::existsForMeterIdsAndSubmissionProcessed] readingDates $readingDates" }
        return meterPointIds
            .map { meterPointId -> getMeterReadings(meterPointId) }
            .flatMap { it.readingsList }
            .putCoreIds()
            .asSequence()
            .filter { it.meterId in meterIds }
            .any { it.readingDttm.toLocalDate() !in readingDates }
    }

    override suspend fun existsForMeterPointIdInLastYear(meterPointId: Long): Boolean {
        return assetsClient.getMeterReadings(
            getMeterReadingRequest {
                meterpointId = meterPointId
                fromDt = LocalDate.now().minusYears(1).toTimestamp()
                toDt = LocalDate.now().toTimestamp()
                status.add(MeterReadingStatus.ACCEPTED.value)
            }
        ).readingsCount > 0
    }

    private suspend fun getMeterReadings(
        meterPointId: Long,
        request: MeterReadingsSearchRequest? = null,
    ) =
        getMeterpointAgreementStartDate(
            id = meterPointId,
            includeCancelledAndDeletedAgreements = request?.includeCancelledAndDeletedAgreements
                ?.takeIf { request.hasIncludeCancelledAndDeletedAgreements() }
                ?: false,
            productAccountId = if (request?.hasProductAccountId() == true) request.productAccountId else null
        )?.let { fromDate ->
            logger.debug { "[::getMeterReadings], fromDate: $fromDate" }

            val searchFrequency = if (request != null && request.hasReadFrequency()) {
                if (featureService.isFeatureEnabled(FeatureName.TMP_SO_23911_DAILY_READINGS_FROM_METER_POINT)) {
                    mapReadFrequency(request.readFrequency)
                } else null
            } else null

            assetsClient.getMeterReadings(
                getMeterReadingRequest {
                    meterpointId = meterPointId
                    fromDt = fromDate
                    toDt = LocalDate.now().toTimestamp()
                    request?.let { req ->
                        status.addAll(
                            req.statusList.map { it.toSiblingEnum<MeterReadingStatus>().value }
                        )
                    }
                    searchFrequency?.let { readingFrequency = searchFrequency }
                }
            )
        } ?: meterReadings {}

    private fun Reading.toMeterReading(register: Register? = null) = MeterReading(
        id = this.id,
        meterPointId = this.meterpointId,
        meterId = this.meterId.takeIf { this.hasMeterId() },
        register = register ?: getCoreRegister(),
        readingDttm = this.readingDttm.toLocalDateTime(),
        fromDttm = this.fromDttm.takeIf { this.hasFromDttm() }?.toLocalDateTime(),
        status = this.status.asEnum<MeterReadingStatus>(),
        sequenceType = this.sequenceType.asEnum(),
        source = this.source.asEnum(),
        quality = this.quality.asEnum(),
        cumulative = this.cumulative.toBigDecimal(),
        consumption = this.consumption.takeIf { this.hasConsumption() }?.toBigDecimal(),
        unit = this.unit.takeIf { this.hasUnit() }?.asEnum<MeterReadingUnit>(),
        workflowStatus = this.workflowStatus.takeIf { this.hasWorkflowStatus() }?.let {
            searchEnum<MeterReadingWorkflowStatus>(
                it
            )
        },
        rateName = this.rateName.takeIf { this.hasRateName() },
        pendingRegisterReadingId = this.pendingRegisterReadingId.takeIf { this.hasPendingRegisterReadingId() },
        deleted = null,
        createdAt = null,
        updatedAt = null,
        errorDescription = null,
        receivedDttm = this.receivedDttm.takeIf { this.hasReceivedDttm() }?.toLocalDateTime(),
        meterPointIdentifier = this.meterPointIdentifier,
        meterIdentifier = this.meter,
        registerIdentifier = this.register
    )

    private fun List<MeterReading>.returnPage(page: Int, pageSize: Int): List<MeterReading> {
        val offset = maxOf((page - 1) * pageSize, 0)
        return subList(offset, minOf(offset + pageSize, size))
    }

    private fun Reading.getCoreRegister() =
        registerRepository.getById(this.meterRegisterId)

    private fun respectsCriteria(
        reading: Reading,
        searchRequest: MeterReadingsSearchRequest,
    ): Boolean {
        val qualities = searchRequest.qualityList.map { it.toString().lowercase() }
        val sources = searchRequest.sourceList.map { it.toString().lowercase() }
        val statuses = searchRequest.statusList.map { it.toString().capitalizeFirstLetter() }

        return !doesNotRespectAllCriteria(reading, searchRequest, qualities, sources, statuses)
    }

    private fun doesNotRespectAllCriteria(
        reading: Reading,
        searchRequest: MeterReadingsSearchRequest,
        qualities: List<String>,
        sources: List<String>,
        statuses: List<String>,
    ) = (searchRequest.meterIdList.isNotEmpty() and (reading.meterId !in searchRequest.meterIdList)) or
            (sources.isNotEmpty() and (reading.source.lowercase() !in sources)) or
            (qualities.isNotEmpty() and (reading.quality.lowercase() !in qualities)) or
            (
                    searchRequest.hasStartDate() and reading.readingDttm.toLocalDate()
                        .isBefore(searchRequest.startDate.toLocalDate())
                    ) or
            (
                    searchRequest.hasEndDate() and reading.readingDttm.toLocalDate()
                        .isAfter(searchRequest.endDate.toLocalDate())
                    ) or
            (searchRequest.statusList.isNotEmpty() and (reading.status !in statuses)) or
            (searchRequest.hasRegisterId() and (reading.meterRegisterId != searchRequest.registerId))

    private suspend fun getMeterpointAgreementStartDate(
        id: Long,
        includeCancelledAndDeletedAgreements: Boolean = false,
        productAccountId: Long? = null,
    ): Timestamp? {
        return try {
            val agreement = agreementsClient.getAgreementByMeterpointId(
                getAgreementByMeterpointIdRequest {
                    this.id = id
                    firstAgreement = true
                    this.includeCancelledAndDeletedAgreements = includeCancelledAndDeletedAgreements
                    productAccountId?.let { this.productAccountId = productAccountId }
                }
            )

            if (!agreement.hasFromDate()) {
                logger.error("[::getMeterpointAgreementStartDate] couldn't get agreement for meterpoint id [$id]")
            }

            agreement.fromDate
        } catch (e: Exception) {
            logger.error(
                "[::getMeterpointAgreementStartDate] meterPointId: $id does not have agreement start date. " +
                        "Error: ${e.message}"
            )
            null
        }
    }

    private fun mapReadFrequency(frequency: energy.so.assets.meterReadings.v2.ReadFrequency) =
        when (frequency) {
            energy.so.assets.meterReadings.v2.ReadFrequency.GREATER_THAN_DAILY_FREQUENCY -> ReadFrequency.GREATER_THAN_DAILY
            energy.so.assets.meterReadings.v2.ReadFrequency.DAILY_FREQUENCY -> ReadFrequency.DAILY
            energy.so.assets.meterReadings.v2.ReadFrequency.HALF_HOURLY_FREQUENCY -> ReadFrequency.HALF_HOURLY
            energy.so.assets.meterReadings.v2.ReadFrequency.UNRECOGNIZED, null -> ReadFrequency.GREATER_THAN_DAILY
        }
}

private fun String.capitalizeFirstLetter() =
    this.lowercase(Locale.getDefault())
        .replaceFirstChar { it.titlecase() }
