package energy.so.assets.server.models

enum class OpeningMeterReadingState {
    NOT_READY_FOR_OPENING_READINGS,
    AWAITING_GAS_READING,
    AWAITING_ELEC_READING,
    AWAITING_ELEC_AND_GAS_READINGS,
    ALL_SUBMITTED,
    MISSED_SUBMISSION_WINDOW
}

enum class SupplyState {
    PRE_ONBOARDING,
    ONBOARDING,
    ENROLMENT_REJECTED,
    ENROLMENT_OBJECTED,
    ENROLMENT_CANCELLED,
    ONBOARDING_PARTIALLY_REJECTED,
    ONBOARDING_PARTIALLY_OBJECTED,
    ONBOARDING_PARTIALLY_CANCELLED,
    ONBOARDED_RECENTLY,
    ON_SUPPLY,
    ONBOARDING_ADDITIONAL_METERPOINT,
    ONBOARDING_ADDITIONAL_METERPOINT_FAILED,
    PARTIAL_LOSS_INITIATED,
    LOSS_INITIATED,
    LOSS_COMPLETE,
    FUTURE_MOVE_OUT,
    FUTURE_MOVE_IN,
    MOVED_OUT,
    UNKNOWN
}

enum class ReadState {
    MISSING_MANUAL_READING,
    SUBMITTED_RECENTLY,
    MISSING_SMART_READING
}
