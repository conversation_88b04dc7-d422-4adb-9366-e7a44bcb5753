package energy.so.assets.server.services

import energy.so.ac.junifer.v1.assets.AssetsClient
import energy.so.ac.junifer.v1.assets.estimatedUsageRequest
import energy.so.ac.junifer.v1.assets.getMeterReadingRequest
import energy.so.assets.server.database.repositories.MeterRepository
import energy.so.assets.server.models.EstimatedRateName
import energy.so.assets.server.models.Meter
import energy.so.assets.server.models.MeterInfoDto
import energy.so.assets.server.models.MeterInfoResponseDto
import energy.so.assets.server.models.MeterReadDto
import energy.so.assets.server.models.MeterReadInfo
import energy.so.assets.server.models.MeterReadInfoResponse
import energy.so.assets.server.models.MeterReadingStatus
import energy.so.assets.server.models.MeterType
import energy.so.assets.server.models.addMeterReadInfo
import energy.so.assets.server.models.jsonFormat
import energy.so.assets.server.models.toMeterReadDtoList
import energy.so.assets.server.models.toMeterReadDtos
import energy.so.commons.exceptions.grpc.PermissionDeniedGrpcException
import energy.so.commons.grpc.clients.junifer.JuniferClient
import energy.so.commons.grpc.clients.junifer.dtos.EnergyUsageUnit
import energy.so.commons.grpc.clients.junifer.dtos.FindEstimatedUsageRequestDto
import energy.so.commons.grpc.clients.junifer.dtos.MeterPointType
import energy.so.commons.grpc.clients.junifer.dtos.MeterReadingsRequestDto
import energy.so.commons.grpc.clients.junifer.dtos.MeterReadingsWithTechnicalDetailsDto
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.queues.publishers.MessagePublisher
import energy.so.commons.session.SessionManager
import energy.so.commons.v2.dtos.IdRequest
import energy.so.commons.v2.dtos.idRequest
import energy.so.customers.client.v2.accounts.AccountsClient
import energy.so.customers.client.v2.accounts.NoNumericIdOnUserSession
import energy.so.customers.client.v2.accounts.UserAccountsAccess
import energy.so.customers.client.v2.accounts.findAccountIdsForSession
import energy.so.junifer.client.v1.JuniferAccountClient
import energy.so.junifer.client.v1.JuniferMeterClient
import energy.so.junifer.v2.MeterPointInfo
import energy.so.junifer.v2.getAccountAgreementRequest
import energy.so.junifer.v2.meterPointInfo
import energy.so.users.client.v2.FeatureClient
import energy.so.users.v2.FeatureName
import energy.so.users.v2.featureNameRequest
import java.time.Clock
import java.time.LocalDate
import java.time.LocalDateTime

private val logger = TraceableLogging.logger { }

class DefaultMeterService(
    private val meterRepository: MeterRepository,
    private val accountsClient: AccountsClient,
    private val juniferAccountClient: JuniferAccountClient,
    private val juniferMeterClient: JuniferMeterClient,
    private val juniferClient: JuniferClient,
    private val messagePublisher: MessagePublisher<String>,
    private val clock: Clock = Clock.systemDefaultZone(),
    private val assetsClient: AssetsClient,
    private val featureClient: FeatureClient,
) : MeterService {

    override suspend fun getMeterById(id: Long): Meter? =
        meterRepository.findById(id)

    override suspend fun getMeterInfo(account: String): MeterInfoResponseDto {
        val meterInfo = mutableListOf<MeterInfoDto>()
        val accountAgreements = juniferAccountClient.getAccountsAgreementsByAccountNumber(
            getAccountAgreementRequest {
                accountNumber = account
            }
        )

        val products = accountAgreements.accountsAgreementsList.filter {
            !it.cancelled.value && (!it.toDt.hasValue() || it.toDt.value.toLocalDate().isAfter(LocalDate.now()))
        }
            .flatMap { it.productsList }

        val gasProducts = products
            .filter { it.type.lowercase().contains("gas") }
            .flatMap { it.assetsList }
            .map { Pair(it.id, it.identifier) }
            .toSet()

        check(gasProducts.size <= 1) { "Unable to respond due to multi-gas meter setup not supported" }

        gasProducts.forEach {
            gasMeterInfo(
                meterPointInfo {
                    id = it.first
                    identifier = it.second
                }
            )?.let { meterInfo.add(it) }
        }

        val electricityProducts = products
            .filter { it.type.lowercase().contains("electricity") }
            .flatMap { it.assetsList }
            .map { Pair(it.id, it.identifier) }
            .toSet()

        check(electricityProducts.size <= 1) { "Unable to respond due to multi-electric meter setup not supported" }

        electricityProducts.forEach {
            meterInfo.addAll(
                electricityMeterInfo(
                    meterPointInfo {
                        id = it.first
                        identifier = it.second
                    }
                )
            )
        }

        return MeterInfoResponseDto(meterInfo)
    }

    private suspend fun getMeterReadDtos(meterPointId: Long): List<MeterReadDto> {
        val useAcJuniferReadingsFeatureEnabled = featureClient.getFeature(
            featureNameRequest { name = FeatureName.TMP_SO_22512_USE_AC_JUNIFER_READINGS }
        ).enabled

        return if (useAcJuniferReadingsFeatureEnabled) {
            // this call requires a Core id
            assetsClient.getMeterReadings(
                getMeterReadingRequest {
                    meterpointId = meterPointId
                    fromDt = LocalDate.now(clock).minusMonths(6).toTimestamp()
                    toDt = LocalDate.now(clock).toTimestamp()
                    status.add(MeterReadingStatus.ACCEPTED.value)
                }
            )
                .readingsList
                .toMeterReadDtos()
        } else {
            val meterPointRequest = idRequest { id = meterPointId }

            // this call requires a Junifer id
            juniferMeterClient.getAcceptedMeterReadings(meterPointRequest)
                .meterReadingsList
                .toMeterReadDtoList()
        }
    }

    override suspend fun getMeterReadInfo(meterPointId: Long): MeterReadInfoResponse {
        val meterReadDtos = getMeterReadDtos(meterPointId)
        val meterReads = mutableListOf<MeterReadInfo>()

        meterReadDtos
            .filter { it.source.lowercase() != "system" }
            .groupBy { it.meterIdentifier }.values.forEach { meterReadDto ->
                val sortedMeterReads = meterReadDto.sortedByDescending { it.readingDateTime }
                val rateNameToUsageMap = getRateNameToUsageMap(meterPointId)

                EstimatedRateName.values().forEach {
                    sortedMeterReads.addMeterReadInfo(
                        meterPointId,
                        it.name.lowercase(),
                        meterReads,
                        rateNameToUsageMap.getOrDefault(it.name.lowercase(), 0.0F)
                    )
                }

                if (meterReads.isEmpty()) {
                    sortedMeterReads.addMeterReadInfo(
                        meterPointId,
                        "",
                        meterReads,
                        rateNameToUsageMap.getOrDefault(EstimatedRateName.Standard.name.lowercase(), 0.0F)
                    )
                }
            }

        return MeterReadInfoResponse(meterReadInfo = meterReads)
    }

    private suspend fun getRateNameToUsageMap(meterPointIdToSearch: Long): Map<String, Float> {
        val useAcJuniferReadingsFeatureEnabled = featureClient.getFeature(
            featureNameRequest { name = FeatureName.TMP_SO_22512_USE_AC_JUNIFER_READINGS }
        ).enabled

        return if (useAcJuniferReadingsFeatureEnabled) {
            assetsClient.getMeterPointEstimatedUsage(
                estimatedUsageRequest {
                    meterPointId = meterPointIdToSearch
                    queryDate = LocalDateTime.now(clock).toTimestamp()
                }
            ).ratesList
                .associateBy { it.rateName.lowercase() }
                .mapValues { it.value.usage.toFloat() }
        } else {
            juniferClient.findEstimatedUsage(
                FindEstimatedUsageRequestDto(
                    meterPointId = meterPointIdToSearch,
                    queryDate = LocalDateTime.now(clock)
                )
            ).rates
                .associateBy { it.rateName.lowercase() }
                .mapValues { it.value.usage.toFloat() }
        }
    }

    override suspend fun submitMeterReadInfo(meterReading: MeterReadingsWithTechnicalDetailsDto) {
        logger.debug { "[::submitMeterReadInfo] Meter reading pushed to pubsub : {$meterReading}" }
        val meterReadingSubmissionMessage = jsonFormat.encodeToString(
            MeterReadingsRequestDto.serializer(),
            meterReading
        )
        messagePublisher.publishMessage(
            message = QueueMessage(
                meterReadingSubmissionMessage
            )
        )
    }

    override suspend fun getMetersByIds(ids: List<Long>): List<Meter> {
        return meterRepository.findByIds(ids)
    }

    override suspend fun getMetersByMeterPointIds(ids: List<Long>): List<Meter> =
        meterRepository.findByMeterPointIds(ids)

    private suspend fun metersByMeterPointInfo(meterPoint: MeterPointInfo): List<energy.so.junifer.v2.Meter> =
        juniferMeterClient.getMeterStructure(IdRequest.newBuilder().setId(meterPoint.id).build()).metersList

    private suspend fun electricityMeterInfo(meterPoint: MeterPointInfo): List<MeterInfoDto> {
        val electricMeters = metersByMeterPointInfo(meterPoint)

        check(electricMeters.size <= 1) { "Unable to respond due to multi-electric meter setup not supported" }

        val meterInfo = mutableListOf<MeterInfoDto>()
        electricMeters.firstOrNull()?.let {
            check(it.registersCount <= 2) { "Unable to respond due to multi-electric registry found." }

            it.registersList.forEach { meterRegistry ->
                meterInfo.add(
                    MeterInfoDto(
                        meterPointId = meterPoint.id,
                        identifier = meterPoint.identifier,
                        meterSerialNumber = it.identifier,
                        registerId = meterRegistry?.identifier ?: "BLANK",
                        unit = EnergyUsageUnit.KWH,
                        type = MeterPointType.MPAN,
                        meterType = MeterType.valueOf(it.meterType)
                    )
                )
            }
        }
        return meterInfo
    }

    private suspend fun gasMeterInfo(meterPoint: MeterPointInfo): MeterInfoDto? {
        val gasMeters = metersByMeterPointInfo(meterPoint)

        check(gasMeters.size <= 1) { "Unable to respond due to multi-gas meter setup not supported" }

        return gasMeters.firstOrNull()?.let {
            val gasMeterRegister = it.registersList.firstOrNull()
            MeterInfoDto(
                meterPointId = meterPoint.id,
                identifier = meterPoint.identifier,
                meterSerialNumber = it.identifier,
                registerId = gasMeterRegister?.identifier ?: "BLANK",
                unit = EnergyUsageUnit.M3,
                type = MeterPointType.MPRN,
                meterType = MeterType.valueOf(it.meterType)
            )
        }
    }

    /**
     * TODO: Currently un-used, added as part of SO-7161, once the account ID is stored on the DB, validate the user
     * has access to the account / meter they are querying.
     *
     * Ensures that the user in the current session has access to the provided account.
     *
     * Note: Depending on the number of these requests we expect, this could get inefficient as we are firing off a
     * network call to be-accounts for every request we get in. We should ensure be-accounts does not also query another
     * service from this.
     *
     * We could look to add some local caching with a short expiry to help here, or perhaps look at adding and extracting
     * the account IDs to the user token / session.
     *
     * @param accountId
     */
    internal suspend fun validateAccount(accountId: Long) {
        when (val result = accountsClient.findAccountIdsForSession(SessionManager.getSession())) {
            is UserAccountsAccess -> {
                if (!result.accountsIds.contains(accountId)) {
                    throw PermissionDeniedGrpcException(
                        "User ${result.userId} does not have permission to access account $accountId"
                    )
                }
            }

            NoNumericIdOnUserSession -> {
                throw PermissionDeniedGrpcException(
                    "No user found in session, cannot authenticate user to access meter points"
                )
            }
        }
    }
}
