package energy.so.assets.server.validators

import arrow.core.ValidatedNel
import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.assets.server.database.repositories.MeterReadingSubmissionRequestRepository
import energy.so.assets.server.models.MeterReadingSubmissionFilter
import energy.so.assets.server.models.MeterReadingsRequestDto
import energy.so.assets.server.models.getReadingDates
import energy.so.assets.server.services.FeatureService
import energy.so.users.v2.FeatureName
import org.koin.core.annotation.Single
import java.time.LocalDate

/**
 * A validator that is responsible for:
 * - Ensuring the meterpoint reading date is not in the future.
 * - Ensuring there is not another MeterReadingRequest for the same date.
 * <AUTHOR>
 */
@Single(createdAtStart = true)
class MeterPointSubmissionsValidator(
    private val meterReadingSubmissionRequestRepository: MeterReadingSubmissionRequestRepository,
    private val featureService: FeatureService,
) : MeterReadingValidator {

    override suspend fun validate(subject: MeterReadingsRequestDto): ValidatedNel<MeterReadingError, *> {
        if (featureService.isFeatureEnabled(FeatureName.TMP_SO_19083_ENABLE_SUBMISSION_VALIDATOR)) {
            val meterPointId = subject.meterPointId
            val readingDates = subject.getReadingDates().map { it.readingDate }

            if (readingDates.any { it > LocalDate.now() }) {
                return MeterReadingInvalidReadingDateError(meterPointId).invalidNel()
            }

            if (meterReadingSubmissionRequestRepository.search(
                    MeterReadingSubmissionFilter(
                        meterPointId = meterPointId,
                        createdAt = readingDates.minOf { it }.atStartOfDay()
                    )
                )
                    .flatMap { it.submissionData.getReadingDates() }
                    .any { readingDates.contains(it.readingDate) }
            ) {
                return MeterReadingAlreadyExistingReadForDateError(meterPointId).invalidNel()
            }
        }
        return validNel()
    }
}
