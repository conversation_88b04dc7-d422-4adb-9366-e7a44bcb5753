package energy.so.assets.server.models

import energy.so.ac.junifer.v1.assets.meterReading
import energy.so.ac.junifer.v1.assets.meterReadingsWithTechnicalDetails
import energy.so.ac.junifer.v1.assets.meterReadingsWithoutTechnicalDetails
import energy.so.ac.junifer.v1.assets.read
import energy.so.ac.junifer.v1.assets.submitMeterReadingsRequest
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.json.serializers.LocalDateAsISO8601Serializer
import energy.so.commons.json.serializers.LocalDateTimeAsISO8601Serializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.ac.junifer.v1.assets.SequenceType as JuniferSequenceType
import energy.so.ac.junifer.v1.assets.SubmissionMode as JuniferSubmissionMode

@Serializable
sealed class MeterReadingsRequestDto {
    abstract val meterPointId: Long
    abstract val userId: Long?
    abstract val billingAccountId: Long?
    fun toProtobuf() = let { from ->
        submitMeterReadingsRequest {
            meterPointId = from.meterPointId
            when (from) {
                is MeterReadingsWithoutTechnicalDetailsDto -> meterReadingWithoutTechnicalDetails = from.toProto()
                is MeterReadingsWithTechnicalDetailsDto -> meterReadingWithTechnicalDetails = from.toProto()
            }
        }
    }
}

fun MeterReadingsRequestDto.getReadingDates() = when (this) {
    is MeterReadingsWithoutTechnicalDetailsDto -> listOf(ReadingDate(this.readingDate))
    is MeterReadingsWithTechnicalDetailsDto -> this.readings.map { ReadingDate(it.readingDateTime.toLocalDate()) }
}

@Serializable
@SerialName("MeterReadingsWithoutTechnicalDetails")
data class MeterReadingsWithoutTechnicalDetailsDto(
    override val meterPointId: Long,
    override val userId: Long? = null,
    override val billingAccountId: Long? = null,
    @Serializable(LocalDateAsISO8601Serializer::class)
    val readingDate: LocalDate,
    var sequenceType: SequenceType,
    val source: String,
    val quality: String,
    val ignoreWarnings: Boolean = false,
    val description: String? = null,
    val registerReads: List<Read>,
    @Serializable(LocalDateAsISO8601Serializer::class)
    val agreementFromDate: LocalDate? = null,
) : MeterReadingsRequestDto() {
    fun toProto() = let { from ->
        meterReadingsWithoutTechnicalDetails {
            readingDate = from.readingDate.toTimestamp()
            sequenceType = from.sequenceType.toSiblingEnum()
            source = from.source
            quality = from.quality
            ignoreWarnings = from.ignoreWarnings
            description = from.description.toNullableString()
            registerReads.addAll(from.registerReads.map { it.toProto() })
        }
    }
}

@Serializable
data class Read(
    val reading: Long,
    val readingType: ReadingType,
    val unitType: UnitType,
) {
    fun toProto() = let { from ->
        read {
            reading = from.reading
            readingType = from.readingType.toSiblingEnum()
        }
    }

    fun isElectricityReading() = unitType != UnitType.Gas
}

@Serializable
@SerialName("MeterReadingsWithTechnicalDetails")
data class MeterReadingsWithTechnicalDetailsDto(
    override val meterPointId: Long,
    override val userId: Long? = null,
    override val billingAccountId: Long? = null,
    val ignoreWarnings: Boolean? = null,
    val submissionMode: SubmissionMode? = SubmissionMode.Submit,
    val readings: List<MeterReadings>,
    val validateOnly: Boolean? = false
) : MeterReadingsRequestDto() {
    fun toProto() = let { from ->
        meterReadingsWithTechnicalDetails {
            ignoreWarnings = from.ignoreWarnings ?: false
            submissionMode = from.submissionMode?.toSiblingEnum<JuniferSubmissionMode>()
                ?: JuniferSubmissionMode.Submit
            validateOnly = from.validateOnly ?: false
            readings.addAll(from.readings.map { it.toProto() })
        }
    }
}

@Serializable
data class MeterReadings(
    val registerId: Long? = null,
    val meterIdentifier: String,
    val registerIdentifier: String?,
    @Serializable(LocalDateTimeAsISO8601Serializer::class)
    val readingDateTime: LocalDateTime,
    var sequenceType: SequenceType? = null,
    val source: String,
    val quality: String,
    val cumulative: Long,
    val description: String? = null,
    val unitType: UnitType,
    @Serializable(LocalDateAsISO8601Serializer::class)
    val agreementFromDate: LocalDate? = null
) {
    fun toProto() = let { from ->
        meterReading {
            meterIdentifier = from.meterIdentifier
            registerIdentifier = from.registerIdentifier.toNullableString()
            readingDateTime = from.readingDateTime.toTimestamp()
            sequenceType = from.sequenceType?.toSiblingEnum<JuniferSequenceType>()
                ?: JuniferSequenceType.First
            source = from.source
            quality = from.quality
            cumulative = from.cumulative
            description = from.description.toNullableString()
        }
    }

    fun isElectricityReading() = unitType != UnitType.Gas
}

enum class ReadingType { Day, Night, Standard }

enum class SubmissionMode { Submit, Replace }

enum class UnitType { Electricity, Electricity_Night, Gas }

enum class SequenceType { Normal, First, Last }
