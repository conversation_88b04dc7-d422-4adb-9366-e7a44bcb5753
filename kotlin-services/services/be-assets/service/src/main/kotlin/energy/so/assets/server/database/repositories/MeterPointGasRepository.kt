package energy.so.assets.server.database.repositories

import energy.so.assets.server.models.MeterPointGas

/**
 * Repository interface exposing all database operations done on [MeterPointGas]
 */
interface MeterPointGasRepository {

    /**
     * Returns a [MeterPointGas] for given id.
     *
     * @param id The id of the entity.
     * @return A [MeterPointGas] matching the id or null otherwise.
     */
    fun findById(id: Long): MeterPointGas?
}
