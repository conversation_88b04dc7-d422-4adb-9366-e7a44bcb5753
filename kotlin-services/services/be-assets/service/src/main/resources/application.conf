environment {
  port = 50053
  port = ${?PORT}
  environment = "development"
  environment = ${?BE_ASSETS_ENV}
}

on-demand {
  onDemandBranchName = "default"
  onDemandBranchName = ${?BRANCH_NAME}
}

database {
  default {
    username = "postgres"
    username = ${?DB_USERNAME}
    password = "postgres"
    password = ${?DB_PASSWORD}
    host = "localhost"
    host = ${?DB_HOST}
    dbName = "be_assets"
    dbName = ${?DB_NAME}
    maximumPoolSize = 10
    maximumPoolSize = ${?DB_MAXIMUM_POOL_SIZE}
  }
}

meter-reading {
  firstReadingPeriodDaysElec = "5"
  firstReadingPeriodDaysElec = ${?METER_READING_FIRST_READING_PERIOD_DAYS_ELEC}
  firstReadingMinPeriodDaysGas = "0"
  firstReadingMinPeriodDaysGas = ${?METER_READING_FIRST_READING_MIN_PERIOD_DAYS_GAS}
  firstReadingMaxPeriodDaysGas = "10"
  firstReadingMaxPeriodDaysGas = ${?METER_READING_FIRST_READING_MAX_PERIOD_DAYS_GAS}
  ignoreMeterWarnings = true
  ignoreMeterWarnings = ${?METER_READING_IGNORE_METER_WARNINGS}
}

be-customers {
  host = "host.docker.internal"
  host = ${?BE_CUSTOMERS_HOST}
  port = 8080
  port = ${?BE_CUSTOMERS_PORT}
  callTimeoutInSeconds = 60
  callTimeoutInSeconds = ${?BE_CUSTOMERS_TIMEOUT}
}

be-identity {
  host = "localhost"
  host = ${?BE_IDENTITY_HOST}
  port = 50052
  port = ${?BE_IDENTITY_PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_IDENTITY_TIMEOUT}
}

be-communications {
  callTimeoutInSeconds = 5
  callTimeoutInSeconds = ${?COMMUNICATIONS_HUB_TIMEOUT}
  host = localhost
  host = ${?COMMUNICATIONS_HUB_HOST}
  port = 50053
  port = ${?COMMUNICATIONS_HUB_PORT}
}

be-junifer {
  host = "localhost"
  host = ${?BE_JUNIFER_HOST}
  port = 50051
  port = ${?BE_JUNIFER_PORT}
  callTimeoutInSeconds = 10
}

be-ac-junifer {
  host = localhost
  host = ${?BE_AC_JUNIFER_HOST}
  port = 8080
  port = ${?BE_AC_JUNIFER_PORT}
  callTimeoutInSeconds = 50
  callTimeoutInSeconds = ${?BE_AC_JUNIFER_TIMEOUT}
}

be-ac-esg {
  host = localhost
  host = ${?BE_AC_ESG_HOST}
  port = 50052
  port = ${?BE_AC_ESG_PORT}
  callTimeoutInSeconds = 50
  callTimeoutInSeconds = ${?BE_AC_ESG_TIMEOUT}
}

communication-template {
  openingMeterReadNotifierEmail = "opening_meter_read_notifier_email"
  outboundPhotoRequestEmail = "outbound_photo_request"
  outboundPhotoRequestFollowUpEmail = "outbound_photo_request_follow_up"
  uploadOutboundPhotosUrl = "https://myaccount.staging.soenergy.co/readings?openPhotoRequest=true"
  uploadOutboundPhotosUrl = ${?UPLOAD_OUTBOUND_PHOTOS_URL}
  genericNovaMeterPhotoHistoryModalUrl = "https://nova.staging.soenergy.co/customer/{{accountNumber}}/meterpoints?openPhotoHistoryModal=true"
  genericNovaMeterPhotoHistoryModalUrl = ${?GENERIC_NOVA_METER_PHOTO_HISTORY_MODAL_URL}
}

outboundPhoto {
  email = "<EMAIL>"
  emailName = "So Energy - Meter Reads Team"
  maxActivityPeriodDays = "7"
  maxActivityPeriodDays = ${?OUTBOUND_PHOTO_MAX_ACTIVITY_PERIOD_DAYS}
}

pubsub {
  projectName = "soe-nonprod-core-apps-e5fb"
  projectName = ${?GCP_PROJECT}
  topics = [
    {
      key = "be-assets-meter-readings-events"
      name = "be-assets-meter-readings-events"
      name = ${?GCP_METER_READINGS_EVENTS_QUEUE_TOPIC_NAME}
      timeoutInSeconds = 10
    },
    {
      key = "be-assets-meter-readings-submissions"
      name = "be-assets-meter-readings-submissions"
      name = ${?GCP_METER_READINGS_SUBMISSIONS_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-meter-read-submission-requests"
      name = "be-assets-meter-read-submission-requests"
      name = ${?GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-queue-persisted-meter-read-submission-requests"
      name = "be-assets-queue-persisted-meter-read-submission-requests"
      name = ${?GCP_BE_ASSETS_QUEUE_PERSISTED_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-update-contacted-reads-status"
      name = "be-assets-update-contacted-reads-status"
      name = ${?GCP_BE_ASSETS_UPDATE_CONTACTED_READS_STATUS_TOPIC_NAME}
    },
    {
      key = "be-assets-photo-evidence-freshdesk-ticket"
      name = "be-assets-photo-evidence-freshdesk-ticket"
      name = ${?GCP_BE_ASSETS_PHOTO_EVIDENCE_FRESHDESK_TICKET_TOPIC_NAME}
    },
    {
      key = "be-assets-meter-read-status-updates"
      name = "be-assets-meter-read-status-updates"
      name = ${?GCP_BE_ASSETS_METER_READ_STATUS_UPDATE_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-outbound-photo-requests"
      name = "be-assets-outbound-photo-requests"
      name = ${?GCP_BE_ASSETS_OUTBOUND_PHOTO_REQUESTS_TOPIC_NAME}
    },
    {
      key = "be-assets-automate-close-outbound-req"
      name = "be-assets-automate-close-outbound-req"
      name = ${?GCP_BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ_TOPIC_NAME}
    }
  ]
  subscriptions = [
    {
      key = "be-assets-meter-readings-submissions-subscription"
      topicKey = "be-assets-meter-readings-submissions"
      name = "be-assets-meter-readings-submissions-subscription"
      name = ${?GCP_METER_READINGS_SUBMISSIONS_QUEUE_SUBSCRIPTION_NAME}
      topic = "be-assets-meter-readings-submissions"
      topic = ${?GCP_METER_READINGS_SUBMISSIONS_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-meter-read-submission-requests-subscription"
      topicKey = "be-assets-meter-read-submission-requests"
      name = "be-assets-meter-read-submission-requests-subscription"
      name = ${?GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_SUBSCRIPTION_NAME}
      topic = "be-assets-meter-read-submission-requests"
      topic = ${?GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME}
      maximumDeliveryAttempts = 10
      maximumDeliveryAttempts = ${?GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_MAXIMUM_DELIVERY_ATTEMPTS}
    },
    {
      key = "be-assets-queue-persisted-meter-read-submission-requests-subscription"
      topicKey = "be-assets-queue-persisted-meter-read-submission-requests"
      name = "be-assets-queue-persisted-meter-read-submission-requests-subscription"
      name = ${?GCP_BE_ASSETS_QUEUE_PERSISTED_METER_READ_SUBMISSION_REQUESTS_QUEUE_SUBSCRIPTION_NAME}
      topic = "be-assets-queue-persisted-meter-read-submission-requests"
      topic = ${?GCP_BE_ASSETS_QUEUE_PERSISTED_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-update-contacted-reads-status-subs"
      topicKey = "be-assets-update-contacted-reads-status"
      name = "be-assets-update-contacted-reads-status-subs"
      name = ${?GCP_BE_ASSETS_UPDATE_CONTACTED_READS_STATUS_SUBSCRIPTION_NAME}
      topic = "be-assets-update-contacted-reads-status"
      topic = ${?GCP_BE_ASSETS_UPDATE_CONTACTED_READS_STATUS_TOPIC_NAME}
    },
    {
      key = "be-assets-photo-evidence-freshdesk-ticket-subs"
      topicKey = "be-assets-photo-evidence-freshdesk-ticket"
      name = "be-assets-photo-evidence-freshdesk-ticket-subs"
      name = ${?GCP_BE_ASSETS_PHOTO_EVIDENCE_FRESHDESK_TICKET_SUBSCRIPTION_NAME}
      topic = "be-assets-photo-evidence-freshdesk-ticket"
      topic = ${?GCP_BE_ASSETS_PHOTO_EVIDENCE_FRESHDESK_TICKET_TOPIC_NAME}
    },
    {
      key = "be-assets-meter-reading-status-updates-subs"
      topicKey = "be-assets-meter-reading-status-updates"
      name = "be-assets-meter-read-status-updates-subs"
      name = ${?GCP_BE_ASSETS_METER_READ_STATUS_UPDATE_QUEUE_TOPIC_NAME}
      topic = "be-assets-meter-reading-status-updates"
      topic = ${?GCP_BE_ASSETS_METER_READ_STATUS_UPDATE_QUEUE_TOPIC_NAME}
    },
    {
      key = "be-assets-outbound-photo-requests-subs"
      topicKey = "be-assets-outbound-photo-requests"
      name = "be-assets-outbound-photo-requests-subs"
      name = ${?GCP_BE_ASSETS_OUTBOUND_PHOTO_REQUESTS_SUBSCRIPTION_NAME}
      topic = "be-assets-outbound-photo-requests"
      topic = ${?GCP_BE_ASSETS_OUTBOUND_PHOTO_REQUESTS_TOPIC_NAME}
    },
    {
      key = "be-assets-automate-close-outbound-req-subs"
      topicKey = "be-assets-automate-close-outbound-req"
      name = "be-assets-automate-close-outbound-req-subs"
      name = ${?GCP_BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ_SUBSCRIPTION_NAME}
      topic = "be-assets-automate-close-outbound-req"
      topic = ${?GCP_BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ_TOPIC_NAME}
    }
  ]
}

redis {
  host = localhost
  host = ${?REDIS_HOST}
  authString = b1558e84-9c09-48e3-bdb7-64861617a0b8
  authString = ${?REDIS_AUTH_STRING}
  cluster = false
  cluster = ${?REDIS_CLUSTER}
  disabled-for-junifer-entity-mapper = false
  disabled-for-junifer-entity-mapper = ${?DISABLED_FOR_JUNIFER_ENTITY_MAPPER}
  topics = {
    meter-reading-status = "meter-reading-status"
  }
}

shadowTraffic {
  enabled = false
  enabled = ${?ENABLED_SHADOW_TRAFFIC}
}

asyncMeterReadingSubmissionRequest {
  maxFailedAttempts = 10
  maxFailedAttempts = ${?GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_MAXIMUM_DELIVERY_ATTEMPTS}
}

googleStorage {
  projectName = "soe-nonprod-core-apps-e5fb"
  projectName = ${?GCP_PROJECT}
  credentialsPath = "/storage/soe-nonprod-core-apps-e5fb-037ed0cdc184.json"
  credentialsPath = ${?GOOGLE_STORAGE_CREDENTIALS_PATH}
  bucketName = "test-bucket-soenergy"
  bucketName = ${?READINGS_PHOTO_EVIDENCE_BUCKET}
  tmpBucketName = "test-tmp-bucket-soenergy"
  tmpBucketName = ${?READINGS_TMP_PHOTO_EVIDENCE_BUCKET}
  readingsEvidenceFolder = "readingsEvidence"
  readingsEvidenceFolder = ${?GCP_READINGS_EVIDENCE_FOLDER}
  uploadExpiryInSeconds = "900"
  uploadExpiryInSeconds = ${?METER_READ_IMAGE_UPLOAD_EXPIRY}
  getExpiryInSeconds = "3600"
  getExpiryInSeconds = ${?METER_READ_IMAGE_GET_EXPIRY}
  uploadMaxFileSize = "20971520"
  uploadMaxFileSize = ${?METER_READ_IMAGE_UPLOAD_MAX_SIZE}
}

freshdesk {
  host = "localhost"
  host = ${?FRESHDESK_HOST}
  connectionTimeout = 60000
  connectionTimeout = ${?FRESHDESK_CONNECTION_TIMEOUT}
  requestTimeout = 60000
  requestTimeout = ${?FRESHDESK_REQUEST_TIMEOUT}
  apiKey = "api-key"
  apiKey = ${?FRESHDESK_API_KEY}
  password = "X"
  password = ${?FRESHDESK_PASSWORD}
  groupId = "1007000005576"
  groupId = ${?FRESHDESK_GROUP_ID}
  sleepMillis = ${?FRESHDESK_SLEEP_MILLIS}
  sleepMillis = "30000"
  delayMillis = ${?FRESHDESK_DELAY_MILLIS}
  delayMillis = "1"
  rateLimit = ${?FRESHDESK_RATE_LIMIT}
  rateLimit = "1"
  maxRetry = ${?FRESHDESK_MAX_RETRY}
  maxRetry = "2"
}

emailClientConfig {
  bootstrapServer = "pkc-l6wr6.europe-west2.gcp.confluent.cloud:9092"
  bootstrapServer = ${?KAFKA_BOOTSTRAP_SERVER}
  schemaRegistryUrl = "https://psrc-kk5gg.europe-west3.gcp.confluent.cloud"
  schemaRegistryUrl = ${?KAFKA_SCHEMA_REGISTRY_URL}
  usernamePassword = "XXXXXXXXXXXX:xxxxxxxxxxxxxx"
  usernamePassword = ${?KAFKA_SCHEMA_REGISTRY_CREDENTIALS}
  jaasConfig = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"USERNAME\" password=\"PASSWORD\";"
  jaasConfig = ${?KAFKA_JAAS_CONFIG}
  requestTimeoutMs = 20000
  basicAuthCredentialsSource = "USER_INFO"
  dotdigitalTopic = "dotdigital-emails"
  dotdigitalTopic = ${?KAFKA_DOTDIGITAL_EMAIL_TOPIC_NAME}
}

awsConnect {
  region = "eu-west-2"
  region = ${?AWS_REGION}
  projectId = "844728591657"
  projectId = ${?AWS_PROJECT_ID}
  accessKey = "accessKey"
  accessKey = ${?AWS_ACCESS_KEY}
  secretAccessKey = "secretAccessKey"
  secretAccessKey = ${?AWS_SECRET_ACCESS_KEY}
  casesDomain = "affbfa92-650d-453f-9386-471f9e81744b"
  casesDomain = ${?AWS_CONNECT_CASES_DOMAIN}
  customerProfilesDomain = "so-energy-dev-customer-profiles"
  customerProfilesDomain = ${?AWS_CONNECT_CUSTOMER_PROFILES_DOMAIN}
  instanceId = "0a808e34-1797-4e32-bc98-96e0bc6a2fd2"
  instanceId = ${?AWS_CONNECT_INSTANCES_ID}
  templateId = "deb06a0b-a97b-471d-8925-c24b78ca3c14"
  templateId = ${?AWS_CONNECT_CASE_TEMPLATE_ID}
  connectTimeout = 30000
  connectTimeout = ${?AWS_CONNECT_TIMEOUT}
  socketTimeout = 60000
  socketTimeout = ${?AWS_SOCKET_TIMEOUT}
  maxRetries = 3
  maxRetries = ${?AWS_CONNECT_MAX_RETRIES}
  attachmentsBucket = "amazon-connect-dev-bucket-214365"
  attachmentsBucket = ${?AWS_CONNECT_ATTACHMENTS_BUCKET}
  instanceAlias = "soenergydev"
  instanceAlias = ${?AWS_CONNECT_INSTANCE_ALIAS}
  queues = {
    customerCare = "arn:aws:connect:eu-west-2:844728591657:instance/0a808e34-1797-4e32-bc98-96e0bc6a2fd2/queue/26d232cc-56a6-4a8c-9378-3752f1a49dce"
    customerCare = ${?AWS_CONNECT_CUSTOMER_CARE_QUEUE_ARN}
  }
  customFields = {
    dueDate = "e8a7e160-c904-4e53-bffc-7aeca0c54390"
    dueDate = ${?AWS_CONNECT_CASE_DUE_DATE_FIELD}
    description = "a3f83902-0ed1-46f3-82d8-8e0e48c450e0"
    description = ${?AWS_CONNECT_CASE_DESCRIPTION_FIELD}
    freshdeskId = "e7ad1753-1fc6-4b56-ac97-28bf8216cfd9"
    freshdeskId = ${?AWS_CONNECT_CASE_FRESHDESK_ID_FIELD}
  }
  emailAddresses = {
    helpAddress = "<EMAIL>"
    helpAddress = ${?AWS_CONNECT_HELP_ADDRESS}
  }
}
