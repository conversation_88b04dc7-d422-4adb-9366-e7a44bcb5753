alter table if exists meter_point_electricity
rename column line_loss_factor_classid to line_loss_factor_class_id;

ALTER TABLE if exists meter_reading ALTER COLUMN status DROP NOT NULL;
ALTER TABLE if exists meter_reading ALTER COLUMN consumption DROP NOT NULL;
ALTER TABLE if exists meter_reading ALTER COLUMN sequence_type DROP NOT NULL;
ALTER TABLE if exists meter_reading ALTER COLUMN meter_id DROP NOT NULL;

ALTER TABLE if exists meter_reading DROP COLUMN invalidity_reason;

create type meter_reading_submission_status as enum ('SUBMITTED', 'ERRORED');
ALTER TABLE if exists meter_reading ADD COLUMN submission_status meter_reading_submission_status NOT NULL;
