create index if not exists postcode_idx on address (postcode);
create index if not exists estimated_usage_meter_point_id_idx on estimated_usage (meter_point_id);
create index if not exists meter_point_history_meter_point_id_idx on meter_point_history (meter_point_id);
create index if not exists meter_meter_point_rel_meter_point_id_idx on meter_meter_point_rel (meter_point_id);
create index if not exists meter_meter_point_rel_meter_id_idx on meter_meter_point_rel (meter_id);
create index if not exists property_address_id_idx on property (address_id);
create index if not exists meter_reading_meter_point_id_idx on meter_reading (meter_point_id);
create index if not exists meter_reading_meter_id_idx on meter_reading (meter_id);
create index if not exists postcode_gsp_cache_postcode_idx on postcode_gsp_cache (postcode);
create index if not exists register_meter_id_idx on register (meter_id);
create index if not exists register_identifier_idx on register (identifier);
