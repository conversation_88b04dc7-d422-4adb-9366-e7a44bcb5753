drop trigger if exists set_updatedat_outbound_photo_request on public.outbound_photo_request;
drop trigger if exists set_updatedat_meter_reading_photo_evidence on public.meter_reading_photo_evidence;

create trigger set_updatedat_outbound_photo_request
    before update
    on public.outbound_photo_request
    for each row
execute procedure public.set_updated_at_function();

create trigger set_updatedat_meter_reading_photo_evidence
    before update
    on public.meter_reading_photo_evidence
    for each row
    execute procedure public.set_updated_at_function();
