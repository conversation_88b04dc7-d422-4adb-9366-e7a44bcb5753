create table address
(
    id           bigint generated by default as identity not null,
    address1     varchar,
    address2     varchar,
    address3     varchar,
    address4     varchar,
    address5     varchar,
    postcode     varchar(8),
    country_code varchar,
    country      varchar,
    deleted      timestamp,
    created_at   timestamp                               not null default current_timestamp,
    updated_at   timestamp                               not null default current_timestamp,

    constraint pk_address primary key (id)
);

CREATE TYPE property_type AS ENUM ('FLAT', 'HOUSE', 'SITE');
create table property
(
    id         bigint generated by default as identity not null,
    address_id bigint                                  not null,
    type       property_type                           not null,
    deleted    timestamp,
    created_at timestamp                               not null default current_timestamp,
    updated_at timestamp                               not null default current_timestamp,

    constraint fk_address_id foreign key (address_id) references address (id),
    constraint pk_property primary key (id)
);

create table property_agreement_rel
(
    id           bigint generated by default as identity not null,
    property_id  bigint                                  not null,
    agreement_id bigint                                  not null,
    from_date    date                                    not null,
    to_date      date,

    constraint pk_property_agreement_rel primary key (id),
    constraint fk_property_id foreign key (property_id) references property (id)
);

create type meter_point_type as enum ('MPAN', 'MPRN');
create type meter_point_measurement_type as enum ('Import', 'Export');
create type operation_type as enum ('CREDIT', 'PREPAY','UNKNOWN');
create type dcc_service_status as enum ('ACTIVE', 'INSTALLED_NOT_COMMISSIONED','NON_ACTIVE','WITHDRAWN');
create type uk_profile_class as enum ('DOMESTIC_UNRESTRICTED','DOMESTIC_ECONOMY_7','NONDOMESTIC_UNRESTRICTED','NONDOMESTIC_ECONOMY_7','NONDOMESTIC_MD_LOAD_FACTOR_0_20','NONDOMESTIC_MD_LOAD_FACTOR_20_30','NONDOMESTIC_MD_LOAD_FACTOR_30_40','NONDOMESTIC_MD_LOAD_FACTOR_40','AUTOGENERATED_HALFHOURLY_PROFILE_CLASS');

create table meter_point
(
    id                     bigint generated by default as identity not null,
    property_id            bigint                                  not null,
    identifier             varchar                                 not null,
    type                   meter_point_type                        not null,
    supply_start_date      date                                    not null,
    change_of_tenancy_fl   boolean                                 not null,
    uk_profile_class       uk_profile_class,
    uk_gsp_group           varchar                                 not null,
    operation_type         operation_type                          not null,
    reading_frequency_code varchar,
    service_type           varchar                                 not null,
    measurement_type       meter_point_measurement_type,
    dcc_service_status     dcc_service_status,
    deleted                timestamp,
    created_at             timestamp                               not null default current_timestamp,
    updated_at             timestamp                               not null default current_timestamp,

    constraint pk_meter_point primary key (id),
    constraint fk_properties_id foreign key (property_id) references property (id)

);

CREATE TYPE meter_type AS ENUM ( 'CHECK', 'H', 'K', 'LAG_', 'LEAD_', 'MAIN_', 'N', 'NCAMR', 'NSS', 'RCAMR', 'RCAMY', 'S', 'SPECL', 'T', 'CM', 'CR', 'ET', 'MT', 'PP', 'TH', 'U', 'S1', 'S2', 'S2A', 'NS', 'S2C', 'S2AD', 'S2BD', 'S2CD', 'S2ADE', 'S2BDE', 'S2CDE', 'S2B', 'CONV');
create table meter_point_electricity
(
    id                                   bigint generated by default as identity not null,
    meter_point_id                       bigint                                  not null,
    energised                            boolean                                 not null,
    line_loss_factor_classId             varchar                                 not null,
    measurement_class                    varchar,
    meter_serial_number                  varchar,
    meter_timeswitch_code                int,
    meter_type                           meter_type,
    smso_market_participant_id           bigint,
    standard_settlement_configuration_id bigint,
    mop_market_participant               varchar,
    distribution_network_operator        varchar,
    deleted                              timestamp,
    created_at                           timestamp                               not null default current_timestamp,
    updated_at                           timestamp                               not null default current_timestamp,

    constraint pk_meter_points_electricity primary key (id),
    constraint fk_meter_point_id foreign key (meter_point_id) references meter_point (id)

);

create table meter_point_gas
(
    id                     bigint generated by default as identity not null,
    meter_point_id         bigint                                  not null,
    designation            varchar,
    meter_serial_number    varchar,
    prepay_detected        boolean,
    igt_identifier         varchar,
    mam_market_participant varchar,
    deleted                timestamp,
    created_at             timestamp                               not null default current_timestamp,
    updated_at             timestamp                               not null default current_timestamp,

    constraint pk_meter_point_gas primary key (id),
    constraint fk_meter_point_id foreign key (meter_point_id) references meter_point (id)

);

CREATE TYPE asset_type AS ENUM ( 'METER','EV_CHARGER','BATTERY');
create table asset
(
    id         bigint generated by default as identity not null,
    type       asset_type                              not null,
    deleted    timestamp,
    created_at timestamp                               not null default current_timestamp,
    updated_at timestamp                               not null default current_timestamp,

    constraint pk_asset primary key (id)
);

create table meter
(
    id         bigint generated by default as identity not null,
    asset_id   bigint                                  not null,
    identifier varchar                                 not null,
    type       meter_type                              not null,
    deleted    timestamp,
    created_at timestamp                               not null default current_timestamp,
    updated_at timestamp                               not null default current_timestamp,

    constraint pk_meter primary key (id),
    constraint fk_assets_id foreign key (asset_id) references asset (id)
);


create table meter_meter_point_rel
(
    id             bigint generated by default as identity not null,
    meter_id       bigint                                  not null,
    meter_point_id bigint                                  not null,
    deleted        timestamp,
    created_at     timestamp                               not null default current_timestamp,
    updated_at     timestamp                               not null default current_timestamp,

    constraint pk_meter_meter_point_rel primary key (id),
    constraint fk_meter_id foreign key (meter_id) references meter (id),
    constraint fk_meter_point_id foreign key (meter_point_id) references meter_point (id),
    constraint uk_meter_id_meter_point_id unique (meter_id, meter_point_id)
);

create table register
(
    id             bigint generated by default as identity not null,
    meter_id       bigint                                  not null,
    identifier     varchar                                 not null,
    type           varchar                                 not null,
    digits         int                                     not null,
    decimal_places int                                     not null,
    rate_name      varchar,
    usage          numeric                                 not null,
    deleted        timestamp,
    created_at     timestamp                               not null default current_timestamp,
    updated_at     timestamp                               not null default current_timestamp,

    constraint pk_register primary key (id),
    constraint fk_meters_id foreign key (meter_id) references meter (id)
);

create type meter_reading_quality as enum ('NORMAL', 'MANUAL', 'ESTIMATED');
create type meter_reading_sequence_type as enum ('NORMAL', 'FIRST', 'LAST');
create type meter_reading_source as enum ('MMR', 'System', 'Customer', 'User', 'Virtual', 'SMR', 'Industry', 'EDMI', 'Secure', 'AMR');
create type meter_reading_status as enum ('ACTIVE', 'CANCELLED', 'FAILED', 'LOADED');
create type meter_reading_unit as enum ('kWh','MWh','kVAh','hour','day','month','second','kVArh','MJ','therm','kW','MW','kVAr','kVA','m3','tm3','hm3','thm3','ft3','tcf','hcf','thcf','year');
create type meter_reading_workflow_status as enum ('VALIDATION_SCHEDULED', 'VALIDATION_SCHEDULED_WITH_OVERRIDE', 'AWAITING_INDUSTRY_RESPONSE', 'VALIDATION_SUCCESS', 'VALIDATION_FAILURE', 'ACCEPTED', 'ERROR', 'SCHEDULED_FOR_PUBLISHING', 'FLAGGED');

create table meter_reading
(
    id                          bigint generated by default as identity not null,
    meter_point_id              bigint                                  not null,
    meter_id                    bigint                                  not null,
    register_id                 bigint                                  not null,
    reading_dttm                timestamp                               not null,
    from_dttm                   timestamp                               not null,
    status                      meter_reading_status                    not null,
    sequence_type               meter_reading_sequence_type             not null,
    source                      meter_reading_source                    not null,
    quality                     meter_reading_quality                   not null,
    cumulative                  numeric                                 not null,
    consumption                 numeric                                 not null,
    unit                        meter_reading_unit                      not null,
    workflow_status             meter_reading_workflow_status           not null,
    invalidity_reason           varchar,
    rate_name                   varchar                                 not null,
    pending_register_reading_id varchar                                 not null,
    deleted                     timestamp,
    created_at                  timestamp                               not null default current_timestamp,
    updated_at                  timestamp                               not null default current_timestamp,

    constraint pk_meter_reading primary key (id),
    constraint fk_meter_points_id foreign key (meter_point_id) references meter_point (id),
    constraint fk_register_id foreign key (register_id) references register (id)
);

create type estimated_usage_type as enum ('ANNUAL_QUANTITY','ESTIMATED_ANNUAL_CONSUMPTION','ANNUAL_CONSUMPTION_DETAILS');
create type estimated_rate_name as enum ('Standard','Day','Night');

create table estimated_usage
(
    id             bigint generated by default as identity not null,
    meter_point_id bigint                                  not null,
    type           estimated_usage_type                    not null,
    rate_name      estimated_rate_name                     not null,
    units          varchar                                 not null,
    usage          numeric                                 not null,
    deleted        timestamp,
    created_at     timestamp                               not null default current_timestamp,
    updated_at     timestamp                               not null default current_timestamp,

    constraint pk_estimated_usage primary key (id),
    constraint fk_meter_point_id foreign key (meter_point_id) references meter_point (id)
);

create type meter_point_event_type as enum ('Created','ManualUpdate','RegistrationStarted','RegistrationRejected','RegistrationWithdrawRequested','RegistrationWithdrawRejected','RegistrationWithdrawn','RegistrationAccepted','RegistrationObjectionRaised','RegistrationObjectionRemoved','RegistrationObjectionUpheld','LossInitiated','LossObjectionRaised','LossObjectionRemoved','LossObjectionUpheld','RegistrationWithdrawnByNewSupplier','LossConfirmed','LossCompleted','RegistrationCancelled','RegistrationConfirmed','RegistrationCompleted','LossCancelled');
create type meter_point_supply_status as enum ( 'NotSupplied', 'RegistrationRequested', 'RegistrationAccepted', 'RegistrationObjected', 'RegistrationWithdrawing', 'RegistrationWithdrawRejected', 'RegistrationConfirmed', 'Registered', 'LossNotified', 'LossObjected', 'LossConfirmed');

create table meter_point_history
(
    id             bigint generated by default as identity not null,
    meter_point_id bigint                                  not null,
    event_type     meter_point_event_type                  not null,
    status         meter_point_supply_status               not null,
    deleted        timestamp,
    created_at     timestamp                               not null default current_timestamp,
    updated_at     timestamp                               not null default current_timestamp,

    constraint pk_meter_points_history primary key (id),
    constraint fk_meter_point_id foreign key (meter_point_id) references meter_point (id)

);
