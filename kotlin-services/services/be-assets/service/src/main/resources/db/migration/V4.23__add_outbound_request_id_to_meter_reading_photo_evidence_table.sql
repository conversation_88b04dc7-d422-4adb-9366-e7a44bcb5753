ALTER TABLE IF EXISTS meter_reading_photo_evidence
    ADD COLUMN IF NOT EXISTS outbound_request_id bigint DEFAULT NULL;

ALTER TABLE IF EXISTS meter_reading_photo_evidence
    ADD CONSTRAINT fk_outbound_request_id FOREIGN KEY (outbound_request_id) REFERENCES outbound_photo_request (id);

ALTER TABLE IF EXISTS meter_reading_photo_evidence
    ALTER COLUMN meter_point_id DROP NOT NULL,
    ALTER COLUMN meter_point_id SET DEFAULT NULL,
    ALTER COLUMN meter_point_identifier DROP NOT NULL,
    ALTER COLUMN meter_point_identifier SET DEFAULT NULL,
    ALTER COLUMN submission_date DROP NOT NULL,
    ALTER COLUMN submission_date SET DEFAULT NULL,
    ALTER COLUMN error_code DROP NOT NULL,
    ALTER COLUMN error_code SET DEFAULT NULL;
