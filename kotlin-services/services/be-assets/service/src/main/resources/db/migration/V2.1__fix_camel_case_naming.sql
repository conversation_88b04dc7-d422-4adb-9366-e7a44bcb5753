DROP VIEW IF EXISTS junifer_meter_point_view;
DROP VIEW IF EXISTS junifer_meter_readings;
DROP VIEW IF EXISTS meter_point_view;
DROP VIEW IF EXISTS meter_structure;
DROP TABLE IF EXISTS "junifer__MeterPoint";
CREATE TABLE IF NOT EXISTS "junifer__MeterPoint"
(
    "id"                    bigint NOT NULL primary key,
    "assetFk"               bigint NOT NULL,
    "utilityMarketFk"       bigint NOT NULL,
    "identifier"            text   NOT NULL,
    "timeZoneTblFk"         bigint NOT NULL,
    "billableFl"            text   NOT NULL,
    "parentMeterPointFk"    bigint NULL,
    "meterPointPublishFk"   bigint NULL,
    "lastPublishDttm"       text   NULL,
    "nextPublishDttm"       text   NULL,
    "newConnectionFl"       text   NOT NULL,
    "primaryRelatedMpanFl"  text   NOT NULL,
    "reference"             text   NULL,
    "deleteFl"              text   NOT NULL,
    "versionNo"             int    NOT NULL,
    "partitionId"           bigint NOT NULL
);

ALTER TABLE "junifer__MeterPoint"
ALTER "lastPublishDttm" TYPE TIMESTAMP WITH TIME ZONE USING "lastPublishDttm"::TIMESTAMP WITH TIME ZONE,
    ALTER "nextPublishDttm" TYPE TIMESTAMP WITH TIME ZONE USING "nextPublishDttm"::TIMESTAMP WITH TIME ZONE;

DROP TABLE IF EXISTS "junifer__Mpan";
CREATE TABLE IF NOT EXISTS "junifer__Mpan"
(
    "id"                          bigint NOT NULL primary key,
    "meterPointFk"                bigint NOT NULL,
    "mpanClass"                   text   NOT NULL,
    "msid"                        text   NULL,
    "ukDistributionAreaFk"        bigint NULL,
    "specialNeedsInformation"     text   NULL,
    "changeOfTenancyFl"           text   NOT NULL,
    "measurementType"             text   NOT NULL,
    "balancingMechanismUnitId"    text   NULL,
    "balancingMechanismUnitDfnFk" bigint NULL,
    "soleTradingUnitFl"           text   NOT NULL,
    "reference"                   text   NULL,
    "deleteFl"                    text   NOT NULL,
    "versionNo"                   int    NOT NULL,
    "partitionId"                 bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__MpanConfigPeriod";
CREATE TABLE IF NOT EXISTS "junifer__MpanConfigPeriod"
(
    "id"                         bigint NOT NULL primary key,
    "mpanFk"                     bigint NOT NULL,
    "fromDt"                     text   NOT NULL,
    "toDt"                       text   NOT NULL,
    "ukLineLossFactorClassFk"    bigint NULL,
    "ukMeterTimeswitchClassFk"   bigint NULL,
    "ukProfileClassFk"           bigint NULL,
    "ukGspGroupFk"               bigint NULL,
    "ukMeasurementClassFk"       bigint NULL,
    "ukStdSettlementConfigFk"    bigint NULL,
    "dcMarketParticipantFk"      bigint NULL,
    "dcAppointedBy"              text   NULL,
    "daMarketParticipantFk"      bigint NULL,
    "daAppointedBy"              text   NULL,
    "mopMarketParticipantFk"     bigint NULL,
    "mopAppointedBy"             text   NULL,
    "mapContractTypeFk"          bigint NULL,
    "supMarketParticipantFk"     bigint NULL,
    "mpanReadingFrequencyFk"     bigint NULL,
    "energisedFl"                text   NOT NULL,
    "operationType"              text   NOT NULL,
    "erroneousTransferFl"        text   NOT NULL,
    "amrFl"                      text   NOT NULL,
    "meterPointServiceTypeFk"    bigint NOT NULL,
    "dccServiceStatus"           text   NULL,
    "mpanCodeOfPracticeFk"       bigint NULL,
    "mpanCommunicationMethodFk"  bigint NULL,
    "meterInstallRequiredFl"     text   NOT NULL,
    "businessSectorFk"           bigint NULL,
    "cssRegistrationId"          text   NULL,
    "reference"                  text   NULL,
    "deleteFl"                   text   NOT NULL,
    "versionNo"                  int    NOT NULL,
    "partitionId"                bigint NOT NULL
);

ALTER TABLE "junifer__MpanConfigPeriod"
ALTER "fromDt" TYPE DATE USING "fromDt"::date,
    ALTER "toDt" TYPE DATE USING "toDt"::date;

DROP TABLE IF EXISTS "junifer__UkProfileClass";
CREATE TABLE IF NOT EXISTS "junifer__UkProfileClass"
(
    "id"              bigint NOT NULL primary key,
    "code"            bigint NOT NULL,
    "fromDt"          text   NOT NULL,
    "toDt"            text   NOT NULL,
    "description"     text   NULL,
    "switchedLoadFl"  text   NOT NULL,
    "reference"       text   NULL,
    "deleteFl"        text   NOT NULL,
    "versionNo"       int    NOT NULL,
    "partitionId"     bigint NOT NULL
);

ALTER TABLE "junifer__UkProfileClass"
ALTER "fromDt" TYPE DATE USING "fromDt"::date,
    ALTER "toDt" TYPE DATE USING "toDt"::date;

DROP TABLE IF EXISTS "junifer__UkGspGroup";
CREATE TABLE IF NOT EXISTS "junifer__UkGspGroup"
(
    "id"           bigint NOT NULL primary key,
    "code"         text   NOT NULL,
    "name"         text   NOT NULL,
    "reference"    text   NULL,
    "deleteFl"     text   NOT NULL,
    "versionNo"    int    NOT NULL,
    "partitionId"  bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__MeterPointServiceType";
CREATE TABLE IF NOT EXISTS "junifer__MeterPointServiceType"
(
    "id"               bigint NOT NULL primary key,
    "name"             text   NOT NULL,
    "automatedReadFl"  text   NOT NULL,
    "reference"        text   NULL,
    "deleteFl"         text   NOT NULL,
    "versionNo"        int    NOT NULL,
    "partitionId"      bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__MarketParticipant";
CREATE TABLE IF NOT EXISTS "junifer__MarketParticipant"
(
    "id"               bigint NOT NULL primary key,
    "code"             text   NOT NULL,
    "name"             text   NOT NULL,
    "utilityMarketFk"  bigint NOT NULL,
    "reference"        text   NULL,
    "deleteFl"         text   NOT NULL,
    "versionNo"        int    NOT NULL,
    "partitionId"      bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__UkDistributionArea";
CREATE TABLE IF NOT EXISTS "junifer__UkDistributionArea"
(
    "id"                 bigint NOT NULL primary key,
    "areaId"             bigint NOT NULL,
    "area"               text   NULL,
    "name"               text   NULL,
    "operator"           text   NOT NULL,
    "emergencyNo"        text   NULL,
    "generalEnquiriesNo" text   NULL,
    "contactAddress"     text   NULL,
    "reference"          text   NULL,
    "deleteFl"           text   NOT NULL,
    "versionNo"          int    NOT NULL,
    "partitionId"        bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__MeterPointSupplyPeriod";
CREATE TABLE IF NOT EXISTS "junifer__MeterPointSupplyPeriod"
(
    "id"                          bigint NOT NULL primary key,
    "meterPointFk"                bigint NOT NULL,
    "supplyStartDt"               text   NOT NULL,
    "supplyEndDt"                 text   NOT NULL,
    "status"                      text   NOT NULL,
    "createdDttm"                 text   NOT NULL,
    "gainFromMarketParticipantFk" bigint NULL,
    "lostToMarketParticipantFk"   bigint NULL,
    "erroneousTransferStatus"     text   NULL,
    "reference"                   text   NULL,
    "deleteFl"                    text   NOT NULL,
    "versionNo"                   int    NOT NULL,
    "partitionId"                 bigint NOT NULL
);

ALTER TABLE "junifer__MeterPointSupplyPeriod"
ALTER "supplyStartDt" TYPE DATE USING "supplyStartDt"::date,
    ALTER "supplyEndDt" TYPE DATE USING "supplyEndDt"::date;

ALTER TABLE "junifer__MeterPointSupplyPeriod"
ALTER "createdDttm" TYPE TIMESTAMP WITH TIME ZONE USING "createdDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__Mprn";
CREATE TABLE IF NOT EXISTS "junifer__Mprn"
(
    "id"                         bigint NOT NULL primary key,
    "meterPointFk"               bigint NOT NULL,
    "lastSubmittedReadEventDt"   text   NULL,
    "changeOfTenancyFl"          text   NOT NULL,
    "lastUpdatedSmartCVDt"       text   NULL,
    "reference"                  text   NULL,
    "deleteFl"                   text   NOT NULL,
    "versionNo"                  int    NOT NULL,
    "partitionId"                bigint NOT NULL
);

ALTER TABLE "junifer__Mprn"
ALTER "lastSubmittedReadEventDt" TYPE DATE USING "lastSubmittedReadEventDt"::date,
    ALTER "lastUpdatedSmartCVDt" TYPE DATE USING "lastUpdatedSmartCVDt"::date;

DROP TABLE IF EXISTS "junifer__MprnConfigPeriod";
CREATE TABLE IF NOT EXISTS "junifer__MprnConfigPeriod"
(
    "id"                           bigint NOT NULL primary key,
    "mprnFk"                       bigint NOT NULL,
    "fromDt"                       text   NOT NULL,
    "toDt"                         text   NOT NULL,
    "ukGasExitZoneFk"              bigint NULL,
    "mamMarketParticipantFk"       bigint NULL,
    "ukGspGroupFk"                 bigint NULL,
    "mamAppointedBy"               text   NULL,
    "mraMarketParticipantFk"       bigint NULL,
    "mraAppointedBy"               text   NULL,
    "shipperMarketParticipantFk"   bigint NULL,
    "supMarketParticipantFk"       bigint NULL,
    "sicCodeFk"                    bigint NULL,
    "readingType"                  text   NULL,
    "mprnReadingFrequencyFk"       bigint NULL,
    "connectionStatus"             text   NULL,
    "marketSector"                 text   NULL,
    "nominationShipperReference"   text   NULL,
    "xoserveConfirmationReference" text   NULL,
    "spManned24HoursFl"            text   NOT NULL,
    "operationType"                text   NOT NULL,
    "erroneousTransferFl"          text   NOT NULL,
    "distanceToNtsExit"            text   NULL,
    "distanceToNtsEntry"           text   NULL,
    "directlyNtsConnectedFl"       text   NOT NULL,
    "ldzOptionalTariffFl"          text   NOT NULL,
    "ntsOptionalTariffFl"          text   NOT NULL,
    "amrFl"                        text   NOT NULL,
    "amrAppointedBy"               text   NULL,
    "ukGasSettlementClassFk"       bigint NULL,
    "ukGasCsepFk"                  bigint NULL,
    "dniFl"                        text   NOT NULL,
    "meterFaultStatusFl"           text   NOT NULL,
    "amrMarketParticipantFk"       bigint NULL,
    "lastCheckReadDt"              text   NULL,
    "mprnSupplyPointCategoryFk"    bigint NULL,
    "class3BatchFreq"              text   NULL,
    "meterPointServiceTypeFk"      bigint NOT NULL,
    "dccServiceStatus"             text   NULL,
    "priorityConsumerCategory"     text   NULL,
    "gasActOwner"                  text   NULL,
    "businessSectorFk"             bigint NULL,
    "cssRegistrationId"            text   NULL,
    "reference"                    text   NULL,
    "deleteFl"                     text   NOT NULL,
    "versionNo"                    int    NOT NULL,
    "partitionId"                  bigint NOT NULL
);

ALTER TABLE "junifer__MprnConfigPeriod"
ALTER "fromDt" TYPE DATE USING "fromDt"::date,
    ALTER "toDt" TYPE DATE USING "toDt"::date,
    ALTER "lastCheckReadDt" TYPE DATE USING "lastCheckReadDt"::date,
    ALTER "distanceToNtsExit" TYPE NUMERIC USING "distanceToNtsExit"::numeric,
    ALTER "distanceToNtsEntry" TYPE NUMERIC USING "distanceToNtsEntry"::numeric;

DROP TABLE IF EXISTS "junifer__ProductPropertyAsset";
CREATE TABLE IF NOT EXISTS "junifer__ProductPropertyAsset"
(
    "id"                 bigint NOT NULL primary key,
    "productFk"          bigint NOT NULL,
    "createdDttm"        text   NOT NULL,
    "createdUserTblFk"   bigint NOT NULL,
    "fromDttm"           text   NOT NULL,
    "toDttm"             text   NOT NULL,
    "propertyTblFk"      bigint NULL,
    "assetFk"            bigint NULL,
    "cancelFl"           text   NOT NULL,
    "cancelledDttm"      text   NULL,
    "cancelledUserTblFk" bigint NULL,
    "reference"          text   NULL,
    "deleteFl"           text   NOT NULL,
    "versionNo"          int    NOT NULL,
    "partitionId"        bigint NOT NULL
);

ALTER TABLE "junifer__ProductPropertyAsset"
ALTER "createdDttm" TYPE TIMESTAMP WITH TIME ZONE USING "createdDttm"::timestamp with time zone,
    ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone,
    ALTER "cancelledDttm" TYPE TIMESTAMP WITH TIME ZONE USING "cancelledDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__Product";
CREATE TABLE IF NOT EXISTS "junifer__Product"
(
    "id"                 bigint NOT NULL primary key,
    "productBundleFk"    bigint NOT NULL,
    "productDfnFk"       bigint NOT NULL,
    "productSubTypeFk"   bigint NOT NULL,
    "name"               text   NULL,
    "fromDttm"           text   NULL,
    "toDttm"             text   NULL,
    "salesTaxFk"         bigint NULL,
    "reference"          text   NULL,
    "deleteFl"           text   NOT NULL,
    "versionNo"          int    NOT NULL,
    "partitionId"        bigint NOT NULL
);

ALTER TABLE "junifer__Product"
ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__ProductBundle";
CREATE TABLE IF NOT EXISTS "junifer__ProductBundle"
(
    "id"                         bigint NOT NULL primary key,
    "productBundleDfnFk"         bigint NOT NULL,
    "agreementTypeFk"            bigint NOT NULL,
    "accountFk"                  bigint NULL,
    "purchasingGroupFk"          bigint NULL,
    "brokerAgentFk"              bigint NULL,
    "name"                       text   NULL,
    "number"                     text   NULL,
    "createdDttm"                text   NOT NULL,
    createdUserTblFk             bigint NOT NULL,
    "orderDttm"                  text   NULL,
    "fromDttm"                   text   NULL,
    "toDttm"                     text   NULL,
    "contractedToDttm"           text   NULL,
    "followOnFl"                 text   NOT NULL,
    "followOnDttm"               text   NULL,
    "followOnProductBundleDfnFk" bigint NULL,
    "terminationReceivedDt"      date   NULL,
    "productTerminationReasonFk" bigint NULL,
    "currencyFk"                 bigint NOT NULL,
    "cancelFl"                   text   NOT NULL,
    "cancelledDttm"              text   NULL,
    "cancelledUserTblFk"         bigint NULL,
    "billableFl"                 text   NOT NULL,
    "reference"                  text   NULL,
    "deleteFl"                   text   NOT NULL,
    "versionNo"                  int    NOT NULL,
    "partitionId"                bigint NOT NULL
);

ALTER TABLE "junifer__ProductBundle"
ALTER "createdDttm" TYPE TIMESTAMP WITH TIME ZONE USING "createdDttm"::timestamp with time zone,
    ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone,
    ALTER "orderDttm" TYPE TIMESTAMP WITH TIME ZONE USING "orderDttm"::timestamp with time zone,
    ALTER "contractedToDttm" TYPE TIMESTAMP WITH TIME ZONE USING "contractedToDttm"::timestamp with time zone,
    ALTER "followOnDttm" TYPE TIMESTAMP WITH TIME ZONE USING "followOnDttm"::timestamp with time zone,
    ALTER "cancelledDttm" TYPE TIMESTAMP WITH TIME ZONE USING "cancelledDttm"::timestamp with time zone;

CREATE OR REPLACE VIEW METER_POINT_VIEW AS
SELECT mp."id",
       mp."identifier",
       'MPAN'                 as type,
       mpsp."supplyStartDt"   as supplyStartDate,
       mpan."changeOfTenancyFl",
       upc."code"             as ukProfileClass,
       ugg."code"             as ukGspGroup,
       mcp."operationType",
       mpst."name"            as meterPointServiceType,
       null                   as mamMarketParticipant,
       mp2."name"             as mopMarketParticipant,
       mcp."dccServiceStatus",
       uda."operator"         as DNO,
       mpan."measurementType",
       mpsp."status"          as supplyStatus,
       pb."accountFk"         as accountId
FROM "junifer__MeterPoint" mp
         INNER JOIN "junifer__Mpan" mpan ON
        mpan."meterPointFk" = mp."id"
         LEFT JOIN "junifer__MpanConfigPeriod" mcp ON
        mcp."mpanFk" = mpan."id"
         INNER JOIN "junifer__UkProfileClass" upc ON
        upc."id" = mcp."ukProfileClassFk"
         INNER JOIN "junifer__UkGspGroup" ugg ON
        ugg."id" = mcp."ukGspGroupFk"
         INNER JOIN "junifer__ProductPropertyAsset" ppa ON ppa."assetFk" = mp."assetFk"
         INNER JOIN "junifer__Product" AS p
                    ON
                            ppa."productFk" = p."id"
         INNER JOIN "junifer__ProductBundle" AS pb
                    ON
                            p."productBundleFk" = pb."id"
         INNER JOIN "junifer__MeterPointServiceType" mpst ON
        mpst."id" = mcp."meterPointServiceTypeFk"
         INNER JOIN "junifer__MarketParticipant" mp2 ON
        mp2."id" = mcp."mopMarketParticipantFk"
         INNER JOIN "junifer__UkDistributionArea" uda ON
        uda."id" = mpan."ukDistributionAreaFk"
         LEFT JOIN "junifer__MeterPointSupplyPeriod" mpsp ON
        mpsp."meterPointFk" = mp."id"

UNION ALL

SELECT mp."id",
       mp."identifier",
       'MPRN'               as type,
       mpsp."supplyStartDt" as supplyStartDate,
       mprn."changeOfTenancyFl",
       null                 as ukProfileClass,
       ugg."code"             as ukGspGroup,
       mcp."operationType",
       mpst."name"            as meterPointServiceType,
       mp2."name"             as mamMarketParticipant,
       null                 as mopMarketParticipant,
       mcp."dccServiceStatus",
       null                 as DNO,
       null                 as measurementType,
       mpsp."status"          as supplyStatus,
       pb."accountFk"       as accountId
FROM "junifer__MeterPoint" mp
         INNER JOIN "junifer__Mprn" mprn ON
        mprn."meterPointFk" = mp."id"
         LEFT JOIN "junifer__MprnConfigPeriod" mcp ON
        mcp."mprnFk" = mprn."id"
         INNER JOIN "junifer__UkGspGroup" ugg ON
        ugg."id" = mcp."ukGspGroupFk"
         INNER JOIN "junifer__ProductPropertyAsset" ppa ON ppa."assetFk" = mp."assetFk"
         INNER JOIN "junifer__Product" AS p
                    ON
                            ppa."productFk" = p."id"
         INNER JOIN "junifer__ProductBundle" AS pb
                    ON
                            p."productBundleFk" = pb."id"
         INNER JOIN "junifer__MeterPointServiceType" mpst ON
        mpst."id" = mcp."meterPointServiceTypeFk"
         INNER JOIN "junifer__MarketParticipant" mp2 ON
        mp2."id" = mcp."mamMarketParticipantFk"
         LEFT JOIN "junifer__MeterPointSupplyPeriod" mpsp ON
        mpsp."meterPointFk" = mp."id";

DROP TABLE IF EXISTS "junifer__MeterPointTimeSeries";
CREATE TABLE IF NOT EXISTS "junifer__MeterPointTimeSeries"
(
    "id"                             bigint NOT NULL primary key,
    "meterPointFk"                   bigint NOT NULL,
    identifier                       text   NULL,
    "toDttm"                         text   NOT NULL,
    "fromDttm"                       text   NOT NULL,
    "continueMeterPointTimeSeriesFk" bigint NULL,
    "virtualFl"                      text   NOT NULL,
    "reference"                      text   NULL,
    "deleteFl"                       text   NOT NULL,
    "versionNo"                      int    NOT NULL,
    "partitionId"                    bigint NOT NULL
);

ALTER TABLE "junifer__MeterPointTimeSeries"
ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__MeterPointPhyTimeSeries";
CREATE TABLE IF NOT EXISTS "junifer__MeterPointPhyTimeSeries"
(
    "id"                            bigint NOT NULL primary key,
    "meterPointTimeSeriesFk"        bigint NOT NULL,
    "meterRegisterConfigPeriodFk"   bigint NOT NULL,
    "reference"                     text   NULL,
    "deleteFl"                      text   NOT NULL,
    "versionNo"                     int    NOT NULL,
    "partitionId"                   bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__MeterRegisterConfigPeriod";
CREATE TABLE IF NOT EXISTS "junifer__MeterRegisterConfigPeriod"
(
    "id"                     bigint NOT NULL primary key,
    "meterRegisterFk"        bigint NOT NULL,
    "fromDttm"               text   NOT NULL,
    "toDttm"                 text   NOT NULL,
    "meterReadingConfigFk"   bigint NOT NULL,
    "meterReadingType"       text   NOT NULL,
    "meterMeasurementTypeFk" bigint NOT NULL,
    "meterRegisterTypeFk"    bigint NULL,
    "metricUnitFk"           bigint NOT NULL,
    "coefficient"            text   NOT NULL,
    "multiplier"             text   NULL,
    "digits"                 int    NOT NULL,
    "decimalPlaces"          int    NOT NULL,
    "correctionFactor"       text   NULL,
    "readingMode"            text   NOT NULL,
    "reference"              text   NULL,
    "deleteFl"               text   NOT NULL,
    "versionNo"              int    NOT NULL,
    "partitionId"            bigint NOT NULL
);

ALTER TABLE "junifer__MeterRegisterConfigPeriod"
ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone,
    ALTER "multiplier" TYPE NUMERIC USING "multiplier"::numeric,
    ALTER "correctionFactor" TYPE NUMERIC USING "correctionFactor"::numeric;

DROP TABLE IF EXISTS "junifer__MeterRegister";
CREATE TABLE IF NOT EXISTS "junifer__MeterRegister"
(
    "id"                 bigint NOT NULL primary key,
    "meterFk"            bigint NOT NULL,
    "identifier"         text   NULL,
    "externalIdentifier" text   NULL,
    "fromDttm"           text   NOT NULL,
    "toDttm"             text   NOT NULL,
    "reference"          text   NULL,
    "deleteFl"           text   NOT NULL,
    "versionNo"          int    NOT NULL,
    "partitionId"        bigint NOT NULL
);

ALTER TABLE "junifer__MeterRegister"
ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "toDttm" TYPE TIMESTAMP WITH TIME ZONE USING "toDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__Meter";
CREATE TABLE IF NOT EXISTS "junifer__Meter"
(
    "id"                 bigint NOT NULL primary key,
    "utilityMarketFk"    bigint NOT NULL,
    "identifier"         text   NOT NULL,
    "timeZoneTblFk"      bigint NOT NULL,
    "location"           text   NULL,
    "locationCode"       text   NULL,
    "meterTypeFk"        bigint NULL,
    "manufacturer"       text   NULL,
    "model"              text   NULL,
    "manufacturedYear"   int    NULL,
    "lastInspectionDt"   text   NULL,
    "reference"          text   NULL,
    "deleteFl"           text   NOT NULL,
    "versionNo"          int    NOT NULL,
    "partitionId"        bigint NOT NULL
);

ALTER TABLE "junifer__Meter"
ALTER "lastInspectionDt" TYPE DATE USING "lastInspectionDt"::date;

DROP TABLE IF EXISTS "junifer__MeterType";
CREATE TABLE IF NOT EXISTS "junifer__MeterType"
(
    "id"              bigint NOT NULL primary key,
    "code"            text   NOT NULL,
    "description"     text   NULL,
    "utilityMarketFk" bigint NOT NULL,
    "prepayFl"        text   NOT NULL,
    "deviceClass"     text   NOT NULL,
    "reference"       text   NULL,
    "deleteFl"        text   NOT NULL,
    "versionNo"       int    NOT NULL,
    "partitionId"     bigint NOT NULL
);


DROP TABLE IF EXISTS "junifer__MeterRegisterType";
CREATE TABLE IF NOT EXISTS "junifer__MeterRegisterType"
(
    "id"              bigint NOT NULL primary key,
    "utilityMarketFk" bigint NOT NULL,
    "name"            text   NOT NULL,
    "code"            text   NOT NULL,
    "reference"       text   NULL,
    "deleteFl"        text   NOT NULL,
    "versionNo"       int    NOT NULL,
    "partitionId"     bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__UkTimePatternRegime";
CREATE TABLE IF NOT EXISTS "junifer__UkTimePatternRegime"
(
    "id"                    bigint NOT NULL primary key,
    "regimeId"              text   NOT NULL,
    "meterRegisterTypeFk"   bigint NULL,
    "gmtIndicatorFl"        text   NOT NULL,
    "teleswitchClock"       text   NOT NULL,
    "reference"             text   NULL,
    "deleteFl"              text   NOT NULL,
    "versionNo"             int    NOT NULL,
    "partitionId"           bigint NOT NULL
);

DROP TABLE IF EXISTS "junifer__UkTprMapping";
CREATE TABLE IF NOT EXISTS "junifer__UkTprMapping"
(
    "id"                      bigint NOT NULL primary key,
    "ukTimePatternRegimeFk"   bigint NOT NULL,
    "ukStdSettlementConfigFk" bigint NOT NULL,
    "ukTprMappingSetFk"       bigint NOT NULL,
    "fromDt"                  text   NOT NULL,
    "toDt"                    text   NOT NULL,
    "rateNameFk"              bigint NOT NULL,
    "reference"               text   NULL,
    "deleteFl"                text   NOT NULL,
    "versionNo"               int    NOT NULL,
    "partitionId"             bigint NOT NULL
);

ALTER TABLE "junifer__UkTprMapping"
ALTER "fromDt" TYPE DATE USING "fromDt"::date,
    ALTER "toDt" TYPE DATE USING "toDt"::date;

DROP TABLE IF EXISTS "junifer__RateName";
CREATE TABLE IF NOT EXISTS "junifer__RateName"
(
    "id"              bigint NOT NULL primary key,
    "rateNameGroupFk" bigint NOT NULL,
    "name"            text   NOT NULL,
    "code"            text   NOT NULL,
    "colour"          text   NOT NULL,
    "orderNo"         int    NOT NULL,
    "reference"       text   NULL,
    "deleteFl"        text   NOT NULL,
    "versionNo"       int    NOT NULL,
    "partitionId"     bigint NOT NULL
);

CREATE OR REPLACE VIEW meter_structure AS
SELECT m.id,
       m.identifier,
       mt.code,
       mt.description,
       mrt.name,
       mrcp.digits,
       mrcp."decimalPlaces",
       mpts."meterPointFk" as meterPointId
FROM "junifer__MeterPointTimeSeries" mpts
         INNER join "junifer__MeterPointPhyTimeSeries" mppts ON
        mppts."meterPointTimeSeriesFk" = mpts.id
         INNER join "junifer__MeterRegisterConfigPeriod" mrcp ON
        mrcp.id = mppts."meterRegisterConfigPeriodFk"
         LEFT JOIN "junifer__MeterRegister" mr ON
        mr."meterFk" = mrcp."meterRegisterFk"
         INNER join "junifer__Meter" m ON
        m.id = mr."meterFk"
         INNER JOIN "junifer__MeterType" mt ON
        mt.id = m."meterTypeFk"
         INNER JOIN "junifer__MeterRegisterType" mrt ON
        mrt.id = mr.id
         LEFT OUTER JOIN "junifer__UkTimePatternRegime" utpm ON
        utpm."meterRegisterTypeFk" = mrt.id
         LEFT OUTER JOIN "junifer__UkTprMapping" utm ON
        utm."ukTimePatternRegimeFk" = utpm.id
         LEFT OUTER JOIN "junifer__RateName" rn ON
        rn.id = utm."rateNameFk";

DROP TABLE IF EXISTS "junifer__MeterReadingManual";
CREATE TABLE IF NOT EXISTS "junifer__MeterReadingManual"
(
    "id"                        bigint not null primary key,
    "meterPointFk"              bigint,
    "meterFk"                   bigint,
    "timeSeriesSourceFk"        bigint,
    "timeSeriesQualityFk"       bigint,
    "description"               text,
    "enteredDttm"               text,
    "enteredUserTblFk"          bigint,
    "suppressIndustryPublishFl" text,
    "reference"                 text,
    "deleteFl"                  text,
    "versionNo"                 integer,
    "partitionId"               bigint
);

ALTER TABLE "junifer__MeterReadingManual"
ALTER "enteredDttm" TYPE TIMESTAMP WITH TIME ZONE USING "enteredDttm"::timestamp with time zone;

DROP TABLE IF EXISTS "junifer__TimeSeriesSource";
CREATE TABLE IF NOT EXISTS "junifer__TimeSeriesSource"
(
    "id"                bigint not null primary key,
    "code"              text,
    "internalKey"       text,
    "languageKey"       text,
    "iconTblFk"         bigint,
    "userEnterableFl"   text,
    "reference"         text,
    "deleteFl"          text,
    "versionNo"         integer,
    "partitionId"       bigint
);

DROP TABLE IF EXISTS "junifer__TimeSeriesQuality";
CREATE TABLE IF NOT EXISTS "junifer__TimeSeriesQuality"
(
    "id"            bigint not null primary key,
    "orderNo"       integer,
    "internalKey"   text,
    "languageKey"   text,
    "code"          text,
    "qualityType"   text,
    "defaultFl"     text,
    "colour"        text,
    "iconTblFk"     bigint,
    "reference"     text,
    "deleteFl"      text,
    "versionNo"     integer,
    "partitionId"   bigint
);

DROP TABLE IF EXISTS "junifer__MetricUnit";
CREATE TABLE IF NOT EXISTS "junifer__MetricUnit"
(
    "id"                 bigint not null primary key,
    "metricUnitTypeFk"   bigint,
    "code"               text,
    "display"            text,
    "reference"          text,
    "deleteFl"           text,
    "versionNo"          integer,
    "partitionId"        bigint
);


DROP TABLE IF EXISTS "junifer__ReadingWorkflowStatus";
CREATE TABLE IF NOT EXISTS "junifer__ReadingWorkflowStatus"
(
    "id"          bigint not null,
    "internalKey" text,
    "languageKey" text,
    "code"        text,
    "iconTblFk"   bigint,
    "reference"   text,
    "deleteFl"    text,
    "versionNo"   integer,
    "partitionId" bigint
);

DROP TABLE IF EXISTS "junifer__UtilityTimeSeriesEvent";
CREATE TABLE IF NOT EXISTS "junifer__UtilityTimeSeriesEvent"
(
    "id"                          bigint not null primary key,
    "taskDfnGroupFk"              bigint,
    "batchId"                     integer,
    "eventSourceFk"               bigint,
    "eventSourceIdentifier"       text,
    "eventId"                     bigint,
    "receivedDttm"                text,
    "meterPointFk"                bigint,
    "meterPointTimeSeriesFk"      bigint,
    "meterFk"                     bigint,
    "meterRegisterFk"             bigint,
    "meterMeasurementTypeFk"      bigint,
    "meterReadingManualFk"        bigint,
    "utilityMarketFk"             bigint,
    "timeSeriesSourceFk"          bigint,
    "timeSeriesQualityFk"         bigint,
    "sequenceType"                text,
    "rawFromDttm"                 text,
    "rawEventDttm"                text,
    "rawTimeZoneTblFk"            bigint,
    "rawConsumption"              text,
    "rawCumulative"               text,
    "rawMetricUnitFk"             bigint,
    "fromDttm"                    text,
    "eventDttm"                   text,
    "timeZoneTblFk"               bigint,
    "consumption"                 text,
    "cumulative"                  text,
    "metricUnitFk"                bigint,
    "reportConsumption"           text,
    "reportCumulative"            text,
    "reportMetricUnitFk"          bigint,
    "status"                      text,
    "timeSeriesEventReasonFk"     bigint,
    "validationFailureInfo"       text,
    "readingWorkflowStatusFk"     bigint,
    "industryInvalidityReason"    text,
    "suppressPublishToIndustryFl" text,
    "dataPartitionId"             bigint,
    "lastStatusUpdateDttm"        text
);

ALTER TABLE "junifer__UtilityTimeSeriesEvent"
ALTER "receivedDttm" TYPE TIMESTAMP WITH TIME ZONE USING "receivedDttm"::timestamp with time zone,
    ALTER "rawFromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "rawFromDttm"::timestamp with time zone,
    ALTER "rawEventDttm" TYPE TIMESTAMP WITH TIME ZONE USING "rawEventDttm"::timestamp with time zone,
    ALTER "fromDttm" TYPE TIMESTAMP WITH TIME ZONE USING "fromDttm"::timestamp with time zone,
    ALTER "eventDttm" TYPE TIMESTAMP WITH TIME ZONE USING "eventDttm"::timestamp with time zone,
    ALTER "lastStatusUpdateDttm" TYPE TIMESTAMP WITH TIME ZONE USING "lastStatusUpdateDttm"::timestamp with time zone,
    ALTER "consumption" TYPE NUMERIC USING "consumption"::numeric,
    ALTER "cumulative" TYPE NUMERIC USING "cumulative"::numeric,
    ALTER "rawConsumption" TYPE NUMERIC USING "rawConsumption"::numeric,
    ALTER "rawCumulative" TYPE NUMERIC USING "rawCumulative"::numeric,
    ALTER "reportConsumption" TYPE NUMERIC USING "reportConsumption"::numeric,
    ALTER "reportCumulative" TYPE NUMERIC USING "reportCumulative"::numeric;

CREATE UNIQUE INDEX IF NOT EXISTS "junifer__UtilityTimeSeriesEventSs1"
    ON "junifer__UtilityTimeSeriesEvent" ("meterPointFk", "eventDttm", "eventId");

CREATE OR REPLACE VIEW junifer_meter_readings AS
SELECT utse.id                         AS id,
       utse."eventDttm"                AS readingDttm,
       utse.status                     AS status,
       utse."sequenceType"             AS sequenceType,
       tss.code                        AS source,
       tsq.code                        AS quality,
       utse.cumulative                 AS cumulative,
       utse.consumption                AS consumption,
       mu.code                         AS unit,
       utse."receivedDttm"             AS receivedDttm,
       utse."fromDttm"                 AS fromDttm,
       m.identifier                    AS meter,
       m.id                            AS meterId,
       mr.identifier                   AS register,
       mr.id                           AS meterRegisterId,
       rws.code                        AS workflowStatus,
       utse."industryInvalidityReason" AS invalidityReason,
       rn.name                         AS rateName,
       utse."meterPointFk"             AS meterPointId
FROM "junifer__UtilityTimeSeriesEvent" utse
         INNER JOIN "junifer__TimeSeriesSource" tss ON tss.id = utse."timeSeriesSourceFk" AND
                                                       tss."deleteFl" = 'N'
         INNER JOIN "junifer__TimeSeriesQuality" tsq ON tsq.id = utse."timeSeriesQualityFk" AND
                                                        tsq."deleteFl" = 'N'
         INNER JOIN "junifer__MetricUnit" mu ON utse."metricUnitFk" = mu.id AND
                                                mu."deleteFl" = 'N'
         INNER JOIN "junifer__MeterPoint" mp ON utse."meterPointFk" = mp.id AND
                                                mp."deleteFl" = 'N'
         INNER JOIN "junifer__MeterRegister" mr ON utse."meterRegisterFk" = mr.id AND
                                                   mr."deleteFl" = 'N'
         INNER JOIN "junifer__Meter" m ON utse."meterFk" = m.id AND
                                          m."deleteFl" = 'N'
         INNER JOIN "junifer__MeterRegisterConfigPeriod" mrcp ON mrcp."meterRegisterFk" = mr.id AND
                                                                 utse."eventDttm" > mrcp."fromDttm" AND
                                                                 utse."eventDttm" <= mrcp."toDttm" AND
                                                                 mrcp."deleteFl" = 'N'
         INNER JOIN "junifer__MeterRegisterType" mrt ON mrt.id = mrcp."meterRegisterTypeFk" AND
                                                        mrt."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__Mpan" mpan ON mpan."meterPointFk" = mp.id AND
                                                 mpan."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__MpanConfigPeriod" mpancp ON mpancp."mpanFk" = mpan.id AND
                                                               utse."eventDttm" > mpancp."fromDt" AND
                                                               utse."eventDttm" <= mpancp."toDt" AND
                                                               mpancp."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__UkTimePatternRegime" utpm ON utpm."meterRegisterTypeFk" = mrt.id AND
                                                                utpm."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__UkTprMapping" utm ON utm."ukTimePatternRegimeFk" = utpm.id AND
                                                        utm."ukStdSettlementConfigFk" =
                                                        mpancp."ukStdSettlementConfigFk" AND
                                                        utm."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__RateName" rn ON rn.id = utm."rateNameFk" AND
                                                   rn."deleteFl" = 'N'
         LEFT OUTER JOIN "junifer__ReadingWorkflowStatus" rws ON utse."readingWorkflowStatusFk" = rws.id AND
                                                                 rws."deleteFl" = 'N';

CREATE INDEX IF NOT EXISTS "junifer__UkTprMappingAk"
    ON "junifer__UkTprMapping" ("ukTimePatternRegimeFk", "ukStdSettlementConfigFk", "ukTprMappingSetFk", "fromDt");

CREATE INDEX IF NOT EXISTS "junifer__UkTimePatternRegimeSs1"
    ON "junifer__UkTimePatternRegime" ("meterRegisterTypeFk");


-- A view used for validation, which presents information about meters / meter points outside the context of a meter reading.
CREATE OR REPLACE VIEW junifer_meter_point_view AS
select jMP.id           as meter_point_id,
       jM.id            as meter_id,
       jMR.id           as meter_register_id,
       jMR."fromDttm"   as meter_register_from,
       jMR."toDttm"     as meter_register_to,
       jMRCP.id         as meter_register_config_period_id,
       jMRCP."fromDttm" as meter_register_config_period_from,
       jMRCP."toDttm"   as meter_register_config_period_to,
       jMU.code         as meter_unit
from "junifer__MeterPoint" jMP
         inner join "junifer__MeterPointTimeSeries" jMPTS
                    on jMPTS."meterPointFk" = jMP.id
                        and jMP."deleteFl" = 'N'
         inner join "junifer__MeterPointPhyTimeSeries" jMPPTS
                    on jMPPTS."meterPointTimeSeriesFk" = jMPTS.id
                        and jMPPTS."deleteFl" = 'N'
         inner join "junifer__MeterRegisterConfigPeriod" jMRCP
                    on jMRCP.id = jMPPTS."meterRegisterConfigPeriodFk"
                        and jMRCP."deleteFl" = 'N'
         inner join "junifer__MetricUnit" jMU
                    on jMU.id = JMRCP."metricUnitFk"
                        and jMU."deleteFl" = 'N'
         inner join "junifer__MeterRegisterType" jMRT
                    on jMRT.id = jMRCP."meterRegisterTypeFk"
                        and jMRT."deleteFl" = 'N'
         inner join "junifer__MeterRegister" jMR
                    on jMR.id = jMRCP."meterRegisterFk"
                        and jMR."deleteFl" = 'N'
         inner join "junifer__Meter" jM
                    on jM.id = jMR."meterFk"
                        and jM."deleteFl" = 'N'
where jMP."deleteFl" = 'N';

drop index if exists "junifer__MeterRegisterConfigPeriodAk";
drop index if exists "junifer__MpanConfigPeriodSs1";
drop index if exists "junifer__MprnConfigPeriodSs1";
drop index if exists "junifer__MprnSs1";
drop index if exists "junifer__MpanSs1";

create index if not exists "junifer__MeterRegisterConfigPeriodAk"
    on "junifer__MeterRegisterConfigPeriod" ("meterRegisterFk", "fromDttm");

create index if not exists "junifer__MpanConfigPeriodSs1"
    on "junifer__MpanConfigPeriod" ("mpanFk", "fromDt", "toDt");

create index if not exists "junifer__MprnConfigPeriodSs1"
    on "junifer__MprnConfigPeriod" ("mprnFk", "fromDt");

create index if not exists "junifer__MprnSs1"
    on "junifer__Mprn" ("meterPointFk");

create index if not exists "junifer__MpanSs1"
    on "junifer__Mpan" ("meterPointFk");

ALTER TABLE "junifer__ReadingWorkflowStatus" ADD PRIMARY KEY (id);