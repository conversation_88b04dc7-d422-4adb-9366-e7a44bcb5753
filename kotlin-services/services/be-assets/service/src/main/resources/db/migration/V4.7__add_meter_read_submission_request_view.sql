--indexes
create index if not exists ix_meter_reading_submission_request_meterpoint_id on meter_reading_submission_request (meterpoint_id);
create index if not exists ix_meter_reading_submission_request_created_at on meter_reading_submission_request (created_at);
create index if not exists ix_meter_reading_submission_request_status on meter_reading_submission_request (status);

--last errored submissions view
drop view if exists last_errored_submissions;
create view last_errored_submissions as
(
with subquery as (select meterpoint_id, max(created_at) as maxCreatedAt
                  from meter_reading_submission_request
                  group by meterpoint_id)
select mrsr.*
from meter_reading_submission_request mrsr
         inner join subquery
                    on mrsr.meterpoint_id = subquery.meterpoint_id and mrsr.created_at = subquery.maxCreatedAt and
                       mrsr.status = 'ERRORED');
