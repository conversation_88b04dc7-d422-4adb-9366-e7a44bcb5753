CREATE OR REPLACE FUNCTION set_updated_at_function()
    <PERSON><PERSON><PERSON>NS TRIGGER AS
$$
BEGIN
    IF NEW.updated_at = OLD.updated_at THEN
        NEW.updated_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

DO
$$
    DECLARE
        t text;
    BEGIN
        FOR t IN
            SELECT table_name
            FROM information_schema.columns
            WHERE column_name = 'updated_at'
            LOOP
                EXECUTE format('DROP TRIGGER IF EXISTS set_updatedAt_%I ON %I', t, t);
            END loop;
    END ;
$$ language 'plpgsql';

DO
$$
    DECLARE
        t text;
    BEGIN
        FOR t IN
            SELECT table_name
            FROM information_schema.columns
            WHERE column_name = 'updated_at'
            LOOP

                EXECUTE format('CREATE TRIGGER set_updatedAt_%I
                        BEFORE UPDATE ON %I
                        FOR EACH ROW EXECUTE PROCEDURE set_updated_at_function()',
                               t, t);
            END loop;
    END;
$$ language 'plpgsql';
