CREATE TABLE IF NOT EXISTS sync_status
(
    id             bigint generated by default as identity not null primary key,
    entity_id      bigint                                  not null,
    transaction_id bigint                                  not null,
    table_name     varchar(255)                            not null
);

CREATE UNIQUE INDEX if not exists sync_status_table_transaction_id_index on sync_status (table_name, transaction_id);


