ALTER TABLE IF EXISTS meter_point
    ADD COLUMN IF NOT EXISTS from_dt date;
ALTER TABLE IF EXISTS meter_point
    ADD COLUMN IF NOT EXISTS to_dt date;

UPDATE meter_point
SET from_dt = current_timestamp
WHERE from_dt IS NULL;

UPDATE meter_point
SET to_dt = current_timestamp
WHERE to_dt IS NULL;

ALTER TABLE IF EXISTS meter_point
    ALTER COLUMN from_dt SET NOT NULL;
ALTER TABLE IF EXISTS meter_point
    ALTER COLUMN to_dt SET NOT NULL;
