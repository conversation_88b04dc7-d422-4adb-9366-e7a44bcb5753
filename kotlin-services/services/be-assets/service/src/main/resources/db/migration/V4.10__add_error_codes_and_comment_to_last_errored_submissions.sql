alter table if exists meter_reading_submission_request
    add column if not exists comment varchar(255);

--last errored submissions view
drop view if exists last_errored_submissions;
create view last_errored_submissions as
(
with subquery as (select meterpoint_id, max(created_at) as maxCreatedAt
                  from meter_reading_submission_request
                  group by meterpoint_id)
select mrsr.*, debug_data -> 0 ->> 'errorCode' as error_code_1, debug_data -> 1 ->> 'errorCode' as error_code_2
from meter_reading_submission_request mrsr
    inner join subquery
on mrsr.meterpoint_id = subquery.meterpoint_id and mrsr.created_at = subquery.maxCreatedAt and
    mrsr.status = 'ERRORED');
