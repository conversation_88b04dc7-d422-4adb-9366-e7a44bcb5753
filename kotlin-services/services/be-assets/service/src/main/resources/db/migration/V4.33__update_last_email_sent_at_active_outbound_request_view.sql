drop view active_outbound_photo_request;

create or replace view active_outbound_photo_request as
select *,
       (case
            when emails_sent_at -> 'second_follow_up_sent_at' is not null
                then (emails_sent_at -> 'second_follow_up_sent_at')::varchar::date
            when emails_sent_at -> 'first_follow_up_sent_at' is not null
                then (emails_sent_at -> 'first_follow_up_sent_at')::varchar::date
            else (emails_sent_at -> 'initial_email_sent_at')::varchar::date end) as last_email_sent_at
from outbound_photo_request
where status not in ('CANCELLED', 'UPLOADED')
  and deleted is null;
