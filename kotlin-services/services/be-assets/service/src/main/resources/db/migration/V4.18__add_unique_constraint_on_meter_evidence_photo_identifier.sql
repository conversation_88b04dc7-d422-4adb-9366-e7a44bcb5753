--cleanup duplicates
CREATE
OR REPLACE FUNCTION cleanup_photo_evidence(img_identifier varchar)
    RETURNS VOID AS
$$
DECLARE
ids bigint[];
    min_id
bigint;
BEGIN
select array_agg(id)
into ids
from meter_reading_photo_evidence
where image_identifier = img_identifier;

SELECT MIN(val)
INTO min_id
FROM unnest(ids) AS val;

delete
from meter_reading_photo_evidence
where id = any (array_remove(ids, min_id));

END;
$$
LANGUAGE plpgsql;

--
CREATE
OR REPLACE FUNCTION cleanup_evidences()
    RETURNS VOID AS
$$
DECLARE
row_record record;
BEGIN
for row_record in

                 (select distinct p1.image_identifier
                  from meter_reading_photo_evidence p1
                           inner join meter_reading_photo_evidence p2 on p1.id < p2.id and p1.image_identifier = p2.image_identifier)
        loop
            perform cleanup_photo_evidence(row_record.image_identifier);
end loop;

END;
$$
LANGUAGE plpgsql;

select cleanup_evidences();

drop function cleanup_evidences();
drop function cleanup_photo_evidence(img_identifier varchar);

--add uq image identifier index
ALTER TABLE IF EXISTS meter_reading_photo_evidence
drop constraint if exists uq_photo_evidence_image_identifer;

ALTER TABLE IF EXISTS meter_reading_photo_evidence
    add constraint uq_photo_evidence_image_identifer unique (image_identifier);
