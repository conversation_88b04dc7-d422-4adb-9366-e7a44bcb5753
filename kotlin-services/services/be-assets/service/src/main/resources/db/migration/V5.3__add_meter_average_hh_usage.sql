CREATE TABLE IF NOT EXISTS meter_reading_average_hh_usage
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT pk_meter_reading_hh_usage PRIMARY KEY,
    meter_point_id BIGINT                              NOT NULL,
    average_usage  DOUBLE PRECISION[48]                NOT NULL,
    unit           meter_reading_unit                  NOT NULL,
    deleted        TIMESTAMP WITH TIME ZONE,
    created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT average_usage_length
        CHECK (array_length(average_usage, 1) = 48)
);