CREATE TABLE IF NOT EXISTS meter_reading_photo_evidence(
    id                  bigint generated by default as identity
        constraint pk_meter_read_photo_evidence primary key,
    account_number      varchar(255)                            not null,
    meter_point_id      bigint                                  not null,
    submission_date     timestamp,
    image_identifier    varchar(255)                            not null,
    error_code          varchar(255)                            not null,
    deleted             timestamp,
    created_at          timestamp default CURRENT_TIMESTAMP     not null,
    updated_at          timestamp default CURRENT_TIMESTAMP     not null
)
