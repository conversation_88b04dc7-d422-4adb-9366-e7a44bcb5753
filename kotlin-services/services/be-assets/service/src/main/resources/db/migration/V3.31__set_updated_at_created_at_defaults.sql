DO
$$
    DECLARE
        t text;
    <PERSON><PERSON>IN
        FOR t IN
            SELECT table_name
            FROM information_schema.columns
            WHERE column_name = 'created_at'
            LOOP
                EXECUTE format('ALTER TABLE %I ALTER COLUMN created_at SET DEFAULT current_timestamp', t);
            END loop;
    END;
$$ language 'plpgsql';


DO
$$
    DECLARE
        t text;
    BEGIN
        FOR t IN
            SELECT table_name
            FROM information_schema.columns
            WHERE column_name = 'updated_at'
            LOOP
                EXECUTE format('ALTER TABLE %I ALTER COLUMN updated_at SET DEFAULT current_timestamp', t);
            END loop;
    END;
$$ language 'plpgsql';
