ALTER TABLE meter_point_electricity
    ALTER COLUMN meter_timeswitch_code TYPE varchar;

DO
$$
    BEGIN
        ALTER TABLE meter_point_electricity
            ALTER COLUMN smso_market_participant_id TYPE varchar;
        ALTER TABLE meter_point_electricity
            RENAME COLUMN smso_market_participant_id TO smso_market_participant_code;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column smso_market_participant_id does not exist';
    END;
$$;


DO
$$
    BEGIN
        ALTER TABLE meter_point_electricity
            ALTER COLUMN standard_settlement_configuration_id TYPE varchar;
        ALTER TABLE meter_point_electricity
            RENAME COLUMN standard_settlement_configuration_id TO standard_settlement_configuration_code;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column standard_settlement_configuration_id does not exist';
    END;
$$;
