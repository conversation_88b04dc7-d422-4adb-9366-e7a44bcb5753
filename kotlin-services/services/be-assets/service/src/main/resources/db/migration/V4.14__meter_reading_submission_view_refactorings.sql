--last errored submissions view
drop view if exists last_errored_submissions;
create view last_errored_submissions as
(
with subquery as (select meterpoint_id, max(created_at) as maxCreatedAt
                  from meter_reading_submission_request
                  group by meterpoint_id)
select mrsr.*
from meter_reading_submission_request mrsr
         inner join subquery
                    on mrsr.meterpoint_id = subquery.meterpoint_id and mrsr.created_at = subquery.maxCreatedAt and
                       mrsr.status = 'ERRORED');

--nova submissions view
drop view if exists nova_submissions;
create view nova_submissions as
(
select *, debug_data -> 0 ->> 'errorCode' as error_code_1, debug_data -> 1 ->> 'errorCode' as error_code_2
from meter_reading_submission_request
where status in ('ERRORED', 'CONTACTED', 'NO_ACTION_REQUIRED', 'MANUAL_SUBMISSION'));
