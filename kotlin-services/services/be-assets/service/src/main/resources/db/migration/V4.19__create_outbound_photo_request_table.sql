create type outbound_photo_request_status as ENUM('REQUESTED', 'UPLOADED');

CREATE TABLE IF NOT EXISTS outbound_photo_request(
    id                  bigint generated by default as identity
    constraint pk_outbound_photo_request primary key,
    account_number      varchar(255)                            not null,
    fuel                meter_reading_fuel                      not null,
    request_reason      varchar(255)                            not null,
    status              outbound_photo_request_status           not null default 'REQUESTED',
    team                varchar(255)                            not null,
    deleted             timestamp,
    created_at          timestamp default CURRENT_TIMESTAMP     not null,
    updated_at          timestamp default CURRENT_TIMESTAMP     not null
)
