alter table meter_point_electricity
    drop constraint if exists pk_meter_points_electricity;
alter table meter_point_electricity
    drop constraint if exists fk_meter_point_id;

DO
$$
    BEGIN
        update meter_point_electricity
        set id = meter_point_id;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column meter_point_id does not exist';
    END;
$$;

alter table meter_point_electricity
    add constraint pk_meter_points_electricity primary key (id);

alter table meter_point_electricity
    drop column if exists meter_point_id;

alter table meter_point_electricity
    add constraint fk_meter_point_id foreign key (id) references meter_point (id);

alter table meter_point_gas
    drop constraint if exists pk_meter_point_gas;
alter table meter_point_gas
    drop constraint if exists fk_meter_point_id;

DO
$$
    BEGIN
        update meter_point_gas
        set id = meter_point_id;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column meter_point_id does not exist';
    END;
$$;


alter table meter_point_gas
    add constraint pk_meter_point_gas primary key (id);
alter table meter_point_gas
    drop column if exists meter_point_id;
alter table meter_point_gas
    add constraint fk_meter_point_id foreign key (id) references meter_point (id);
