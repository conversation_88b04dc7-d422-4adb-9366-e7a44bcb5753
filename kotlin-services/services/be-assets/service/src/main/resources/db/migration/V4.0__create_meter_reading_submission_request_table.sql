create type meter_reading_submission_request_status as enum ('PERSISTED', 'QUEUED', 'SUBMITTED', 'ERRORED', 'CONTACT_NEEDED');

create table public.meter_reading_submission_request
(
    id              bigint generated by default as identity
        constraint pk_meter_reading_submission_request
            primary key,
    submission_data json not null,
    user_id         bigint not null,
    meterpoint_id   bigint not null,
    debug_data      json,
    status          meter_reading_submission_request_status NOT NULL,
    failed_attempts int       default 0,
    last_attempt    timestamp,
    deleted         timestamp,
    created_at      timestamp default CURRENT_TIMESTAMP     not null,
    updated_at      timestamp default CURRENT_TIMESTAMP     not null
);

create trigger set_updatedat_meter_reading_submission_request
    before update
    on public.meter_reading_submission_request
    for each row
execute procedure public.set_updated_at_function();
