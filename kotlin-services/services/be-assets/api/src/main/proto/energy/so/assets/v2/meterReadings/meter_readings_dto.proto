syntax = "proto3";
package energy.so.assets.meterReadings.v2;
option java_multiple_files = true;

import "energy/so/commons/v2/search/commons_search.proto";
import "energy/so/commons/null_values.proto";
import "google/protobuf/timestamp.proto";
import "energy/so/assets/v2/register/registers_dto.proto";

message BulkXRITMRequest {
  repeated string mpxns = 1;
}

message BulkXRITMResponse {
  repeated XRITMError errors = 1;
}

message XRITMError {
  string meterpointId = 1;
  string mpxn = 2;
  string error = 3;
}

message MeterReading {
  int64 id = 1;
  int64 meter_point_id = 2;
  commons.NullableInt64 meter_id = 3;
  optional energy.so.assets.register.v2.Register register = 4;
  google.protobuf.Timestamp reading_dttm = 5;
  commons.NullableTimestamp from_dttm = 6;
  MeterReadingQuality.Quality quality = 7;
  MeterReadingSequenceType sequence_type = 8;
  MeterReadingSource source = 9;
  optional MeterReadingStatusMessage.MeterReadingStatus status = 10;
  optional MeterReadingUnit unit = 11;
  optional MeterReadingWorkflowStatusMessage.MeterReadingWorkflowStatus workflow_status = 12;
  double cumulative = 13;
  commons.NullableDouble consumption = 14;
  commons.NullableString rate_name = 15;
  commons.NullableString pending_register_reading_id = 16;
  commons.NullableTimestamp deleted = 17;
  commons.NullableTimestamp created_at = 18;
  commons.NullableTimestamp updated_at = 19;
  commons.NullableTimestamp receivedDttm = 20;
  commons.NullableString meterPointIdentifier = 21;
  commons.NullableString error_description = 22;
  commons.NullableString meterIdentifier = 23;
  commons.NullableString registerIdentifier = 24;
}

enum MeterReadingSequenceType {
  NORMAL = 0;
  FIRST = 1;
  LAST = 2;
}

enum MeterReadingSource {
  MMR = 0;
  System = 1;
  Customer = 2;
  User = 3;
  Virtual = 4;
  SMR = 5;
  Industry = 6;
  EDMI = 7;
  Secure = 8;
  AMR = 9;
  GCMDS = 10;
}

message MeterReadingStatusMessage {
  enum MeterReadingStatus {
    ACCEPTED = 0;
    PENDING = 1;
    UNKNOWN = 2;
    REMOVED = 3;
  }
}

enum MeterReadingUnit {
  kWh = 0;
  MWh = 1;
  kVAh = 2;
  hour = 3;
  day = 4;
  month = 5;
  second = 6;
  kVArh = 7;
  MJ = 8;
  therm = 9;
  kW = 10;
  MW = 11;
  kVAr = 12;
  kVA = 13;
  m3 = 14;
  tm3 = 15;
  hm3 = 16;
  thm3 = 17;
  ft3 = 18;
  tcf = 19;
  hcf = 20 ;
  thcf = 21 ;
  year = 22 ;
}

message MeterReadingWorkflowStatusMessage {
  enum MeterReadingWorkflowStatus {
    VALIDATION_SCHEDULED = 0;
    VALIDATION_SCHEDULED_WITH_OVERRIDE = 1;
    AWAITING_INDUSTRY_RESPONSE = 2;
    VALIDATION_SUCCESS = 3;
    VALIDATION_FAILURE = 4;
    ACCEPTED = 5;
    ERROR = 6;
    SCHEDULED_FOR_PUBLISHING = 7;
    FLAGGED = 8;
  }
}

enum MeterReadingUnitType {
  ELECTRICITY = 0;
  ELECTRICITY_NIGHT = 1;
  GAS = 2;
}

enum MeterReadingReadingType {
  STANDARD = 0;
  DAY = 1;
  NIGHT = 2;
}

enum MeterReadingGrouping {
  NO_GROUPING = 0;
  DAILY = 1;
  MONTHLY = 2;
}

// Need to do this because of conflict in enum values
message MeterReadingQuality{
  enum Quality {
    NORMAL = 0;
    MANUAL = 1;
    ESTIMATED = 2;
  }
}

message MeterReadingsSearchRequest {
  repeated int64 meterId = 1;
  repeated int64 meterPointId = 2;
  repeated MeterReadingQuality.Quality quality = 3;
  repeated MeterReadingSource source = 4;
  optional bool onlyLast = 5;
  optional google.protobuf.Timestamp startDate = 6;
  optional google.protobuf.Timestamp endDate = 7;
  optional bool submissionProcessed = 8;
  optional bool deleted = 9;
  energy.so.commons.v2.search.Pagination pagination = 10;
  repeated MeterReadingStatusMessage.MeterReadingStatus status = 12;
  optional bool enableAllHistoricalReadings = 13;
  optional bool includeCancelledAndDeletedAgreements = 14;
  optional int64 productAccountId = 15;
  optional int64 registerId = 16;
  optional MeterReadingGrouping grouping = 17;
  optional ReadFrequency readFrequency = 18;
}

enum ReadFrequency {
  GREATER_THAN_DAILY_FREQUENCY = 0;
  DAILY_FREQUENCY = 1;
  HALF_HOURLY_FREQUENCY = 2;
}

enum ReadingSource{
  WEBSITE = 0;
  AGENT = 1;
  IVR = 2;
  JUNIFER = 3;
}

message MeterReadingsSearchResponse {
  repeated MeterReading results = 1;
  optional energy.so.commons.v2.search.SearchResponseMetadata metadata = 2;
}

message CreateMeterReadingRequest {
  energy.so.commons.NullableInt64 meter_point_id = 1; // be-assets meter point id
  double cumulative = 2; // Meter reading
  google.protobuf.Timestamp readingAt = 3;
  MeterReadingUnit unitType = 4;
}

message SubmitMeterReadingRequest {
  repeated string digits = 1;
  MeterReadingUnitType unit_type = 2;
  commons.NullableString meter_identifier = 3;
  int64 meter_point_id = 4;
  int64 units = 5;
  commons.NullableString register_identifier = 6;
  google.protobuf.Timestamp reading_at = 7;
  optional MeterReadingReadingType reading_type = 8;
  bool validate_only = 9;
  commons.NullableInt64 register_id = 10;
}

message SubmitMeterReadingList {
  int64 userId = 1;
  int64 billingAccountId = 2;
  repeated SubmitMeterReadingRequest meters = 3;
  SubmitMeterReadingFlow flow = 4;
  ReadingSource source = 5;
}

message SubmitMoveInMeterReadingList {
  string accountNumber = 1;
  string userEmail = 2;
  repeated SubmitMeterReadingRequest meters = 3;
  SubmitMeterReadingFlow flow = 4;
}

message AccountReadingWithTechnicalDetails {
  int64 value = 1;
  int64 meterId = 2;
  int64 registerId = 4;
  google.protobuf.Timestamp readingDate = 6;
}

message AccountReadingWithoutTechnicalDetails {
  int64 value = 1;
  int64 meterpointId = 2;
  MeterReadingReadingType readingType = 3;
  google.protobuf.Timestamp readingDate = 4;
}

message SubmitAccountReadingWithTechnicalDetailsRequest {
  int64 userId = 1;
  int64 billingAccountId = 2;
  repeated AccountReadingWithTechnicalDetails readings = 3;
}

message SubmitAccountReadingWithoutTechnicalDetailsRequest {
  int64 userId = 1;
  int64 billingAccountId = 2;
  repeated AccountReadingWithoutTechnicalDetails readings = 3;
}

message SubmitAccountMeterReadingError {
  string errorCode = 1;
  commons.NullableInt64 register_id = 2;
  string message = 3;
}

message SubmitAccountMeterReadingResponse {
  bool success = 1;
  repeated SubmitAccountMeterReadingError error = 2;
}

message SubmitMeterReadingError {
  string code = 1;
  commons.NullableString meter_identifier = 2;
  commons.NullableString register_identifier = 3;
  string message = 4;
}

message SubmitMeterReadingResponse {
  bool success = 1;
  repeated SubmitMeterReadingError error = 2;
}

message GetLastElectricityMeterReadingRequest {
  int64 meterPointId = 1;
}

message GetFirstElectricityMeterReadingRequest {
  int64 meterPointId = 1;
}

message GetElectricityMeterReadingsByDateRequest {
  int64 meterPointId = 1;
  google.protobuf.Timestamp startDate = 2;
  google.protobuf.Timestamp endDate = 3;
}

message ElectricityMeterReadingsByDateResponse {
  repeated MeterReading results = 1;
}

message ResolveReadingSubmissionErrorRequest {
  int64 submissionId = 1;
}

message MeterReadingStatusRequest {
  int64 meterPointId = 1;
}

message ReadingSubmissions {
  google.protobuf.Timestamp submissionTime = 1;
  MeterReadingSubmissionStatus meterReadingSubmissionStatus = 2;
  repeated MeterReadingSubmissionValue meterReadingSubmissionValues = 3;
  int64 id = 4;
  string email = 5;
  string accountNumber = 6;
  repeated MeterReadingSubmissionError error = 7;
}

message MeterReadingStatusResponse {
  repeated ReadingSubmissions readingSubmissions = 1;
}

message MeterReadingSubmissionValue {
  int64 value = 1;
  MeterReadingSubmissionType type = 2;
  google.protobuf.Timestamp submissionDate = 3;
  optional MeterReadingUnitType meterType = 4;
}

message MeterReadingSubmissionError {
  string errorCode = 1;
  string errorDescription = 2;
}

message AddMeterReadingPhotoEvidenceRequest {
  repeated PhotoEvidenceMetadata metadata = 1;
}

message AddMeterReadingOutboundPhotoRequest {
  repeated PhotoEvidenceMetadata metadata = 1;
  int64 outboundRequestId = 2;
}

message OutboundRequestsResponse {
  repeated OutboundRequest outboundRequests = 1;
}

message OutboundRequest {
  int64 id = 1;
  string accountNumber = 2;
  PhotoEvidenceMetadata.MeterReadingFuel fuel = 3;
  string requestReason = 4;
  OutboundRequestStatus status = 5;
  int64 freshdeskGroupId = 6;
  commons.NullableInt64 freshdeskTicketId = 7;
  commons.NullableInt64 emailMessageId = 8;
  string freshdeskGroupName = 9;
  google.protobuf.Timestamp createdAt = 10;
  commons.NullableTimestamp updatedAt = 11;
  commons.NullableTimestamp deleted = 12;
}

message PhotoEvidenceMetadata {

  enum MeterReadingFuel {
    UNKNOWN_FUEL = 0;
    ELEC = 1;
    GAS = 2;
    DUAL = 3;
  }

  string accountNumber = 1;
  commons.NullableInt64 meterPointId = 2;
  commons.NullableString meterPointIdentifier = 3;
  commons.NullableTimestamp submissionDate = 4;
  commons.NullableInt64 standardRead = 5;
  commons.NullableInt64 dayRead = 6;
  commons.NullableInt64 nightRead = 7;
  commons.NullableString errorCode = 8;
  string imageIdentifier = 9;
  optional MeterReadingFuel fuel = 10;
}

message MeterReadingPhotoEvidenceUploadUrlRequest {
  repeated string imageName = 1;
}

message MeterReadingPhotoEvidenceUploadUrl {
  string imageName = 1;
  string url = 2;
}

message MeterReadingPhotoEvidenceUploadUrlResponse {
  repeated MeterReadingPhotoEvidenceUploadUrl imageToUrl = 1;
}

message MeterReadingPhotoEvidenceGetUrlRequest {
  string imageIdentifier = 1;
}

message MeterReadingPhotoEvidenceSignedUpGetUrl {
  string url = 1;
}

message GetMeterReadingPhotoEvidenceRequest {
  string accountNumber = 1;
}

message GetMeterReadingPhotoEvidenceResponse {
  repeated MeterReadingPhotoEvidenceByFreshdeskTicketId meterReadingPhotoEvidences = 1;
}

message MeterReadingPhotoEvidenceByFreshdeskTicketId {
  int64 freshdeskTicketId = 1;
  repeated MeterReadingPhotoEvidence meterReadingPhotoEvidences = 2;
}

message MeterReadingPhotoEvidence {
  commons.NullableString meterPointIdentifier = 1;
  commons.NullableTimestamp submissionDate = 2;
  commons.NullableInt64 standardRead = 3;
  commons.NullableInt64 dayRead = 4;
  commons.NullableInt64 nightRead = 5;
  commons.NullableString errorCode = 6;
  string imageIdentifier = 7;
  PhotoEvidenceMetadata.MeterReadingFuel fuel = 8;
  commons.NullableString requestReason = 9;
  google.protobuf.Timestamp createdAt = 10;
}

message FreshdeskMeterReadingPhotoEvidenceRequest {
  string accountNumber = 1;
  repeated FreshdeskMeterReadingPhotoEvidence photoEvidences = 2;
}

message FreshdeskMeterReadingPhotoEvidence {
  commons.NullableInt64 freshdeskTicketId = 1;
  string imageIdentifier = 2;
  commons.NullableString caseId = 3;
}

message DeleteMeterReadingPhotoEvidenceRequest {
  string imageIdentifier = 1;
}

message OutboundMeterReadingPhotoRequest {
  string accountNumber = 1;
  PhotoEvidenceMetadata.MeterReadingFuel meterPointType = 2;
  string requestReason = 3;
  int64 freshdeskGroupId = 4;
  string freshdeskGroupName = 5;
}

message SendOutboundFollowUpEmailRequest {
  int64 outboundRequestId = 1;
  OutboundRequestStatus outboundRequestCurrentStatus = 2;
}

enum MeterReadingSubmissionType {
  Day = 0;
  Night = 1;
  Standard = 2;
}

enum MeterReadingSubmissionStatus {
  PENDING = 0;
  ERROR = 1;
  CONTACTED = 2;
  SUBMITTED = 4;
  MANUAL_SUBMISSION = 5;
  NO_ACTION_REQUIRED = 6;
}

enum SubmitMeterReadingFlow {
  SYNC = 0;
  ASYNC_WITH_ONE_RETRY = 1;
  ASYNC_WITH_MULTIPLE_RETRIES = 2;
}

enum OutboundRequestStatus {
  REQUESTED = 0;
  UPLOADED = 1;
  FRESHDESK_TICKET_CREATED = 2;
  CANCELLED = 3;
  EMAIL_SENT = 4;
  FIRST_FOLLOW_UP_SENT = 5;
  SECOND_FOLLOW_UP_SENT = 6;
}