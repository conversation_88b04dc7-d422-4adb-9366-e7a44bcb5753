Feature: Meter Readings

  Background:
    * url url

  # For now all numeric meter_ids are valid as we hard code the response
  Scenario: A valid meter_point_id returns a 200 for the latest meter reading for that meter_point_id
    Given path '/meter_points/1234/readings/latest'
    When method GET
    Then status 200

  Scenario: An invalid meter_point_id returns a 400 for the latest meter reading
    Given path '/meter_points/034b4218-5242-4c6c-9487-3b4b1910c99b/readings/latest'
    When method GET
    Then status 400
