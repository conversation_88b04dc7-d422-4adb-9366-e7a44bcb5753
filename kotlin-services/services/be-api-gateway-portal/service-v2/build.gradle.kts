plugins {
    id("energy.so.be-commons-conventions-rest")
    id("energy.so.conventions.openapi")
    id("energy.so.conventions.spring")
}

dependencies {
    implementation(libs.opentelemetry.annotations)
    implementation(libs.springdoc.openapi)

    implementation(project(":be-commons-json"))
    implementation(project(":be-commons-grpc"))
    implementation(project(":be-ac-junifer-api"))
    implementation(project(":be-identity-api"))
    implementation(project(":be-customers-api"))
    implementation(project(":be-communications-api"))
    implementation(project(":be-assets-api"))
    implementation(project(":be-comms-int-api"))
    implementation(project(":be-financials-api"))
    implementation(project(":be-products-api"))
    implementation(project(":be-commons-redis"))
    implementation(project(":be-dotdigital-client"))
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation(libs.java.jwt)
    implementation(libs.spring.grpc.client)
    implementation("com.github.doyaaaaaken:kotlin-csv-jvm:0.15.2")
    implementation(libs.jackson.dataformat.csv)
    implementation(libs.jedis)
    implementation(libs.ehcache)

    testFixturesImplementation(project(":be-customers-api"))
    testFixturesImplementation(project(":be-ac-junifer-api"))
    testFixturesImplementation(project(":be-identity-api"))
    testFixturesImplementation(project(":be-assets-api"))
    testFixturesImplementation(project(":be-comms-int-api"))
    testFixturesImplementation(project(":be-financials-api"))
    testFixturesImplementation(project(":be-products-api"))
    testImplementation(project(":be-commons-database-test-extensions"))
    testImplementation("org.springframework.boot:spring-boot-starter-webflux")
    testImplementation(libs.kotest.framework.datatest)
    testImplementation(libs.projectreactor.test)
    testImplementation(libs.testcontainers.junit.jupiter)
    testImplementation("io.kotest:kotest-extensions-now:5.6.0")
    testFixturesImplementation("com.github.doyaaaaaken:kotlin-csv-jvm:0.15.2")
    testFixturesImplementation(libs.jackson.dataformat.csv)
}

openApiGenerate.apply {
    apiPackage.set("energy.so.generated.portal.api")
    modelPackage.set("energy.so.generated.portal.model")
    invokerPackage.set("energy.so.generated.portal.invoker")
    configOptions.put("serviceInterface", "true")
    configOptions.put("serviceImplementation", "true")
    configOptions.put("useTags", "true")
    configOptions.put("enumPropertyNaming", "UPPERCASE")
    importMappings.put("Set", "kotlin.collections.Set")
    typeMappings.put("array+set", "Set")
}

application {
    mainClass.set("energy.so.core.api.gateway.PortalApiGatewayServer")
}

jib {
    container.mainClass = "energy.so.core.api.gateway.PortalApiGatewayServerKt"
}

sonarqube {
    properties {
        property("sonar.junit.reportPaths", "${project.projectDir}/build/test-results/test")
        property(
            "sonar.exclusions",
            listOf(
                "src/main/kotlin/energy/so/core/api/gateway/modules/**",
                "src/main/kotlin/energy/so/core/api/gateway/models/**",
                "src/main/kotlin/energy/so/core/api/gateway/config/**",
                "src/main/kotlin/energy/so/core/api/gateway/PortalApiGatewayServer.kt",
                "src/main/kotlin/energy/so/core/api/gateway/PortalApiGatewayConfig.kt"
            )
        )
    }
}

tasks.test {
    // https://github.com/mockk/mockk/issues/681
    jvmArgs("--add-opens", "java.base/java.time=ALL-UNNAMED")
}
