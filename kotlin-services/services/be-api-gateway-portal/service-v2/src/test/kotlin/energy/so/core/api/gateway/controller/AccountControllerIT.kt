package energy.so.core.api.gateway.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import energy.so.ac.junifer.v1.assets.AssetServiceGrpc
import energy.so.ac.junifer.v1.meterpoint.MeterPointServiceGrpc
import energy.so.assets.meter.v2.MetersGrpc.MetersBlockingStub
import energy.so.assets.meterPoints.v2.MeterPointsGrpc.MeterPointsBlockingStub
import energy.so.assets.meterPoints.v2.getMeterPointsRequest
import energy.so.assets.meterReadings.v2.MeterReadingsGrpc.MeterReadingsBlockingStub
import energy.so.assets.meterReadings.v2.meterReadingStatusRequest
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.dtos.idRequestList
import energy.so.core.api.gateway.helpers.mockIsTokenValidAnyFalse
import energy.so.core.api.gateway.helpers.mockIsTokenValidTrue
import energy.so.core.api.gateway.security.SessionSupplier
import energy.so.core.api.gateway.service.accountstates.strategies.SupplyStateStrategy
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.ACCOUNT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.PRODUCT_VARIANT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.authenticatedUser
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.billingAccountResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.cancelSwitchRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.getAccountResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.productAccountResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.renewWindowRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.renewWindowResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.userByIdRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.userByIdResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.validCustomerAccountsByCurrentAccountIdResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountStatePrecannedData.ACCOUNT_ID_2
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreement
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreementsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreementsWithSmartPayAsYouGoResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.productDetailsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.productDetailsSmartPayAsYouGoResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.METER_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.elecMeterPointsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.frequencyRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.frequencyResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.getMeterReadingRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.meterResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.readingSubmissionResponse
import energy.so.core.portal.api.gateway.v2.fixtures.FinancialsPrecannedData.billingAccountTransactionProto
import energy.so.customers.agreements.v2.AgreementsGrpc.AgreementsBlockingStub
import energy.so.customers.agreements.v2.getAgreementByMeterpointIdRequest
import energy.so.customers.billingaccounts.v2.BillingAccountsGrpc.BillingAccountsBlockingStub
import energy.so.customers.productaccounts.v2.ProductAccountsGrpc.ProductAccountsBlockingStub
import energy.so.customers.v2.configurations.ConfigurationsServiceGrpc.ConfigurationsServiceBlockingStub
import energy.so.customers.v2.customers.CustomersGrpc.CustomersBlockingStub
import energy.so.generated.portal.model.SupplyState
import energy.so.payments.v2.BillingAccountTransactionsGrpc.BillingAccountTransactionsBlockingStub
import energy.so.payments.v2.billingAccountTransactionsResponse
import energy.so.products.v2.ProductDetailsServiceGrpc.ProductDetailsServiceBlockingStub
import energy.so.users.v2.UserAuthenticationServiceGrpc.UserAuthenticationServiceBlockingStub
import energy.so.users.v2.UsersGrpc.UsersBlockingStub
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import java.time.format.DateTimeFormatter
import org.hamcrest.Matchers.`is`
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@SpringBootTest
@AutoConfigureMockMvc
@MockKExtension.ConfirmVerification
class AccountControllerIT(
    @Autowired private val mockMvc: MockMvc,
    @Autowired private val objectMapper: ObjectMapper,
    @MockkBean private val authenticationClient: UserAuthenticationServiceBlockingStub,
    @MockkBean private val billingAccountTransactionsClient: BillingAccountTransactionsBlockingStub,
    @MockkBean private val billingAccountsClient: BillingAccountsBlockingStub,
    @MockkBean private val usersClient: UsersBlockingStub,
    @MockkBean private val productDetailsClient: ProductDetailsServiceBlockingStub,
    @MockkBean private val sessionSupplier: SessionSupplier,
    @MockkBean private val customerClient: CustomersBlockingStub,
    @MockkBean private val productAccountClient: ProductAccountsBlockingStub,
    @MockkBean private val meterPointClient: MeterPointsBlockingStub,
    @MockkBean private val meterClient: MetersBlockingStub,
    @MockkBean private val meterReadingClient: MeterReadingsBlockingStub,
    @MockkBean private val supplyStateStrategy: SupplyStateStrategy,
    @MockkBean private val configurationsClient: ConfigurationsServiceBlockingStub,
    @MockkBean private val agreementsClient: AgreementsBlockingStub,
    @MockkBean private val assetServiceClient: AssetServiceGrpc.AssetServiceBlockingStub,
    @MockkBean private val acMeterPointClient: MeterPointServiceGrpc.MeterPointServiceBlockingStub,
) : BehaviorSpec({

    afterEach {
        clearAllMocks()
    }

    given("/api/v1/accounts/{accountId}") {

        and("there is an account for the provided id") {
            every {
                billingAccountsClient.getBillingAccountById(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns billingAccountResponse
            every {
                billingAccountTransactionsClient.getByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns billingAccountTransactionsResponse {
                billingAccountTransaction.addAll(listOf(billingAccountTransactionProto))
            }
            every {
                customerClient.getProductDetailsByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns AccountPrecannedData.productDetailsResponseProto
            every { supplyStateStrategy.computeState(ACCOUNT_ID) } returns SupplyState.ON_SUPPLY

            and("valid auth token is given") {
                mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                val validAccessToken = authenticationClient.mockIsTokenValidTrue()

                `when`("getAccount is called") {

                    then("response is OK") {
                        mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}") {
                            header("Authorization", validAccessToken)
                            accept = MediaType.APPLICATION_JSON
                            contentType = MediaType.APPLICATION_JSON
                        }.andExpect {
                            status { isOk() }
                            jsonPath("$.accountID", `is`(getAccountResponse.accountID.toInt()))
                            jsonPath("$.accountNumber", `is`(getAccountResponse.accountNumber))
                            jsonPath("$.balance", `is`(getAccountResponse.balance))
                            jsonPath("$.isWHDEligible", `is`(getAccountResponse.isWHDEligible))
                            jsonPath(
                                "$.startDate",
                                `is`(getAccountResponse.startDate?.format(DateTimeFormatter.ISO_LOCAL_DATE))
                            )
                            jsonPath(
                                "$.closeDate",
                                `is`(getAccountResponse.closeDate?.format(DateTimeFormatter.ISO_LOCAL_DATE))
                            )
                            jsonPath(
                                "$.supplyAddress.postcode",
                                `is`(getAccountResponse.supplyAddress?.postcode)
                            )
                            jsonPath(
                                "$.supplyAddress.addressLine1",
                                `is`(getAccountResponse.supplyAddress?.addressLine1)
                            )
                            jsonPath(
                                "$.supplyAddress.addressLine2",
                                `is`(getAccountResponse.supplyAddress?.addressLine2)
                            )
                            jsonPath(
                                "$.supplyAddress.addressLine3",
                                `is`(getAccountResponse.supplyAddress?.addressLine3)
                            )
                            jsonPath(
                                "$.supplyAddress.countryCode",
                                `is`(getAccountResponse.supplyAddress?.countryCode)
                            )
                            jsonPath(
                                "$.billingAddress.postcode",
                                `is`(getAccountResponse.billingAddress?.postcode)
                            )
                            jsonPath(
                                "$.billingAddress.addressLine1",
                                `is`(getAccountResponse.billingAddress?.addressLine1)
                            )
                            jsonPath(
                                "$.billingAddress.addressLine2",
                                `is`(getAccountResponse.billingAddress?.addressLine2)
                            )
                            jsonPath(
                                "$.billingAddress.addressLine3",
                                `is`(getAccountResponse.billingAddress?.addressLine3)
                            )
                            jsonPath(
                                "$.billingAddress.countryCode",
                                `is`(getAccountResponse.billingAddress?.countryCode)
                            )
                            jsonPath("isOnSupply", `is`(true))
                            jsonPath("isClosed", `is`(false))
                        }.andReturn()

                        verify { billingAccountsClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                        verify {
                            billingAccountTransactionsClient.getByBillingAccountId(
                                idRequest {
                                    id = ACCOUNT_ID
                                }
                            )
                        }
                        verify {
                            customerClient.getProductDetailsByBillingAccountId(
                                idRequest {
                                    id = ACCOUNT_ID
                                }
                            )
                        }
                        verify { supplyStateStrategy.computeState(ACCOUNT_ID) }
                        verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                    }
                }
            }

            and("invalid auth token is given") {
                mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                authenticationClient.mockIsTokenValidAnyFalse()

                `when`("getTransactions is called") {

                    then("response is Forbidden") {
                        mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}") {
                            header("Authorization", "invalid token")
                            accept = MediaType.APPLICATION_JSON
                            contentType = MediaType.APPLICATION_JSON
                        }.andExpect {
                            status { isForbidden() }
                        }.andReturn()

                        verify(exactly = 0) {
                            billingAccountsClient.getBillingAccountById(
                                idRequest {
                                    id = ACCOUNT_ID
                                }
                            )
                        }
                    }
                }
            }

            and("account does not belong to currently logged in user") {
                mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

                then("Forbidden returned") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID_2}") {
                        header("Authorization", authenticationClient.mockIsTokenValidTrue())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                    }.andExpect {
                        status { isForbidden() }
                    }.andReturn()

                    verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                }
            }
        }
    }

    given("get meters for account") {

        and("valid auth token is given") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            every {
                productAccountClient.getProductAccountsByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns productAccountResponse

            every {
                meterPointClient.getMeterPointsByIds(
                    getMeterPointsRequest {
                        ids.add(ACCOUNT_ID)
                        includeMeterReadings = true
                    }
                )
            } returns elecMeterPointsResponse
            every {
                meterReadingClient.getLastMeterReadingStatus(
                    meterReadingStatusRequest {
                        meterPointId = ACCOUNT_ID
                    }
                )
            } returns readingSubmissionResponse

            every { acMeterPointClient.getMeterPointReadFrequency(frequencyRequest) } returns frequencyResponse

            `when`("get meters for account id") {

                then("response is OK") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}/meters") {
                        header("Authorization", authenticationClient.mockIsTokenValidTrue())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                    }.andExpect {
                        status { isOk() }
                        MockMvcResultMatchers.jsonPath("$")
                            .value { listOf(meterResponse) }
                    }.andReturn()
                    verify { productAccountClient.getProductAccountsByBillingAccountId(idRequest { id = ACCOUNT_ID }) }
                    verify {
                        meterPointClient.getMeterPointsByIds(
                            getMeterPointsRequest {
                                ids.add(ACCOUNT_ID)
                                includeMeterReadings = true
                            }
                        )
                    }
                    verify {
                        meterReadingClient.getLastMeterReadingStatus(
                            meterReadingStatusRequest {
                                meterPointId = ACCOUNT_ID
                            }
                        )
                    }
                    verify { acMeterPointClient.getMeterPointReadFrequency(frequencyRequest) }
                    verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                }
            }
        }

        and("account does not belong to currently logged in user") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            then("Forbidden returned") {
                mockMvc.get("/api/v1/accounts/${ACCOUNT_ID_2}/meters") {
                    header("Authorization", authenticationClient.mockIsTokenValidTrue())
                    accept = MediaType.APPLICATION_JSON
                    contentType = MediaType.APPLICATION_JSON
                }.andExpect {
                    status { isForbidden() }
                }.andReturn()

                verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
            }
        }

        and("invalid auth token is given") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            `when`("method is called") {

                then("response is Forbidden") {
                    mockMvc.post("/api/v1/accounts/${ACCOUNT_ID}/meters") {
                        header("Authorization", authenticationClient.mockIsTokenValidAnyFalse())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                        content = objectMapper.writeValueAsString(cancelSwitchRequest())
                    }.andExpect {
                        status { isForbidden() }
                    }.andReturn()

                    verify(exactly = 0) {
                        productAccountClient.getProductAccountsByBillingAccountId(
                            idRequest {
                                id = ACCOUNT_ID
                            }
                        )
                    }
                    verify(exactly = 0) {
                        meterPointClient.getMeterPointsByIds(
                            getMeterPointsRequest {
                                ids.add(
                                    ACCOUNT_ID
                                )
                                includeMeterReadings = true
                            }
                        )
                    }
                    verify(
                        exactly = 0
                    ) { meterClient.getMetersByMeterPointIds(idRequestList { idRequests.add(ACCOUNT_ID) }) }
                    verify(exactly = 0) {
                        meterReadingClient.getLastMeterReadingStatus(
                            meterReadingStatusRequest {
                                meterPointId = ACCOUNT_ID
                            }
                        )
                    }
                }
            }
        }
    }

    given("GET /api/v1/accounts/{accountId}/agreements") {

        and("request is valid") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            every {
                productDetailsClient.getProductDetailsByProductVariantIds(
                    idRequestList {
                        idRequests.add(PRODUCT_VARIANT_ID)
                    }
                )
            } returns productDetailsResponse

            every {
                productAccountClient.getProductAccountsByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns AgreementsPrecannedData.productAccountResponse

            every {
                meterPointClient.getMeterPointsByIds(
                    getMeterPointsRequest {
                        ids.addAll(listOf(AccountPrecannedData.METER_POINT_ID, AccountPrecannedData.METER_POINT_ID_2))
                        includeMeterReadings = false
                    }
                )
            } returns AgreementsPrecannedData.meterPointsResponse

            every {
                supplyStateStrategy.computeState(ACCOUNT_ID)
            } returns SupplyState.ON_SUPPLY

            every {
                configurationsClient.getConfiguration(renewWindowRequest)
            } returns renewWindowResponse

            every {
                agreementsClient.getAgreementByMeterpointId(
                    getAgreementByMeterpointIdRequest {
                        id = AccountPrecannedData.METER_POINT_ID
                        firstAgreement = false
                        includeCancelledAndDeletedAgreements = false
                        includeFutureAgreements = true
                    }
                )
            } returns agreement

            every {
                agreementsClient.getAgreementByMeterpointId(
                    getAgreementByMeterpointIdRequest {
                        id = AccountPrecannedData.METER_POINT_ID_2
                        firstAgreement = false
                        includeCancelledAndDeletedAgreements = false
                        includeFutureAgreements = true
                    }
                )
            } returns agreement

            every {
                assetServiceClient.getEstimatedUsage(any())
            } returns AccountPrecannedData.findEstimatedUsageResponse

            every {
                billingAccountsClient.getBillingAccountById(idRequest { id = ACCOUNT_ID })
            } returns billingAccountResponse

            `when`("endpoint is called for non-smart-pay-as-you-go") {
                then("response is ok and content equals expected content") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}/agreements") {
                        header("Authorization", authenticationClient.mockIsTokenValidTrue())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                    }.andExpect {
                        status { isOk() }
                        content {
                            json(objectMapper.writeValueAsString(agreementsResponse))
                        }
                    }.andReturn()

                    verify {
                        productAccountClient.getProductAccountsByBillingAccountId(
                            idRequest {
                                id = ACCOUNT_ID
                            }
                        )
                    }
                    verify {
                        productDetailsClient.getProductDetailsByProductVariantIds(
                            idRequestList {
                                idRequests.add(PRODUCT_VARIANT_ID)
                            }
                        )
                    }
                    verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                }
            }
        }

        and("request is valid and on smart pay as you go") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            every {
                productDetailsClient.getProductDetailsByProductVariantIds(
                    idRequestList {
                        idRequests.add(PRODUCT_VARIANT_ID)
                    }
                )
            } returns productDetailsSmartPayAsYouGoResponse

            every {
                productAccountClient.getProductAccountsByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } returns AgreementsPrecannedData.productAccountResponse

            every {
                meterPointClient.getMeterPointsByIds(
                    getMeterPointsRequest {
                        ids.addAll(listOf(AccountPrecannedData.METER_POINT_ID, AccountPrecannedData.METER_POINT_ID_2))
                        includeMeterReadings = false
                    }
                )
            } returns AgreementsPrecannedData.meterPointsResponse

            every {
                supplyStateStrategy.computeState(ACCOUNT_ID)
            } returns SupplyState.ON_SUPPLY

            every {
                configurationsClient.getConfiguration(renewWindowRequest)
            } returns renewWindowResponse

            every {
                agreementsClient.getAgreementByMeterpointId(
                    getAgreementByMeterpointIdRequest {
                        id = AccountPrecannedData.METER_POINT_ID
                        firstAgreement = false
                        includeCancelledAndDeletedAgreements = false
                        includeFutureAgreements = true
                    }
                )
            } returns agreement

            every {
                agreementsClient.getAgreementByMeterpointId(
                    getAgreementByMeterpointIdRequest {
                        id = AccountPrecannedData.METER_POINT_ID_2
                        firstAgreement = false
                        includeCancelledAndDeletedAgreements = false
                        includeFutureAgreements = true
                    }
                )
            } returns agreement

            every {
                assetServiceClient.getEstimatedUsage(any())
            } returns AccountPrecannedData.findEstimatedUsageResponse

            every {
                billingAccountsClient.getBillingAccountById(idRequest { id = ACCOUNT_ID })
            } returns billingAccountResponse

            `when`("endpoint is called for smart-pay-as-you-go") {

                then("response is ok and content equals expected content") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}/agreements") {
                        header("Authorization", authenticationClient.mockIsTokenValidTrue())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                    }.andExpect {
                        status { isOk() }
                        content {
                            json(objectMapper.writeValueAsString(agreementsWithSmartPayAsYouGoResponse))
                        }
                    }.andReturn()

                    verify {
                        productAccountClient.getProductAccountsByBillingAccountId(
                            idRequest {
                                id = ACCOUNT_ID
                            }
                        )
                    }
                    verify {
                        productDetailsClient.getProductDetailsByProductVariantIds(
                            idRequestList {
                                idRequests.add(PRODUCT_VARIANT_ID)
                            }
                        )
                    }
                    verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                }
            }
        }

        and("account does not belong to currently logged in user") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            then("Forbidden returned") {
                mockMvc.get("/api/v1/accounts/${ACCOUNT_ID_2}/agreements") {
                    header("Authorization", authenticationClient.mockIsTokenValidTrue())
                    accept = MediaType.APPLICATION_JSON
                    contentType = MediaType.APPLICATION_JSON
                }.andExpect {
                    status { isForbidden() }
                }.andReturn()

                verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
            }
        }

        and("invalid auth token is given") {
            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            `when`("endpoint is called") {

                then("response is Forbidden") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}/agreements") {
                        header("Authorization", authenticationClient.mockIsTokenValidAnyFalse())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                        content = objectMapper.writeValueAsString(getMeterReadingRequest(METER_ID))
                    }.andExpect {
                        status { isForbidden() }
                    }.andReturn()
                    verify(exactly = 0) {
                        productAccountClient.getProductAccountsByBillingAccountId(any())
                        productDetailsClient.getProductDetailsByProductVariantIds(any())
                    }
                }
            }
        }

        and("exception thrown from server") {

            every {
                productAccountClient.getProductAccountsByBillingAccountId(
                    idRequest {
                        id = ACCOUNT_ID
                    }
                )
            } throws UnknownGrpcException("Something went wrong", "error", "error")

            every {
                supplyStateStrategy.computeState(ACCOUNT_ID)
            } returns SupplyState.ON_SUPPLY

            every {
                configurationsClient.getConfiguration(renewWindowRequest)
            } returns renewWindowResponse

            every {
                billingAccountsClient.getBillingAccountById(idRequest { id = ACCOUNT_ID })
            } returns billingAccountResponse

            mockAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)

            `when`("endpoint is called") {

                then("response is Bad Request") {
                    mockMvc.get("/api/v1/accounts/${ACCOUNT_ID}/agreements") {
                        header("Authorization", authenticationClient.mockIsTokenValidTrue())
                        accept = MediaType.APPLICATION_JSON
                        contentType = MediaType.APPLICATION_JSON
                        content = objectMapper.writeValueAsString(getMeterReadingRequest(METER_ID))
                    }.andExpect {
                        status { isInternalServerError() }
                    }.andReturn()
                    verify {
                        productAccountClient.getProductAccountsByBillingAccountId(any())
                        productDetailsClient.getProductDetailsByProductVariantIds(any()) wasNot Called
                    }
                    verifyAccountBelongsToCurrentUser(sessionSupplier, usersClient, billingAccountsClient)
                }
            }
        }
    }
})

fun mockAccountBelongsToCurrentUser(
    sessionSupplier: SessionSupplier,
    usersClient: UsersBlockingStub,
    billingAccountsClient: BillingAccountsBlockingStub,
) {
    every { sessionSupplier.getUserId() } returns authenticatedUser.userId.toLong()
    every { usersClient.getUserById(any()) } returns userByIdResponse
    every { billingAccountsClient.getCustomerAccountsByCurrentAccountId(any()) } returns
            validCustomerAccountsByCurrentAccountIdResponse
}

fun verifyAccountBelongsToCurrentUser(
    sessionSupplier: SessionSupplier,
    userClient: UsersBlockingStub,
    billingAccountClient: BillingAccountsBlockingStub,
) {
    verify { sessionSupplier.getUserId() }
    verify { userClient.getUserById(userByIdRequest) }
    verify { billingAccountClient.getCustomerAccountsByCurrentAccountId(any()) }
}
