package energy.so.core.api.gateway.service.alerts.marketingbanners

import energy.so.core.api.gateway.cache.AccountPreferencesCache
import energy.so.core.api.gateway.service.alerts.marketingbanners.MarketingBanners.optIntoMarketingBanner
import energy.so.core.portal.api.gateway.v2.fixtures.AlertsPrecannedData.ACCOUNT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AlertsPrecannedData.accountStates
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.emailContactPreference
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.testMarketingPreference
import energy.so.customers.v2.preferences.getAccountPreferencesResponse
import energy.so.generated.portal.model.MarketingBannerName
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class OptIntoMarketingBannerStrategyTest : BehaviorSpec({

    val accountPreferenceCache = mockk<AccountPreferencesCache>()

    val sut = OptIntoMarketingBannerStrategy(accountPreferenceCache)

    given("::bannerName") {
        sut.bannerName shouldBe MarketingBannerName.OPT_INTO_MARKETING
    }

    given("a request to compute the marketing opt in banner") {

        and("customer has opted into marketing") {
            every { accountPreferenceCache.getAccountPreferences(ACCOUNT_ID) } returns getAccountPreferencesResponse {
                preferences.addAll(listOf(emailContactPreference))
            }

            `when`("method is called") {
                val result = sut.computeBanner(ACCOUNT_ID, accountStates)

                then("null is returned") {
                    verify { accountPreferenceCache.getAccountPreferences(ACCOUNT_ID) }
                    result shouldBe null
                }
            }
        }

        and("customer has not opted into marketing") {
            every { accountPreferenceCache.getAccountPreferences(ACCOUNT_ID) } returns getAccountPreferencesResponse {
                preferences.addAll(listOf(testMarketingPreference))
            }

            `when`("method is called") {
                val result = sut.computeBanner(ACCOUNT_ID, accountStates)

                then("banner is returned") {
                    verify { accountPreferenceCache.getAccountPreferences(ACCOUNT_ID) }
                    result shouldBe optIntoMarketingBanner
                }
            }
        }
    }
})
