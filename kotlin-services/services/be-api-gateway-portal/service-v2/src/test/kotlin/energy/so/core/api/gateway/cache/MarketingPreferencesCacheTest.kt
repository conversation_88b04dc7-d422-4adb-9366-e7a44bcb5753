package energy.so.core.api.gateway.cache

import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.ACCOUNT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.emailContactPreference
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.testMarketingPreference
import energy.so.customers.v2.preferences.AccountPreferencesGrpc
import energy.so.customers.v2.preferences.getAccountPreferencesRequest
import energy.so.customers.v2.preferences.getAccountPreferencesResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class MarketingPreferencesCacheTest : BehaviorSpec({

    val mockPreferencesClient = mockk<AccountPreferencesGrpc.AccountPreferencesBlockingStub>()

    val sut = AccountPreferencesCache(mockPreferencesClient)

    afterEach {
        confirmVerified(mockPreferencesClient)
        clearAllMocks()
    }

    given("a request to retrieve marketing preferences") {

        val request = getAccountPreferencesRequest {
            this.accountId = ACCOUNT_ID
        }

        val response = getAccountPreferencesResponse {
            preferences.addAll(listOf(emailContactPreference, testMarketingPreference))
        }

        `when`("the request is made") {

            and("the value is not cached") {

                sut.clear()

                every { mockPreferencesClient.getAccountPreferences(request) } returns response

                val preferences = sut.getAccountPreferences(ACCOUNT_ID)

                // This should be pulled from the cache
                val preferences2 = sut.getAccountPreferences(ACCOUNT_ID)

                then("the value should be retrieved and cached") {
                    verify(exactly = 1) { mockPreferencesClient.getAccountPreferences(request) }
                    preferences shouldBe preferences2
                    preferences.preferencesList.size shouldBe 2
                }
            }

            and("the value is cached") {

                sut.clear()
                sut.populate(ACCOUNT_ID, response)

                val preferences = sut.getAccountPreferences(ACCOUNT_ID)

                then("the value should be retrieved") {
                    verify(exactly = 0) { mockPreferencesClient.getAccountPreferences(request) }
                    preferences.preferencesList.size shouldBe 2
                }
            }
        }
    }
})
