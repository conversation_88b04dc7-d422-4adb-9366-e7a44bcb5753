package energy.so.core.api.gateway.service.alerts.marketingbanners

import energy.so.core.api.gateway.cache.AccountPreferencesCache
import energy.so.core.api.gateway.service.alerts.marketingbanners.MarketingBanners.updateMarketingInterests
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.ACCOUNT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AlertsPrecannedData.accountStates
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.emailContactPreference
import energy.so.core.portal.api.gateway.v2.fixtures.PreferencesPrecannedData.testMarketingPreference
import energy.so.customers.v2.preferences.copy
import energy.so.customers.v2.preferences.getAccountPreferencesResponse
import energy.so.dotdigital.client.DotDigitalV2Exception
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class MarketingInterestsBannerStrategyTest : BehaviorSpec({

    val mockPreferencesCache = mockk<AccountPreferencesCache>()

    val sut = MarketingInterestsBannerStrategy(mockPreferencesCache)

    afterEach {
        confirmVerified(mockPreferencesCache)
    }

    given("a request to compute the marketing banner") {

        `when`("the user is opted into email and has not set any marketing preferences") {

            every { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) } returns getAccountPreferencesResponse {
                preferences.addAll(listOf(emailContactPreference, testMarketingPreference.copy {
                    value = "false"
                }))
            }

            val banner = sut.computeBanner(ACCOUNT_ID, accountStates)

            then("the banner should show") {
                verify { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) }
                banner shouldBe updateMarketingInterests
            }
        }

        `when`("the user is opted into email and has set a marketing preference") {

            every { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) } returns getAccountPreferencesResponse {
                preferences.addAll(listOf(emailContactPreference, testMarketingPreference))
            }

            val banner = sut.computeBanner(ACCOUNT_ID, accountStates)

            then("the banner should not show") {
                verify { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) }
                banner shouldBe null
            }
        }

        `when`("the user not opted into email") {

            every { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) } returns getAccountPreferencesResponse {
                preferences.addAll(listOf(emailContactPreference.copy {
                    value = "false"
                }, testMarketingPreference.copy {
                    value = "false"
                }))
            }

            val banner = sut.computeBanner(ACCOUNT_ID, accountStates)

            then("the banner should not show") {
                verify { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) }
                banner shouldBe null
            }
        }

        `when`("the call to retrieve the account preferences fails") {

            every { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) } throws DotDigitalV2Exception("Oops")

            val banner = sut.computeBanner(ACCOUNT_ID, accountStates)

            then("no banner should be returned") {
                verify { mockPreferencesCache.getAccountPreferences(ACCOUNT_ID) }
                banner shouldBe null
            }
        }
    }
})
