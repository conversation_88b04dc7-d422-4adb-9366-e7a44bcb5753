package energy.so.core.api.gateway.service

import energy.so.ac.junifer.v1.assets.AssetServiceGrpc
import energy.so.ac.junifer.v1.assets.EstimatedUsage
import energy.so.ac.junifer.v1.meterpoint.MeterPointServiceGrpc
import energy.so.assets.meterPoints.v2.MeterPointsGrpc.MeterPointsBlockingStub
import energy.so.assets.meterPoints.v2.copy
import energy.so.assets.meterPoints.v2.getMeterPointsRequest
import energy.so.assets.meterReadings.v2.MeterReadingsGrpc.MeterReadingsBlockingStub
import energy.so.assets.meterReadings.v2.meterReadingStatusRequest
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.dtos.idRequestList
import energy.so.core.api.gateway.service.accountstates.strategies.SupplyStateStrategy
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.ACCOUNT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.METER_POINT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.METER_POINT_ID_2
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.PRODUCT_VARIANT_ID
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.billingAccountResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.estimatedUsageResponseHH
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.estimatedUsageResponseWithNoData
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.findEstimatedUsageResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.getAccountResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.productDetailsResponseProto
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.renewWindowRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AccountPrecannedData.renewWindowResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.METER_POINT_IDENTIFIER_2
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreement
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreementResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreements
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreementsResponseHH
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.agreementsResponseHHNoAnnualCost
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.fixedAgreement
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.fixedAgreementsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.localDateNow
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.meterPointsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.nonrenewableAgreementsResponseForFutureDated
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.productDetails
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.productDetailsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.productDetailsResponseHH
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.unrenewableAgreementsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.variableAgreement
import energy.so.core.portal.api.gateway.v2.fixtures.AgreementsPrecannedData.variableAgreementsResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.defaultMeterRegisterDay
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.defaultMeterRegisterNight
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.elecMeterPoint
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.frequencyRequest
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.frequencyResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.meterResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.readingSubmissionResponse
import energy.so.core.portal.api.gateway.v2.fixtures.AssetsPrecannedData.smartMeterNoRegister
import energy.so.core.portal.api.gateway.v2.fixtures.FinancialsPrecannedData.billingAccountTransactionProto
import energy.so.customers.agreements.v2.Agreement
import energy.so.customers.agreements.v2.AgreementsGrpc
import energy.so.customers.agreements.v2.copy
import energy.so.customers.agreements.v2.getAgreementByMeterpointIdRequest
import energy.so.customers.billingaccounts.v2.BillingAccountsGrpc.BillingAccountsBlockingStub
import energy.so.customers.v2.configurations.ConfigurationName
import energy.so.customers.v2.configurations.ConfigurationsServiceGrpc
import energy.so.customers.v2.configurations.GetConfigurationResponse
import energy.so.customers.v2.configurations.getConfigurationResponse
import energy.so.customers.v2.customers.CustomersGrpc
import energy.so.generated.portal.model.SupplyState
import energy.so.payments.v2.BillingAccountTransactionsGrpc.BillingAccountTransactionsBlockingStub
import energy.so.payments.v2.billingAccountTransactionsResponse
import energy.so.products.v2.GetProductDetailsResponse
import energy.so.products.v2.ProductDetailsServiceGrpc.ProductDetailsServiceBlockingStub
import energy.so.products.v2.copy
import energy.so.products.v2.getProductDetailsResponse
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

@MockKExtension.ConfirmVerification
class AccountServiceTest : BehaviorSpec({

    val billingAccountClient = mockk<BillingAccountsBlockingStub>()
    val billingAccountTransactionsClient = mockk<BillingAccountTransactionsBlockingStub>()
    val customerClient = mockk<CustomersGrpc.CustomersBlockingStub>()
    val meterPointsClient = mockk<MeterPointsBlockingStub>()
    val meterReadingClient = mockk<MeterReadingsBlockingStub>()
    val productDetailsServiceClient = mockk<ProductDetailsServiceBlockingStub>()
    val accountUtilityService = mockk<AccountUtilityService>()
    val supplyStateStrategy = mockk<SupplyStateStrategy>()
    val configurationsClient = mockk<ConfigurationsServiceGrpc.ConfigurationsServiceBlockingStub>()
    val agreementsClient = mockk<AgreementsGrpc.AgreementsBlockingStub>()
    val assetServiceClient = mockk<AssetServiceGrpc.AssetServiceBlockingStub>()
    val mockAcMeterPointClient = mockk<MeterPointServiceGrpc.MeterPointServiceBlockingStub>()

    val sut = AccountService(
        billingAccountTransactionsClient,
        billingAccountClient,
        customerClient,
        meterPointsClient,
        meterReadingClient,
        productDetailsServiceClient,
        accountUtilityService,
        supplyStateStrategy,
        configurationsClient = configurationsClient,
        agreementsClient = agreementsClient,
        assetServiceClient = assetServiceClient,
        acMeterPointClient = mockAcMeterPointClient,
    )

    afterTest {
        clearAllMocks()
    }

    fun mocksForAgreements(
        supplyState: SupplyState = SupplyState.ON_SUPPLY,
        productDetailsResp: GetProductDetailsResponse = productDetailsResponse,
        renewalWindowResponse: GetConfigurationResponse = renewWindowResponse,
        agreementByMeterpointResponse: Agreement = agreement,
        agreementsByProductAccountResponse: List<Agreement> = agreements,
        estimatedUsage: EstimatedUsage = findEstimatedUsageResponse,
    ) {
        every {
            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
        } returns agreementsByProductAccountResponse

        every {
            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                idRequestList {
                    idRequests.add(PRODUCT_VARIANT_ID)
                })
        } returns productDetailsResp

        every {
            meterPointsClient.getMeterPointsByIds(getMeterPointsRequest {
                ids.addAll(listOf(METER_POINT_ID, METER_POINT_ID_2))
                includeMeterReadings = false
            })
        } returns meterPointsResponse

        every {
            configurationsClient.getConfiguration(renewWindowRequest)
        } returns renewalWindowResponse

        every {
            agreementsClient.getAgreementByMeterpointId(getAgreementByMeterpointIdRequest {
                id = METER_POINT_ID
                firstAgreement = false
                includeCancelledAndDeletedAgreements = false
                includeFutureAgreements = true
            })
        } returns agreementByMeterpointResponse

        every {
            agreementsClient.getAgreementByMeterpointId(getAgreementByMeterpointIdRequest {
                id = METER_POINT_ID_2
                firstAgreement = false
                includeCancelledAndDeletedAgreements = false
                includeFutureAgreements = true
            })
        } returns agreementByMeterpointResponse

        every {
            supplyStateStrategy.computeState(ACCOUNT_ID)
        } returns supplyState

        every {
            assetServiceClient.getEstimatedUsage(any())
        } returns estimatedUsage
    }



    given("get account") {

        and("valid request") {
            every {
                billingAccountClient.getBillingAccountById(idRequest {
                    id = ACCOUNT_ID
                })
            } returns billingAccountResponse
            every {
                billingAccountTransactionsClient.getByBillingAccountId(idRequest { id = ACCOUNT_ID })
            } returns billingAccountTransactionsResponse {
                billingAccountTransaction.addAll(listOf(billingAccountTransactionProto))
            }
            every {
                customerClient.getProductDetailsByBillingAccountId(idRequest {
                    id = ACCOUNT_ID
                })
            } returns productDetailsResponseProto
            every { supplyStateStrategy.computeState(ACCOUNT_ID) } returns SupplyState.ON_SUPPLY

            `when`("get account by id is called") {
                val result = sut.getAccount(ACCOUNT_ID)

                then("valid response is returned") {
                    verify { billingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                    verify {
                        billingAccountTransactionsClient.getByBillingAccountId(idRequest {
                            id = ACCOUNT_ID
                        })
                    }
                    verify { customerClient.getProductDetailsByBillingAccountId(idRequest { id = ACCOUNT_ID }) }
                    result shouldBe getAccountResponse
                }
            }
        }

        and("request to get billing account fails") {
            every {
                billingAccountTransactionsClient.getByBillingAccountId(idRequest { id = ACCOUNT_ID })
            } returns billingAccountTransactionsResponse {
                billingAccountTransaction.addAll(listOf(billingAccountTransactionProto))
            }
            every {
                customerClient.getProductDetailsByBillingAccountId(idRequest {
                    id = ACCOUNT_ID
                })
            } returns productDetailsResponseProto
            every {
                billingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID })
            } throws UnknownGrpcException(
                message = "something went wrong",
                errorCode = ErrorCodes.COMMONS_ENTITY_NOT_FOUND,
                errorCategory = ErrorCategories.CUSTOMERS,
            )
            every { supplyStateStrategy.computeState(ACCOUNT_ID) } returns SupplyState.ON_SUPPLY

            `when`("get account by id is called") {
                then("exception is thrown") {
                    shouldThrow<UnknownGrpcException> {
                        sut.getAccount(ACCOUNT_ID)
                    }
                    verify {
                        billingAccountTransactionsClient.getByBillingAccountId(idRequest {
                            id = ACCOUNT_ID
                        })
                    }
                    verify { customerClient.getProductDetailsByBillingAccountId(idRequest { id = ACCOUNT_ID }) }
                    verify { billingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                }
            }
        }
    }



    given("get meters for account") {

        and("meter has registers") {

            and("meters/registers have readings") {
                every {
                    accountUtilityService.getMeterPointsByBillingAccount(
                        ACCOUNT_ID,
                        AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                    )
                } returns listOf(elecMeterPoint)

                every {
                    meterReadingClient.getLastMeterReadingStatus(meterReadingStatusRequest {
                        meterPointId = ACCOUNT_ID
                    })
                } returns readingSubmissionResponse

                every { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) } returns frequencyResponse

                `when`("get meters for account id") {
                    val result = sut.getMetersByBillingAccountId(ACCOUNT_ID)

                    then("valid meter response is returned") {
                        result shouldBe listOf(meterResponse)
                        verify {
                            accountUtilityService.getMeterPointsByBillingAccount(
                                ACCOUNT_ID,
                                AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                            )
                        }
                        verify {
                            meterReadingClient.getLastMeterReadingStatus(
                                meterReadingStatusRequest {
                                    meterPointId = ACCOUNT_ID
                                }
                            )
                        }
                        verify { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) }
                    }
                }
            }

            and("meters/registers have no readings") {
                every {
                    accountUtilityService.getMeterPointsByBillingAccount(
                        ACCOUNT_ID,
                        AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                    )
                } returns listOf(elecMeterPoint)

                every {
                    meterReadingClient.getLastMeterReadingStatus(
                        meterReadingStatusRequest {
                            meterPointId = ACCOUNT_ID
                        }
                    )
                } throws EntityNotFoundGrpcException("not found")

                every { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) } returns frequencyResponse

                `when`("get meters for account id") {
                    val result = sut.getMetersByBillingAccountId(ACCOUNT_ID)

                    then("valid meter response is returned without readings present") {
                        result shouldBe listOf(meterResponse.copy(pendingSubmissionStatus = null))
                        verify {
                            accountUtilityService.getMeterPointsByBillingAccount(
                                ACCOUNT_ID,
                                AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                            )
                        }
                        verify {
                            meterReadingClient.getLastMeterReadingStatus(
                                meterReadingStatusRequest {
                                    meterPointId = ACCOUNT_ID
                                }
                            )
                        }
                        verify { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) }
                    }
                }
            }

            and("unknown exception occurs requesting readings") {
                every {
                    accountUtilityService.getMeterPointsByBillingAccount(
                        ACCOUNT_ID,
                        AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                    )
                } returns listOf(elecMeterPoint)

                every {
                    meterReadingClient.getLastMeterReadingStatus(meterReadingStatusRequest {
                        meterPointId = ACCOUNT_ID
                    })
                } throws UnknownGrpcException("unknown", "error", "code")

                every { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) } returns frequencyResponse

                `when`("get meters for account id") {
                    val result = sut.getMetersByBillingAccountId(ACCOUNT_ID)

                    then("valid meter response is returned without readings present") {
                        result shouldBe listOf(meterResponse.copy(pendingSubmissionStatus = null))
                        verify {
                            accountUtilityService.getMeterPointsByBillingAccount(
                                ACCOUNT_ID,
                                AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                            )
                        }
                        verify {
                            meterReadingClient.getLastMeterReadingStatus(
                                meterReadingStatusRequest {
                                    meterPointId = ACCOUNT_ID
                                }
                            )
                        }
                        verify { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) }
                    }
                }

            }
        }

        and("meter has no registers") {

            val meterPointNoRegisters = elecMeterPoint.copy {
                meters.clear()
                meters.add(smartMeterNoRegister)
            }

            every {
                accountUtilityService.getMeterPointsByBillingAccount(
                    ACCOUNT_ID,
                    AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                )
            } returns listOf(meterPointNoRegisters)

            every {
                meterReadingClient.getLastMeterReadingStatus(
                    meterReadingStatusRequest {
                        meterPointId = ACCOUNT_ID
                    }
                )
            } returns readingSubmissionResponse

            every { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) } returns frequencyResponse

            `when`("get meters for account id") {
                val result = sut.getMetersByBillingAccountId(ACCOUNT_ID)

                then("valid meter response with default registers is returned") {
                    result shouldBe listOf(
                        meterResponse.copy(
                            registers = listOf(
                                defaultMeterRegisterDay,
                                defaultMeterRegisterNight
                            ),
                            hasMeterTechnicalDetails = false
                        )
                    )
                    verify {
                        accountUtilityService.getMeterPointsByBillingAccount(
                            ACCOUNT_ID,
                            AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                        )
                    }
                    verify {
                        meterReadingClient.getLastMeterReadingStatus(
                            meterReadingStatusRequest {
                                meterPointId = ACCOUNT_ID
                            }
                        )
                    }
                    verify { mockAcMeterPointClient.getMeterPointReadFrequency(frequencyRequest) }
                }
            }
        }

        and("request fails to get product accounts") {
            every {
                accountUtilityService.getMeterPointsByBillingAccount(
                    ACCOUNT_ID,
                    AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                )
            } throws UnknownGrpcException(
                message = "something went wrong",
                errorCode = ErrorCodes.COMMONS_ENTITY_NOT_FOUND,
                errorCategory = ErrorCategories.CUSTOMERS,
            )

            `when`("get meters for account id") {
                then("exception is thrown") {
                    shouldThrow<UnknownGrpcException> {
                        sut.getMetersByBillingAccountId(ACCOUNT_ID)
                    }
                    verify {
                        accountUtilityService.getMeterPointsByBillingAccount(
                            ACCOUNT_ID,
                            AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                        )
                    }
                }
            }
        }

        and("request fails to get meter points") {
            every {

                accountUtilityService.getMeterPointsByBillingAccount(
                    ACCOUNT_ID,
                    AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                )
            } throws UnknownGrpcException(
                message = "something went wrong",
                errorCode = ErrorCodes.COMMONS_ENTITY_NOT_FOUND,
                errorCategory = ErrorCategories.CUSTOMERS,
            )

            `when`("get meters for account id") {
                then("exception is thrown") {
                    shouldThrow<UnknownGrpcException> {
                        sut.getMetersByBillingAccountId(ACCOUNT_ID)
                    }
                    verify {
                        accountUtilityService.getMeterPointsByBillingAccount(
                            ACCOUNT_ID,
                            AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
                        )
                    }
                }
            }
        }
    }

    given("get agreements") {

        and("request is valid") {
            mocksForAgreements(
                supplyState = SupplyState.ON_SUPPLY,
                agreementByMeterpointResponse = variableAgreement,
                agreementsByProductAccountResponse = listOf(variableAgreement),
            )
            and("renewal conditions met for variable tariff") {
                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe variableAgreementsResponse

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("first day of renewal window for fixed tariff") {
                mocksForAgreements(
                    supplyState = SupplyState.ON_SUPPLY,
                    agreementsByProductAccountResponse = listOf(fixedAgreement),
                    agreementByMeterpointResponse = fixedAgreement
                )
                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe fixedAgreementsResponse

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("account not on supply") {
                mocksForAgreements(supplyState = SupplyState.ONBOARDING)

                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe unrenewableAgreementsResponse

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("tariff is TOU Product") {
                mocksForAgreements(
                    productDetailsResp = productDetailsResponseHH,
                    estimatedUsage = estimatedUsageResponseHH,
                    agreementByMeterpointResponse = variableAgreement,
                    agreementsByProductAccountResponse = listOf(variableAgreement),
                )

                `when`("service is called and estimated usage returned from junifer") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe agreementsResponseHH

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("tariff is TOU Product") {
                mocksForAgreements(
                    productDetailsResp = productDetailsResponseHH,
                    estimatedUsage = estimatedUsageResponseWithNoData,
                    agreementByMeterpointResponse = variableAgreement,
                    agreementsByProductAccountResponse = listOf(variableAgreement),
                )

                `when`("service is called and no estimated usage returned from junifer") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe agreementsResponseHHNoAnnualCost

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("account is terminating") {

                mocksForAgreements(supplyState = SupplyState.LOSS_INITIATED)

                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe unrenewableAgreementsResponse

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }
            }

            and("not in renewal period") {

                and("agreement not open ended") {
                    mocksForAgreements(
                        renewalWindowResponse = getConfigurationResponse {
                            configurationValue = "1"
                            configurationName = ConfigurationName.RENEWAL_WINDOW
                        },
                        agreementsByProductAccountResponse = listOf(fixedAgreement),
                        agreementByMeterpointResponse = fixedAgreement
                    )

                    `when`("service is called") {
                        val response = sut.getAgreements(ACCOUNT_ID)

                        then("response ok") {
                            response shouldBe fixedAgreementsResponse.map {
                                it.copy(
                                    canRenew = false, renewalFromDate = it.endDate?.minusDays(1)
                                )
                            }

                            verify {
                                accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                            }
                            verify {
                                productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                    idRequestList {
                                        idRequests.add(PRODUCT_VARIANT_ID)
                                    })
                            }
                        }
                    }

                }

                and("agreement open-ended") {

                    mocksForAgreements(
                        renewalWindowResponse = getConfigurationResponse {
                            configurationValue = "1"
                            configurationName = ConfigurationName.RENEWAL_WINDOW
                        }
                    )

                    `when`("service is called") {
                        val response = sut.getAgreements(ACCOUNT_ID)

                        then("response ok") {
                            response shouldBe listOf(
                                agreementResponse.copy(
                                    renewalFromDate = agreementResponse.endDate?.minusDays(1),
                                ),
                                agreementResponse.copy(
                                    renewalFromDate = agreementResponse.endDate?.minusDays(1),
                                    meterpointID = METER_POINT_ID_2,
                                    meterpointIdentifier = METER_POINT_IDENTIFIER_2
                                )
                            )

                            verify {
                                accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                            }
                            verify {
                                productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                    idRequestList {
                                        idRequests.add(PRODUCT_VARIANT_ID)
                                    })
                            }
                        }
                    }

                }
            }

            and("product is export") {

                mocksForAgreements(
                    productDetailsResp = getProductDetailsResponse {
                        results.add(productDetails.copy { export = true })
                    }
                )

                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe listOf(
                            agreementResponse.copy(
                                canRenew = false,
                                isExportTariff = true
                            ),
                            agreementResponse.copy(
                                canRenew = false,
                                isExportTariff = true,
                                meterpointID = METER_POINT_ID_2,
                                meterpointIdentifier = METER_POINT_IDENTIFIER_2
                            )
                        )

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }

            }

            and("future agreement exists") {

                mocksForAgreements(
                    agreementByMeterpointResponse = agreement.copy {
                        fromDate = LocalDate.now().plusDays(1).toTimestamp()
                    }
                )

                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe unrenewableAgreementsResponse

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }

            }

            and("future agreement exists while other conditions are ok for renewal") {

                mocksForAgreements(
                    agreementByMeterpointResponse = agreement.copy {
                        fromDate = localDateNow.plusDays(1).toTimestamp()
                    },
                    agreementsByProductAccountResponse = listOf(agreement.copy {
                        toDate = localDateNow.plusDays(renewWindowResponse.configurationValue.toLong() - 1)
                            .toNullableTimestamp()
                    })
                )

                `when`("service is called") {
                    val response = sut.getAgreements(ACCOUNT_ID)

                    then("response ok") {
                        response shouldBe nonrenewableAgreementsResponseForFutureDated

                        verify {
                            accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                        }
                        verify {
                            productDetailsServiceClient.getProductDetailsByProductVariantIds(
                                idRequestList {
                                    idRequests.add(PRODUCT_VARIANT_ID)
                                })
                        }
                    }
                }

            }
        }

        and("exception thrown from server") {
            every {
                accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
            } throws UnknownGrpcException(
                message = "unknown error",
                errorCategory = ErrorCategories.PRODUCTS,
                errorCode = ErrorCodes.COMMONS_UNHANDLED_ERROR
            )

            every {
                billingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID })
            } returns billingAccountResponse

            every {
                supplyStateStrategy.computeState(ACCOUNT_ID)
            } returns SupplyState.ON_SUPPLY

            every {
                configurationsClient.getConfiguration(renewWindowRequest)
            } returns renewWindowResponse

            `when`("service is called") {
                then("exception is thrown") {

                    shouldThrow<UnknownGrpcException> {
                        sut.getAgreements(ACCOUNT_ID)
                    }

                    verify {
                        accountUtilityService.getAgreementsByProductAccount(ACCOUNT_ID, true)
                    }

                    verify {
                        productDetailsServiceClient.getProductDetailsByProductVariantIds(any()) wasNot Called
                    }
                }
            }
        }
    }
})
