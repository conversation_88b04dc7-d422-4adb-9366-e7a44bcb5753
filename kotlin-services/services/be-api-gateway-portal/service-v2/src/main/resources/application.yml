server:
  port: ${BE_API_GATEWAY_PORTAL_V2_SERVER_PORT:50051}
  max-http-request-header-size: ${BE_API_GATEWAY_PORTAL_V2_MAX_REQUEST_HEADER_SIZE:12000}
environment:
  environment: ${BE_APP_API_GATEWAY_PORTAL_V2_ENV:development}

springdoc:
  swagger-ui:
    url: /openapi.json

spring:
  autoconfigure:
    exclude:
      - net.devh.boot.grpc.client.autoconfigure.GrpcClientMetricAutoConfiguration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: 6379
      password: ${REDIS_AUTH_STRING:b1558e84-9c09-48e3-bdb7-64861617a0b8}
      repositories:
        enabled: false

redis:
  topic:
    meterReadingStatus: meter-reading-status

grpc:
  client:
    identityService:
      address: ${BE_IDENTITY_HOST:localhost}:${BE_IDENTITY_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_IDENTITY_TIMEOUT:10}
    financialsService:
      address: ${BE_FINANCIALS_HOST:localhost}:${BE_FINANCIALS_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_FINANCIALS_TIMEOUT:10}
    customersService:
      address: ${BE_CUSTOMERS_HOST:localhost}:${BE_CUSTOMERS_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_CUSTOMERS_TIMEOUT:10}
    acJuniferService:
      address: ${BE_AC_JUNIFER_HOST:localhost}:${BE_AC_JUNIFER_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_AC_JUNIFER_TIMEOUT:50}
    assetsService:
      address: ${BE_ASSETS_HOST:localhost}:${BE_ASSETS_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_ASSETS_TIMEOUT:10}
    productService:
      address: ${BE_PRODUCTS_HOST:localhost}:${BE_PRODUCTS_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_PRODUCTS_TIMEOUT:50}
    commsHubService:
      address: ${BE_COMMS_HUB_HOST:localhost}:${BE_COMMS_HUB_PORT:50051}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: plaintext
      callDeadlineInSeconds: ${BE_COMMS_HUB_TIMEOUT:50}

payments:
  accepted-payment-redirect-domains: ${ACCEPTED_PAYMENT_REDIRECT_DOMAINS:so.energy,soenergy.co}

caches:
  account-preference-cache-expiry: ${ACCOUNT_PREFERENCE_CACHE_EXPIRY:300}