{"openapi": "3.0.1", "info": {"title": "Portal API Gateway", "version": "2.0.0"}, "servers": [{"url": "https://server.com", "description": "New server"}, {"url": "https://portal-api-gateway-v2.so.energy", "description": "Production"}, {"url": "https://portal-api-gateway-v2.staging.soenergy.co", "description": "Staging"}, {"url": "https://portal-api-gateway-v2.pre-production.soenergy.co", "description": "Pre-Production"}, {"url": "http://localhost:8090", "description": "Local"}], "tags": [{"name": "Authentication", "description": "Endpoints related to authenticating a user with the gateway."}, {"name": "Account", "description": "Endpoints related to accounts and account management."}, {"name": "Account State", "description": "Endpoints related to retrieval of account state."}, {"name": "Address", "description": "Endpoints related to the retrieval of addresses."}, {"name": "<PERSON><PERSON><PERSON>", "description": "Endpoints related alerting."}, {"name": "Bill", "description": "Endpoints related bills and billing."}, {"name": "Change Of Tenancy", "description": "Endpoints related to Change of Tenancy (COT)."}, {"name": "Customer", "description": "Endpoints related to customers."}, {"name": "Config", "description": "Endpoints related to configuration."}, {"name": "Energy Usage", "description": "Endpoints related to retrieving energy usage."}, {"name": "Ev Tariff", "description": "Endpoints related to the retrieval of EV Tariffs."}, {"name": "Innovation Tariff", "description": "Endpoints related alerting."}, {"name": "Meter Point", "description": "Endpoints related to the manipulation of meter points."}, {"name": "Meter Readings", "description": "Endpoints related to the manipulation of meter readings."}, {"name": "Payments", "description": "Endpoints related to payments."}, {"name": "Preferences", "description": "Endpoints related to managing account preferences."}, {"name": "Quotes", "description": "Endpoints related to quoting."}, {"name": "Smart Meter", "description": "Endpoints related to smart meters."}, {"name": "Smart Pay As You Go", "description": "Endpoints related to Smart Pay As You Go registration."}], "paths": {"/api/v1/auth/login": {"post": {"operationId": "Authenticate", "tags": ["Authentication"], "summary": "Authenticate a user", "parameters": [{"in": "header", "name": "User-Agent", "schema": {"type": "string"}}, {"in": "header", "name": "Origin", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationRequest"}}}}, "responses": {"200": {"description": "Valid authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResponse"}}}, "headers": {"Set-Cookie": {"schema": {"type": "string", "example": "JSESSIONID=abcde12345; Path=/; HttpOnly"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/auth/password": {"post": {"operationId": "Update password", "tags": ["Authentication"], "summary": "Update password for authenticated user. If no authenticated user, update by token only if provided in payload", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordRequest"}}}}, "responses": {"200": {"description": "Successfully updated password", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordResponse"}}}, "headers": {"Set-Cookie": {"schema": {"type": "string", "example": "JSESSIONID=abcde12345; Path=/; HttpOnly"}}}}, "4XX": {"description": "Failed to update password for user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/auth/logout": {"post": {"operationId": "Logout", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "summary": "Logout a user", "responses": {"200": {"description": "User logged out", "headers": {"Set-Cookie": {"schema": {"type": "string", "example": "JSESSIONID=abcde12345; Path=/; HttpOnly"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "parameters": [{"in": "header", "name": "Authorization", "schema": {"type": "string", "example": "Bearer: token"}, "required": true}, {"in": "cookie", "name": "auth_refresh_token", "schema": {"type": "string", "example": "Bearer: token"}, "required": true}]}, "/api/v1/auth/refresh": {"post": {"operationId": "refreshToken", "tags": ["Authentication"], "summary": "Refresh token", "parameters": [{"in": "cookie", "name": "auth_refresh_token", "schema": {"type": "string"}}, {"in": "header", "name": "Origin", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Valid authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/auth/sendActivationEmail": {"post": {"operationId": "sendActivationEmail", "tags": ["Authentication"], "summary": "Re-send activation email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendActivationEmailRequest"}}}}, "responses": {"204": {"description": "Activation email was sent successfully"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/auth/sendPasswordResetEmail": {"post": {"operationId": "sendPasswordResetEmail", "tags": ["Authentication"], "summary": "Send a reset password email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendResetPasswordEmailRequest"}}}}, "responses": {"204": {"description": "Reset password email was sent successfully"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/transactions": {"get": {"operationId": "getTransactionsV2", "tags": ["Payments"], "security": [{"bearerAuth": []}], "x-spring-paginated": true, "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "description": "Number of items to return", "required": true, "in": "query", "schema": {"type": "integer"}}, {"name": "offset", "description": "Page number to return", "required": true, "in": "query", "schema": {"type": "integer"}}, {"name": "from", "in": "query", "required": false, "description": "filter transactions from date", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "required": false, "description": "filter transactions to date", "schema": {"type": "string", "format": "date-time"}}], "summary": "Get transactions for account", "responses": {"200": {"description": "Transactions returned", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTransactionsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/agreements": {"get": {"operationId": "GetAgreements", "tags": ["Account"], "security": [{"bearerAuth": []}], "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "summary": "Get agreements for account", "responses": {"200": {"description": "Agreements returned", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgreementsResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}": {"get": {"operationId": "getAccount", "tags": ["Account"], "security": [{"bearerAuth": []}], "summary": "Get account information", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Account Information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAccountResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/alerts-banners": {"get": {"operationId": "getAlertsAndBanners", "tags": ["<PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "summary": "Get the alerts and marketing banners that are active for this account", "parameters": [{"in": "path", "name": "accountId", "schema": {"type": "integer", "format": "int64"}, "required": true}, {"in": "query", "name": "alertTag", "description": "Possible values: home, tariff. See AlertTagEnum for all valid values.", "schema": {"type": "string"}, "required": false}, {"in": "cookie", "name": "deactivated_alerts_cookie", "schema": {"type": "string", "example": "[\n  {\n    \"accountId\": \"123\",\n    \"alerts\": [\n      {\n        \"alertId\": \"openingMeterReading\",\n        \"timestamp\": \"2024-04-05T12:12:12\"\n      }\n    ],\n    \"banners\" :[\n    { \n      \"bannerId\":\"marketing\",\n      \"timestamp\": \"2024-04-05T12:12:12\"\n    }\n   ]\n  }\n]"}}, {"in": "query", "name": "bannerTag", "description": "Possible values: home, usage. See BannerTagEnum for all valid values.", "schema": {"type": "string"}, "required": false}], "responses": {"200": {"description": "The active alerts and marketing banners for the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/alerts/deactivate": {"post": {"operationId": "deactivate<PERSON>lert", "tags": ["<PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "summary": "Deactivate an alert", "parameters": [{"in": "path", "name": "accountId", "schema": {"type": "integer", "format": "int64"}, "required": true}, {"in": "cookie", "name": "deactivated_alerts_cookie", "schema": {"type": "string", "example": "[\n  {\n    \"accountId\": \"123\",\n    \"alerts\": [\n      {\n        \"alertId\": \"openingMeterReading\",\n        \"timestamp\": \"2024-04-05T12:12:12\"\n      }\n    ],\n    \"banners\" :[\n    { \n      \"bannerId\":\"marketing\",\n      \"timestamp\": \"2024-04-05T12:12:12\"\n    }\n   ]\n  }\n]"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeactivateAlertRequest"}}}}, "responses": {"200": {"description": "The active alerts and marketing banners for the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/bills/{billId}/download": {"get": {"operationId": "getBillFileByIdAndAccountIdV2", "tags": ["Bill"], "security": [{"bearerAuth": []}], "summary": "Get an account's file bill by id and account id", "parameters": [{"in": "path", "name": "accountId", "schema": {"type": "integer", "format": "int64"}, "required": true}, {"in": "path", "name": "billId", "schema": {"type": "integer", "format": "int64"}, "required": true}], "responses": {"200": {"description": "Link to download the bill PDF", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillFileLinkResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/bills/{billId}": {"get": {"operationId": "getBillByIdAndAccountIdV2", "tags": ["Bill"], "security": [{"bearerAuth": []}], "summary": "Get an account's bill by id and account id", "parameters": [{"in": "path", "name": "accountId", "schema": {"type": "integer", "format": "int64"}, "required": true}, {"in": "path", "name": "billId", "schema": {"type": "integer", "format": "int64"}, "required": true}], "responses": {"200": {"description": "Bill", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bill"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/bills": {"get": {"operationId": "getBillsSummaryByAccountIdV2", "tags": ["Bill"], "security": [{"bearerAuth": []}], "summary": "Get bills summary for an account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Bills Summary of the account", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Bill<PERSON><PERSON><PERSON>y"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/meterReadingReminder": {"get": {"operationId": "getMeterReadingReminderV2", "tags": ["Meter Readings"], "security": [{"bearerAuth": []}], "summary": "Get meter reading reminder frequency for an account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "The meter reading reminder frequency", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeterReadingReminderResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"operationId": "updateMeterReadingReminderV2", "tags": ["Meter Readings"], "security": [{"bearerAuth": []}], "summary": "Update meter reading reminder for an account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMeterReadingReminderRequest"}}}}, "responses": {"200": {"description": "Meter reading reminder updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeterReadingReminderResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/readings": {"get": {"operationId": "getMeterReadingsV2", "tags": ["Meter Readings"], "security": [{"bearerAuth": []}], "x-spring-paginated": true, "summary": "Get a list of filtered meter readings for the provided account. Readings are returned in a paginated manner based on `offset` and `limit`", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "description": "Number of items to return", "required": true, "in": "query", "schema": {"type": "integer"}}, {"name": "offset", "description": "Page number to return", "required": true, "in": "query", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMeterReadingsRequest"}}}}, "responses": {"200": {"description": "The meter readings matching the specified conditions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMeterReadingsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/appointments": {"get": {"operationId": "getMeterAppointmentsV2", "tags": ["Smart Meter"], "security": [{"bearerAuth": []}], "x-spring-paginated": true, "summary": "Get a list of meter appointments created for the account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "The meter appointments created for the account", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetMeterAppointmentResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/customers/me": {"get": {"operationId": "getCustomer", "tags": ["Customer"], "security": [{"bearerAuth": []}], "summary": "Get current customer information", "responses": {"200": {"description": "Current customer information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCustomerResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/customers/{customerId}": {"patch": {"operationId": "updateCustomer", "tags": ["Customer"], "security": [{"bearerAuth": []}], "summary": "Update customer information", "parameters": [{"name": "customerId", "in": "path", "required": true, "description": "The customer ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerRequest"}}}}, "responses": {"200": {"description": "Customer information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCustomerResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/customers/{customerId}/psr": {"post": {"operationId": "storeVulnerabilitiesAndAssociatedServices", "tags": ["Psr"], "security": [{"bearerAuth": []}], "summary": "Store PSR customer vulnerabilities and selected services", "parameters": [{"name": "customerId", "in": "path", "required": true, "description": "The customer ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PsrCustomerSettings"}}}}, "responses": {"200": {"description": "Customer psr stored successfully"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "get": {"operationId": "getPsrCustomerSettings", "tags": ["Psr"], "security": [{"bearerAuth": []}], "summary": "Fetch PSR customer vulnerabilities and selected services for the given customer", "parameters": [{"name": "customerId", "in": "path", "required": true, "description": "The customer ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Customer psr fetched successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PsrCustomerSettingsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/customers/{customerId}/smartMeterReadingFrequency": {"get": {"operationId": "getSmartMeterReadingFrequency", "tags": ["Customer"], "security": [{"bearerAuth": []}], "summary": "Get smart meter reading frequency for customer", "parameters": [{"name": "customerId", "in": "path", "required": true, "description": "The customer ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Smart meter reading frequency", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSmartMeterReadingFrequencyResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"operationId": "updateSmartMeterReadingFrequency", "tags": ["Customer"], "security": [{"bearerAuth": []}], "summary": "Update smart meter reading frequency for customer", "parameters": [{"name": "customerId", "in": "path", "required": true, "description": "The customer ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSmartMeterReadingFrequencyRequest"}}}}, "responses": {"204": {"description": "Smart meter reading frequency updated successfully"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/cancelSwitch": {"post": {"operationId": "cancelSwitchV2", "tags": ["Change Of Tenancy"], "security": [{"bearerAuth": []}], "summary": "Cancels the meter switches of the account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "AccountId ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelSwitchRequest"}}}}, "responses": {"204": {"description": "Cancellation was made successfully"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/config/featureFlags": {"get": {"operationId": "getFeatureFlags", "tags": ["Config"], "security": [{"bearerAuth": []}], "summary": "Get feature flags status", "responses": {"200": {"description": "Feature flags status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFeatureFlagsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/directDebitMandate": {"get": {"operationId": "downloadDirectDebitMandatePdfV2", "tags": ["Payments"], "security": [{"bearerAuth": []}], "summary": "Download the PDF file for direct debit mandate of the account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Direct Debit Mandate PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/paymentScheduleDetails": {"get": {"operationId": "getPaymentScheduleDetailsV2", "tags": ["Payments"], "security": [{"bearerAuth": []}], "summary": "Get current and future paymentSchedules for an account.", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Current and future paymentSchedules for an account.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentScheduleDetails"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/variable-payment-details": {"get": {"operationId": "getVariablePaymentDetailsV2", "tags": ["Payments"], "security": [{"bearerAuth": []}], "summary": "Retrieve payment information related to variable customers", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Information about variable payment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVariablePaymentDetailsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/meters": {"get": {"operationId": "getMetersByBillingAccountId", "tags": ["Account"], "security": [{"bearerAuth": []}], "summary": "Retrieve list of meters for given account", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "List of meters", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MeterResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/state": {"get": {"operationId": "getAccountStates", "tags": ["Account State"], "security": [{"bearerAuth": []}], "summary": "Retrieve account states", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Object containing the account states", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountStates"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/unbilled": {"get": {"operationId": "getUnbilledV2", "tags": ["Bill"], "security": [{"bearerAuth": []}], "summary": "Retrieve details about current charges present in the next bill, excluding standing charges unless requested.", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}, {"name": "includeStandingCharges", "in": "query", "required": false, "description": "Whether to include standing charges in total usage cost.", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Object containing details about pending and next bill", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Unbilled"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/submitReadings": {"post": {"operationId": "submitReadingsV2", "tags": ["Meter Readings"], "security": [{"bearerAuth": []}], "summary": "Submit a meter reading", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MeterReadingWithTechnicalDetails"}}}}}, "responses": {"204": {"description": "Reading submitted"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/energyUsage": {"get": {"operationId": "getEnergyUsageV2", "tags": ["Energy Usage"], "security": [{"bearerAuth": []}], "summary": "Retrieve an accounts energy usage, across a given period, aggregated to a given value.", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}, {"name": "granularity", "in": "query", "required": true, "description": "The granularity that the response should be aggregated to. For example, 'DAY' granularity will return energy usage values for each day, whereas 'YEAR' will return aggregated values for the entire year.", "schema": {"$ref": "#/components/schemas/EnergyUsageGranularity"}}, {"name": "fromDate", "in": "query", "required": true, "description": "From when to query energy usage. This value is exclusive.", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "required": true, "description": "The final date to query energy usage for. This value is inclusive.", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "A successful retrieval of energy usage values.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnergyUsageResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/smartPayAsYouGo/eligibility": {"get": {"operationId": "checkSmartPayAsYouGoEligibility", "tags": ["Smart Pay As You Go"], "security": [{"bearerAuth": []}], "summary": "Retrieve So Pay As You Go Eligibility", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}, {"name": "billingAccountNumber", "in": "query", "required": false, "description": "The Billing Account Number (same as <PERSON><PERSON> Account Number)", "schema": {"$ref": "components.json#/components/schemas/billingAccountNumber"}}], "responses": {"200": {"description": "So Pay As You Go elegibility response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SmartPayAsYouGoEligibilityResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/smartPayAsYouGo/executionDate": {"get": {"operationId": "getSmartPayAsYouGoExecutionDate", "tags": ["Smart Pay As You Go"], "security": [{"bearerAuth": []}], "summary": "Calculate So Pay As You Go Execution date based in configured number of working days", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 2879}}, {"name": "billingAccountNumber", "in": "query", "required": false, "description": "The Billing Account Number (same as <PERSON><PERSON> Account Number)", "schema": {"$ref": "components.json#/components/schemas/billingAccountNumber"}}], "responses": {"200": {"description": "So Pay As You Go execution date response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartPayAsYouGoExecutionDateResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/smartPayAsYouGo/quote": {"post": {"operationId": "createSmartPayAsYouGoQuote", "tags": ["Smart Pay As You Go"], "security": [{"bearerAuth": []}], "summary": "create a quote for Smart Pay As You Go", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The Billing account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartPayAsYouGoQuoteRequest"}}}}, "responses": {"200": {"description": "Smart pay as you go quote response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartPayAsYouGoQuoteResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/smartPayAsYouGo": {"post": {"operationId": "postSmartPayAsYouGo", "tags": ["Smart Pay As You Go"], "security": [{"bearerAuth": []}], "summary": "register for Smart Pay As You Go", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The Billing account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}, {"name": "billingAccountNumber", "in": "query", "required": false, "description": "The Billing Account Number (same as <PERSON><PERSON> Account Number)", "schema": {"$ref": "components.json#/components/schemas/billingAccountNumber"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "components.json#/components/schemas/SmartPayAsYouGoRequest"}}}}, "responses": {"204": {"description": "Smart pay as you go registration sent"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/submitReadingsWithoutMeterTechnicalDetails": {"post": {"operationId": "submitReadingsWithoutMeterTechnicalDetailsV2", "tags": ["Meter Readings"], "security": [{"bearerAuth": []}], "summary": "Submit a meter reading with no meter technical details", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MeterReadingWithoutTechnicalDetails"}}}}}, "responses": {"204": {"description": "Reading submitted"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/dvla/checkVehicleIsEv": {"post": {"operationId": "checkVehicleIsEV", "tags": ["Ev Tariff"], "summary": "Check if a vehicle is an EV based on registration plate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EvCheckRegistrationPlate"}}}}, "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EvCheckResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/addresses/{postcode}": {"get": {"operationId": "addressesByPostcode", "tags": ["Address"], "description": "return a list of addresses for a given postcode with an indication for those with billing accounts", "summary": "return a list of addresses for a given postcode with an indication for those with billing accounts", "parameters": [{"name": "postcode", "in": "path", "required": true, "description": "postcode", "schema": {"$ref": "#/components/schemas/Postcode"}}], "responses": {"200": {"description": "Addresses found", "content": {"application/json": {"schema": {"type": "array", "minItems": 0, "maxItems": 1000, "items": {"$ref": "#/components/schemas/AddressMeterPointDetails"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/meters/details": {"post": {"operationId": "getPublicMetersByIdentifiers", "tags": ["Meter Point"], "description": "return meterPoint type and usage threshold", "summary": "return meterPoint type and usage threshold", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeterpointIdentifiersRequest"}}}}, "responses": {"200": {"description": "MeterPoint found", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PublicMeterResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/meterPoints/{meterPointIdentifier}/ees/checkEvTariffEligibility": {"get": {"operationId": "checkEvTariffEligibilityForMeterPointByIdentifierViaEES", "tags": ["Ev Tariff"], "summary": "Check if a meter is eligible for EV tariff", "parameters": [{"name": "meterPointIdentifier", "in": "path", "required": true, "description": "The meter point identifier", "schema": {"type": "string", "example": "1800060845200"}}], "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EvTariffSmartMeterCheckResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/meterPoints/{meterPointIdentifier}/checkEvTariffEligibility": {"get": {"operationId": "checkEvTariffEligibilityForMeterPointByIdentifier", "tags": ["Ev Tariff"], "summary": "Check if a meter is eligible for EV tariff", "parameters": [{"name": "meterPointIdentifier", "in": "path", "required": true, "description": "The meter point identifier", "schema": {"type": "string", "example": "1800060845200"}}], "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EvTariffSmartMeterCheckResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/change-of-tenancy/move-in": {"post": {"operationId": "submitChangeOfTenancyMoveIn", "description": "change of tenancy move in", "tags": ["Change Of Tenancy"], "summary": "Submit a request for change of tenancy move-in", "requestBody": {"required": true, "description": "change of tenancy move in request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeOfTenancyMoveInRequest"}}}}, "responses": {"200": {"description": "Move-in request has been successfully created."}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/banking-details/validate": {"post": {"operationId": "validateBankingDetails", "description": "Banking Details Validation", "tags": ["Payments"], "summary": "Validate banking details", "requestBody": {"required": true, "description": "Banking Details Request", "content": {"application/json": {"schema": {"$ref": "components.json#/components/schemas/BankingDetailsRequest"}}}}, "responses": {"200": {"description": "Banking Details has been successfully validated.", "content": {"application/json": {"schema": {"$ref": "components.json#/components/schemas/BankingDetailsResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/quotes": {"post": {"operationId": "createQuotes", "tags": ["Quotes"], "summary": "Generate quotes for a customer", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuoteRequest"}}}}, "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QuotesResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/quotes": {"post": {"operationId": "createQuotesForRenewal", "tags": ["Quotes"], "summary": "Generate renewal quotes for a customer", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID of the account", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuoteForRenewal"}}}}, "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QuotesResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/renewals": {"post": {"tags": ["Renewals"], "operationId": "createRenewal", "summary": "Renew customer on to a new agreement", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Billing Account ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenewalCreationRequest"}}}}, "responses": {"201": {"description": "Renewal request completed response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenewalCreationResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/meterpoints/{meterpointId}/estimatedUsage": {"get": {"operationId": "getEstimatedEnergyUsage", "tags": ["Meter Point"], "summary": "Get the estimated usage for a meterpoint", "parameters": [{"name": "meterpointId", "in": "path", "required": true, "description": "The meterpoint ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "Estimated usage found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EstimatedUsageResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/quotes/evCharger": {"post": {"tags": ["Quotes"], "operationId": "createEvChargerQuote", "summary": "Generate an EV charger quote request for a customer", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEvChargerQuoteRequest"}}}}, "responses": {"201": {"description": "EV Charger Quote request completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEvChargerQuoteResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/accounts/{accountId}/preferences": {"get": {"operationId": "getAccountPreferencesV2", "tags": ["Preferences"], "security": [{"bearerAuth": []}], "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "summary": "Get preferences for account", "responses": {"200": {"description": "Preferences returned", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountPreference"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"operationId": "updateAccountPreferencesV2", "tags": ["Preferences"], "security": [{"bearerAuth": []}], "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "Account ID", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountPreference"}}}}}, "summary": "Update preferences for account", "responses": {"204": {"description": "Preferences were successfully updated"}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{billingAccountId}/innovationTariff/eligibility": {"get": {"operationId": "getInnovationTariffEligibility", "tags": ["Innovation Tariff"], "security": [{"bearerAuth": []}], "parameters": [{"name": "billingAccountId", "in": "path", "required": true, "description": "Billing Account ID", "schema": {"type": "integer", "format": "int64"}}], "summary": "Get Innovation Tariff Eligibility for a billing account Id", "responses": {"200": {"description": "Result of eligibility checks returned", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InnovationTariffEligibilityResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/psr/categories": {"get": {"operationId": "getPsrVulnerabilityCategories", "tags": ["Psr"], "summary": "Get Psr Vulnerability categories", "responses": {"200": {"description": "Psr vulnerability categories list returned", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VulnerabilityCategoriesResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/psr/services": {"get": {"operationId": "getPsrServicesByVulnerabilityIds", "tags": ["Psr"], "summary": "Get Services for Vulnerability IDs", "parameters": [{"name": "ids", "in": "query", "required": true, "description": "A string of all the Vulnerabilities.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Psr Services", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServicesResponse"}}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/payments/{identifier}": {"get": {"operationId": "getPaymentByIdentifier", "tags": ["Payments"], "security": [{"bearerAuth": []}], "summary": "Get a payment by a unique identifier", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}, {"name": "identifier", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "string", "description": "The unique identifier of the payment. The type of identifier depends on the identifierType provided."}}, {"name": "identifierType", "in": "query", "required": true, "description": "The type of identifier to search for", "schema": {"$ref": "#/components/schemas/PaymentIdentifierType"}}], "responses": {"200": {"description": "Payment found and returned", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Payment"}}}}, "404": {"description": "Payment not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/accounts/{accountId}/payments": {"post": {"operationId": "makePayment", "tags": ["Payments"], "security": [{"bearerAuth": []}], "summary": "Make a card payment", "parameters": [{"name": "accountId", "in": "path", "required": true, "description": "The account ID", "schema": {"type": "integer", "format": "int64", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MakePaymentRequest"}}}}, "responses": {"200": {"description": "Payment successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MakePaymentResponse"}}}}, "4XX": {"description": "Client error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "5XX": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"emailAddress": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "email address", "pattern": "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"}, "phoneNumber": {"type": "string", "example": "***********", "description": "phone number", "pattern": "^\\+?\\d{10,14}$"}, "AuthenticationRequest": {"type": "object", "required": ["email", "password", "extendedSession"], "properties": {"email": {"$ref": "#/components/schemas/emailAddress"}, "password": {"type": "string", "example": "password"}, "extendedSession": {"type": "boolean", "example": "false"}}}, "UpdatePasswordRequest": {"type": "object", "required": ["newPassword"], "properties": {"newPassword": {"type": "string", "example": "pass5679!"}, "oldPassword": {"type": "string", "example": "pass2388%"}, "token": {"type": "string", "example": "ahgsagdad21621fds"}}}, "UpdatePasswordResponse": {"type": "object", "required": ["success"], "properties": {"success": {"type": "boolean", "example": "true"}, "error": {"type": "string", "example": "New password must be at least 8 characters, contain a digit, upper case and lower case"}}}, "AuthenticationResponse": {"type": "object", "required": ["sessionExpiry", "accessToken", "accessTokenExpiry"], "properties": {"sessionExpiry": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "accessToken": {"type": "string", "example": "token"}, "accessTokenExpiry": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}}}, "RefreshTokenRequest": {"type": "object", "required": ["extendedSession"], "properties": {"extendedSession": {"type": "boolean", "example": "true"}}}, "SendActivationEmailRequest": {"type": "object", "required": ["email"], "properties": {"email": {"$ref": "#/components/schemas/emailAddress"}}}, "SendResetPasswordEmailRequest": {"type": "object", "required": ["email"], "properties": {"email": {"$ref": "#/components/schemas/emailAddress"}}}, "GetTransactionsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResponse"}}, "meta": {"$ref": "#/components/schemas/PagedResponseMetadata"}}}, "TransactionResponse": {"type": "object", "required": ["id", "amountInPounds", "balanceAfterTransactionInPounds", "description", "reference", "status", "type", "group"], "properties": {"id": {"type": "integer", "example": 1, "format": "int64"}, "amountInPounds": {"type": "number", "example": 1.99, "format": "double"}, "balanceAfterTransactionInPounds": {"type": "number", "example": 1.99, "format": "double"}, "createdDate": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00"}, "acceptedDate": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00"}, "cancelledDate": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00"}, "rejectedDate": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00"}, "description": {"type": "string"}, "reference": {"type": "string"}, "status": {"type": "string", "enum": ["Accepted", "Pending", "Rejected", "Authorising"]}, "type": {"$ref": "#/components/schemas/TransactionType"}, "group": {"$ref": "#/components/schemas/TransactionGroup"}}}, "TransactionType": {"type": "string", "enum": ["DirectDebitPayment", "CardPayment", "PaymentPlan", "BankTransfer", "Bill", "<PERSON><PERSON><PERSON><PERSON>", "ExitFees", "Refund", "RAFCredit", "WarmHomeDiscount", "RevisedBill", "GoodWill", "GSOP", "WriteOffSwitchAway", "WriteOffBackBilling", "WriteOffOther", "HMGovDiscountEBSS", "BalanceTransfer", "PayGTransfer", "Other"]}, "TransactionGroup": {"type": "string", "enum": ["Payments", "Charges", "Refunds", "Credit", "BalanceTransfers", "Other"]}, "AgreementsResponse": {"type": "object", "required": ["agreementID"], "properties": {"agreementID": {"type": "integer", "example": 1, "format": "int64"}, "meterpointID": {"type": "integer", "example": 1, "format": "int64"}, "meterpointIdentifier": {"description": "Meter point identifier", "example": "***************"}, "isActive": {"type": "boolean", "example": true}, "isSmartPayAsYouGo": {"type": "boolean", "example": true}, "startsInFuture": {"type": "boolean", "example": true}, "canRenew": {"type": "boolean", "example": true}, "renewalFromDate": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00"}, "startDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "endDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "tariffDurationInMonths": {"type": "integer", "format": "int64", "example": "12"}, "tariffName": {"type": "string", "example": "Electricity"}, "displayName": {"type": "string", "example": "Electricity"}, "fuelType": {"type": "string", "enum": ["Gas", "Electricity"], "example": "Electricity"}, "productRateType": {"type": "string", "enum": ["STANDARD", "ECO7", "TOU"], "example": "STANDARD"}, "tariffStandingRateInPence": {"type": "number", "example": 1.2, "format": "double"}, "tariffDayRateInPence": {"type": "number", "example": 2.4, "format": "double"}, "tariffNightRateInPence": {"type": "number", "example": 5.5, "format": "double"}, "tariffStandardRateInPence": {"type": "number", "example": 1.8, "format": "double"}, "tariffEarlyTerminationChargeInPounds": {"type": "number", "example": 1.9, "format": "double"}, "isTariffVariable": {"type": "boolean", "example": true}, "isTariffMultirate": {"type": "boolean", "example": true}, "isExportTariff": {"type": "boolean", "example": true}, "costPerYearInPounds": {"type": "number", "example": 1200.0, "format": "double"}, "costPerMonthInPounds": {"type": "number", "example": 100.0, "format": "double"}, "tariffRates": {"type": "array", "items": {"$ref": "#/components/schemas/TariffRate"}}}}, "TariffRate": {"type": "object", "properties": {"name": {"type": "string", "example": "peak"}, "startHour": {"type": "string", "example": "05:00"}, "endHour": {"type": "string", "example": "23:00"}, "rateInPence": {"type": "number", "example": 23.18, "format": "double"}}}, "BillFileLinkResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "example": "234"}, "link": {"type": "string", "example": "https://www.so.energy/download-bill-link"}}}, "BillLine": {"type": "object", "properties": {"type": {"type": "string", "enum": ["electricity", "gas", "exitFees", "vat", "standingCharges"], "example": "electricity", "description": "Type of bill lines"}, "amountInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Amount in pounds"}, "vatRate": {"type": "number", "format": "double", "example": "2.00", "description": "Only for VAT"}}}, "Bill": {"type": "object", "required": ["billID"], "properties": {"billID": {"type": "integer", "format": "int64", "example": 12}, "totalAmountInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Total charges in pounds"}, "electricityChargesInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Electricity charges in pounds"}, "gasChargesInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Gas charges in pounds"}, "vatChargesInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Vat charges in pounds"}, "creditAppliedInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Credit applied in pounds"}, "balanceAfterBillInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Balance in pounds"}, "isFinalBill": {"type": "boolean", "description": "Whether the bill is final", "example": false}, "startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "endDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "createdDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "acceptedDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "isRevised": {"type": "boolean", "description": "Whether the bill was superseded", "example": false}, "isRevisedVersion": {"type": "boolean", "description": "Whether the bill's version number is greater than 1 or not", "example": true}, "openingBalanceInPounds": {"type": "number", "format": "double", "example": "23.00", "description": "Opening balance in pounds"}, "closingBalanceInPounds": {"type": "number", "format": "double", "example": "23.00", "description": "Opening balance in pounds"}, "billLines": {"type": "array", "items": {"$ref": "#/components/schemas/BillLine"}}}}, "BillSummary": {"type": "object", "required": ["billID"], "properties": {"billID": {"type": "integer", "format": "int64", "example": 12}, "totalAmountInPounds": {"type": "number", "format": "double", "example": "123.00", "description": "Total charges in pounds"}, "isFinalBill": {"type": "boolean", "description": "Whether the bill is final", "example": false}, "startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "endDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "createdDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "acceptedDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "isRevised": {"type": "boolean", "description": "Whether the bill was superseded", "example": false}, "isRevisedVersion": {"type": "boolean", "description": "Whether the bill's version number is greater than 1 or not", "example": true}}}, "GetAccountResponse": {"type": "object", "required": ["accountID"], "properties": {"accountID": {"type": "integer", "format": "int64", "example": "123", "description": "Account ID"}, "accountNumber": {"type": "string", "example": "********", "description": "Account number"}, "balance": {"type": "number", "format": "double", "example": "123.00", "description": "Balance"}, "startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "closeDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "isWHDEligible": {"type": "boolean", "description": "Whether is WHD eligible", "example": false}, "supplyAddress": {"$ref": "#/components/schemas/Address"}, "billingAddress": {"$ref": "#/components/schemas/Address"}, "isOnSupply": {"type": "boolean", "description": "Whether is on supply", "example": false}, "isClosed": {"type": "boolean", "description": "Whether the account is closed", "example": false}, "isTerminating": {"type": "boolean", "description": "Whether the account is terminating", "example": false}}}, "SmartPayAsYouGoEligibilityResponse": {"type": "object", "required": ["eligibleForSpayg"], "properties": {"mpxnIdentifier": {"type": "string", "description": "Mpxn identifiers"}, "eligibleForSpayg": {"type": "boolean", "description": "Valid for SPAYG or not or false if daily limiter reached"}, "ineligibleReason": {"type": "string", "description": "Reason for not being eligible for SPAYG including daily limiter"}}}, "SmartPayAsYouGoExecutionDateResponse": {"type": "object", "required": ["executionDate"], "properties": {"executionDate": {"type": "string", "format": "date", "example": "2025-02-02", "description": "Execution Date for switch over to Smart Pay As You Go"}}}, "GetMeterReadingsRequest": {"type": "object", "description": "Get meter readings request", "properties": {"fromDate": {"type": "string", "format": "date", "example": "2024-06-15", "description": "Date starting from which the readings should be returned"}, "toDate": {"type": "string", "format": "date", "example": "2024-07-15", "description": "Date until which the readings should be returned"}, "meterId": {"type": "integer", "format": "int64", "description": "Meter identifier for the readings"}, "registerId": {"type": "integer", "format": "int64", "description": "Register identifier for the readings"}, "grouping": {"type": "string", "enum": ["daily", "monthly"], "description": "Optionally return the last reading from each day or month"}}}, "GetMeterReadingsResponse": {"type": "object", "description": "A meter reading response page", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/GetMeterReadingsResponseItem"}, "description": "The meter reading response items on the requested page"}, "meta": {"$ref": "#/components/schemas/PagedResponseMetadata"}}}, "GetMeterReadingsResponseItem": {"type": "object", "description": "A meter reading response", "properties": {"readingDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00", "description": "Date and time when the reading was done"}, "registerID": {"type": "integer", "format": "int64", "description": "ID of the register"}, "meterID": {"type": "integer", "format": "int64", "description": "ID of the meter"}, "meterpointID": {"type": "integer", "format": "int64", "description": "ID of the meterpoint"}, "consumption": {"type": "number", "format": "double", "description": "Consumed amount"}, "cumulative": {"type": "number", "format": "double", "description": "Cumulative consumption"}, "source": {"type": "string", "enum": ["customer", "agent", "system", "smart", "industry", "manual"], "description": "Simplified sources mapped from Junifer readings sources"}, "isEstimated": {"type": "boolean", "example": false, "description": "Whether the provided reading is an estimation or not"}}}, "PagedResponseMetadata": {"type": "object", "description": "Metadata attached to a response of a paged request", "properties": {"pageNumber": {"type": "integer", "description": "Index of the page that was returned", "format": "int32"}, "pageSize": {"type": "integer", "description": "Size of the page that was returned", "format": "int32"}, "totalElements": {"type": "integer", "description": "Total number of elements across all pages", "format": "int32"}, "totalPages": {"type": "integer", "description": "Total number of available pages", "format": "int32"}}}, "UpdateCustomerRequest": {"type": "object", "description": "Update customer request", "properties": {"isDarkMode": {"type": "boolean", "description": "Whether dark mode is enabled", "example": false}, "isMarketingOptIn": {"type": "boolean", "description": "This value is deprecated. To update the marketing opt in, please use the /preferences endpoint", "example": false, "deprecated": true}, "phone2": {"type": "string", "description": "Second phone number to be added to primary contact", "example": "************"}, "accountIDLastVisited": {"type": "integer", "format": "int64", "example": "123", "description": "Account ID last visited"}, "evType": {"type": "string", "enum": ["HYBRID_ELECTRIC", "ELECTRIC", "NO_EV"], "example": "HYBRID_ELECTRIC", "description": "Type of EV the customer owns"}, "intendToBuyEv": {"type": "boolean", "example": "false", "description": "Whether the customer intends to buy an EV"}, "evTariffMarketingConsent": {"type": "boolean", "example": "false", "default": false, "description": "Whether the customer has consented to receive marketing about EV tariffs"}}}, "GetCustomerResponse": {"type": "object", "required": ["customerID"], "properties": {"customerID": {"type": "integer", "format": "int64", "example": "123", "description": "Customer ID"}, "accountIDs": {"type": "array", "items": {"type": "integer", "format": "int64", "example": "123", "description": "Account ID"}}, "accountIDLastVisited": {"type": "integer", "format": "int64", "example": "123", "description": "Last visited accountID of for the customer"}, "energySourceVote": {"$ref": "#/components/schemas/EnergySourceVote"}, "isDarkMode": {"type": "boolean", "description": "Whether dark mode is enabled for the customer", "example": false}, "isMarketingOptIn": {"type": "boolean", "description": "This value is deprecated. To retrieve this value, use the /preferences endpoint.", "example": false, "deprecated": true}, "firstName": {"type": "string", "description": "Customer's first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Customer's last name", "example": "<PERSON><PERSON>"}, "phone1": {"type": "string", "description": "Customer's primary phone number", "example": "********"}, "phone2": {"type": "string", "description": "Customer's secondary phone number", "example": "********"}, "email": {"$ref": "#/components/schemas/emailAddress"}, "isEligibleForMyNewAccount": {"type": "boolean", "description": "Wheter the customer is eligible to use my new account", "example": false}, "address": {"$ref": "#/components/schemas/Address"}}}, "GetSmartMeterReadingFrequencyResponse": {"type": "object", "required": ["frequency"], "properties": {"frequency": {"$ref": "#/components/schemas/SmartMeterReadingFrequency"}}}, "UpdateSmartMeterReadingFrequencyRequest": {"type": "object", "required": ["frequency"], "properties": {"frequency": {"$ref": "#/components/schemas/SmartMeterReadingFrequency"}}}, "SmartMeterReadingFrequency": {"type": "string", "enum": ["half<PERSON>ourly", "daily", "monthly"], "description": "Frequency of smart meter readings"}, "GetFeatureFlagsResponse": {"type": "object", "required": ["warmHomeDiscount", "homeCoverPromo", "referrals", "smartPayAsYouGo"], "properties": {"warmHomeDiscount": {"type": "boolean", "description": "Whether the Warm Home Discount flag is enabled", "example": false}, "homeCoverPromo": {"type": "boolean", "description": "Whether the Home Cover Promo flag is enabled", "example": false}, "referrals": {"type": "boolean", "description": "Whether the Referrals flag is enabled", "example": false}, "smartPayAsYouGo": {"type": "boolean", "description": "Whether the Smart Pay As You Go flag is enabled", "example": false}}}, "MeterReadingReminderResponse": {"type": "object", "required": ["isEnabled", "frequency"], "properties": {"isEnabled": {"type": "boolean", "description": "Whether reminders are enabled", "example": false}, "frequency": {"$ref": "#/components/schemas/ReminderFrequency"}, "quarterlyReminderStartMonth": {"$ref": "#/components/schemas/QuarterlyReminderStartMonth"}}}, "UpdateMeterReadingReminderRequest": {"type": "object", "description": "Update meter reading reminders request", "required": ["isEnabled", "frequency"], "properties": {"isEnabled": {"type": "boolean", "description": "Whether reminders are enabled", "example": false}, "frequency": {"$ref": "#/components/schemas/ReminderFrequency"}, "quarterlyReminderStartMonth": {"$ref": "#/components/schemas/QuarterlyReminderStartMonth"}}}, "ReminderFrequency": {"type": "string", "enum": ["monthly", "quarterly"], "description": "Frequency with which meter reading reminders should be sent"}, "QuarterlyReminderStartMonth": {"type": "string", "enum": ["first", "second", "third"], "description": "If quarterly frequency is enabled, the order of the month inside the quarter when the reminder should be sent"}, "EnergySourceVote": {"type": "string", "enum": ["wind", "solar", "hydro", "biomass"]}, "CancelSwitchRequest": {"type": "object", "description": "Cancel account switch request", "required": ["cancellationReason"], "properties": {"cancellationReason": {"type": "string", "description": "Cancellation Reason", "example": "Some reason"}}}, "AccountStates": {"type": "object", "description": "List of states for the account", "required": ["smartPayAsYouGo", "reads", "directDebit", "meterpointID", "agreements", "openingMeterReading", "complaints", "meterAppointment", "billGeneration"], "properties": {"smartPayAsYouGo": {"$ref": "#/components/schemas/SmartPayAsYouGoState"}, "reads": {"$ref": "#/components/schemas/ReadState"}, "directDebit": {"$ref": "#/components/schemas/DirectDebitState"}, "agreements": {"$ref": "#/components/schemas/AgreementState"}, "openingMeterReading": {"$ref": "#/components/schemas/OpeningMeterReadingState"}, "complaints": {"$ref": "#/components/schemas/ComplaintState"}, "meterAppointment": {"$ref": "#/components/schemas/MeterAppointmentState"}, "billGeneration": {"$ref": "#/components/schemas/BillGenerationState"}, "supply": {"$ref": "#/components/schemas/SupplyState"}}}, "SmartPayAsYouGoState": {"type": "string", "enum": ["off", "on", "pending", "partial", "not_eligible"], "example": "pending", "description": "Current state for smart pay as you go"}, "ReadState": {"type": "string", "enum": ["missingManualReading", "submittedRecently", "missingSmartReading"], "example": "missingManualReading", "description": "Current state for reads"}, "AgreementState": {"type": "string", "enum": ["preSsdCoolingOffPeriod", "postSsdCoolingOffPeriod", "<PERSON><PERSON><PERSON><PERSON>", "earlyRenewalPeriod", "standardRenewalPeriod", "fixedTariffRenewalAgreedCoolingOffPeriod", "fixedTariffCoolingOffPeriod", "fixedTariffRenewalAgreedAwaitingStart", "fixedTariffNoActionRequired", "noActiveAgreement", "unknown"], "example": "<PERSON><PERSON><PERSON><PERSON>", "description": "Current state for agreements"}, "OpeningMeterReadingState": {"type": "string", "description": "Current account state for opening meter readings", "enum": ["notReadyForOpeningReadings", "awaitingGasReading", "awaitingElecReading", "awaitingElecAndGasReadings", "missedSubmissionWindow", "allSubmitted"]}, "ComplaintState": {"type": "string", "description": "Current account state for complaints", "enum": ["noComplaints", "closedComplaints", "Unknown"]}, "MeterAppointmentState": {"type": "string", "description": "Current account state for meter appointment", "enum": ["traditionalSmartEligible", "appointmentBooked", "other"]}, "BillGenerationState": {"type": "string", "description": "Current account state for bill generation", "enum": ["noB<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "billSentWithin31Days", "noBillWithin31Days", "billingSuppressed", "noBillAndBillingRecentlyUnsuppressed", "finalBillSent", "unknown"]}, "SupplyState": {"type": "string", "description": "Current account state for energy supply", "enum": ["preOnboarding", "onboarding", "enrolmentRejected", "enrolmentObjected", "enrolmentCancelled", "onboardingPartiallyRejected", "onboardingPartiallyObjected", "onboardingPartiallyCancelled", "onboardedRecently", "onSupply", "onboardingAdditionalMeterpoint", "onboardingAdditionalMeterpointFailed", "partialLossInitiated", "lossInitiated", "lossComplete", "futureMoveOut", "futureMoveIn", "movedOut", "unknown"]}, "DirectDebitState": {"type": "string", "description": "Current state for direct debit", "enum": ["noPriorDirectDebit", "pendingActivation", "failedSetup", "activeFixed", "activeVariable", "failedPayment", "cancelled", "unknown"]}, "Unbilled": {"type": "object", "description": "Details about pending and next bill", "required": ["lastBilledDate"], "properties": {"lastBilledDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00", "description": "End date of the last produced bill"}, "nextBill": {"$ref": "#/components/schemas/NextBill"}, "pendingBill": {"$ref": "#/components/schemas/PendingBill"}}}, "NextBill": {"type": "object", "description": "Details about pending and next bill", "required": ["startDate", "endDate", "electricityUsage", "gasUsage", "totalCostInPounds"], "properties": {"startDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "endDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "electricityUsage": {"$ref": "#/components/schemas/Usage"}, "gasUsage": {"$ref": "#/components/schemas/Usage"}, "totalCostInPounds": {"type": "number", "format": "double", "example": "123.0", "description": "Sum of elec and gas charges including standing charges"}, "totalConsumptionInKwh": {"type": "number", "format": "double", "example": "123.0", "description": "Sum of elec and gas consumption in Kwh"}}}, "PendingBill": {"type": "object", "description": "Details about pending bill dates", "required": ["startDate", "endDate"], "properties": {"startDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "endDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}}}, "Usage": {"type": "object", "description": "Details about gas or meter usage", "required": ["consumptionInKwh", "usageCostInPounds", "isEstimated", "unableToEstimate", "isSupplied", "usageEndDate"], "properties": {"consumptionInKwh": {"type": "number", "format": "double", "example": "123.0"}, "usageCostInPounds": {"type": "number", "format": "double", "example": "123.0", "description": "Excludes standing charges"}, "isEstimated": {"type": "boolean", "example": true, "description": "Describes if consumptionInKwh and usageCostInPounds is estimated or not"}, "unableToEstimate": {"type": "boolean", "example": true, "description": "Describes if not enough reads existent to estimate TodaysReadingKwh"}, "isSupplied": {"type": "boolean", "example": true, "description": "Whether the fuel is being supplied during the bill period"}, "usageEndDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}}}, "PublicMeterResponse": {"type": "object", "description": "Meter list response used for non-auth endpoint", "required": ["meterpointID"], "properties": {"meterpointID": {"type": "integer", "format": "int64", "example": "123", "description": "Meter point ID"}, "meterpointIdentifier": {"type": "string", "description": "Meter point identifier", "example": "***************"}, "meterIdentifier": {"type": "string", "description": "Meter identifier", "example": "A09N045345"}, "isElectricity": {"type": "boolean", "description": "Whether meter is elec type", "example": false}, "isGas": {"type": "boolean", "description": "Whether meter is gas type", "example": false}, "isMultirateMeter": {"type": "boolean", "description": "Whether meter is multi rate", "example": false}, "registers": {"type": "array", "items": {"$ref": "#/components/schemas/MeterRegister"}}}}, "MeterResponse": {"allOf": [{"$ref": "#/components/schemas/PublicMeterResponse"}, {"type": "object", "properties": {"meterID": {"type": "integer", "format": "int64", "example": "123", "description": "Meter ID"}, "meterpointType": {"$ref": "#/components/schemas/MeterpointType"}, "meterType": {"$ref": "#/components/schemas/MeterType"}, "profileClass": {"type": "string", "description": "Profile class", "example": "1"}, "gspGroup": {"type": "string", "description": "Gsp group", "example": "_N"}, "serialNumber": {"type": "string", "description": "Meter serial number", "example": "*********"}, "isSmartMeter": {"type": "boolean", "description": "Whether meter is smart", "example": false}, "isSmet2": {"type": "boolean", "description": "Whether meter is smet2", "example": false}, "isExportMeter": {"type": "boolean", "description": "Whether meter is export", "example": false}, "hasMeterTechnicalDetails": {"type": "boolean", "description": "Whether meter has technical details", "example": false}, "isOpeningReadingRequired": {"type": "boolean", "description": "Whether meter requires open reading", "example": false}, "openingReadingWindowStartDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "openingReadingWindowEndDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "switchCancellationDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "switchCancellationDeadline": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "supplyStartDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "unit": {"$ref": "#/components/schemas/MeterUnitType"}, "pendingSubmissionStatus": {"$ref": "#/components/schemas/PendingSubmissionStatus"}, "readFrequency": {"$ref": "#/components/schemas/ReadFrequency"}, "isFaultySmartMeter": {"type": "boolean", "description": "Whether smart meter is faulty", "example": false}, "simplifiedSupplyStatus": {"$ref": "#/components/schemas/SimplifiedSupplyStatus"}, "isWithinOpeningMeterReadingWindow": {"type": "boolean", "description": "Whether open meter reading is within window", "example": false}}}]}, "SimplifiedSupplyStatus": {"type": "string", "enum": ["PRE_ONBOARDING", "ONBOARDING", "REGISTRATION_REJECTED", "REGISTRATION_OBJECTED", "REGISTRATION_CANCELLED", "ON_SUPPLY", "LOSS_INITIATED", "LOSS_OBJECTED", "LOSS_COMPLETED", "STATUS_UNKNOWN"]}, "MeterRegister": {"type": "object", "description": "Meter register object", "properties": {"registerID": {"type": "integer", "format": "int64", "example": "123", "description": "Register ID"}, "registerIdentifier": {"type": "string", "example": "L", "description": "Register identifier"}, "digitCount": {"type": "integer", "format": "int32", "example": "123", "description": "Digit count"}, "rateName": {"$ref": "#/components/schemas/MeterRegisterRateName"}, "lastNonEstimatedReading": {"$ref": "#/components/schemas/MeterReading"}, "lastSmartReading": {"$ref": "#/components/schemas/MeterReading"}}}, "MeterReading": {"type": "object", "description": "Meter reading object", "properties": {"readingDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "submittedDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "value": {"type": "number", "format": "double", "example": "123.0", "description": "Meter reading value"}}}, "PendingSubmissionStatus": {"type": "object", "description": "Pending submission status", "properties": {"submissionDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "readingDate": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00"}, "submissionStatus": {"type": "string", "description": "Submission status", "example": "PENDING"}, "value": {"type": "number", "format": "double", "example": "123.0", "description": "Meter reading value"}, "submittedReadings": {"type": "array", "items": {"$ref": "#/components/schemas/SubmittedReading"}}}}, "SubmittedReading": {"type": "object", "description": "Submitted meter reading", "properties": {"value": {"type": "integer", "format": "int64", "example": "123", "description": "Meter reading value"}, "type": {"$ref": "#/components/schemas/MeterRegisterRateName"}}}, "MeterpointType": {"type": "string", "enum": ["mprn", "mpan"], "description": "Type of the meterpoint"}, "MeterUnitType": {"type": "string", "enum": ["kWh", "MWh", "kVAh", "hour", "day", "month", "second", "kVArh", "MJ", "therm", "kW", "MW", "kVA", "kVAr", "m3", "tm3", "hm3", "thm3", "ft3", "tcf", "hcf", "thcf", "year"], "description": "Unit type of the meter"}, "MeterRegisterRateName": {"type": "string", "enum": ["day", "night", "standard"], "description": "Rate name of meter register"}, "MeterType": {"type": "string", "enum": ["CHECK", "H", "K", "LAG_", "LEAD_", "MAIN_", "N", "NCAMR", "NSS", "RCAMR", "RCAMY", "S", "SPECL", "T", "CM", "CR", "ET", "MT", "PP", "TH", "U", "S1", "S2", "S2A", "NS", "S1C", "S2C", "S2AD", "S2BD", "S2CD", "S2ADE", "S2BDE", "S2CDE", "S2B", "CONV", "UNKNOWN"]}, "MeterReadingWithTechnicalDetails": {"type": "object", "description": "Meter reading", "required": ["value", "meterId", "registerId", "readingDate"], "properties": {"value": {"type": "integer", "format": "int64", "description": "Reading value", "example": 100}, "meterId": {"type": "integer", "format": "int64", "description": "Meter id", "example": 100}, "registerId": {"type": "integer", "format": "int64", "description": "Register id", "example": 100}, "readingDate": {"type": "string", "format": "date", "description": "The date the reading is submitted for", "example": "2017-02-17"}}}, "MoveInMeterReading": {"type": "object", "description": "Meter reading for move in payload", "required": ["meterPointId", "meterIdentifier", "meterReading", "readingDate", "rateName", "type"], "properties": {"meterIdentifier": {"type": "string", "description": "Meter point identifier"}, "meterPointId": {"type": "integer", "format": "int64", "description": "Meter point id", "example": 100}, "meterReading": {"type": "integer", "format": "int64", "description": "Reading value", "example": 100}, "readingDate": {"type": "string", "format": "date", "description": "The date the reading is submitted for", "example": "2017-02-17"}, "rateName": {"$ref": "#/components/schemas/MeterRegisterRateName"}, "type": {"type": "string", "enum": ["Electricity", "Gas"], "description": "Type of fuel", "example": "electricity"}, "registerIdentifier": {"type": "string", "example": "L", "description": "Register identifier"}}}, "MeterReadingWithoutTechnicalDetails": {"type": "object", "description": "Meter reading without technical details", "required": ["value", "meterpointId", "rateName", "readingDate"], "properties": {"value": {"type": "integer", "format": "int64", "description": "Reading value", "example": 100}, "meterpointId": {"type": "integer", "format": "int64", "description": "Meterpoint id", "example": 100}, "rateName": {"type": "string", "enum": ["Day", "Night", "Standard"], "description": "Rate name", "example": "Day"}, "readingDate": {"type": "string", "format": "date", "description": "The date the reading is submitted for", "example": "2017-02-17"}}}, "Address": {"type": "object", "description": "An address", "required": ["postcode", "addressLine1", "countryCode"], "properties": {"postcode": {"$ref": "#/components/schemas/Postcode"}, "addressLine1": {"type": "string", "description": "First line of the address", "example": "London Rd."}, "addressLine2": {"type": "string", "description": "Second line of the address", "example": "Greater London"}, "addressLine3": {"type": "string", "description": "Third line of the address", "example": "South England"}, "countryCode": {"type": "string", "description": "Country code of the address", "example": "UK"}}}, "AddressMeterPointDetails": {"type": "object", "description": "An address", "required": ["address"], "properties": {"address": {"$ref": "#/components/schemas/Address"}, "fullAddress": {"type": "string", "description": "Concatenation of addressLines from address", "example": "40 the Glen, Something Road, the Town"}, "mprn": {"type": "string", "description": "Meter Point Reference Number", "minLength": 1, "maxLength": 23, "example": "*********"}, "mpan": {"type": "string", "description": "Meter Point Administration Number", "minLength": 1, "maxLength": 23, "example": "*************"}, "isMpanSmartMeter": {"type": "boolean", "description": "Is electricity meter point a smart meter", "example": true}, "billingAccountId": {"type": "integer", "format": "int64", "example": "1234", "description": "The billing account ID"}, "candidateFDOR": {"type": "string", "format": "date", "example": "2010-10-01", "description": "Candidate first date of responsibility"}}}, "Postcode": {"type": "string", "description": "UK postal code", "pattern": "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][ABD-HJLNP-UW-Z]{2}$"}, "PaymentScheduleDetails": {"type": "object", "properties": {"currentSchedule": {"$ref": "#/components/schemas/PaymentSchedule"}, "futureSchedule": {"$ref": "#/components/schemas/PaymentSchedule"}}}, "PaymentSchedule": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date", "description": "Start date of the PaymentSchedule", "example": "2024-01-02"}, "endDate": {"type": "string", "format": "date", "description": "End date of the PaymentSchedule", "example": "2024-01-02"}, "isSeasonal": {"type": "boolean", "description": "Whether PaymentSchedule is seasonal", "example": false}, "paymentDayOfTheMonth": {"type": "integer", "example": 1, "format": "int32"}, "nextPaymentAmountInPounds": {"type": "number", "example": 123.34, "format": "double"}, "nextPaymentDate": {"type": "string", "format": "date", "description": "Next payment date of the PaymentSchedule", "example": "2024-01-02"}, "paymentAmount": {"$ref": "#/components/schemas/PaymentAmount"}}}, "PaymentAmount": {"type": "object", "properties": {"equalPaymentAmountInPounds": {"type": "number", "example": 123.34, "format": "double", "description": "Equal payment amount in pounds"}, "summerPaymentAmountInPounds": {"type": "number", "example": 123.34, "format": "double", "description": "Summer payment amount in pounds"}, "winterPaymentAmountInPounds": {"type": "number", "example": 123.34, "format": "double", "description": "Winter payment amount in pounds"}}}, "GetVariablePaymentDetailsResponse": {"type": "object", "properties": {"nextPaymentDate": {"type": "string", "format": "date", "description": "Next payment date for customer on variable tariff", "example": "2024-01-02"}}}, "GetMeterAppointmentResponse": {"type": "object", "required": ["appointmentID", "createdDate"], "properties": {"appointmentID": {"type": "integer", "format": "int64", "description": "Appointment identifier", "example": 123}, "createdDate": {"type": "string", "format": "date-time", "description": "Date and time when the appointment was created", "example": "2024-06-06"}, "startDate": {"type": "string", "format": "date-time", "description": "Date and time when the appointment started", "example": "2024-06-06"}, "endDate": {"type": "string", "format": "date-time", "description": "Date and time when the appointment ended", "example": "2024-06-06"}, "type": {"$ref": "#/components/schemas/MeterAppointmentType"}, "status": {"$ref": "#/components/schemas/MeterAppointmentStatus"}, "repairType": {"$ref": "#/components/schemas/MeterRepairType"}, "fuelType": {"$ref": "#/components/schemas/MeterAppointmentFuelType"}, "closedDate": {"type": "string", "format": "date-time", "description": "Date and time when the appointment was finalized", "example": "2024-06-06"}, "bookingReference": {"type": "string", "description": "Internal reference of the appointment", "example": "STL/123456"}, "elecMeterpointIdentifier": {"type": "string", "description": "Identifier for electricity meterpoint"}, "elecMeterpointSerialNumber": {"type": "string", "description": "Serial number for electricity meterpoint"}, "gasMeterpointIdentifier": {"type": "string", "description": "Identifier for gas meterpoint"}, "gasMeterpointSerialNumber": {"type": "string", "description": "Serial number for gas meterpoint"}, "isMedicalEquipmentPresent": {"type": "boolean", "description": "Whether medical equipment is present"}, "isEligibleForCancellation": {"type": "boolean", "description": "Whether appointment is eligible for cancellation"}, "contactDetails": {"$ref": "#/components/schemas/ContactDetails"}}}, "MeterAppointmentType": {"type": "string", "enum": ["installation", "repair"]}, "MeterAppointmentStatus": {"type": "string", "enum": ["booked", "failed", "cancelled", "completed"]}, "MeterRepairType": {"type": "string", "enum": ["meterInvestigation", "meterReboot", "meterPowerCycle", "recommission", "meterExchange"]}, "MeterAppointmentFuelType": {"type": "string", "enum": ["electricity", "gas", "dualFuel"]}, "ContactDetails": {"type": "object", "properties": {"fullName": {"type": "string", "description": "Full name of contact", "example": "<PERSON>"}, "phoneNumber": {"$ref": "#/components/schemas/phoneNumber"}, "email": {"$ref": "#/components/schemas/emailAddress"}}}, "EvCheckRegistrationPlate": {"type": "object", "required": ["registrationPlate"], "properties": {"registrationPlate": {"type": "string", "description": "Registration plate to be checked", "example": "AA19AAA", "pattern": "^[A-Za-z0-9]{1,7}$"}}}, "EvCheckResponse": {"type": "object", "required": ["isEv"], "properties": {"isEv": {"type": "boolean", "description": "Validation result of Ev check", "example": true}, "fuelType": {"type": "string", "description": "The fuel type of a successfully verified vehicle", "example": "ELECTRICITY"}}}, "MeterpointIdentifiersRequest": {"type": "object", "required": ["identifiers"], "properties": {"identifiers": {"type": "array", "items": {"type": "string", "description": "MeterPoint identifier", "example": "12345678"}}}}, "EvTariffSmartMeterCheckResponse": {"type": "object", "required": ["isEligible"], "properties": {"isEligible": {"type": "boolean", "description": "Validation result of Ev tariff smart meter check", "example": true}, "errorCode": {"type": "string", "description": "The error code", "example": "NOT_SMART_METER"}}}, "CreateQuoteRequest": {"type": "object", "required": ["postcode", "hasEconomySevenMeter", "estimatedUsageType", "fuelType", "generateQuotesForEvTariffs"], "properties": {"postcode": {"type": "string", "description": "Postal code of the address", "example": "555555"}, "hasEconomySevenMeter": {"type": "boolean", "description": "Flag for seven meter calculation", "example": true}, "estimatedUsageType": {"$ref": "#/components/schemas/EstimatedUsageType"}, "fuelType": {"$ref": "#/components/schemas/FuelType"}, "electricityUsagePerYearInKwh": {"type": "integer", "description": "Electricity amount used in interval", "format": "int64", "example": 2700}, "gasUsagePerYearInKwh": {"type": "integer", "description": "Gas amount used in interval", "format": "int64", "example": 2700}, "generateQuotesForEvTariffs": {"type": "boolean", "description": "Flag for including EV tariffs", "example": true}}}, "FuelType": {"type": "string", "description": "type of fuel", "enum": ["DUAL_FUEL", "GAS_ONLY", "ELECTRICITY_ONLY"]}, "EstimatedUsageType": {"type": "string", "description": "type of estimated usage", "enum": ["LOW", "MEDIUM", "HIGH", "EV_OWNER", "EXACT_AMOUNT"]}, "QuotesResponse": {"type": "object", "required": ["quoteId", "tariffName", "rate", "fuelType", "costPerYearInPounds", "costPerMonthInPounds"], "properties": {"quoteId": {"type": "integer", "format": "int64", "example": "555555"}, "tariffName": {"type": "string", "description": "The name of the tariff", "example": "So Sunflower One Year"}, "shortTariffName": {"type": "string", "description": "The short name of the tariff", "example": "So Sunflower"}, "rate": {"type": "string", "description": "The rate of the tariff:fixed or variable", "example": "Fixed"}, "tariffLengthInMonths": {"type": "integer", "description": "The duration of the tariff in months", "format": "int32", "example": 12}, "payment": {"type": "string", "description": "Payment type", "example": "Monthly Direct Debit"}, "fuelType": {"type": "string", "description": "The fuel type of the quote request", "example": "Dual Fuel"}, "costPerYearInPounds": {"type": "integer", "description": "Cost of the tariff per year", "format": "int64", "example": 840}, "costPerMonthInPounds": {"type": "integer", "description": "Cost of the tariff per month", "format": "int64", "example": 70}, "elecProductType": {"$ref": "#/components/schemas/ElecProductType"}, "tariffType": {"$ref": "#/components/schemas/TariffType"}, "elecRates": {"type": "array", "items": {"$ref": "#/components/schemas/ElecTariffRate"}}, "elecStandingChargeInPence": {"type": "string", "description": "The standing charge of the elec product"}, "elecConsumptionPerYearInKwh": {"type": "string", "description": "The total kwh consumption for elec per year"}, "gasCostPerKwhInPence": {"type": "string", "description": "The cost per kwh in pence for gas product"}, "gasStandingChargeInPence": {"type": "string", "description": "The standing charge of the gas product"}, "gasConsumptionPerYearInKwh": {"type": "string", "description": "The total kwh consumption for gas per year"}, "earlyExitFeePerFuelInPounds": {"type": "string", "description": "The exit fee per fuel in pounds"}, "supplyStartDate": {"type": "string", "description": "The supply start date for the tariff", "example": "14 Nov 2024"}, "firstDdDate": {"type": "string", "example": "14 Nov 2024"}, "seasonalAdjustmentFactor": {"type": "integer", "description": "The percent difference between hot and cold months if seasonal monthly payments selected", "format": "int32", "example": 25}, "currentReferralFee": {"type": "integer", "format": "int32"}, "elecProductCode": {"type": "string", "description": "The product for the elec product"}, "gasProductCode": {"type": "string", "description": "The product for the gas product"}, "renewalElecStartDate": {"type": "string", "description": "The start date for the renewal elec product"}, "renewalGasStartDate": {"type": "string", "description": "The start date for the renewal gas product"}, "mustSetUpHHReadingConsent": {"type": "boolean", "description": "Whether the HH meterpoint consent has to be set or not before renewal"}, "hasComplexMeterConfiguration": {"type": "boolean", "description": "If renewal quotes have complex meter configuration, this flag is set to true, else false"}, "elecMeterPointIdentifier": {"type": "string", "description": "The electric meter point identifier"}, "gasMeterPointIdentifier": {"type": "string", "description": "The gas meter point identifier"}, "isRecommendedForRenewal": {"type": "boolean", "description": "Whether the quote is recommended or not"}, "elecAnnualCostInPounds": {"type": "integer", "format": "int64", "description": "The cost of the electric fuel portion of the tariff per year, in pounds"}, "gasAnnualCostInPounds": {"type": "integer", "format": "int64", "description": "The cost of the gas fuel portion of the tariff per year, in pounds"}}}, "EstimatedUsageResponse": {"type": "object", "required": ["success", "meterpointId", "meterpointType", "rates"], "properties": {"meterpointId": {"type": "integer", "description": "The ID of the meterpoint", "format": "int64", "example": "12345"}, "meterpointType": {"$ref": "#/components/schemas/MeterpointType"}, "rates": {"type": "array", "items": {"$ref": "#/components/schemas/EstimatedRate"}}}}, "EstimatedRate": {"type": "object", "required": ["usage", "name"], "properties": {"usage": {"type": "number", "description": "The estimated usage.", "example": 100.2, "format": "double"}, "name": {"type": "string", "description": "The estimated rate name", "enum": ["Standard", "Day", "Night"]}}}, "ElecProductType": {"type": "string", "description": "type of Elec Tariff", "enum": ["Eco7", "Standard", "TOU"]}, "TariffType": {"type": "string", "description": "type of Tariff", "enum": ["Standard", "Green", "EV"]}, "ElecTariffRate": {"type": "object", "required": ["costPerKwhInPence"], "properties": {"name": {"type": "string", "description": "The name of the rate.", "example": "Day"}, "fromTime": {"type": "string", "description": "The start time of the TOU rate", "example": "14:00"}, "toTime": {"type": "string", "description": "The end time of the TOU rate", "example": "14:00"}, "costPerKwhInPence": {"type": "string", "description": "The pence cost per kwh for the specific elec rate"}, "eco7ElecConsumptionPerYearInKwh": {"type": "string", "description": "The elec consumption per year for eco7 meter(day or night)"}}}, "CreateQuoteForRenewal": {"type": "object", "required": ["generateQuotesForEvTariffs"], "properties": {"generateQuotesForEvTariffs": {"type": "boolean"}, "generateQuotesForInnovationTariff": {"type": "boolean", "description": "Flag for including Innovation tariffs", "example": false, "default": false}}}, "AlertsResponse": {"type": "object", "required": ["alerts"], "properties": {"alerts": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}, "marketingBanner": {"$ref": "#/components/schemas/MarketingBanner"}}}, "Alert": {"type": "object", "description": "The alerts that are active for the account", "required": ["name", "severity", "title", "subtitle", "isDismissible", "tags"], "properties": {"name": {"$ref": "#/components/schemas/AlertName"}, "severity": {"$ref": "#/components/schemas/AlertSeverity"}, "title": {"description": "Title for the alert, to be shown onscreen", "type": "string"}, "subtitle": {"description": "Body text for the alert, to be shown onscreen", "type": "string"}, "buttonText": {"description": "Label text for the button on the alert", "type": "string"}, "buttonLink": {"description": "URL link text for the button on the alert", "type": "string"}, "actionTag": {"type": "string", "description": "The action tag of the alert", "enum": ["renew", "readings", "booking", "usage", "payments", "contactUs"]}, "isDismissible": {"type": "boolean", "description": "Whether the alert can be dismissed"}, "tags": {"type": "array", "description": "List of domain tags for the alert", "format": "set", "items": {"$ref": "#/components/schemas/AlertTag"}}}}, "AlertName": {"type": "string", "description": "Name of the alert", "enum": ["fixedTariffRenewal", "renewSuccess", "missingSmartRead", "smartMeterInstallationBooked", "noPriorDirectDebit", "failingDirectDebitPayment", "failedDirectDebitSetup", "missingDirectDebitOnNonDDTariff", "cancelledDirectDebit", "lossInitiatedDirectDebitWarning", "partialLossInitiated", "cancelledDirectDebitDuringSwitchAway", "missingManualReadFirstNotice", "failedLastRead", "missingElectricityOMR", "missingGasOMR", "accountClosed", "onboardingAdditionalMeterpoint", "recentlyOnboarded", "enrolmentObjected", "enrolmentRejected", "seasonalPaymentNotice", "winterPaymentNotice", "unbilledAccount", "onboardingSmartIssue1", "onboardingSmartIssue2", "onboardingSmartSuccess", "switchCancelled", "outstandingDepositRequest", "smartPayAsYouGoPending", "smartPayAsYouGoOn"]}, "AlertSeverity": {"type": "string", "description": "Severity of the alert", "enum": ["info", "warning", "critical", "success"]}, "DeactivateAlertRequest": {"type": "object", "description": "Request for deactivating an alert", "required": ["name"], "properties": {"name": {"$ref": "#/components/schemas/AlertName"}}}, "AlertTag": {"type": "string", "description": "A domain associated with an alert", "enum": ["home", "tariff"]}, "MarketingBanner": {"type": "object", "description": "The marketing banners that are active for the account", "required": ["name", "title", "subtitle", "buttonText", "buttonLink", "illustrationName", "tags"], "properties": {"name": {"$ref": "#/components/schemas/MarketingBannerName"}, "title": {"description": "Title for the banner, to be shown onscreen", "type": "string"}, "subtitle": {"description": "Body text for the banner, to be shown onscreen", "type": "string"}, "buttonText": {"description": "Label text for the button on the banner", "type": "string"}, "buttonLink": {"description": "URL link text for the button on the banner", "type": "string"}, "illustrationName": {"description": "Name of the illustration to be used for the banner", "type": "string"}, "tags": {"type": "array", "description": "List of domain tags for the banner", "format": "set", "items": {"$ref": "#/components/schemas/BannerTag"}}}}, "BannerTag": {"type": "string", "description": "A domain associated with a banner", "enum": ["home", "usage"]}, "MarketingBannerName": {"type": "string", "description": "Name/identifier of the banner", "enum": ["evTrad", "renewalReminder", "switchToFixedTariff", "bookSmartMeterInstallation", "referFriend", "optIntoMarketing", "homeCover", "e<PERSON><PERSON><PERSON><PERSON>", "heatPumps", "voteForEnergySource", "solarQuote", "getSmartTrackUsage", "updateMarketingInterests"]}, "Error": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "The session id.", "example": "cc8543b5-5ee8-4a48-994c-d5a8f2a26269"}, "traceId": {"type": "string", "description": "The trace id.", "example": "5ca9a22f-4763-4d2a-9c1e-8f1f34540373"}, "message": {"type": "string", "description": "The exception message.", "example": "Authentication has failed."}, "timestamp": {"type": "string", "format": "date-time", "description": "The time at which the error was thrown", "example": "2021-07-19T13:03:08.249499+01:00"}, "errorCategory": {"type": "string", "description": "The category of the error.", "example": "gateway-external"}, "errorCode": {"$ref": "#/components/schemas/ErrorCode"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}, "causes": {"type": "array", "items": {"$ref": "#/components/schemas/Cause"}}}}, "Field": {"type": "object", "properties": {"name": {"type": "string"}, "errorCategory": {"type": "string"}, "errorCode": {"$ref": "#/components/schemas/ErrorCode"}}}, "Cause": {"type": "object", "properties": {"name": {"type": "string"}, "errorCategory": {"type": "string"}, "errorCode": {"$ref": "#/components/schemas/ErrorCode"}, "entityId": {"type": "integer", "format": "int64", "example": 123, "description": "Identifier of the entity that the error is linked to (e.g. meterId, meterPointId etc.)"}}}, "ErrorCode": {"type": "string", "enum": ["invalidCredentials", "accountLocked", "unknownError", "readSubmissionError", "readSubmissionValidationError", "invalidBankingDetails", "invalidFieldFormat", "notFound", "readingTooLow", "readingTooHigh", "meterPointSupplyEnding", "genericError", "100", "101", "001", "002", "003", "004", "999", "100", "200", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127", "400", "404", "429", "503", "500"]}, "ChangeOfTenancyMoveInRequest": {"type": "object", "description": "change of tenancy - move in request", "required": ["billingAccountId", "newOccupantContact", "supplyAddress", "firstDateOfResponsibility"], "properties": {"billingAccountId": {"type": "integer", "format": "int64", "description": "billing account id"}, "newOccupantContact": {"$ref": "#/components/schemas/OccupantContact"}, "supplyAddress": {"$ref": "#/components/schemas/Address"}, "firstDateOfResponsibility": {"type": "string", "format": "date", "example": "2024-01-01", "description": "first date of responsibility"}, "directDebitInfo": {"$ref": "#/components/schemas/DirectDebitInfo"}, "billingAddress": {"$ref": "#/components/schemas/Address"}, "meterReadings": {"type": "array", "items": {"$ref": "#/components/schemas/MoveInMeterReading"}}}}, "OccupantContact": {"type": "object", "required": ["firstName", "lastName", "email", "phone1", "dateOfBirth", "marketingOptIn"], "properties": {"title": {"type": "string", "example": "Miss", "description": "title"}, "firstName": {"type": "string", "example": "<PERSON>", "description": "first name"}, "lastName": {"type": "string", "example": "<PERSON>", "description": "last name"}, "email": {"$ref": "#/components/schemas/emailAddress"}, "phone1": {"$ref": "#/components/schemas/phoneNumber"}, "marketingOptIn": {"type": "boolean", "example": true, "description": "marketing opt in"}, "dateOfBirth": {"type": "string", "format": "date", "example": "2000-01-24", "description": "date of birth"}}}, "DirectDebitInfo": {"type": "object", "required": ["sortCode", "accountNumber", "accountName", "paymentAmount", "paymentDayOfMonth"], "properties": {"sortCode": {"$ref": "components.json#/components/schemas/BankingSortCode"}, "accountNumber": {"$ref": "components.json#/components/schemas/BankingAccountNumber"}, "accountName": {"description": "account in the name of", "example": "bank account name", "type": "string"}, "paymentAmount": {"type": "integer", "format": "int64", "description": "payment amount", "example": "25", "minimum": 25}, "paymentDayOfMonth": {"type": "integer", "format": "int64", "example": "5", "description": "payment day of the month", "minimum": 1, "maximum": 28}}}, "EnergyUsageGranularity": {"type": "string", "description": "Represents the presented granularity of energy usage; to what level the energy usage values are aggregated to.", "enum": ["HH", "DAY", "WEEK", "MONTH", "YEAR"], "example": "DAY"}, "EnergyUsageFuelType": {"type": "string", "enum": ["GAS", "ELECTRICITY"], "example": "GAS"}, "EnergyUsageValue": {"type": "object", "required": ["startDate", "endDate"], "properties": {"kwh": {"type": "number", "description": "The aggregated kwh value of the period. If this value is null, this represents a missing value for this period. A zero value represents no usage.", "example": 100.0, "format": "double"}, "cost": {"type": "number", "description": "The aggregated cost value in pence of the period. If this value is null, this represents a missing value for this period. A zero value represents no cost.", "example": 100.0, "format": "double"}, "startDate": {"type": "string", "description": "The start date-time that the kwh / cost value relates to.", "format": "date-time", "example": "2024-01-01T00:00:00"}, "endDate": {"type": "string", "description": "The end date-time that the kwh / cost value relates to.", "format": "date-time", "example": "2024-01-01T00:00:00"}}}, "EnergyUsageResponse": {"type": "array", "items": {"$ref": "#/components/schemas/EnergyUsage"}}, "EnergyUsage": {"type": "object", "required": ["meterpointId", "registerId", "rateName", "startDate", "fuelType", "granularity", "endDate", "values"], "description": "Represents a collection of energy usage values for a single rate, on a single meterpoint.", "properties": {"meterpointId": {"type": "integer", "description": "The meterpoint ID that the collection of energy usage values relates to.", "example": 1, "format": "int64"}, "registerId": {"type": "integer", "description": "The register ID that the collection of energy usage values relates to.", "example": 1, "format": "int64"}, "rateName": {"type": "string", "description": "The name of the rate that the collection of energy usage values relates to.", "example": "day"}, "fuelType": {"$ref": "#/components/schemas/EnergyUsageFuelType"}, "granularity": {"$ref": "#/components/schemas/EnergyUsageGranularity"}, "startDate": {"type": "string", "description": "The start date of the requested energy usage period. This is the value from the request.", "format": "date-time", "example": "2024-01-01T00:00:00"}, "endDate": {"type": "string", "description": "The end date of the requested energy usage period. This is the value from the request.", "format": "date-time", "example": "2024-01-01T00:00:00"}, "values": {"type": "array", "description": "A collection of values which represent each distinct granularity period, between the requested start and end time. For example, with a granularity of 'DAY' and 7 days requests, the values array should return 7 energy usage values.", "items": {"$ref": "#/components/schemas/EnergyUsageValue"}}}}, "SmartPayAsYouGoQuoteRequest": {"type": "object", "required": ["activeStartDate"], "description": "Represents a spayg quote request.", "properties": {"activeStartDate": {"type": "string", "description": "The date of spayg start.", "format": "date", "example": "2024-01-01"}}}, "SmartPayAsYouGoQuoteResponse": {"type": "object", "required": ["tariffName", "fuelType"], "properties": {"tariffName": {"type": "string", "description": "The name of the tariff", "example": "So Sunflower One Year"}, "fuelType": {"type": "string", "description": "The fuel type of the quote request", "example": "Dual Fuel"}, "elecProductType": {"$ref": "#/components/schemas/ElecProductType"}, "elecRates": {"type": "array", "items": {"$ref": "#/components/schemas/ElecSpaygTariffRate"}}, "elecStandingChargeInPence": {"type": "string", "description": "The standing charge of the elec product"}, "elecConsumptionPerYearInKwh": {"type": "string", "description": "The total kwh consumption for elec per year"}, "gasCostPerKwhInPence": {"type": "string", "description": "The cost per kwh in pence for gas product"}, "gasStandingChargeInPence": {"type": "string", "description": "The standing charge of the gas product"}, "gasConsumptionPerYearInKwh": {"type": "string", "description": "The total kwh consumption for gas per year"}, "earlyExitFeePerFuelInPounds": {"type": "string", "description": "The exit fee per fuel in pounds"}, "elecProductCode": {"type": "string", "description": "The product for the elec product"}, "gasProductCode": {"type": "string", "description": "The product for the gas product"}}}, "ElecSpaygTariffRate": {"type": "object", "required": ["costPerKwhInPence"], "properties": {"name": {"type": "string", "description": "The name of the rate.", "example": "Day"}, "costPerKwhInPence": {"type": "string", "description": "The pence cost per kwh for the specific elec rate"}, "eco7ElecConsumptionPerYearInKwh": {"type": "string", "description": "The elec consumption per year for eco7 meter(day or night)"}}}, "CreateEvChargerQuoteResponse": {"type": "object", "properties": {"quoteId": {"type": "integer", "format": "int64", "description": "the quote ID"}, "createdAt": {"type": "string", "format": "date-time"}}}, "CreateEvChargerQuoteRequest": {"type": "object", "required": ["firstName", "lastName", "postCode", "emailAddress", "phoneNumber"], "properties": {"title": {"type": "string", "description": "the user's title"}, "firstName": {"type": "string", "description": "The user's first name"}, "lastName": {"type": "string", "description": "The user's last name"}, "postCode": {"$ref": "#/components/schemas/Postcode"}, "address": {"type": "string", "description": "The address of the property to be quoted for installation"}, "emailAddress": {"$ref": "#/components/schemas/emailAddress"}, "phoneNumber": {"$ref": "#/components/schemas/phoneNumber"}, "hasOffStreetParking": {"type": "boolean", "description": "Users must have off-street parking to qualify for EV charger installation"}, "isAuthorisedToAlter": {"type": "boolean", "description": "Users must be authorised to make alterations to the property, (such as being an owner/occupier) to qualify for EV charger installation"}, "accountNumber": {"type": "string", "description": "The user's So Energy account number, if they receive their energy supply from So Energy"}, "unitRate": {"type": "string", "description": "The unit rate of the user's electricity supply, if they receive their energy supply from So Energy"}, "tariff": {"type": "string", "description": "The So Energy tariff name on the user's account, if they receive their energy supply from So Energy"}}}, "RenewalCreationRequest": {"type": "object", "properties": {"creditExitFee": {"type": "boolean", "default": false, "description": "credit exit fee flag, true if the customer has a credit exit fee", "example": true}, "paymentSchedule": {"$ref": "#/components/schemas/RenewalPaymentSchedule"}, "newAgreements": {"type": "array", "items": {"$ref": "#/components/schemas/RenewalAgreement"}}, "tariffName": {"type": "string", "description": "optional tariff name", "example": "So Sunflower One Year"}, "durationMonths": {"type": "integer", "format": "int32", "description": "optional duration in months", "example": 12}}, "required": ["creditExitFee", "paymentSchedule", "newAgreements"]}, "RenewalPaymentSchedule": {"type": "object", "properties": {"amount": {"type": "number", "format": "double", "description": "monthly cost in pounds", "example": 180.5}, "seasonal": {"type": "boolean", "description": "seasonal payment flag, true if the customer has seasonal payments", "example": false}, "estimatedAnnualCost": {"type": "number", "format": "double", "description": "estimated annual cost in pounds", "example": 1250.5}}}, "RenewalAgreement": {"type": "object", "properties": {"meterPointIdentifier": {"type": "string", "description": "meter point identifier for the renewal agreement", "example": "1591020102350"}, "annualUsage": {"type": "number", "format": "double", "description": "optional annual usage in kWh", "example": 2750}, "productCode": {"type": "string", "description": "product code for the renewal agreement", "example": "E1R-SPBS-24M-C"}, "creditExitFee": {"type": "number", "format": "double", "description": "credit exit fee if exists", "example": 50}, "startDate": {"type": "string", "format": "date", "description": "start date of agreement", "example": "2026-02-04"}, "unitRates": {"$ref": "#/components/schemas/RenewalUnitRates"}}, "required": ["meterPointIdentifier", "productCode", "startDate"]}, "RenewalUnitRates": {"type": "object", "properties": {"day": {"type": "number", "format": "double", "description": "day rate in kwh", "example": 0.28}, "night": {"type": "number", "format": "double", "description": "night rate in kwh", "example": 0.18}, "standard": {"type": "number", "format": "double", "description": "standard rate in kwh", "example": 0.25}, "standingCharge": {"type": "number", "format": "double", "description": "standing charge in kwh", "example": 0.35}}}, "RenewalCreationResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "Renewal ID"}, "hasComplexMeterConfiguration": {"type": "boolean", "description": "indicates if the renewal has a complex meter configuration", "example": false}, "hasOutOfSyncTariffs": {"type": "boolean", "description": "indicates if the renewal has out of sync tariffs", "example": false}}}, "ReadFrequency": {"type": "string", "enum": ["MONTHLY", "DAILY", "HALF_HOURLY"], "description": "How frequently reads are generated on a meterpoint"}, "AccountPreference": {"type": "object", "required": ["name", "value", "type"], "properties": {"name": {"type": "string", "description": "The name of the account preference", "example": "marketingOptIn"}, "value": {"type": "string", "description": "The value the account preference takes", "example": "false"}, "type": {"$ref": "#/components/schemas/AccountPreferenceType"}}}, "AccountPreferenceType": {"type": "string", "enum": ["MARKETING_PREFERENCE", "CONTACT_PREFERENCE"], "description": "Represents the different types of account preferences available,"}, "InnovationTariffEligibilityResponse": {"type": "object", "properties": {"isEligible": {"type": "boolean", "description": "Innovation tariff eligibility check overall result", "example": true}, "hasInvite": {"type": "boolean", "description": "Indicates if user has invite to the innovation tariff", "example": true}}}, "VulnerabilityCategoriesResponse": {"type": "object", "required": ["id", "name", "description", "vulnerabilities"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "name": {"type": "string", "description": "Vulnerability category name"}, "description": {"type": "string", "description": "Vulnerability category description"}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/Vulnerability"}, "description": "Vulnerabilities within the category"}}}, "Vulnerability": {"type": "object", "required": ["id", "displayOrder", "label", "description", "requiresEndDate"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "displayOrder": {"type": "integer", "format": "int32", "description": "The order in which we show the vulnerability"}, "label": {"type": "string", "description": "Vulnerability title"}, "description": {"type": "string", "description": "Vulnerability description"}, "requiresEndDate": {"type": "boolean", "description": "End date for the vulnerability"}}}, "ServicesResponse": {"type": "object", "required": ["id", "title", "label", "description", "alwaysOn", "availableNow"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "title": {"type": "string", "description": "Service title"}, "label": {"type": "string", "description": "Service label"}, "description": {"type": "string", "description": "Service description"}, "alwaysOn": {"type": "boolean", "description": "Defines if service is on by default"}, "availableNow": {"type": "boolean", "description": "Defines if service is currently available"}, "serviceItems": {"type": "array", "description": "Extra items required such as text or date field", "items": {"$ref": "#/components/schemas/ServiceItem"}}}}, "ServiceItem": {"type": "object", "required": ["id", "name", "description", "type", "required", "displayOrder"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "name": {"type": "string", "description": "Service Item name"}, "description": {"type": "string", "description": "Service item description"}, "type": {"type": "string", "description": "Type of item to show the UI such as date or input"}, "required": {"type": "boolean", "description": "Defines if field is required"}, "displayOrder": {"type": "integer", "format": "int64", "description": "The order in which we show the items"}, "placeholder": {"type": "string", "description": "Field placeholder message"}}}, "PsrCustomerVulnerability": {"type": "object", "required": ["id", "startDate"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "endDate": {"type": "string", "format": "date", "example": "2024-01-01"}}}, "PsrCustomerService": {"type": "object", "required": ["id", "startDate"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "endDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "extraData": {"type": "array", "items": {"$ref": "#/components/schemas/PsrExtraDataField"}}}}, "PsrExtraDataField": {"type": "object", "required": ["id", "field", "value"], "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "field": {"type": "string", "example": "2024-01-01"}, "value": {"type": "string", "example": "2024-01-01"}}}, "PsrCustomerSettings": {"type": "object", "required": ["accountId", "customerVulnerabilities", "customerServices"], "properties": {"accountId": {"type": "integer", "format": "int64"}, "customerVulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/PsrCustomerVulnerability"}}, "customerServices": {"type": "array", "items": {"$ref": "#/components/schemas/PsrCustomerService"}}}}, "PsrCustomerVulnerabilityResponse": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PsrCustomerVulnerability"}], "required": ["label"], "properties": {"label": {"type": "string", "example": "Over 65"}}}, "PsrCustomerServiceResponse": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PsrCustomerService"}], "required": ["label", "availableNow"], "properties": {"label": {"type": "string", "example": "Nominee scheme"}, "availableNow": {"type": "boolean", "example": true}}}, "PsrCustomerSettingsResponse": {"type": "object", "required": ["customerVulnerabilities", "customerServices"], "properties": {"customerVulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/PsrCustomerVulnerabilityResponse"}}, "customerServices": {"type": "array", "items": {"$ref": "#/components/schemas/PsrCustomerServiceResponse"}}}}, "MakePaymentRequest": {"type": "object", "required": ["customerNumber", "email", "amountInPence", "token", "paymentUseCase"], "properties": {"customerNumber": {"type": "string", "example": "00112233"}, "email": {"type": "string", "example": "<EMAIL>"}, "amountInPence": {"type": "integer", "format": "int64", "description": "Payment amount in pence, must be between 500 and 500000", "example": "500"}, "token": {"type": "string", "description": "Token from payment iFrame"}, "successRedirectUrl": {"type": "string"}, "failureRedirectUrl": {"type": "string"}, "paymentUseCase": {"$ref": "#/components/schemas/PaymentUseCase"}, "customerName": {"type": "string", "example": "Token from payment iFrame"}}}, "MakePaymentResponse": {"type": "object", "required": ["confirmationNumber", "amountInPence", "externalId"], "properties": {"confirmationNumber": {"type": "string"}, "paymentId": {"type": "integer", "format": "int32"}, "amountInPence": {"type": "integer", "format": "int64", "description": "Payment amount in pence, must be between 500 and 500000", "example": "500"}, "authorisedAt": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00Z", "description": "Date and time of payment authorisation"}, "redirectUrl": {"type": "string", "description": "When 3DS Challenge is Required"}, "externalId": {"type": "string", "description": "The ID of the payment in the external payments system."}}}, "Payment": {"type": "object", "required": ["id", "amount", "reference", "source", "status"], "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "juniferPaymentId": {"type": "integer", "format": "int64"}, "customerNumber": {"type": "string"}, "accountNumber": {"$ref": "components.json#/components/schemas/billingAccountNumber"}, "amount": {"type": "integer", "format": "int64"}, "cardLast4Digits": {"type": "string"}, "cardExpiryDate": {"type": "string"}, "reference": {"type": "string"}, "errorCode": {"type": "string"}, "errorMessage": {"type": "string"}, "authorisedAt": {"type": "string", "format": "date-time", "example": "2025-01-01T00:00:00Z", "description": "Date and time of payment authorisation"}, "externalId": {"type": "string", "description": "The ID of the payment in the external payments system."}, "ckoSessionId": {"type": "string"}, "status": {"type": "string"}, "source": {"type": "string"}}}, "PaymentUseCase": {"type": "string", "enum": ["SOLAR", "WEBSITE", "ELECTRIC_VEHICLE", "MOTO"]}, "PaymentIdentifierType": {"type": "string", "enum": ["EXTERNAL_ID", "CKO_SESSION_ID"]}}}}