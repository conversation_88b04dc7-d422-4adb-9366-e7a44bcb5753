package energy.so.core.api.gateway.cache

import energy.so.customers.v2.preferences.AccountPreferencesGrpc
import energy.so.customers.v2.preferences.GetAccountPreferencesResponse
import energy.so.customers.v2.preferences.getAccountPreferencesRequest
import java.time.Duration
import org.ehcache.CacheManager
import org.ehcache.config.builders.CacheConfigurationBuilder
import org.ehcache.config.builders.CacheManagerBuilder
import org.ehcache.config.builders.ExpiryPolicyBuilder
import org.ehcache.config.builders.ResourcePoolsBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

const val ACCOUNT_PREFERENCE_CACHE = "accountPreferenceCache"

@Component
class AccountPreferencesCache(
    private val preferencesClient: AccountPreferencesGrpc.AccountPreferencesBlockingStub,
    @Value("\${caches.account-preference-cache-expiry}") private val cacheExpiry: Long = 300L,
) {

    val cacheManager: CacheManager = CacheManagerBuilder.newCacheManagerBuilder().withCache(
        ACCOUNT_PREFERENCE_CACHE,
        CacheConfigurationBuilder.newCacheConfigurationBuilder(
            String::class.java,
            GetAccountPreferencesResponse::class.java,
            ResourcePoolsBuilder.heap(1000),
        ).withExpiry(
            ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(cacheExpiry))
        )
    ).build(true)


    fun getAccountPreferences(accountId: Long): GetAccountPreferencesResponse {
        val preferenceCache = getAccountPreferenceCache()
        var preferences = preferenceCache.get(accountId.toString())
        if (preferences == null) {
            preferences = preferencesClient.getAccountPreferences(
                getAccountPreferencesRequest { this.accountId = accountId }
            )
            preferenceCache.put(accountId.toString(), preferences)
        }
        return preferences
    }

    internal fun clear() = getAccountPreferenceCache().clear()

    internal fun getAccountPreferenceCache() = cacheManager.getCache(
        ACCOUNT_PREFERENCE_CACHE,
        String::class.java,
        GetAccountPreferencesResponse::class.java
    )

    internal fun populate(accountId: Long, preferences: GetAccountPreferencesResponse) =
        getAccountPreferenceCache().put(accountId.toString(), preferences)
}