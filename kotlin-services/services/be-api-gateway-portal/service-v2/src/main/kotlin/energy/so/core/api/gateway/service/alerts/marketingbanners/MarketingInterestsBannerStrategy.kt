package energy.so.core.api.gateway.service.alerts.marketingbanners

import energy.so.commons.logging.TraceableLogging
import energy.so.core.api.gateway.cache.AccountPreferencesCache
import energy.so.core.api.gateway.service.alerts.marketingbanners.MarketingBanners.updateMarketingInterests
import energy.so.customers.v2.preferences.PreferenceType
import energy.so.generated.portal.model.AccountStates
import energy.so.generated.portal.model.BannerTag
import energy.so.generated.portal.model.MarketingBanner
import energy.so.generated.portal.model.MarketingBannerName
import org.springframework.stereotype.Component

const val EMAIL_PREFERENCE = "email"

@Component
class MarketingInterestsBannerStrategy(
    private val preferencesCache: AccountPreferencesCache,
) : MarketingBannerStrategy {

    private val logger = TraceableLogging.logger { }

    override val tags: Set<BannerTag>
        get() = updateMarketingInterests.tags

    override val bannerName: MarketingBannerName
        get() = MarketingBannerName.OPT_INTO_MARKETING

    override fun computeBanner(
        accountId: Long,
        accountStates: AccountStates,
    ): MarketingBanner? {

        val preferences = try {
            preferencesCache.getAccountPreferences(accountId)
        } catch (e: Exception) {
            // A failure here should not crash the banner logic
            logger.error(e) { "Unable to retrieve marketing preferences for account $accountId" }
            return null
        }

        val emailOptIn = preferences.preferencesList.any {
            it.type == PreferenceType.CONTACT_PREFERENCE && it.name == EMAIL_PREFERENCE && it.value == "true"
        }
        val noSetPreference = preferences.preferencesList.none {
            it.type == PreferenceType.MARKETING_PREFERENCE && it.value == "true"
        }

        return if (emailOptIn && noSetPreference) {
            updateMarketingInterests
        } else {
            null
        }
    }
}