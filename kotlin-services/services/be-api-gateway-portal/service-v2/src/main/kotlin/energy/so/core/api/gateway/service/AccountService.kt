package energy.so.core.api.gateway.service

import energy.so.ac.junifer.v1.assets.AssetServiceGrpc
import energy.so.ac.junifer.v1.assets.EstimatedUsage
import energy.so.ac.junifer.v1.assets.estimatedUsageRequest
import energy.so.ac.junifer.v1.meterpoint.MeterPointServiceGrpc
import energy.so.ac.junifer.v1.meterpoint.readFrequencyConsentRequest
import energy.so.assets.meterPoints.v2.MeterPoint
import energy.so.assets.meterPoints.v2.MeterPointsGrpc.MeterPointsBlockingStub
import energy.so.assets.meterPoints.v2.getMeterPointsRequest
import energy.so.assets.meterReadings.v2.MeterReadingsGrpc.MeterReadingsBlockingStub
import energy.so.assets.meterReadings.v2.meterReadingStatusRequest
import energy.so.commons.NullableTimestamp
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.dtos.idRequestList
import energy.so.core.api.gateway.security.SecurityCoroutineContext
import energy.so.core.api.gateway.service.accountstates.strategies.SupplyStateStrategy
import energy.so.core.api.gateway.service.mappers.AccountMapper.isOnSupply
import energy.so.core.api.gateway.service.mappers.AccountMapper.isTerminating
import energy.so.core.api.gateway.service.mappers.AccountMapper.toResponse
import energy.so.core.api.gateway.service.mappers.AgreementMapper.toAgreementsResponse
import energy.so.core.api.gateway.service.mappers.AssetsMapper.toResponse
import energy.so.core.api.gateway.service.mappers.isFutureDated
import energy.so.customers.agreements.v2.Agreement
import energy.so.customers.agreements.v2.AgreementType
import energy.so.customers.agreements.v2.AgreementsGrpc.AgreementsBlockingStub
import energy.so.customers.agreements.v2.getAgreementByMeterpointIdRequest
import energy.so.customers.billingaccounts.v2.BillingAccount
import energy.so.customers.billingaccounts.v2.BillingAccountsGrpc.BillingAccountsBlockingStub
import energy.so.customers.v2.configurations.ConfigurationName
import energy.so.customers.v2.configurations.ConfigurationsServiceGrpc.ConfigurationsServiceBlockingStub
import energy.so.customers.v2.configurations.getConfigurationRequest
import energy.so.customers.v2.customers.CustomersGrpc.CustomersBlockingStub
import energy.so.customers.v2.customers.ProductDetailsResponse
import energy.so.generated.portal.api.AccountApiService
import energy.so.generated.portal.model.AgreementsResponse
import energy.so.generated.portal.model.GetAccountResponse
import energy.so.generated.portal.model.MeterResponse
import energy.so.generated.portal.model.ReadFrequency
import energy.so.generated.portal.model.SupplyState
import energy.so.payments.v2.BillingAccountTransactionsGrpc.BillingAccountTransactionsBlockingStub
import energy.so.payments.v2.BillingAccountTransactionsResponse
import energy.so.products.v2.ProductDetails
import energy.so.products.v2.ProductDetailsServiceGrpc.ProductDetailsServiceBlockingStub
import energy.so.products.v2.ProductVariantType
import java.time.Duration
import java.time.LocalDate
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

private const val PENCE_TO_POUND_CONVERSION_FACTOR = 100
private const val EV_OFF_PEAK_USAGE_PERCENT = 0.41

@Service
class AccountService(
    private val billingAccountTransactionsClient: BillingAccountTransactionsBlockingStub,
    private val billingAccountsClient: BillingAccountsBlockingStub,
    private val customersClient: CustomersBlockingStub,
    private val meterPointsBlockingStub: MeterPointsBlockingStub,
    private val meterReadingClient: MeterReadingsBlockingStub,
    private val productDetailsClient: ProductDetailsServiceBlockingStub,
    private val accountUtilityService: AccountUtilityService,
    private val supplyStateStrategy: SupplyStateStrategy,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO,
    private val configurationsClient: ConfigurationsServiceBlockingStub,
    private val agreementsClient: AgreementsBlockingStub,
    private val assetServiceClient: AssetServiceGrpc.AssetServiceBlockingStub,
    private val acMeterPointClient: MeterPointServiceGrpc.MeterPointServiceBlockingStub,
) : AccountApiService {

    private val logger = TraceableLogging.logger { }

    override fun getAccount(accountId: Long): GetAccountResponse =
        runBlocking {
            val deferredResults = listOf(
                async(dispatcher + SecurityCoroutineContext()) {
                    billingAccountTransactionsClient.getByBillingAccountId(
                        idRequest {
                            id = accountId
                        }
                    )
                },
                async(dispatcher + SecurityCoroutineContext()) {
                    customersClient.getProductDetailsByBillingAccountId(
                        idRequest {
                            id = accountId
                        }
                    )
                },
                async(dispatcher + SecurityCoroutineContext()) {
                    billingAccountsClient.getBillingAccountById(
                        idRequest {
                            id = accountId
                        }
                    )
                },
                async(dispatcher + SecurityCoroutineContext()) {
                    supplyStateStrategy.computeState(accountId)
                }
            )

            val results = deferredResults.awaitAll()
            val transactions = results[0] as BillingAccountTransactionsResponse
            val productDetails = results[1] as ProductDetailsResponse
            val billingAccount = results[2] as BillingAccount
            val supplyState = results[3] as SupplyState

            billingAccount.toResponse(transactions.billingAccountTransactionList, productDetails, supplyState)
        }

    override fun getAgreements(accountId: Long): List<AgreementsResponse> {
        val supplyState = supplyStateStrategy.computeState(accountId)
        val renewalWindowDays = getRenewalWindowDays()

        return accountUtilityService.getAgreementsByProductAccount(accountId, true)
            .flatMap { agreement ->
                val meterPointsList = meterPointsBlockingStub.getMeterPointsByIds(
                    getMeterPointsRequest {
                        ids.addAll(agreement.meterPointsList.map { it.id })
                        includeMeterReadings = false
                    }
                ).meterPointsList

                val gspGroups = meterPointsList.map { it.ukGspGroup }.toSet()

                val productDetails = productDetailsClient.getProductDetailsByProductVariantIds(
                    idRequestList {
                        idRequests.addAll(agreement.productVariantsList.map { it.id })
                    }
                ).resultsList.firstOrNull {
                    gspGroups.isEmpty() || it.gspGroup.name in gspGroups
                }

                productDetails?.let {
                    val estimatedAnnualCosts: Map<Long, MeterPointEstimatedCost> = meterPointsList.associate {
                        it.id to MeterPointEstimatedCost(it.identifier, calculateAnnualCost(it, productDetails))
                    }

                    val canRenew = computeCanRenew(
                        RenewalContext(
                            agreement = agreement,
                            renewalWindowDays = renewalWindowDays,
                            productDetails = productDetails
                        ),
                        AccountStatusContext(
                            isOnSupply = supplyState.isOnSupply(),
                            isTerminating = supplyState.isTerminating()
                        ),
                        meterPointsList
                    )

                    agreement.toAgreementsResponse(
                        productDetails,
                        canRenew,
                        renewalWindowDays,
                        estimatedAnnualCosts
                    )
                } ?: emptyList()
            }
    }

    override fun getMetersByBillingAccountId(accountId: Long): List<MeterResponse> =
        accountUtilityService.getMeterPointsByBillingAccount(
            accountId,
            AccountUtilityService.MeterPointQueryType.INCLUDE_READINGS
        )
            .flatMap { meterPoint ->

                val frequency = try {
                    acMeterPointClient.getMeterPointReadFrequency(readFrequencyConsentRequest {
                        meterPointId = meterPoint.id
                    }).readFrequency.toSiblingEnum<ReadFrequency>()
                } catch (e: Exception) {
                    logger.error(e) { "Unexpected exception while obtaining meterpoint consent for meterpoint ${meterPoint.id}." }
                    ReadFrequency.MONTHLY
                }

                try {
                    val lastMeterReadingStatus = meterReadingClient.getLastMeterReadingStatus(
                        meterReadingStatusRequest {
                            this.meterPointId = meterPoint.id
                        }
                    )
                    meterPoint.toResponse(lastMeterReadingStatus, frequency)
                } catch (e: Exception) {
                    when (e) {
                        is EntityNotFoundGrpcException -> meterPoint.toResponse(frequency = frequency)

                        else -> {
                            logger.error(
                                "Unexpected exception getting last reading status for meterpoint" +
                                        " with id: ${meterPoint.id}. Message: ${e.printStackTrace()}"
                            )
                            return meterPoint.toResponse(frequency = frequency)
                        }
                    }
                }
            }

    private fun calculateAnnualCost(meterPoint: MeterPoint, productDetails: ProductDetails?): Double? {
        val estimatedUsage = assetServiceClient.getEstimatedUsage(
            estimatedUsageRequest {
                meterPointId = meterPoint.id
                queryDate = LocalDate.now().toTimestamp()
                useHistoricalReadings = true.toNullableBoolean()
            }
        )

        return calculateYearlySpendPoundsV2(
            productDetails ?: throw IllegalArgumentException("Product details cannot be null"),
            estimatedUsage,
            productDetails.fuel == ProductVariantType.ELECTRICITY_MULTIPLE
        )
    }

    private fun EstimatedUsage.getRateUsage(rateName: String) =
        ratesList.firstOrNull { it.rateName == rateName }?.usage ?: 0.0

    private fun calculateYearlySpendPoundsV2(
        productDetail: ProductDetails,
        estimatedUsage: EstimatedUsage,
        isDualFuel: Boolean,
    ): Double? {
        val elecDaytimeKwhPerYear = estimatedUsage.getRateUsage("Day")
        val elecNighttimeKwhPerYear = estimatedUsage.getRateUsage("Night")
        val standardKwhPerYear = estimatedUsage.getRateUsage("Standard")

        val standingChargePencePerDay = productDetail.standingCharge
        val dualFuelDiscountPoundsPerYear = productDetail.dualFuelDiscount
        val onlineDiscountPoundsPerYear = productDetail.onlineDiscount
        val standingChargeSpend = standingChargePencePerDay.times(daysInYear())
        val dualFuelDiscount = if (isDualFuel) -dualFuelDiscountPoundsPerYear else 0.0
        val onlineDiscount = -onlineDiscountPoundsPerYear
        val standingChargeAndDiscounts = standingChargeSpend.plus(dualFuelDiscount).plus(onlineDiscount)

        return if (energy.so.products.v2.RateType.TOU == productDetail.rateType) {
            val unitRatePeak = productDetail.tariffRatesList.maxBy { it.rateValue.value }.rateValue.value
            val unitRateOffPeak = productDetail.tariffRatesList.minBy { it.rateValue.value }.rateValue.value
            val offPeakConsumption = standardKwhPerYear.times(EV_OFF_PEAK_USAGE_PERCENT)
            val peakConsumption = standardKwhPerYear.minus(offPeakConsumption)
            val offPeakTimeSpend = unitRateOffPeak.times(offPeakConsumption)
            val peakTimeSpend = unitRatePeak.times(peakConsumption)
            if (standardKwhPerYear == 0.0)
                null // this means when no estimated usage found in junifer, return null for the annual cost
            else
                peakTimeSpend.plus(offPeakTimeSpend).plus(standingChargeAndDiscounts)
                    .div(PENCE_TO_POUND_CONVERSION_FACTOR)
        } else if (ProductVariantType.ELECTRICITY_MULTIPLE == productDetail.fuel) {
            val dayTimeCost = productDetail.tariffRatesList.first { "day" == it.rateName.value }
                .rateValue.value.times(elecDaytimeKwhPerYear)
            val nightTimeCost = productDetail.tariffRatesList.first { "night" == it.rateName.value }
                .rateValue.value.times(elecNighttimeKwhPerYear)

            dayTimeCost.plus(nightTimeCost).plus(standingChargeAndDiscounts) / PENCE_TO_POUND_CONVERSION_FACTOR
        } else {
            productDetail.tariffRatesList.first().rateValue.value
                .times(standardKwhPerYear)
                .plus(standingChargeAndDiscounts) / PENCE_TO_POUND_CONVERSION_FACTOR
        }
    }

    fun daysInYear(startingDaysFromToday: Long = 19): Long {
        val from = LocalDate.now().plusDays(startingDaysFromToday)
        return Duration.between(from.atStartOfDay(), from.plusYears(1).atStartOfDay()).toDays()
    }

    private fun getRenewalWindowDays() = configurationsClient.getConfiguration(
        getConfigurationRequest {
            configurationName = ConfigurationName.RENEWAL_WINDOW.toSiblingEnum<ConfigurationName>()
        }
    ).configurationValue.toLong()

    private fun computeCanRenew(
        renewalContext: RenewalContext,
        accountStatus: AccountStatusContext,
        meterPoints: List<MeterPoint>,
    ): Boolean {
        val conditionsMet = accountStatus.isOnSupply &&
                !accountStatus.isTerminating &&
                !hasFutureAgreements(meterPoints) &&
                !(renewalContext.productDetails!!.export)

        return conditionsMet &&
                (
                        isInRenewalPeriod(renewalContext.renewalWindowDays, renewalContext.agreement.toDate) ||
                                renewalContext.agreement.type == AgreementType.OPEN_ENDED
                        )
    }

    private fun hasFutureAgreements(meterPointsList: List<MeterPoint>): Boolean {
        return meterPointsList.any { meterPoint ->
            agreementsClient.getAgreementByMeterpointId(
                getAgreementByMeterpointIdRequest {
                    id = meterPoint.id
                    firstAgreement = false // sorts by desc order
                    includeCancelledAndDeletedAgreements = false
                    includeFutureAgreements = true
                }
            ).isFutureDated()
        }
    }

    private fun isInRenewalPeriod(renewalWindowDays: Long, toDate: NullableTimestamp) = toDate.getValueOrNull()?.let {
        !it.toLocalDate().minusDays(renewalWindowDays).isAfter(LocalDate.now())
    } ?: false
}

class MeterPointEstimatedCost(
    val identifier: String,
    val cost: Double?,
)

data class RenewalContext(
    val agreement: Agreement,
    val renewalWindowDays: Long,
    val productDetails: ProductDetails?,
)

data class AccountStatusContext(
    val isOnSupply: Boolean,
    val isTerminating: Boolean,
)
