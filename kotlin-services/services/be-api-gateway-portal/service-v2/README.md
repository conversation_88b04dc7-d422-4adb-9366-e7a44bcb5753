# BE API Gateway Portal V2 - Spring Version

A new version of the Gateway Portal, simplified, implemented using Spring Framework that will serve the new version of My account

## How to author
As with all our other gateways the service endpoint and data objects are generated 

#### How to generate
We  generate via the `openApi.json` file, found `src/main/resources/static`.
There is a co-located ancillary file, `components.json` where we can define common or repeated data objects.

Once you have defined your endpoint and data object you should run the gradle task `openApiGenerate`. 

#### What does this do?
Let's take an example to see what is produced. We'll create a simple endpoint that take a reference and return a result as a date.

*We are just going to add some of the data we'd add to openApi.json*

```json
    "/api/v1/accounts/{accountId}/smartPayAsYouGo/quote": {
      "post": {
        "operationId": "createSmartPayAsYouGoQuote",
        "tags": [
          "SmartPayAsYouGo"
        ],
        "summary": "create a quote for Smart Pay As You Go",
        "parameters": [
          {
            "name": "accountId",
            "in": "path",
            "required": true,
            "description": "The Billing Account ID, this is used to validate that the user is active",
            "schema": {
              "type": "integer",
              "format": "int64",
              "example": 123
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/SmartPayAsYouGoQuoteRequest"
              }
            }
          }

```
Within the `build` folder of the module we should find the folder ` generated-src/src`. 
In the `api` folder we have two classes the naming of which is derived from the *first `tag name` with `tags`*
 - generated
   - `SmartPayAsYouGoApiController` - a SpringBoot RestApplication class
   - `SmartPayAsYouGoApiService` - an interface the implementation of which is autowired into the controller class
 - manually created
   - the implementation for `SmartPayAsYouGoApiService`, 
     - *current practice is to put this class in the `service` package*
     - *current practice for naming this class is the same as the interface but dropping the `Api` from the name, e.g `SmartPayAsYouGoService`.*
   - the testing for both controller and implemented code

#### What next?
Well this is only part of the story. We need to get this call to the actual service which contains our business logic. 
Continuing our example:
 - this calls into `be-customer` which has several modules:
   - `api` - this contains:
     - routing - this is manually created (and can look confusing) code that link from the `api` to the `service` modules
     - grpc - for this we us [protoBuf](https://protobuf.dev/)
   - `service` - finally this is where our business logic and access to data exists
     - controller - defers to a single service (ideally but is not an enforced rule) via Ktor or spring boot injection


## How use linting

The [linter](https://ktlint.github.io/) can be run using  
Mac: `$: ./gradlew ktlint`  
Windows: `$: gradlew.bat ktlint`

and its auto-formatter can be run using  
Mac: `$: ./gradlew ktlintFormat`  
Windows: `$: gradlew.bat ktlintFormat`

## How to test

The tests can be easily run using  
Mac: `$: ./gradlew test`  
Windows: `$: gradlew.bat test`

## Things to note
  1. add `accountId` as a path parameter on the account controller endpoints - _this is used to validate that the user is a valid and logged on user_
  1. ...
