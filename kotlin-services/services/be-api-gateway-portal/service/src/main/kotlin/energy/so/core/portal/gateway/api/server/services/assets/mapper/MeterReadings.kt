package energy.so.core.portal.gateway.api.server.services.assets.mapper

import energy.so.assets.meterReadings.v2.ReadingSource
import energy.so.assets.meterReadings.v2.SubmitMeterReadingError
import energy.so.assets.meterReadings.v2.SubmitMeterReadingFlow
import energy.so.assets.meterReadings.v2.SubmitMeterReadingResponse
import energy.so.assets.meterReadings.v2.submitMeterReadingList
import energy.so.assets.meterReadings.v2.submitMeterReadingRequest
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.core.portal.gateway.api.server.services.assets.dto.SubmitMeterReadingRequestDto
import energy.so.core.portal.gateway.api.server.services.assets.dto.SubmitMeterReadingRequestDtoList
import energy.so.core.portal.gateway.api.server.services.assets.dto.SubmitMeterReadingsErrorDto
import energy.so.core.portal.gateway.api.server.services.assets.dto.SubmitMeterReadingsResponseDto
import java.time.LocalDate

fun SubmitMeterReadingRequestDtoList.toProtobuf(
    billingAccountId: Long,
    userId: Long,
    pubSubEnabled: Boolean,
) = let { dto ->
    val dtoList = dto.meters.map { it.toProtobuf() }

    submitMeterReadingList {
        this.userId = userId
        this.billingAccountId = billingAccountId
        this.flow =
            if (pubSubEnabled) SubmitMeterReadingFlow.ASYNC_WITH_MULTIPLE_RETRIES else SubmitMeterReadingFlow.SYNC
        meters.addAll(dtoList)
        this.source = ReadingSource.WEBSITE
    }
}

fun SubmitMeterReadingRequestDto.toProtobuf() = let { dto ->
    val readingTimeStamp = if (dto.readingDate != null) dto.readingDate.toTimestamp() else LocalDate.now().toTimestamp()

    submitMeterReadingRequest {
        digits.addAll(dto.digits)
        meterIdentifier = dto.meterIdentifier.toNullableString()
        meterPointId = dto.meterPointId
        unitType = dto.unitType.toSiblingEnum()
        units = dto.units
        registerIdentifier = dto.registerIdentifier.toNullableString()
        readingAt = readingTimeStamp
        validateOnly = dto.validateOnly ?: false
        registerId = dto.registerId.toNullableInt64()
    }
}

private fun SubmitMeterReadingError.toDto() = SubmitMeterReadingsErrorDto(
    code = code,
    meterIdentifier = meterIdentifier.getValueOrNull(),
    registerIdentifier = registerIdentifier.getValueOrNull(),
    message = message
)

fun SubmitMeterReadingResponse.toDto() = SubmitMeterReadingsResponseDto(
    success = success,
    error = errorList.map { it.toDto() }
)
