-- This will be executed manually on GCP and
-- result will be copied to be_customers.public.move_accounts_to_daily_reads_consents with pagination
SELECT DISTINCT mda.Account_Number as junifer_account_number, a.email as email, MPXN as mpxn, meterpoint_id as meter_point_id, a.customer_name as customer_name
FROM `soe-prod-data-core-7529.soe_junifer_model.w_meter_details_attributes` mda
    LEFT JOIN `soe-prod-data-core-7529.soe_junifer_model.w_meterpoint_d` mp on mp.mpxn_identifier = mda.MPXN
    LEFT JOIN `soe-prod-data-core-7529.soe_junifer_model.w_account_d` a on mda.Account_Number = a.account_number
WHERE mp.smart_meter_flag = 'Y'
  AND a.account_status = 'Active'
  AND MSN_To_Date >= current_date()
ORDER BY junifer_account_number ASC limit (100) offset (0)