CREATE EXTENSION dblink;
SELECT dblink_connect('so_smart_meter_booking_production','host=************** port=5432 dbname=so_smart_meter_booking_production user=postgres password=qyJ23L235H8X4-LkKx6k_Z options=-csearch_path=');

INSERT INTO smart_meter_bookings (
    slot_date_time,
    billing_account_id,
    customer_name,
    customer_email,
    customer_phone,
    address,
    postcode,
    mpan,
    mprn,
    gas_meter_serial_number,
    electricity_meter_serial_number,
    aes_job_number,
    aes_patch_availability_id,
    cancellation_date_time,
    meter_reading_frequency,
    medical_equipment,
    junifer_ticket_id,
    fuel_type,
    source,
    updated_at,
    installed_datetime,
    failed_datetime,
    aes_status,
    aes_job_type
)

SELECT * FROM dblink('so_smart_meter_booking_production', 'select TO_TIMESTAMP(concat(us.date, '' '', us.start_time), ''YYYY-MM-DD HH24:MI:SS'') AT TIME ZONE ''UTC'' AS slot_date_time,
                                                                  b.account_id AS billing_account_id,
                                                                  b.at_home_name AS customer_name,
                                                                  b.at_home_email AS customer_email,
                                                                  CASE
                                                                      WHEN (b.at_home_phone_number is not null) THEN b.at_home_phone_number
                                                                      WHEN (b.at_home_phone_number is null) AND (b.customer_phone is not null) THEN b.customer_phone
                                                                      ELSE ''Not present in ruby''
                                                                      END AS customer_phone,
                                                                  b.customer_address AS address,
                                                                  b.customer_postcode AS postcode,
                                                                  b.mpan AS mpan,
                                                                  b.mprn AS mprn,
                                                                  b.gas_meter_serial_number AS gas_meter_serial_number,
                                                                  b.electric_meter_serial_number AS electricity_meter_serial_number,
                                                                  b.job_number AS aes_job_number,
                                                                  us.installer_availability_id AS aes_patch_availability_id,
                                                                  b.cancellation_date_time AS cancellation_date_time,
                                                                  ''DAILY'' AS meter_reading_frequency,
                                                                  b.needs_medical_equipment AS medical_equipment,
                                                                  b.junifer_ticket_id AS junifer_ticket_id,
                                                                  CASE
                                                                      WHEN(b.fuel_type = ''DUAL_FUEL'') THEN ''DUAL''
                                                                      WHEN(b.fuel_type = ''ELEC_ONLY'') THEN ''ELECTRICITY_ONLY''
                                                                      WHEN(b.fuel_type = ''GAS_ONLY'') THEN ''GAS_ONLY''
                                                                      WHEN(b.fuel_type is null) THEN ''DUAL''
                                                                      END AS fuel_type,
                                                                  ''PORTAL'' AS source, -- This enum needs to be updated in kotlin
                                                                  NOW() AS updated_at,
                                                                  b.installation_date_time AS installed_datetime,
                                                                  b.failed_date_time AS failed_datetime,
                                                                  wc.status AS aes_status,
                                                                  wc.fuel_type AS aes_job_type
                                                           FROM public.bookings b
                                                                    INNER JOIN (SELECT s.installer_availability_id, s.start_time, s.date, s.booking_id FROM public.slots s WHERE ctid IN (SELECT max(ctid) FROM public.slots GROUP BY booking_id )) us ON b.id = us.booking_id
                                                                    LEFT OUTER JOIN (SELECT MAX(w.fuel_type) AS fuel_type, MAX(w.status) AS status, w.booking_id FROM public.work_completions w WHERE w.installer = ''AES'' GROUP BY w.booking_id) wc ON b.id = wc.booking_id
                                                           WHERE b.job_number IS NOT NULL AND b.cancellation_date_time IS NULL AND b.installation_date_time IS NULL
                                                             AND (b.gas_work_completion_id IS NULL OR b.electric_work_completion_id IS NULL)'
                  )
                  AS p(
                       slot_date_time TIMESTAMP,
                       billing_account_id TEXT,
                       customer_name TEXT,
                       customer_email TEXT,
                       customer_phone TEXT,
                       address TEXT,
                       postcode TEXT,
                       mpan TEXT,
                       mprn TEXT,
                       gas_meter_serial_number TEXT,
                       electricity_meter_serial_number TEXT,
                       aes_job_number TEXT,
                       aes_patch_availability_id TEXT,
                       cancellation_date_time TIMESTAMP,
                       meter_reading_frequency READING_SUBMISSION_FREQUENCY,
                       medical_equipment BOOLEAN,
                       junifer_ticket_id TEXT,
                       fuel_type FUEL_TYPE,
                       source SMART_METER_BOOKING_SOURCE,
                       updated_at TIMESTAMP,
                       installed_datetime TIMESTAMP,
                       failed_datetime TIMESTAMP,
                       aes_status TEXT,
                       aes_job_type TEXT
        );