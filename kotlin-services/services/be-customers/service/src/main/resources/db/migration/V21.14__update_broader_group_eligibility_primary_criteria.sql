--update text
--1
update warm_home_discount_criteria
set text = 'You (or your partner) receive Income Support and one of the following:'
where type = 'primary'
  and text = 'Income support';

--2
update warm_home_discount_criteria
set text = 'You (or your partner) receive Income-related Employment and Support Allowance (IR ESA) which includes a support component or are a member of the work-related activity group and one of the following:'
where type = 'primary'
  and text =
      'Income-related Employment and Support Allowance (IR ESA) which includes a support component or Support Allowance IR ESA and a member of the work related activity group';

--3
update warm_home_discount_criteria
set text = 'You (or your partner) receive income-based Job Seekers Allowance and one of the following:'
where type = 'primary'
  and text =
      'Income-based Jobseekers allowance';

--4
update warm_home_discount_criteria
set text = 'You (or your partner) receive child tax credit by virtue of an award which is based on an annual income not exceeding £19,978 and one of the following:'
where type = 'primary'
  and text like
      '%Child tax credit by virtue of an award which is based on an annual income not exceeding%';

--5
update warm_home_discount_criteria
set text = 'You (or your partner) receive Universal Credit, and have an earned income of between zero and £1,665 in at least one month during the period 01 April 2024 to 31 March 2025 and one of the following:'
where type = 'primary'
  and text like '%You receive Universal Credit, and have an earned income of between zero and%';

--6
insert
into warm_home_discount_criteria(text, type)
values ('You (or your partner) receive Universal Credit, are not in work or self-employed and one of the following:',
        'primary');

--7
update warm_home_discount_criteria
set text = 'You have a Gross taxable annual household income of less than £19,978 and one of the following:'
where type = 'primary'
  and text like '%You have a Gross taxable annual household Income of less than%';

--8
update warm_home_discount_criteria
set text = 'You (or your partner) receive the Savings Credit element of Pension Credits only and do not receive the Guaranteed Credit element of Pension Credits and one of the following:'
where type = 'primary'
  and text like
      '%You receive Pension Credits covering Savings Credit only and you do not receive the Guaranteed Credit element of Pension Credit%';

--9
update warm_home_discount_criteria
set text = 'You (or your partner) receive Housing Benefit in the relevant assessment period and one of the following:'
where type = 'primary'
  and text like '%You receive Housing Benefit in the relevant assessment period%';

--10
insert
into warm_home_discount_criteria(text, type)
values ('You (or your partner) qualify for Free School Meals support and one of the following:', 'primary');

--add ordinal
alter table if exists warm_home_discount_criteria
    add column if not exists ordinal numeric (3,1);

update warm_home_discount_criteria
set ordinal = 1.0
where text = 'You (or your partner) receive Income Support and one of the following:';

update warm_home_discount_criteria
set ordinal = 2.0
where text =
      'You (or your partner) receive Income-related Employment and Support Allowance (IR ESA) which includes a support component or are a member of the work-related activity group and one of the following:';

update warm_home_discount_criteria
set ordinal = 3.0
where text = 'You (or your partner) receive income-based Job Seekers Allowance and one of the following:';

update warm_home_discount_criteria
set ordinal = 4.0
where text =
      'You (or your partner) receive child tax credit by virtue of an award which is based on an annual income not exceeding £19,978 and one of the following:';

update warm_home_discount_criteria
set ordinal = 5.0
where text =
      'You (or your partner) receive Universal Credit, and have an earned income of between zero and £1,665 in at least one month during the period 01 April 2024 to 31 March 2025 and one of the following:';

update warm_home_discount_criteria
set ordinal = 6.0
where text =
      'You (or your partner) receive Universal Credit, are not in work or self-employed and one of the following:';

update warm_home_discount_criteria
set ordinal = 7.0
where text = 'You have a Gross taxable annual household income of less than £19,978 and one of the following:';

update warm_home_discount_criteria
set ordinal = 8.0
where text =
      'You (or your partner) receive the Savings Credit element of Pension Credits only and do not receive the Guaranteed Credit element of Pension Credits and one of the following:';

update warm_home_discount_criteria
set ordinal = 9.0
where text =
      'You (or your partner) receive Housing Benefit in the relevant assessment period and one of the following:';

update warm_home_discount_criteria
set ordinal = 10.0
where text = 'You (or your partner) qualify for Free School Meals support and one of the following:';

alter table if exists warm_home_discount_criteria
drop constraint if exists primary_ordinal_not_null;
alter table if exists warm_home_discount_criteria
    add constraint primary_ordinal_not_null check ((type = 'primary' and ordinal is not null) or type = 'secondary' );

create index if not exists ix_warm_home_discount_criteria_ordinal on warm_home_discount_criteria (ordinal);
