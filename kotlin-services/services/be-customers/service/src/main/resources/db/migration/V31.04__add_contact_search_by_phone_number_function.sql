CREATE INDEX IF NOT EXISTS ix_contact_phone_number1 ON contact(phone_number1);
CREATE INDEX IF NOT EXISTS ix_contact_phone_number2 ON contact(phone_number2);
CREATE INDEX IF NOT EXISTS ix_contact_phone_number3 ON contact(phone_number3);

DROP FUNCTION IF EXISTS contact_search_by_phone_number(search_phone_number VARCHAR);
CREATE OR REPLACE FUNCTION contact_search_by_phone_number(
    search_phone_number VARCHAR
)
    RETURNS TABLE(
        billing_account_ids           TEXT,
        account_contact_customer_ids  TEXT,
        customer_contact_customer_ids TEXT
    )
AS
$body$

SELECT
    string_agg(distinct ba.id::text, ',')          as billing_account_ids,
    string_agg(distinct ba.customer_id::text, ',') as account_contact_customer_ids,
    string_agg(distinct c.id::text, ',')           as customer_contact_customer_ids
FROM contact co
         LEFT OUTER JOIN billing_account_contact bac
                         ON co.id = bac.contact_id
                             AND bac.cancelled IS NULL
                             AND bac.deleted IS NULL
                             AND (bac.to_date IS NULL OR bac.to_date >= current_timestamp)
         LEFT OUTER JOIN customer_contact cc
                         ON co.id = cc.contact_id
                             AND cc.deleted IS NULL
                             AND cc.cancelled IS NULL
                             AND (cc.to_date IS NULL OR cc.to_date >= current_timestamp)
         LEFT OUTER JOIN billing_account ba
                         ON bac.billing_account_id = ba.id
                             AND ba.deleted IS NULL
                             AND ba.closed IS NULL
                             AND ba.cancelled IS NULL
                             AND ba.terminated IS NULL
                             AND (ba.to_date IS NULL OR ba.to_date >= current_timestamp)
         LEFT OUTER JOIN customer c
                         ON cc.customer_id = c.id
                             AND c.deleted IS NULL
                             AND c.state = 'Active'
WHERE (co.phone_number1 = search_phone_number OR co.phone_number2 = search_phone_number OR co.phone_number3 = search_phone_number)
  AND co.deleted IS NULL

    $body$
    LANGUAGE sql;
