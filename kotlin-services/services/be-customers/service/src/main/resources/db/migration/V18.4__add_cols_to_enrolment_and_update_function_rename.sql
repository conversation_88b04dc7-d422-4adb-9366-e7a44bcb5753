ALTER TABLE enrolment ADD COLUMN if not exists submission_time  timestamp;
ALTER TABLE enrolment ADD COLUMN if not exists cancellation_reason varchar;
ALTER TABLE enrolment ADD COLUMN if not exists old_billing_account_id bigint;

CREATE OR REPLACE FUNCTION claim_enrolments(
    enrolmentType enrolment_type,
    enrolmentStatus varchar,
    moveInBefore date,
    moveInAfter date
)
    RETURNS SETOF enrolment AS
$$
BEGIN
    RETURN QUERY
        WITH cte AS (
            SELECT
                *
            FROM enrolment enrol
            WHERE enrol.type = coalesce(enrolmentType, enrol.type)
              AND enrol.status = coalesce(enrolmentStatus, enrol.status)
              AND enrol.move_in_date <= coalesce(moveInBefore, enrol.move_in_date)
              AND enrol.move_in_date >= coalesce(moveInAfter, enrol.move_in_date)
            )
            UPDATE enrolment e
                SET status = 'PROCESSING',
                    submission_time = now()
                FROM cte
                WHERE e.id = cte.id
                RETURNING e.*;
END;
$$
    LANGUAGE 'plpgsql';

