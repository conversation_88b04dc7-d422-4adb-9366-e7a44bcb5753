CREATE
OR REPLACE FUNCTION cleanup_whd_criteria(criteria_text varchar)
    RETURNS VOID AS
$$
DECLARE
ids    bigint[];
    min_id
bigint;
BEGIN
select array_agg(id)
into ids
from warm_home_discount_criteria
where type = 'secondary'
  and text = criteria_text;

SELECT MIN(val)
INTO min_id
FROM unnest(ids) AS val;

update warm_home_discount_application
set secondary_criteria_id = min_id
where secondary_criteria_id = any (ids);

update warm_home_discount_criteria_rel
set secondary_criteria_id = min_id
where secondary_criteria_id = any (ids);

delete
from warm_home_discount_criteria
where id = any (array_remove(ids, min_id));

END;
$$
LANGUAGE plpgsql;

--
CREATE
OR REPLACE FUNCTION cleanup_whd()
    RETURNS VOID AS
$$
DECLARE
row_record record;
BEGIN
for row_record in
        with aux as
                 (select distinct text, count(*) as total
                  from warm_home_discount_criteria
                  where type = 'secondary'
                  group by text)
select text
from aux
where aux.total > 1 loop
            perform cleanup_whd_criteria(row_record.text);
end loop;

END;
$$
LANGUAGE plpgsql;

select cleanup_whd();

drop function cleanup_whd();
drop function cleanup_whd_criteria(criteria_text varchar);

--update criterion text
update warm_home_discount_criteria
set text = 'You have parental responsibility for a child under the age of 5 who normally lives with you'
where text =
      'You have parental responsibility for a child under the age of 5 or under the age of 18 in full-time education who normally lives with you'
  and type = 'secondary';

insert into warm_home_discount_criteria(text, type)
VALUES ('You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
        'secondary');

update warm_home_discount_criteria
set text = 'You receive child tax credit which includes a disability or severe disability element'
where text =
      'You receive Child tax credit which includes a disability or severe disability element; or the disabled child premium'
  and type = 'secondary';

insert into warm_home_discount_criteria(text, type)
VALUES ('You receive a disabled child premium', 'secondary');

insert into warm_home_discount_criteria(text, type)
VALUES ('You are in receipt of Personal Independence Payments', 'secondary');

update warm_home_discount_criteria
set text = 'You have limited capability for work or limited capability for work, and work-related activity'
where text =
      'You have limited capability for work or limited capability for work and work related activity'
  and type = 'secondary';

insert into warm_home_discount_criteria(text, type)
VALUES ('You are in receipt of Child Benefit', 'secondary');

--links
CREATE OR REPLACE FUNCTION link_whd_criterion(primary_text varchar, secondary_text varchar[])
    RETURNS VOID AS
$$
DECLARE
primary_id   bigint;
    secondary_id bigint;
    index        int;
BEGIN
SELECT id
INTO primary_id
FROM warm_home_discount_criteria
where text = primary_text
  and type = 'primary';

if primary_id is not null then
        FOR index IN 1 .. array_length(secondary_text, 1)
            loop
select id
into secondary_id
from warm_home_discount_criteria
where type = 'secondary'
  and text = secondary_text[index];

if secondary_id is not null then
INSERT INTO warm_home_discount_criteria_rel (primary_criteria_id, secondary_criteria_id, ordinal)
SELECT primary_id, secondary_id, index;
end if;
end loop;
end if;
END;
$$ LANGUAGE plpgsql;

delete from warm_home_discount_criteria_rel;
alter table if exists warm_home_discount_criteria_rel add column if not exists ordinal numeric(3,1);
create index if not exists ix_warm_home_discount_criteria_rel_ordinal on warm_home_discount_criteria(ordinal);

select link_whd_criterion('You (or your partner) receive Income Support and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive Income-related Employment and Support Allowance (IR ESA) which includes a support component or are a member of the work-related activity group and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive income-based Job Seekers Allowance and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive child tax credit by virtue of an award which is based on an annual income not exceeding £19,978 and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive Universal Credit, and have an earned income of between zero and £1,665 in at least one month during the period 01 April 2024 to 31 March 2025 and one of the following:', array [
    'You have limited capability for work or limited capability for work, and work-related activity',
                          'You are in receipt of the disabled child element',
                          'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive Universal Credit, are not in work or self-employed and one of the following:', array [
    'You have limited capability for work or limited capability for work, and work-related activity',
                          'You are in receipt of the disabled child element',
                          'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You have a Gross taxable annual household income of less than £19,978 and one of the following:', array [
    'You are in receipt of the disabled child element',
                          'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over',
                          'You are in employment'
                              ]);
select link_whd_criterion('You (or your partner) receive the Savings Credit element of Pension Credits only and do not receive the Guaranteed Credit element of Pension Credits and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) receive Housing Benefit in the relevant assessment period and one of the following:', array [
    'You have parental responsibility for a child under the age of 5 who normally lives with you',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You receive child tax credit which includes a disability or severe disability element',
                          'You receive a disabled child premium',
                          'You receive a disability premium, enhanced disability premium or severe disability premium',
                          'You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium',
                          'You are in receipt of Personal Independence Payments',
                          'You are aged 65 or over'
                              ]);
select link_whd_criterion('You (or your partner) qualify for Free School Meals support and one of the following:', array [
    'You are in receipt of Child Benefit',
                          'You have parental responsibility for a child under the age of 19, born on or after 1 April 2005, who is in full time education and normally lives with you',
                          'You are in receipt of Personal Independence Payments'
                              ]);
drop function link_whd_criterion(primary_text varchar, secondary_text varchar[]);

alter table if exists warm_home_discount_criteria
drop constraint if exists criteria_unique;
alter table if exists warm_home_discount_criteria
    add constraint criteria_unique unique (type, text);

alter table if exists warm_home_discount_criteria_rel
    alter column ordinal set not null;
