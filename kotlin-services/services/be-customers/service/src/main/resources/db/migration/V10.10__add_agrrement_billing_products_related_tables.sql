-- update agreement table
DO
$$
    BEGIN
        CREATE TYPE agreement_type AS ENUM ('OPEN_ENDED', 'FIXED_12_MONTHS', 'FIXED_24_MONTHS', 'FIXED_18_MONTHS', 'FIXED_30_MONTHS', 'FIXED_36_MONTHS');
    EXCEPTION
        WHEN duplicate_object THEN null;
    END
$$;


ALTER TABLE if exists agreements
    DROP COLUMN if exists status,
    DROP COLUMN if exists service_account_id,
    DROP COLUMN if exists service_option_id,
    DROP COLUMN if exists "source";

DO
$$
    BEGIN
        ALTER TABLE if exists agreements
            RENAME COLUMN cancelled_at TO deleted;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column agreements.cancelled_at does not exist';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE if exists agreements
            RENAME COLUMN external_data TO products;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column agreements.external_data does not exist';
    END;
$$;
DO
$$
    BEGIN
        ALTER TABLE if exists agreements
            RENAME COLUMN starts_at TO from_date;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column agreements.starts_at does not exist';
    END;
$$;
DO
$$
    BEGIN
        ALTER TABLE if exists agreements
            RENAME COLUMN ends_at TO to_date;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column agreements.ends_at does not exist';
    END;
$$;
DO
$$
    BEGIN
        ALTER TABLE if exists agreements
            RENAME COLUMN property_id TO quote_id;
    EXCEPTION
        WHEN undefined_column THEN RAISE NOTICE 'column agreements.property_id does not exist';
    END;
$$;



ALTER TABLE if exists agreements
    ADD COLUMN if not exists updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;
ALTER TABLE if exists agreements
    ADD COLUMN if not exists type agreement_type NOT NULL;

-- create table billing_account_settings
CREATE TABLE if not exists billing_account_setting
(
    id                   bigint generated by default as identity not null,
    billing_account_id   bigint                                  not null,
    billing_cycle        billing_cycle                           not null,
    bill_delivery_method bill_delivery_method                    not null default 'EMAIL',
    deleted              timestamp,
    created_at           timestamp                               not null default current_timestamp,
    updated_at           timestamp                               not null default current_timestamp,

    CONSTRAINT billing_account_settings_pkey PRIMARY KEY (id),
    CONSTRAINT billing_account_id_fk FOREIGN KEY (billing_account_id) REFERENCES public.billing_account (id)
);

-- create table billing_account_contact
CREATE TABLE if not exists billing_account_contact
(
    id                 bigint generated by default as identity not null,
    billing_account_id bigint                                  not null,
    contact_id         bigint                                  not null,
    from_date          timestamp,
    to_date            timestamp,
    "primary"          boolean                                 not null,
    deleted            timestamp,
    created_at         timestamp                               not null default current_timestamp,
    updated_at         timestamp                               not null default current_timestamp,

    CONSTRAINT billing_account_contact_pkey PRIMARY KEY (id),
    CONSTRAINT billing_account_id_fk FOREIGN KEY (billing_account_id) REFERENCES public.billing_account (id),
    CONSTRAINT contact_id_fk FOREIGN KEY (contact_id) REFERENCES public.contacts (id)
);

-- create product_account_agreement_rel
CREATE TABLE if not exists product_account_agreement_rel
(
    id                 bigint generated by default as identity not null,
    product_account_id bigint                                  not null,
    agreement_id       bigint                                  not null,
    deleted            timestamp,
    created_at         timestamp                               not null default current_timestamp,
    updated_at         timestamp                               not null default current_timestamp,

    CONSTRAINT product_account_agreement_rel_pkey PRIMARY KEY (id),
    CONSTRAINT product_account_id_fk FOREIGN KEY (product_account_id) REFERENCES public.product_account (id),
    CONSTRAINT agreement_id_fk FOREIGN KEY (agreement_id) REFERENCES public.agreements (id)
);
