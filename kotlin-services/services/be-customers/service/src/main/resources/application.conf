environment {
  port = 50052
  port = ${?PORT}
  environment = "development"
  environment = ${?BE_CUSTOMERS_ENV}
}

on-demand {
    onDemandBranchName = "default"
    onDemandBranchName = ${?BRANCH_NAME}
}

redis {
  host = localhost
  host = ${?REDIS_HOST}
  authString = b1558e84-9c09-48e3-bdb7-64861617a0b8
  authString = ${?REDIS_AUTH_STRING}
  cluster = false
  cluster = ${?REDIS_CLUSTER}
  disabled-for-junifer-entity-mapper = false
  disabled-for-junifer-entity-mapper = ${?DISABLED_FOR_JUNIFER_ENTITY_MAPPER}
}

database {
    default {
        username = "postgres"
        username = ${?DB_USERNAME}
        password = "password"
        password = ${?DB_PASSWORD}
        host = "localhost"
        host = ${?DB_HOST}
        dbName = "be_customers"
        dbName = ${?DB_NAME}
        maximumPoolSize = 10
        maximumPoolSize = ${?DB_MAXIMUM_POOL_SIZE}
    }
}


freshdesk {
    host = "localhost"
    host = ${?FRESHDESK_HOST}
    connectionTimeout = 60000
    connectionTimeout = ${?FRESHDESK_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?FRESHDESK_REQUEST_TIMEOUT}
    apiKey = "api-key"
    apiKey = ${?FRESHDESK_API_KEY}
    password = "X"
    password = ${?FRESHDESK_PASSWORD}
    groupId = "*************"
    groupId = ${?FRESHDESK_GROUP_ID}
    accountLinkTemplate = "https://nova.so.energy/customer/%s"
}

trustpilot {
    host = "localhost"
    host = ${?TRUSTPILOT_HOST}
    inviteApiHost = "localhost"
    inviteApiHost = ${?TRUSTPILOT_INVITE_API_HOST}
    connectionTimeout = 60000
    connectionTimeout = ${?TRUSTPILOT_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?TRUSTPILOT_REQUEST_TIMEOUT}
    clientId = "client-id"
    clientId = ${?TRUSTPILOT_API_CLIENT_ID}
    clientSecret = "client-secret"
    clientSecret = ${?TRUSTPILOT_API_CLIENT_SECRET}
    stubEmail = ""
    stubEmail = ${?TRUSTPILOT_STUB_EMAIL}
}

dvla {
    host = "localhost"
    host = ${?DVLA_HOST}
    connectionTimeout = 60000
    connectionTimeout = ${?DVLA_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?DVLA_REQUEST_TIMEOUT}
    apiKeyHeaderName = "x-api-key"
    apiKey = "api-key"
    apiKey = ${?DVLA_API_KEY}
}

ees {
    host = "localhost"
    host = ${?EES_HOST}
    connectionTimeout = 60000
    connectionTimeout = ${?EES_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?EES_REQUEST_TIMEOUT}
    apiKey = "api-key"
    apiKey = ${?EES_API_KEY}
}

bankHoliday {
    host = "localhost"
    host = ${?BANK_HOLIDAY_HOST}
    connectionTimeout = 60000
    connectionTimeout = ${?BANK_HOLIDAY_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?BANK_HOLIDAY_REQUEST_TIMEOUT}
}

pubsub {
    projectName = "soe-nonprod-core-apps-e5fb"
    projectName = ${?GCP_PROJECT}
    topics = [
        {
            key = "be-automation-junifer-accounts-review"
            name = "be-automation-junifer-accounts-review"
            name = ${?GCP_AUTOMATION_JUNIFER_ACCOUNTS_REVIEW_TOPIC_NAME}
        },
        {
            key = "be-customers-cooling-off-reminder"
            name = "be-customers-cooling-off-reminder"
            name = ${?GCP_CUSTOMERS_COOLING_OFF_REMINDER_TOPIC_NAME}
        },
        {
            key = "be-customers-smart-meter"
            name = "be-customers-smart-meter"
            name = ${?GCP_CUSTOMERS_SMART_METER_TOPIC_NAME}
        },
        {
            key = "be-customers-referral-processing"
            name = "be-customers-referral-processing"
            name = ${?GCP_CUSTOMERS_REFERRAL_PROCESSING_TOPIC_NAME}
        },
        {
            key = "be-customers-smart-meter-sms"
            name = "be-customers-smart-meter-sms"
            name = ${?GCP_CUSTOMERS_SMART_METER_SMS_TOPIC_NAME}
        },
        {
            key = "be-customers-dca-account-update"
            name = "be-customers-dca-account-update"
            name = ${?GCP_CUSTOMERS_DCA_ACCOUNT_UPDATE_TOPIC_NAME}
        },
        {
            key = "be-customers-smart-pay-as-you-go-completions"
            name = "be-customers-smart-pay-as-you-go-completions"
            name = ${?GCP_CUSTOMERS_SPAYG_COMPLETIONS_TOPIC_NAME}
        },
        {
            key = "be-customers-smart-pay-as-you-go-reminders"
            name = "be-customers-smart-pay-as-you-go-reminders"
            name = ${?GCP_CUSTOMERS_SPAYG_REMINDERS_TOPIC_NAME}
        }
    ]
    subscriptions = [
        {
            key = "be-assets-send-reminders"
            name = "be-assets-send-reminders"
            name = ${?GCP_ASSETS_SEND_REMINDERS_QUEUE_SUBSCRIPTION_NAME}
            topicKey = "be-assets-send-reminders"
            topic = "be-assets-send-reminders"
            topic = ${?GCP_ASSETS_SEND_REMINDERS_TOPIC_NAME}
        },
        {
            key = "be-customers-send-cooling-off-reminders"
            name = "be-customers-send-cooling-off-reminders"
            name = ${?GCP_CUSTOMERS_COOLING_OFF_REMINDER_TOPIC_SUBSCRIPTION_NAME}
            topicKey = "be-customers-cooling-off-reminder"
            topic = "be-customers-cooling-off-reminder"
            topic = ${?GCP_CUSTOMERS_COOLING_OFF_REMINDER_TOPIC_NAME}
        },
        {
            key = "be-customers-trigger-trustpilot-invitation"
            name = "be-customers-trigger-trustpilot-invitation"
            name = ${?GCP_TRSUSTPILOT_INVITATION_TOPIC_SUBSCRIPTION_NAME}
            topicKey = "be-customers-trustpilot-invitation"
            topic = "be-customers-trustpilot-invitation"
            topic = ${?GCP_TRSUSTPILOT_INVITATION_TOPIC_NAME}
        },
        {
            key = "be-automation-run-junifer-accounts-review-task"
            name = "be-automation-run-junifer-accounts-review-task"
            name = ${?GCP_AUTOMATION_RUN_JUNIFER_ACCOUNTS_REVIEW_TASK_QUEUE_SUBSCRIPTION_NAME}
            topicKey = "be-automation-junifer-accounts-review"
            topic = "be-automation-junifer-accounts-review"
            topic = ${?GCP_AUTOMATION_JUNIFER_ACCOUNTS_REVIEW_TOPIC_NAME}
        },
        {
            key = "be-customers-smart-meter-subscription"
            name = "be-customers-smart-meter-subscription"
            name = ${?GCP_CUSTOMERS_SMART_METER_SUBSCRIPTION_NAME}
            topicKey = "be-customers-smart-meter"
            topic = "be-customers-smart-meter"
            topic = ${?GCP_CUSTOMERS_SMART_METER_TOPIC_NAME}
        },
        {
            key = "be-customers-trigger-referral-processing",
            name = "be-customers-trigger-referral-processing",
            name = ${?GCP_CUSTOMERS_REFERRAL_PROCESSING_TOPIC_SUBSCRIPTION_NAME}
            topicKey = "be-customers-referral-processing"
            topic = "be-customers-referral-processing"
            topic = ${?GCP_CUSTOMERS_REFERRAL_PROCESSING_TOPIC_NAME}
        },
        {
            key = "be-customers-move-with-us-cot-processing-subs",
            name = "be-customers-move-with-us-cot-processing-subs",
            name = ${?GCP_CUSTOMERS_MOVE_WITH_US_COT_SUBSCRIPTION_NAME}
            topicKey = "be-customers-move-with-us-cot-processing"
            topic = "be-customers-move-with-us-cot-processing"
            topic = ${?GCP_CUSTOMERS_MOVE_WITH_US_COT_TOPIC_NAME}
        },
        {
             key = "be-customers-smart-meter-sms-subscription"
             name = "be-customers-smart-meter-sms-subscription"
             name = ${?GCP_CUSTOMERS_SMART_METER_SMS_SUBSCRIPTION_NAME}
             topicKey = "be-customers-smart-meter-sms"
             topic = "be-customers-smart-meter-sms"
             topic = ${?GCP_CUSTOMERS_SMART_METER_SMS_TOPIC_NAME}
        },
        {
            key = "be-customers-trigger-cot-send-final-bill",
            name = "be-customers-trigger-cot-send-final-bill",
            name = ${?GCP_CUSTOMERS_COT_FINAL_BILL_SUBSCRIPTION_NAME}
            topicKey = "be-customers-trigger-cot-send-final-bill"
            topic = "be-customers-trigger-cot-send-final-bill"
            topic = ${?GCP_CUSTOMERS_COT_FINAL_BILL_TOPIC_NAME}
        },
        {
            key = "be-customers-cot-move-in-final-bill",
            name = "be-customers-cot-move-in-final-bill",
            name = ${?GCP_CUSTOMERS_COT_MOVE_IN_FINAL_BILL_SUBSCRIPTION_NAME}
            topicKey = "be-customers-cot-move-in-final-bill"
            topic = "be-customers-cot-move-in-final-bill"
            topic = ${?GCP_CUSTOMERS_COT_MOVE_IN_FINAL_BILL_TOPIC_NAME}
        },
        {
            key = "be-customers-trigger-retry-enrolment"
            name = "be-customers-trigger-retry-enrolment"
            name = ${?GCP_CUSTOMERS_RETRY_ENROLMENT_SUBSCRIPTION_NAME}
            topicKey = "be-customers-trigger-retry-enrolment"
            topic = "be-customers-trigger-retry-enrolment"
            topic = ${?GCP_CUSTOMERS_RETRY_ENROLMENT_TOPIC_NAME}
         },
         {
            key = "be-assets-update-contacted-reads-status"
            name = "be-assets-update-contacted-reads-status"
            name = ${?GCP_ASSETS_UPDATE_CONTACTED_READS_STATUS_SUBSCRIPTION_NAME}
            topicKey = "be-assets-update-contacted-reads-status"
            topic = "be-assets-update-contacted-reads-status"
            topic = ${?GCP_ASSETS_UPDATE_CONTACTED_READS_STATUS_TOPIC_NAME}
         },
         {
            key = "be-customers-dca-account-update-subscription"
            name = "be-customers-dca-account-update-subscription"
            name = ${?GCP_CUSTOMERS_DCA_ACCOUNT_UPDATE_SUBSCRIPTION_NAME}
            topicKey = "be-customers-dca-account-update"
            topic = "be-customers-dca-account-update"
            topic = ${?GCP_CUSTOMERS_DCA_ACCOUNT_UPDATE_TOPIC_NAME}
         },
         {
            key = "be-customers-dca-payment-update-subscription"
            name = "be-customers-dca-payment-update-subscription"
            name = ${?GCP_CUSTOMERS_DCA_PAYMENT_UPDATE_SUBSCRIPTION_NAME}
            topicKey = "be-customers-dca-payment-update"
            topic = "be-customers-dca-payment-update"
            topic = ${?GCP_CUSTOMERS_DCA_PAYMENT_UPDATE_TOPIC_NAME}
         },
         {
            key = "be-customers-dca-account-closure-subscription"
            name = "be-customers-dca-account-closure-subscription"
            name = ${?GCP_CUSTOMERS_DCA_ACCOUNT_CLOSURE_SUBSCRIPTION_NAME}
            topicKey = "be-customers-dca-account-closure"
            topic = "be-customers-dca-account-closure"
            topic = ${?GCP_CUSTOMERS_DCA_ACCOUNT_CLOSURE_TOPIC_NAME}
         },
         {
            key = "be-customers-smart-pay-as-you-go-reminders"
            name = "be-customers-smart-pay-as-you-go-reminders"
            name = ${?GCP_CUSTOMERS_SPAYG_REMINDERS_SUBSCRIPTION_NAME}
            topicKey = "be-customers-smart-pay-as-you-go-reminders"
            topic = "be-customers-smart-pay-as-you-go-reminders"
            topic = ${?GCP_CUSTOMERS_SPAYG_REMINDERS_TOPIC_NAME}
         },
         {
             key = "be-customers-smart-pay-as-you-go-completions"
             name = "be-customers-smart-pay-as-you-go-completions"
             name = ${?GCP_CUSTOMERS_SPAYG_COMPLETIONS_SUBSCRIPTION_NAME}
             topicKey = "be-customers-smart-pay-as-you-go-completions"
             topic = "be-customers-smart-pay-as-you-go-completions"
             topic = ${?GCP_CUSTOMERS_SPAYG_COMPLETIONS_TOPIC_NAME}
         }
    ]
}

be-junifer {
    host = "be-junifer"
    host = ${?BE_JUNIFER_HOST}
    port = 50052
    port = ${?BE_JUNIFER_PORT}
    callTimeoutInSeconds = 60
    callTimeoutInSeconds = ${?BE_JUNIFER_TIMEOUT}
}

be-identity {
    host = "localhost"
    host = ${?BE_IDENTITY_HOST}
    port = 50052
    port = ${?BE_IDENTITY_PORT}
    callTimeoutInSeconds = 10
    callTimeoutInSeconds = ${?BE_IDENTITY_TIMEOUT}
}

be-assets {
  host = "be-assets"
  host = ${?BE_ASSETS_HOST}
  port = 50052
  port = ${?BE_ASSETS_PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_ASSETS_TIMEOUT}
}

be-payments {
  host = "localhost"
  host = ${?BE_PAYMENTS_HOST}
  port = 50052
  port = ${?BE_PAYMENTS_PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_PAYMENTS_TIMEOUT}
}

be-billings {
  host = "be-billings"
  host = ${?BE_BILLINGS_HOST}
  port = 50052
  port = ${?BE_BILLINGS_PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_BILLINGS_TIMEOUT}
}

be-communications {
  callTimeoutInSeconds = 5
  callTimeoutInSeconds = ${?COMMUNICATIONS_HUB_TIMEOUT}
  host = localhost
  host = ${?COMMUNICATIONS_HUB_HOST}
  port = 50053
  port = ${?COMMUNICATIONS_HUB_PORT}
}

be-ac-junifer {
  callTimeoutInSeconds = 5
  callTimeoutInSeconds = ${?BE_AC_JUNIFER_TIMEOUT}
  host = localhost
  host = ${?BE_AC_JUNIFER_HOST}
  port = 50053
  port = ${?BE_AC_JUNIFER_PORT}
}

be-products {
  host = "localhost"
  host = ${?BE_PRODUCTS_HOST}
  port = 50054
  port = ${?BE_PRODUCTS_PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_PRODUCTS_TIMEOUT}
}

be-customers {
  host = "localhost"
  host = ${?BE_CUSTOMERS_HOST}
  port = 50052
  port = ${?PORT}
  callTimeoutInSeconds = 10
  callTimeoutInSeconds = ${?BE_CUSTOMERS_TIMEOUT}
}

be-tickets {
  host = "localhost"
  host = ${?BE_TICKETS_HOST}
  port = 50052
  port = ${?BE_TICKETS_PORT}
  callTimeoutInSeconds = 120
  callTimeoutInSeconds = ${?BE_TICKETS_CALL_TIMEOUT}
}

communicationTemplate {
    coolingOffReminder = "cooling_off_reminder"
    reviewWithUs = "review_with_us"
    meterReadingReminderEmail = "meter_reading_reminder"
    refereeEarnedCreditEmail = "referee_earned_credit"
    referredEarnedCreditEmail = "referred_earned_credit"
    referredSignupNotificationEmail = "referred_signup_notification"
    psrCreationEmail = "psr_creation_email"
    psrConfirmationEmail = "psr_confirmation_email"
    requestDepositEmail = "request_deposit_email"
    moveCustomersToDailyReadsEmail = "move_customers_to_daily_reads_email"
    switchCancelledAsNoDepositReceivedEmail = "switch_cancelled_as_no_deposit_received_email"
    depositReceivedEmail = "deposit_received_email"
}

account-review-config {
    maxConcurrency = 5
    maxConcurrency = ${?MAX_CONCURRENCY}
    listLimit = 5
    listLimit = ${?LIST_LIMIT}
    reason = "Ops Billing - Failed bills"
    reason = ${?CANCEL_ACCOUNT_REVIEW_REASON}
    description = "Account has either a failed, dirty or draft bill. This will be reviewed before we issue any further bills."
    description = ${?CANCEL_ACCOUNT_REVIEW_DESCRIPTION}
}

encryption-config {
    secret = "SECRET"
    secret = ${?ENROLMENT_SECRET}
    salt = "SALT"
    salt = ${?ENROLMENT_SALT}
}

meter-exchange-account-review-config {
    maxConcurrency = 5
    maxConcurrency = ${?MAX_CONCURRENCY}
    listLimit = 0
    listLimit = ${?LIST_LIMIT}
    reason = "Ops Metering - Smart Meter Exchange"
    reason = ${?MEX_ACCOUNT_REVIEW_REASON}
    description = "Account is in review for six weeks from their smart meter install while we wait for the new meter details. No need to escalate."
    description = ${?MEX_ACCOUNT_REVIEW_DESCRIPTION}
    toDate = true
    reviewLengthInWeeks = 6
}

cancel-account-review-communication-config {
    communicationEmailList = "<EMAIL>"
    communicationEmailList = ${?CANCEL_ACCOUNT_REVIEW_EMAIL_LIST}
    communicationName = "automated_script_cancellation_review_period"
    communicationSubject = "Account review periods cancellation process finished running"
    communicationHtmlHeader = "Account review periods cancellation"
}

create-account-review-communication-config {
    communicationEmailList = "<EMAIL>"
    communicationEmailList = ${?CANCEL_ACCOUNT_REVIEW_EMAIL_LIST}
    communicationName = "automated_script_creation_review_period"
    communicationSubject = "Creation of account review periods process finished running"
    communicationHtmlHeader = "Account review periods creation"
}

googleStorage {
  projectName = "soe-prod-core-apps-ef5b"
  projectName = ${?GCP_PROJECT}
  enableUploads = "false"
  enableUploads = ${?SHADOW_TRAFFIC_EXTERNAL_CLIENTS_ENABLED}
  credentialsPath = "/storage/soe-nonprod-core-apps-e5fb-037ed0cdc184.json"
  credentialsPath = ${?GOOGLE_STORAGE_CREDENTIALS_PATH}
}

whd-image-config {
  psrUploadBucketName = "test-bucket-soenergy"
  psrUploadBucketName = ${?WHD_PSR_IMAGE_BUCKET}
  uploadExpiryInSeconds = "900"
  uploadExpiryInSeconds = ${?WHD_PSR_IMAGE_UPLOAD_EXPIRY}
}

whdDataBucketConfig {
  bucketName = "be-customers-staging"
  bucketName = ${?WHD_EXPORT_DATA_BUCKET}
  exportDataFolder = "export-data"
  exportDataFolder = ${?WHD_EXPORT_DATA_FOLDER}
  exportAuditCandidatesFolder = "export-audit-candidates"
  exportAuditCandidatesFolder = ${?WHD_EXPORT_AUDIT_CANDIDATES_FOLDER}
}

reportProblemConfig {
  bucketName = "be-customers-staging"
  bucketName = ${?REPORT_PROBLEM_BUCKET_NAME}
  techReportsFolder = "tech-reports"
  techReportsFolder = ${?REPORT_PROBLEM_TECH_REPORTS_FOLDER}
}

meter-exchange-account-review-communication-config {
    communicationEmailList = "<EMAIL>"
    communicationEmailList = ${?METER_EXCHANGE_ACCOUNT_REVIEW_EMAIL_LIST}
    communicationName = "automated_script_meter_exchange_account_review_periods"
    communicationSubject = "Meter exchange account review periods process finished running"
    communicationHtmlHeader = "Meter exchange account review periods creation"
}

sync {
    customerSyncEnabled = "false"
    syncCustomerEnableUserCreation = "false"
    syncCustomerEnableUserCreation = ${?BE_CUSTOMERS_SYNC_CUSTOMER_ENABLE_USER_CREATION}
    syncCustomerEnablePatch3rdPartyFlow = "false"
    syncCustomerEnablePatch3rdPartyFlow = ${?BE_CUSTOMERS_SYNC_CUSTOMER_ENABLE_PATCH_3RD_PARTY_FLOW}
}

uswitch {
  protocol = "https"
  protocol = ${?USWITCH_PROTOCOL}
  host = "staging-api.energy.uswitchsuppliers.com"
  host = ${?USWITCH_HOST}
  port = "443"
  port = ${?USWITCH_PORT}
  authApiUrl = "https://auth.energy.uswitchsuppliers.com/oauth/token"
  authApiUrl = ${?AUTH_API_PATH}
  clientId = "6tqd4We6nQuSWEOXHFOonau1Q5dQyq6Q"
  clientId =  ${?CLIENT_ID}
  clientSecret = "i3RonEU2Pl5dbV3Hcdtoac85G9gZjd8jx4sYyXfsSPRt_T-DiF4tmjunES6KHpOJ"
  clientSecret = ${?CLIENT_SECRET}
  grantType = "client_credentials"
  grantType =  ${?GRANT_TYPE}
  audience = "https://staging-api.energy.uswitchsuppliers.com/"
  audience = ${?AUDIENCE}
  connectionTimeout = 60000
  connectionTimeout = ${?USWITCH_CONNECTION_TIMEOUT}
  requestTimeout = 60000
  requestTimeout = ${?USWITCH_REQUEST_TIMEOUT}
  messageProcessingCount = 1
  messageProcessingCount = ${?USWITCH_MESSAGE_PROCESSING_COUNT}
}

so-energy-website {
    host = "localhost:3000"
    host = ${?BE_ENROLMENT_WEBSITE_HOST}
    customerCreatePath = "api/customers"
    customerCreatePath = ${?BE_ENROLMENT_CUSTOMER_CREATE_PATH}
    apiKey = "website-api-key"
    apiKey = ${?BE_ENROLMENT_WEBSITE_API_KEY}
    timeoutInMilliseconds = 10
    timeoutInMilliseconds = ${?BE_ENROLMENT_WEBSITE_TIMEOUT}
}

bigquery-project-gentrack {
    projectId = "soe-nonprod-data-gentrack"
    projectId = ${?BIGQUERY_GENTRACK_PROJECT_ID}
    dataset = "soe_junifer_source_delta"
    dataset = ${?BIGQUERY_GENTRACK_DATASET}
}

bigquery-project-aes {
    projectId = "soe-prod-data-core-7529"
    projectId = ${?BIGQUERY_AES_PROJECT_ID}
    dataset = "soe_usmartmetering"
    dataset = ${?BIGQUERY_AES_DATASET}
}

ruby {
  host = "ruby-smbp-be"
  host = ${?RUBY_HOST}
  port = 80
  port = ${?RUBY_PORT}
  apiKey = "0490afdc4a8c5a5d5723096e584dacb12dedddd87ffe4c151ac91c48c846986a85734cffd4ab32ade3d4ae17a76cf94b25cc4dd8d9765c5c85be405fdba15823"
  apiKey = ${?RUBY_API_KEY}
  connectionTimeout = 60000
  connectionTimeout = ${?RUBY_CONNECTION_TIMEOUT}
  requestTimeout = 60000
  requestTimeout = ${?RUBY_REQUEST_TIMEOUT}
}

smart-meter {
    slotLengthInHours = "4"
    slotLengthInHours = ${?BE_CUSTOMERS_SLOT_LENGTH_HOURS}
    amStartTime = "08:00"
    amStartTime = ${?BE_CUSTOMERS_AM_START}
    pmStartTime = "12:00"
    pmStartTime = ${?BE_CUSTOMERS_PM_START}
    smbpBookingConfirmationTemplateName = "smbp_booking_confirmation"
    smbpBookingConfirmationTemplateName = ${?BE_CUSTOMERS_SMBP_BOOKING_TEMPLATE_NAME}
    smbpCancellationConfirmationTemplateName = "smbp_cancellation_confirmation"
    smbpCancellationConfirmationTemplateName = ${?BE_CUSTOMERS_SMBP_CANCELLATION_TEMPLATE_NAME}
    smbpBookingPortalUrl = "https://smbp.staging.soenergy.co/"
    smbpBookingPortalUrl = ${?BE_CUSTOMERS_SMBP_PORTAL_URL}
    smbpSuccessfulInstallTemplateName = "smbp_install_success"
    smbpSuccessfulInstallTemplateName = ${?BE_CUSTOMERS_SUCCESS_INSTALL_TEMPLATE_NAME}
    smbpCriticalFailedInstallTemplateName = "smbp_install_critical_failure"
    smbpCriticalFailedInstallTemplateName = ${?BE_CUSTOMERS_CRIT_FAIL_INSTALL_TEMPLATE_NAME}
    smbpMinorFailedInstallTemplateName = "smbp_install_minor_failure"
    smbpMinorFailedInstallTemplateName = ${?BE_CUSTOMERS_MINOR_FAIL_INSTALL_TEMPLATE_NAME}
    createRubyBooking = "true"
    createRubyBooking = ${?BE_CUSTOMERS_CREATE_RUBY_BOOKING}
}

aes {
    clientId = "8550"
    clientId = ${?BE_CUSTOMERS_AES_CLIENT_ID}
    clientSecret = "ZrElmDN1ZUfYVuMgC1E1BxrdXyqjb25CPjwGt0S7"
    clientSecret = ${?BE_CUSTOMERS_AES_CLIENT_SECRET}
    clientHost = "https://atomuk.dev"
    clientHost = ${?BE_CUSTOMERS_AES_CLIENT_HOST}
    requestTimeoutMillis = 50000
    requestTimeoutMillis = ${?BE_CUSTOMERS_AES_CLIENT_REQUEST_TIMEOUT_MILLIS}
    connectionTimeoutMillis = 40000
    connectionTimeoutMillis = ${?BE_CUSTOMERS_AES_CLIENT_CONNECTION_TIMEOUT_MILLIS}
    keepAliveTime = 120000
    keepAliveTime = ${?BE_CUSTOMERS_AES_CLIENT_KEEP_ALIVE_TIME_MILLIS}
    connectAttempts = 4
    connectAttempts = ${?BE_CUSTOMERS_AES_CLIENT_CONNECT_ATTEMPTS}
    bebocApiEmail = "<EMAIL>"
    bebocApiEmail = ${?BE_CUSTOMERS_BEBOC_API_EMAIL}
    bebocApiKey = "secret"
    bebocApiKey = ${?BE_CUSTOMERS_BEBOC_API_KEY}
    bebocClientAuthHost = "https://staging.hub.reachwm.io"
    bebocClientAuthHost = ${?BE_CUSTOMERS_BEBOC_AUTH_HOST}
    bebocClientRequestHost = "https://aes.staging.nexus-api.reachwm.io",
    bebocClientRequestHost = ${?BE_CUSTOMERS_BEBOC_REQUEST_HOST}
    bebocSqsAccessKey = bebocSqsAccessKey
    bebocSqsAccessKey = ${?BE_CUSTOMERS_BEBOC_SQS_ACCESS_KEY}
    bebocSqsSecretKey = "bebocSqsSecretKey"
    bebocSqsSecretKey = ${?BE_CUSTOMERS_BEBOC_SQS_SECRET_KEY}
    bebocAppointmentEventsSqsUrl = "https://sqs.eu-west-2.amazonaws.com/598299844743/Beboc-Update-Test"
    bebocAppointmentEventsSqsUrl = ${?BE_CUSTOMERS_BEBOC_APPOINTMENT_EVENTS_SQS_URL}
    bebocAwsRegion = eu-west-2
    bebocAwsRegion = ${?BE_CUSTOMERS_BEBOC_AWS_REGION}
    enableBebocSqsConsumer = true
    enableBebocSqsConsumer = ${?BE_CUSTOMERS_ENABLE_BEBOC_SQS_CONSUMER}
    bebocSqsConsumerPollingDelayInMillis = 60000
    bebocSqsConsumerPollingDelayInMillis = ${?BE_CUSTOMERS_BEBOC_CONSUMER_POLLING_DELAY_IN_MILLIS}
}

ruby {
  host = "ruby-smbp-be"
  host = ${?RUBY_HOST}
  port = 80
  port = ${?RUBY_PORT}
  apiKey = "0490afdc4a8c5a5d5723096e584dacb12dedddd87ffe4c151ac91c48c846986a85734cffd4ab32ade3d4ae17a76cf94b25cc4dd8d9765c5c85be405fdba15823"
  apiKey = ${?RUBY_API_KEY}
  connectionTimeout = 60000
  connectionTimeout = ${?RUBY_CONNECTION_TIMEOUT}
  requestTimeout = 60000
  requestTimeout = ${?RUBY_REQUEST_TIMEOUT}
}

defaults {
    voucherEnabled = true
    voucherEnabled = ${?VOUCHERS_ENABLED}
    baseUrl = "Application_base_url"
    baseUrl = ${?APPLICATION_BASE_URL}
}

sms {
    confirmationSmsTemplateName = "smbp_booking_confirmation_sms"
    confirmationSmsTemplateName = ${?BE_CUSTOMERS_SMBP_CONFIRMATION_SMS_TEMPLATE}
    firstReminderTemplateName = "smbp_first_sms_reminder"
    firstReminderTemplateName = ${?BE_CUSTOMERS_FIRST_SMS_REMINDER_TEMPLATE}
    secondReminderTemplateName = "smbp_second_sms_reminder"
    secondReminderTemplateName = ${?BE_CUSTOMERS_SECOND_SMS_REMINDER_TEMPLATE}
    rescheduleBaseLink = "https://smbp.staging.soenergy.co/reschedule-appointment"
    rescheduleBaseLink = ${?BE_CUSTOMERS_SMS_RESCHEDULE_LINK}
    thresholdDays = "7"
    thresholdDays = ${?BE_CUSTOMERS_SMS_THRESHOLD_DAYS}
    sendSmsFlag = "false"
    sendSmsFlag = ${?BE_CUSTOMERS_SEND_SMS_FLAG}
}

referral {
    promoFalkirkCustomerNumber = "test"
    promoFalkirkCustomerNumber = ${?PROMO_FALKIRK_CUSTOMER_NUMBER}
    coolingOffPeriod = 14
    coolingOffPeriod = ${?COOLING_OFF_PERIOD}
    currentReferralFee = 50
    currentReferralFee = ${?CURRENT_REFERRAL_FEE}
}

shadowTraffic {
    enabled = false
    enabled = ${?ENABLED_SHADOW_TRAFFIC}
    sharedKey = "sharedKey"
    sharedKey = ${?SHADOW_TRAFFIC_SHARED_KEY}
    externalClientsEnabled = true
    externalClientsEnabled = ${?SHADOW_TRAFFIC_EXTERNAL_CLIENTS_ENABLED}
}

greenData {
    conversionFactor = "2016:0.41205,2017:0.35156,2018:0.28307,2019: 0.25560,2020:0.23314,2021:0.21233,2022:0.19338"
    conversionFactor = ${?CONVERSION_FACTOR}
}

sm-appointment-image-config {
  bucketName = "be-ac-esg-staging"
  bucketName = ${?SM_APP_IMAGE_BUCKET}
  uploadExpiryInSeconds = "900"
  uploadExpiryInSeconds = ${?SM_APP_IMAGE_UPLOAD_EXPIRY}
}

transunion {
    creditCheckHost = "ct.core-data.transunion.co.uk"
    creditCheckHost = ${?TRANSUNION_CREDIT_CHECK_HOST}
    passwordResetHost = "clientadmin.ct.transunion.co.uk"
    passwordResetHost = ${?TRANSUNION_PASSWORD_RESET_HOST}
    username = "So Energy TV CTest"
    username = ${?TRANSUNION_CREDIT_CHECK_USERNAME}
    companyName = "So Energy TV API CTest"
    companyName = ${?TRANSUNION_CREDIT_CHECK_COMPANY_NAME}
    password = "example"
    password = ${?TRANSUNION_CREDIT_CHECK_PASSWORD}
    projectId = "soe-nonprod-core-apps-e5fb"
    projectId = ${?GCP_PROJECT}
    connectionTimeout = 60000
    connectionTimeout = ${?TRANSUNION_CREDIT_CHECK_CONNECTION_TIMEOUT}
    requestTimeout = 60000
    requestTimeout = ${?TRANSUNION_CREDIT_CHECK_REQUEST_TIMEOUT}
    sleepMillisBeforePwdReset = 5000
    sleepMillisBeforePwdReset = ${?TRANSUNION_SLEEP_MILLIS_BEFORE_PWD_RESET}
}

conectia {
  host = "localhost"
  host = ${?CONECTIA_HOST}
  connectionTimeout = 60000
  connectionTimeout = ${?CONECTIA_CONNECTION_TIMEOUT}
  requestTimeout = 60000
  requestTimeout = ${?CONECTIA_REQUEST_TIMEOUT}
}

meter-read-consent-email-config {
    fromDate = "from_date_template"
}

smartMeterInterestConfig {
  bucketName = "be-customers-staging"
  bucketName = ${?SMART_METER_INTEREST_BUCKET_NAME}
  smartMeterInterestFolder = "smart-meter-interest"
  smartMeterInterestFolder = ${?SMART_METER_INTEREST_FOLDER}
}

dot-digital {
    apiUser = "test"
    apiUser = ${?DOT_DIGITAL_MARKETING_API_USER}
    apiPassword = "test"
    apiPassword = ${?DOT_DIGITAL_MARKETING_API_PASSWORD}
    connectionTimeout = "30000"
    connectionTimeout = ${?DOT_DIGITAL_CONNECTION_TIMEOUT_MILLIS}
    requestTimeout = "30000"
    requestTimeout = ${?DOT_DIGITAL_REQUEST_TIMEOUT_MILLIS}
    region = "r1"
    region = ${?DOT_DIGITAL_MARKETING_REGION}
}
awsConnect {
  region = "eu-west-2"
  region = ${?AWS_REGION}
  projectId = "844728591657"
  projectId = ${?AWS_PROJECT_ID}
  accessKey = "accessKey"
  accessKey = ${?AWS_ACCESS_KEY}
  secretAccessKey = "secretAccessKey"
  secretAccessKey = ${?AWS_SECRET_ACCESS_KEY}
  casesDomain = "affbfa92-650d-453f-9386-471f9e81744b"
  casesDomain = ${?AWS_CONNECT_CASES_DOMAIN}
  customerProfilesDomain = "so-energy-dev-customer-profiles"
  customerProfilesDomain = ${?AWS_CONNECT_CUSTOMER_PROFILES_DOMAIN}
  instanceId = "0a808e34-1797-4e32-bc98-96e0bc6a2fd2"
  instanceId = ${?AWS_CONNECT_INSTANCES_ID}
  templateId = "deb06a0b-a97b-471d-8925-c24b78ca3c14"
  templateId = ${?AWS_CONNECT_CASE_TEMPLATE_ID}
  connectTimeout = 30000
  connectTimeout = ${?AWS_CONNECT_TIMEOUT}
  socketTimeout = 60000
  socketTimeout = ${?AWS_SOCKET_TIMEOUT}
  maxRetries = 3
  maxRetries = ${?AWS_CONNECT_MAX_RETRIES}
  attachmentsBucket = "amazon-connect-dev-bucket-214365"
  attachmentsBucket = ${?AWS_CONNECT_ATTACHMENTS_BUCKET}
  instanceAlias = "soenergydev"
  instanceAlias = ${?AWS_CONNECT_INSTANCE_ALIAS}
  queues = {
    customerCare = "arn:aws:connect:eu-west-2:844728591657:instance/0a808e34-1797-4e32-bc98-96e0bc6a2fd2/queue/26d232cc-56a6-4a8c-9378-3752f1a49dce"
    customerCare = ${?AWS_CONNECT_CUSTOMER_CARE_QUEUE_ARN}
  }
  customFields = {
    dueDate = "e8a7e160-c904-4e53-bffc-7aeca0c54390"
    dueDate = ${?AWS_CONNECT_CASE_DUE_DATE_FIELD}
    description = "a3f83902-0ed1-46f3-82d8-8e0e48c450e0"
    description = ${?AWS_CONNECT_CASE_DESCRIPTION_FIELD}
    freshdeskId = "e7ad1753-1fc6-4b56-ac97-28bf8216cfd9"
    freshdeskId = ${?AWS_CONNECT_CASE_FRESHDESK_ID_FIELD}
  }
  emailAddresses = {
    helpAddress = "<EMAIL>"
    helpAddress = ${?AWS_CONNECT_HELP_ADDRESS}
  }
}

features = {
    cacheExpirySeconds = "300"
    cacheExpirySeconds = ${?FEATURE_CACHE_EXPIRY_SECONDS}
}

