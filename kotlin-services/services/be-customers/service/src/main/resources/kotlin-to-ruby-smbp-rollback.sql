CREATE
EXTENSION dblink;
SELECT dblink_connect('be_customers',
                      'host=************** port=5432 dbname=be_customers user=postgres password= options=-csearch_path=');

-- Change the WHERE id > 10000 condition before running to include only new bookings
--  STEP 1 - Populate Bookings
INSERT INTO public.bookings(account_id, at_home_name, at_home_email, at_home_phone_number,
                            customer_address, customer_postcode, mpan, mprn, gas_meter_serial_number,
                            electric_meter_serial_number,
                            job_number, cancellation_date_time, meter_reading_frequency,
                            needs_medical_equipment, junifer_ticket_id, fuel_type, updated_at, installation_date_time,
                            failed_date_time, created_at)
SELECT *
FROM dblink('be_customers', 'SELECT billing_account_id AS account_id,
                                                    customer_name AS at_home_name,
                                                    customer_email AS at_home_email,
                                                    customer_phone AS at_home_phone_number,
                                                    address AS customer_address,
                                                    postcode AS customer_postcode,
                                                    mpan AS mpan,
                                                    mprn AS mprn,
                                                    gas_meter_serial_number AS gas_meter_serial_number,
                                                    electricity_meter_serial_number AS electric_meter_serial_number,
                                                    aes_job_number AS job_number,
                                                    cancellation_date_time AS cancellation_date_time,
                                                    meter_reading_frequency AS meter_reading_frequency,
                                                    medical_equipment AS needs_medical_equipment,
                                                    junifer_ticket_id AS junifer_ticket_id,
                                                    fuel_type AS fuel_type,
                                                    updated_at AS updated_at,
                                                    installed_datetime AS installation_date_time,
                                                    failed_datetime AS failed_date_time,
                                                    created_at AS created_at
                                            FROM public.smart_meter_bookings WHERE id > 10000')
         AS p(
              account_id int4,
              at_home_name TEXT,
              at_home_email TEXT,
              at_home_phone_number TEXT,
              customer_address TEXT,
              customer_postcode TEXT,
              mpan TEXT,
              mprn TEXT,
              gas_meter_serial_number TEXT,
              electricity_meter_serial_number TEXT,
              job_number TEXT,
              cancellation_date_time TIMESTAMP,
              meter_reading_frequency TEXT,
              needs_medical_equipment BOOLEAN,
              junifer_ticket_id int4,
              fuel_type TEXT,
              updated_at TIMESTAMP,
              installed_datetime TIMESTAMP,
              failed_datetime TIMESTAMP,
              created_at TIMESTAMP
        );


--  STEP 2 - Populate Slots
INSERT INTO public.slots (installer_availability_id, start_time, end_time, date, updated_at, created_at, booking_id)
SELECT sb.aes_patch_availability_id    as installer_availability_id,
       CAST(sb.slot_date_time AS TIME) as start_time,
       CAST(sb.slot_date_time AS TIME) + INTERVAL '4 hours' AS end_time, CAST (sb.slot_date_time AS DATE) as date, now() as updated_at, now() as created_at, booking.id AS booking_id
FROM public.bookings booking
    INNER JOIN
    (SELECT * FROM dblink('be_customers', 'SELECT  aes_patch_availability_id AS installer_availability_id,
            updated_at AS updated_at,
            slot_date_time AS at_home_email,
            created_at AS created_at,
            billing_account_id AS account_id,
            aes_job_number AS job_number
        FROM public.smart_meter_bookings WHERE id > 3338')
    AS p(
    aes_patch_availability_id TEXT, updated_at TIMESTAMP, slot_date_time TIMESTAMP, created_at TIMESTAMP, account_id INT4, job_number TEXT
    )) sb
ON sb.job_number = booking.job_number AND sb.account_id = booking.account_id;

--  STEP 2 - Populate work_completions
INSERT INTO work_completions(job_number, status, fuel_type, updated_at, created_at, booking_id, installer)
SELECT sb.job_number as job_number,
       sb.status     as status,
       sb.fuel_type,
       now()         AS updated_at,
       now()         AS created_at,
       booking.id    AS booking_id,
       'AES'         AS installer
FROM public.bookings booking
         INNER JOIN
     (SELECT *
      FROM dblink('be_customers', 'SELECT  aes_patch_availability_id AS installer_availability_id,
            updated_at AS updated_at,
            slot_date_time AS at_home_email,
            created_at AS created_at,
            billing_account_id AS account_id,
            aes_job_number AS job_number,
            aes_status as status,
            fuel_type as  fuel_type
        FROM public.smart_meter_bookings WHERE id > 3338')
               AS p(
                    aes_patch_availability_id TEXT,
                    updated_at TIMESTAMP,
                    slot_date_time TIMESTAMP,
                    created_at TIMESTAMP,
                    account_id INT4,
                    job_number TEXT,
                    status TEXT,
                    fuel_type TEXT
              )) sb ON sb.job_number = booking.job_number AND sb.account_id = booking.account_id;
