package energy.so.core.customers.server.services

import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.updateCustomerRequest
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.model.tables.pojos.CustomerPreferenceLog
import energy.so.core.customers.server.database.repositories.CustomerPreferenceLogRepository
import energy.so.core.customers.server.database.repositories.CustomerSettingRepository
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.Customer
import energy.so.customers.client.models.CustomerSetting
import energy.so.customers.client.models.EnergySource
import energy.so.customers.client.models.EvType
import energy.so.customers.customersetting.v2.PatchCustomerSettingRequest
import energy.so.customers.customersetting.v2.PreferenceCheckedLog
import energy.so.dotdigital.client.DotDigitalV2Exception
import energy.so.users.v2.FeatureName
import java.time.LocalDateTime
import org.jooq.DSLContext

private val logger = TraceableLogging.logger {}

class CustomerSettingService(
    private val customerSettingRepository: CustomerSettingRepository,
    private val preferenceLogRepository: CustomerPreferenceLogRepository,
    private val customersSyncClient: CustomersSyncClient,
    private val featureService: FeatureService,
    private val customerPreferencesService: CustomerPreferencesService,
) {

    suspend fun updateCustomerSetting(customer: Customer, request: PatchCustomerSettingRequest): CustomerSetting {
        logger.debug("[::updateCustomerSetting][request : $request] Updating customer setting")
        val savedCustomerSettings = customerSettingRepository.findById(customer.customerSetting.id!!)
        val newCustomerSetting = savedCustomerSettings
            ?.let { updateCustomerSetting(request, it) }
            ?: saveNewCustomerSettings(request)

        handleMarketingOptInChange(request, customer)

        return newCustomerSetting
    }

    suspend fun getCustomerSetting(primaryContact: Contact?, settingId: Long): CustomerSetting? {
        val setting = customerSettingRepository.findById(settingId)

        val email = primaryContact?.email
        if (email == null) {
            logger.warn("No email on primary contact ${primaryContact?.id}")
        }

        // SO_23981: The marketing opt-in value is stored in dot digital, not our DB.
        if (setting != null && email != null && featureService.isFeatureEnabled(FeatureName.TMP_SO_23981_MARKETING_OPT_IN_FROM_DOT_DIGITAL)) {
            try {
                val marketingOptIn = customerPreferencesService.getMarketingOptIn(email)
                return setting.copy(marketingOptIn = marketingOptIn)
            } catch (e: DotDigitalV2Exception) {
                logger.error(e) { "Unexpected exception retrieving marketing preference for email ${primaryContact.email}: ${e.message}" }
                return setting
            }
        }
        return setting
    }

    fun save(customerSetting: CustomerSetting): CustomerSetting =
        customerSettingRepository.save(customerSetting)

    fun upsert(customerSetting: CustomerSetting, dslContext: DSLContext): CustomerSetting =
        customerSettingRepository.upsert(customerSetting, dslContext)

    fun upsertPreferenceCheckedLog(log: PreferenceCheckedLog) {
        logger.debug("[upsertPreferenceLogs][preferenceLog : $log] upserting customer preference logs")
        preferenceLogRepository.upsertLastChecked(log)
    }

    fun getLastLogDate(log: PreferenceCheckedLog): LocalDateTime {
        logger.debug("[preferenceLogs][preferenceLog : $log] getting last log")
        val lastLog = preferenceLogRepository.findByCustomerIdAndType(log.customerId, log.type.toSiblingEnum())
        return resolveLatestDate(lastLog)
    }

    private suspend fun handleMarketingOptInChange(request: PatchCustomerSettingRequest, customer: Customer) {
        if (request.hasMarketingOptIn()) {
            logger.debug(
                "[::updateCustomerSetting][handleMarketingOptInChange][request : $request] marketingOptIn was updated. Sending new data to Junifer"
            )

            // Synchronize with Junifer
            customersSyncClient.updateCustomer(
                updateCustomerRequest {
                    id = request.customerId.toString()
                    marketingOptOut = !request.marketingOptIn
                }
            )

            // Synchronize with dot digital
            updateMarketingPreference(request.marketingOptIn, customer)
        }
    }

    private fun saveNewCustomerSettings(request: PatchCustomerSettingRequest): CustomerSetting {
        logger.debug(
            "[::updateCustomerSetting][request : $request] Did not find customer setting for customer. Will save a new customer setting"
        )

        return customerSettingRepository.save(
            CustomerSetting(
                marketingOptIn = if (request.hasMarketingOptIn()) request.marketingOptIn else false,
                darkMode = if (request.hasDarkMode()) request.darkMode else false,
                smartMeterInterest = if (request.hasSmartMeterInterest()) request.smartMeterInterest else false,
                referralsEnabled = if (request.hasReferralsEnabled()) request.referralsEnabled else true,
                energySourceVote = if (request.hasEnergySource()) request.energySource.toSiblingEnum<EnergySource>() else null,
                evType = if (request.hasEvType()) request.evType.toSiblingEnum<EvType>() else null,
                intendToBuyEv = if (request.hasIntendToBuyEv()) request.intendToBuyEv else null,
                evTariffMarketingConsent = if (request.hasEvTariffMarketingConsent()) request.evTariffMarketingConsent else null,
            )
        )
    }

    private suspend fun updateMarketingPreference(marketingOptIn: Boolean, customer: Customer) {
        if (marketingOptIn) {
            customer.primaryContact?.email?.let { customerPreferencesService.subscribeContact(it) }
        } else {
            customer.primaryContact?.email?.let { customerPreferencesService.unsubscribeContact(it) }
        }
    }

    private fun updateCustomerSetting(
        request: PatchCustomerSettingRequest,
        oldSetting: CustomerSetting,
    ): CustomerSetting {
        logger.debug(
            "[::updateCustomerSetting][request : $request] Found customer setting for customer. Will update customer setting"
        )

        return customerSettingRepository.save(
            oldSetting.copy(
                marketingOptIn = if (request.hasMarketingOptIn()) request.marketingOptIn else oldSetting.marketingOptIn,
                darkMode = if (request.hasDarkMode()) request.darkMode else oldSetting.darkMode,
                smartMeterInterest = if (request.hasSmartMeterInterest()) request.smartMeterInterest else oldSetting.smartMeterInterest,
                referralsEnabled = if (request.hasReferralsEnabled()) request.referralsEnabled else oldSetting.referralsEnabled,
                energySourceVote = if (request.hasEnergySource()) request.energySource.toSiblingEnum<EnergySource>() else oldSetting.energySourceVote,
                evType = if (request.hasEvType()) request.evType.toSiblingEnum<EvType>() else oldSetting.evType,
                intendToBuyEv = if (request.hasIntendToBuyEv()) request.intendToBuyEv else oldSetting.intendToBuyEv,
                evTariffMarketingConsent = if (request.hasEvTariffMarketingConsent()) request.evTariffMarketingConsent else oldSetting.evTariffMarketingConsent,
            )
        )
    }

    private fun resolveLatestDate(lastLog: CustomerPreferenceLog?): LocalDateTime {
        if (lastLog == null) return LocalDateTime.MIN

        return lastLog.preferenceLastCheckedAt!!
    }
}
