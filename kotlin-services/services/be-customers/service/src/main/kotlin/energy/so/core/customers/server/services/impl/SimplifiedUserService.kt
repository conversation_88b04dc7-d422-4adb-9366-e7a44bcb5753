package energy.so.core.customers.server.services.impl

import energy.so.ac.junifer.v1.accounts.AccountsClient
import energy.so.ac.junifer.v1.accounts.Status
import energy.so.ac.junifer.v1.accounts.Ticket
import energy.so.ac.junifer.v1.accounts.getTicketsForAccountNumberRequest
import energy.so.assets.accountStates.v2.AssetsAccountState
import energy.so.assets.accountStates.v2.getAssetsAccountStateRequest
import energy.so.assets.api.v2.AccountStateClient
import energy.so.assets.api.v2.MeterPointsClient
import energy.so.assets.api.v2.MeterReadingsClient
import energy.so.assets.meter.v2.MeterType.S1
import energy.so.assets.meter.v2.MeterType.S2
import energy.so.assets.meter.v2.MeterType.S2A
import energy.so.assets.meter.v2.MeterType.S2AD
import energy.so.assets.meter.v2.MeterType.S2ADE
import energy.so.assets.meter.v2.MeterType.S2B
import energy.so.assets.meter.v2.MeterType.S2BD
import energy.so.assets.meter.v2.MeterType.S2BDE
import energy.so.assets.meter.v2.MeterType.S2C
import energy.so.assets.meter.v2.MeterType.S2CD
import energy.so.assets.meter.v2.MeterType.S2CDE
import energy.so.assets.meterPoints.v2.MeasurementType
import energy.so.assets.meterPoints.v2.MeterPoint
import energy.so.assets.meterPoints.v2.MeterPointType
import energy.so.assets.meterPoints.v2.getMeterPointsRequest
import energy.so.assets.meterReadings.v2.MeterReadingUnitType
import energy.so.assets.meterReadings.v2.SubmitMeterReadingFlow
import energy.so.assets.meterReadings.v2.SubmitMeterReadingRequest
import energy.so.assets.meterReadings.v2.submitMeterReadingList
import energy.so.assets.meterReadings.v2.submitMeterReadingRequest
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.idRequest
import energy.so.core.customers.server.database.repositories.CustomerUserRelRepository
import energy.so.core.customers.server.models.BillingAccount
import energy.so.core.customers.server.services.BillingAccountService
import energy.so.core.customers.server.services.VulnerabilityService
import energy.so.core.customers.server.services.smartMeter.SmartMeterService
import energy.so.customers.v2.simplifieduser.SimplifiedUserResponse
import energy.so.customers.v2.simplifieduser.SubmitMeterReadResponse
import energy.so.customers.v2.simplifieduser.SubmitMeterReadingError
import energy.so.customers.v2.simplifieduser.simplifiedUserResponse
import energy.so.customers.v2.simplifieduser.submitMeterReadResponse
import energy.so.customers.v2.simplifieduser.submitMeterReadingError
import energy.so.customers.v2.smartmeter.AvailableSlotsResponse
import energy.so.customers.v2.smartmeter.availableSlotsRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.math.absoluteValue

const val COOLING_OFF_PERIOD_DAYS = 14L
const val SMART_METER_WINDOW_DAYS = 42L

val SMART_METER_TYPES = listOf(S1, S2, S2A, S2B, S2C, S2AD, S2BD, S2CD, S2ADE, S2BDE, S2CDE)

private const val UPCOMING_OR_RECENT_RANGE_WEEKS = 8L
private const val U_SMART_TICKET_DEFINITION_CODE = "uSmartInstallAndCommission"
private const val U_SMART_TICKET_KEY_IDENTIFIER_FRAGMENT = "uSmart-IaC"
private const val U_SMART_SUMMARY_LOCALDATE_PATTERN = "\\d{4}-\\d{2}-\\d{2}" // YYYY-MM-DD

private val logger = TraceableLogging.logger { }

class SimplifiedUserService(
    val billingAccountService: BillingAccountService,
    val vulnerabilityService: VulnerabilityService,
    val smartMeterService: SmartMeterService,
    val meterPointsClient: MeterPointsClient,
    val accountStateClient: AccountStateClient,
    val juniferAccountsClient: AccountsClient,
    val customerUserRelRepository: CustomerUserRelRepository,
    val meterReadingsClient: MeterReadingsClient,
) {

    suspend fun getSimplifiedUser(customerId: Long, billingAccountId: Long): SimplifiedUserResponse {
        val billingAccount = getBillingAccount(billingAccountId)
        val meterPoints = getMeterPoints(billingAccountId)
        val accountStates = getAccountStates(billingAccountId)

        return simplifiedUserResponse {
            accountNumber = billingAccount.number
            withinRegistrationCancellationPeriodElec = withinRegistrationCancellationPeriod(
                billingAccount, meterPoints.meterPointsList, MeterPointType.MPAN
            )
            withinRegistrationCancellationPeriodGas = withinRegistrationCancellationPeriod(
                billingAccount, meterPoints.meterPointsList, MeterPointType.MPRN
            )
            withinRegistrationCoolingOffPeriodElec = withinCoolingOffPeriod(billingAccount)
            withinRegistrationCoolingOffPeriodGas = withinCoolingOffPeriod(billingAccount)
            elecSupplyStatus = getSupplyStatus(meterPoints.meterPointsList, MeterPointType.MPAN).toNullableString()
            gasSupplyStatus = getSupplyStatus(meterPoints.meterPointsList, MeterPointType.MPRN).toNullableString()
            hasMultipleMprn = meterPoints.meterPointsList.count { it.type == MeterPointType.MPRN } > 1
            hasMultipleMpan = meterPoints.meterPointsList.count { it.type == MeterPointType.MPAN } > 1
            hasExportMeter = meterPoints.meterPointsList.count { it.measurementType == MeasurementType.EXPORT } > 0
            isOnPsr = vulnerabilityService.getPsrForCustomer(idRequest { id = customerId }).isNotEmpty()
            currentReadsAccountState = accountStates.readState
            hasGasSmartMeter = hasSmartMeter(meterPoints.meterPointsList, MeterPointType.MPRN)
            hasElecSmartMeter = hasSmartMeter(meterPoints.meterPointsList, MeterPointType.MPAN)
            hasMultiRateElecMeter = hasMultiRateElecMeter(meterPoints.meterPointsList)
            hasGasMeterTechDetails = hasMeterTechDetails(meterPoints.meterPointsList, MeterPointType.MPRN)
            hasElecMeterTechDetails = hasMeterTechDetails(meterPoints.meterPointsList, MeterPointType.MPAN)
            accountState = getAccountState(billingAccount)
            hasSmartMeterAptAvailability = hasSmartmeterAvailability(meterPoints.meterPointsList)
            hasUpcomingOrRecentMEX = getJuniferTicketsAndResolveMEX(billingAccountId)
        }
    }

    private suspend fun getJuniferTickets(billingAccountId: Long) =
        juniferAccountsClient.getTicketsForAccountNumber(
            getTicketsForAccountNumberRequest {
                accountId = billingAccountId
                status = Status.Open
                includeIndirect = false
            }
        )

    internal fun getAccountState(billingAccount: BillingAccount) = if (billingAccount.deleted != null) {
        "Deleted"
    } else if (billingAccount.terminated != null) {
        "Terminated"
    } else if (billingAccount.closed != null) {
        "Closed"
    } else if (billingAccount.cancelled != null) {
        "Cancelled"
    } else if (billingAccount.to != null && !isWithinRange(
            billingAccount.from, billingAccount.to, LocalDateTime.now()
        )
    ) {
        "Inactive"
    } else {
        "Active"
    }

    private fun hasSmartMeter(meterPoints: List<MeterPoint>, meterPointType: MeterPointType): Boolean {
        return meterPoints.filter { it.type == meterPointType }.flatMap { it.metersList }
            .any { it.type in SMART_METER_TYPES }
    }

    private suspend fun getAccountStates(billingAccountId: Long) =
        accountStateClient.getAccountStates(
            getAssetsAccountStateRequest {
                accountId = billingAccountId
                accountState.add(AssetsAccountState.READ)
            }
        )

    private suspend fun getBillingAccount(billingAccountId: Long) =
        billingAccountService.getBillingAccountById(billingAccountId)
            ?: throw EntityNotFoundException("Could not find a billing account with ID $billingAccountId")

    private suspend fun getMeterPoints(billingAccountId: Long, includeReads: Boolean = false) =
        billingAccountService.getAgreementMeterPointIdsByBillingAccountId(billingAccountId).let {
            meterPointsClient.getMeterPointsByIds(
                getMeterPointsRequest {
                    ids.addAll(it)
                    includeMeterReadings = includeReads
                }
            )
        }

    // Billing accounts are created once an enrolment was successful, thus the switch start time is the account creation time
    private fun calculateSwitchStart(billingAccount: BillingAccount): LocalDateTime? =
        if (billingAccount.cancelled == null && billingAccount.terminated == null && billingAccount.closed == null && billingAccount.deleted == null) {
            billingAccount.createdAt
        } else {
            null
        }

    private fun withinCoolingOffPeriod(billingAccount: BillingAccount): Boolean =
        calculateSwitchStart(billingAccount)?.let { LocalDateTime.now() < it.plusDays(COOLING_OFF_PERIOD_DAYS) }
            ?: false

    private fun withinRegistrationCancellationPeriod(
        billingAccount: BillingAccount,
        meterPoints: List<MeterPoint>,
        type: MeterPointType,
    ): Boolean {
        val switchStart = calculateSwitchStart(billingAccount)
        val ssd = meterPoints.firstOrNull { it.type == type }?.supplyStartDate?.toLocalDateTime()

        // Registration cancellation period is from switch initiated date to supply start date -1 at 5pm
        return if (switchStart != null && ssd != null) {
            LocalDateTime.now().isAfter(switchStart) && LocalDateTime.now()
                .isBefore(ssd.minusDays(1).with(LocalTime.of(17, 0, 0)))
        } else {
            false
        }
    }

    private fun getSupplyStatus(meterPoints: List<MeterPoint>, type: MeterPointType) = meterPoints.firstOrNull {
        it.type == type && it.meterPointHistory != null && it.meterPointHistory.supplyStatus != null
    }?.meterPointHistory?.supplyStatus?.name

    private fun hasMultiRateElecMeter(meterPoints: List<MeterPoint>) = meterPoints.any { mp ->
        mp.type == MeterPointType.MPAN && mp.metersList.isNotEmpty() && mp.metersList.any { it.registersList.size > 1 }
    }

    private fun hasMeterTechDetails(meterPoints: List<MeterPoint>, type: MeterPointType) =
        meterPoints.any { it.type == type && it.metersList.isNotEmpty() }

    private fun isWithinRange(from: LocalDateTime, to: LocalDateTime, date: LocalDateTime) =
        !(date.isBefore(from) || date.isAfter(to))

    private suspend fun hasSmartmeterAvailability(meterPoints: List<MeterPoint>): Boolean = try {
        // Generate a set of unique postcodes
        val postcodes = meterPoints.mapNotNull { it.property.address.postcode.getValueOrNull() }.toSet()
        // If any postcode has any availability, this returns true
        postcodes.any {
            hasSmAvailability(
                smartMeterService.getAvailableSlots(
                    availableSlotsRequest {
                        postcode = it
                        startDate = LocalDate.now().plusDays(1).toTimestamp()
                        endDate = LocalDate.now().plusDays(SMART_METER_WINDOW_DAYS + 1).toTimestamp()
                    }
                )
            )
        }
    } catch (e: Exception) {
        logger.error(e) { "Unexpected exception when querying smart meter availability" }
        false
    }

    // We consider there to be availability if there is any slot, on any day.
    private fun hasSmAvailability(response: AvailableSlotsResponse) = response.daysList.any { slotDay ->
        slotDay != null && slotDay.slotsList.any { it != null }
    }

    internal suspend fun getJuniferTicketsAndResolveMEX(billingAccountId: Long): Boolean {
        val juniferTickets = try {
            getJuniferTickets(billingAccountId)
        } catch (e: Exception) {
            logger.error(e) { "Failed to retrieve junifer tickets for billing account: $billingAccountId" }
            return false
        }

        return hasUpcomingOrRecentMEX(juniferTickets.ticketsList)
    }

    internal fun hasUpcomingOrRecentMEX(tickets: List<Ticket>): Boolean {
        return tickets.any { ticket ->
            val isOpen = ticket.status == Status.Open
            val isUSmartKeyIdentifierPresent = ticket.keyIdentifier.contains(U_SMART_TICKET_KEY_IDENTIFIER_FRAGMENT)
            val isUSmartTicket = ticket.ticketDefinitionCode == U_SMART_TICKET_DEFINITION_CODE

            // If a parseable YYYY-MM-DD date exists in the ticket summary, in a range of +/- 8 weeks.
            val isDateUpcomingOrRecent = runCatching {
                Regex(U_SMART_SUMMARY_LOCALDATE_PATTERN).find(ticket.summary)?.value?.let { dateString ->
                    LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE)
                }
            }.getOrNull()?.let { ticketDate ->
                ChronoUnit.WEEKS.between(ticketDate, LocalDate.now()).absoluteValue <= UPCOMING_OR_RECENT_RANGE_WEEKS
            } ?: false

            return isOpen && isUSmartKeyIdentifierPresent && isUSmartTicket && isDateUpcomingOrRecent
        }
    }

    /**
     * Submits a meter reading submission to Junifer
     *
     * @param accountId The billing account ID
     * @param customerId The customer ID, used to resolve the userId.
     * @param gasReading Should be blank if there is no gas meter or no reading to submit.
     * @param elecReading1 A rate 1, day, or N meter read OR unspecified
     *
     * @return A [SubmitMeterReadResponse] object containing the success status and any error messages.
     */
    suspend fun submitMeterReadAction(
        accountId: Long,
        customerId: Long,
        submissionDate: LocalDate,
        gasReading: String,
        elecReading1: String,
    ): SubmitMeterReadResponse {
        val errors = mutableListOf<SubmitMeterReadingError>()

        // Require at least one reading is present
        if (elecReading1.isBlank() && gasReading.isBlank()) {
            errors.add(submitMeterReadingError { message = "At least one reading to be provided" })
            return submitMeterReadResponse {
                success = false
                error += errors
            }
        }

        val userIdentifier = runCatching {
            getUserId(customerId)
        }.getOrElse {
            errors.add(
                submitMeterReadingError {
                    message = "Failed to retrieve user id for customer $customerId: ${it.message}"
                }
            )
            return submitMeterReadResponse {
                success = false
                error += errors
            }
        }

        val meterPointsList = runCatching {
            val meterPoints = getMeterPoints(accountId, includeReads = true).meterPointsList
            require(meterPoints.isNotEmpty(), { "No meter points found for account: $accountId" })
            meterPoints
        }.getOrElse {
            errors.add(
                submitMeterReadingError {
                    message = "Failed to retrieve meter points for account $customerId: ${it.message}"
                }
            )
            return submitMeterReadResponse {
                success = false
                error += errors
            }
        }

        val readingRequests = mutableListOf<SubmitMeterReadingRequest>()

        if (gasReading.isNotBlank()) {
            val (request, err) = createReadingRequest(
                gasReading,
                meterPointsList,
                MeterPointType.MPRN,
                MeterReadingUnitType.GAS,
                submissionDate,
                accountId
            )

            when {
                request != null -> {
                    readingRequests += request
                }

                err != null -> {
                    return submitMeterReadResponse {
                        success = false
                        error += err
                    }
                }
            }
        }

        if (elecReading1.isNotBlank()) {
            val (request, err) = createReadingRequest(
                elecReading1,
                meterPointsList,
                MeterPointType.MPAN,
                MeterReadingUnitType.ELECTRICITY,
                submissionDate,
                accountId
            )

            when {
                request != null -> {
                    readingRequests += request
                }

                err != null -> {
                    return submitMeterReadResponse {
                        success = false
                        error += err
                    }
                }
            }
        }

        runCatching {
            val response = meterReadingsClient.submitMeterReadings(
                submitMeterReadingList {
                    userId = userIdentifier
                    billingAccountId = accountId
                    meters.addAll(readingRequests)
                    flow = SubmitMeterReadingFlow.SYNC

                }
            )

            return submitMeterReadResponse {
                success = (response.success)
                error.addAll(
                    response.errorList.map {
                        submitMeterReadingError {
                            message = buildString {
                                append("Meter read submission failed. Error code: ${it.code}")
                                it.meterIdentifier.getValueOrNull()?.takeIf { it.isNotBlank() }?.let {
                                    append(", Meter Identifier: $it")
                                }
                                it.registerIdentifier.getValueOrNull()?.takeIf { it.isNotBlank() }?.let {
                                    append(", Register Identifier: $it")
                                }
                                append(", Message: ${it.message}")
                            }
                        }
                    }
                )
            }
        }.getOrElse {
            errors.add(
                submitMeterReadingError {
                    message = "Unexpected error encountered during meter read submission. ${it.message}"
                }
            )
            return submitMeterReadResponse {
                success = false
                error += errors
            }
        }
    }

    private fun getUserId(customerId: Long): Long {
        val userId = customerUserRelRepository.getMostRecentUserIdByCustomerId(customerId)
        return userId ?: throw NoSuchElementException("No user ID found for customer $customerId")
    }

    private fun createReadingRequest(
        reading: String,
        meterPointsList: List<MeterPoint>,
        meterPointType: MeterPointType,
        readingUnitType: MeterReadingUnitType,
        submissionDate: LocalDate,
        accountId: Long,
    ): Pair<SubmitMeterReadingRequest?, SubmitMeterReadingError?> {
        return runCatching {
            val meterPoint = meterPointsList.firstOrNull { it.type == meterPointType } ?: throw NoSuchElementException(
                "Unable to find meterpoint of correct type for this account."
            )

            buildSubmitMeterReadingRequest(reading, readingUnitType, submissionDate, meterPoint)
        }.fold(onSuccess = { request -> Pair(request, null) }, onFailure = { exception ->
            val errMsg =
                "Unexpected error creating submitMeterReadingRequest for ${readingUnitType.name.lowercase()} reading $reading, account: $accountId, error type: ${exception.javaClass.simpleName}, details: [${exception.message}]"

            Pair(null, submitMeterReadingError { message = errMsg })
        })
    }

    internal fun buildSubmitMeterReadingRequest(
        reading: String,
        readingUnitType: MeterReadingUnitType,
        submissionDate: LocalDate,
        meterPoint: MeterPoint,
        onlyValidate: Boolean = false,
    ) = submitMeterReadingRequest {
        digits.addAll(reading.map { it.toString() })
        unitType = readingUnitType
        meterPoint.metersList.firstOrNull()?.identifier.let { meterIdentifier = it.toNullableString() }
        meterPointId = meterPoint.id
        units = reading.toLong()
        meterPoint.metersList.firstOrNull()?.registersList?.firstOrNull()?.identifier?.let { registerIdentifier = it }
        readingAt = submissionDate.toTimestamp()
        validateOnly = onlyValidate
    }
}
