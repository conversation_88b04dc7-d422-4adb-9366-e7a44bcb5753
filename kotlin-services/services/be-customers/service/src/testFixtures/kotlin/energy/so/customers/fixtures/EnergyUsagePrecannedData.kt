package energy.so.customers.fixtures

import energy.so.ac.junifer.v1.assets.reading
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.customers.fixtures.BillingAccountData.BILLING_ACCOUNT_ID
import energy.so.customers.v2.usage.EnergyUsageGranularity
import energy.so.customers.v2.usage.energyUsageRequest
import java.time.LocalDateTime

object EnergyUsagePrecannedData {


    val energyUsageRequest = energyUsageRequest {
        accountId = BILLING_ACCOUNT_ID
        granularity = EnergyUsageGranularity.DAY
        fromDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toTimestamp()
        toDate = LocalDateTime.of(2024, 1, 2, 0, 0, 0).toTimestamp()
    }

    val oneDayHHIncomplete = listOf(
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // 48 HH periods
    )

    val oneDayHHComplete = listOf(
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // 48 HH periods
    )

    val oneDayHHEmpty = listOf(
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // 48 HH periods
    )

    val oneWeekHHComplete = listOf(
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 1
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 2
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 3
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 4
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 5
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 6
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, // day 7 - 336 HH periods
    )

    val oneWeekHHIncomplete = listOf(
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 1
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 2
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 3
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 4
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 5
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 6
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, // day 7 - 336 HH periods
    )

    val oneWeekHHEmpty = listOf(
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 1
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 2
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 3
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 4
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 5
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 6
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, // day 7 - 336 HH periods
    )

    val week1Start = LocalDateTime.of(2024, 12, 31, 0, 0, 0)
    val week1ReadingsIncomplete = listOf(
        reading {
            readingDttm = week1Start.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        }
    )

    val week2Start = week1Start.plusWeeks(1)
    val week2ReadingsIncomplete = listOf(
        reading {
            readingDttm = week2Start.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        }
    )

    val week1And2ReadingsIncomplete = week1ReadingsIncomplete + week2ReadingsIncomplete

    val week1ReadingsComplete = week1ReadingsIncomplete + listOf(
        reading {
            readingDttm = week1Start.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week1Start.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        }
    )

    val week2ReadingsComplete = week2ReadingsIncomplete + listOf(
        reading {
            readingDttm = week2Start.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = week2Start.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        }
    )

    val week1And2ReadingsComplete = week1ReadingsComplete + week2ReadingsComplete

    val twoWeeksHHIncomplete = oneWeekHHIncomplete + oneWeekHHIncomplete
    val twoWeeksHHComplete = oneWeekHHComplete + oneWeekHHComplete

    val oneWeekHHIncompleteAndOneWeekHHEmpty = oneWeekHHIncomplete + oneWeekHHEmpty
    val oneWeekHHCompleteAndOneWeekHHEmpty = oneWeekHHComplete + oneWeekHHEmpty

    // 28 days, 7 + 7 + 7 + 7
    val hhIncomplete28d =
        oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete

    val hhComplete28d =
        oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete

    val hhEmpty28d =
        oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty

    // 31 days, 7 + 7 + 7 + 7 + 1 + 1 + 1
    val hhIncomplete31d =
        oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete +
                oneDayHHIncomplete + oneDayHHIncomplete + oneDayHHIncomplete

    val hhComplete31d =
        oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete +
                oneDayHHComplete + oneDayHHComplete + oneDayHHComplete

    val hhEmpty31d =
        oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty +
                oneDayHHEmpty + oneDayHHEmpty + oneDayHHEmpty

    // 30 days, 7 + 7 + 7 + 7 + 1 + 1
    val hhIncomplete30d =
        oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete + oneWeekHHIncomplete +
                oneDayHHIncomplete + oneDayHHIncomplete

    val hhComplete30d =
        oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete + oneWeekHHComplete +
                oneDayHHComplete + oneDayHHComplete

    val hhEmpty30d =
        oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty + oneWeekHHEmpty +
                oneDayHHEmpty + oneDayHHEmpty

    val janAndFebIncompleteHH = hhIncomplete31d + hhIncomplete28d
    val janAndFebCompleteHH = hhComplete31d + hhComplete28d
    val janIncompleteFebEmptyHH = hhIncomplete31d + hhEmpty28d

    val dayBeforeJanStart = LocalDateTime.of(2024, 12, 31, 0, 0)
    val janDailyReadingsIncomplete = listOf(
        reading {
            readingDttm = dayBeforeJanStart.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(8).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(8).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(9).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(9).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(10).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(10).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(11).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(11).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(12).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(12).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(13).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(13).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(14).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(14).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(15).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(15).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(16).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(16).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(17).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(17).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(18).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(18).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(19).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(19).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(20).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(20).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(21).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(21).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(22).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(22).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(23).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(23).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(24).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(24).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(25).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(25).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(26).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(26).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(27).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(27).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(28).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(28).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(29).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(29).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
    )

    val janDailyReadingsComplete = janDailyReadingsIncomplete + listOf(
        reading {
            readingDttm = dayBeforeJanStart.plusDays(30).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(30).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(31).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeJanStart.plusDays(31).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
    )

    val dayBeforeFebStart = LocalDateTime.of(2025, 1, 31, 0, 0)
    val febDailyReadingsIncomplete = listOf(
        reading {
            readingDttm = dayBeforeFebStart.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(1).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(2).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(3).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(4).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(5).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(6).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(7).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(8).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(8).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(9).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(9).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(10).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(10).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(11).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(11).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(12).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(12).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(13).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(13).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(14).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(14).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(15).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(15).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(16).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(16).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(17).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(17).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(18).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(18).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(19).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(19).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(20).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(20).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(21).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(21).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(22).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(22).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(23).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(23).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(24).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(24).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(25).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(25).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(26).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(26).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
    )

    val febDailyReadingsComplete = febDailyReadingsIncomplete + listOf(
        reading {
            readingDttm = dayBeforeFebStart.plusDays(27).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(27).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(28).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_1.toLong()
        },
        reading {
            readingDttm = dayBeforeFebStart.plusDays(28).toTimestamp()
            consumption = 1.0
            cumulative = 1.0
            meterRegisterId = REGISTER_IDENTIFIER_2.toLong()
        })


    val yearHHIncomplete =
        hhIncomplete31d + // Jan
                hhIncomplete28d + // Feb
                hhIncomplete31d + // March
                hhIncomplete30d +  // April
                hhIncomplete31d + // May
                hhIncomplete30d + // June
                hhIncomplete31d + // July
                hhIncomplete31d + // August
                hhIncomplete30d + // September
                hhIncomplete31d + // October
                hhIncomplete30d + // November
                hhIncomplete31d // December

    val yearHHComplete =
        hhComplete31d + // Jan
                hhComplete28d + // Feb
                hhComplete31d + // March
                hhComplete30d +  // April
                hhComplete31d + // May
                hhComplete30d + // June
                hhComplete31d + // July
                hhComplete31d + // August
                hhComplete30d + // September
                hhComplete31d + // October
                hhComplete30d + // November
                hhComplete31d // December

    val yearHHEmpty =
        hhEmpty31d + // Jan
                hhEmpty28d + // Feb
                hhEmpty31d + // March
                hhEmpty30d +  // April
                hhEmpty31d + // May
                hhEmpty30d + // June
                hhEmpty31d + // July
                hhEmpty31d + // August
                hhEmpty30d + // September
                hhEmpty31d + // October
                hhEmpty30d + // November
                hhEmpty31d // December
}