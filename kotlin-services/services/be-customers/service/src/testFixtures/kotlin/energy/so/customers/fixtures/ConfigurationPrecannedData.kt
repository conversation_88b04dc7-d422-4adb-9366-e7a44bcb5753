package energy.so.customers.fixtures

import energy.so.commons.model.enums.ConfigurationName
import energy.so.commons.model.tables.pojos.Configuration
import energy.so.customers.v2.configurations.getConfigurationRequest
import energy.so.customers.v2.configurations.getConfigurationResponse

val renewWindowConfiguration = Configuration(
    name = ConfigurationName.RENEWAL_WINDOW,
    value = "60"
)

val renewWindowRequest = getConfigurationRequest {
    configurationName = energy.so.customers.v2.configurations.ConfigurationName.RENEWAL_WINDOW
}


val renewWindowResponse = getConfigurationResponse {
    configurationValue = "60"
    configurationName = energy.so.customers.v2.configurations.ConfigurationName.RENEWAL_WINDOW
}
