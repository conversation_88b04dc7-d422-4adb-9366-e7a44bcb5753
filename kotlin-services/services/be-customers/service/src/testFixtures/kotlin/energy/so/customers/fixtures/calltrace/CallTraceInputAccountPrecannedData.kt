package energy.so.customers.fixtures.calltrace

import energy.so.commons.model.enums.CallTraceAuditConfirmedEnum
import energy.so.commons.model.enums.CallTraceAuditGdprEnum
import energy.so.core.customers.server.calltrace.CallTraceAudit
import energy.so.core.customers.server.calltrace.CallTraceInputAccount
import energy.so.customers.client.models.Address
import energy.so.customers.client.models.BillDeliveryMethod
import energy.so.core.customers.server.models.BillingAccount
import energy.so.core.customers.server.models.BillingAccountClass
import energy.so.customers.client.models.BillingAccountSettings
import energy.so.core.customers.server.models.BillingAccountType
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.ContactType
import energy.so.customers.client.models.Title
import java.time.LocalDate
import java.time.LocalDateTime

object CallTraceInputAccountPrecannedData {
    object Constants {
        const val ACCOUNT_NUMBER = "1"
        const val CONTACT_ID = 1L

        val TITLE = Title.DR
        const val FIRST_NAME = "Glen"
        const val LASTNAME = "Glenberry"
        const val EMAIL = "<EMAIL>"
        val DATE_OF_BIRTH = LocalDate.of(1964, 1, 1)
        const val ADDRESS = "11 Prime Road"
        val CREATED_DATE = LocalDateTime.MIN
        val UPDATED_DATE = LocalDateTime.MIN
        const val CARE_OF = ""
        const val PHONE_NUMBER_1 = "***********"
        const val PHONE_NUMBER_2 = ""
        const val PHONE_NUMBER_3 = ""
        val FROM_DATE = LocalDateTime.of(2024, 1, 1, 0, 0)
        val TO_DATE = LocalDateTime.of(2050, 1, 1, 0, 0)

        const val ADDRESS_1 = "11 Prime Road"
        const val ADDRESS_2 = "Fictionville"
        const val ADDRESS_3 = "London"
        const val ADDRESS_4 = ""
        const val ADDRESS_5 = ""
        const val ADDRESS_6 = ""
        const val ADDRESS_7 = ""
        const val ADDRESS_8 = ""
        const val ADDRESS_9 = ""
        const val POSTCODE = "E1 1E1"
        const val COUNTRY_CODE = "GBR"
        val ADDRESS_TYPE = ContactType.RESIDENTIAL.name
        const val nameTraceCSVHeaders =
            "clientId,name,title,forename,othername,surname,dateOfBirth,address1,address2,address3,address4,address5,address6,address7,postcode"
    }

    fun callTraceInputAccount(
        billingAccountId: Long = 1L,
        juniferAccountNumber: String = Constants.ACCOUNT_NUMBER,
        contactWithoutAnyContractInfo: Contact = contact(
            id = 1L,
            type = ContactType.RESIDENTIAL,
        ),
        settings: BillingAccountSettings = settings(
            billDeliveryMethod = BillDeliveryMethod.EMAIL,
            receivePost = false,
            receiveSms = false,
            receiveEmail = false
        )
    ): CallTraceInputAccount {
        return CallTraceInputAccount(
            billingAccountId = billingAccountId,
            juniferAccountNumber = juniferAccountNumber, // Question for later - is this the right name?
            contact = contactWithoutAnyContractInfo,
            settings = settings
        )
    }

    fun settings(
        billDeliveryMethod: BillDeliveryMethod = BillDeliveryMethod.EMAIL,
        receivePost: Boolean = false,
        receiveSms: Boolean = false,
        receiveEmail: Boolean = false
    ) = BillingAccountSettings(
        billDeliveryMethod = billDeliveryMethod,
        receivePost = receivePost,
        receiveSms = receiveSms,
        receiveEmail = receiveEmail
    )

    fun contact(
        id: Long? = Constants.CONTACT_ID,
        type: ContactType = ContactType.RESIDENTIAL,
        title: Title? = Constants.TITLE,
        firstName: String? = Constants.FIRST_NAME,
        lastName: String? = Constants.LASTNAME,
        email: String? = Constants.EMAIL,
        dateOfBirth: LocalDate? = Constants.DATE_OF_BIRTH,
        address: Address? = address(),
        createdAt: LocalDateTime? = Constants.CREATED_DATE,
        updatedAt: LocalDateTime? = Constants.UPDATED_DATE,
        careOf: String? = Constants.CARE_OF,
        phoneNumber1: String? = Constants.PHONE_NUMBER_1,
        phoneNumber2: String? = Constants.PHONE_NUMBER_2,
        phoneNumber3: String? = Constants.PHONE_NUMBER_3,
        fromDate: LocalDateTime? = Constants.FROM_DATE,
        toDate: LocalDateTime? = Constants.TO_DATE,
        customerContactId: Long? = Constants.CONTACT_ID,
        deleted: LocalDateTime? = null,
    ): Contact {
        return Contact(
            id = id,
            firstName = firstName,
            lastName = lastName,
            email = email,
            dateOfBirth = dateOfBirth,
            address = address,
            createdAt = createdAt,
            updatedAt = updatedAt,
            type = type,
            title = title,
            careOf = careOf,
            phoneNumber1 = phoneNumber1,
            phoneNumber2 = phoneNumber2,
            phoneNumber3 = phoneNumber3,
            fromDate = fromDate,
            toDate = toDate,
            customerContactId = customerContactId,
            deleted = deleted,
        )
    }

    fun address(
        id: Long? = Constants.CONTACT_ID,
        address1: String? = Constants.ADDRESS_1,
        address2: String? = Constants.ADDRESS_2,
        address3: String? = Constants.ADDRESS_3,
        address4: String? = Constants.ADDRESS_4,
        address5: String? = Constants.ADDRESS_5,
        address6: String? = Constants.ADDRESS_6,
        address7: String? = Constants.ADDRESS_7,
        address8: String? = Constants.ADDRESS_8,
        address9: String? = Constants.ADDRESS_9,
        postcode: String? = Constants.POSTCODE,
        countryCode: String? = Constants.COUNTRY_CODE,
        type: String = Constants.ADDRESS_TYPE,
        deleted: LocalDateTime? = null,
        createdAt: LocalDateTime = LocalDateTime.MIN,
        updatedAt: LocalDateTime = LocalDateTime.MIN,
    ): Address {
        return Address(
            id = id,
            address1 = address1,
            address2 = address2,
            address3 = address3,
            address4 = address4,
            address5 = address5,
            address6 = address6,
            address7 = address7,
            address8 = address8,
            address9 = address9,
            postcode = postcode,
            countryCode = countryCode,
            type = type,
            deleted = deleted,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }

    fun billingAccount(
        id: Long? = null,
        type: BillingAccountType = BillingAccountType.CR_INVOICE,
        name: String = "name",
        number: String = "number",
        currency: String = "GBP",
        `class`: BillingAccountClass = BillingAccountClass.INVOICE,
        from: LocalDateTime = LocalDateTime.MIN,
        to: LocalDateTime? = null,
        closed: LocalDateTime? = null,
        cancelled: LocalDateTime? = null,
        terminated: LocalDateTime? = null,
        deleted: LocalDateTime? = null,
        createdAt: LocalDateTime? = null,
        updatedAt: LocalDateTime? = null,
        primaryContact: Contact? = contact(),
        customerId: Long = 0,
        settings: BillingAccountSettings? = null,
    ): BillingAccount {
        return BillingAccount(
            id = id,
            type = type,
            name = name,
            number = number,
            currency = currency,
            `class` = `class`,
            from = from,
            to = to,
            closed = closed,
            cancelled = cancelled,
            terminated = terminated,
            deleted = deleted,
            createdAt = createdAt,
            updatedAt = updatedAt,
            primaryContact = primaryContact,
            customerId = customerId,
            settings = settings,
        )
    }

    fun callTraceAudit(
        juniferAccountNumber: String,
        dataUpdated: String,
        gdprEnum: CallTraceAuditGdprEnum,
        confirmStatus: CallTraceAuditConfirmedEnum
    ) = CallTraceAudit(
        juniferAccountNumber,
        dataUpdated,
        gdprEnum,
        confirmStatus
    )
}
