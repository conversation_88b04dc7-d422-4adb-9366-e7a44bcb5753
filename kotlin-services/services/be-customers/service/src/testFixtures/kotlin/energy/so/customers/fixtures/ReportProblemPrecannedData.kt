package energy.so.customers.fixtures

import com.google.protobuf.ByteString
import com.google.protobuf.kotlin.toByteString
import energy.so.commons.extension.toJooqJson
import energy.so.commons.model.enums.CustomerClass
import energy.so.commons.model.enums.CustomerType
import energy.so.commons.model.tables.pojos.Customer
import energy.so.core.customers.server.dtos.ReportProblemDto
import energy.so.core.customers.server.models.ReportProblem
import energy.so.core.customers.server.models.ReportProblemAttachment
import energy.so.core.customers.server.models.SessionDetails
import energy.so.core.customers.server.models.SessionDetails.Companion.fromJson
import energy.so.core.customers.server.models.SessionDetails.Companion.fromProto
import energy.so.customers.reportproblems.v2.metadata
import energy.so.customers.reportproblems.v2.reportProblemResponse
import energy.so.customers.reportproblems.v2.sessionDetails
import java.time.LocalDateTime
import java.util.UUID
import energy.so.commons.model.tables.pojos.CustomerMetadata

object ReportProblemsData {
    // REPORT PROBLEM VALUES
    const val USER_ID = 1L
    const val CUSTOMER_ID = 1L
    const val DESCRIPTION = "ReportProblemDescription"
    const val TEST_IMAGE = "/testImage.svg"
    const val TEST_FAIL_TYPE_ATTACHMENT = "/failTypeAttachment.txt"
    const val TEST_FAIL_CONTENT_SIZE_ATTACHMENT = "/testImageFailContentSize.jpg"

    // REPORT PROBLEM ATTACHMENTS VALUES
    const val PROBLEM_REPORT_ID = 1L
    const val IMAGE_CONTENT_TYPE = "image/svg+xml"
    val image: ByteString = readFromFile(TEST_IMAGE)
    val IMAGE_FILE_NAME = "${UUID.nameUUIDFromBytes(image.toByteArray())}.svg"
    val IMAGE_FILE_NAME_WITHOUT_EXTENSION = "${UUID.nameUUIDFromBytes(image.toByteArray())}"
    val IMAGE_FILE_SIZE = image.size()
    val imgFailType = readFromFile(TEST_FAIL_TYPE_ATTACHMENT)
    val imgFailContentSize = readFromFile(TEST_FAIL_CONTENT_SIZE_ATTACHMENT)
    val SESSION_DETAILS = SessionDetails(
        os = "os",
        browserInfo = "browserInfo",
        mobile = true,
        appVersion = "appVersion",
        accountNumber = "accountNumber",
        screenSize = 1366,
    )

    // COMMON VALUES
    val CONSTANT_DATE: LocalDateTime = LocalDateTime.now().withNano(0)
    val CUSTOMER_METADATA_ID = 123L
    val nowLocalDateTime = LocalDateTime.now().withHour(0).withSecond(0).withNano(0)

    val customer = Customer(
        id = CUSTOMER_ID,
        number = "123456",
        firstName = "Michael",
        lastName = "Long",
        customerSettingId = 1,
        customerMetadataId = CUSTOMER_METADATA_ID,
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS
    )
    val unsavedReportProblem = ReportProblem(
        id = PROBLEM_REPORT_ID,
        customerId = CUSTOMER_ID,
        description = DESCRIPTION,
        createdAt = CONSTANT_DATE,
        updatedAt = CONSTANT_DATE,
        sessionDetails = SESSION_DETAILS
    )

    val unsavedPartialReportProblem = ReportProblem(
        customerId = CUSTOMER_ID,
        description = DESCRIPTION
    )

    val unsavedReportProblemAttachments = ReportProblemAttachment(
        reportProblemId = PROBLEM_REPORT_ID,
        imageFileName = IMAGE_FILE_NAME,
        imageContentType = IMAGE_CONTENT_TYPE,
        imageFileSize = IMAGE_FILE_SIZE,
        createdAt = CONSTANT_DATE,
        updatedAt = CONSTANT_DATE,
        imageUpdatedAt = CONSTANT_DATE
    )

    val unsavedPartialReportProblemAttachments = ReportProblemAttachment(
        reportProblemId = PROBLEM_REPORT_ID,
        imageFileName = IMAGE_FILE_NAME,
        imageFileSize = IMAGE_FILE_SIZE,
        imageContentType = IMAGE_CONTENT_TYPE
    )

    val sessionDetailsObj = sessionDetails {
        os = "os"
        browserInfo = "browserInfo"
        mobile = true
        appVersion = "appVersion"
        accountNumber = "1"
        screenSize = 1
    }

    val metaObj = metadata {
        customerId = CUSTOMER_ID
        description = DESCRIPTION
        sessionDetails = sessionDetailsObj
    }

    val reportProblemDto = ReportProblemDto(
        meta = metaObj,
        imagesList = listOf(image)
    )

    val reportProblemModel = ReportProblem(
        id = 1L,
        customerId = CUSTOMER_ID,
        description = DESCRIPTION,
        createdAt = CONSTANT_DATE,
        updatedAt = CONSTANT_DATE,
        sessionDetails = fromProto(sessionDetailsObj)
    )

    val reportProblemAttachmentModel = ReportProblemAttachment(
        id = 1,
        reportProblemId = PROBLEM_REPORT_ID,
        imageFileName = IMAGE_FILE_NAME,
        imageContentType = IMAGE_CONTENT_TYPE,
        imageFileSize = IMAGE_FILE_SIZE,
        createdAt = CONSTANT_DATE,
        updatedAt = CONSTANT_DATE,
        imageUpdatedAt = CONSTANT_DATE
    )

    val reportProblemResponse = reportProblemResponse {
        response = "Success for: 1"
    }

    val customerMetadata = CustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )

    fun readFromFile(fileName: String): ByteString {
        val fileContent = ReportProblemsData::class.java.getResource(fileName)?.readBytes()
        return fileContent!!.toByteString()
    }
}
