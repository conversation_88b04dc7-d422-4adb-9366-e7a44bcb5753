package energy.so.customers.fixtures

import energy.so.commons.grpc.extensions.toNullableString
import energy.so.customers.v2.contactRequest

val nullValue: String? = null

val contacts = listOf(
    contactRequest {
        primaryContact = true
        phone = "123456789".toNullableString()
        email = "<EMAIL>".toNullableString()
    },
    contactRequest {
        primaryContact = false
        phone = "987654321".toNullableString()
        email = "<EMAIL>".toNullableString()
    },
    contactRequest {
        primaryContact = false
        phone = nullValue.toNullableString()
        email = nullValue.toNullableString()
    }
)

val phoneTestDataSuit = listOf(
    contactRequest {
        primaryContact = true
        phone = "123456789".toNullableString()
    } to "123456789",
    contactRequest {
        primaryContact = false
        phone = "987654321".toNullableString()
    } to "987654321",
    contactRequest {
        primaryContact = false
        phone = nullValue.toNullableString()
    } to null,
    contactRequest {
        primaryContact = true
        phone = nullValue.toNullableString()
    } to "123456789",
    contactRequest {
        primaryContact = true
        phone = "".toNullableString()
    } to "123456789"
)

val emailTestDataSuit = listOf(
    contactRequest {
        primaryContact = true
        email = "<EMAIL>".toNullableString()
    } to "<EMAIL>",
    contactRequest {
        primaryContact = false
        email = "<EMAIL>".toNullableString()
    } to "<EMAIL>",
    contactRequest {
        primaryContact = false
        email = nullValue.toNullableString()
    } to null,
    contactRequest {
        primaryContact = true
        email = nullValue.toNullableString()
    } to "<EMAIL>"
)
