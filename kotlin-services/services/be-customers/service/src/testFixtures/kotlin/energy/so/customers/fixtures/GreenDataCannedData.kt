package energy.so.customers.fixtures

import energy.so.assets.meterReadings.v2.electricityMeterReadingsByDateResponse
import energy.so.assets.meterReadings.v2.getElectricityMeterReadingsByDateRequest
import energy.so.assets.meterReadings.v2.getFirstElectricityMeterReadingRequest
import energy.so.assets.meterReadings.v2.getLastElectricityMeterReadingRequest
import energy.so.assets.meterReadings.v2.meterReading
import energy.so.assets.register.v2.register
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.core.customers.server.models.Agreement
import energy.so.core.customers.server.models.AgreementType
import energy.so.customers.fixtures.AgreementPrecannedData.noAssetContents
import energy.so.customers.fixtures.AgreementPrecannedData.oneAssetContents
import energy.so.customers.fixtures.AgreementPrecannedData.twoAssetsContents
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

val REGISTER_IDENTIFIER_1 = "1"
val REGISTER_IDENTIFIER_2 = "2"

val agreement = Agreement(
    id = 1L,
    type = AgreementType.FIXED_12_MONTHS,
    from = localDateTimeNow.withNano(0),
    to = localDateTimeNow.withNano(0).plusDays(20),
    cancelled = false,
    number = "1",
    quoteId = 24,
    contents = oneAssetContents,
    createdAt = localDateTimeNow,
    updatedAt = LocalDateTime.now(),
    deleted = null,
    ordered = LocalDate.now(),
)

val getFirstReadingRequest = getFirstElectricityMeterReadingRequest {
    meterPointId = 412
}

val getFirstReadingResponse = meterReading {
    readingDttm = LocalDateTime.of(LocalDate.of(2020, 1, 1), LocalTime.MIN).toTimestamp()
}

val getFirstReadingRequest2 = getFirstElectricityMeterReadingRequest {
    meterPointId = 413
}

val getLastReadingRequest = getLastElectricityMeterReadingRequest {
    meterPointId = 412
}

val getLastReadingResponse = meterReading {
    readingDttm = LocalDateTime.of(LocalDate.of(2021, 12, 1), LocalTime.MAX).toTimestamp()
}

val getLastReadingRequest2 = getLastElectricityMeterReadingRequest {
    meterPointId = 413
}

val getElectricityMeterReadingsByDateRequest2020 = getElectricityMeterReadingsByDateRequest {
    meterPointId = 412
    startDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0).toTimestamp()
    endDate = LocalDateTime.of(2020, 12, 31, 23, 59, 59).toTimestamp()
}

val getElectricityMeterReadingsByDateRequest2021 = getElectricityMeterReadingsByDateRequest {
    meterPointId = 412
    startDate = LocalDateTime.of(2021, 1, 1, 0, 0, 0).toTimestamp()
    endDate = LocalDateTime.of(2021, 12, 31, 23, 59, 59).toTimestamp()
}

val getElectricityMeterReadingsByDateRequest2020_413 = getElectricityMeterReadingsByDateRequest {
    meterPointId = 413
    startDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0).toTimestamp()
    endDate = LocalDateTime.of(2020, 12, 31, 23, 59, 59).toTimestamp()
}

val getElectricityMeterReadingsByDateRequest2021_413 = getElectricityMeterReadingsByDateRequest {
    meterPointId = 413
    startDate = LocalDateTime.of(2021, 1, 1, 0, 0, 0).toTimestamp()
    endDate = LocalDateTime.of(2021, 12, 31, 23, 59, 59).toTimestamp()
}

val getElectricityMeterReadingsByDateResponse2020SingleRegister = electricityMeterReadingsByDateResponse {
    results.addAll(
        listOf(
        meterReading {
            cumulative = 100.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 200.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 12, 1), LocalTime.MAX).toTimestamp()
        }
    ))
}

val getElectricityMeterReadingsByDateResponse2021SingleRegister = electricityMeterReadingsByDateResponse {
    results.addAll(
        listOf(
        meterReading {
            cumulative = 300.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 400.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 12, 1), LocalTime.MAX).toTimestamp()
        }
    ))
}

val getElectricityMeterReadingsByDateResponse2020MultipleRegister = electricityMeterReadingsByDateResponse {
    results.addAll(
        listOf(
        meterReading {
            cumulative = 100.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 200.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 12, 1), LocalTime.MAX).toTimestamp()
        },
        meterReading {
            cumulative = 100.00
            register = register { identifier = REGISTER_IDENTIFIER_2.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 200.00
            register = register { identifier = REGISTER_IDENTIFIER_2.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2020, 12, 1), LocalTime.MAX).toTimestamp()
        }
    ))
}

val getElectricityMeterReadingsByDateResponse2021MultipleRegister = electricityMeterReadingsByDateResponse {
    results.addAll(
        listOf(
        meterReading {
            cumulative = 300.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 400.00
            register = register { identifier = REGISTER_IDENTIFIER_1.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 12, 1), LocalTime.MAX).toTimestamp()
        },
        meterReading {
            cumulative = 300.00
            register = register { identifier = REGISTER_IDENTIFIER_2.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIN).toTimestamp()
        },
        meterReading {
            cumulative = 400.00
            register = register { identifier = REGISTER_IDENTIFIER_2.toNullableString() }
            readingDttm = LocalDateTime.of(LocalDate.of(2021, 12, 1), LocalTime.MAX).toTimestamp()
        }
    ))
}

val emptyMeterPointResponse = listOf(agreement.copy(contents = noAssetContents))

val singleMeterPointResponse = listOf(agreement.copy(contents = oneAssetContents))

val multipleMeterPointResponse = listOf(agreement.copy(contents = twoAssetsContents))
