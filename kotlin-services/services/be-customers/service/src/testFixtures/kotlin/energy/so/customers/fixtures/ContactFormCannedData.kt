package energy.so.customers.fixtures

import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.core.customers.server.models.FreshdeskCreateTicketRequestDto
import energy.so.core.customers.server.models.FreshdeskCreateTicketResponseDto
import energy.so.customers.v2.contactform.ContactFormSubmissionRequest
import energy.so.customers.v2.contactform.contactFormSubmissionRequest
import java.time.LocalDateTime

object ContactFormCannedData {
    fun contactFormSubmissionRequest(ticketSubject: String): ContactFormSubmissionRequest {
        return contactFormSubmissionRequest {
            name = "<PERSON> Testman"
            subject = ticketSubject
            email = "<EMAIL>"
            accountId = 101L.toNullableInt64()
            message = "message"
        }
    }

    fun freshdeskCreateTicketRequest(ticketSubject: String, customFieldMapping: String) = FreshdeskCreateTicketRequestDto(
        name = "<PERSON> Testman",
        email = "<EMAIL>",
        priority = 1,
        status = 2,
        source = 2,
        description = "message",
        tags = listOf(ticketSubject),
        group_id = *************,
        custom_fields = mapOf("cf_what_is_your_query_about" to customFieldMapping, "cf_sent_on_behalf" to "False"),
        subject = "Contact Form - $ticketSubject",
        requester_id = 101
    )

    val freshdeskCreateTicketResponseDto = FreshdeskCreateTicketResponseDto(
        cc_emails = emptyList(),
        fwd_emails = emptyList(),
        reply_cc_emails = emptyList(),
        ticket_cc_emails = emptyList(),
        fr_escalated = false,
        spam = false,
        email_config_id = null,
        group_id = *************,
        priority = 1,
        requester_id = 50000009716919,
        responder_id = null,
        source = 2,
        status = 2,
        company_id = null,
        subject = "Contact Form - My Meter",
        support_email = null,
        to_emails = null,
        product_id = null,
        id = 94,
        type = null,
        due_by = LocalDateTime.of(2023,6,20,11,50,11),
        fr_due_by = LocalDateTime.of(2023,6,20,11,50,11),
        is_escalated = false,
        description = "<div>Test!</div>",
        description_test = "Test!",
        customFields = mapOf("cf_what_is_your_query_about" to "Metering"),
        created_at = LocalDateTime.of(2023,6,20,11,50,11),
        updated_at = LocalDateTime.of(2023,6,20,11,50,11),
        tags = listOf("Metering"),
        attachments = emptyList(),
        internal_agent_id = null,
        internal_group_id = null,
        nr_due_by = null,
        nr_escalated = false
    )
}
