package energy.so.customers.fixtures

import com.google.protobuf.NullValue
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableLongString
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.model.tables.pojos.CustomVulnerability
import energy.so.commons.model.tables.pojos.SyncStatu
import energy.so.commons.model.tables.references.CUSTOM_VULNERABILITY
import energy.so.commons.nullableInt64
import energy.so.commons.nullableTimestamp
import energy.so.commons.v2.sync.OperationType
import energy.so.commons.v2.sync.syncResponse
import energy.so.customers.sync.v2.copy
import energy.so.customers.sync.v2.customVulnerabilityEntity
import energy.so.customers.sync.v2.customVulnerabilityEntityRequest
import java.time.LocalDate
import java.time.LocalDateTime

object CustomVulnerabilityPrecannedData {
    const val ID_1 = 1L
    const val ID_2 = 2L
    const val GENERIC_STRING = "GENERIC_STRING"
    val nowLocalDateTime = LocalDateTime.now().withNano(0)
    val nowLocalDate = LocalDate.now()

    val customVulnerabilityPojo = CustomVulnerability(
        id = ID_1,
        customerId = ID_1,
        juniferCustomVulnDfnId = ID_1,
        propertyId = ID_1,
        fromDate = nowLocalDate,
        toDate = nowLocalDate,
        name = GENERIC_STRING,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val customVulnerabilitySyncEntity = customVulnerabilityEntity {
        customerId = ID_1.toNullableInt64()
        juniferCustomVulnDfnId = ID_1.toNullableInt64()
        propertyId = ID_1.toNullableInt64()
        fromDate = nowLocalDate.toNullableTimestamp()
        toDate = nowLocalDate.toNullableTimestamp()
        syncTransactionId = ID_1.toNullableLongString()
        deleted = nullableTimestamp { }
        name = GENERIC_STRING.toNullableString()
    }

    val customerVulnerabilitySyncEntityRequestCreate = customVulnerabilityEntityRequest {
        operationType = OperationType.CREATE
        customVulnerabilityEntity = customVulnerabilitySyncEntity
    }

    val invalidCustomerVulnerabilitySyncEntityRequestCreate = customerVulnerabilitySyncEntityRequestCreate.copy {
        customVulnerabilityEntity =
            customVulnerabilitySyncEntity.copy { customerId = nullableInt64 { null_ = NullValue.NULL_VALUE } }
    }

    val customerVulnerabilitySyncEntityRequestPatch = customVulnerabilityEntityRequest {
        operationType = OperationType.PATCH
        customVulnerabilityEntity = customVulnerabilitySyncEntity.copy {
            id = ID_1.toNullableInt64()
            propertyId = ID_2.toNullableInt64()
        }
    }

    val invalidCustomerVulnerabilitySyncEntityRequestPatch = customerVulnerabilitySyncEntityRequestPatch.copy {
        customVulnerabilityEntity =
            customVulnerabilitySyncEntity.copy { id = nullableInt64 { null_ = NullValue.NULL_VALUE } }
    }

    val customerVulnerabilitySyncEntityRequestDelete = customVulnerabilityEntityRequest {
        operationType = OperationType.DELETE
        customVulnerabilityEntity = customVulnerabilitySyncEntity.copy { id = ID_1.toNullableInt64() }
    }

    val invalidCustomerVulnerabilitySyncEntityRequestDelete = customerVulnerabilitySyncEntityRequestDelete.copy {
        customVulnerabilityEntity =
            customVulnerabilitySyncEntity.copy { id = nullableInt64 { null_ = NullValue.NULL_VALUE } }
    }

    val syncStatusPojo = SyncStatu(
        id = ID_1,
        entityId = ID_1,
        transactionId = ID_1.toString(),
        tableName = CUSTOM_VULNERABILITY.name
    )

    val syncResponse = syncResponse {
        id = ID_1.toNullableInt64()
    }
}
