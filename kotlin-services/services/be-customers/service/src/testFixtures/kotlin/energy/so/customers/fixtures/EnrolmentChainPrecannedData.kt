package energy.so.customers.fixtures

import energy.so.commons.model.enums.EnrolmentType
import energy.so.core.customers.server.models.ConsumptionEstimation
import energy.so.core.customers.server.models.ConsumptionEstimationPeriod
import energy.so.core.customers.server.models.CustomerEnrolmentBroker
import energy.so.core.customers.server.models.EnrolmentAddress
import energy.so.core.customers.server.models.EnrolmentContactDetails
import energy.so.core.customers.server.models.EnrolmentModel
import energy.so.core.customers.server.models.EnrolmentPayload
import energy.so.core.customers.server.models.EnrolmentPaymentMethod
import energy.so.core.customers.server.models.EnrolmentProductDetails
import energy.so.core.customers.server.models.EnrolmentStatus
import energy.so.core.customers.server.models.Meter
import energy.so.core.customers.server.models.ProductType
import energy.so.customers.fixtures.BobsData.primaryContactEmail
import energy.so.customers.fixtures.CustomersData.ENROLMENT_ID
import java.time.LocalDate


const val VOUCHER = "voucher"
val bobsBirthday = LocalDate.of(2001, 10, 10)

val enrolmentSupplyAddress = EnrolmentAddress(
    address1 = "Some address",
    address2 = "Address 2",
    address3 = "Address 3",
    postcode = "N9 RFG",
    country = "GREAT_BRITAIN",
    careOf = "Care of",
    deleted = false
)

val bobsContactDetails = EnrolmentContactDetails(
    title = "Mr",
    firstName = BobsData.contactFirstname,
    lastName = BobsData.contactLastame,
    email = primaryContactEmail,
    birthdate = bobsBirthday,
    primaryContact = true,
    communicationMethod = "electronic",
    phone = "********9",
    contactAddress = EnrolmentAddress(
        address1 = "Contact address",
        address2 = "Contact Address 2",
        address3 = "Contact Address 3",
        postcode = "SE3 RFG",
        country = "GREAT_BRITAIN",
        careOf = "Contact Care of",
        deleted = false
    )
)

val bobsEnrolmentPayload = EnrolmentPayload(
    marketingOptedIn = true,
    quoteId = "123",
    extraAssistance = true,
    supplyAddress = enrolmentSupplyAddress,
    paymentMethod = EnrolmentPaymentMethod(
        type = "direct_debit",
        paymentDayOfMonth = 2,
        paymentAmount = "34565",
        bankAccountName = "Acc Name",
        bankAccountNumber = "********",
        bankAccountSortCode = "23-56-85",
        seasonalPayment = false,
        deleted = false
    ),
    contacts = setOf(
        bobsContactDetails
    ),
    products = setOf(
        EnrolmentProductDetails(
            code = "E2-RG-D",
            type = ProductType.ELECTRICITY,
            meters = setOf(
                Meter(
                    measurementType = "export",
                    identifier = "***************",
                )
            ),
            consumptionEstimation = ConsumptionEstimation(
                consumption = 45.3,
                estimatedCost = 346.0,
                period = ConsumptionEstimationPeriod.Month,
                e7DaytimeConsumptionProportion = 4.0,
            )
        ),
        EnrolmentProductDetails(
            code = "g2-RG-D",
            type = ProductType.GAS,
            meters = setOf(
                Meter(
                    measurementType = "export",
                    identifier = "***********",
                )
            ),
            consumptionEstimation = null
        )
    )
)

val bobsContactDetailsWithUnparsableTitle = bobsContactDetails.copy(title = "XY")
val bobsContactDetailsWithMissingFields = bobsContactDetails.copy(
    title = null,
    firstName = null,
    birthdate = null,
)

val bobsPendingEnrolmentModel = EnrolmentModel(
    id = ENROLMENT_ID,
    broker = CustomerEnrolmentBroker.SO_ENERGY,
    thirdPartySwitchReference = "3323",
    status = EnrolmentStatus.PENDING,
    type = EnrolmentType.NEW_ENROLMENT,
    payload = bobsEnrolmentPayload,
    email = primaryContactEmail,
    voucher = VOUCHER,
    supplyPostCode = "N9 RFG",
    mpans = listOf(
        "***************"
    ),
    mprns = listOf(
        "***********"
    )
)

val bobsFailedToCreateCoreAccountsEnrolmentModel = bobsPendingEnrolmentModel.copy(
    status = EnrolmentStatus.FAILED_TO_CREATE_CORE_ACCOUNTS
)
val bobsCreatedCoreAccountsEnrolmentModel = bobsPendingEnrolmentModel.copy(
    status = EnrolmentStatus.CREATED_CORE_ACCOUNTS,
    customerId = 789,
    billingAccountId = 11,
    juniferAccountId = "789",
    juniferCustomerId = "11"
)

val bobsAppliedVoucherEnrolmentModel = bobsCreatedCoreAccountsEnrolmentModel.copy(
    status = EnrolmentStatus.APPLIED_VOUCHER,
)

val bobsFailedInJuniferEnrolmentModel = bobsPendingEnrolmentModel.copy(
    status = EnrolmentStatus.FAILED_IN_JUNIFER
)

val bobsCreatedUserEnrolmentModel = bobsCreatedCoreAccountsEnrolmentModel.copy(
    status = EnrolmentStatus.CREATED_USER
)

val bobsSentPasswordResetEnrolmentModel = bobsCreatedCoreAccountsEnrolmentModel.copy(
    status = EnrolmentStatus.PASSWORD_RESET_EMAIL_SENT
)

