package energy.so.customers.fixtures

import energy.so.commons.exceptions.http.ClientHttpException
import energy.so.core.customers.server.models.Error
import energy.so.core.customers.server.models.GetTechnicalDetailsByMpanResponse
import energy.so.core.customers.server.models.Header
import energy.so.core.customers.server.models.MeterDetails
import energy.so.core.customers.server.models.Parameter
import energy.so.core.customers.server.models.ParameterSet
import energy.so.core.customers.server.models.Result
import energy.so.core.customers.server.models.UtilityMatch
import energy.so.customers.fixtures.BillingAccountData.ID_1
import energy.so.customers.v2.evtariff.checkSmartMeterEvTariffEligibilityRequest
import energy.so.customers.v2.evtariff.checkSmartMeterEvTariffEligibilityResponse
import energy.so.customers.v2.evtariff.checkVehicleIsEvRequest
import energy.so.customers.v2.evtariff.checkVehicleIsEvResponse
import io.ktor.http.HttpStatusCode
import kotlinx.datetime.LocalDate

object EvTariffPrecannedData {

    const val evRegistrationPlate = "AA19EEE"
    const val nonEvRegistrationPlate = "AA19AAA"
    const val validMeterPointIdentifier = "*********"
    const val notFoundMeterPointIdentifier = "*********"
    const val erroredMeterPointIdentifier = "*********"
    val protoCheckVehicleIsEvRequest = checkVehicleIsEvRequest {
        registrationPlate = evRegistrationPlate
    }
    val protoCheckSmartMeterEvTariffEligibilityRequest = checkSmartMeterEvTariffEligibilityRequest {
        meterPointIdentifier = validMeterPointIdentifier
    }

    val validCheckVehicleIsEvResponse = checkVehicleIsEvResponse {
        isEv = true
        fuelType = "HYBRID_ELECTRIC"
    }
    val notEvCheckVehicleIsEvResponse = checkVehicleIsEvResponse {
        isEv = false
        fuelType = "DIESEL"
    }

    val validMeterPointEvTariffEligibilityResponse = checkSmartMeterEvTariffEligibilityResponse {
        isEligible = true
    }

    val validTechnicalDetailsByMpanResponse = getTechnicalDetailsByMpanResponse("S1", "A", "01")
    val notSmartMeterTechnicalDetailsByMpanResponse = getTechnicalDetailsByMpanResponse("N")
    val notDccActiveTechnicalDetailsByMpanResponse = getTechnicalDetailsByMpanResponse("S1")
    val eco7TechnicalDetailsByMpanResponse = getTechnicalDetailsByMpanResponse("S1", "A", "02")

    val erroredTechnicalDetailsByMpanResponse = GetTechnicalDetailsByMpanResponse(
        Header(
            LocalDate.toString(),
            ID_1.toInt(),
            "281.3034",
            ID_1.toString()
        ), listOf(
            Result(
                listOf(
                    Error(
                        "CODE", "Description"
                    )
                ),
                ParameterSet(
                    listOf(
                        Parameter(
                            "MPAN",
                            validMeterPointIdentifier
                        )
                    )
                ),
                emptyList()
            )
        )
    )

    val noResultsTechnicalDetailsByMpanResponse = GetTechnicalDetailsByMpanResponse(
        Header(
            LocalDate.toString(),
            ID_1.toInt(),
            "281.3034",
            ID_1.toString()
        ), emptyList()
    )

    const val notFoundTechnicalDetailsByMpanResponse =
        """<Fault xmlns="http://schemas.microsoft.com/ws/2005/05/envelope/none">
                <Code>
                    <Value>Sender</Value>
                </Code>
                <Reason>
                    <Text xml:lang="en-GB">MPAN core was not found.</Text>
                </Reason>
                <Detail>
                    <MissingMPANFault xmlns="http://schemas.datacontract.org/2004/07/CandC.WSSUDS.SecurityModel.Faults" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                        <ErrorCode>DAT1002</ErrorCode>
                        <ErrorText>MPAN core was not found.</ErrorText>
                        <Variables xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
                    </MissingMPANFault>
                </Detail>
            </Fault>"""

    val badRequestHttpException = ClientHttpException(
        errorCategory = "errorCategory",
        errorCode = "errorCode",
        status = HttpStatusCode.BadRequest,
        message = "message"
    )


    private fun getTechnicalDetailsByMpanResponse(
        meterType: String? = null,
        dccServiceFlag: String? = null,
        profileClass: String? = null,
    ) = GetTechnicalDetailsByMpanResponse(
        Header(
            toString(),
            ID_1.toInt(),
            "281.3034",
            ID_1.toString()
        ), listOf(
            Result(
                emptyList(),
                ParameterSet(
                    listOf(
                        Parameter(
                            "MPAN",
                            validMeterPointIdentifier
                        )
                    )
                ),
                listOf(
                    UtilityMatch(
                        listOf(Parameter("dcc_service_flag", dccServiceFlag), Parameter("profile_class", profileClass)),
                        "UKey",
                        "UType",
                        listOf(MeterDetails(listOf(Parameter("meter_type", meterType))))
                    )
                )
            )
        )
    )
}
