package energy.so.customers.fixtures

import com.google.protobuf.NullValue
import energy.so.ac.junifer.v1.changeoftenancy.CreateChangeOfTenancyResponse
import energy.so.ac.junifer.v1.changeoftenancy.createChangeOfTenancyResponse
import energy.so.ac.junifer.v1.customers.enrolAdditionalAccountResponse
import energy.so.assets.meterPoints.v2.MeterPointType
import energy.so.assets.meterPoints.v2.UkProfileClass
import energy.so.assets.meterPoints.v2.getMPXNsByMeterPointIdsResponse
import energy.so.assets.meterPoints.v2.mPXN
import energy.so.assets.meterReadings.v2.MeterReadingUnitType
import energy.so.assets.meterReadings.v2.submitMeterReadingRequest
import energy.so.commons.NullableInt64
import energy.so.commons.NullableString
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.grpc.extensions.toNullableInt32
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.ChangeOfTenancyStatu
import energy.so.commons.model.enums.CotChannel
import energy.so.commons.model.enums.EnrolmentType
import energy.so.commons.model.enums.OccupantType
import energy.so.commons.model.tables.pojos.MoveInMeterReading
import energy.so.core.customers.server.models.ConsumptionEstimation
import energy.so.core.customers.server.models.ConsumptionEstimationPeriod
import energy.so.core.customers.server.models.Country
import energy.so.core.customers.server.models.CustomerEnrolmentBroker
import energy.so.core.customers.server.models.EnrolmentAddress
import energy.so.core.customers.server.models.EnrolmentContactDetails
import energy.so.core.customers.server.models.EnrolmentModel
import energy.so.core.customers.server.models.EnrolmentPayload
import energy.so.core.customers.server.models.EnrolmentPaymentMethod
import energy.so.core.customers.server.models.EnrolmentProductDetails
import energy.so.core.customers.server.models.EnrolmentStatus
import energy.so.core.customers.server.models.ForwardingAddress
import energy.so.core.customers.server.models.GetMoveOutChangeOfTenancyDto
import energy.so.core.customers.server.models.Meter
import energy.so.core.customers.server.models.MoveInChangeOfTenancy
import energy.so.core.customers.server.models.MoveInChangeOfTenancyRequest
import energy.so.core.customers.server.models.MoveOutChangeOfTenancy
import energy.so.core.customers.server.models.MoveOutChangeOfTenancyRequest
import energy.so.core.customers.server.models.NewOccupantContact
import energy.so.core.customers.server.models.PropertyMeterPoint
import energy.so.core.customers.server.services.MpxnProductRefMapping
import energy.so.core.customers.server.services.ProductRefs
import energy.so.core.customers.server.services.impl.ChangeOfTenancyFlexibleProductRefHelper.Companion.ProductDetail
import energy.so.core.customers.server.services.impl.JuniferChangeOfTenancyClient
import energy.so.core.customers.server.utils.sanitizePostcode
import energy.so.customers.changeoftenancy.v2.ChangeOfTenancyChannel
import energy.so.customers.changeoftenancy.v2.ChangeOfTenancyStatus
import energy.so.customers.changeoftenancy.v2.CountryCodeEnum
import energy.so.customers.changeoftenancy.v2.FuelType
import energy.so.customers.changeoftenancy.v2.NewOccupierDetails
import energy.so.customers.changeoftenancy.v2.OccupantTypeEnum
import energy.so.customers.changeoftenancy.v2.RateNameType
import energy.so.customers.changeoftenancy.v2.address
import energy.so.customers.changeoftenancy.v2.cancelMoveWithUsChangeOfTenancyRequest
import energy.so.customers.changeoftenancy.v2.contactDetails
import energy.so.customers.changeoftenancy.v2.copy
import energy.so.customers.changeoftenancy.v2.createMoveInChangeOfTenancyRequest
import energy.so.customers.changeoftenancy.v2.createMoveInChangeOfTenancyResponse
import energy.so.customers.changeoftenancy.v2.createMoveOutChangeOfTenancyRequest
import energy.so.customers.changeoftenancy.v2.directDebitDetails
import energy.so.customers.changeoftenancy.v2.directDebitInfo
import energy.so.customers.changeoftenancy.v2.forwardingAddress
import energy.so.customers.changeoftenancy.v2.moveInChangeOfTenancyData
import energy.so.customers.changeoftenancy.v2.moveInMeterReading
import energy.so.customers.changeoftenancy.v2.moveOutChangeOfTenancyData
import energy.so.customers.changeoftenancy.v2.moveWithUsChangeOfTenancy
import energy.so.customers.changeoftenancy.v2.moveWithUsChangeOfTenancyResponse
import energy.so.customers.changeoftenancy.v2.occupantContact
import energy.so.customers.changeoftenancy.v2.submitMoveWithUsChangeOfTenancyRequest
import energy.so.customers.changeoftenancy.v2.supplyAddress
import energy.so.customers.changeoftenancy.v2.updateMoveWithUsChangeOfTenancyRequest
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.Title
import energy.so.customers.fixtures.BillingAccountData.BILLING_ACCOUNT_ID
import energy.so.customers.fixtures.BobsData.firstName
import energy.so.customers.fixtures.BobsData.getCustomerProto
import energy.so.customers.fixtures.BobsData.lastName
import energy.so.customers.v2.consumptionEstimationRequest
import energy.so.customers.v2.productRequest
import energy.so.products.v2.GspGroup
import energy.so.products.v2.ProductType
import energy.so.products.v2.ProductVariantType
import java.time.LocalDate
import java.time.LocalDateTime

const val NEW_CUSTOMER_ID = 321L
const val NEW_ACCOUNT_ID = 3679L
const val JUNIFER_CHANGE_OF_TENANCY_ID = 21L
const val PHONE_NUMBER = "*********"
const val COT_BILLING_ACCOUNT_ID = 10L
val MOVE_IN_DATE = LocalDate.now().plusDays(5)
val DATE_OF_BIRTH = LocalDate.of(2000, 12, 12)
private const val POSTCODE = "NN156Jk"
const val TEST_EMAIL = "<EMAIL>"
const val AGENT_EMAIL = "<EMAIL>"
private const val OLD_BILLING_ACCOUNT_NUMBER = "********"
const val COT_MWU_CUSTOMER_ID = 1L
const val NEW_BILLING_ACCOUNT_NUMBER = "1234"
private const val CARE_OF = "Jane Doe"
private const val DEFAULT_STRING = "TEST"

val currentDate = LocalDate.now()

val contactDetailsMoveIn = contactDetails {
    marketingOptIn = true
    title = "Mrs".toNullableString()
    forename = "Bart".toNullableString()
    surname = "Barton"
    address = address {
        careOf = CARE_OF
        address1 = "23 Test Address"
        address2 = "Bulevard"
        postcode = POSTCODE
        countryCode = CountryCodeEnum.GB
    }
    email = TEST_EMAIL.toNullableString()
    phone1 = "***********"
    phone2 = "***********".toNullableString()
    phone3 = "***********".toNullableString()
    dateOfBirth = DATE_OF_BIRTH.toTimestamp()
    primaryContact = true
}

val dualFuelPropertyMeterPoint = listOf(
    PropertyMeterPoint(
        propertyId = 1,
        meterPointId = 412,
        meterPointType = MeterPointType.MPAN,
        ukProfileClass = UkProfileClass.DOMESTIC_UNRESTRICTED
    ),
    PropertyMeterPoint(
        propertyId = 1,
        meterPointId = 413,
        meterPointType = MeterPointType.MPRN,
        ukProfileClass = UkProfileClass.DOMESTIC_UNRESTRICTED
    )
)

val novaChangeOfTenancyMoveInRequest = createMoveInChangeOfTenancyRequest {
    contacts.addAll(listOf(contactDetailsMoveIn))
    firstDayOfResponsibility = MOVE_IN_DATE.toTimestamp()
    directDebitInfo = directDebitInfo {
        bankAccountSortCode = "********"
        bankAccountNumber = "121212"
        bankAccountName = "Bart Barton"
        regularPaymentAmount = 120
        regularPaymentDayOfMonth = 2
        seasonalPaymentFl = false
    }
    agentEmail = AGENT_EMAIL
    newOccupierDetails = NewOccupierDetails.Known
    accountNumber = OLD_BILLING_ACCOUNT_NUMBER
    electricityProductRef = "E8291821".toNullableString()
    gasProductRef = "G8392SO28".toNullableString()
    channel = ChangeOfTenancyChannel.NOVA
}

val moveInMeterReadingList = listOf(
    moveInMeterReading {
        meterIdentifier = "**********"
        meterPointId = 11111L
        meterReading = 11111L
        readingDate = LocalDateTime.now().toTimestamp()
        registerIdentifier = "L1".toNullableString()
        rateName = RateNameType.Day
        type = FuelType.Electricity
    },
    moveInMeterReading {
        meterIdentifier = "**********"
        meterPointId = 22222L
        meterReading = 22222L
        readingDate = LocalDateTime.now().toTimestamp()
        registerIdentifier = "L2".toNullableString()
        rateName = RateNameType.Night
        type = FuelType.Electricity
    },
    moveInMeterReading {
        meterIdentifier = "**********"
        meterPointId = 33333L
        meterReading = 33333L
        readingDate = LocalDateTime.now().toTimestamp()
        registerIdentifier = NullableString.getDefaultInstance()
        rateName = RateNameType.Standard
        type = FuelType.Gas
    }
)

val submitMoveInMeterReadingRequestList = listOf(
    submitMeterReadingRequest {
        digits.addAll(listOf("1", "1", "1", "1", "1"))
        units = 11111L
        readingAt = LocalDateTime.now().toTimestamp()
        meterIdentifier = "L1".toNullableString()
        meterPointId = 11111L
        registerIdentifier = "L1".toNullableString()
        unitType = MeterReadingUnitType.ELECTRICITY
        validateOnly = false
    },
    submitMeterReadingRequest {
        digits.addAll(listOf("2", "2", "2", "2", "2"))
        units = 22222L
        readingAt = LocalDateTime.now().toTimestamp()
        meterIdentifier = "**********".toNullableString()
        meterPointId = 22222L
        registerIdentifier = "L2".toNullableString()
        unitType = MeterReadingUnitType.ELECTRICITY_NIGHT
        validateOnly = false
    },
    submitMeterReadingRequest {
        digits.addAll(listOf("3", "3", "3", "3", "3"))
        units = 33333L
        readingAt = LocalDateTime.now().toTimestamp()
        meterIdentifier = "**********".toNullableString()
        meterPointId = 33333L
        registerIdentifier = NullableString.getDefaultInstance()
        unitType = MeterReadingUnitType.GAS
        validateOnly = false
    }
)

val myAccountChangeOfTenancyMoveInRequest = novaChangeOfTenancyMoveInRequest.copy {
    billingAccountId = BILLING_ACCOUNT_ID
    channel = ChangeOfTenancyChannel.MY_ACCOUNT
    clearAgentEmail()
    clearAccountNumber()
    meterReadings.addAll(moveInMeterReadingList)
}

val moveInMeterReadings = listOf(
    MoveInMeterReading(
        id = null,
        moveInChangeOfTenancyRequestId = 1001L,
        meterIdentifier = "1234567",
        meterPointId = 9001L,
        meterReading = 54321L,
        readingDate = LocalDate.of(2025, 7, 2),
        registerIdentifier = "L1",
        rateName = "Day",
        fuelType = "Electricity",
    ), MoveInMeterReading(
        id = null,
        moveInChangeOfTenancyRequestId = 1001L,
        meterIdentifier = "********556677",
        meterPointId = 9002L,
        meterReading = 12345L,
        readingDate = LocalDate.of(2025, 7, 3),
        registerIdentifier = "L2",
        rateName = "Night",
        fuelType = "Electricity",
    )
)

val changeOfTenancyMoveInResponse = createMoveInChangeOfTenancyResponse {
    accountNumber = NEW_BILLING_ACCOUNT_NUMBER
    contacts.add(contactDetailsMoveIn)
}


val forwardingAddressGrpc = forwardingAddress {
    mpan = "mpan".toNullableString()
    mprn = "mprn".toNullableString()
    addressLine1 = "23 Test Address"
    postcode = POSTCODE
    countryCode = "GB"
}

val newOccupantContactGrpc = occupantContact {
    type = OccupantTypeEnum.UNKNOWN
    name = "Bart Barton".toNullableString()
    email = TEST_EMAIL.toNullableString()
    phoneNumber = "***********".toNullableString()
}

val contactDetails = contactDetails {
    email = "<EMAIL>".toNullableString()
    title = "Mr".toNullableString()
    forename = "John".toNullableString()
    surname = "Doe"
    phone1 = "***********"
    phone2 = "***********".toNullableString()
    primaryContact = true
}

val validCreateChangeOfTenancyRequest = createMoveOutChangeOfTenancyRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    lastDateOfResponsibility = currentDate.toString()
    type = OccupantTypeEnum.UNKNOWN
    forwardingAddress = forwardingAddressGrpc
    newOccupantContact = newOccupantContactGrpc
    this.contacts.addAll(listOf(contactDetails))
    channel = "MY_ACCOUNT"
}

val validCreateChangeOfTenancyWithExitFeesRequest = createMoveOutChangeOfTenancyRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    lastDateOfResponsibility = currentDate.plusDays(20).toString()
    type = OccupantTypeEnum.UNKNOWN
    forwardingAddress = forwardingAddressGrpc
    newOccupantContact = newOccupantContactGrpc
    this.contacts.addAll(listOf(contactDetails))
    channel = "MY_ACCOUNT"
}

val validCreateChangeOfTenancyInCoolingOffPeriodRequest = createMoveOutChangeOfTenancyRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    lastDateOfResponsibility = currentDate.plusDays(7).toString()
    type = OccupantTypeEnum.UNKNOWN
    forwardingAddress = forwardingAddressGrpc
    newOccupantContact = newOccupantContactGrpc
    this.contacts.addAll(listOf(contactDetails))
    channel = "MY_ACCOUNT"
}

val validCreateChangeOfTenancyNovaRequest = createMoveOutChangeOfTenancyRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    lastDateOfResponsibility = currentDate.toString()
    type = OccupantTypeEnum.UNKNOWN
    forwardingAddress = forwardingAddressGrpc
    newOccupantContact = newOccupantContactGrpc
    this.contacts.addAll(listOf(contactDetails))
    channel = "NOVA"
    agentEmail = "<EMAIL>"
}

val newOccupantContact = NewOccupantContact(
    type = OccupantType.UNKNOWN,
    name = "Bart Barton",
    email = TEST_EMAIL,
    phoneNumber = "***********"
)

val newOccupantContactMoveIn = NewOccupantContact(
    type = OccupantType.UNKNOWN,
    name = "Bart Barton",
    email = TEST_EMAIL,
    phoneNumber = "***********",
    dateOfBirth = DATE_OF_BIRTH
)

val forwardingAddressDto = ForwardingAddress(
    mpan = "mpan",
    mprn = "mprn",
    addressLine1 = "23 Test Address",
    postcode = POSTCODE,
    countryCode = "GB",
)

val changeOfTenancyRequest = MoveOutChangeOfTenancyRequest(
    lastDateOfResponsibility = currentDate,
    type = OccupantType.UNKNOWN,
    forwardingAddress = forwardingAddressDto,
    newOccupantContact = newOccupantContact,
    billingAccountId = BillingAccountData.billingAccount.id!!
)

val changeOfTenancyRequestWithExitFees = MoveOutChangeOfTenancyRequest(
    lastDateOfResponsibility = currentDate.plusDays(20),
    type = OccupantType.UNKNOWN,
    forwardingAddress = forwardingAddressDto,
    newOccupantContact = newOccupantContact,
    billingAccountId = BillingAccountData.billingAccount.id!!
)

val changeOfTenancyRequestWithExitFeesInCoolingOffPeriod = MoveOutChangeOfTenancyRequest(
    lastDateOfResponsibility = currentDate.plusDays(7),
    type = OccupantType.UNKNOWN,
    forwardingAddress = forwardingAddressDto,
    newOccupantContact = newOccupantContact,
    billingAccountId = BillingAccountData.billingAccount.id!!
)


val moveInChangeOfTenancyRequest = MoveInChangeOfTenancyRequest(
    firstDateOfResponsibility = MOVE_IN_DATE,
    type = OccupantType.UNKNOWN,
    forwardingAddress = ForwardingAddress(
        addressLine1 = "23 Test Address",
        addressLine2 = "Bulevard",
        postcode = POSTCODE,
        countryCode = "GB",
    ),
    newOccupantContact = newOccupantContactMoveIn,
    billingAccountId = BillingAccountData.billingAccount.id!!
)

val pendingChangeOfTenancy = MoveOutChangeOfTenancy(
    billingAccountId = BILLING_ACCOUNT_ID,
    lastDateOfResponsibility = currentDate,
    status = ChangeOfTenancyStatu.PENDING,
    newOccupantContact = newOccupantContact,
    changeOfTenancyRequest = changeOfTenancyRequest,
    channel = CotChannel.MY_ACCOUNT,
    agentEmail = ""
)

val pendingChangeOfTenancyWithExitFees = MoveOutChangeOfTenancy(
    billingAccountId = BILLING_ACCOUNT_ID,
    lastDateOfResponsibility = currentDate.plusDays(20),
    status = ChangeOfTenancyStatu.PENDING,
    newOccupantContact = newOccupantContact,
    changeOfTenancyRequest = changeOfTenancyRequestWithExitFees,
    channel = CotChannel.MY_ACCOUNT,
    agentEmail = ""
)

val pendingChangeOfTenancyInCoolingOffPeriod = MoveOutChangeOfTenancy(
    billingAccountId = BILLING_ACCOUNT_ID,
    lastDateOfResponsibility = currentDate.plusDays(7),
    status = ChangeOfTenancyStatu.PENDING,
    newOccupantContact = newOccupantContact,
    changeOfTenancyRequest = changeOfTenancyRequestWithExitFeesInCoolingOffPeriod,
    channel = CotChannel.MY_ACCOUNT,
    agentEmail = ""
)

val pendingNovaChangeOfTenancy = MoveOutChangeOfTenancy(
    billingAccountId = BILLING_ACCOUNT_ID,
    lastDateOfResponsibility = currentDate,
    status = ChangeOfTenancyStatu.PENDING,
    newOccupantContact = newOccupantContact,
    changeOfTenancyRequest = changeOfTenancyRequest,
    channel = CotChannel.NOVA,
    agentEmail = "<EMAIL>"
)

val pendingNovaMoveInChangeOfTenancy = MoveInChangeOfTenancy(
    billingAccountId = BILLING_ACCOUNT_ID,
    firstDateOfResponsibility = MOVE_IN_DATE,
    status = ChangeOfTenancyStatu.PENDING,
    newOccupantContact = newOccupantContactMoveIn,
    changeOfTenancyRequest = moveInChangeOfTenancyRequest,
    channel = CotChannel.NOVA
)

val pendingChangeOfTenancySaved: MoveOutChangeOfTenancy = pendingChangeOfTenancy.copy(
    id = 1L,
    newOccupantContact = newOccupantContact.copy(id = 1L),
    changeOfTenancyRequest = changeOfTenancyRequest.copy(id = 1L)
)

val pendingChangeOfTenancyWithExitFeesSaved: MoveOutChangeOfTenancy = pendingChangeOfTenancyWithExitFees.copy(
    id = 1L,
    newOccupantContact = newOccupantContact.copy(id = 1L),
    changeOfTenancyRequest = changeOfTenancyRequestWithExitFees.copy(id = 1L)
)

val pendingChangeOfTenancyInCoolingOffPeriodSaved: MoveOutChangeOfTenancy =
    pendingChangeOfTenancyInCoolingOffPeriod.copy(
        id = 1L,
        newOccupantContact = newOccupantContact.copy(id = 1L),
        changeOfTenancyRequest = changeOfTenancyRequestWithExitFeesInCoolingOffPeriod.copy(id = 1L)
    )

val pendingNovaChangeOfTenancySaved: MoveOutChangeOfTenancy = pendingNovaChangeOfTenancy.copy(
    id = 1L,
    newOccupantContact = newOccupantContact.copy(id = 1L),
    changeOfTenancyRequest = changeOfTenancyRequest.copy(id = 1L)
)

val pendingNovaMoveInChangeOfTenancySaved: MoveInChangeOfTenancy = pendingNovaMoveInChangeOfTenancy.copy(
    id = 1L,
    newOccupantContact = newOccupantContactMoveIn.copy(id = 1L),
    changeOfTenancyRequest = moveInChangeOfTenancyRequest.copy(id = 1L),
    createdAt = LocalDateTime.now(),
)

const val ELEC_CODE = "TEST-ELEC-CODE"
const val GAS_CODE = "TEST-GAS-CODE"

val mpxnResponseElec412 = mPXN {
    id = 412
    type = MeterPointType.MPAN
    identifier = "MPAN412"
}
val mpxnResponseElec413 = mPXN {
    id = 413
    type = MeterPointType.MPAN
    identifier = "MPAN413"
}
val mpxnResponseGas414 = mPXN {
    id = 414
    type = MeterPointType.MPRN
    identifier = "MPRN414"
}
val mpxnResponseGas788 = mPXN {
    id = 788
    type = MeterPointType.MPRN
    identifier = "MPRN788"
}
val mpxnResponseGas789 = mPXN {
    id = 789
    type = MeterPointType.MPRN
    identifier = "MPRN789"
}

val multipleMpxnsResponse = getMPXNsByMeterPointIdsResponse {
    mPXNList.addAll(
        listOf(
            mpxnResponseElec412, mpxnResponseElec413, mpxnResponseGas414,
            mpxnResponseGas788, mpxnResponseGas789
        )
    )
}


val changeOfTenancyResponse = createChangeOfTenancyResponse {
    newCustomer = getCustomerProto
    newAccount = BobsData.billingAccountProto
    changeOfTenancyId = JUNIFER_CHANGE_OF_TENANCY_ID
}

fun createdMoveOutChangeOfTenancy(
    moveOutChangeOfTenancy: MoveOutChangeOfTenancy = pendingChangeOfTenancy,
    careOf: String = CARE_OF,
    response: CreateChangeOfTenancyResponse = changeOfTenancyResponse,
) = JuniferChangeOfTenancyClient.CreatedMoveOutChangeOfTenancy(
    moveOutChangeOfTenancy = moveOutChangeOfTenancy,
    careOf = careOf,
    response = response,
)

fun createdMoveOutChangeOfTenancyWithExitFees(
    moveOutChangeOfTenancy: MoveOutChangeOfTenancy = pendingChangeOfTenancyWithExitFees,
    careOf: String = CARE_OF,
    response: CreateChangeOfTenancyResponse = changeOfTenancyResponse,
) = JuniferChangeOfTenancyClient.CreatedMoveOutChangeOfTenancy(
    moveOutChangeOfTenancy = moveOutChangeOfTenancy,
    careOf = careOf,
    response = response,
)

fun createdMoveOutChangeOfTenancyInCoolingOffPeriod(
    moveOutChangeOfTenancy: MoveOutChangeOfTenancy = pendingChangeOfTenancyInCoolingOffPeriod,
    careOf: String = CARE_OF,
    response: CreateChangeOfTenancyResponse = changeOfTenancyResponse,
) = JuniferChangeOfTenancyClient.CreatedMoveOutChangeOfTenancy(
    moveOutChangeOfTenancy = moveOutChangeOfTenancy,
    careOf = careOf,
    response = response,
)

val changeOfTenancyDto = GetMoveOutChangeOfTenancyDto(
    accountId = COT_BILLING_ACCOUNT_ID,
    endDateOfResponsibility = LocalDate.of(2023, 10, 30),
    occupantType = OccupantType.LANDLORD,
    submittedDtm = LocalDateTime.of(2023, 9, 30, 9, 9, 9),
    cotStatus = ChangeOfTenancyStatu.PENDING,
    errorDescription = null,
    newAddress = forwardingAddressDto,
    nextOccupantContact = newOccupantContact
)


val changeOfTenancyDtoNoNewOccupierDetails = GetMoveOutChangeOfTenancyDto(
    accountId = COT_BILLING_ACCOUNT_ID,
    endDateOfResponsibility = LocalDate.of(2023, 10, 30),
    occupantType = OccupantType.LANDLORD,
    submittedDtm = LocalDateTime.of(2023, 9, 30, 9, 9, 9),
    cotStatus = ChangeOfTenancyStatu.PENDING,
    newAddress = forwardingAddressDto,
)

val moveOutChangeOfTenancyDataObject = moveOutChangeOfTenancyData {
    billingAccountId = COT_BILLING_ACCOUNT_ID
    lastDateOfResponsibility = LocalDate.of(2023, 10, 30).toTimestamp()
    type = OccupantTypeEnum.LANDLORD
    submittedAt = LocalDateTime.of(2023, 9, 30, 9, 9, 9).toTimestamp()
    forwardingAddress = forwardingAddressGrpc
    newOccupantContact = newOccupantContactGrpc
}

val moveInCotDTO: MoveInChangeOfTenancy = MoveInChangeOfTenancy(
    billingAccountId = COT_BILLING_ACCOUNT_ID,
    firstDateOfResponsibility = LocalDate.of(2023, 10, 30),
    status = ChangeOfTenancyStatu.SUCCESSFUL,
    channel = CotChannel.NOVA,
    newJuniferCustomerId = NEW_CUSTOMER_ID,
    newJuniferAccountId = NEW_ACCOUNT_ID,
    createdAt = LocalDateTime.of(2023, 9, 30, 9, 9, 9),
    changeOfTenancyRequest = moveInChangeOfTenancyRequest
)
val moveInChangeOfTenancyDataObject = moveInChangeOfTenancyData {
    billingAccountId = COT_BILLING_ACCOUNT_ID
    firstDateOfResponsibility = LocalDate.of(2023, 10, 30).toTimestamp()
    status = ChangeOfTenancyStatus.SUCCESSFUL
    newJuniferAccountId = NEW_ACCOUNT_ID.toNullableInt64()
    newJuniferCustomerId = NEW_CUSTOMER_ID.toNullableInt64()
    createdAt = LocalDateTime.of(2023, 9, 30, 9, 9, 9).toTimestamp()
    channel = ChangeOfTenancyChannel.NOVA
}


val elecProduct = productRequest {
    code = "elec-code"
    type = "ELECTRICITY"
    meterPointIdentifiers.addAll(listOf("1"))
    consumptionEstimation = consumptionEstimationRequest {
        consumption = "1.0"
        estimatedCost = "5.6".toNullableString()
        period = "YeAr"
        e7DaytimeConsumptionProportion = "5.6".toNullableString()
    }
}

val gasProduct = productRequest {
    code = "gas-code"
    type = "GAS"
    meterPointIdentifiers.addAll(listOf("2"))
    consumptionEstimation = consumptionEstimationRequest {
        consumption = "1.0"
        estimatedCost = "5.6".toNullableString()
        period = "MoNth"
    }
}

private const val addressLine = "1 Sun Street"
private const val bobsEmail = "bob@asd.c"
private const val bobsPostdode = "AL1 4ND"
private const val elecCode = "E1R-SOOC-TEST"
private const val gasCode = "G1R-SOOC-TEST"


val nullStringValue = NullableString.newBuilder().setNull(NullValue.NULL_VALUE).build()

val grpcSubmitMoveWithUsChangeOfTenancyRequest = submitMoveWithUsChangeOfTenancyRequest {
    billingAccountId = 123L
    supplyAddress = supplyAddress {
        addressLine1 = addressLine
        addressLine2 = nullStringValue
        addressLine3 = nullStringValue
        postcode = bobsPostdode
        countryCode = "GB"
        careOf = nullStringValue
    }
    directDebitDetails = directDebitDetails {
        bankAccountName = "Mr Bob".toNullableString()
        bankAccountSortCode = "12-34-56".toNullableString()
        bankAccountNumber = "********".toNullableString()
        paymentDayOfMonth = 25.toNullableInt32()
        paymentAmount = 5.2.toNullableDouble()
    }
    products.addAll(listOf(elecProduct, gasProduct))
    moveInDate = MOVE_IN_DATE.toTimestamp()
}

const val BANK_ACCOUNT_NAME = "Mr Bob"
const val BANK_ACCOUNT_NAME_ENCODED = "F+xNjv0Vf/Beb3Tohhg3nQ=="
const val BANK_ACCOUNT_NUMBER = "********"
const val BANK_ACCOUNT_NUMBER_ENCODED = "fsmjDAuXA57q2kmpHQ+2jg=="
const val BANK_ACCOUNT_SORT_CODE = "12-34-56"
const val BANK_ACCOUNT_SORT_CODE_ENCODED = "05ny6A9mGahqBB262ijSRw=="

val moveWithUsContactModel = Contact(
    id = 121L,
    firstName = firstName,
    lastName = lastName,
    email = "bob@asd.c",
    type = energy.so.commons.model.enums.ContactType.BUSINESS.toSiblingEnum(),
    title = energy.so.commons.model.enums.Title.MR.toSiblingEnum<Title>(),
)
val moveWithUsEnrolmentContactModel = moveWithUsContactModel.let {
    EnrolmentContactDetails(
        title = it.title.toString(),
        firstName = it.firstName,
        lastName = it.lastName,
        email = it.email,
        birthdate = it.dateOfBirth,
        primaryContact = true,
        phone = it.phoneNumber1 ?: it.phoneNumber2 ?: it.phoneNumber3,
        contactAddress = EnrolmentAddress(
            careOf = null,
            address1 = addressLine,
            address2 = null,
            address3 = null,
            postcode = sanitizePostcode(bobsPostdode),
            country = Country.fromCode("GB").name
        )
    )
}

val enrolmentModelFromMoveWithUsRequest = EnrolmentModel(
    broker = CustomerEnrolmentBroker.COT_MWU_SELF_SERVE,
    thirdPartySwitchReference = "COT_MWU_Self_Serve - ********-0000-0000-0000-************",
    status = EnrolmentStatus.PENDING,
    type = EnrolmentType.COT_MOVE_WITH_US,

    email = bobsEmail,
    supplyPostCode = bobsPostdode,
    mpans = listOf("1"),
    mprns = listOf("2"),
    retryCount = 0,
    moveInDate = MOVE_IN_DATE,
    oldBillingAccountId = 123L,
    customerId = COT_MWU_CUSTOMER_ID,

    payload = EnrolmentPayload(
        marketingOptedIn = false,
        extraAssistance = false,
        supplyAddress = EnrolmentAddress(
            careOf = null,
            address1 = addressLine,
            address2 = null,
            address3 = null,
            postcode = bobsPostdode,
            country = "GREAT_BRITAIN",
        ),
        contacts = setOf(moveWithUsEnrolmentContactModel),
        paymentMethod = EnrolmentPaymentMethod(
            type = "direct_debit",
            bankAccountName = BANK_ACCOUNT_NAME_ENCODED,
            bankAccountNumber = BANK_ACCOUNT_NUMBER_ENCODED,
            bankAccountSortCode = BANK_ACCOUNT_SORT_CODE_ENCODED,
            paymentDayOfMonth = 25,
            paymentAmount = "5.2",
            seasonalPayment = false,
            deleted = null,
        ),
        products = setOf(
            EnrolmentProductDetails(
                code = "elec-code",
                type = energy.so.core.customers.server.models.ProductType.ELECTRICITY,
                meters = setOf(
                    Meter(
                        identifier = "1",
                    )
                ),
                consumptionEstimation = ConsumptionEstimation(
                    consumption = 1.0,
                    estimatedCost = 5.6,
                    e7DaytimeConsumptionProportion = 5.6,
                    period = ConsumptionEstimationPeriod.Year,
                )
            ),
            EnrolmentProductDetails(
                code = "gas-code",
                type = energy.so.core.customers.server.models.ProductType.GAS,
                meters = setOf(
                    Meter(
                        identifier = "2",
                    )
                ),
                consumptionEstimation = ConsumptionEstimation(
                    consumption = 1.0,
                    estimatedCost = 5.6,
                    period = ConsumptionEstimationPeriod.Month,
                )
            )
        )
    )
)

val unEncodedPaymentMethod = EnrolmentPaymentMethod(
    type = "direct_debit",
    bankAccountName = BANK_ACCOUNT_NAME,
    bankAccountNumber = BANK_ACCOUNT_NUMBER,
    bankAccountSortCode = BANK_ACCOUNT_SORT_CODE,
    paymentDayOfMonth = 25,
    paymentAmount = "5.2",
    seasonalPayment = false,
    deleted = null,
)

val unEncodedMwuPayload = enrolmentModelFromMoveWithUsRequest.payload.copy(paymentMethod = unEncodedPaymentMethod)

val unEnCodedMwuEnrolmentModel = enrolmentModelFromMoveWithUsRequest.copy(
    payload = unEncodedMwuPayload
)

val enrolAdditionalAccountResponse = enrolAdditionalAccountResponse {
    account = BobsData.billingAccountProto
}


val getMoveWithUsChangeOfTenancyResponse = moveWithUsChangeOfTenancyResponse {
    mwuChangeOfTenancy = moveWithUsChangeOfTenancy {
        enrolmentId = 456L
        status = enrolmentModelFromMoveWithUsRequest.status.name
        moveInDate = enrolmentModelFromMoveWithUsRequest.moveInDate!!.toTimestamp()
        newSupplyAddress = supplyAddress {
            addressLine1 = addressLine
            addressLine2 = nullValue.toNullableString()
            addressLine3 = nullValue.toNullableString()
            postcode = bobsPostdode
            countryCode = "GB"
            careOf = nullValue.toNullableString()
        }
    }
}

val emptyGetMoveWithUsChangeOfTenancyResponse = moveWithUsChangeOfTenancyResponse {}

val updateMoveWithUsCotRequest = updateMoveWithUsChangeOfTenancyRequest {
    billingAccountId = 1L
    moveInDate = MOVE_IN_DATE.toTimestamp()
}

val cancelMoveWithUsCotRequest = cancelMoveWithUsChangeOfTenancyRequest {
    billingAccountId = 1L
}

val productRefsWithMpxnProductMap = ProductRefs(
    elecProductRef = elecCode,
    gasProductRef = gasCode,
    mpxnProductMap = mapOf(
        Pair(
            ProductType.ELECTRICITY_PRODUCT,
            listOf(
                MpxnProductRefMapping(
                    mpxnIdentifier = "MPAN412",
                    productRef = elecCode,
                    type = ProductType.ELECTRICITY_PRODUCT,
                ),
                MpxnProductRefMapping(
                    mpxnIdentifier = "MPAN413",
                    productRef = elecCode,
                    type = ProductType.ELECTRICITY_PRODUCT,
                )
            )
        ),
        Pair(
            ProductType.GAS_PRODUCT,
            listOf(
                MpxnProductRefMapping(
                    mpxnIdentifier = "MPRN414",
                    productRef = gasCode,
                    type = ProductType.GAS_PRODUCT
                ),
                MpxnProductRefMapping(
                    mpxnIdentifier = "MPRN788",
                    productRef = gasCode,
                    type = ProductType.GAS_PRODUCT
                ),
                MpxnProductRefMapping(
                    mpxnIdentifier = "MPRN789",
                    productRef = gasCode,
                    type = ProductType.GAS_PRODUCT
                )
            )
        )
    )
)

val productRefs = ProductRefs(
    elecProductRef = ELEC_CODE,
    gasProductRef = GAS_CODE,
    mpxnProductMap = null,
)

val productDetail = ProductDetail(
    productVariantId = 1L,
    code = "code",
    type = ProductType.GAS_PRODUCT,
    fuel = ProductVariantType.GAS_SINGLE,
    gspGroup = GspGroup.UNRECOGNIZED,
    earlyTerminationCharge = 75.0
)
