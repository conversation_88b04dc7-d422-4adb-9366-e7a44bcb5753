package energy.so.customers.fixtures

import com.google.protobuf.Timestamp
import energy.so.ac.junifer.v1.accounts.createAccountTicketRequest
import energy.so.ac.junifer.v1.accounts.createAccountTicketResponse
import energy.so.aes.client.v1.models.AppointmentType
import energy.so.aes.client.v1.models.BebocAvailabilityRequest
import energy.so.aes.client.v1.models.BebocAvailabilityResponse
import energy.so.aes.client.v1.models.BebocAvailabilityResult
import energy.so.aes.client.v1.models.BebocAvailabilitySlot
import energy.so.aes.client.v1.models.BebocError
import energy.so.aes.client.v1.models.BookJobDataErrors
import energy.so.aes.client.v1.models.BookJobNumberResponse
import energy.so.aes.client.v1.models.BookJobResponse
import energy.so.aes.client.v1.models.CancelJobRequest
import energy.so.aes.client.v1.models.CancelJobResponse
import energy.so.aes.client.v1.models.ReserveSlotAESRequest
import energy.so.aes.client.v1.models.ReserveSlotAESResponse
import energy.so.aes.client.v1.models.ReserveSlotSuccess
import energy.so.aes.client.v1.models.ReserveSlotsEvent
import energy.so.aes.client.v1.models.ResponseStatus
import energy.so.aes.client.v1.models.UpdateJobErrors
import energy.so.aes.client.v1.models.UpdateJobResponse
import energy.so.aes.client.v1.models.beboc.BebocContact
import energy.so.aes.client.v1.models.beboc.BebocCustomer
import energy.so.aes.client.v1.models.beboc.BebocJobType
import energy.so.aes.client.v1.models.beboc.BebocSite
import energy.so.aes.client.v1.models.beboc.BookImmediateResult
import energy.so.aes.client.v1.models.beboc.BookReservationAESRequest
import energy.so.aes.client.v1.models.beboc.BookReservationAESResponse
import energy.so.aes.client.v1.models.beboc.ElectricMeterDetails
import energy.so.aes.client.v1.models.beboc.GasMeterDetails
import energy.so.aes.client.v1.models.beboc.MeterDetails
import energy.so.assets.meter.v2.meterInfoAcc
import energy.so.assets.meter.v2.MeterPointType.MPAN
import energy.so.assets.meter.v2.MeterPointType.MPRN
import energy.so.assets.meterPoints.v2.DccServiceStatus
import energy.so.assets.meterPoints.v2.MeasurementType
import energy.so.assets.meterPoints.v2.MeterPointElectricity
import energy.so.assets.meterPoints.v2.MeterPointGas
import energy.so.assets.meterPoints.v2.MeterPointHistory
import energy.so.assets.meterPoints.v2.MeterPointType
import energy.so.assets.meterPoints.v2.OperationType
import energy.so.assets.meterPoints.v2.UkProfileClass
import energy.so.assets.meterPoints.v2.meterPoint
import energy.so.assets.meterPoints.v2.meterPointResponse
import energy.so.assets.properties.v2.PropertyType
import energy.so.assets.properties.v2.address
import energy.so.assets.properties.v2.property
import energy.so.commons.NullableTimestamp
import energy.so.commons.exceptions.dto.CauseDto
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.enums.FuelType
import energy.so.commons.model.enums.InstallationType.TRADITIONAL
import energy.so.commons.model.enums.MeterType
import energy.so.commons.model.enums.ProductAccountType
import energy.so.commons.model.enums.ReadingSubmissionFrequency
import energy.so.commons.model.enums.SmartMeterBookingSource
import energy.so.commons.model.enums.SmsStatu
import energy.so.commons.model.tables.pojos.CustomerPropertyRel
import energy.so.commons.model.tables.pojos.ProductAccountAgreementRel
import energy.so.commons.model.tables.pojos.SmartMeterBooking
import energy.so.commons.model.tables.pojos.SmartMeterBookingReservation
import energy.so.commons.model.tables.pojos.SmartMeterBookingSmsReminder
import energy.so.commons.model.tables.pojos.SmartMeterBookingsHistory
import energy.so.commons.model.tables.pojos.SmartMeterRegisterInterest
import energy.so.commons.model.tables.pojos.SqsSurveyData
import energy.so.commons.queues.models.QueueMessage
import energy.so.commons.v2.dtos.idRequestStr
import energy.so.communications.v1.sendSMSRequest
import energy.so.communications.v1.sendSMSResponse
import energy.so.core.customers.server.exceptions.AESException
import energy.so.core.customers.server.exceptions.AESInvalidArgumentException
import energy.so.core.customers.server.exceptions.SmartMeterCampaignNotFoundException
import energy.so.core.customers.server.exceptions.UnknownSmartMeterCampaignException
import energy.so.core.customers.server.extensions.toJooqEntity
import energy.so.core.customers.server.grpc.SmartMeterGrpcService
import energy.so.core.customers.server.models.BillingAccount
import energy.so.core.customers.server.models.BillingAccountClass
import energy.so.core.customers.server.models.BillingAccountType
import energy.so.core.customers.server.models.CreateRubyBookingRequest
import energy.so.customers.client.models.Customer
import energy.so.customers.client.models.CustomerClass
import energy.so.customers.client.models.CustomerMetadata
import energy.so.customers.client.models.CustomerType
import energy.so.core.customers.server.models.DESC_CANCELLATION
import energy.so.core.customers.server.models.DESC_CRITICAL_FAILURE
import energy.so.core.customers.server.models.DESC_NON_CRITICAL_FAILURE
import energy.so.core.customers.server.models.DESC_SUCCESS
import energy.so.core.customers.server.models.EventLogDto
import energy.so.core.customers.server.models.ProductAccount
import energy.so.core.customers.server.models.STEP_CODE_CANCELLATION
import energy.so.core.customers.server.models.STEP_CODE_CRITICAL_FAILURE
import energy.so.core.customers.server.models.STEP_CODE_NON_CRITICAL_FAILURE
import energy.so.core.customers.server.models.STEP_CODE_SUCCESS
import energy.so.core.customers.server.models.smartMeter.AesBebocStatusUpdate
import energy.so.core.customers.server.models.smartMeter.BebocQuestion
import energy.so.core.customers.server.models.smartMeter.BebocSurvey
import energy.so.core.customers.server.models.smartMeter.RegisteredInterestCsv
import energy.so.core.customers.server.models.smartMeter.RegisteredInterestResponse
import energy.so.core.customers.server.models.smartMeter.SMBookingHistory
import energy.so.core.customers.server.models.smartMeter.SMBookingHistoryResponse
import energy.so.core.customers.server.models.smartMeter.SMBookingSurvey
import energy.so.core.customers.server.models.smartMeter.SMHistory
import energy.so.customers.client.models.BillingAccountSettings
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.ContactType
import energy.so.customers.fixtures.TestDataUtils.localDateTime
import energy.so.customers.fixtures.TestDataUtils.offsetDateTime
import energy.so.customers.v2.smartmeter.AvailableSlotsRequest
import energy.so.customers.v2.smartmeter.AvailableSlotsResponse
import energy.so.customers.v2.smartmeter.BookReservationRequest
import energy.so.customers.v2.smartmeter.CancelBookingRequest
import energy.so.customers.v2.smartmeter.CancelBookingResponse
import energy.so.customers.v2.smartmeter.CreateBookingResponse
import energy.so.customers.v2.smartmeter.FuseLocation
import energy.so.customers.v2.smartmeter.InstallationType
import energy.so.customers.v2.smartmeter.ReserveEvent
import energy.so.customers.v2.smartmeter.ReserveSlotRequest
import energy.so.customers.v2.smartmeter.ReserveSlotResponse
import energy.so.customers.v2.smartmeter.Slot
import energy.so.customers.v2.smartmeter.SlotDay
import energy.so.customers.v2.smartmeter.amendBookingPortalCampaignRequest
import energy.so.customers.v2.smartmeter.availableSlotsRequest
import energy.so.customers.v2.smartmeter.availableSlotsResponse
import energy.so.customers.v2.smartmeter.bookReservationRequest
import energy.so.customers.v2.smartmeter.campaignAccountDetails
import energy.so.customers.v2.smartmeter.campaignAccountsList
import energy.so.customers.v2.smartmeter.cancelBookingRequest
import energy.so.customers.v2.smartmeter.cancelBookingResponse
import energy.so.customers.v2.smartmeter.copy
import energy.so.customers.v2.smartmeter.createBookingRequest
import energy.so.customers.v2.smartmeter.createBookingResponse
import energy.so.customers.v2.smartmeter.createCampaignRequest
import energy.so.customers.v2.smartmeter.createCampaignsFromListRequest
import energy.so.customers.v2.smartmeter.getBookingsResponse
import energy.so.customers.v2.smartmeter.reserveEvent
import energy.so.customers.v2.smartmeter.reserveSlotRequest
import energy.so.customers.v2.smartmeter.reserveSlotResponse
import energy.so.customers.v2.smartmeter.slot
import energy.so.customers.v2.smartmeter.slotDay
import energy.so.customers.v2.smartmeter.smtMeterBooking
import energy.so.customers.v2.smartmeter.updateBookingRequest
import energy.so.customers.v2.smartmeter.updateCampaignRequest
import energy.so.customers.v2.smartmeter.updateCampaignsFromListRequest
import energy.so.tickets.v2.updateTicketStepRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import org.jooq.JSON

object SmartMeterCannedData {

    const val JUNIFER_ID = 1234L
    const val JUNIFER_ID_STR = "1234"
    const val JOB_NUMBER = 563445L
    const val JOB_NUMBER_STR = "563445"
    private const val ACCOUNT_NUMBER = "734"
    private const val PATCH_AVAILABILITY_ID = 123123L
    const val AES_EXCEPTION_MESSAGE = "AES Error"
    private const val CUSTOMER_EMAIL = "<EMAIL>"
    const val CUSTOMER_FIRST_NAME = "Bob"
    const val CUSTOMER_LAST_NAME = "Data"
    const val CUSTOMER_NAME = "Bob Data"
    private const val CUSTOMER_OUTCODE = "AA1"

    val genericAESException = AESException(AES_EXCEPTION_MESSAGE)
    val genericAESInvalidArgumentException = AESInvalidArgumentException(AES_EXCEPTION_MESSAGE)

    private val firstSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 12, 8, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 12, 12, 0).toTimestamp()
        timeslot = "8am - 12pm"
    }

    private val secondSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 12, 9, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 12, 13, 0).toTimestamp()
        timeslot = "9am - 1pm"
    }

    private val thirdSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 24, 8, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 24, 12, 0).toTimestamp()
        timeslot = "8am - 12pm"
    }

    private val fourthSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 24, 9, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 24, 13, 0).toTimestamp()
        timeslot = "9am - 1pm"
    }

    private val provisionalSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 11, 11, 8, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 11, 11, 12, 0).toTimestamp()
    }

    private val provisionalSlotDay: SlotDay = slotDay {
        date = LocalDateTime.of(
            2023, 11, 11,
            8, 0, 0, 0
        ).toTimestamp()
        slots.addAll(listOf(provisionalSlot))
        provisional = true
    }

    private val firstSlotDay: SlotDay = slotDay {
        date = LocalDateTime.of(
            2023, 12, 12,
            0, 0, 0, 0
        ).toTimestamp()
        slots.addAll(listOf(firstSlot, secondSlot))
        provisional = false
    }

    private val secondSlotDay: SlotDay = slotDay {
        date = LocalDateTime.of(
            2023, 12, 24,
            0, 0, 0, 0
        ).toTimestamp()
        slots.addAll(listOf(thirdSlot, fourthSlot))
        provisional = false
    }

    val cannedAvailableSlotsRequest: AvailableSlotsRequest = availableSlotsRequest {
        postcode = "HA3 9AB"
        startDate = LocalDateTime.of(
            2023, 12, 12,
            12, 0, 0, 0
        ).toTimestamp()
        endDate = LocalDateTime.of(
            2023, 12, 24,
            12, 0, 0, 0
        ).toTimestamp()
    }

    val cannedAvailableSlotsResponse: AvailableSlotsResponse = availableSlotsResponse {
        days.addAll(listOf(firstSlotDay, secondSlotDay))
        bookingType = "SMBP"
    }

    val cannedProvisionalAvailableSlotsResponse: AvailableSlotsResponse = availableSlotsResponse {
        days.addAll(listOf(provisionalSlotDay))
        bookingType = "SMBP"
    }

    val cannedAESAndProvisionalAvailableSlotsResponse: AvailableSlotsResponse = availableSlotsResponse {
        days.addAll(listOf(firstSlotDay, secondSlotDay, provisionalSlotDay))
        bookingType = "SMBP"
    }

    val provisionalSlots: List<SlotDay> =
        listOf(
            slotDay {
                date = LocalDateTime.of(
                    2023, 11, 11,
                    8, 0, 0, 0
                ).toTimestamp()
                provisional = true
                slots.add(slot {
                    startTime = LocalDateTime.of(
                        2023, 11, 11,
                        8, 0, 0, 0
                    ).toTimestamp()
                    endTime = LocalDateTime.of(
                        2023, 11, 11,
                        12, 0, 0, 0
                    ).toTimestamp()
                })
            }
        )

    val aCancelBookingRequest: CancelBookingRequest = cancelBookingRequest {
        jobNumber = "1"
        cancellationReason = "Customer Requested"
        cancellationReasonId = "1"
        sendConfirmationEmail = true
        billingAccountId = 1
    }

    val aCancelJobRequest: CancelJobRequest = CancelJobRequest(
        jobNumber = "1",
        cancellationReasonId = "1",
        cancellationReason = "Customer Requested",
        sendConfirmationEmail = true

    )

    val cancelBookingUpdateCampaignRequest = updateCampaignRequest {
        billingAccountId = 1
        cancellationEmailSent = true
    }

    val aCancelBookingResponse: CancelBookingResponse = cancelBookingResponse {
        statusCode = 200
        message = "Success"
        responseStatus = ResponseStatus.SUCCESS.toString()
    }

    val aSuccessfulCancelJobResponse: CancelJobResponse = CancelJobResponse(
        status = ResponseStatus.SUCCESS,
        message = "Success",
        statusCode = 200
    )

    val aFailedCancelJobResponse: CancelJobResponse = CancelJobResponse(
        status = ResponseStatus.FAIL,
        message = "test",
        statusCode = 500
    )

    val createBookingRequest = createBookingRequest {
        billingAccountId = JUNIFER_ID.toString()
        customerFirstName = CUSTOMER_FIRST_NAME
        customerLastName = CUSTOMER_LAST_NAME
        customerAddress = "12 Kent Rd"
        customerCity = "Glasgow"
        customerPostcode = "G3 7BY"
        customerEmail = CUSTOMER_EMAIL
        customerPhone = "************"
        mpan = ""
        mprn = "981273"
        gasMeterSerialNumber = "987213"
        electricityMeterSerialNumber = ""
        bookingSource = ""
        customerId = "10101"
        medicalEquipment = true
        timeSlot = "8am - 12pm"
        slotStartDateTime = LocalDateTime.of(2024, 4, 12, 9, 0, 0, 0).toTimestamp()
        slotEndDateTime = LocalDateTime.of(2024, 4, 12, 13, 0, 0, 0).toTimestamp()
        appointmentType = "NEW_CONNECTION"
    }

    val createBookingRequestWithAgentEmail = createBookingRequest.copy {
        agentEmail = "<EMAIL>"
    }

    val createBookingIMBPRequest = createBookingRequest {
        billingAccountId = JUNIFER_ID.toString()
        customerFirstName = CUSTOMER_FIRST_NAME
        customerLastName = CUSTOMER_LAST_NAME
        customerAddress = "12 Kent Rd"
        customerCity = "Glasgow"
        customerPostcode = "G3 7BY"
        customerEmail = CUSTOMER_EMAIL
        customerPhone = "************"
        mpan = ""
        mprn = "981273"
        gasMeterSerialNumber = "987213"
        electricityMeterSerialNumber = ""
        bookingSource = ""
        customerId = "10101"
        timeSlot = "8am - 12pm"
    }

    val createBookingUpdateCampaignRequest = updateCampaignRequest {
        billingAccountId = JUNIFER_ID
        bookedAppointment = true
    }

    val cannedAesBookingResponse = BookJobResponse(
        statusCode = 200,
        status = ResponseStatus.SUCCESS,
        data = BookJobNumberResponse(
            jobNumber = "987"
        ),
        message = "Appointment booked"
    )

    val cannedAesBookingErrorResponse422 = BookJobResponse(
        statusCode = 422,
        status = ResponseStatus.FAIL,
        message = "Selected date is no longer available.",
        errors = BookJobDataErrors(jobType = listOf("error message"))
    )

    val cannedAesBookingErrorResponse500 = BookJobResponse(
        statusCode = 500,
        status = ResponseStatus.FAIL,
        message = "Error in booking appointment"
    )

    val cannedCreateBookingResponse: CreateBookingResponse =
        createBookingResponse {
            jobNumber = "9876"
        }

    val grpcAesCreateEvent = EventLogDto(
        event = EventType.SM_CREATE_GRPC,
        billingAccountId = JUNIFER_ID.toString(),
        description = createBookingRequest.toString()
    )

    val grpcAesCreateWithAgentEmailEvent = EventLogDto(
        event = EventType.SM_CREATE_GRPC,
        billingAccountId = JUNIFER_ID.toString(),
        description = createBookingRequestWithAgentEmail.toString()
    )

    val grpcAesCreateInvalidArgumentErrorEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.SM_CREATE_ERROR,
        isError = true,
        description = genericAESInvalidArgumentException.message
    )
    val grpcAesCreateErrorEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.SM_CREATE_ERROR,
        isError = true,
        description = genericAESException.message
    )
    val grpcAesCreateResponseEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_CREATE_RESPONSE,
        description = cannedAesBookingResponse.toString()
    )
    val grpcAesCreateError422ResponseEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_CREATE_RESPONSE,
        description = cannedAesBookingErrorResponse422.toString()
    )
    val grpcAesCreateError500ResponseEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_CREATE_RESPONSE,
        description = cannedAesBookingErrorResponse500.toString()
    )

    fun grpcCreateJuniferTicketResponse(desc: String) = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.JUNIFER_SM_BOOK_TICKET_RESPONSE,
        description = desc
    )

    val juniferSmartMeterBookingCreateTicketResponse = createAccountTicketResponse {
        id = 654
    }
    val juniferSmartMeterBookingCreateTicketRequest = createAccountTicketRequest {
        accountId = JUNIFER_ID.toString()
        ticketDefinitionCode = "SmartMeterBooking"
        ticketStepCode = "SMART-BOOKING-CREATED"
        summary = "Smart meter installation booked successfully"
    }
    val acJuniferSmartMeterBookingCreateTicketRequest = createAccountTicketRequest {
        accountId = JUNIFER_ID.toString()
        ticketDefinitionCode = "SmartMeterBooking"
        ticketStepCode = "SMART-BOOKING-CREATED"
        summary = "Smart meter installation booked successfully"
    }

    val grpcAesCreateGenericErrorEvent = EventLogDto(
        event = EventType.SM_CREATE_ERROR,
        billingAccountId = JUNIFER_ID.toString(),
        isError = true,
        description = "Unexpected GRPC Error"
    )

    val grpcUpdateBookingRequest = updateBookingRequest {
        billingAccountId = JUNIFER_ID
        jobNumber = JOB_NUMBER_STR
        installationType = InstallationType.SMETS2
        patchAvailabilityId = PATCH_AVAILABILITY_ID
        mainFuseLocation = FuseLocation.OUTSIDE
        fuseBoxAccess = FuseLocation.FREELY_ACCESSIBLE
        councilAssociation = "council message"
        slotStartDateTime = LocalDateTime.of(12, 12, 12, 9, 0).toTimestamp()
        slotEndDateTime = LocalDateTime.of(12, 12, 12, 13, 0).toTimestamp()
        timeSlot = "8am - 12pm"
    }

    val grpcUpdateEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.SM_UPDATE_GRPC,
        description = "SmartMeterGrpcService-updateBooking-called",
    )
    val grpcUpdateAesErrorEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_UPDATE_ERROR,
        description = genericAESException.message,
        isError = true
    )
    val grpcUpdateAesInvalidArgumentEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_UPDATE_ERROR,
        description = genericAESInvalidArgumentException.message,
        isError = true
    )
    val grpcUpdateUnknownErrorEvent = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.SM_UPDATE_ERROR,
        description = "Generic caught exception",
        isError = true
    )
    val aesStatusUpdateGprc = EventLogDto(
        event = EventType.AES_STATUS_UPDATE_GPRC,
        description = "SmartMeterGrpcService-aesStatusUpdate-called"
    )
    val aesStatusUpdateFailed = EventLogDto(
        event = EventType.AES_STATUS_UPDATE_FAILED,
        isError = true,
        description = "dummy"
    )
    val getBookingsByAccIdGprc = EventLogDto(
        event = EventType.SM_GET_BOOKINGS_BY_ACCOUNT_GRPC,
        description = "SmartMeterGrpcService-getBookingsByAccountId-called 5678"
    )
    val getBookingsByAccIdFailed = EventLogDto(
        event = EventType.SM_GET_BOOKINGS_BY_ACCOUNT_FAILED,
        isError = true,
        description = "dummy"
    )

    val unsubscribeEvent = EventLogDto(
        event = EventType.UNSUBSCRIBE_USER_GRPC,
        description = "SmartMeterGrpcService-unsubscribeUser-called"
    )

    val unsubscribeEventError = EventLogDto(
        event = EventType.RUBY_REMOVE_ACC_FROM_CAMPAIGN_FAILED,
        description = "test",
        isError = true
    )
    val aesUpdateBookingResponse200 = UpdateJobResponse(
        message = "valid response",
        status = ResponseStatus.SUCCESS,
        statusCode = 200,
    )
    val aesUpdateResponse200Event = EventLogDto(
        billingAccountId = JUNIFER_ID.toString(),
        event = EventType.AES_UPDATE_RESPONSE,
        description = aesUpdateBookingResponse200.toString()
    )
    val aesUpdateBookingResponse422 = UpdateJobResponse(
        message = AES_EXCEPTION_MESSAGE,
        status = ResponseStatus.FAIL,
        statusCode = 422,
        errors = UpdateJobErrors(jobNumber = listOf("errored"))
    )
    val aesUpdateResponse422Event = aesUpdateResponse200Event.copy(
        description = aesUpdateBookingResponse422.toString()
    )
    val aesUpdateBookingResponse400 = UpdateJobResponse(
        message = "invalid response",
        status = ResponseStatus.FAIL,
        statusCode = 404,
        errors = UpdateJobErrors()
    )
    val aesUpdateResponse400Event = aesUpdateResponse200Event.copy(
        description = aesUpdateBookingResponse400.toString()
    )

    val smartMeterBooking = SmartMeterBooking(
        id = 3L,
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0),
        billingAccountId = JUNIFER_ID.toString(),
        customerName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
        customerEmail = CUSTOMER_EMAIL,
        customerPhone = "************",
        address = "3 Kent Road, Glasgow",
        postcode = "G3 7BY",
        mpan = "23HJ12123K123",
        mprn = null,
        gasMeterSerialNumber = "************",
        electricityMeterSerialNumber = null,
        aesJobNumber = JOB_NUMBER.toString(),
        aesPatchAvailabilityId = "23456",
        cancellationDateTime = null,
        meterReadingFrequency = ReadingSubmissionFrequency.DAILY,
        medicalEquipment = false,
        previousTicketStepCode = "",
        fuelType = FuelType.DUAL,
        source = SmartMeterBookingSource.CAMPAIGN_EMAIL,
        createdAt = offsetDateTime,
        updatedAt = localDateTime,
        juniferTicketId = "101",
        customerId = 10101L,
        timeslot = "9am - 12pm"
    )

    val smartMeterBookingIMBP = SmartMeterBooking(
        id = 3L,
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0),
        billingAccountId = JUNIFER_ID.toString(),
        customerName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
        customerEmail = CUSTOMER_EMAIL,
        customerPhone = "************",
        address = "3 Kent Road, Glasgow",
        postcode = "G3 7BY",
        mpan = "23HJ12123K123",
        mprn = null,
        gasMeterSerialNumber = "************",
        electricityMeterSerialNumber = null,
        aesJobNumber = JOB_NUMBER.toString(),
        aesPatchAvailabilityId = "23456",
        cancellationDateTime = null,
        meterReadingFrequency = ReadingSubmissionFrequency.DAILY,
        medicalEquipment = false,
        previousTicketStepCode = "",
        fuelType = FuelType.DUAL,
        source = SmartMeterBookingSource.CAMPAIGN_EMAIL,
        createdAt = offsetDateTime,
        updatedAt = localDateTime,
        juniferTicketId = "101",
        customerId = 10101L,
        meterType = MeterType.DUAL,
        appointmentType = "Meter Exchange",
        installationType = energy.so.commons.model.enums.InstallationType.SMETS2,
        timeslot = "9am - 12pm"
    )

    val cancelledSmartMeterBooking = SmartMeterBooking(
        id = 4L,
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0),
        billingAccountId = JUNIFER_ID.toString(),
        customerName = CUSTOMER_FIRST_NAME,
        customerEmail = CUSTOMER_EMAIL,
        customerPhone = "************",
        address = "3 Kent Road, Glasgow",
        postcode = "G3 7BY",
        mpan = "23HJ12123K123",
        mprn = null,
        gasMeterSerialNumber = "************",
        electricityMeterSerialNumber = null,
        aesJobNumber = JOB_NUMBER.toString(),
        aesPatchAvailabilityId = "23456",
        cancellationDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        meterReadingFrequency = ReadingSubmissionFrequency.DAILY,
        medicalEquipment = false,
        previousTicketStepCode = "",
        fuelType = FuelType.DUAL,
        source = SmartMeterBookingSource.CAMPAIGN_EMAIL,
        createdAt = offsetDateTime,
        updatedAt = localDateTime,
        juniferTicketId = "101"
    )

    val completedSmartMeterBooking = SmartMeterBooking(
        id = 4L,
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0),
        billingAccountId = JUNIFER_ID.toString(),
        customerName = CUSTOMER_FIRST_NAME,
        customerEmail = CUSTOMER_EMAIL,
        customerPhone = "************",
        address = "3 Kent Road, Glasgow",
        postcode = "G3 7BY",
        mpan = "23HJ12123K123",
        mprn = null,
        gasMeterSerialNumber = "************",
        electricityMeterSerialNumber = null,
        aesJobNumber = JOB_NUMBER.toString(),
        aesPatchAvailabilityId = "23456",
        cancellationDateTime = null,
        meterReadingFrequency = ReadingSubmissionFrequency.DAILY,
        medicalEquipment = false,
        previousTicketStepCode = "",
        fuelType = FuelType.DUAL,
        source = SmartMeterBookingSource.CAMPAIGN_EMAIL,
        createdAt = offsetDateTime,
        updatedAt = localDateTime,
        juniferTicketId = "101",
        installedDatetime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
    )

    val aSmtMeterBooking = smtMeterBooking {
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0).toTimestamp()
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0).toTimestamp()
        billingAccountId = JUNIFER_ID.toString()
        customerName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME"
        customerEmail = CUSTOMER_EMAIL
        customerPhone = "************"
        address = "3 Kent Road, Glasgow"
        postcode = "G3 7BY"
        mpan = "23HJ12123K123"
        mprn = ""
        gasMeterSerialNumber = "************"
        electricityMeterSerialNumber = ""
        aesJobNumber = JOB_NUMBER.toString()
        aesPatchAvailabilityId = "23456"
        meterReadingFrequency = "DAILY"
        juniferTicketId = "101"
        fuelType = energy.so.customers.v2.smartmeter.FuelType.DUAL
        source = "CAMPAIGN_EMAIL"
        createdAt = offsetDateTime.toTimestamp()
        updatedAt = localDateTime.toTimestamp()
        installedDatetime = Timestamp.getDefaultInstance()
        failedDatetime = Timestamp.getDefaultInstance()
        cancellationDateTime = Timestamp.getDefaultInstance()
        previousTicketStepCode = ""
        aesJobType = ""
        aesStatus = "booked"
        appointmentType = ""
        meterType = ""
        installationType = ""
        timeSlot = "9am - 12pm"
        appointmentId = 3
    }

    val aSmtMeterBookingIMBP = aSmtMeterBooking.copy {
        appointmentType = "Meter Exchange"
        meterType = "Dual"
        installationType = "SMETS2"
    }

    val aGetBookingsResponse = getBookingsResponse {
        bookings.add(aSmtMeterBooking)
    }

    val aGetBookingsIMBPResponse = getBookingsResponse {
        bookings.add(aSmtMeterBookingIMBP)
    }

    val aesStatusUpdateSuccess =
        EventLogDto(event = EventType.AES_STATUS_UPDATE_SUCCESS, description = smartMeterBooking.toString())

    val aesStatusUpdateCriticalFailure =
        EventLogDto(event = EventType.AES_STATUS_UPDATE_CRITICAL_FAILURE, description = smartMeterBooking.toString())

    val aesStatusUpdateRescheduled =
        EventLogDto(
            event = EventType.AES_STATUS_UPDATE_RESCHEDULED,
            description = smartMeterBooking.toString()
        )

    val aesStatusUpdateCancellation =
        EventLogDto(event = EventType.AES_STATUS_UPDATE_CANCELLATION, description = smartMeterBooking.toString())

    val aesStatusUpdateUnexpectedStatus =
        EventLogDto(event = EventType.AES_STATUS_UPDATE_UNEXPECTED_STATUS, description = "")

    val aTicketUpdateRequest = updateTicketStepRequest {
        ticketId = 101
        summary = "SMBP Cancellation"
        description = "Booking cancelled by user."
        ticketStepCode = "SMART-BOOKING-CANCEL"
    }

    val smbpBookingQueueEmailMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "booking",
            "slot_length" to "4",
            "portal_url" to "url",
            "job_id" to JOB_NUMBER.toString(),
            "SMBP_type" to "booking",
            "type" to "confirmation"
        )
    )

    val smbpBookingQueueSmsMessageConfirmation = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "booking",
            "type" to "booking",
            "job_id" to JOB_NUMBER_STR
        )
    )

    val smbpBookingQueueSmsMessageReminder = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "reminder",
            "type" to "reminder",
            "job_id" to JOB_NUMBER_STR
        )
    )
    val smbpCampaignQueueMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "campaign",
            "SMBP_type" to "campaign",
            "portal_url" to "url"
        )
    )
    val anSMBPQueueEmailMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "confirmation",
            "slot_length" to "4",
            "portal_url" to "url",
            "job_id" to "563445",
            "SMBP_type" to "booking",
            "type" to "confirmation"
        )
    )

    val anSMBPQueueEmailCancellationMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "cancellation",
            "slot_length" to "4",
            "portal_url" to "url",
            "job_id" to "563445",
            "SMBP_type" to "booking",
            "type" to "confirmation"
        )
    )

    val anSMBPQueueSmsMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "confirmation",
            "slot_length" to "4",
            "portal_url" to "url",
            "job_id" to "563445",
            "SMBP_type" to "booking",
            "type" to "reminder"
        )
    )

    val anSMBPQueueInvalidMessage = QueueMessage(
        "1234", null, mapOf(
            "template_name" to "booking",
            "slot_length" to "4",
            "portal_url" to "url",
            "job_id" to "563445",
            "SMBP_type" to "booking",
            "type" to "nope"
        )
    )

    val juniferTicketAesStatusUpdateSuccess = updateTicketStepRequest {
        ticketId = 101
        ticketStepCode = STEP_CODE_SUCCESS
        description = DESC_SUCCESS
    }

    val juniferTicketAesStatusUpdateCriticalFailure = updateTicketStepRequest {
        ticketId = 101
        ticketStepCode = STEP_CODE_CRITICAL_FAILURE
        description = DESC_CRITICAL_FAILURE
    }

    val juniferTicketAesStatusUpdateNonCriticalFailure = updateTicketStepRequest {
        ticketId = 101
        ticketStepCode = STEP_CODE_NON_CRITICAL_FAILURE
        description = DESC_NON_CRITICAL_FAILURE
    }

    val juniferTicketAesStatusUpdateCancellation = updateTicketStepRequest {
        ticketId = 101
        ticketStepCode = STEP_CODE_CANCELLATION
        description = DESC_CANCELLATION
    }

    val aCreateRubyBookingRequest: CreateRubyBookingRequest = CreateRubyBookingRequest(
        meter_reading_frequency = "DAILY",
        date = "2024-04-12",
        junifer_account_number = JUNIFER_ID.toString(),
        customer_id = "10101",
        fuel_type = "2",
        installer = "aes",
        job_type = "2",
        customer_address = "12 Kent Rd",
        customer_postcode = "G3 7BY",
        customer_email = CUSTOMER_EMAIL,
        customer_phone = "************",
        customer_fullname = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
        needs_medical_equipment = "true",
        mprn = "981273",
        gas_meter_serial_number = "987213",
        imbp_booking = "false",
        job_number = "987",
        current_slot = "09:00",
        journey_start_date_time = "2024-04-12T09:00",
        appointment_type = "5",
        install_type = "3",
        first_name = "Bob",
        last_name = "Data",
        customer_city = "Glasgow",
        county = "Glasgow",
        booked = "true"
    )

    // Smart Meter Campaigns
    val createSmartMeterCampaignRequest = createCampaignRequest {
        billingAccountId = JUNIFER_ID
        eligible = true
        customerName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME"
        customerEmail = CUSTOMER_EMAIL
        outcode = CUSTOMER_OUTCODE
        accessedBookingPortal = true
    }
    val createSmartMeterCampaignRequestJooqEntity = createSmartMeterCampaignRequest.toJooqEntity()

    val createCampaignTicketRequestResponse = createAccountTicketResponse { id = 1111 }
    val updateRecordJuniferTicketIdRequest = updateCampaignRequest {
        billingAccountId = JUNIFER_ID
        juniferTicketId = 1111
    }

    val abandonedCartSmartMeterCampaignJooqEntity = createSmartMeterCampaignRequest.toJooqEntity()
        .copy(
            billingAccountId = "1111",
            bookedAppointment = false,
            accessedBookingPortalDate = LocalDateTime.now().minusDays(5)
        )
    val createCampaignEventLog = EventLogDto(
        event = EventType.SM_CREATE_CAMPAIGN_GRPC,
        description = "SmartMeterGrpcService-createCampaign called"
    )
    val createCampaignErrorEventLog = EventLogDto(
        event = EventType.SM_CREATE_CAMPAIGN_ERROR,
        description = "SmartMeterGrpcService-createCampaign threw exception",
        isError = true,
    )

    val updateSmartMeterCampaignRequest = updateCampaignRequest {
        billingAccountId = JUNIFER_ID
    }
    val updateCampaignEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGN_GRPC,
        description = "SmartMeterGrpcService-updateCampaign called"
    )
    val updateCampaignDatabaseErrorEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGN_DATABASE_ERROR,
        description = "SmartMeterGrpcService-updateCampaign failed finding campaign for $JUNIFER_ID",
        isError = true,
    )
    val updateCampaignUnknownErrorEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGN_ERROR,
        description = "SmartMeterGrpcService-updateCampaign threw exception",
        isError = true,
    )

    val amendSmartMeterBookingPortalCampaignRequest = amendBookingPortalCampaignRequest {
        billingAccountId = JUNIFER_ID
        customerEmail = CUSTOMER_EMAIL
        customerName = CUSTOMER_FIRST_NAME
        outcode = CUSTOMER_OUTCODE
        accessedBookingPortal = true
        source = "source"
    }
    val updateSmartMeterBookingPortalCampaignRequest = updateCampaignRequest {
        billingAccountId = JUNIFER_ID
        customerEmail = CUSTOMER_EMAIL
        customerName = CUSTOMER_FIRST_NAME
        outcode = CUSTOMER_OUTCODE
        accessedBookingPortal = true
    }
    val amendSmartMeterBookingPortalCampaignEventLog = EventLogDto(
        event = EventType.SM_AMEND_BOOKING_PORTAL_CAMPAIGN_GRPC,
        description = "SmartMeterGrpcService-amendBookingPortalCampaign called via source"
    )
    val amendSmartMeterBookingPortalCampaignErrorEventLog = EventLogDto(
        event = EventType.SM_AMEND_BOOKING_PORTAL_CAMPAIGN_ERROR,
        description = "SmartMeterGrpcService-amendBookingPortalCampaign threw exception",
        isError = true,
    )

    val validAccountNumberList = listOf(1111L, 2222L, 3333L, 4444L)
    val validCampaignAccountsList = campaignAccountsList {
        accountNumber.addAll(validAccountNumberList)
        accountNumber.addAll(validAccountNumberList)
        accountNumber.addAll(validAccountNumberList)
    }
    val grpcCampaignAccountEventLog = EventLogDto(
        event = EventType.SM_GET_CAMPAIGN_ACCOUNTS_GRPC,
        description = "SmartMeterGrpcService-getCampaignAccounts-called"
    )
    val grpcCampaignAccountBigQueryErrorLog = EventLogDto(
        event = EventType.SM_GET_CAMPAIGN_ACCOUNTS_BIG_QUERY_ERROR,
        description = "AES Data not updated on BigQuery for ${LocalDate.now()}",
        isError = true
    )
    val grpcCampaignAccountGenericErrorLog = EventLogDto(
        event = EventType.SM_GET_CAMPAIGN_ACCOUNTS_ERROR,
        description = "Unexpected GRPC Error",
        isError = true
    )

    private val campaignAccountDetailsList = listOf(
        campaignAccountDetails {
            billingAccountId = JUNIFER_ID
            customerEmail = CUSTOMER_EMAIL
            customerName = CUSTOMER_NAME
        },
        campaignAccountDetails {
            billingAccountId = 4444L
            customerEmail = CUSTOMER_EMAIL
            customerName = CUSTOMER_NAME
        }
    )
    val updateCampaignsFromListRequest = updateCampaignsFromListRequest {
        accounts.addAll(campaignAccountDetailsList)
        eligible = true
        active = true
    }
    val createCampaignsFromListRequest = createCampaignsFromListRequest {
        accounts.addAll(campaignAccountDetailsList)
        eligible = true
    }
    val createCampaignsFromListGrpcEventLog = EventLogDto(
        event = EventType.SM_CREATE_CAMPAIGNS_FROM_LIST_GRPC,
        description = "SmartMeterGrpcService-getCampaignAccounts-called"
    )
    val genericUnknownSmartMeterCampaignException = UnknownSmartMeterCampaignException(
        message = "Generic Error Throw",
        errors = listOf(
            CauseDto("message", "SMBP", "422")
        )
    )
    val createCampaignsFromListInternalErrorEventLog = EventLogDto(
        event = EventType.SM_CREATE_CAMPAIGNS_FROM_LIST_ERROR,
        description = genericUnknownSmartMeterCampaignException.message,
        isError = true
    )
    val createCampaignsFromListGenericExceptionEventLog = EventLogDto(
        event = EventType.SM_CREATE_CAMPAIGNS_FROM_LIST_ERROR,
        description = SmartMeterGrpcService.UNKNOWN_GRPC_ERROR,
        isError = true
    )
    val updateCampaignsFromListGrpcEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGNS_FROM_LIST_GRPC,
        description = "SmartMeterGrpcService-getCampaignAccounts-called"
    )
    val genericSmartMeterCampaignNotFoundException = SmartMeterCampaignNotFoundException(
        errors = listOf(
            CauseDto("message", "SMBP", "422")
        )
    )
    val updateCampaignsFromListInternalExceptionEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGNS_FROM_LIST_ERROR,
        description = genericSmartMeterCampaignNotFoundException.message,
        isError = true
    )
    val updateCampaignsFromListGenericExceptionEventLog = EventLogDto(
        event = EventType.SM_UPDATE_CAMPAIGNS_FROM_LIST_ERROR,
        description = SmartMeterGrpcService.UNKNOWN_GRPC_ERROR,
        isError = true
    )

    // SMS Reminder Service
    val anSmsReminderRecord = SmartMeterBookingSmsReminder(
        bookingId = 1,
        firstSmsDate = LocalDateTime.of(2012, 12, 12, 12, 12, 12),
        secondSmsDate = LocalDateTime.of(2012, 12, 12, 12, 12, 12),
        firstSmsStatus = SmsStatu.pending,
        secondSmsStatus = SmsStatu.pending,
    )

    val aSendSMSResponse = sendSMSResponse {
        twilioSmsId = "1"
        internalSmsId = "1"
    }

    val aMockUpcomingBookingsList = listOf(
        smartMeterBooking,
        smartMeterBooking.copy(id = 4L),
        smartMeterBooking.copy(id = 5L, cancellationDateTime = LocalDateTime.of(2012, 12, 12, 12, 12, 12, 12)),
        smartMeterBooking.copy(id = 6L)
    )

    val firstMockReminder = SmartMeterBookingSmsReminder(
        bookingId = 3L,
        firstSmsDate = LocalDateTime.of(2023, 11, 11, 11, 11, 11),
        firstSmsStatus = SmsStatu.pending,
        cancelledDatetime = null
    )

    val secondMockReminder = SmartMeterBookingSmsReminder(
        bookingId = 4L,
        firstSmsDate = LocalDateTime.of(2021, 12, 12, 12, 12, 12),
        firstSmsStatus = SmsStatu.pending,
        secondSmsDate = LocalDateTime.of(2023, 11, 11, 11, 11, 11),
        secondSmsStatus = SmsStatu.pending,
        cancelledDatetime = null
    )

    val thirdMockReminder = SmartMeterBookingSmsReminder(
        bookingId = 5L,
        cancelledDatetime = LocalDateTime.of(2012, 12, 12, 12, 12, 12)
    )

    val firstExpectedSmsRequest = sendSMSRequest {
        templateName = "first"
        recipientName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME"
        recipientPhone = "************"
        customAttributes.putAll(
            mapOf(
                "name" to "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
                "appointment_number" to "563445",
                "date" to "2023-10-10",
                "time" to "09:00",
                "reschedule_link" to "link?jobNumber=563445"
            )
        )
    }

    val secondExpectedSmsRequest = sendSMSRequest {
        templateName = "second"
        recipientName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME"
        recipientPhone = "************"
        customAttributes.putAll(
            mapOf(
                "name" to "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
                "appointment_number" to "563445",
                "date" to "2023-10-10",
                "time" to "09:00",
                "reschedule_link" to "link?jobNumber=563445"
            )
        )
    }

    val aBebocAvailabilityRequest: BebocAvailabilityRequest = BebocAvailabilityRequest(
        start = LocalDate.of(2023, 12, 12),
        end = LocalDate.of(2023, 12, 24),
        postcode = "HA3 9AB",
        supplierConstant = "SO_ENERGY"
    )

    val aSuccessfulBebocAvailabilityResponse: BebocAvailabilityResponse = BebocAvailabilityResponse(
        result = listOf(
            BebocAvailabilityResult(
                day = LocalDate.of(2023, 12, 12),
                slots = listOf(
                    BebocAvailabilitySlot(
                        availableUnits = 1,
                        timeSlot = "8am - 12pm",
                        startTime = LocalTime.of(8, 0, 0),
                        endTime = LocalTime.of(12, 0, 0)
                    ),
                    BebocAvailabilitySlot(
                        availableUnits = 1,
                        timeSlot = "9am - 1pm",
                        startTime = LocalTime.of(9, 0, 0),
                        endTime = LocalTime.of(13, 0, 0)
                    ),
                    BebocAvailabilitySlot(
                        availableUnits = 0,
                        timeSlot = "3pm - 7pm",
                        startTime = LocalTime.of(15, 0, 0),
                        endTime = LocalTime.of(19, 0, 0)
                    )
                )
            ),
            BebocAvailabilityResult(
                day = LocalDate.of(2023, 12, 24),
                slots = listOf(
                    BebocAvailabilitySlot(
                        availableUnits = 5,
                        timeSlot = "8am - 12pm",
                        startTime = LocalTime.of(8, 0, 0),
                        endTime = LocalTime.of(12, 0, 0)
                    ),
                    BebocAvailabilitySlot(
                        availableUnits = 6,
                        timeSlot = "9am - 1pm",
                        startTime = LocalTime.of(9, 0, 0),
                        endTime = LocalTime.of(13, 0, 0)
                    )
                )
            )
        )
    )

    val aSuccessfulBebocNoAvailabilityResponse: BebocAvailabilityResponse = BebocAvailabilityResponse(
        result = emptyList()
    )

    val aFailedBebocAvailabilityResponse: BebocAvailabilityResponse = BebocAvailabilityResponse(
        error = BebocError(
            code = 500,
            message = "test",
            description = "test"
        )
    )

    private val firstBebocSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 12, 8, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 12, 12, 0).toTimestamp()
        timeslot = "8am - 12pm"
    }

    private val secondBebocSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 12, 9, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 12, 13, 0).toTimestamp()
        timeslot = "9am - 1pm"

    }

    private val thirdBebocSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 24, 8, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 24, 12, 0).toTimestamp()
        timeslot = "8am - 12pm"

    }

    private val fourthBebocSlot: Slot = slot {
        startTime = LocalDateTime.of(2023, 12, 24, 9, 0).toTimestamp()
        endTime = LocalDateTime.of(2023, 12, 24, 13, 0).toTimestamp()
        timeslot = "9am - 1pm"

    }

    private val firstBebocSlotDay: SlotDay = slotDay {
        date = LocalDateTime.of(
            2023, 12, 12,
            0, 0, 0, 0
        ).toTimestamp()
        slots.addAll(listOf(firstBebocSlot, secondBebocSlot))
        provisional = false
    }

    private val secondBebocSlotDay: SlotDay = slotDay {
        date = LocalDateTime.of(
            2023, 12, 24,
            0, 0, 0, 0
        ).toTimestamp()
        slots.addAll(listOf(thirdBebocSlot, fourthBebocSlot))
        provisional = false
    }

    val bebocExpectedSlotDays: AvailableSlotsResponse = availableSlotsResponse {
        days.addAll(listOf(firstBebocSlotDay, secondBebocSlotDay))
        bookingType = "SMBP"
    }

    val mockReserveSlotRequest: ReserveSlotRequest = reserveSlotRequest {
        postcode = "HA1 9AB"
        timeslot = "8am - 12pm"
        day = LocalDate.of(2023, 12, 12).toTimestamp()
        meterType = energy.so.customers.v2.smartmeter.FuelType.DUAL
        appointmentType = "NEW_CONNECTION"
        installType = InstallationType.SMETS2
    }

    val mockReserveEvent: ReserveEvent = reserveEvent {
        id = "4"
        deleted = false
        type = "SCHEDULED_EXCHANGE"
        user = "test"
        status = "Completed"
        createdOn = LocalDateTime.of(2023, 12, 12, 12, 12, 12).toTimestamp()
        updatedAt = LocalDateTime.of(2023, 12, 13, 0, 0, 0).toTimestamp()
    }

    val mockReserveSlotResponse: ReserveSlotResponse = reserveSlotResponse {
        deleted = false
        outwardCode = "HA1"
        jobType = "DUAL FUEL"
        appointmentType = "SCHEDULED_EXCHANGE"
        editedBy = ""
        status = "Completed"
        id = "1"
        events.add(mockReserveEvent)

    }

    val mockReserveSlotAESSuccessResponse: ReserveSlotAESResponse = ReserveSlotAESResponse(
        result = ReserveSlotSuccess(
            deleted = false,
            outwardCode = "HA1",
            jobType = "DUAL FUEL",
            appointmentType = "SCHEDULED_EXCHANGE",
            editedBy = "",
            events = listOf(
                ReserveSlotsEvent(
                    id = "4",
                    deleted = false,
                    type = "SCHEDULED_EXCHANGE",
                    user = "test",
                    status = "Completed",
                    createdOn = LocalDateTime.of(2023, 12, 12, 12, 12, 12),
                    updatedOn = LocalDateTime.of(2023, 12, 13, 0, 0, 0)
                )
            ),
            status = "Completed",
            id = "1"
        )
    )

    val mockReserveSlotAESErrorResponse: ReserveSlotAESResponse = ReserveSlotAESResponse(
        error = BebocError(
            code = 400,
            description = "test",
            message = "test"
        )
    )

    val mockReserveSlotAESRequest: ReserveSlotAESRequest = ReserveSlotAESRequest(
        supplier = "SO_ENERGY",
        postcode = "HA1 9AB",
        day = LocalDate.of(2023, 12, 12),
        timeSlot = ("8am - 12pm"),
        appointmentType = AppointmentType.NEW_CONNECTION.bebocValue,
        jobType = BebocJobType.DUAL_FUEL_SMETS_2
    )

    val mockBookReservationRequest: BookReservationRequest = bookReservationRequest {
        reservationId = "reservationId"
        customerFirstName = "Johnny"
        customerLastName = "McTest"
        customerAddress = "16 Test Ave"
        customerCity = "Testton"
        customerPostcode = "TS1 8AJ"
        customerPhone = "***********"
        customerEmail = "<EMAIL>"
        mpan = "123"
        electricMeterSerialNumber = "0285543"
        mprn = "456"
        gasMeterSerialNumber = "*********"
        accountId = "1234"
    }

    val mockBookReservationAESRequest: BookReservationAESRequest = BookReservationAESRequest(
        reservationId = "reservationId",
        customer = BebocCustomer(
            firstName = "Johnny",
            surname = "McTest",
            customerNumber = "1234",
            flexible = false,
            accountType = "credit",
            vulnerabilities = emptyList(),
            accountNumber = "1234"
        ),
        site = BebocSite(
            address1 = "16 Test Ave",
            address2 = "",
            address3 = "",
            city = "Testton",
            county = ""
        ),
        contact = BebocContact(
            firstName = "Johnny",
            surname = "McTest",
            customerNumber = "NotRequired",
            contactSms = "***********",
            contactTelephone = "***********",
            contactEmail = "<EMAIL>",
            preferredContact = "mobile"
        ),
        meterDetails = MeterDetails(
            gas = GasMeterDetails(
                mprn = mockBookReservationRequest.mprn,
                meterSerialNumber = mockBookReservationRequest.gasMeterSerialNumber
            ),
            electric = ElectricMeterDetails(
                mpan = mockBookReservationRequest.mpan,
                meterSerialNumber = mockBookReservationRequest.electricMeterSerialNumber
            )
        )
    )

    val mockBookReservationAESSuccessResponse: BookReservationAESResponse = BookReservationAESResponse(
        result = BookImmediateResult(
            bookingReference = "2468"
        )
    )

    val mockBookReservationAESFailureResponse: BookReservationAESResponse = BookReservationAESResponse(
        error = BebocError(
            code = 500,
            message = "bad",
            description = "news"
        )
    )

    val mockBookReservationResponse: CreateBookingResponse = createBookingResponse {
        jobNumber = "2468"
    }

    val mockSmartMeterBookingReservation: SmartMeterBookingReservation = SmartMeterBookingReservation(
        postcode = "HA1 9AB",
        day = LocalDate.of(2023, 12, 12),
        timeslot = "8am - 12pm",
        installtype = energy.so.commons.model.enums.InstallationType.SMETS2,
        appointmenttype = "NEW_CONNECTION",
        metertype = MeterType.DUAL,
        reservationid = "1"
    )

    val firstSmartMeterBookingHistoryRecord = SmartMeterBookingsHistory(
        id = 3L,
        slotStartDateTime = LocalDateTime.of(2023, 10, 10, 9, 0, 0),
        slotEndDateTime = LocalDateTime.of(2023, 10, 10, 12, 0, 0),
        billingAccountId = JUNIFER_ID.toString(),
        customerName = "$CUSTOMER_FIRST_NAME $CUSTOMER_LAST_NAME",
        customerEmail = CUSTOMER_EMAIL,
        customerPhone = "************",
        address = "3 Kent Road, Glasgow",
        postcode = "G3 7BY",
        mpan = "23HJ12123K123",
        mprn = null,
        gasMeterSerialNumber = "************",
        electricityMeterSerialNumber = null,
        aesJobNumber = JOB_NUMBER.toString(),
        aesPatchAvailabilityId = "23456",
        cancellationDateTime = null,
        meterReadingFrequency = ReadingSubmissionFrequency.DAILY,
        medicalEquipment = false,
        previousTicketStepCode = "",
        fuelType = FuelType.DUAL,
        source = SmartMeterBookingSource.CAMPAIGN_EMAIL,
        createdAt = OffsetDateTime.of(localDateTime, ZoneOffset.UTC),
        updatedAt = localDateTime,
        juniferTicketId = "101",
        customerId = 10101L,
        smartMeterBookingId = 9L,
        timeslot = "9am - 12pm",
    )

    val secondSmartMeterBookingHistoryRecord = firstSmartMeterBookingHistoryRecord.copy(id = 5L, mprn = "128736182738")
    val thirdSmartMeterBookingHistoryRecord = firstSmartMeterBookingHistoryRecord.copy(
        id = 6L,
        mprn = "123123124245356233", postcode = "HA3 8RU"
    )

    val firstSqsSurveyData = SqsSurveyData(
        id = 1L,
        name = "Jeff",
        groupName = "Group",
        question = "What is a unit test?",
        answer = "this",
        bucketName = "testBucket",
        fileIndentifier = "identify",
        createdAt = localDateTime,
        smartMeterBookingId = 9L
    )

    val secondSqsSurveyData = firstSqsSurveyData.copy(id = 2L, answer = "not this")
    val thirdSqsSurveyData = firstSqsSurveyData.copy(id = 3L, answer = "a third thing")

    val bebocJsonBody = """
        
        {
          "appointmentId": "667ae12e987ce144a792ea9c",
          "appointmentStatus": "COMPLETED",
          "bookingReference": "STL/123718",
          "mpan": "1200060154610",
          "mprn": "7603268001",
          "engineer": null,
          "supplierConstant": "SO_ENERGY",
          "jobTypeConstant": "DUAL_FUEL_SMETS_2",
          "appointmentTypeConstant": "SCHEDULED_EXCHANGE",
          "addressFirstLine": "Gately Court Flat 42, Samuel Street",
          "postcode": "SE15 6FB",
          "appointmentDate": "2024-01-28",
          "appointmentStartTime": "08:00:00",
          "surveys":[
{
      "name": "Risk Assessment",
      "groupName": "Gaining Access",
      "id": 2,
      "sequence": 0,
      "rootId": 1,
      "groupId": 2,
      "questions": [
        {
          "type": 2,
          "answer": "No",
          "key": "risk_assessment_access_problems",
          "question": "Are any animals, adults or children creating any access problems?",
          "id": "3001"
        },
        {
          "type": 2,
          "answer": "No",
          "key": "risk_assessment_lighting_levels",
          "question": "Are there any problems with the lighting levels in the working area, which would lead you to need additional lighting?",
          "id": "3002",
          "photoUrl": "testUrl"

        }
]
}
       ],
    "events":
    [
        {
            "type": "BOOKING",
            "user":
            {
                "email": "<EMAIL>",
                "name": "Bonnie McCormack",
                "id": "659a621e7e4216f69361dd1f",
                "active": true,
                "deleted": false
            },
            "status": "BOOKED",
            "createdOn": "2024-06-25T15:24:30.693Z",
            "updatedOn": "2024-06-25T15:24:30.693Z",
            "_id": "667ae12e987ce144a792eaa0"
        }
    ],
    "comments":[],
    "timeSlot": {
         "_id": "5ead363e5f799a95362be875",
                "name": "8am - 12pm",
                "startTime": "1970-01-01T08:00:00.000Z",
                "endTime": "1970-01-01T12:00:00.000Z",
                "duration": 240,
                "migration": true
            },
        "reason": " Customer Cancellation",
        "subReason": " Customer cancelled booking (A022)",
        "profileClass": "01",
        "ssc": "0393",
        "elecMeterSerialNumber": "S07A19282",
        "gasMeterSerialNumber": "686858"
    }
    """.trimIndent()

    val aMeterPointRequest = idRequestStr { id = "000001" }

    val aMeterPoint = meterPoint {
        id = 1
        property = property {
            id = 2
            address = address {
                id = 1
                address1 = "address".toNullableString()
                address2 = "address".toNullableString()
                address3 = "address".toNullableString()
                address4 = "address".toNullableString()
                address5 = "address".toNullableString()
                address6 = "address".toNullableString()
                address7 = "address".toNullableString()
                address8 = "address".toNullableString()
                address9 = "address".toNullableString()
                postcode = "HA3 8RU".toNullableString()
                countryCode = "UK".toNullableString()
                type = "Flat"
                deleted = NullableTimestamp.getDefaultInstance()
                createdAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
                updatedAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
            }
            type = PropertyType.FLAT
            deleted = NullableTimestamp.getDefaultInstance()
            createdAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
            updatedAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
        }
        identifier = "000001"
        type = MeterPointType.MPAN
        supplyStartDate = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
        ukProfileClass = UkProfileClass.UNKNOWN_UK_PROFILE_CLASS
        ukGspGroup = "_A"
        operationType = OperationType.CREDIT
        readingFrequencyCode = "test".toNullableString()
        serviceType = "test".toNullableString()
        measurementType = MeasurementType.EXPORT
        dccServiceStatus = DccServiceStatus.UNKNOWN_DCC_SERVICE_STATUS
        deleted = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toNullableTimestamp()
        changeOfTenancyFl = false
        meters.add(energy.so.assets.meter.v2.Meter.getDefaultInstance())
        readings.add(energy.so.assets.meterReadings.v2.MeterReading.getDefaultInstance())
        createdAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
        updatedAt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
        meterPointGas = MeterPointGas.getDefaultInstance()
        meterPointElectricity = MeterPointElectricity.getDefaultInstance()
        meterPointHistory = MeterPointHistory.getDefaultInstance()
        fromDt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
        toDt = LocalDateTime.of(2022, 2, 2, 2, 2, 2).toTimestamp()
    }

    val aMeterPointResponse = meterPointResponse { meterPoint = aMeterPoint }

    val aCustomerPropertRel = CustomerPropertyRel(
        id = 1,
        customerId = 3,
        propertyId = 2
    )

    val aCustomer = Customer(
        id = 3,
        number = "customerNumber",
        firstName = "test",
        lastName = "mcTestingTon",
        `class` = CustomerClass.CONSUMER_UNKNOWN,
        customerSetting = CustomerSettingData.customerSettings,
        metadata = CustomerMetadata(),
        type = CustomerType.UNKNOWN,
    )

    val aBillingAccount = BillingAccount(
        id = 1,
        `class` = BillingAccountClass.INVOICE,
        name = "name",
        number = "456",
        type = BillingAccountType.CUS_U,
        currency = "GBP",
        customerId = 3,
        from = LocalDateTime.of(2022, 2, 2, 2, 2, 2),
        settings = BillingAccountSettings(
            id = 1
        )
    )
    val smartMeterBookingList = listOf(
        SmartMeterBooking(
            id = 1,
            campaignId = null,
            slotStartDateTime = LocalDateTime.of(2024, 10, 10, 8, 0, 0),
            billingAccountId = "8765",
            customerName = "Linda Dsouza",
            customerEmail = "<EMAIL>",
            customerPhone = "***********",
            address = "12 Kent Rd, Glasgow",
            postcode = "G3 7BY",
            mpan = "*************",
            mprn = "**********",
            gasMeterSerialNumber = "GAS0453725",
            electricityMeterSerialNumber = "ELEC409J03841",
            aesJobNumber = "STL/126018",
            aesPatchAvailabilityId = null,
            cancellationDateTime = null,
            meterReadingFrequency = null,
            medicalEquipment = false,
            juniferTicketId = "********",
            previousTicketStepCode = null,
            fuelType = FuelType.DUAL,
            source = SmartMeterBookingSource.IMBP,
            createdAt = OffsetDateTime.of(LocalDateTime.of(2024, 7, 10, 8, 0, 0), ZoneOffset.UTC),
            updatedAt = null,
            aesJobType = null,
            aesStatus = null,
            installedDatetime = null,
            failedDatetime = null,
            customerId = null,
            agentEmail = null,
            appointmentType = null,
            installationType = null,
            meterType = MeterType.DUAL,
            slotEndDateTime = LocalDateTime.of(2024, 10, 10, 12, 0, 0),
            aesUpdateRequestJson = JSON.json(
                """
               {
                 "appointmentId": "65ac88ff29988b79b550c18f",
                 "appointmentStatus": "COMPLETED",
                 "bookingReference": "STL/124284",
                 "mpan": "1300009454103",
                 "mprn": "4309292547",
                 "engineer": {
                   "name": "Adam Berry",
                   "email": "<EMAIL>",
                   "id": "65652036cf69ccdbba10ad5b",
                   "active": true
                 },
                 "comments": [
                   {
                     "user": {
                       "_id": "658ea8f8edfe8184166cd085",
                       "email": "<EMAIL>",
                       "name": "Katie Brown",
                       "roles": [
                         "5ebd703a5f799a9536f049c5",
                         "5ead363d5f799a95362be7c6",
                         "5ead363d5f799a95362be7c3",
                         "5eb68e673f2e5e0011baecd8",
                         "5da3991e4fc5f258731c09e3"
                       ],
                       "active": true,
                       "hidden": false,
                       "superUser": false,
                       "patches": [],
                       "groups": [],
                       "hubId": 8082,
                       "deleted": false,
                       "qualifications": [],
                       "licenses": [],
                       "__v": 0,
                       "id": "658ea8f8edfe8184166cd085"
                     },
                     "public": true,
                     "comment": "JOB TO BE ELEC ONLY AS CUTSOMER REFUSED GAS MEX",
                     "_id": "6698e367bc84a87b24c3a6e3",
                     "updatedOn": "2024-07-18T09:41:59.765Z",
                     "createdOn": "2024-07-18T09:41:59.765Z",
                     "deleted": false
                   }
                 ],
                 "events": [
                   {
                     "type": "BOOKING",
                     "user": {
                       "email": "<EMAIL>",
                       "name": "SO API",
                       "active": true,
                       "deleted": false
                     },
                     "status": "BOOKED",
                     "createdOn": "2024-07-10T10:45:02.178Z",
                     "updatedOn": "2024-07-10T10:45:02.178Z",
                     "_id": "668e662e01ae8d03fd65da91"
                   },
                   {
                     "type": "TECH",
                     "engineer": {},
                     "data": "Event BOOKED has been automatically sent on S2 Queue.",
                     "createdOn": "2024-07-10T10:45:02.273Z",
                     "updatedOn": "2024-07-10T10:45:02.273Z",
                     "_id": "668e662e01ae8d03fd65daa3",
                     "user": {
                       "email": "<EMAIL>",
                       "name": "SO API",
                       "active": true,
                       "deleted": false
                     }
                   }
                 ],
                 "supplierConstant": "SO_ENERGY",
                 "jobTypeConstant": "ELECTRIC",
                 "appointmentTypeConstant": "EMERGENCY_EXCHANGE",
                 "addressFirstLine": "8 Carnarvon Grove",
                 "postcode": "NE66 1PT",
                 "appointmentDate": "2024-01-28",
                 "appointmentStartTime": "08:00:00"
               }
           """.trimIndent()
            ),
        )
    )

    val smartMeterBookingsHistoryList =
        listOf(
            SmartMeterBookingsHistory(
                id = 1,
                campaignId = null,
                slotStartDateTime = LocalDateTime.of(2024, 10, 10, 8, 0, 0),
                billingAccountId = "8765",
                customerName = "Linda Dsouza",
                customerEmail = "<EMAIL>",
                customerPhone = "***********",
                address = "12 Kent Rd, Glasgow",
                postcode = "G3 7BY",
                mpan = "*************",
                mprn = "**********",
                gasMeterSerialNumber = "GAS0453725",
                electricityMeterSerialNumber = "ELEC409J03841",
                aesJobNumber = "STL/126018",
                aesPatchAvailabilityId = null,
                cancellationDateTime = null,
                meterReadingFrequency = null,
                medicalEquipment = false,
                juniferTicketId = "********",
                previousTicketStepCode = null,
                fuelType = FuelType.DUAL,
                source = SmartMeterBookingSource.IMBP,
                createdAt = OffsetDateTime.of(LocalDateTime.of(2024, 7, 10, 8, 0, 0), ZoneOffset.UTC),
                updatedAt = null,
                aesJobType = null,
                aesStatus = null,
                installedDatetime = null,
                failedDatetime = null,
                customerId = null,
                agentEmail = null,
                appointmentType = null,
                installationType = null,
                meterType = MeterType.DUAL,
                slotEndDateTime = LocalDateTime.of(2024, 10, 10, 12, 0, 0),
                aesUpdateRequestJson = null,
                smartMeterBookingId = 1,
            )
        )

    val surveyDataList = listOf(
        SqsSurveyData(
            id = 1,
            name = "surveyName1",
            groupName = "surveyGroupName1",
            question = "Customer at home ?",
            answer = "yes",
            bucketName = "",
            fileIndentifier = null,
            createdAt = LocalDateTime.of(2024, 7, 10, 8, 0, 0),
            smartMeterBookingId = 1
        )
    )

    val smHistory: List<SMHistory> = listOf(
        SMHistory(
            id = 1,
            campaignId = null,
            slotStartDateTime = LocalDateTime.of(2024, 10, 10, 8, 0, 0),
            billingAccountId = "8765",
            customerName = "Linda Dsouza",
            customerEmail = "<EMAIL>",
            customerPhone = "***********",
            address = "12 Kent Rd, Glasgow",
            postcode = "G3 7BY",
            mpan = "*************",
            mprn = "**********",
            gasMeterSerialNumber = "GAS0453725",
            electricityMeterSerialNumber = "ELEC409J03841",
            aesJobNumber = "STL/126018",
            aesPatchAvailabilityId = null,
            cancellationDateTime = null,
            meterReadingFrequency = null,
            medicalEquipment = false,
            juniferTicketId = "********",
            previousTicketStepCode = null,
            fuelType = FuelType.DUAL,
            source = SmartMeterBookingSource.IMBP,
            createdAt = OffsetDateTime.of(LocalDateTime.of(2024, 7, 10, 8, 0, 0), ZoneOffset.UTC),
            aesJobType = null,
            aesStatus = null,
            installedDatetime = null,
            failedDatetime = null,
            customerId = null,
            agentEmail = null,
            appointmentType = null,
            installationType = null,
            meterType = MeterType.DUAL,
            slotEndDateTime = LocalDateTime.of(2024, 10, 10, 12, 0, 0),
            aesUpdateRequestJson = null,
            timeslot = null,
        )
    )

    val smBookingSurvey = listOf(
        SMBookingSurvey(
            name = "surveyName1",
            groupName = "surveyGroupName1",
            question = "Customer at home ?",
            answer = "yes",
            fileUrl = "",
            createdAt = LocalDateTime.of(2024, 7, 10, 8, 0, 0),
        )
    )

    val smBookingHistoryResponse =
        SMBookingHistoryResponse(
            bookings = listOf(
                SMBookingHistory(
                    id = 1,
                    campaignId = null,
                    slotStartDateTime = LocalDateTime.of(2024, 10, 10, 8, 0, 0),
                    billingAccountId = "8765",
                    customerName = "Linda Dsouza",
                    customerEmail = "<EMAIL>",
                    customerPhone = "***********",
                    address = "12 Kent Rd, Glasgow",
                    postcode = "G3 7BY",
                    mpan = "*************",
                    mprn = "**********",
                    gasMeterSerialNumber = "GAS0453725",
                    electricityMeterSerialNumber = "ELEC409J03841",
                    aesJobNumber = "STL/126018",
                    aesPatchAvailabilityId = null,
                    cancellationDateTime = null,
                    meterReadingFrequency = null,
                    medicalEquipment = false,
                    juniferTicketId = "********",
                    previousTicketStepCode = null,
                    fuelType = FuelType.DUAL,
                    source = SmartMeterBookingSource.IMBP,
                    createdAt = OffsetDateTime.of(LocalDateTime.of(2024, 7, 10, 8, 0, 0), ZoneOffset.UTC),
                    updatedAt = null,
                    aesJobType = null,
                    aesStatus = null,
                    installedDatetime = null,
                    failedDatetime = null,
                    customerId = null,
                    agentEmail = null,
                    appointmentType = null,
                    installationType = null,
                    meterType = MeterType.DUAL,
                    slotEndDateTime = LocalDateTime.of(2024, 10, 10, 12, 0, 0),
                    aesUpdateRequestJson = JSON.json(
                        """
                            {
                              "appointmentId": "65ac88ff29988b79b550c18f",
                              "appointmentStatus": "COMPLETED",
                              "bookingReference": "STL/124284",
                              "mpan": "1300009454103",
                              "mprn": "4309292547",
                              "engineer": {
                                "name": "Adam Berry",
                                "email": "<EMAIL>",
                                "id": "65652036cf69ccdbba10ad5b",
                                "active": true
                              },
                              "comments": [
                                {
                                  "user": {
                                    "_id": "658ea8f8edfe8184166cd085",
                                    "email": "<EMAIL>",
                                    "name": "Katie Brown",
                                    "roles": [
                                      "5ebd703a5f799a9536f049c5",
                                      "5ead363d5f799a95362be7c6",
                                      "5ead363d5f799a95362be7c3",
                                      "5eb68e673f2e5e0011baecd8",
                                      "5da3991e4fc5f258731c09e3"
                                    ],
                                    "active": true,
                                    "hidden": false,
                                    "superUser": false,
                                    "patches": [],
                                    "groups": [],
                                    "hubId": 8082,
                                    "deleted": false,
                                    "qualifications": [],
                                    "licenses": [],
                                    "__v": 0,
                                    "id": "658ea8f8edfe8184166cd085"
                                  },
                                  "public": true,
                                  "comment": "JOB TO BE ELEC ONLY AS CUTSOMER REFUSED GAS MEX",
                                  "_id": "6698e367bc84a87b24c3a6e3",
                                  "updatedOn": "2024-07-18T09:41:59.765Z",
                                  "createdOn": "2024-07-18T09:41:59.765Z",
                                  "deleted": false
                                }
                              ],
                              "events": [
                                {
                                  "type": "BOOKING",
                                  "user": {
                                    "email": "<EMAIL>",
                                    "name": "SO API",
                                    "active": true,
                                    "deleted": false
                                  },
                                  "status": "BOOKED",
                                  "createdOn": "2024-07-10T10:45:02.178Z",
                                  "updatedOn": "2024-07-10T10:45:02.178Z",
                                  "_id": "668e662e01ae8d03fd65da91"
                                },
                                {
                                  "type": "TECH",
                                  "engineer": {},
                                  "data": "Event BOOKED has been automatically sent on S2 Queue.",
                                  "createdOn": "2024-07-10T10:45:02.273Z",
                                  "updatedOn": "2024-07-10T10:45:02.273Z",
                                  "_id": "668e662e01ae8d03fd65daa3",
                                  "user": {
                                    "email": "<EMAIL>",
                                    "name": "SO API",
                                    "active": true,
                                    "deleted": false
                                  }
                                }
                              ],
                              "supplierConstant": "SO_ENERGY",
                              "jobTypeConstant": "ELECTRIC",
                              "appointmentTypeConstant": "EMERGENCY_EXCHANGE",
                              "addressFirstLine": "8 Carnarvon Grove",
                              "postcode": "NE66 1PT",
                              "appointmentDate": "2024-01-28",
                              "appointmentStartTime": "08:00:00"
                            }
                        """.trimIndent()
                    ),
                    history = smHistory,
                    surveys = smBookingSurvey,
                    bebocApplicationId = "65ac88ff29988b79b550c18f",
                    engineerName = "Adam Berry",
                    engineerEmail = "<EMAIL>",
                    events = listOf(),
                    comments = listOf()
                )
            )
        )
    val aContact = Contact(
        id = 1,
        firstName = "test",
        lastName = "mcTestingTon",
        email = "<EMAIL>",
        phoneNumber1 = "077305978173",
        type = ContactType.BROKER
    )
    val expectedCreateBookingResponse = CreateBookingResponse.newBuilder()
        .setJobNumber("STL/21387")
        .build()


    val expectedSmartMeterBookingViaBebocSqs = SmartMeterBooking(
        3,
        null,
        LocalDateTime.of(2024, 7, 22, 13, 0),
        "1",
        "test mcTestingTon",
        "<EMAIL>",
        "077305978173",
        "Address",
        "HA3 8RU",
        "000001",
        "000002",
        "gasSerial",
        "elecSerial",
        "563445",
        "test",
        null,
        null,
        null,
        null,
        null,
        FuelType.DUAL,
        SmartMeterBookingSource.SQS,
        null,
        null,
        "DUAL",
        "BOOKED",
        null,
        null,
        3,
        null,
        "appointmentTypeTest",
        null,
        MeterType.DUAL,
        LocalDateTime.of(2024, 2, 4, 13, 0),
        null,
        "8AM - 12PM"
    )

    val firstMockSqsSurveyData = SqsSurveyData(
        name = "Test testington",
        groupName = "test group",
        question = "is this a test?",
        answer = "yes",
        bucketName = "bucket",
        fileIndentifier = "file",
        smartMeterBookingId = 3
    )

    val secondMockSqsSurveyData = SqsSurveyData(
        name = "Test testington",
        groupName = "test group",
        question = "is this not a test?",
        answer = "no",
        bucketName = "",
        fileIndentifier = ""
    )


    val aesStatusBookedMessage = AesBebocStatusUpdate(
        jobNumber = "563445",
        jobType = "DUAL",
        status = "BOOKED",
        jsonSqsMessage = "{\"dummy\":\"message\"}",
        appointmentDate = "2024-07-22",
        appointmentStartTime = "13:00:00",
        appointmentEndDateTime = "2024-02-04T13:00:00.000Z",
        address = "Address",
        aesPatchAvailabilityId = "test",
        appointmentType = "appointmentTypeTest",
        elecMeterSerialNumber = "elecSerial",
        gasMeterSerialNumber = "gasSerial",
        mpan = "000001",
        mprn = "000002",
        postcode = "HA3 8RU",
        timeslot = "8AM - 12PM",
        surveys = listOf(firstMockSqsSurveyData)
    )

    val firstBebocQuestion = BebocQuestion(
        type = 1,
        answer = "yes",
        question = "is this a test?",
        id = 2,
        photoUrl = "url"
    )

    val firstBebocSurvey = BebocSurvey(
        name = "Test testington",
        groupName = "test group",
        id = 11,
        sequence = 12,
        rootId = 13,
        groupId = 14,
        questions = listOf(firstBebocQuestion)
    )

    val secondBebocQuestion = BebocQuestion(
        type = 3,
        answer = "no",
        question = "is this not a test?",
        id = 4
    )

    val secondBebocSurvey = BebocSurvey(
        name = "Test testington",
        groupName = "test group",
        id = 21,
        sequence = 22,
        rootId = 23,
        groupId = 24,
        questions = listOf(secondBebocQuestion)
    )

    val smartMeterRegisterInterest = SmartMeterRegisterInterest(
        juniferAccountNumber = "********",
        customerName = "James Bond",
        customerEmail = "<EMAIL>",
        customerComment = "Any date after 20th Sep",
        mpans = "*************,*************",
        mprns = "**********",
        rescheduleAttempt = true,
        createdAt = offsetDateTime
    )

    val aProductAccountAgreementRelRecord = ProductAccountAgreementRel(
        productAccountId = 123,
        agreementId = 3
    )

    val aProductAccount = ProductAccount.fromEntity(
        energy.so.commons.model.tables.pojos.ProductAccount(
            id = 1,
            billingAccountId = 8765,
            type = ProductAccountType.GAS,
            createdAt = LocalDateTime.of(2022, 2, 2, 2, 2),
            updatedAt = LocalDateTime.of(2022, 2, 2, 2, 2)
        ),
        billingAccount = aBillingAccount
    )

    val meterInfoAccount = meterInfoAcc {
        identifier = "*************"
        meterSerialNumber = "************"
        registerId = "1"
        unit = "kWh"
        type = MPAN
        meterPointId = 123L
        meterType = energy.so.assets.meter.v2.MeterType.N
        accountNumber = "0076345"
    }

    val aSmartMeterInterest = SmartMeterRegisterInterest(
        id = 1,
        juniferAccountNumber = "0076345",
        customerName = "James Bond",
        customerEmail = "<EMAIL>",
        customerComment = "Any date after 20th Sep",
        mpans = "*************,*************",
        mprns = "**********",
        rescheduleAttempt = true,
        createdAt = offsetDateTime,
    )

    val registeredInterestResponse = RegisteredInterestResponse(
        bookingByInterest = mapOf(smartMeterRegisterInterest to smartMeterBooking.copy(installationType = TRADITIONAL)),
        meterDetails = listOf(
            meterInfoAcc {
                identifier = "GK1234987"
                type = MPRN
                accountNumber = "********"
            },
            meterInfoAcc {
                identifier = "*************"
                type = MPAN
                accountNumber = "********"
            },
        )
    )

    val registeredInterestCsv = RegisteredInterestCsv(
        customerName = "James Bond",
        customerEmail = "<EMAIL>",
        juniferAccountNumber = "0076345",
        supplyPostcode = "",
        dateRegistered = offsetDateTime.toLocalDate().toString(),
        metersOnSite = "Electric traditional meter (MPAN *************)",
        slotInterestReason = "",
        rescheduleAttempt = "Yes",
        customerComment = "Any date after 20th Sep",
        appointmentDetails = ""
    )

    val smartMeterInterestFileUrl =
        "https://storage.googleapis.com/test-bucket/smart-meter-interest/smart-meter-interest-2025-01-01T16%3A51%3A07.761628.csv"

}
