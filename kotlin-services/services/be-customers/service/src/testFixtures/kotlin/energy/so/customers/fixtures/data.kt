package energy.so.customers.fixtures

import energy.so.core.customers.server.config.CommunicationTemplate
import energy.so.core.customers.server.models.message.BankDetails
import energy.so.core.customers.server.models.message.CorrespondenceAddress
import energy.so.core.customers.server.models.message.Customer
import energy.so.core.customers.server.models.message.Electricity
import energy.so.core.customers.server.models.message.Gas
import energy.so.core.customers.server.models.message.PafAddress
import energy.so.core.customers.server.models.message.Payload
import energy.so.core.customers.server.models.message.PhoneNumbers
import energy.so.core.customers.server.models.message.ProductInformation
import energy.so.core.customers.server.models.message.SupplyAddress
import energy.so.core.customers.server.trustpilot.TrustpilotConfig

val communicationTemplate = CommunicationTemplate(
    coolingOffReminder = "cooling-off-reminder",
    reviewWithUs = "review-with-us",
    referredEarnedCreditEmail = "t3",
    refereeEarnedCreditEmail = "t4",
    meterReadingReminderEmail = "t5",
    referredSignupNotificationEmail = "t6",
    psrCreationEmail = "t7",
    requestDepositEmail = "depositRequest",
    moveCustomersToDailyReadsEmail = "t9",
    switchCancelledAsNoDepositReceivedEmail = "t10",
    depositReceivedEmail = "t11",
    psrConfirmationEmail = "t12",
)

val trustpilotConfig = TrustpilotConfig(
    clientId = "id",
    clientSecret = "secret",
    host = "host",
    inviteApiHost = "invite-host",
    connectionTimeout = 10000,
    requestTimeout = 20000,
    stubEmail = ""
)

val gas: Gas = Gas(
    isDualFuel = true,
    tariffId = "ABC-DEF-180910",
    currentPersonalProjectionPence = 120000.0,
    newTariffStandingChargePence = 20.445,
    newPaymentMethod = "Monthly Direct Debit",
    newTariffPrimaryRatePence = 3.728,
    currentSupplier = "Big Energy",
    meterType = "credit",
    currentPaymentMethod = "Monthly Direct Debit",
    currentAnnualConsumptionKwh = 28923.0,
    mprn = "3308152301",
    quotedAnnualPricePence = 115275.0,
    newTariffName = "Fixed Tariff",
    currentTariffName = "Standard (Variable)",
    currentExitFeePence = 222.0,
    currentPrimaryRatePence = 111.0,
    currentProductEndDate = "",
    currentStandingChargePence = 500.0,
    newTariffExitFeePence = 10.0
)

val electricity: Electricity = Electricity(
    mpan = "2000004115448",
    isDualFuel = false,
    newTariffNightRatePence = 100.0,
    tariffId = "ABC-DEF-180910",
    currentPrimaryRatePence = 100.0,
    currentPersonalProjectionPence = 100.0,
    newTariffStandingChargePence = 100.0,
    newPaymentMethod = "Monthly Direct Debit",
    isEconomy7 = true,
    currentExitFeePence = 100.0,
    newTariffExitFeePence = 100.0,
    newTariffPrimaryRatePence = 100.0,
    currentSupplier = "Big Energy",
    meterType = "credit",
    currentAnnualNightConsumptionKwh = 100.0,
    currentPaymentMethod = "Monthly Direct Debit",
    currentNightRatePence = 100.0,
    currentAnnualConsumptionKwh = 100.0,
    currentStandingChargePence = 100.0,
    currentProductEndDate = "12.11.2022",
    quotedAnnualPricePence = 100.0,
    newTariffName = "ABC-DEF-180910",
    currentTariffName = "Standard (Variable)",
    currentAnnualDayConsumptionKwh = 100.0
)


val bankDetails: BankDetails = BankDetails(
    bankAccountName = "Harry Potter",
    bankAccountNumber = "********",
    bankName = "LLoyds Bank",
    bankSortCode = "123412",
    directDebitPaymentDay = "1"
)

val productInformation: ProductInformation = ProductInformation(
    gas = gas,
    electricity = electricity
)

val pafAddress = PafAddress(
    doubleDependentLocality = "String",
    town = "Whitehouse",
    county = "Buckinghamshire",
    thoroughfare = "thoroughfare",
    buildingNumber = "12",
    organisationName = "N/A",
    dependentThoroughfare = "dependentThoroughfare",
    buildingName = "buildingName",
    poBox = "1",
    dependentLocality = "dependentLocality",
    subBuildingName = "subBuildingName",
    postCode = "MK34RT"
)

val customer: Customer = Customer(
    emailAddress = "<EMAIL>",
    title = "Mr",
    givenNames = "Harry",
    familyName = "Potter",
    phoneNumbers = listOf(PhoneNumbers(phoneNumber = "**********", phoneType = "Mobile")),
    correspondenceAddress = CorrespondenceAddress(
        pafAddress
    )
)

val supplyAddress: SupplyAddress = SupplyAddress(
    pafAddress = pafAddress,
    regionName = "East Midland",
    regionNumber = "1"
)

val payload: Payload = Payload(
    switchedAt = *************.3,
    supplyAddress = supplyAddress,
    customerInformation = customer,
    productInformation = productInformation,
    bankDetails = bankDetails,
    billingConsent = true
)
