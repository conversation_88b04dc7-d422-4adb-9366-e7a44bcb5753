//package energy.so.customers.fixtures
//
//import energy.so.core.customers.server.services.CustomerService
//import energy.so.core.customers.server.services.impl.DefaultCustomerService
//import energy.so.junifer.sync.v1.customers.CustomersSyncClient
//import energy.so.users.client.v2.UsersClient
//
//class InMemoryCustomerService(
//    private val customersRepository: InMemoryCustomerRepository = InMemoryCustomerRepository(),
//    private val usersClient: UsersClient,
//    private val syncClient: CustomersSyncClient,
//    private val billingAccountRepository: InMemoryBillingAccountRepository = InMemoryBillingAccountRepository()
//) : CustomerService by DefaultCustomerService(
//    customersRepository = customersRepository,
//    usersClient = usersClient,
//    syncClient = syncClient,
//    billingAccountRepository  = billingAccountRepository
//) {
//    val customers = customersRepository.customers
//    val billingAccounts = billingAccountRepository.billingAccounts
//
//    fun getBillingAccountRepository(): InMemoryBillingAccountRepository {
//        return this.billingAccountRepository;
//    }
//}