package energy.so.customers.fixtures

import energy.so.core.customers.server.exceptions.responseValidator
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.HttpResponseValidator
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

object MockHttpClient {
    private fun generateMockEngine(
        responseBody: ByteReadChannel,
        responseCode: HttpStatusCode,
    ) = MockEngine {
        respond(
            responseBody,
            responseCode,
            headersOf("Content-Type" to listOf(ContentType.Application.Json.toString()))
        )
    }

    fun generateMockHttpClient(
        responseBody: ByteReadChannel,
        responseCode: HttpStatusCode,
    ): HttpClient {
        val mockEngine: MockEngine = generateMockEngine(
            responseBody = responseBody,
            responseCode = responseCode
        )
        return HttpClient(mockEngine) {

            install(ContentNegotiation) {
                json(
                    Json {
                        isLenient = true
                        ignoreUnknownKeys = true
                    }
                )
            }

            defaultRequest {
                contentType(ContentType.Application.Json)
            }

            expectSuccess = false

            HttpResponseValidator {
                validateResponse { response -> responseValidator(response) }
            }
        }
    }

    fun generateMockHttpClientWithoutValidator(
        responseBody: ByteReadChannel,
        responseCode: HttpStatusCode,
    ): HttpClient {
        val mockEngine: MockEngine = generateMockEngine(
            responseBody = responseBody,
            responseCode = responseCode
        )
        return HttpClient(mockEngine) {

            install(ContentNegotiation) {
                json(
                    Json {
                        isLenient = true
                        ignoreUnknownKeys = true
                    }
                )
            }

            defaultRequest {
                contentType(ContentType.Application.Json)
            }

            expectSuccess = false
        }
    }
}
