package energy.so.customers.fixtures

import energy.so.core.customers.server.models.CustomFieldsDto
import energy.so.core.customers.server.models.ErrorDto
import energy.so.core.customers.server.models.FreshdeskContactDto
import energy.so.core.customers.server.models.FreshdeskTicketDto
import energy.so.core.customers.server.models.FreshdeskContactRequestDto
import energy.so.core.customers.server.models.FreshdeskContactErrorResponseDto
import energy.so.core.customers.server.trustpilot.COMPLAIN_TICKET_TYPE
import energy.so.core.customers.server.trustpilot.TICKET_OPEN_STATUS
import energy.so.customers.fixtures.TestDataUtils.localDateTime
import java.time.LocalDateTime


object FreshDeskCannedData {

    const val ACCOUNT_ID = 234L
    const val REQUESTER_ID = 123L
    const val JUNIFER_CONTACT_ID = 345L
    const val EMAIL = "<EMAIL>"
    const val DUPLICATE_EMAIL = "<EMAIL>"
    val otherEmails = listOf(
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    )

    val freshDeskContact = FreshdeskContactDto(
        id = REQUESTER_ID,
        email = EMAIL,
        locale = "en",
        otherEmails = otherEmails,
        customFields = CustomFieldsDto(
            firstName = "Rupa",
            lastName = "Gini",
            juniferContactId = JUNIFER_CONTACT_ID
        )
    )

    val freshDeskContactNoOtherEmails = freshDeskContact.copy(
        otherEmails = emptyList()
    )

    val freshDeskContactResponse = mapOf(
        "id" to REQUESTER_ID,
        "email" to EMAIL,
        "language" to "en",
        "other_emails" to otherEmails,
        "custom_fields" to mapOf(
            "surname" to "Gini",
            "forename" to "Rupa",
            "junifer_contact_id" to 345
        )
    )

    val freshDeskContactResponseNoOtherEmails = mapOf(
        "id" to REQUESTER_ID,
        "email" to EMAIL,
        "language" to "en",
        "other_emails" to emptyList<String>(),
        "custom_fields" to mapOf(
            "surname" to "Gini",
            "forename" to "Rupa",
            "junifer_contact_id" to 345
        )
    )

    val validContactPayload = FreshdeskContactRequestDto(
        phone = "0044123456",
        mobile = "004789123",
        name = "Rupa Gini",
        email = EMAIL,
        otherEmails = emptyList(),
        customFields = CustomFieldsDto(
            firstName = "Rupa",
            lastName = "Gini",
            juniferContactId = JUNIFER_CONTACT_ID
        ),
    )

    val duplicateEmailContactPayload = validContactPayload.copy(
        email = DUPLICATE_EMAIL
    )

    val errorContactResponse = FreshdeskContactErrorResponseDto(
        errors = listOf(
            ErrorDto(
                field = "email",
                message = "It should be a unique value",
            )
        )
    )

    val okContactResponse = FreshdeskContactErrorResponseDto(
        errors = emptyList()
    )

    val freshDeskTickets = listOf(
        FreshdeskTicketDto(
            id = 1,
            requesterId = REQUESTER_ID,
            type = "Customer",
            source = 1,
            status = 2,
            createdAt = LocalDateTime.of(2023, 4, 26, 20, 20, 35)
        ),
        FreshdeskTicketDto(
            id = 2,
            requesterId = REQUESTER_ID,
            type = "Customer",
            source = 1,
            status = 5,
            createdAt = LocalDateTime.of(2021, 5, 12, 9, 28, 47)
        )
    )

    val freshDeskTicketsWithin2Months = listOf(
        FreshdeskTicketDto(
            id = 1,
            requesterId = REQUESTER_ID,
            type = "Customer",
            source = 1,
            status = 2,
            createdAt = LocalDateTime.now().minusDays(45)
        ),
        FreshdeskTicketDto(
            id = 2,
            requesterId = REQUESTER_ID,
            type = "Customer",
            source = 1,
            status = 5,
            createdAt = LocalDateTime.now().minusMonths(2)
        )
    )

    val freshDeskComplainTicketsWithin8Months = listOf(
        FreshdeskTicketDto(
            id = 2,
            requesterId = REQUESTER_ID,
            type = COMPLAIN_TICKET_TYPE,
            source = 1,
            status = TICKET_OPEN_STATUS,
            createdAt = localDateTime.minusMonths(7)
        )
    )
}
