package energy.so.customers.fixtures

import energy.so.commons.grpc.clients.junifer.dtos.CreatePaymentDto
import energy.so.commons.grpc.clients.junifer.dtos.PaymentStatus
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.UpdateStatu
import energy.so.commons.model.enums.UpdateType
import energy.so.commons.model.tables.pojos.UpdatesFromDcaAuditLog
import energy.so.core.customers.server.models.dca.DcaPaymentUpdateRequest
import energy.so.customers.billingaccounts.v2.BillingAccountResponse
import energy.so.payments.v2.PaymentSource
import energy.so.payments.v2.registerExternalPaymentRequest
import java.time.LocalDateTime
import org.jooq.JSON

val accountNumber = "********"

val aDcaPaymentUpdate = DcaPaymentUpdateRequest(
    uid = "FirstLocate-123",
    clientRef = "123456",
    dcaRef = "FL-123",
    clientName = "So Energy",
    segment = "Known-Live-NA",
    name = "<PERSON>",
    date = "31/01/2025",
    time = "15:30",
    takenBy = "<PERSON>",
    dateCleared = "",
    method = "",
    amount = "300.5",
    creditCardFee = "",
    allocatedToDebt = "",
    allocatedToCharges = "",
    initialDebt = "",
    charges = "",
    totalPayments = "",
    balance = "",
    commissionRate = "",
    commission = "",
    caseDateInstructed = "",
    contra = "",
    contraForId = "",
    opCode = "",
    itemId = "",
    paymentRef = "",
)

val jsonMessage =
    """{"A": "FirstLocate-123", "B": "123456", "C": "FL-123", "D": "So Energy", "E": "Known-Live-NA", "F": "John Doe", "G": "31/01/2025", "H": "15:30", "I": "Jane Doe", "J": "", "K": "", "L": "300.5", "M": "", "N": "", "O": "", "P": "", "Q": "", "R": "", "S": "", "T": "", "U": "", "V": "", "W": "", "X": "", "Y": ""}"""


val aBillingAccountResponse = BillingAccountResponse.newBuilder().setJuniferAccountId(100L).build()

val createPayment = CreatePaymentDto(
    accountId = aBillingAccountResponse.juniferAccountId,
    amountInPence = aDcaPaymentUpdate.amountInPence,
    status = PaymentStatus.SUCCESSFUL,
    paymentMethodType = "PAYMENT_SUCCEEDED",
    description = "Payment to DCA: First Locate",
    createdAt = aDcaPaymentUpdate.formattedDate,
)
val aRegisterExternalPaymentRequest = registerExternalPaymentRequest {
    paymentSource = PaymentSource.DCA
    reference = "123e4567-e89b-12d3-a456-************"
    externalId = "external-Id"
    accountNumber = "********"
    amountInPence = 1313
    processedOn = LocalDateTime.of(2025, 10, 10, 1, 3).toTimestamp()
    billingAccountId = *********
}
val aPendingDcaPaymentUpdateLog = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "",
    updateType = UpdateType.PAYMENT,
    jsonRequestReceived = JSON.json(jsonMessage),
    status = UpdateStatu.PENDING,
    filename = "/payments/dca_payment.csv",
    dca = "First Locate",
    uuid = "123456"
)

val anErrorDcaPaymentUpdateLog =
    aPendingDcaPaymentUpdateLog.copy(
        id = 1,
        juniferAccountNumber = accountNumber,
        status = UpdateStatu.FAILURE,
        error = "Unable to find billing account with ${accountNumber}",
    )

val aSuccessfulDcaPaymentUpdateLog = aPendingDcaPaymentUpdateLog.copy(
    id = 1,
    juniferAccountNumber = accountNumber,
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json("""{"amount": "${aDcaPaymentUpdate.amount}"}"""),
)