package energy.so.customers.fixtures.calltrace

import energy.so.commons.json.utils.toJsonObject
import energy.so.commons.model.enums.CallTraceTransactionStatusEnum
import energy.so.core.customers.server.calltrace.CallTraceInDebitEmailDto
import energy.so.core.customers.server.calltrace.CallTraceResponseAsJson
import energy.so.core.customers.server.calltrace.CallTraceTransaction
import java.time.LocalDateTime.now
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import energy.so.commons.model.tables.pojos.CallTraceTransaction as PojoCallTraceTransaction

object CallTraceTransactionPrecannedData {
    val nowLocalDateTime = now().withHour(0).withSecond(0).withNano(0)

    object Constants {
        const val ACCOUNT_NUMBER = "1234"
        const val ACCOUNT_NUMBER_MULTIPLE_ROWS = "2345"
        const val CONTACT_ID = 1L
        const val CALL_TRACE_EMAIL = "<EMAIL>"
        const val CALL_TRACE_IN_TOUCH_MOBILE = "***********"

        const val JSON_CSV_ROW = "{name: 'Glen', surname: 'Glenberry'}"
    }

    val transactionPojo = PojoCallTraceTransaction(
        id = Constants.CONTACT_ID,
        jsonSent = Constants.JSON_CSV_ROW,
        sentAt = nowLocalDateTime,
        accountNumber = Constants.ACCOUNT_NUMBER,
        status = CallTraceTransactionStatusEnum.ADDED,
    )

    val transaction = CallTraceTransaction(
        jsonSent = Constants.JSON_CSV_ROW,
        sentAt = nowLocalDateTime,
        accountNumber = Constants.ACCOUNT_NUMBER,
        status = CallTraceTransactionStatusEnum.ADDED,
        errorMessage = null,
        jsonReceived = null,
        receivedAt = null,
    )

    val csvBadHeader = "BadClientId,SequenceNo,CIG_URN,DataDNA,Name,Title,Forename,Othername,Surname,InTouchEmail\n"
    val csvBadOutputFileString = csvBadHeader +
            "${ Constants.ACCOUNT_NUMBER },1,-**********,********,MR JARNAIL S SINGH,MR,JARNAIL,S,SINGH,${ Constants.CALL_TRACE_EMAIL },\n"

    val csvHeader = "ClientId,SequenceNo,CIG_URN,DataDNA,Name,Title,Forename,Othername,Surname,InTouchEmail\n"
    val csvOutputFileString = csvHeader +
            "${ Constants.ACCOUNT_NUMBER },1,-**********,********,MR JARNAIL S SINGH,MR,JARNAIL,S,SINGH,${ Constants.CALL_TRACE_EMAIL },\n" +
            "${ Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS },1,-**********,********,MRS SHAHANARA AKTAR MIAH,MRS,SHAHANARA,AKTAR,MIAH,,\n" +
            "${ Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS },1,-**********,********,MRS Desmond,MRS,SHAHANARA,AKTAR,MIAH,,\n"+
            "${ Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS },1,-**********,********,MRS Desmond,MRS,SHAHANARA,AKTAR,MIAH,wibble,"

    val parsedOutputCsv = listOf(
        mapOf("ClientId" to Constants.ACCOUNT_NUMBER, "SequenceNo" to "1", "CIG_URN" to "-**********", "DataDNA" to "********", "Name" to "MR JARNAIL S SINGH", "Title" to "MR", "Forename" to "JARNAIL", "Othername" to "S", "Surname" to "SINGH", "InTouchEmail" to Constants.CALL_TRACE_EMAIL).toJsonObject(),
        mapOf("ClientId" to Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS, "SequenceNo" to "1", "CIG_URN" to "-**********", "DataDNA" to "********", "Name" to "MRS SHAHANARA AKTAR MIAH", "Title" to "MRS", "Forename" to "SHAHANARA", "Othername" to "AKTAR", "Surname" to "MIAH", "InTouchEmail" to "").toJsonObject(),
        mapOf("ClientId" to Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS, "SequenceNo" to "1", "CIG_URN" to "-**********", "DataDNA" to "********", "Name" to "MRS Desmond", "Title" to "MRS", "Forename" to "SHAHANARA", "Othername" to "AKTAR", "Surname" to "MIAH", "InTouchEmail" to "").toJsonObject(),
        mapOf("ClientId" to Constants.ACCOUNT_NUMBER_MULTIPLE_ROWS, "SequenceNo" to "1", "CIG_URN" to "-**********", "DataDNA" to "********", "Name" to "MRS Desmond", "Title" to "MRS", "Forename" to "SHAHANARA", "Othername" to "AKTAR", "Surname" to "MIAH", "InTouchEmail" to "wibble").toJsonObject()
    )
    val allData = mapOf(
        "ClientId" to Constants.ACCOUNT_NUMBER,
        "SequenceNo" to "1",
        "CIG_URN" to "-**********",
        "DataDNA" to "********",
        "Name" to "MR JARNAIL S SINGH",
        "Title" to "MR",
        "Forename" to "JARNAIL",
        "Othername" to "S",
        "Surname" to "SINGH",
        "InTouchEmail" to Constants.CALL_TRACE_EMAIL,
        "InTouchMobile" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
        "InTouchLandline" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
        "telephone" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
    ).toJsonObject()
    val brokenAllData = mapOf(
        "ClientIdBadName" to Constants.ACCOUNT_NUMBER,
        "SequenceNo" to "1",
        "CIG_URN" to "-**********",
        "DataDNA" to "********",
        "Name" to "MR JARNAIL S SINGH",
        "Title" to "MR",
        "Forename" to "JARNAIL",
        "Othername" to "S",
        "Surname" to "SINGH",
        "InTouchEmail" to Constants.CALL_TRACE_EMAIL,
        "InTouchMobile" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
        "InTouchLandline" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
        "telephone" to Constants.CALL_TRACE_IN_TOUCH_MOBILE,
    ).toJsonObject()
    val allDataPlusLandLine = mapOf(
        "ClientId" to Constants.ACCOUNT_NUMBER,
        "SequenceNo" to "1",
        "CIG_URN" to "-**********",
        "DataDNA" to "********",
        "Name" to "MR JARNAIL S SINGH",
        "Title" to "MR",
        "Forename" to "JARNAIL",
        "Othername" to "S",
        "Surname" to "SINGH",
        "InTouchEmail" to Constants.CALL_TRACE_EMAIL,
        "InTouchLandline" to Constants.CALL_TRACE_IN_TOUCH_MOBILE
    ).toJsonObject()

    fun callTraceInDebitEmailDto(
        emailFieldValue: String? = "<EMAIL>",
        phone1FieldValue: String? = "***********",
        phone2FieldValue: String? = "***********",
        phone3FieldValue: String? = "***********",
    ):CallTraceInDebitEmailDto {
        return CallTraceInDebitEmailDto (firstName = "Geoff",
            customerEmail = "<EMAIL>",
            emailFieldValue = emailFieldValue,
            phone1FieldValue = phone1FieldValue,
            phone2FieldValue = phone2FieldValue,
            phone3FieldValue = phone3FieldValue,
            accountNumber = "********",
            accountBalance = "50",
            accountBalanceLow = "true",
            templateName = "call_trace_in_debit_email"
        )
    }

    fun transaction(
        accountNumber: String = Constants.ACCOUNT_NUMBER,
        status: CallTraceTransactionStatusEnum = CallTraceTransactionStatusEnum.RECEIVED,
        jsonSent: String = Constants.JSON_CSV_ROW,
        jsonReceived: String = jsonReceived(accountNumber)
    ): CallTraceTransaction
    {
        return CallTraceTransaction(
            jsonSent = jsonSent,
            sentAt = nowLocalDateTime,
            accountNumber = accountNumber,
            status = status,
            errorMessage = null,
            jsonReceived = jsonReceived,
            receivedAt = nowLocalDateTime,
        )
    }

    private fun jsonReceived(accountNumber: String) =
        Json.encodeToString(parsedOutputCsv.filter { CallTraceResponseAsJson(it).clientId() == accountNumber })
}
