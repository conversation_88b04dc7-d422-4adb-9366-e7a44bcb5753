package energy.so.customers.fixtures.calltrace

import energy.so.commons.model.enums.CallTraceAuditConfirmedEnum
import energy.so.commons.model.enums.CallTraceAuditGdprEnum
import energy.so.core.customers.server.calltrace.CallTraceAudit
import java.time.LocalDateTime
import java.time.LocalDateTime.now

object CallTraceAuditPrecannedData {
    val nowLocalDateTime = now().withHour(0).withSecond(0).withNano(0)

    object Constants {
        const val ACCOUNT_NUMBER = "1234"
    }

    fun audit(
        accountNumber: String = Constants.ACCOUNT_NUMBER,
        dataUpdated: String = "<EMAIL>",
        gdprType: CallTraceAuditGdprEnum = CallTraceAuditGdprEnum.EMAIL,
        confirmStatus: CallTraceAuditConfirmedEnum = CallTraceAuditConfirmedEnum.UNCONFIRMED,
        processed: LocalDateTime? = null,
    ): CallTraceAudit {
        return CallTraceAudit(
            accountNumber = accountNumber,
            dataUpdated = dataUpdated,
            gdprType = gdprType,
            confirmStatus = confirmStatus,
            processed = processed,
        )
    }
}
