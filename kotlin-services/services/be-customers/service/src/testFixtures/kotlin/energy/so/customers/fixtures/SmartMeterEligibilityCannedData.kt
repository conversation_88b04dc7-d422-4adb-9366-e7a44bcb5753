package energy.so.customers.fixtures

import energy.so.customers.billingaccounts.v2.BillingAccountMeter
import energy.so.customers.billingaccounts.v2.BillingAccountMeterPoint
import energy.so.customers.billingaccounts.v2.BillingAccountResponse
import energy.so.customers.billingaccounts.v2.MeterType
import energy.so.customers.v2.smartmeter.InstallationType
import energy.so.customers.v2.smartmeter.SMEligibilityReason
import energy.so.customers.v2.smartmeter.SMEligibilityResponse

object SmartMeterEligibilityCannedData {

    private val meterSMETS1: BillingAccountMeter = BillingAccountMeter.newBuilder().setType(MeterType.S1).build()
    private val meterSMETS2: BillingAccountMeter = BillingAccountMeter.newBuilder().setType(MeterType.S2).build()
    private val meterTrad: BillingAccountMeter = BillingAccountMeter.newBuilder().setType(MeterType.NSS).build()


    val validMeterPointTrad = BillingAccountMeterPoint.newBuilder()
        .addMeters(meterTrad)
        .build()

    val validMeterPointSMETS2 = BillingAccountMeterPoint.newBuilder()
        .addMeters(meterSMETS2)
        .build()

    val validMeterPointSMETS1 = BillingAccountMeterPoint.newBuilder()
        .addMeters(meterSMETS1)
        .build()

    val tooComplexMeterPoint = BillingAccountMeterPoint.newBuilder()
        .addMeters(meterTrad)
        .addMeters(meterSMETS2)
        .build()

    val accountResponseSwitchingAway = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(true)
        .build()

    val accountResponseMetersTooComplex = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(false)
        .addGasMeterpointData(tooComplexMeterPoint)
        .build()

    val accountResponseNoMeters = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(false)
        .build()

    val accountResponseOnlyGas = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(false)
        .addGasMeterpointData(validMeterPointTrad)
        .build()

    val accountResponseOnlyElec = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(false)
        .addElectricMeterpointData(validMeterPointTrad)
        .build()

    val accountResponseDualFuel = BillingAccountResponse
        .newBuilder()
        .setBillingAccountId(12345)
        .setSwitchingAway(false)
        .addElectricMeterpointData(validMeterPointTrad)
        .addGasMeterpointData(validMeterPointTrad)
        .build()

    val eligibilityResponseOnlyGas = SMEligibilityResponse.newBuilder().setGasMeterType(InstallationType.TRADITIONAL)
        .setElecMeterType(InstallationType.NOT_PRESENT).setReason(SMEligibilityReason.ELIGIBLE_GAS_EXCHANGE).build()

    val eligibilityResponseOnlyElec = SMEligibilityResponse.newBuilder().setGasMeterType(InstallationType.NOT_PRESENT)
        .setElecMeterType(InstallationType.TRADITIONAL).setReason(SMEligibilityReason.ELIGIBLE_ELEC_EXCHANGE).build()

    val eligibilityResponseDual = SMEligibilityResponse.newBuilder().setGasMeterType(InstallationType.TRADITIONAL)
        .setElecMeterType(InstallationType.TRADITIONAL).setReason(SMEligibilityReason.ELIGIBLE_DUAL_EXCHANGE).build()


}