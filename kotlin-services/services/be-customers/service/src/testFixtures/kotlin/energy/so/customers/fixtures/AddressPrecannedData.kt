package energy.so.customers.fixtures

import energy.so.assets.addresses.v2.postcode
import energy.so.core.customers.server.models.AddressMeterPointSupplier

object AddressPrecannedData {
    fun addressWithBillingAccountId(house: String, mpanMeterPointId: Long = 599, mprnMeterPointId: Long = 699): AddressMeterPointSupplier {
        return AddressMeterPointSupplier(
            address1 = "$house Hertingfordbury",
            address2 = "the town",
            address3 = null,
            address4 = "the county",
            postcode = TEST_POSTCODE.value,
            mpan = "**********",
            mpanMeterPointId = mpanMeterPointId,
            mprn = "**********",
            mprnMeterPointId = mprnMeterPointId,
        )
    }

    val TEST_POSTCODE = postcode { value = "SG14 2LG" }
    const val BILLING_ACCOUNT_ID = 12345L
}