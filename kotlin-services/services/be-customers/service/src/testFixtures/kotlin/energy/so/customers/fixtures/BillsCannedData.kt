package energy.so.customers.fixtures

import energy.so.commons.grpc.utils.toTimestamp
import energy.so.customers.fixtures.TestDataUtils.localDateTime
import energy.so.junifer.v2.bill
import java.time.LocalDateTime
import kotlin.random.Random.Default.nextInt

object BillsCannedData {

    val bill = bill {
        id = nextInt()
        billPeriodId = nextInt()
        number = "number"
        createdDate = localDateTime.minusDays(41).toTimestamp()
        status = "ACCEPTED"
        currency = "GBP"
        issueDate = LocalDateTime.now().minusDays(41).toTimestamp()
        balance = 10F
    }
}