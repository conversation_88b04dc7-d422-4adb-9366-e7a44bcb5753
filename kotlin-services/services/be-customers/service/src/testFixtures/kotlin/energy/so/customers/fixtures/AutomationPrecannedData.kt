package energy.so.customers.fixtures

import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.automation.v2.models.TaskResultResponseKt.taskResult
import energy.so.commons.automation.v2.models.taskResultResponse

val pubsubConfiguration = SubscriptionConfiguration(
    name = "subscription name",
    topic = "topic name",
    key = "key",
    topicKey = "topicKey"
)

val successfulResults = taskResultResponse {
    results.add(taskResult { data.add("1") })
    fields.add("ID")
}
