package energy.so.customers.fixtures

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

/** <AUTHOR> 29/08/2023 */
object TestDataUtils {

//    val offsetDateTime: OffsetDateTime =
//        OffsetDateTime.parse("2023-10-10T09:00:00Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME)

    val localDateTime: LocalDateTime = LocalDateTime.of(2023, 11, 10, 9, 0, 0)

    val offsetDateTime: OffsetDateTime =
        OffsetDateTime.of(localDateTime, ZoneOffset.ofHours(1))

    val localDate: LocalDate = localDateTime.toLocalDate()
}