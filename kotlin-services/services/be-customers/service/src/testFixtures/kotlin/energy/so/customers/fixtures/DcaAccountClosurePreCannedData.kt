package energy.so.customers.fixtures

import energy.so.ac.junifer.v1.accounts.Status
import energy.so.ac.junifer.v1.accounts.Ticket
import energy.so.ac.junifer.v1.accounts.copy
import energy.so.ac.junifer.v1.accounts.ticket
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.UpdateStatu
import energy.so.commons.model.enums.UpdateType
import energy.so.commons.model.tables.pojos.DcaClosureStrategyConfiguration
import energy.so.commons.model.tables.pojos.UpdatesFromDcaAuditLog
import energy.so.commons.queues.models.QueueMessage
import energy.so.core.customers.server.models.dca.DcaAccountClosureRequest
import energy.so.customers.billingaccounts.v2.BillingAccountResponse
import energy.so.dca.v2.createDCAConfigurationResponse
import java.time.LocalDateTime
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.JSON

val jsonFormat = Json { encodeDefaults = false; ignoreUnknownKeys = true }

val dcaAccClosureMessageUnrecoverable = """
            {
                "A": "********",
                "B": "",
                "C": "",
                "D": "",
                "E": "",
                "F": "",
                "G": "",
                "H": "",
                "I": "",
                "J": "",
                "K": "",
                "L": "",
                "M": "",
                "N": "LIQ",
                "O": "",
                "P": "",
                "Q": "",
                "R": ""
            }
        """.trimIndent()

val initialDbLogUnrecoverable = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "", // Keep this as empty string initially, as this data is in the raw message
    updateType = UpdateType.CLOSURE,
    status = UpdateStatu.PENDING,
    jsonRequestReceived = JSON.json(QueueMessage(data = dcaAccClosureMessageUnrecoverable).data),
    filename = null,
    dca = "Opos",
)

val successDbLogUnrecoverable = initialDbLogUnrecoverable.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json(jsonFormat.encodeToString(DcaAccountClosureRequest(
        accountNumber = "********",
        dcaRef = "",
        clientName = "",
        segment = "",
        customerName = "",
        closureDate = "",
        caseDateInstructed = "",
        initialDebt = "",
        interest = "",
        credit = "",
        writtenOff = "",
        paid = "",
        balance = "",
        closureType = "LIQ",
        closureReason = "",
        lastRPCDate = "",
        loanOrItemID = "",
        closeoutID = ""
    )))
)

val failureDbLogUnrecoverable = initialDbLogUnrecoverable.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.FAILURE,
    error = "dummy exception",
)

val dcaAccClosureMessageError = """
            {
                "A": "********",
                "B": "",
                "C": "",
                "D": "",
                "E": "",
                "F": "",
                "G": "",
                "H": "",
                "I": "",
                "J": "",
                "K": "",
                "L": "",
                "M": "",
                "N": "DUP",
                "O": "",
                "P": "",
                "Q": "",
                "R": ""
            }
        """.trimIndent()

val initialDbLogError = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "", // Keep this as empty string initially, as this data is in the raw message
    updateType = UpdateType.CLOSURE,
    status = UpdateStatu.PENDING,
    jsonRequestReceived = JSON.json(QueueMessage(data = dcaAccClosureMessageError).data),
    filename = null,
    dca = "Opos",
)

val successDbLogError = initialDbLogError.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json(jsonFormat.encodeToString(DcaAccountClosureRequest(
        accountNumber = "********",
        dcaRef = "",
        clientName = "",
        segment = "",
        customerName = "",
        closureDate = "",
        caseDateInstructed = "",
        initialDebt = "",
        interest = "",
        credit = "",
        writtenOff = "",
        paid = "",
        balance = "",
        closureType = "DUP",
        closureReason = "",
        lastRPCDate = "",
        loanOrItemID = "",
        closeoutID = ""
    )))
)

val dcaAccClosureMessageSuccess = """
            {
                "A": "********",
                "B": "",
                "C": "",
                "D": "",
                "E": "",
                "F": "",
                "G": "",
                "H": "",
                "I": "",
                "J": "",
                "K": "",
                "L": "",
                "M": "",
                "N": "SIF",
                "O": "",
                "P": "",
                "Q": "",
                "R": ""
            }
        """.trimIndent()

val initialDbLogSuccess = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "", // Keep this as empty string initially, as this data is in the raw message
    updateType = UpdateType.CLOSURE,
    status = UpdateStatu.PENDING,
    jsonRequestReceived = JSON.json(QueueMessage(data = dcaAccClosureMessageSuccess).data),
    filename = null,
    dca = "Opos",
)

val successDbLogSuccess = initialDbLogSuccess.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json(jsonFormat.encodeToString(DcaAccountClosureRequest(
        accountNumber = "********",
        dcaRef = "",
        clientName = "",
        segment = "",
        customerName = "",
        closureDate = "",
        caseDateInstructed = "",
        initialDebt = "",
        interest = "",
        credit = "",
        writtenOff = "",
        paid = "",
        balance = "",
        closureType = "SIF",
        closureReason = "",
        lastRPCDate = "",
        loanOrItemID = "",
        closeoutID = ""
    )))
)

val dcaAccClosureMessageDeceased = """
            {
                "A": "********",
                "B": "",
                "C": "",
                "D": "",
                "E": "",
                "F": "",
                "G": "",
                "H": "",
                "I": "",
                "J": "",
                "K": "",
                "L": "",
                "M": "",
                "N": "Deceased",
                "O": "",
                "P": "",
                "Q": "",
                "R": ""
            }
        """.trimIndent()

val initialDbLogDeceased = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "", // Keep this as empty string initially, as this data is in the raw message
    updateType = UpdateType.CLOSURE,
    status = UpdateStatu.PENDING,
    jsonRequestReceived = JSON.json(QueueMessage(data = dcaAccClosureMessageDeceased).data),
    filename = null,
    dca = "Opos",
)

val successDbLogDeceased = initialDbLogDeceased.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json(jsonFormat.encodeToString(DcaAccountClosureRequest(
        accountNumber = "********",
        dcaRef = "",
        clientName = "",
        segment = "",
        customerName = "",
        closureDate = "",
        caseDateInstructed = "",
        initialDebt = "",
        interest = "",
        credit = "",
        writtenOff = "",
        paid = "",
        balance = "",
        closureType = "Deceased",
        closureReason = "",
        lastRPCDate = "",
        loanOrItemID = "",
        closeoutID = ""
    )))
)

val dcaAccClosureMessageRecall = """
            {
                "A": "********",
                "B": "",
                "C": "",
                "D": "",
                "E": "",
                "F": "",
                "G": "",
                "H": "",
                "I": "",
                "J": "",
                "K": "",
                "L": "",
                "M": "",
                "N": "RTC",
                "O": "",
                "P": "",
                "Q": "",
                "R": ""
            }
        """.trimIndent()

val initialDbLogRecall = UpdatesFromDcaAuditLog(
    juniferAccountNumber = "", // Keep this as empty string initially, as this data is in the raw message
    updateType = UpdateType.CLOSURE,
    status = UpdateStatu.PENDING,
    jsonRequestReceived = JSON.json(QueueMessage(data = dcaAccClosureMessageRecall).data),
    filename = null,
    dca = "Opos",
)

val successDbLogRecall = initialDbLogRecall.copy(
    juniferAccountNumber = "********",
    status = UpdateStatu.SUCCESS,
    updatedData = JSON.json(jsonFormat.encodeToString(DcaAccountClosureRequest(
        accountNumber = "********",
        dcaRef = "",
        clientName = "",
        segment = "",
        customerName = "",
        closureDate = "",
        caseDateInstructed = "",
        initialDebt = "",
        interest = "",
        credit = "",
        writtenOff = "",
        paid = "",
        balance = "",
        closureType = "RTC",
        closureReason = "",
        lastRPCDate = "",
        loanOrItemID = "",
        closeoutID = ""
    )))
)


val aDcaAccountClosureConfig =
    DcaClosureStrategyConfiguration(
        id = 1,
        closureCode = "LIQ",
        closureDescription = "Liquidation",
        route = "Unrecoverable",
        createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
    )

val aClosureConfigUnrecoverable = DcaClosureStrategyConfiguration(
    id = 1,
    closureCode = "liq",
    closureDescription = "Liquidation",
    route = "unrecoverable",
    createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
)



val aClosureConfigError = DcaClosureStrategyConfiguration(
    id = 1,
    closureCode = "dup",
    closureDescription = "Duplicate",
    route = "error",
    createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
)

val aClosureConfigSuccess = DcaClosureStrategyConfiguration(
    id = 1,
    closureCode = "sif",
    closureDescription = "Settlement in Full",
    route = "success",
    createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
)

val aClosureConfigDeceased = DcaClosureStrategyConfiguration(
    id = 1,
    closureCode = "deceased",
    closureDescription = "Deceased",
    route = "Deceased",
    createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
)

val aClosureConfigRecall = DcaClosureStrategyConfiguration(
    id = 1,
    closureCode = "rtc",
    closureDescription = "Return At Client Request",
    route = "recall",
    createdAt = LocalDateTime.of(2020, 1, 1, 1, 0, 0)
)

val openDcaTicket = ticket {
    id = 1
    status = Status.Open
    keyIdentifier = "OPOS-5582"
    ticketDefinitionCode = "Opos"
    summary = "Assigned"
    createdDttm = LocalDateTime.of(2025, 1, 10, 1, 0, 0)
        .toTimestamp()
}

val closedDcaTicket = ticket {
    id = 2
    status = Status.Closed
    keyIdentifier = "COEO-5580"
    ticketDefinitionCode = "Coeo"
    summary = "UEP - Uneconomic to Pursue"
    createdDttm = LocalDateTime.of(2025, 1, 4, 1, 0, 0)
        .toTimestamp()
}

val closePreDcaNewTicket = ticket {
    id = 2
    status = Status.Closed
    keyIdentifier = "PreDCANew-01"
    ticketDefinitionCode = "PreDCANew"
    summary = "Assigned to DCA"
    createdDttm = LocalDateTime.of(2025, 1, 2, 1, 0, 0)
        .toTimestamp()
}

val openPreDcaRecallTicket = closePreDcaNewTicket.copy {
    status = Status.Open
    createdDttm = LocalDateTime.of(2025, 1, 11, 1, 0, 0)
        .toTimestamp()
    ticketDefinitionCode = "PreDCA-Recall"
}

val aValidBillingAccountResponse = BillingAccountResponse.newBuilder()
    .setJuniferAccountId(100L)
    .setBillingAccountId(200)
    .build()

val aDcaConfigResponse = createDCAConfigurationResponse {
    id = 1
    dcaName = "Coeo"
    weight = 1
    iterationMin = 1
    iterationMax = null.toNullableInt64()
    ticketDefinitionCode = "Coeo"
}

