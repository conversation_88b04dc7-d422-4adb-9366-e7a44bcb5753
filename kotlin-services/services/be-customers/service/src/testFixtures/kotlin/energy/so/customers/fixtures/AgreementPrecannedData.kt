package energy.so.customers.fixtures

import com.google.protobuf.NullValue
import energy.so.assets.meter.v2.MeterType
import energy.so.assets.meterPoints.v2.MeterPointType
import energy.so.assets.meterPoints.v2.SimplifiedSupplyStatus
import energy.so.assets.meterPoints.v2.meterPointsResponse
import energy.so.assets.register.v2.register
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.AgreementType
import energy.so.commons.model.enums.BillDeliveryMethod
import energy.so.commons.model.enums.BillingAccountClass
import energy.so.commons.model.enums.BillingAccountType
import energy.so.commons.model.enums.BillingCycle
import energy.so.commons.model.enums.CustomerClass
import energy.so.commons.model.enums.CustomerType
import energy.so.commons.model.enums.ProductAccountType
import energy.so.commons.model.tables.pojos.Agreement
import energy.so.commons.model.tables.pojos.BillingAccount
import energy.so.commons.model.tables.pojos.BillingAccountSetting
import energy.so.commons.model.tables.pojos.Customer
import energy.so.commons.model.tables.pojos.CustomerMetadata
import energy.so.commons.model.tables.pojos.CustomerSetting
import energy.so.commons.model.tables.pojos.ProductAccount
import energy.so.commons.model.tables.pojos.ProductAccountAgreementRel
import energy.so.commons.nullableString
import energy.so.core.customers.server.extensions.objectToStruct
import energy.so.core.customers.server.models.CustomerEnrolmentBroker
import energy.so.core.customers.server.models.Renewal
import energy.so.core.customers.server.models.RenewalStatus
import energy.so.core.customers.server.models.toProto
import energy.so.customers.agreements.v2.agreement
import energy.so.customers.agreements.v2.agreementsList
import energy.so.customers.agreements.v2.getActiveAgreementsByBillingAccountIdResponse
import energy.so.customers.agreements.v2.meterPoint
import energy.so.customers.agreements.v2.productVariant
import energy.so.customers.client.models.BillingAccountSettings
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.ContactType
import energy.so.customers.client.models.Title
import energy.so.customers.fixtures.BobsData.billingAccountId
import energy.so.customers.fixtures.BobsData.billingAccountId2
import energy.so.customers.fixtures.BobsData.nowLocalDateTime
import energy.so.customers.fixtures.EnergyUsagePrecannedData.energyUsageRequest
import energy.so.customers.renewals.v2.createRenewalRequest
import energy.so.customers.renewals.v2.getLastAgreementRenewByBillingIdsResponse
import energy.so.customers.renewals.v2.lastAgreementRenew
import energy.so.customers.renewals.v2.paymentSchedule
import energy.so.customers.renewals.v2.unitRates
import energy.so.payments.v2.modifyPaymentScheduleRequest
import energy.so.products.v2.GspGroup
import energy.so.products.v2.ProductType
import energy.so.products.v2.ProductVariantType
import energy.so.products.v2.RateType
import energy.so.products.v2.copy
import energy.so.products.v2.getProductDetailsResponse
import energy.so.products.v2.getProductsByProductVariantIdsResponse
import energy.so.products.v2.getProductsResponse
import energy.so.products.v2.product
import energy.so.products.v2.productDetails
import energy.so.products.v2.productWithAssociatedProductVariant
import energy.so.products.v2.tariffRate
import java.time.LocalDate
import java.time.LocalDateTime
import org.jooq.JSON
import energy.so.commons.model.tables.pojos.Renewal as JooqRenewal

const val BOBS_AGREEMENT_ID = 123L
const val BOBS_AGREEMENT_ID_2 = 456L
const val BOBS_BILLING_ACCOUNT_ID = 678L
const val BOBS_BILLING_ACCOUNT_SETTINGS_ID = 670L
const val BOBS_CUSTOMER_ID = 234L
const val BOBS_SETTING_ID = 123L
const val CONTACT_ID = 12L
const val CUSTOMER_METADATA_ID = 123L
const val CUSTOMER_METADATA_ID_3 = 3L
const val PRODUCT_ACCOUNT_ID = 456L

val localDateTimeNow = LocalDateTime.now().withNano(0).withSecond(0)

object AgreementPrecannedData {

    const val METER_POINT_ID = 412L
    const val METER_POINT_ID_2 = 413L
    const val METER_ID = 415L
    const val METER_ID_2 = 416L
    const val METER_IDENTIFIER_1 = "23L3005892"
    const val METER_IDENTIFIER_2 = "73L3005892"

    private val AGREEMENT_NUMBER = "12345"
    val QUOTE_ID = 321L

    val bobsAgreementsResponse = agreement {
        id = BOBS_AGREEMENT_ID
        number = AGREEMENT_NUMBER
        fromDate = localDateTimeNow.toTimestamp()
        toDate = localDateTimeNow.plusDays(10).toNullableTimestamp()
        quoteId = QUOTE_ID
        createdAt = localDateTimeNow.toTimestamp()
        updatedAt = localDateTimeNow.toTimestamp()
        type = energy.so.core.customers.server.models.AgreementType.FIXED_12_MONTHS.toSiblingEnum()
    }

    val bobsGetAgreementsByBillingAccountIdResponse = getActiveAgreementsByBillingAccountIdResponse {
        agreements.add(bobsAgreementsResponse)
    }

    val noAssetContents = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "1",
                        "meterpoints": [
                           
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val oneAssetContents = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "1",
                        "meterpoints": [
                            {
                                "id":412
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val oneAssetContentsProductB = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "2",
                        "meterpoints": [
                            {
                                "id":412
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val oneAssetContentsBProductB = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "2",
                        "meterpoints": [
                            {
                                "id":413
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val twoAssetsContents = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "1",
                        "meterpoints": [
                            {
                                "id":412
                            },
                            {
                                "id":413
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val dualFuelContent = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "1",
                        "meterpoints": [
                            {
                                "id":412
                            }
                        ]
                    },
                    {
                        "id": "2",
                        "meterpoints": [
                            {
                                "id":413
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )
    val oneAssetContentsB = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "2",
                        "meterpoints": [
                            {
                                "id":413
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val oneAssetContentsC = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "3",
                        "meterpoints": [
                            {
                                "id":414
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )


    val twoAssetsContentsB = JSON.json(
        """
            {
                "productvariants": [
                    {
                        "id": "4",
                        "meterpoints": [
                            {
                                "id":788
                            },
                            {
                                "id":789
                            }
                        ]
                    }
                ]
            }""".trimIndent()
    )

    val bobsAgreement = energy.so.core.customers.server.models.Agreement(
        id = BOBS_AGREEMENT_ID,
        number = AGREEMENT_NUMBER,
        from = localDateTimeNow,
        to = localDateTimeNow.plusDays(10),
        cancelled = false,
        createdAt = localDateTimeNow,
        deleted = null,
        quoteId = QUOTE_ID,
        contents = oneAssetContents,
        updatedAt = localDateTimeNow,
        type = energy.so.core.customers.server.models.AgreementType.FIXED_12_MONTHS,
        ordered = LocalDate.now(),
    )

    val bobsElecFlexAgreement = energy.so.core.customers.server.models.Agreement(
        id = BOBS_AGREEMENT_ID,
        number = AGREEMENT_NUMBER,
        from = localDateTimeNow.minusMinutes(5),
        to = null,
        cancelled = false,
        createdAt = localDateTimeNow.minusMinutes(5),
        deleted = null,
        quoteId = null,
        ordered = localDateTimeNow.toLocalDate(),
        contents = oneAssetContents,
        updatedAt = localDateTimeNow.minusMinutes(5),
        type = energy.so.core.customers.server.models.AgreementType.OPEN_ENDED,
        renewal = null
    )

    val bobsGasFlexAgreement = bobsElecFlexAgreement.copy(
        id = BOBS_AGREEMENT_ID_2,
        number = AGREEMENT_NUMBER + 1,
        contents = oneAssetContentsBProductB
    )

    val bobsAgreement90days = energy.so.core.customers.server.models.Agreement(
        id = BOBS_AGREEMENT_ID,
        number = AGREEMENT_NUMBER,
        from = localDateTimeNow,
        to = localDateTimeNow.plusDays(90),
        cancelled = false,
        createdAt = localDateTimeNow,
        deleted = null,
        quoteId = QUOTE_ID,
        contents = oneAssetContents,
        updatedAt = localDateTimeNow,
        type = energy.so.core.customers.server.models.AgreementType.FIXED_12_MONTHS,
        ordered = LocalDate.now(),
    )

    val bobsJooqAgreement = Agreement(
        id = BOBS_AGREEMENT_ID,
        number = "12345",
        fromDate = localDateTimeNow,
        toDate = localDateTimeNow.plusDays(1),
        createdAt = localDateTimeNow,
        deleted = null,
        quoteId = 321L,
        contents = oneAssetContents,
        updatedAt = localDateTimeNow,
        type = AgreementType.FIXED_12_MONTHS,
        cancelled = false,
        renewalId = null,
        ordered = LocalDateTime.now(),
    )

    val bobsJooqAgreement2 = Agreement(
        id = BOBS_AGREEMENT_ID_2,
        number = "67890",
        fromDate = localDateTimeNow,
        toDate = localDateTimeNow.plusDays(1),
        createdAt = localDateTimeNow,
        deleted = null,
        quoteId = 321L,
        contents = oneAssetContents,
        updatedAt = localDateTimeNow,
        type = AgreementType.FIXED_12_MONTHS,
        cancelled = true,
        renewalId = null,
        ordered = LocalDateTime.now(),
    )

    val bobsAgreementModel = energy.so.core.customers.server.models.Agreement.fromJooq(bobsJooqAgreement)
    val bobsAgreementModel2 = energy.so.core.customers.server.models.Agreement.fromJooq(bobsJooqAgreement2)

    val productVariantProto = productVariant {
        id = 1
        meterPoints.add(meterPoint { id = 412 })
    }

    val bobsAgreementProto = agreement {
        id = bobsJooqAgreement.id!!
        number = bobsJooqAgreement.number!!
        fromDate = bobsJooqAgreement.fromDate!!.toTimestamp()
        toDate = bobsJooqAgreement.toDate!!.toNullableTimestamp()
        createdAt = bobsJooqAgreement.createdAt!!.toTimestamp()
        quoteId = bobsJooqAgreement.quoteId!!
        contents.add(bobsJooqAgreement.contents!!.objectToStruct())
        updatedAt = bobsJooqAgreement.updatedAt!!.toTimestamp()
        type = bobsJooqAgreement.type!!.toSiblingEnum()
        productVariants.add(productVariantProto)
        meterPoints.add(meterPoint { id = 412 })
    }

    val bobsAgreementProto2 = agreement {
        id = bobsJooqAgreement2.id!!
        number = bobsJooqAgreement2.number!!
        fromDate = bobsJooqAgreement2.fromDate!!.toTimestamp()
        toDate = bobsJooqAgreement2.toDate!!.toNullableTimestamp()
        createdAt = bobsJooqAgreement2.createdAt!!.toTimestamp()
        quoteId = bobsJooqAgreement2.quoteId!!
        contents.add(bobsJooqAgreement2.contents!!.objectToStruct())
        updatedAt = bobsJooqAgreement2.updatedAt!!.toTimestamp()
        type = bobsJooqAgreement2.type!!.toSiblingEnum()
        productVariants.add(productVariantProto)
        meterPoints.add(meterPoint { id = 412 })
    }


    val bobsGetAgreementsResponse = agreementsList {
        agreements.addAll(listOf(bobsAgreementProto, bobsAgreementProto2))
    }

    val bobsAgreementsForBillingAccountResponse = agreementsList {
        agreements.addAll(
            listOf(bobsAgreementModel.toProto(), bobsAgreementModel2.toProto())
        )
    }

    val bobsGetAgreementsEmptyResponse = agreementsList {
        emptyList<Agreement>()
    }

    val bobsJooqCustomer = Customer(
        id = BOBS_CUSTOMER_ID,
        number = "12345",
        createdAt = localDateTimeNow,
        firstName = "Bob",
        lastName = "Sinclair",
        customerSettingId = BOBS_SETTING_ID,
        customerMetadataId = CUSTOMER_METADATA_ID,
        deleted = null,
        updatedAt = localDateTimeNow,
        `class` = CustomerClass.CONSUMER_RESIDENTIAL,
        type = CustomerType.RESIDENTIAL
    )

    val jooqBillingAccountSettings = BillingAccountSetting(
        id = BOBS_BILLING_ACCOUNT_SETTINGS_ID,
        billDeliveryMethod = BillDeliveryMethod.BOTH,
        billingCycle = BillingCycle.ANNUALLY,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        deleted = null
    )

    val bobsJooqBillingAccount = BillingAccount(
        id = BOBS_BILLING_ACCOUNT_ID,
        customerId = BOBS_CUSTOMER_ID,
        type = BillingAccountType.CR_INVOICE,
        name = "Bob Billing Account",
        number = "12345",
        currency = "GBP",
        `class` = BillingAccountClass.INVOICE,
        fromDate = localDateTimeNow,
        toDate = localDateTimeNow,
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        settingsId = BOBS_BILLING_ACCOUNT_SETTINGS_ID
    )

    val contact = Contact(
        id = CONTACT_ID,
        firstName = "Joe",
        lastName = "Luck",
        email = "test@asd.c",
        type = ContactType.BUSINESS,
        title = Title.MR
    )

    val billingAccountSettings = BillingAccountSettings(
        id = BOBS_BILLING_ACCOUNT_SETTINGS_ID,
        billDeliveryMethod = energy.so.customers.client.models.BillDeliveryMethod.BOTH,
        billingCycle = energy.so.customers.client.models.BillingCycle.ANNUALLY,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        deleted = null
    )

    val billingAccountModel = energy.so.core.customers.server.models.BillingAccount(
        id = BOBS_BILLING_ACCOUNT_ID,
        customerId = BOBS_CUSTOMER_ID,
        name = "bobsBillingAccount",
        number = "123",
        `class` = energy.so.core.customers.server.models.BillingAccountClass.COST_STATEMENT,
        type = energy.so.core.customers.server.models.BillingAccountType.CB_INVOICE,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        currency = "GBP",
        to = localDateTimeNow,
        from = localDateTimeNow,
        primaryContact = contact,
        settings = billingAccountSettings
    )

    val bobsJooqProductAccount = ProductAccount(
        id = PRODUCT_ACCOUNT_ID,
        billingAccountId = BOBS_BILLING_ACCOUNT_ID,
        type = ProductAccountType.ELECTRICITY,
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow
    )

    val bobsJooqProductAccountAgreementRel = ProductAccountAgreementRel(
        id = 123L,
        productAccountId = PRODUCT_ACCOUNT_ID,
        agreementId = BOBS_AGREEMENT_ID
    )

    val bobsJooqCustomerSetting = CustomerSetting(
        id = BOBS_SETTING_ID,
        marketingOptIn = false,
        smartMeterInterest = false,
        referralsEnabled = true,
        darkMode = false,
    )

    val bobsJooqCustomerMetadata = CustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        psrRequestedAt = localDateTimeNow,
        lastTicketAt = localDateTimeNow,
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
    )

    val bobsRenewal = Renewal(
        id = 1,
        status = RenewalStatus.IN_PROGRESS,
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        broker = CustomerEnrolmentBroker.SO_ENERGY
    )

    val bobsJooqRenewal = JooqRenewal(
        id = 1,
        status = "IN_PROGRESS",
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        billingAccountId = BOBS_BILLING_ACCOUNT_ID,
        broker = CustomerEnrolmentBroker.SO_ENERGY.name
    )

    val renewalRequest = createRenewalRequest {
        accountId = 1L
        creditExitFee = true
        paymentSchedule = paymentSchedule {
            amount = 17.00
            seasonal = false
            estimatedAnnualCost = 76.65
        }
        newAgreements.add(energy.so.customers.renewals.v2.agreement {
            meterPointIdentifier = "412"
            annualUsage = 24907.00
            productCode = "G1R-SPBI-12M-C"
            creditExitFee = 5.00
            startDate = LocalDate.now().toString()
            unitRates = unitRates {
                day = 0.21
                night = 0.00
                standard = 0.21
                standingCharge = 0.21
            }
        })
    }
    val evRenewalRequest = createRenewalRequest {
        accountId = 1L
        creditExitFee = true
        paymentSchedule = paymentSchedule {
            amount = 307.00
            seasonal = false
            estimatedAnnualCost = 3689.00
        }
        newAgreements.add(energy.so.customers.renewals.v2.agreement {
            meterPointIdentifier = "412"
            productCode = "E2R-STTE-12M-C"
            startDate = LocalDate.now().toString()
            creditExitFee = 5.00
        })
        tariffName = "So Test EV".toNullableString()
    }
    val renewalRequestNoDetails = createRenewalRequest {
        accountId = 1L
        creditExitFee = true
        paymentSchedule = paymentSchedule {
            amount = 17.00
            seasonal = false
            estimatedAnnualCost = 76.65
        }
        newAgreements.add(energy.so.customers.renewals.v2.agreement {
            meterPointIdentifier = "*********"
            annualUsage = 24907.00
            productCode = "G1R-SPBI-12M-C"
            creditExitFee = 5.00
            startDate = LocalDate.now().toString()
            unitRates = unitRates {
                day = 0.21
                night = 0.00
                standard = 0.21
                standingCharge = 0.21
            }
        })
    }

    val billingIdsToAgreementRenewTimestamp = mapOf(
        billingAccountId to nowLocalDateTime,
        billingAccountId2 to nowLocalDateTime,
    )

    private val lastAgreementRenew1 = lastAgreementRenew {
        billingAccountId = BobsData.billingAccountId
        lastAgreementRenew = nowLocalDateTime.toNullableTimestamp()
    }

    private val lastAgreementRenew2 = lastAgreementRenew {
        billingAccountId = billingAccountId2
        lastAgreementRenew = nowLocalDateTime.toNullableTimestamp()
    }

    val getLastAgreementRenewByBillingIdsResponse =
        getLastAgreementRenewByBillingIdsResponse {
            lastRenewals.addAll(
                listOf(
                    lastAgreementRenew1,
                    lastAgreementRenew2,
                )
            )
        }

    val oneProductDetailResponse = getProductDetailsResponse {
        results.add(productDetails {
            type = ProductType.ELECTRICITY_PRODUCT
            productVariantId = "1"
            code = "E2R-SOOC-040216"
        })
    }

    val oneProductDetailResponseB = getProductDetailsResponse {
        results.add(productDetails {
            type = ProductType.GAS_PRODUCT
            productVariantId = "2"
            code = "G1R-SGRN-300924"
        })
    }

    val elecSingleFlexGetProductDetail = productDetails {
        type = ProductType.ELECTRICITY_PRODUCT
        productVariantId = "1"
        code = "E1R-SOOC-040216"
        productVariantFromDt = energyUsageRequest.fromDate
        gspGroup = GspGroup._A
        fuel = ProductVariantType.ELECTRICITY_SINGLE
        standardRate = 0.0
        dayRate = 22.0
        nightRate = 0.0
        standingCharge = 0.0
        directDebit = true
        rateType = RateType.STANDARD
        variable = true
    }

    val elecSingleFlexGetProductDetailsResponse = getProductDetailsResponse {
        results.add(elecSingleFlexGetProductDetail)
    }

    val elecSingleFixedDeanProductDetail = productDetails {
        type = ProductType.ELECTRICITY_PRODUCT
        productVariantId = "100"
        code = "E1R-SPDE-12M-C"
        productVariantFromDt = energyUsageRequest.fromDate
        gspGroup = GspGroup._A
        fuel = ProductVariantType.ELECTRICITY_SINGLE
        standardRate = 0.0
        dayRate = 30.0
        nightRate = 0.0
        standingCharge = 0.12
        directDebit = true
        rateType = RateType.STANDARD
        variable = false
    }

    val elecSingleFixedCypressProductDetail = elecSingleFixedDeanProductDetail.copy {
        productVariantId = "101"
        code = "E1R-SPCY-12M-C"
        dayRate = 29.5
    }

    val elecDualFixedDeanProductDetail = elecSingleFixedDeanProductDetail.copy {
        productVariantId = "102"
        code = "E2R-SPDE-12M-C"
        fuel = ProductVariantType.ELECTRICITY_MULTIPLE
        nightRate = 15.0
        rateType = RateType.ECO7
    }

    val elecDualFixedCypressProductDetail = elecDualFixedDeanProductDetail.copy {
        productVariantId = "103"
        code = "E2R-SPCY-12M-C"
        nightRate = 14.5
    }

    val gasSingleFlexGetProductDetail = productDetails {
        type = ProductType.GAS_PRODUCT
        productVariantId = "2"
        code = "G1R-SOOC-040216"
        productVariantFromDt = energyUsageRequest.fromDate
        gspGroup = GspGroup._A
        fuel = ProductVariantType.GAS_SINGLE
        standardRate = 22.0
        dayRate = 0.0
        nightRate = 0.0
        standingCharge = 0.0
        directDebit = true
        rateType = RateType.STANDARD
        variable = true
    }

    val bobsElecSingleDeanAgreement = energy.so.core.customers.server.models.Agreement(
        id = 9999L,
        number = "9999",
        from = localDateTimeNow.minusMinutes(5),
        to = null,
        cancelled = false,
        createdAt = localDateTimeNow.minusMinutes(5),
        deleted = null,
        quoteId = null,
        ordered = localDateTimeNow.toLocalDate(),
        contents = JSON.json("""
            {
                "productvariants": [
                    {
                        "id": ${elecSingleFixedDeanProductDetail.productVariantId},
                        "meterpoints": [
                            {
                                "id":$METER_POINT_ID
                            }
                        ]
                    }
                ]
            }""".trimIndent()),
        updatedAt = localDateTimeNow.minusMinutes(5),
        type = energy.so.core.customers.server.models.AgreementType.FIXED_12_MONTHS,
        renewal = null
    )

    val bobsElecDualDeanAgreement = bobsElecSingleDeanAgreement.copy(
        contents = JSON.json("""
        {
            "productvariants": [
                {
                    "id": ${elecDualFixedDeanProductDetail.productVariantId},
                    "meterpoints": [
                        {
                            "id":$METER_POINT_ID
                        }
                    ]
                }
            ]
        }""".trimIndent())
    )

    val gasSingleFlexGetProductDetailsResponse = getProductDetailsResponse {
        results.add(gasSingleFlexGetProductDetail)
    }

    val dualFuelSoFlexGetProductDetailsResponse = getProductDetailsResponse {
        results.addAll(listOf(elecSingleFlexGetProductDetail, gasSingleFlexGetProductDetail))
    }

    val elecDualFlexGetProductDetail = elecSingleFlexGetProductDetail.copy {
        type = ProductType.ELECTRICITY_PRODUCT
        code = "E2R-SOOC-040216"
        fuel = ProductVariantType.ELECTRICITY_MULTIPLE
        standardRate = 0.0
        dayRate = 24.0
        nightRate = 12.0
        standingCharge = 0.0
        directDebit = true
        rateType = RateType.ECO7
    }

    val dualFuelSoFlexElecMultiGetProductDetailsResponse = getProductDetailsResponse {
        results.addAll(
            listOf(
                elecDualFlexGetProductDetail,
                gasSingleFlexGetProductDetail
            )
        )
    }

    val evTariffRates = listOf(
        tariffRate {
            rateName = "SO EV Peak".toNullableString()
            rateValue = 25.0.toNullableDouble()
            fromHour = "0500".toNullableString()
            toHour = "0000".toNullableString()
        },
        tariffRate {
            rateName = "SO EV Off-Peak".toNullableString()
            rateValue = 13.0.toNullableDouble()
            fromHour = "0000".toNullableString()
            toHour = "0500".toNullableString()
        }
    )

    val evTariffRateNames = evTariffRates.mapNotNull { it.rateName.getValueOrNull() }
        .distinct()

    val elecDualBasingEvGetProductDetail = elecSingleFlexGetProductDetail.copy {
        productVariantId = "500"
        type = ProductType.ELECTRICITY_PRODUCT
        code = "E2R-SBSE-12M-C"
        fuel = ProductVariantType.ELECTRICITY_SINGLE
        standardRate = 0.0
        dayRate = 25.0
        nightRate = 0.0
        standingCharge = 0.0
        directDebit = true
        tariffRates.addAll(evTariffRates)
        rateType = RateType.TOU
    }

    val bobsElecDualBasingEvAgreement = bobsElecSingleDeanAgreement.copy(
        contents = JSON.json("""
        {
            "productvariants": [
                {
                    "id": ${elecDualBasingEvGetProductDetail.productVariantId},
                    "meterpoints": [
                        {
                            "id":$METER_POINT_ID
                        }
                    ]
                }
            ]
        }""".trimIndent())
    )

    val dualFuelEvElecMultiGetProductDetailsResponse = getProductDetailsResponse {
        results.addAll(
            listOf(
                elecDualBasingEvGetProductDetail,
                gasSingleFlexGetProductDetail
            )
        )
    }

    val touTariffRates = listOf(
        tariffRate {
            rateName = "TOU Cheap".toNullableString()
            rateValue = 20.0.toNullableDouble()
            fromHour = "1100".toNullableString()
            toHour = "1930".toNullableString()
        },
        tariffRate {
            rateName = "TOU Standard".toNullableString()
            rateValue = 29.0.toNullableDouble()
            fromHour = "1930".toNullableString()
            toHour = "0200".toNullableString()
        },
        tariffRate {
            rateName = "TOU Standard".toNullableString()
            rateValue = 29.0.toNullableDouble()
            fromHour = "0500".toNullableString()
            toHour = "1100".toNullableString()
        },
        tariffRate {
            rateName = "TOU Super Cheap".toNullableString()
            rateValue = 17.0.toNullableDouble()
            fromHour = "0200".toNullableString()
            toHour = "0500".toNullableString()
        }
    )

    val touTariffRateNames = touTariffRates.mapNotNull { it.rateName.getValueOrNull() }
        .distinct()

    val elecDualInnovationGetProductDetail = elecSingleFlexGetProductDetail.copy {
        productVariantId = "600"
        type = ProductType.ELECTRICITY_PRODUCT
        code = "E3R-TOU1366177-300525"
        fuel = ProductVariantType.ELECTRICITY_SINGLE
        standardRate = 0.0
        dayRate = 29.0
        nightRate = 0.0
        standingCharge = 0.0
        directDebit = true
        tariffRates.addAll(touTariffRates)
        rateType = RateType.TOU
    }

    val bobsElecDualInnovationAgreement = bobsElecSingleDeanAgreement.copy(
        contents = JSON.json("""
        {
            "productvariants": [
                {
                    "id": ${elecDualInnovationGetProductDetail.productVariantId},
                    "meterpoints": [
                        {
                            "id":$METER_POINT_ID
                        }
                    ]
                }
            ]
        }""".trimIndent())
    )

    val dualFuelTouElecMultiGetProductDetailsResponse = getProductDetailsResponse {
        results.addAll(
            listOf(
                elecDualInnovationGetProductDetail,
                gasSingleFlexGetProductDetail
            )
        )
    }

    val oneSmartMeterPointResponse = meterPointsResponse {
        meterPoints.add(energy.so.assets.meterPoints.v2.meterPoint {
            id = METER_POINT_ID
            identifier = METER_POINT_ID.toString()
            simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
            meters.add(energy.so.assets.meter.v2.meter {
                type = MeterType.S1
                isSmartMeter = false
                isSmetsVersion2 = false
                registers.addAll(
                    listOf(
                        register { },
                        register { }
                    ))
            })
            ukGspGroup = "_A"
        })
    }

    val oneNoSmartMeterPointResponse = meterPointsResponse {
        meterPoints.add(energy.so.assets.meterPoints.v2.meterPoint {
            id = METER_POINT_ID
            identifier = METER_POINT_ID.toString()
            simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
            meters.add(energy.so.assets.meter.v2.meter {
                type = MeterType.CM
                isSmartMeter = false
                isSmetsVersion2 = false
            })
            ukGspGroup = "_A"
        })
    }

    val twoMeterPointMultiRegisterResponse = meterPointsResponse {
        meterPoints.addAll(
            listOf(
                energy.so.assets.meterPoints.v2.meterPoint {
                    id = METER_POINT_ID
                    identifier = METER_POINT_ID.toString()
                    type = MeterPointType.MPAN
                    simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
                    ukGspGroup = "_A"
                    meters.add(energy.so.assets.meter.v2.meter {
                        id = METER_ID
                        identifier = METER_IDENTIFIER_1
                        type = MeterType.S1
                        isSmartMeter = true
                        isSmetsVersion2 = false
                        registers.addAll(
                            listOf(
                                register {
                                    id = 1L
                                    identifier = REGISTER_IDENTIFIER_1.toNullableString()
                                    rateName = nullableString { null_ = NullValue.NULL_VALUE }
                                },
                                register {
                                    id = 2L
                                    identifier = REGISTER_IDENTIFIER_2.toNullableString()
                                    rateName = nullableString { null_ = NullValue.NULL_VALUE }
                                },
                            )
                        )
                    })
                },
                energy.so.assets.meterPoints.v2.meterPoint {
                    id = METER_POINT_ID_2
                    identifier = METER_POINT_ID_2.toString()
                    type = MeterPointType.MPRN
                    simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
                    ukGspGroup = "_A"
                    meters.add(energy.so.assets.meter.v2.meter {
                        id = METER_ID_2
                        identifier = METER_IDENTIFIER_2
                        type = MeterType.S1
                        isSmartMeter = true
                        isSmetsVersion2 = false
                        registers.addAll(
                            listOf(
                                register {
                                    id = 1L
                                    identifier = REGISTER_IDENTIFIER_1.toNullableString()
                                    rateName = nullableString { null_ = NullValue.NULL_VALUE }
                                }
                            )
                        )
                    })
                },
            )
        )
    }

    val oneMeterPointResponse = meterPointsResponse {
        meterPoints.addAll(
            listOf(
                energy.so.assets.meterPoints.v2.meterPoint {
                    id = METER_POINT_ID
                    identifier = METER_POINT_ID.toString()
                    simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
                    ukGspGroup = "_A"
                    meters.add(energy.so.assets.meter.v2.meter {
                        id = METER_ID
                        identifier = METER_IDENTIFIER_1
                        type = MeterType.S1
                        isSmartMeter = true
                        isSmetsVersion2 = false
                        registers.addAll(
                            listOf(
                                register {
                                    id = 1L
                                    identifier = REGISTER_IDENTIFIER_1.toNullableString()
                                    rateName = nullableString { null_ = NullValue.NULL_VALUE }
                                }
                            )
                        )
                    })
                }
            )
        )
    }


    val oneMeterPointResponseB = meterPointsResponse {
        meterPoints.add(energy.so.assets.meterPoints.v2.meterPoint {
            id = 413L
            identifier = "413"
            simplifiedSupplyStatus = SimplifiedSupplyStatus.ON_SUPPLY
        })
    }

    val oneProductByVariantResponse = getProductsByProductVariantIdsResponse {
        products.add(productWithAssociatedProductVariant {
            product = product {
                name = "Electricity"
            }
            productVariantId = 1
        })
    }

    val paymentScheduleRequest = modifyPaymentScheduleRequest {
        billingAccountId = renewalRequest.accountId
        seasonalPaymentFl = renewalRequest.paymentSchedule.seasonal
        amount = renewalRequest.paymentSchedule.amount
        fromDt =
            LocalDate.parse(renewalRequest.newAgreementsList.minOf { it.startDate })
                .atStartOfDay()
                .toTimestamp()
    }

    val oneProductResponse = getProductsResponse {
        products.add(
            product { type = ProductType.ELECTRICITY_PRODUCT })
    }
}
