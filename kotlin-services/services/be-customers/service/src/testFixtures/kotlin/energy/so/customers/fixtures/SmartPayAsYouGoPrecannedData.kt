package energy.so.customers.fixtures

import energy.so.ac.junifer.v1.accounts.meterPointChangeOfModeRequest
import energy.so.ac.junifer.v1.accounts.updateAccountToSmartPayAsYouGoRequest
import energy.so.assets.meterPoints.v2.mpxnSmartPayAsYouGoEligibility
import energy.so.assets.meterPoints.v2.mpxnSmartPayAsYouGoEligibilityRequest
import energy.so.assets.meterPoints.v2.mpxnSmartPayAsYouGoEligibilityResponse
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.SmartPayAsYouGoStatusType
import energy.so.core.customers.server.models.Agreement
import energy.so.core.customers.server.models.AgreementType
import energy.so.core.customers.server.smartpayasyougo.AgreementSmartPayAsYouGoState
import energy.so.core.customers.server.smartpayasyougo.SmartPayAsYouGo
import energy.so.core.customers.server.smartpayasyougo.SmartPayAsYouGoDetail
import energy.so.customers.smartpayasyougo.v2.accountIdentifier
import energy.so.customers.fixtures.AgreementPrecannedData.QUOTE_ID
import energy.so.customers.fixtures.AgreementPrecannedData.oneAssetContents
import energy.so.customers.smartpayasyougo.v2.mpxnSmartPayAsYouGoElegibility
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoEligibilityResponse
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoExecutionDateResponse
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoMeterPointRequest
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoMeterPointResponse
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoRequest
import energy.so.customers.smartpayasyougo.v2.smartPayAsYouGoResponse
import energy.so.products.v2.GspGroup
import energy.so.products.v2.ProductType
import energy.so.products.v2.productDetails
import java.time.LocalDateTime

const val BILLING_ACCOUNT_NUMBER = "***********"
const val ELEC_MP = "*************"
const val GAS_MP = "*********"
const val ELEC_PROD_REFERENCE = "E1R-PAYG-C"
const val GAS_PROD_REFERENCE = "G1R-PAYG-C"

val customerProtoSpaygResponse = smartPayAsYouGoEligibilityResponse {
    mpxnSmartPayAsYouGoElegibility.add(
        mpxnSmartPayAsYouGoElegibility {
            mpxnIdentifier = "1213232".toNullableString()
            isEligible = true
            ineligibleReason = null.toNullableString()
        }
    )
}

val customerProtoSpaygEDResponse = smartPayAsYouGoExecutionDateResponse {
    executionDate = LocalDateTime.now().plusDays(4).toTimestamp()
}

val completedSmartPayAsYouGoResponse = smartPayAsYouGoResponse {
    smartPayAsYouGoId = 1L
    results.add(
        smartPayAsYouGoMeterPointResponse {
            meterPointIdentifier = ELEC_MP
            status = energy.so.customers.smartpayasyougo.v2.SmartPayAsYouGoStatusType.COMPLETED
        }
    )
}

val customerProtoSpaygLimiterTriggeredResponse = smartPayAsYouGoEligibilityResponse {
    mpxnSmartPayAsYouGoElegibility.add(
        mpxnSmartPayAsYouGoElegibility {
            mpxnIdentifier = "1213232".toNullableString()
            isEligible = false
            ineligibleReason = "daily limiter reached".toNullableString()
        }
    )
}

val customerProtoSpaygIneligibleResponse = smartPayAsYouGoEligibilityResponse {
    mpxnSmartPayAsYouGoElegibility.add(
        mpxnSmartPayAsYouGoElegibility {
            mpxnIdentifier = "1213232".toNullableString()
            isEligible = false
            ineligibleReason = "not a smart meter".toNullableString()
        }
    )
}

val assetsProtoSpaygEligibilityResponse = mpxnSmartPayAsYouGoEligibilityResponse {
    mpxnSmartPayAsYouGoEligibility.add(mpxnSmartPayAsYouGoEligibility {
        mpxnIdentifier = "1213232".toNullableString()
        isEligible = true
        ineligibleReason = null.toNullableString()
    })
}

val assetsProtoSpaygIneligibleResponse = mpxnSmartPayAsYouGoEligibilityResponse {
    mpxnSmartPayAsYouGoEligibility.add(mpxnSmartPayAsYouGoEligibility {
        mpxnIdentifier = "1213232".toNullableString()
        isEligible = false
        ineligibleReason = "not a smart meter".toNullableString()
    })
}

val assetsProtoSpaygElegibilityRequest = mpxnSmartPayAsYouGoEligibilityRequest { meterPointIds.add(412) }

val completedSmartPayAsYouGoDetail = energy.so.commons.model.tables.pojos.SmartPayAsYouGoDetail(
    id = 1L,
    smartPayAsYouGoId = 1L,
    meterPointIdentifier = "************",
    productReference = "E1R-PAYG-12M-C",
    status = SmartPayAsYouGoStatusType.COMPLETED,
    executionDate = LocalDateTime.now().plusDays(3),
    creditTransferRatio = null,
    createdAt = LocalDateTime.now().minusDays(5),
    updatedAt = LocalDateTime.now().minusDays(5),
)
val smartPayAsYouGo = energy.so.commons.model.tables.pojos.SmartPayAsYouGo(
    id = 1L,
    billingAccountNumber = "********",
    createdAt = LocalDateTime.now().minusDays(5),
    updatedAt = LocalDateTime.now().minusDays(5),
    deleted = null
)


val executionDueAtNow = LocalDateTime.now()

val smartPayAsYouGoProtoRequest = smartPayAsYouGoRequest {
    accountIdentifier{
        number = BILLING_ACCOUNT_NUMBER
    }
    requests.addAll(
        listOf(
            smartPayAsYouGoMeterPointRequest {
                meterPointIdentifier = ELEC_MP
                productReference = ELEC_PROD_REFERENCE
                executionDate = executionDueAtNow.toTimestamp()
            },
            smartPayAsYouGoMeterPointRequest {
                meterPointIdentifier = GAS_MP
                productReference = GAS_PROD_REFERENCE
                executionDate = executionDueAtNow.toTimestamp()
            }
        )
    )
}

val invalidSmartPayAsYouGoProtoRequest = smartPayAsYouGoRequest {
    accountIdentifier{
        number = BILLING_ACCOUNT_NUMBER
    }
    requests.addAll(listOf())
}

val pendingSmartPayAsYouGo = SmartPayAsYouGo(
    id = 1L,
    billingAccountNumber = BILLING_ACCOUNT_NUMBER,
    details = mutableListOf(
        SmartPayAsYouGoDetail(
            meterPointIdentifier = ELEC_MP,
            productReference = ELEC_PROD_REFERENCE,
            executionDate = executionDueAtNow,
        ),
        SmartPayAsYouGoDetail(
            meterPointIdentifier = GAS_MP,
            productReference = GAS_PROD_REFERENCE,
            executionDate = executionDueAtNow,
        ),
    )
)

val updateAccountToSmartPayAsYouGoProtoRequest = updateAccountToSmartPayAsYouGoRequest {
    billingAccountNumber = BILLING_ACCOUNT_NUMBER
    requests.addAll(
        listOf(
            meterPointChangeOfModeRequest {
                meterPointIdentifier = ELEC_MP
                productReference = ELEC_PROD_REFERENCE
                executionDttm = executionDueAtNow.toTimestamp()
            },
            meterPointChangeOfModeRequest {
                meterPointIdentifier = GAS_MP
                productReference = GAS_PROD_REFERENCE
                executionDttm = executionDueAtNow.toTimestamp()
            },
        )
    )
}

val liveAgreement = Agreement(
    from = localDateTimeNow.withHour(0),
    to = localDateTimeNow.plusDays(10),
    id = 1,
    number = "1",
    type = AgreementType.OPEN_ENDED,
    quoteId = QUOTE_ID,
    contents = oneAssetContents,
    updatedAt = localDateTimeNow,
    createdAt = localDateTimeNow,
    deleted = null,
    cancelled = null,
)

val futureDatedAgreement = Agreement(
    from = localDateTimeNow.plusDays(10),
    to = localDateTimeNow.plusDays(20),
    id = 1,
    number = "1",
    type = AgreementType.OPEN_ENDED,
    quoteId = QUOTE_ID,
    contents = oneAssetContents,
    updatedAt = localDateTimeNow,
    createdAt = localDateTimeNow,
    deleted = null,
    cancelled = null,
)

val smartPayAsYouGoProductDetails = productDetails {
    type = ProductType.ELECTRICITY_PRODUCT
    productVariantId = "1"
    code = "E1R-PAYG-C"
    smartPayg = true
    gspGroup = GspGroup._A
}

val notSmartPayAsYouGoProductDetails = productDetails {
    type = ProductType.ELECTRICITY_PRODUCT
    productVariantId = "1"
    code = "E1R-PAYG-C"
    smartPayg = false
    gspGroup = GspGroup._A
}

val eligibleSmartPayAsYouGoMPXN = mpxnSmartPayAsYouGoEligibility {
    isEligible = true
    mpxnIdentifier = "123456789".toNullableString()
    ineligibleReason = null.toNullableString()
}

val notEligibleSmartPayAsYouGoMPXN = mpxnSmartPayAsYouGoEligibility {
    isEligible = false
    mpxnIdentifier = "987654321".toNullableString()
    ineligibleReason = "Just because".toNullableString()
}

val liveSmartPayAsYouGoState = AgreementSmartPayAsYouGoState(
    liveAgreement,
    smartPayAsYouGoProductDetails,
    eligibleSmartPayAsYouGoMPXN
)

val pendingSmartPayAsYouGoState = AgreementSmartPayAsYouGoState(
    futureDatedAgreement,
    smartPayAsYouGoProductDetails,
    eligibleSmartPayAsYouGoMPXN
)

val eligibleButNotSmartPayAsYouGoState = AgreementSmartPayAsYouGoState(
    liveAgreement,
    notSmartPayAsYouGoProductDetails,
    eligibleSmartPayAsYouGoMPXN
)

val notEligibleSmartPayAsYouGoState = AgreementSmartPayAsYouGoState(
    liveAgreement,
    notSmartPayAsYouGoProductDetails,
    notEligibleSmartPayAsYouGoMPXN
)