package energy.so.customers.fixtures

import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.v2.sync.OperationType
import energy.so.commons.v2.sync.syncResponse
import energy.so.customers.sync.v2.contactEntity
import energy.so.customers.sync.v2.contactEntityRequest
import energy.so.customers.sync.v2.copy


object ContactCannedData {
    const val ID_1 = 1L
    const val TYPE = "type"
    const val SYNC_TRANSACTION_ID = "0123456"
    val invalidContactEntityRequestPatch = contactEntityRequest {
        operationType = OperationType.PATCH
        contactEntity = contactEntity {}
    }

    val invalidContactEntityRequestCreate = contactEntityRequest {
        operationType = OperationType.CREATE
        contactEntity = contactEntity {}
    }

    val invalidContactEntityRequestDelete = contactEntityRequest {
        operationType = OperationType.DELETE
        contactEntity = contactEntity {}
    }

    val validContactEntityRequestCreate = contactEntityRequest {
        operationType = OperationType.CREATE
        contactEntity = contactEntity {
            id = ID_1.toNullableInt64()
            type = TYPE.toNullableString()
            syncTransactionId = SYNC_TRANSACTION_ID.toNullableString()
        }
    }

    val validContactEntityRequestPatch = validContactEntityRequestCreate.copy {
        operationType = OperationType.PATCH
    }

    val validContactEntityRequestDelete = validContactEntityRequestCreate.copy {
        operationType = OperationType.DELETE
    }

    val syncResponse = syncResponse {
        id = CustomVulnerabilityPrecannedData.ID_1.toNullableInt64()
    }
}
