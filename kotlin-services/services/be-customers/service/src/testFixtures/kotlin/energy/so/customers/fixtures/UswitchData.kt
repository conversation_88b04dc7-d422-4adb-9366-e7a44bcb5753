package energy.so.customers.fixtures

import energy.so.core.customers.server.config.USwitchConfig
import energy.so.core.customers.server.extensions.toUswitchDate
import energy.so.core.customers.server.models.message.AcknowledgeResponse
import energy.so.core.customers.server.models.message.BankDetails
import energy.so.core.customers.server.models.message.CorrespondenceAddress
import energy.so.core.customers.server.models.message.Customer
import energy.so.core.customers.server.models.message.Electricity
import energy.so.core.customers.server.models.message.Fuel
import energy.so.core.customers.server.models.message.Gas
import energy.so.core.customers.server.models.message.Message
import energy.so.core.customers.server.models.message.PafAddress
import energy.so.core.customers.server.models.message.Payload
import energy.so.core.customers.server.models.message.PhoneNumbers
import energy.so.core.customers.server.models.message.ProductInformation
import energy.so.core.customers.server.models.message.SupplementaryInformation
import energy.so.core.customers.server.models.message.SupplyAddress
import energy.so.core.customers.server.models.message.SwitchStatus
import energy.so.core.customers.server.models.message.SwitchStatus.IN_PROGRESS
import energy.so.core.customers.server.models.message.SwitchStatusReport
import energy.so.core.customers.server.models.message.UnreadResponse
import java.time.LocalDate

const val SWITCH_REF = "e5cc9aa0-739d-4b8f-8aa9-01dfff269b92"
const val MESSAGE_ID = "3f88f0ee-c10f-410f-8b02-b1062fa4a892"
const val JUNIFER_CUSTOMER_ID = 111L
const val CUSTOMER_ENROLLMENT_ID = 342L

val message = Message(
    action = "sale",
    messageId = MESSAGE_ID,
    switchRef = SWITCH_REF,
    version = "0.3",
    payload = Payload(
        switchedAt = 1548846440000.0,
        supplyAddress = SupplyAddress(
            pafAddress = PafAddress(
                doubleDependentLocality = "",
                town = "London",
                county = "",
                thoroughfare = "Copper Row",
                buildingNumber = "5",
                organisationName = "Uswitch",
                dependentThoroughfare = "",
                buildingName = "The Cooperage",
                poBox = "",
                dependentLocality = "",
                subBuildingName = "",
                postCode = "SE1 2LH"
            ),
            regionName = "London",
            regionNumber = "12"
        ),
        customerInformation = Customer(
            correspondenceAddress = CorrespondenceAddress(
                pafAddress = PafAddress(
                    doubleDependentLocality = "",
                    town = "London",
                    county = "",
                    thoroughfare = "Copper Row",
                    buildingNumber = "5",
                    organisationName = "Uswitch",
                    dependentThoroughfare = "",
                    buildingName = "The Cooperage",
                    poBox = "",
                    dependentLocality = "",
                    subBuildingName = "",
                    postCode = "SE1 2LH"
                )
            ), emailAddress = "<EMAIL>",
            title = "Dr",
            givenNames = "Hanry",
            familyName = "Blake",
            phoneNumbers = listOf(PhoneNumbers(phoneNumber = "0723412332343", phoneType = "Mobile"))
        ),
        productInformation = ProductInformation(
            electricity = Electricity(
                mpan = "2000004115448",
                isDualFuel = false,
                newTariffNightRatePence = 100.0,
                tariffId = "ABC-DEF-180910",
                currentPrimaryRatePence = 100.0,
                currentPersonalProjectionPence = 100.0,
                newTariffStandingChargePence = 100.0,
                newPaymentMethod = "Monthly Direct Debit",
                isEconomy7 = false,
                currentExitFeePence = 100.0,
                newTariffExitFeePence = 100.0,
                newTariffPrimaryRatePence = 100.0,
                currentSupplier = "Big Energy",
                meterType = "credit",
                currentAnnualNightConsumptionKwh = 100.0,
                currentPaymentMethod = "Monthly Direct Debit",
                currentNightRatePence = 100.0,
                currentAnnualConsumptionKwh = 100.0,
                currentStandingChargePence = 100.0,
                currentProductEndDate = "2022/11/11",
                quotedAnnualPricePence = 100.0,
                newTariffName = "ABC-DEF-180910",
                currentTariffName = "Standard (Variable)",
                currentAnnualDayConsumptionKwh = 100.0
            ),
            gas = Gas(
                isDualFuel = true,
                tariffId = "ABC-DEF-180910",
                currentPersonalProjectionPence = 12000.0,
                newTariffStandingChargePence = 20.445,
                newPaymentMethod = "Monthly Direct Debit",
                newTariffPrimaryRatePence = 3.7345,
                currentSupplier = "Big Energy",
                meterType = "credit",
                currentPaymentMethod = "Monthly Direct Debit",
                currentAnnualConsumptionKwh = 28923.0,
                mprn = "**********",
                quotedAnnualPricePence = 115275.0,
                newTariffName = "Fixed Tariff",
                currentTariffName = "Standard (Variable)",
                currentExitFeePence = 100.0,
                currentStandingChargePence = 200.0,
                currentPrimaryRatePence = 300.9,
                newTariffExitFeePence = 10.0,
                currentProductEndDate = "2022/11/11"
            )
        ),
        bankDetails = BankDetails(
            bankAccountName = "Dr Henry Blake",
            bankAccountNumber = "********",
            bankName = "Posh Bank PLC",
            bankSortCode = "123456",
            directDebitPaymentDay = "1"
        ),
        supplementaryInformation = SupplementaryInformation(
            dateOfBirth = "30/11/1980",
            seasonalPayment = true
        ),
        billingConsent = false
    ),
    sessionId = "********",
    testSwitch = false

)

val unreadResponse = UnreadResponse(
    listOf(
        message
    )
)

val acknowledgeResponse = AcknowledgeResponse(
    message = "The following message IDs have been acknowledged.",
    data = setOf(SWITCH_REF)
)

val switchStatusReportList =
    listOf(
        SwitchStatusReport(
            switchRef = SWITCH_REF,
            fuel = Fuel.ELECTRICITY,
            status = IN_PROGRESS,
            contactCustomer = false,
            reason = "",
            effectiveDate = LocalDate.now().toUswitchDate(),
            supplierDropoutCode = ""
        )
    )

val switchStatusReportListForLiveCustomer =
    listOf(
        SwitchStatusReport(
            switchRef = SWITCH_REF,
            fuel = null,
            status = SwitchStatus.LIVE,
            contactCustomer = false,
            reason = "Resubmit of enrolment",
            effectiveDate = LocalDate.now().toUswitchDate(),
            supplierDropoutCode = ""
        )
    )


val uSwitchConfig = USwitchConfig(
    protocol = "https",
    host = "staging-api.energy.uswitchsuppliers.com",
    port = 443,
    authApiUrl = "https://auth.energy.uswitchsuppliers.com/oauth/token",
    clientId = "6tqd4We6nQuSWEOXHFOonau1Q5dQyq6Q",
    clientSecret = "i3RonEU2Pl5dbV3Hcdtoac85G9gZjd8jx4sYyXfsSPRt_T-DiF4tmjunES6KHpOJ",
    grantType = "client_credentials",
    audience = "https://staging-api.energy.uswitchsuppliers.com/",
    connectionTimeout = 60000,
    requestTimeout = 60000,
    messageProcessingCount = 1
)
