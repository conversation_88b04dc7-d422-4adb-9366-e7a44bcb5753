package energy.so.customers.fixtures

import com.google.cloud.secretmanager.v1.AccessSecretVersionResponse
import com.google.cloud.secretmanager.v1.Secret
import com.google.cloud.secretmanager.v1.SecretName
import com.google.cloud.secretmanager.v1.SecretPayload
import com.google.cloud.secretmanager.v1.SecretVersion
import com.google.protobuf.ByteString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.core.customers.server.transunion.model.PasswordResetStatus
import java.time.LocalDateTime

object GcpSecretManagerPrecannedData {

    val projectId = "some_cluster"
    val secretId = "TRANSUNION_CREDIT_CHECK_PASSWORD"
    val secretName = SecretName.of(projectId, secretId)
    val secret = Secret.newBuilder()
        .setName(secretName.toString())
        .build()

    val secretValue = "password123!"
    val payload = SecretPayload.newBuilder()
        .setData(ByteString.copyFromUtf8(secretValue))
        .build()

    val secretVersion1 = SecretVersion.newBuilder()
        .setName("$secretName/1")
        .setState(SecretVersion.State.ENABLED)
        .setCreateTime(LocalDateTime.now().minusMinutes(3).toTimestamp())
        .build()
    val secretVersion2 = SecretVersion.newBuilder()
        .setName("$secretName/2")
        .setState(SecretVersion.State.ENABLED)
        .setCreateTime(LocalDateTime.now().minusMinutes(2).toTimestamp())
        .build()
    val secretVersion3 = SecretVersion.newBuilder()
        .setName("$secretName/3")
        .setState(SecretVersion.State.ENABLED)
        .setCreateTime(LocalDateTime.now().minusMinutes(1).toTimestamp())
        .build()

    val disabledSecretVersion = SecretVersion.newBuilder()
        .setName(secretName.toString())
        .setState(SecretVersion.State.DISABLED)
        .setCreateTime(LocalDateTime.now().minusMinutes(1).toTimestamp())
        .build()

    val accessVersionResponse: AccessSecretVersionResponse = AccessSecretVersionResponse.newBuilder()
        .setName(secretName.toString())
        .setPayload(payload)
        .build()

    fun passwordResetRecord(
        id: Long = 1L,
        password: String,
        status: PasswordResetStatus = PasswordResetStatus.NEW_PASSWORD_GENERATED,
    ) = energy.so.commons.model.tables.pojos.CreditCheckPasswordReset(
        id = id,
        encryptedPassword = password,
        status = status.name
    )
}