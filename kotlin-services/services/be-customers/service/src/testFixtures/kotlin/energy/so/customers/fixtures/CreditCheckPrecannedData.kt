package energy.so.customers.fixtures

import energy.so.commons.model.tables.pojos.RegistrationCreditCheckAutomationConfig
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

/**
 * @created 28/10/2024
 * <AUTHOR>
 */

val regCCAutomationConfig = RegistrationCreditCheckAutomationConfig(
    configEnabled = true,
    configScheduledCronExpression = "",
    createdAt = OffsetDateTime.parse("2022-12-03T10:16:30Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME),
)