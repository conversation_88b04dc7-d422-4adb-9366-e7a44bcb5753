package energy.so.customers.fixtures


import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage

const val CUSTOMER_ENROLMENT_ID = "1"
const val PROJECT_NAME = "test-project"

val validMessage = QueueMessage(data = CUSTOMER_ENROLMENT_ID, eventType = EventType.UPDATED)
val invalidMessage = QueueMessage(data = "abc", eventType = EventType.UPDATED)
val SUBSCRIPTION_CONFIG = SubscriptionConfiguration(
    name = "test-name",
    topic = "test-topic",
    key = "key",
    topicKey = "topicKey"
)
