package energy.so.customers.fixtures

import com.google.cloud.bigquery.Field
import com.google.cloud.bigquery.FieldList
import com.google.cloud.bigquery.FieldValue
import com.google.cloud.bigquery.FieldValueList
import com.google.cloud.bigquery.LegacySQLTypeName
import com.google.protobuf.NullValue
import energy.so.ac.junifer.v1.accounts.Status
import energy.so.ac.junifer.v1.accounts.account
import energy.so.ac.junifer.v1.accounts.cancelAccountReviewPeriodRequest
import energy.so.ac.junifer.v1.accounts.copy
import energy.so.ac.junifer.v1.accounts.createAccountReviewPeriodRequest
import energy.so.ac.junifer.v1.accounts.createAccountReviewPeriodResponse
import energy.so.ac.junifer.v1.accounts.getTicketsForAccountNumberResponse
import energy.so.ac.junifer.v1.accounts.ticket
import energy.so.ac.junifer.v1.assets.meterIdToRegister
import energy.so.ac.junifer.v1.assets.meterIdToRegistersResponse
import energy.so.ac.junifer.v1.assets.meterPointStructureRegister
import energy.so.ac.junifer.v1.assets.mpan
import energy.so.ac.junifer.v1.customers.addressForGetCustomer
import energy.so.ac.junifer.v1.customers.companyAddressForGetCustomer
import energy.so.ac.junifer.v1.customers.getCustomer
import energy.so.ac.junifer.v1.customers.linkForGetCustomer
import energy.so.ac.junifer.v1.customers.linksForGetCustomer
import energy.so.ac.junifer.v1.customers.primaryContactForGetCustomer
import energy.so.assets.meter.v2.MeterType
import energy.so.assets.meter.v2.meter
import energy.so.assets.meterPoints.v2.DccServiceStatus
import energy.so.assets.meterPoints.v2.MeasurementType
import energy.so.assets.meterPoints.v2.MeterPointHistory
import energy.so.assets.meterPoints.v2.MeterPointSupplyStatus
import energy.so.assets.meterPoints.v2.MeterPointType
import energy.so.assets.meterPoints.v2.UkProfileClass
import energy.so.assets.meterPoints.v2.getMPXNsByMeterPointIdsResponse
import energy.so.assets.meterPoints.v2.mPXN
import energy.so.assets.meterPoints.v2.meterPoint
import energy.so.assets.meterPoints.v2.meterPointElectricity
import energy.so.assets.meterPoints.v2.meterPointGas
import energy.so.assets.meterPoints.v2.meterPointHistory
import energy.so.assets.meterPoints.v2.meterPointsResponse
import energy.so.assets.properties.v2.PropertyType
import energy.so.assets.properties.v2.address
import energy.so.assets.properties.v2.property
import energy.so.assets.register.v2.register
import energy.so.commons.NullableString
import energy.so.commons.NullableTimestamp
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableInt32
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableLongString
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.CreditCheckStatu
import energy.so.commons.model.enums.CustomerClass
import energy.so.commons.model.enums.CustomerType
import energy.so.commons.model.enums.EnrolmentBroker
import energy.so.commons.model.enums.EnrolmentType
import energy.so.commons.model.enums.MatchLevel
import energy.so.commons.model.enums.PreferenceType
import energy.so.commons.model.enums.ReferralStatu
import energy.so.commons.model.tables.pojos.BillingAccountSetting
import energy.so.commons.model.tables.pojos.BrokerCreditCheck
import energy.so.commons.model.tables.pojos.Customer
import energy.so.commons.model.tables.pojos.CustomerContact
import energy.so.commons.model.tables.pojos.CustomerMetadata
import energy.so.commons.model.tables.pojos.CustomerPreferenceLog
import energy.so.commons.model.tables.pojos.CustomerPsrRel
import energy.so.commons.model.tables.pojos.CustomerReferral
import energy.so.commons.model.tables.pojos.RegistrationCreditCheck
import energy.so.commons.model.tables.pojos.RegistrationCreditCheckAddress
import energy.so.commons.model.tables.pojos.SyncStatu
import energy.so.commons.model.tables.references.CUSTOMER
import energy.so.commons.nullableBoolean
import energy.so.commons.nullableInt64
import energy.so.commons.nullableString
import energy.so.commons.nullableTimestamp
import energy.so.commons.v2.sync.OperationType
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.communications.v1.dtos.RecipientDto
import energy.so.communications.v1.dtos.SenderDto
import energy.so.core.customers.server.creditcheck.model.CreditCheckConstants.ACCOUNT_NUMBER
import energy.so.core.customers.server.creditcheck.model.CreditCheckConstants.CUSTOMER_NAME
import energy.so.core.customers.server.creditcheck.model.CreditCheckConstants.DEPOSIT_AMOUNT
import energy.so.core.customers.server.creditcheck.model.CreditCheckConstants.DEPOSIT_DEADLINE_DATE
import energy.so.core.customers.server.creditcheck.model.CreditCheckConstants.DEPOSIT_DEADLINE_TIME
import energy.so.core.customers.server.database.models.ActiveBillingAccountItem
import energy.so.core.customers.server.database.models.UpsertCustomerDto
import energy.so.core.customers.server.dtos.CreateCustomerPsrRequestDto
import energy.so.core.customers.server.models.Agreement
import energy.so.core.customers.server.models.AgreementType
import energy.so.core.customers.server.models.BillingAccount
import energy.so.core.customers.server.models.BillingAccountClass
import energy.so.core.customers.server.models.BillingAccountType
import energy.so.core.customers.server.models.Cancellation
import energy.so.core.customers.server.models.ConsumptionEstimation
import energy.so.core.customers.server.models.ConsumptionEstimationPeriod
import energy.so.core.customers.server.models.CreateCustomerDto
import energy.so.core.customers.server.models.CreateReminderDto
import energy.so.core.customers.server.models.CustomFieldsDto
import energy.so.core.customers.server.models.CustomVulnerability
import energy.so.core.customers.server.models.CustomerEnrolmentBroker
import energy.so.core.customers.server.models.CustomerFilterResult
import energy.so.core.customers.server.models.CustomerPsr
import energy.so.core.customers.server.models.EnrolmentAddress
import energy.so.core.customers.server.models.EnrolmentContactDetails
import energy.so.core.customers.server.models.EnrolmentPayload
import energy.so.core.customers.server.models.EnrolmentPaymentMethod
import energy.so.core.customers.server.models.EnrolmentProductDetails
import energy.so.core.customers.server.models.ErrorDto
import energy.so.core.customers.server.models.FreshdeskContactDto
import energy.so.core.customers.server.models.FreshdeskContactErrorResponseDto
import energy.so.core.customers.server.models.FreshdeskContactRequestDto
import energy.so.core.customers.server.models.Meter
import energy.so.core.customers.server.models.ProductAccountType
import energy.so.core.customers.server.models.Psr
import energy.so.core.customers.server.models.Reminder
import energy.so.core.customers.server.models.ReminderFrequency
import energy.so.core.customers.server.models.ReminderType
import energy.so.core.customers.server.models.UpdateReminderDto
import energy.so.core.customers.server.models.UserInfo
import energy.so.core.customers.server.models.fromJooq
import energy.so.core.customers.server.models.toCorrespondenceAddress
import energy.so.core.customers.server.models.toProtobuf
import energy.so.core.customers.sync.models.SyncStatus
import energy.so.customers.accounts.v2.AccountFinderRequest
import energy.so.customers.accounts.v2.AccountFinderResponseKt.foundAccount
import energy.so.customers.accounts.v2.AccountFuel
import energy.so.customers.accounts.v2.CreateAccountTicketStatus
import energy.so.customers.accounts.v2.accountFinderRequest
import energy.so.customers.accounts.v2.accountFinderResponse
import energy.so.customers.accounts.v2.copy
import energy.so.customers.accounts.v2.editAccountReviewPeriodRequest
import energy.so.customers.admin.v2.createUserForBillingAccountRequest
import energy.so.customers.billingaccounts.v2.accountDetailsAddress
import energy.so.customers.billingaccounts.v2.getAccountDetailsResponse
import energy.so.customers.client.models.Address
import energy.so.customers.client.models.BillDeliveryMethod
import energy.so.customers.client.models.BillingAccountSettings
import energy.so.customers.client.models.BillingCycle
import energy.so.customers.client.models.Contact
import energy.so.customers.client.models.ContactType
import energy.so.customers.client.models.CorrespondenceAddress
import energy.so.customers.client.models.CreatedFromType
import energy.so.customers.client.models.CustomerSetting
import energy.so.customers.client.models.EnergySource
import energy.so.customers.client.models.EvType
import energy.so.customers.client.models.Title
import energy.so.customers.customersetting.v2.patchCustomerSettingRequest
import energy.so.customers.customersetting.v2.preferenceCheckedLog
import energy.so.customers.fixtures.AgreementPrecannedData.METER_ID
import energy.so.customers.fixtures.AgreementPrecannedData.METER_ID_2
import energy.so.customers.fixtures.AgreementPrecannedData.oneAssetContents
import energy.so.customers.fixtures.BobsData.ID_1
import energy.so.customers.fixtures.BobsData.ID_2
import energy.so.customers.fixtures.BobsData.billingAccountId
import energy.so.customers.fixtures.BobsData.billingContactPojo
import energy.so.customers.fixtures.BobsData.customer3Metadata
import energy.so.customers.fixtures.BobsData.customerMetadata
import energy.so.customers.fixtures.BobsData.customerSettings
import energy.so.customers.fixtures.BobsData.enrolmentCreatedAt
import energy.so.customers.fixtures.BobsData.nowLocalDateTime
import energy.so.customers.fixtures.CustomersData.BILLING_ACCOUNT_NUMBER
import energy.so.customers.fixtures.CustomersData.CUSTOMER_ID
import energy.so.customers.fixtures.CustomersData.CUSTOMER_ID_2
import energy.so.customers.fixtures.CustomersData.EMAIL
import energy.so.customers.fixtures.CustomersData.FIRST_NAME
import energy.so.customers.fixtures.CustomersData.JUNIFER_CUSTOMER_ID
import energy.so.customers.fixtures.CustomersData.LAST_NAME
import energy.so.customers.fixtures.CustomersData.NUMBER
import energy.so.customers.fixtures.CustomersData.PHONE_NUMBER
import energy.so.customers.fixtures.CustomersData.PHONE_NUMBER1
import energy.so.customers.fixtures.CustomersData.PHONE_NUMBER_INVALID_LENGTH
import energy.so.customers.fixtures.CustomersData.RATE
import energy.so.customers.fixtures.CustomersData.RENEWAL_STATUS
import energy.so.customers.fixtures.CustomersData.USAGE
import energy.so.customers.fixtures.RemindersData.ALIGNMENT
import energy.so.customers.fixtures.RemindersData.contact
import energy.so.customers.referral.v2.ReferralStatus
import energy.so.customers.referral.v2.referralResponse
import energy.so.customers.reminders.v2.createReminderRequest
import energy.so.customers.reminders.v2.searchReminderRequest
import energy.so.customers.reminders.v2.updateReminderRequest
import energy.so.customers.sync.v2.addressEntity
import energy.so.customers.sync.v2.addressEntityRequest
import energy.so.customers.sync.v2.agreementEntity
import energy.so.customers.sync.v2.agreementEntityRequest
import energy.so.customers.sync.v2.billingAccountContactEntity
import energy.so.customers.sync.v2.billingAccountContactEntityRequest
import energy.so.customers.sync.v2.billingAccountEntity
import energy.so.customers.sync.v2.billingAccountEntityRequest
import energy.so.customers.sync.v2.billingAccountSettingEntity
import energy.so.customers.sync.v2.contactEntity
import energy.so.customers.sync.v2.contactEntityRequest
import energy.so.customers.sync.v2.copy
import energy.so.customers.sync.v2.customerContactEntity
import energy.so.customers.sync.v2.customerContactEntityRequest
import energy.so.customers.sync.v2.customerEntity
import energy.so.customers.sync.v2.customerEntityRequest
import energy.so.customers.sync.v2.customerPropertyRelEntity
import energy.so.customers.sync.v2.customerPropertyRelEntityRequest
import energy.so.customers.sync.v2.customerPsrRelEntity
import energy.so.customers.sync.v2.customerPsrRelEntityRequest
import energy.so.customers.sync.v2.customerSettingsEntity
import energy.so.customers.sync.v2.psrEntity
import energy.so.customers.sync.v2.psrEntityRequest
import energy.so.customers.v2.addressRequest
import energy.so.customers.v2.consumptionEstimationRequest
import energy.so.customers.v2.contactRequest
import energy.so.customers.v2.createCustomerEnrolmentRequest
import energy.so.customers.v2.customers.CancellationType
import energy.so.customers.v2.customers.Referral
import energy.so.customers.v2.customers.addPhoneNumberRequest
import energy.so.customers.v2.customers.cancellation
import energy.so.customers.v2.customers.contact
import energy.so.customers.v2.customers.copy
import energy.so.customers.v2.customers.counts
import energy.so.customers.v2.customers.createCustomerRequest
import energy.so.customers.v2.customers.customer
import energy.so.customers.v2.customers.productDetails
import energy.so.customers.v2.customers.productDetailsResponse
import energy.so.customers.v2.customers.referral
import energy.so.customers.v2.customers.reminderDetails
import energy.so.customers.v2.customers.renewal
import energy.so.customers.v2.customers.voucher
import energy.so.customers.v2.paymentMethodRequest
import energy.so.customers.v2.productRequest
import energy.so.customers.vulnerability.v2.createCustomerPsrRequest
import energy.so.payments.v2.BillingAccountTransaction
import energy.so.payments.v2.PaymentScheduleFrequency
import energy.so.payments.v2.billingAccountTransaction
import energy.so.payments.v2.billingAccountTransactionsResponse
import energy.so.payments.v2.paymentSchedule
import energy.so.products.v2.GspGroup
import energy.so.products.v2.ProductType
import energy.so.products.v2.getProductDetailsResponse
import energy.so.users.v2.FeatureName
import energy.so.users.v2.feature
import energy.so.users.v2.featureNameRequest
import energy.so.users.v2.userResponse
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.time.LocalDateTime.of
import java.time.LocalTime
import java.time.Month
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.Optional
import org.apache.commons.lang3.RandomStringUtils.randomAlphabetic
import org.jooq.JSON
import energy.so.ac.junifer.v1.accounts.Status as createAccountTicketStatusJuniferProto
import energy.so.ac.junifer.v1.accounts.address as accountAddressJuniferProto
import energy.so.ac.junifer.v1.accounts.createAccountTicketRequest as createAccountTicketRequestJuniferProto
import energy.so.ac.junifer.v1.accounts.createAccountTicketResponse as createAccountTicketResponseJuniferProto
import energy.so.ac.junifer.v1.accounts.createTicketEntities as createTicketEntitiesJuniferProto
import energy.so.ac.junifer.v1.accounts.ticketLinks as ticketLinksJuniferProto
import energy.so.ac.junifer.v1.accounts.updateAccountContactRequest as updateAccountContactRequestJuniferProto
import energy.so.ac.junifer.v1.customers.createCustomerVulnerabilityRequest as JuniferCreateCustomerVulnerabilityRequest
import energy.so.commons.model.enums.AgreementType as JooqAgreementType
import energy.so.commons.model.enums.BillingAccountClass as JooqBillingAccountClass
import energy.so.commons.model.enums.BillingAccountType as JooqBillingAccountType
import energy.so.commons.model.enums.ProductAccountType as JooqProductAccountType
import energy.so.commons.model.tables.pojos.Address as JooqAddress
import energy.so.commons.model.tables.pojos.Agreement as JooqAgreement
import energy.so.commons.model.tables.pojos.BillingAccount as JooqBillingAccount
import energy.so.commons.model.tables.pojos.BillingAccountContact as JooqBillingAccountContact
import energy.so.commons.model.tables.pojos.Contact as JooqContact
import energy.so.commons.model.tables.pojos.CustomerMetadata as JooqCustomerMetadata
import energy.so.commons.model.tables.pojos.CustomerSetting as JooqCustomerSetting
import energy.so.commons.model.tables.pojos.CustomerUserRel as JooqCustomerUserRel
import energy.so.commons.model.tables.pojos.ProductAccount as JooqProductAccount
import energy.so.commons.model.tables.pojos.ProductAccountAgreementRel as JooqProductAccountAgreementRel
import energy.so.core.customers.server.models.ProductAccount as ModelProductAccount
import energy.so.customers.accounts.v2.address as accountAddressCustomerProto
import energy.so.customers.accounts.v2.createAccountTicketRequest as createAccountTicketRequestCustomerProto
import energy.so.customers.accounts.v2.createAccountTicketResponse as createAccountTicketResponseCustomerProto
import energy.so.customers.accounts.v2.createTicketEntities as createTicketEntitiesCustomerProto
import energy.so.customers.accounts.v2.ticketLinks as ticketLinksCustomerProto
import energy.so.customers.accounts.v2.updateAccountContactRequest as updateAccountContactRequestCustomerProto
import energy.so.customers.client.models.Customer as CustomerModel
import energy.so.customers.client.models.CustomerClass as CustomerClassModel
import energy.so.customers.client.models.CustomerMetadata as ModelsCustomerMetadata
import energy.so.customers.client.models.CustomerSetting as ModelsCustomerSetting
import energy.so.customers.client.models.CustomerType as CustomerTypeModel
import energy.so.customers.customersetting.v2.EnergySource as GrpcEnergySource
import energy.so.customers.customersetting.v2.PreferenceType as PreferenceTypeGrpc
import energy.so.customers.v2.customers.Customer as GrpcCustomer

object CustomersData {
    const val ID_1 = 1L
    const val CUSTOMER_ID = 1L
    const val CUSTOMER_ID_2 = 2L
    const val CUSTOMER_ID_3 = 3L
    const val JUNIFER_CUSTOMER_ID = 12L
    const val ENROLMENT_ID = 34567L
    const val NUMBER = "123456"
    const val FIRST_NAME = "first name"
    const val LAST_NAME = "last name"
    const val CUSTOMER_CLASS = "CONSUMER_BUSINESS"
    const val CUSTOMER_TYPE = "BUSINESS"
    const val EMAIL = "<EMAIL>"
    const val PHONE_NUMBER1 = "**********"
    const val PHONE_NUMBER2 = "**********"
    const val PHONE_NUMBER = "**********"
    const val NEW_PHONE_NUMBER = "**********"
    const val PHONE_NUMBER_INVALID_LENGTH = "**********1011121314"
    const val PHONE_NUMBER_INVALID_FORMAT = "aaaa1234"
    const val RENEWAL_STATUS = "Pending"
    const val BILLING_ACCOUNT_NUMBER = "123"
    private val dateNow = LocalDate.now()
    private const val LOW_PERCENTAGE = 1
    private const val HIGH_PERCENTAGE = 1
    private const val FREQUENCY_MULTIPLIER = 1
    const val COOLING_OFF_PERIOD = 14L
    const val GENERIC_STRING = "string"
    const val SUCCESS_STRING = "success"
    const val USAGE = 25.0
    const val RATE = 25.0
    const val CUSTOMER_EMAIL = "<EMAIL>"
    const val CUSTOMER_PHONE_NUMBER = "********"

    val frozenLocalDateTime = now()

    val customerFilterResult = CustomerFilterResult(
        customerIds = setOf(CUSTOMER_ID),
        billingAccountIds = setOf(500)
    )

    val paymentScheduleProto = paymentSchedule {
        id = ID_1
        toDt = frozenLocalDateTime.toNullableTimestamp()
        fromDt = frozenLocalDateTime.toNullableTimestamp()
        frequency = PaymentScheduleFrequency.MONTHLY
        stoppedAt = frozenLocalDateTime.toNullableTimestamp()
        amount = 10.0
        lowPercentage = LOW_PERCENTAGE.toNullableInt32()
        highPercentage = HIGH_PERCENTAGE.toNullableInt32()
        frequencyMultiplier = FREQUENCY_MULTIPLIER
        seasonalPaymentFl = true
        frequencyAlignmentDt = dateNow.toNullableTimestamp()
        firstPaymentDt = dateNow.toNullableTimestamp()
        createdAt = frozenLocalDateTime.toTimestamp()
    }

    val jooqCustomerSetting = JooqCustomerSetting(
        id = 1,
        referralsEnabled = true,
        marketingOptIn = false,
        smartMeterInterest = false,
        darkMode = false,
    )

    val newCustomer = CustomerModel(
        id = CUSTOMER_ID,
        number = NUMBER,
        customerSetting = customerSettings,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        metadata = customerMetadata,
        `class` = CustomerClassModel.CONSUMER_BUSINESS,
        type = CustomerTypeModel.BUSINESS,
        primaryContact = contact
    )

    val newCustomer2 = CustomerModel(
        id = CUSTOMER_ID,
        number = NUMBER,
        customerSetting = customerSettings,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        metadata = customerMetadata,
        createdAt = nowLocalDateTime.toTimestamp().toLocalDateTime(),
        updatedAt = nowLocalDateTime,
        `class` = CustomerClass.CONSUMER_BUSINESS.toSiblingEnum(),
        type = CustomerType.BUSINESS.toSiblingEnum(),
    )

    val customer3 = CustomerModel(
        id = CUSTOMER_ID_3,
        metadata = customer3Metadata,
        number = NUMBER,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        `class` = CustomerClass.CONSUMER_BUSINESS.toSiblingEnum(),
        type = CustomerType.BUSINESS.toSiblingEnum(),
        customerSetting = customerSettings
    )

    val correspondenceAddress = CorrespondenceAddress(
        address1 = "Some address",
        address2 = "Address 2",
        address3 = "Address 3",
        careOf = "Care of",
        postcode = "N9 RFG",
        countryCode = "GB",
    )

    val customerToUpsert = energy.so.customers.client.models.Customer(
        number = "********",
        firstName = "Joe",
        lastName = "Bloggs",
        `class` = energy.so.customers.client.models.CustomerClass.CONSUMER_UNKNOWN,
        type = energy.so.customers.client.models.CustomerType.UNKNOWN,
        metadata = energy.so.customers.client.models.CustomerMetadata(
            brokerAgent = CustomerEnrolmentBroker.SO_ENERGY.name,
            correspondenceAddress = correspondenceAddress,
            createdFrom = CreatedFromType.ENROLMENT
        ),
        customerSetting = CustomerSetting(
            marketingOptIn = true,
            smartMeterInterest = false,
            referralsEnabled = true,
            darkMode = false,
        ),
    )

    val billingAccountToUpsert = BillingAccount(
        type = BillingAccountType.CB_INVOICE,
        name = "billing acc",
        number = "1234",
        currency = "GBP",
        `class` = BillingAccountClass.INVOICE,
        from = of(1970, 1, 1, 0, 0, 0),
        customerId = 987,
        settings = BillingAccountSettings(billingCycle = BillingCycle.MONTHLY_ANNIVERSARY)
    )

    val newCustomerNoReferralEnabled = CustomerModel(
        id = CUSTOMER_ID,
        number = NUMBER,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        `class` = CustomerClass.CONSUMER_BUSINESS.toSiblingEnum(),
        type = CustomerType.BUSINESS.toSiblingEnum(),
        createdAt = nowLocalDateTime.toTimestamp().toLocalDateTime(),
        updatedAt = nowLocalDateTime,
        metadata = customerMetadata,
        customerSetting = customerSettings.copy(referralsEnabled = false)
    )

    val unsavedCustomer = Customer(
        number = NUMBER,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS
    )

    val savedCustomer = unsavedCustomer.copy(
        id = CUSTOMER_ID,
        customerSettingId = 1,
        customerMetadataId = CUSTOMER_METADATA_ID,
        createdAt = frozenLocalDateTime,
        number = "123456",
    )

    val secondSavedCustomer = unsavedCustomer.copy(
        id = CUSTOMER_ID_2,
        customerSettingId = 2,
        createdAt = frozenLocalDateTime
    )

    val createCustomerDto = CreateCustomerDto(
        number = NUMBER,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        email = EMAIL,
        phoneNumber = PHONE_NUMBER1,
        createdAt = null,
        juniferCustomerId = null
    )

    val createCustomerRequest = createCustomerRequest {
        number = NUMBER
        firstName = FIRST_NAME
        lastName = LAST_NAME
        email = EMAIL
        phoneNumber = PHONE_NUMBER1
    }

    val customerEntityRequest = customerEntity {
        number = NUMBER.toNullableString()
        firstName = FIRST_NAME.toNullableString()
        lastName = LAST_NAME.toNullableString()
        class_ = CUSTOMER_CLASS.toNullableString()
        type = CUSTOMER_TYPE.toNullableString()
        primaryEmail = EMAIL.toNullableString()
        settings = customerSettingsEntity {
            marketingOptIn = true.toNullableBoolean()
        }
        syncTransactionId = JUNIFER_CUSTOMER_ID.toNullableLongString()
    }

    val createCustomerEntityRequest = customerEntityRequest {
        operationType = OperationType.CREATE
        customerEntity = customerEntityRequest
    }

    val customerEntityRequestWithId = customerEntityRequest.copy { id = ID_1.toNullableInt64() }

    val patchCustomerEntityRequest = customerEntityRequest {
        operationType = OperationType.PATCH
        customerEntity = customerEntityRequestWithId
    }

    val upsertCustomerDto = UpsertCustomerDto(
        id = CUSTOMER_ID,
        number = Optional.of(NUMBER),
        createdAt = frozenLocalDateTime,
        firstName = Optional.of(FIRST_NAME),
        lastName = Optional.of(LAST_NAME),
        email = Optional.of(EMAIL),
        phoneNumber = Optional.of(PHONE_NUMBER1),
    )


    val customerSetting = JooqCustomerSetting(
        id = 1,
        marketingOptIn = false,
        smartMeterInterest = false,
        referralsEnabled = true,
        darkMode = false,
    )

    val customerSettingNoReferral = JooqCustomerSetting(
        id = 1,
        referralsEnabled = false,
        darkMode = false,
    )

    val jooqCustomerMetadata = JooqCustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )

    val accountCancellationRecord = energy.so.commons.model.tables.pojos.Cancellation(
        billingAccountId = 1L,
        reason = "reason",
        type = energy.so.commons.model.enums.CancellationType.ACCOUNT,
        createdAt = of(2023, 3, 15, 13, 0, 0),
        updatedAt = of(2023, 3, 15, 13, 0, 0)
    )

    val accountCancellation = Cancellation(
        billingAccountId = 1L,
        reason = "reason",
        type = Cancellation.SwitchType.ACCOUNT,
        createdAt = of(2023, 3, 15, 13, 0, 0),
        updatedAt = of(2023, 3, 15, 13, 0, 0)
    )

    val myAccountFeatureName = featureNameRequest {
        name = FeatureName.MY_ACCOUNT
    }

    val enabledMyAccountFeatureProto = feature {
        name = FeatureName.MY_ACCOUNT
        enabled = true
    }

    val disabledMyAccountFeatureProto = feature {
        name = FeatureName.MY_ACCOUNT
        enabled = false
    }
}

/**
 * This object contains well-known data about a fictional test subject called Bob. The intention behind having an
 * object hold test data is inspired by Martin Fowler and the ObjectMother:
 *
 * https://martinfowler.com/bliki/ObjectMother.html
 *
 * Often tests will end up rewriting the same ad-hoc test data, creating noise in the test, and duplication overall.
 * These objects provide a way to:
 *
 * 1) share test data among tests
 * 2) let tests focus on executing code and running assertions, rather than orchestrating data
 */
object BobsData {
    val TEST_STRING = "TEST"
    val SUPPLY_ADDRESS = "SUPPLY ADDRESS"
    val SUPPLY_ADDRESS_POSTCODE = "SUPPLY ADDRESS POSTCODE"
    const val billingAccountId = 674L
    const val billingAccountId2 = 675L
    const val customerId = 123L
    const val userId = 1236L
    const val customerSettingId = 123L
    const val customerMetadataId = 123L
    val ID_1 = 1L
    val ID_2 = 2L
    val ID_3 = 3L
    val ID_4 = 4L
    val nowLocalDateTime = now().withHour(0).withSecond(0).withNano(0)
    val enrolmentCreatedAt = of(2024, 10, 10, 10, 10, 10)

    const val firstName = "Bob First Name"
    const val lastName = "Bob Last Name"
    const val bobsEmail = "<EMAIL>"
    const val contactFirstname = "Mary"
    const val contactLastame = "Doe"
    const val primaryContactEmail = "<EMAIL>"
    val frozenLocalDateTime = now()

    val agreementJooq = JooqAgreement(
        id = 1L,
        number = "test",
        createdAt = nowLocalDateTime,
        quoteId = 1L,
        contents = JSON.json("{\"productvariants\": [{\"id\": 1,\"meterpoints\":[{\"id\":\"1\"}]}]}"),
        updatedAt = nowLocalDateTime,
        fromDate = nowLocalDateTime,
        toDate = nowLocalDateTime.plusDays(20),
        type = JooqAgreementType.OPEN_ENDED
    )

    val userResponse = userResponse {
        id = userId
        this.firstName = BobsData.firstName
        this.lastName = BobsData.lastName
        email = "<EMAIL>"
        externalId = "1"
        currentAccountId = 1L
    }

    val agreementModel = Agreement.fromJooq(agreementJooq)

    val billingAccount = JooqBillingAccount(
        id = billingAccountId,
        customerId = customerId,
        name = "bobsBillingAccount",
        number = "123",
        `class` = energy.so.commons.model.enums.BillingAccountClass.COST_STATEMENT,
        type = energy.so.commons.model.enums.BillingAccountType.CB_INVOICE,
        settingsId = ID_1,
        fromDate = enrolmentCreatedAt.plusDays(5)
    )

    val jooqBillingAccountSettings = BillingAccountSetting(
        id = ID_1,
        billDeliveryMethod = energy.so.commons.model.enums.BillDeliveryMethod.BOTH,
        billingCycle = energy.so.commons.model.enums.BillingCycle.ANNUALLY,
        receiveEmail = false,
        receivePost = false,
        receiveSms = false,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null
    )

    val billingAccountSettings = BillingAccountSettings(
        id = 1L,
        billDeliveryMethod = BillDeliveryMethod.BOTH,
        billingCycle = BillingCycle.ANNUALLY,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null
    )

    val contact = Contact(
        id = 1L,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        email = EMAIL,
        type = ContactType.BUSINESS,
        title = Title.MR,
        fromDate = now().minusDays(20),
        toDate = null,
        phoneNumber1 = PHONE_NUMBER1
    )

    val BOBS_BILLING_ACCOUNT_NUMBER = "123"
    val billingAccountModel = BillingAccount(
        id = billingAccountId,
        customerId = customerId,
        name = "bobsBillingAccount",
        number = BOBS_BILLING_ACCOUNT_NUMBER,
        `class` = BillingAccountClass.COST_STATEMENT,
        type = BillingAccountType.CB_INVOICE,
        createdAt = now(),
        updatedAt = now(),
        currency = "GBP",
        to = now(),
        from = now(),
        primaryContact = contact,
        settings = billingAccountSettings
    )

    val listOfAgreements = listOf(
        Agreement(
            id = 1,
            type = AgreementType.FIXED_12_MONTHS,
            from = frozenLocalDateTime.minusDays(30),
            to = frozenLocalDateTime.plusDays(20),
            cancelled = false,
            number = TEST_STRING,
            quoteId = null,
            contents = null,
            updatedAt = frozenLocalDateTime,
            createdAt = frozenLocalDateTime,
            deleted = null,
            ordered = LocalDate.now(),
        ),
        Agreement(
            id = 2,
            type = AgreementType.FIXED_18_MONTHS,
            from = frozenLocalDateTime.minusDays(20),
            to = frozenLocalDateTime.minusDays(10),
            cancelled = false,
            number = TEST_STRING,
            quoteId = null,
            contents = null,
            updatedAt = frozenLocalDateTime,
            createdAt = frozenLocalDateTime,
            deleted = null,
            ordered = LocalDate.now(),
        ),
        Agreement(
            id = 2,
            type = AgreementType.FIXED_24_MONTHS,
            from = frozenLocalDateTime.minusDays(6),
            to = frozenLocalDateTime.plusDays(10),
            cancelled = false,
            number = TEST_STRING,
            quoteId = null,
            contents = null,
            updatedAt = frozenLocalDateTime,
            createdAt = frozenLocalDateTime,
            deleted = null,
            ordered = LocalDate.now(),
        ),
    )

    val agreements = JooqAgreement(
        id = RemindersData.AGREEMENT_ID,
        type = JooqAgreementType.OPEN_ENDED,
        fromDate = localDateTimeNow.minusDays(1),
        toDate = localDateTimeNow.plusDays(1),
        number = "number",
        quoteId = 1,
        contents = oneAssetContents,
        updatedAt = now(),
        createdAt = localDateTimeNow,
        ordered = localDateTimeNow,
    )

    val productAccount = ModelProductAccount(
        id = 1L,
        type = JooqProductAccountType.ELECTRICITY.toSiblingEnum(),
        activeAgreements = listOf(
            Agreement.fromJooq(
                // TODO: Duplicated from RemindersData, fix.
                JooqAgreement(
                    id = RemindersData.AGREEMENT_ID,
                    type = JooqAgreementType.OPEN_ENDED,
                    fromDate = localDateTimeNow.minusDays(1),
                    toDate = localDateTimeNow.plusDays(1),
                    number = "number",
                    quoteId = 1,
                    contents = oneAssetContents,
                    updatedAt = now(),
                    createdAt = localDateTimeNow,
                    ordered = localDateTimeNow,
                )
            )
        ),
        billingAccount = billingAccountModel,
        createdAt = now(),
        updatedAt = now(),
        agreements = emptyList()
    )

    val supplyAddressData = addressRequest {
        address1 = "Some address"
        address2 = "Address 2".toNullableString()
        address3 = "Address 3".toNullableString()
        postcode = "N9RFG"
        countryCode = "GB"
        careOf = "Care of".toNullableString()
    }

    val correspondenceAddressNullCareOf = CorrespondenceAddress(
        address1 = supplyAddressData.address1,
        address2 = supplyAddressData.address2.getValueOrNull(),
        address3 = supplyAddressData.address3.getValueOrNull(),
        postcode = supplyAddressData.postcode,
        countryCode = supplyAddressData.countryCode
    )

    val customerMetadata = ModelsCustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        correspondenceAddress = supplyAddressData.toCorrespondenceAddress(),
        psrRequestedAt = of(2022, 12, 2, 0, 0, 0),
        lastTicketAt = of(2022, 12, 2, 0, 0, 0),
    )

    val customer3Metadata = ModelsCustomerMetadata(
        id = 3,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        lastAgreementRenew = nowLocalDateTime,
    )

    val customerSettings = ModelsCustomerSetting(
        id = customerSettingId,
        referralsEnabled = true,
        marketingOptIn = false,
        smartMeterInterest = false,
        lastBannerShownAt = of(2022, 12, 2, 0, 0, 0),
        darkMode = false,
        evType = EvType.NO_EV,
        intendToBuyEv = false,
        evTariffMarketingConsent = false,
    )

    val voucherGrpc = voucher {
        code = "SNMLKJI"
        url = "Application_base_url/SNMLKJI"
        currentReferralFee = 50
    }

    val cancellationGrpc = cancellation {
        id = 1L
        createdAt = of(2023, 3, 16, 16, 0, 0).toTimestamp()
        updatedAt = of(2023, 3, 16, 16, 0, 0).toTimestamp()
        deletedAt = nullableTimestamp { null_ = NullValue.NULL_VALUE }
        reason = "reason".toNullableString()
        type = CancellationType.METERPOINT
        meterPointId = 1L.toNullableInt64()
    }

    val renewalGrpc = renewal {
        agreementIds.add(ID_1.toString())
        accountNumber = BILLING_ACCOUNT_NUMBER
        status = RENEWAL_STATUS
    }

    val referralDetailsGrpc = energy.so.customers.v2.customers.referralDetails {
        protectedName = "${contact.title} $FIRST_NAME $LAST_NAME"
        status = ReferralStatus.PENDING.toSiblingEnum()
        credit = 100
    }

    val referralDetailsGrpcNoTitle = energy.so.customers.v2.customers.referralDetails {
        protectedName = "$FIRST_NAME $LAST_NAME"
        status = ReferralStatus.PENDING.toSiblingEnum()
        credit = 100
    }

    val referralGrpc = referral {
        counts = counts {
            pendingCount = 1
        }
        list.addAll(
            listOf(
                referralDetailsGrpc
            )
        )
    }

    val referralGrpcNoTitle = referral {
        counts = counts {
            pendingCount = 1
        }
        list.addAll(
            listOf(
                referralDetailsGrpcNoTitle // TODO(after SO-3331) add when implement referral
            )
        )
    }

    val bobCustomerResponse = CustomerModel(
        id = CUSTOMER_ID,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        `class` = CustomerClassModel.CONSUMER_BUSINESS,
        type = CustomerTypeModel.BUSINESS,
        number = NUMBER,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        metadata = customerMetadata,
        customerSetting = customerSettings,
        primaryContact = contact
    )

    val meterReadingReminderGrpc = reminderDetails {
        alignment = ALIGNMENT
        frequency = energy.so.customers.reminders.v2.ReminderFrequency.MONTHLY
        updatedAt = nowLocalDateTime.toTimestamp()
        lastSentAt = nowLocalDateTime.toNullableTimestamp()
    }

    val bobsCustomerWithoutVoucherResponseGrpc = customer {
        id = ID_1
        currentAccountId = ID_1
        cancellations.addAll(listOf(cancellationGrpc))
        renewals.add(renewalGrpc)
        referral = referralGrpc
        meterReadingReminder = meterReadingReminderGrpc //TODO(SO-9698) add when we have reminder
        brokerAgent = "test".toNullableString()
        evCampaign = false
        setting = customerSettings.toProtobuf()
        firstName = FIRST_NAME
        lastName = LAST_NAME
        number = "123456"
        customerClass = GrpcCustomer.CustomerClass.CONSUMER_BUSINESS
        customerType = GrpcCustomer.CustomerType.BUSINESS
    }

    val bobCustomerResponseGrpc = bobsCustomerWithoutVoucherResponseGrpc.copy { voucher = voucherGrpc }

    val protoContact = contact {
        id = RemindersData.CONTACT_ID
        firstName = "Joe"
        lastName = "Luck"
        email = "test@asd.c"
        contactType = energy.so.customers.v2.customers.ContactType.BUSINESS
        title = energy.so.customers.v2.customers.Title.MR
        dateOfBirth = nullableTimestamp { null_ = NullValue.NULL_VALUE }
    }

    val bobsCustomerBasicResponseGrpc = customer {
        id = ID_1
        currentAccountId = 0L
        referral = Referral.getDefaultInstance()
        brokerAgent = "test".toNullableString()
        evCampaign = false
        setting = customerSettings.toProtobuf()
        firstName = FIRST_NAME
        lastName = LAST_NAME
        number = "123456"
        customerClass = GrpcCustomer.CustomerClass.CONSUMER_BUSINESS
        customerType = GrpcCustomer.CustomerType.BUSINESS
        primaryContact = protoContact
    }

    val customer = Customer(
        id = customerId,
        customerSettingId = customerSettingId,
        customerMetadataId = customerMetadataId,
        createdAt = frozenLocalDateTime,
        number = "123456",
        firstName = firstName,
        lastName = lastName,
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS
    )

    val addressPojo = JooqAddress(
        id = ID_1,
        address1 = TEST_STRING,
        address2 = TEST_STRING,
        address3 = TEST_STRING,
        address4 = TEST_STRING,
        address5 = TEST_STRING,
        address6 = null,
        address7 = null,
        address8 = null,
        address9 = null,
        postcode = TEST_STRING,
        countryCode = "great_britain",
        type = TEST_STRING,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val address = Address(
        id = ID_1,
        address1 = TEST_STRING,
        address2 = TEST_STRING,
        address3 = TEST_STRING,
        address4 = TEST_STRING,
        address5 = TEST_STRING,
        address6 = null,
        address7 = null,
        address8 = null,
        address9 = null,
        postcode = TEST_STRING,
        countryCode = "great_britain",
        type = TEST_STRING,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val customerContact = Contact(
        id = ID_1,
        firstName = firstName,
        lastName = lastName,
        email = "bob@asd.c",
        type = ContactType.BUSINESS,
        title = Title.MR,
        phoneNumber1 = PHONE_NUMBER,
        phoneNumber2 = PHONE_NUMBER,
        phoneNumber3 = PHONE_NUMBER,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val customerContactRel = CustomerContact(
        id = ID_1,
        customerId = ID_1,
        contactId = ID_1,
        fromDate = nowLocalDateTime.minusDays(1),
        toDate = nowLocalDateTime.plusDays(1),
        primary = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val customerContactRelModel = energy.so.core.customers.server.models.CustomerContact(
        id = ID_1,
        customerId = ID_1,
        contactId = ID_1,
        fromDate = nowLocalDateTime.minusDays(1),
        toDate = nowLocalDateTime.plusDays(1),
        primary = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val billingContactPojo = JooqContact(
        id = ID_1,
        firstName = firstName,
        lastName = lastName,
        email = "bob@asd.c",
        type = energy.so.commons.model.enums.ContactType.BUSINESS,
        title = energy.so.commons.model.enums.Title.MR,
        phoneNumber1 = PHONE_NUMBER,
        phoneNumber2 = PHONE_NUMBER,
        phoneNumber3 = PHONE_NUMBER,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        addressId = ID_1,
    )

    val bilingAccountContactPojo = JooqBillingAccountContact(
        id = ID_1,
        billingAccountId = ID_1,
        contactId = ID_1,
        fromDate = nowLocalDateTime.minusDays(1),
        toDate = nowLocalDateTime.plusDays(1),
        primary = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val customerContactJooq = JooqContact(
        id = 121L,
        firstName = firstName,
        lastName = lastName,
        email = "bob@asd.c",
        type = energy.so.commons.model.enums.ContactType.BUSINESS,
        title = energy.so.commons.model.enums.Title.MR,
        updatedAt = nowLocalDateTime,
        createdAt = nowLocalDateTime,
    )

    val contactModel = Contact(
        id = 121L,
        firstName = firstName,
        lastName = lastName,
        email = "bob@asd.c",
        type = energy.so.commons.model.enums.ContactType.BUSINESS.toSiblingEnum(),
        title = energy.so.commons.model.enums.Title.MR.toSiblingEnum<Title>(),
    )

    val contactModel2 = Contact(
        id = 121L,
        firstName = firstName,
        lastName = lastName,
        email = "bob@asd.c",
        type = energy.so.commons.model.enums.ContactType.BUSINESS.toSiblingEnum(),
        title = energy.so.commons.model.enums.Title.MR.toSiblingEnum<Title>(),
        updatedAt = nowLocalDateTime,
        createdAt = nowLocalDateTime,
        customerContactId = 121L
    )

    val companyAddressForGetCustomerData = companyAddressForGetCustomer {
        addressType = "Standard address"
        address1 = "1 High Street"
        address2 = "Highton"
        address3 = "Highville"
        address4 = "Hightown"
        address5 = "Highborough"
        postcode = "TE1 2ST"
        countryCode = "GB"
        country = "Great Britain"
    }

    val addressForGetCustomerData = addressForGetCustomer {
        addressType = "Standard Address"
        careOf = "Ms Test Gas"
        address1 = "123 The Street"
        address2 = "Central Town"
        address3 = "London"
        address4 = "Highville"
        address5 = "Hightown"
        postcode = "E20 1AB"
        countryCode = "GB"
        country = "Great Britain"
    }

    val linkForGetCustomerData = linkForGetCustomer {
        self = "http://127.0.0.1:43002/rest/v1/contacts/123"
    }

    val primaryContactDataForGetCustomerData = primaryContactForGetCustomer {
        id = 123
        contactType = "Residential"
        primary = true
        title = "Ms"
        forename = "Test"
        surname = "Gas"
        initials = "TG"
        jobTitle = "Inspector"
        address = addressForGetCustomerData
        email = "<EMAIL>"
        dateOfBirth = LocalDate.of(2020, 1, 1).toTimestamp()
        phoneNumber1 = "**********"
        phoneNumber2 = "**********"
        phoneNumber3 = "**********"
        customerContactId = 456
        cancelled = false
        fromDttm = of(2020, 1, 1, 1, 1, 1).toTimestamp()
        toDttm = of(2020, 1, 1, 1, 2, 3).toTimestamp()
        links = linkForGetCustomerData
    }

    val linksForGetCustomerData = linksForGetCustomer {
        self = "http://127.0.0.1:43002/rest/v1/contacts/123"
        accounts = "http://127.0.0.1:43002/rest/v1/customers/123/accounts"
        securityQuestions = "http://127.0.0.1:43002/rest/v1/customers/123/securityQuestions"
        billingEntities = "http://127.0.0.1:43001/rest/v1/customers/123/billingEntities"
        prospects = "http://127.0.0.1:43002/rest/v1/customers/123/prospects"
    }

    val getCustomerProto = getCustomer {
        id = 11
        name = "Mr Joe Bloggs"
        number = "********"
        customerClass = "CONSUMER_BUSINESS"
        customerType = "BUSINESS"
        state = "Prospect"
        title = "Mr"
        forename = "Joe"
        surname = "Bloggs"
        companyName = "Energy Ltd"
        companyNumber = "**********"
        companyAddress = companyAddressForGetCustomerData
        bereavementFl = false
        marketingOptOutFl = false
        creditScore = 1
        taxExemptReason = "1"
        primaryContact = primaryContactDataForGetCustomerData
        links = linksForGetCustomerData
    }

    val protoAccountId = "789"
    val billingAccountProto = account {
        id = protoAccountId
        type = "CB-INVOICE"
        name = "billing acc"
        number = "1234"
        currency = "GBP"
        accountClass = "Invoice".toNullableString()
        billingCycle = "Monthly - Anniversary".toNullableString()
    }

    val enrolmentRequest = createCustomerEnrolmentRequest {
        broker = "broker"
        supplyAddress = supplyAddressData
        marketingOptedIn = true
        contacts.add(contactRequest {
            firstName = NullableString.newBuilder().setValue("John").build()
            lastName = "Doe"
            primaryContact = true
            title = NullableString.newBuilder().setValue("MR").build()
            birthdate = NullableTimestamp.newBuilder().setValue(now().toTimestamp()).build()
            email = NullableString.newBuilder().setValue("<EMAIL>").build()
            phone = NullableString.newBuilder().setValue("*********0").build()
            address = addressRequest {
                address1 = "address 1"
                address2 = NullableString.newBuilder().setValue("address2").build()
                address3 = NullableString.newBuilder().setValue("address3").build()
                countryCode = "GB"
                postcode = "NW8 7ED"
            }
        })
    }

    val billingAccountContact = customerContact.copy(
        id = 122L
    )

    val customerContactRelation = CustomerContact(
        id = 121L,
        customerId = customerId,
        contactId = customerContactJooq.id,
        fromDate = localDateTimeNow.minusDays(10),
        toDate = localDateTimeNow.plusDays(10),
        primary = true,
        createdAt = now(),
        updatedAt = now()
    )

    val billingContactRelation = JooqBillingAccountContact(
        id = 122L,
        billingAccountId = billingAccountId,
        contactId = billingAccountContact.id,
        fromDate = now().minusDays(10),
        toDate = now().plusDays(10),
        primary = true
    )

    val addPhoneNumberRequest = addPhoneNumberRequest {
        customerId = ID_1
        phoneNumber = PHONE_NUMBER
    }


    val addPhoneNumberRequestInvalid = addPhoneNumberRequest {
        customerId = ID_1
        phoneNumber = PHONE_NUMBER_INVALID_LENGTH
    }

    val dateOfBirth = LocalDate.of(2000, 1, 1)
    const val addressLine1 = "Contact address"
    const val addressLine2 = "Contact Address 2"
    const val addressLine3 = "Contact Address 3"
    const val postCode = "SE3 RFG"

    val elecProductRequest = productRequest {
        code = "E2-RG-D"
        meterPointIdentifiers.add("***************")
        meterPointIdentifiers.add("***************")
        type = "electricity"
        consumptionEstimation = consumptionEstimationRequest {
            consumption = "45.30"
            estimatedCost = "346.00".toNullableString()
            period = "month"
            e7DaytimeConsumptionProportion = "4.00".toNullableString()
        }
    }

    val gasProductRequest = productRequest {
        code = "GAS-RG-D"
        meterPointIdentifiers.add("2123456")
        type = "gas"
    }

    val customerEnrolmentRequest = createCustomerEnrolmentRequest {
        type = "new_customer"
        marketingOptedIn = true
        broker = "so_energy"
        voucher = "voucher".toNullableString()
        quoteId = "quote".toNullableString()
        extraAssistance = true.toNullableBoolean()
        supplyAddress = supplyAddressData
        paymentMethod = paymentMethodRequest {
            type = "direct_debit"
            paymentDayOfMonth = 2.toNullableInt32()
            paymentAmount = "345.65".toNullableString()
            bankAccountName = BANK_ACCOUNT_NAME
            bankAccountNumber = BANK_ACCOUNT_NUMBER
            bankAccountSortCode = BANK_ACCOUNT_SORT_CODE
            seasonalPayment = true
        }
        contacts.add(contactRequest {
            title = "MR".toNullableString()
            firstName = contactFirstname.toNullableString()
            lastName = contactLastame
            email = primaryContactEmail.toNullableString()
            birthdate = dateOfBirth.toNullableTimestamp()
            primaryContact = true
            communicationMethod = "electronic"
            address = addressRequest {
                address1 = addressLine1
                address2 = addressLine2.toNullableString()
                address3 = addressLine3.toNullableString()
                postcode = postCode
                postcode = "SE3 RFG"
                countryCode = "GB"
                careOf = "Contact Care of".toNullableString()
            }
            phone = NullableString.newBuilder().setValue("*********").build()
        })

        products.add(elecProductRequest)
        products.add(gasProductRequest)
        evType = "ELECTRICITY".toNullableString()
        intendToBuyEv = false.toNullableBoolean()
        evTariffMarketingConsent = false.toNullableBoolean()
    }

    val validAccountFinderRequest: AccountFinderRequest = accountFinderRequest {
        fuel = AccountFuel.DUAL
    }

    val invalidAccountFinderRequest = validAccountFinderRequest.copy {
        multipleMeters = true
    }

    val accountFinderFoundAccount = foundAccount {
        accountId = AccountsData.ACCOUNT_ID
        accountNumber = AccountsData.NUMBER
    }

    val validAccountFinderResponse = accountFinderResponse {
        accounts.add(accountFinderFoundAccount)
    }

    val foundAccountFieldValueList: FieldValueList = FieldValueList.of(
        listOf(
            FieldValue.of(FieldValue.Attribute.PRIMITIVE, AccountsData.ACCOUNT_ID.toString()),
            FieldValue.of(FieldValue.Attribute.PRIMITIVE, AccountsData.ACCOUNT_ID.toString()),
        ),
        FieldList.of(
            Field.newBuilder("accountId", LegacySQLTypeName.INTEGER).build(),
            Field.newBuilder("accountNumber", LegacySQLTypeName.STRING).build(),
        )
    )

    val foundAccountConstructorResponse = foundAccount {
        accountId = AccountsData.ACCOUNT_ID
        accountNumber = AccountsData.ACCOUNT_ID.toString()
    }

    val createUserForBillingAccountRequest = createUserForBillingAccountRequest {
        juniferAccountNumber = BOBS_BILLING_ACCOUNT_NUMBER
        skipEmail = false
    }

    val productDetailsClientResponse = getProductDetailsResponse {
        results.add(energy.so.products.v2.productDetails {
            type = ProductType.ELECTRICITY_PRODUCT
            name = "electricity name smth"
            productVariantId = "1"
            display = CustomersData.GENERIC_STRING
            dayRate = RATE
            nightRate = RATE
            standardRate = RATE
            gspGroup = GspGroup._F
            directDebit = true
        })
    }

    val meterPointsResponseObj = meterPointsResponse {
        meterPoints.add(meterPoint {
            type = MeterPointType.MPAN
            meters.add(meter { id = ID_1 })
            dccServiceStatus = DccServiceStatus.ACTIVE
            measurementType = MeasurementType.IMPORT
            property = property {
                address = address {
                    id = CustomersData.ID_1
                    address1 = TEST_STRING.toNullableString()
                    address2 = TEST_STRING.toNullableString()
                    address3 = TEST_STRING.toNullableString()
                    address4 = TEST_STRING.toNullableString()
                    address5 = TEST_STRING.toNullableString()
                    postcode = TEST_STRING.toNullableString()
                    countryCode = "great_britain".toNullableString()
                    createdAt = nowLocalDateTime.toTimestamp()
                    updatedAt = nowLocalDateTime.toTimestamp()
                }
            }
            meterPointHistory = meterPointHistory {
                id = ID_1
                meterPointId = ID_1
                eventType = MeterPointHistory.MeterPointEventType.Created
                supplyStatus = MeterPointSupplyStatus.Registered
                createdAt = nowLocalDateTime.toTimestamp()
                updatedAt = nowLocalDateTime.toTimestamp()
            }
            ukGspGroup = "_F"
        })
    }

    val meterPointsResponseObjNoProperty = meterPointsResponse {
        meterPoints.add(meterPoint {
            meters.add(meter { id = ID_1 })
            dccServiceStatus = DccServiceStatus.ACTIVE
            ukGspGroup = "_F"
        })
    }

    val productDetailsResponseObj = productDetailsResponse {
        supplyAddress = energy.so.customers.v2.customers.address {
            id = CustomersData.ID_1
            address1 = SUPPLY_ADDRESS
            address2 = SUPPLY_ADDRESS
            address3 = SUPPLY_ADDRESS
            address4 = SUPPLY_ADDRESS
            address5 = SUPPLY_ADDRESS
            postcode = SUPPLY_ADDRESS_POSTCODE
            countryCode = "great_britain"
            createdAt = nowLocalDateTime.toTimestamp()
            updatedAt = nowLocalDateTime.toTimestamp()
        }
        productDetails = productDetails {
            display = CustomersData.GENERIC_STRING
            dayRate = RATE
            nightRate = RATE
            standardRate = RATE
            gspGroup = GspGroup._F.name
            productType = energy.so.customers.v2.customers.ProductType.ELECTRICITY_PRODUCT
        }
        estimated = USAGE
    }

    val propertyWithSupplyAddress = property {
        address = address {
            id = CustomersData.ID_1
            address1 = SUPPLY_ADDRESS.toNullableString()
            address2 = SUPPLY_ADDRESS.toNullableString()
            address3 = SUPPLY_ADDRESS.toNullableString()
            address4 = SUPPLY_ADDRESS.toNullableString()
            address5 = SUPPLY_ADDRESS.toNullableString()
            postcode = SUPPLY_ADDRESS_POSTCODE.toNullableString()
            countryCode = "great_britain".toNullableString()
            createdAt = nowLocalDateTime.toTimestamp()
            updatedAt = nowLocalDateTime.toTimestamp()
        }
        deleted = NullableTimestamp.getDefaultInstance()
        createdAt = nowLocalDateTime.toTimestamp()
        updatedAt = nowLocalDateTime.toTimestamp()
    }

    val getMPXNsByMeterPointIdsResponseWithSupplyAddress = getMPXNsByMeterPointIdsResponse {
        mPXNList.add(mPXN {
            property = propertyWithSupplyAddress
        })
    }

    val bobsUserInfo = UserInfo(
        email = EMAIL,
        customerId = CUSTOMER_ID,
        firstName = FIRST_NAME,
        lastName = LAST_NAME,
        accountId = billingAccountId
    )


    val transUnionAddresses = listOf(
        RegistrationCreditCheckAddress(
            billingAccountId = billingAccountId,
            buildingname = "Some address",
            street1 = "Address 2",
            street2 = "Address 3",
            postcode = "N9 RFG",
            isSupplyAddress = true,
        ),
        RegistrationCreditCheckAddress(
            billingAccountId = billingAccountId,
            buildingname = addressLine1,
            street1 = addressLine2,
            street2 = addressLine3,
            postcode = postCode,
            isSupplyAddress = false,
        ),
    )
}

object PsrData {
    private val CONSTANT_DATE1 = now()
    private val CONSTANT_DATE2 = now().withYear(2040)
    const val PSR_ID0 = 234L
    const val PSR_ID1 = 26L
    const val PSR_ID2 = 456L
    const val ACCOUNT_ID1 = 1L
    const val ACCOUNT_ID2 = 2L
    const val NAME0 = "Some name"
    const val NAME1 = "Pensionable age"
    const val NAME2 = "Deaf or hearing impaired"
    const val NOTE = "Some note"
    const val CUSTOMER_PSR_REL_ID0 = 1L
    const val CUSTOMER_PSR_REL_ID1 = 10L
    const val CUSTOMER_PSR_REL_ID2 = 20L
    const val WHD_CORE_GROUP_VULNERABILITY_ID = 26L

    val FROM_DATE: LocalDate = LocalDate.of(2020, Month.JANUARY, 1)
    val TO_DATE: LocalDate = LocalDate.of(2020, Month.FEBRUARY, 1)
    val PROPERTY_ID0 = 234L
    val PROPERTY_ID1 = 123L
    val PROPERTY_ID2 = 456L

    val psr0 = Psr(
        id = PSR_ID0,
        name = NAME0,
        codeElec = "elec0",
        codeGas = "gas0",
        expirable = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )
    val psr1 = Psr(
        id = PSR_ID1,
        name = NAME1,
        codeElec = "elec",
        codeGas = "gas",
        expirable = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )
    val psr2 = Psr(
        id = PSR_ID2,
        name = NAME2,
        codeElec = "elec2",
        codeGas = "gas2",
        expirable = true,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,

        )
    val psrWithBlankGasCode = Psr(
        id = PSR_ID1,
        name = NAME1,
        codeElec = "elec",
        codeGas = "",
        expirable = true
    )
    val psrWithBlankElecCode = Psr(
        id = PSR_ID1,
        name = NAME1,
        codeElec = "",
        codeGas = "gas",
        expirable = true
    )
    val customerPsr0 = CustomerPsr(
        psr = psr0,
        customerId = CUSTOMER_ID,
        propertyId = PROPERTY_ID0,
        id = CUSTOMER_PSR_REL_ID0,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )
    val customerPsr26 = CustomerPsr(
        psr = psr1,
        customerId = CUSTOMER_ID,
        propertyId = PROPERTY_ID1,
        id = CUSTOMER_PSR_REL_ID1,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate(),
    )

    val customerPsr2 = CustomerPsr(
        psr = psr2,
        customerId = CUSTOMER_ID_2,
        propertyId = PROPERTY_ID2,
        id = CUSTOMER_PSR_REL_ID2,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )

    val customerSetting = JooqCustomerSetting(
        id = 1,
    )
    val customer = Customer(
        id = ACCOUNT_ID1,
        number = "123456",
        firstName = "Michael",
        lastName = "Long",
        customerSettingId = 1L,
        customerMetadataId = CUSTOMER_METADATA_ID,
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS
    )
    val customer2 = Customer(
        id = ACCOUNT_ID2,
        number = "234567",
        firstName = "Joe",
        lastName = "Iron",
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS
    )
    val customerPsrRel0 = CustomerPsrRel(
        id = CUSTOMER_PSR_REL_ID0,
        customerId = ACCOUNT_ID1,
        psrId = PSR_ID0,
        propertyId = PROPERTY_ID0,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )
    val customerPsrRel1 = CustomerPsrRel(
        id = CUSTOMER_PSR_REL_ID1,
        customerId = ACCOUNT_ID1,
        psrId = PSR_ID1,
        propertyId = PROPERTY_ID1,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )
    val customerPsrRel2 = CustomerPsrRel(
        id = CUSTOMER_PSR_REL_ID2,
        customerId = ACCOUNT_ID2,
        psrId = PSR_ID2,
        propertyId = PROPERTY_ID2,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )
    val customer2PsrRel = CustomerPsrRel(
        id = CUSTOMER_PSR_REL_ID2,
        customerId = ACCOUNT_ID2,
        psrId = PSR_ID2,
        propertyId = PROPERTY_ID2,
        fromDate = CONSTANT_DATE1.toLocalDate(),
        toDate = CONSTANT_DATE2.toLocalDate()
    )
    val vulnerability = CustomVulnerability(
        id = ID_1,
        name = NAME2,
        propertyId = PROPERTY_ID1,
        juniferCustomVulnDfnId = WHD_CORE_GROUP_VULNERABILITY_ID,
        customerId = ACCOUNT_ID1,
        fromDate = LocalDate.now().minusMonths(10),
        toDate = LocalDate.now().plusMonths(10),
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )
    val expiredVulnerability = CustomVulnerability(
        id = ID_1,
        name = NAME2,
        propertyId = PROPERTY_ID1,
        juniferCustomVulnDfnId = WHD_CORE_GROUP_VULNERABILITY_ID,
        customerId = ACCOUNT_ID1,
        fromDate = LocalDate.now().minusMonths(10),
        toDate = LocalDate.now().minusMonths(1),
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )
    val createCustomerPsrRequest = let {
        createCustomerPsrRequest {
            accountId = ACCOUNT_ID1
            psrName = NAME1
            fromDate = FROM_DATE.toNullableTimestamp()
            toDate = TO_DATE.toNullableTimestamp()
            note = NOTE.toNullableString()
        }
    }

    val partialCreateCustomerPsrRequest = createCustomerPsrRequest {
        accountId = ACCOUNT_ID1
        psrName = NAME1
    }

    val createCustomerPsrRequestDto = CreateCustomerPsrRequestDto(
        accountId = ACCOUNT_ID1,
        psrName = NAME1,
        fromDate = FROM_DATE,
        toDate = TO_DATE,
        note = NOTE,
    )

    val customerUserRel = JooqCustomerUserRel(
        customerId = ID_1,
        userId = ID_1
    )

    val customerMetadata = CustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )

    val createCustomerVulnerabilityRequest = JuniferCreateCustomerVulnerabilityRequest {
        this.customerId = ID_1.toString()
        this.id = ID_1
        this.fromDt = nullableTimestamp { null_ = NullValue.NULL_VALUE }
        this.toDt = nullableTimestamp { null_ = NullValue.NULL_VALUE }
        this.propertyTblId = ID_1
    }
}

object AccountsData {

    const val CUSTOMER_ID = 123L
    const val JUNIFER_ACCOUNT_ID = 9876L
    const val ACCOUNT_ID: Long = 1
    const val CONTACT_ID: Long = 2
    const val NUMBER: String = "1000"
    private const val TICKET_DEFINITION_CODE = "SendMeterReadingReminderCommunication"
    private const val TICKET_DEFINITION_NAME = "Send Meter Reading Reminder Communication"
    private const val TICKET_STEP_CODE = "SendTheCommunication"
    private const val TICKET_STEP_NAME = "Send The Communication"
    private const val TICKET_PRIORITY = "Medium"
    private const val KEY_IDENTIFIER = "SENDMRMC"
    private const val SUMMARY = "Send monthly meter reading reminder for 'Joe Bloggs'"
    private const val DESCRIPTION = "Description"
    private const val ACCOUNT_LINK = "http://localhost:43002/rest/v1/accounts/103"
    private const val TICKET_ID = 10L
    private const val ACCOUNT_REVIEW_PERIOD_JUNIFER_ID = 111L
    const val EXCEPTION_MESSAGE = "dummy"
    const val EXCEPTION_CATEGORY = "tickets"
    const val ACCOUNT_EXCEPTION_CATEGORY = "account"

    val CREATED_AT: LocalDateTime = of(LocalDate.of(2022, Month.FEBRUARY, 27), LocalTime.of(10, 30, 0))

    val accountDto = BillingAccount(
        id = ACCOUNT_ID,
        type = BillingAccountType.CR_LANDLORD,
        name = "Bobs",
        number = "734",
        currency = "GBP",
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        customerId = CUSTOMER_ID,
        `class` = BillingAccountClass.INVOICE,
        from = localDateTimeNow,
        to = localDateTimeNow,
        settings = null
    )

    val createAccountTicketRequestCustomersProto = createAccountTicketRequestCustomerProto {
        accountId = ACCOUNT_ID.toString()
        ticketDefinitionCode = TICKET_DEFINITION_CODE
        ticketStepCode = TICKET_STEP_CODE
        summary = SUMMARY
        description = DESCRIPTION.toNullableString()
    }

    val createAccountTicketRequestJuniferProto = createAccountTicketRequestJuniferProto {
        accountId = ACCOUNT_ID.toString()
        ticketDefinitionCode = TICKET_DEFINITION_CODE
        ticketStepCode = TICKET_STEP_CODE
        summary = SUMMARY
        description = DESCRIPTION.toNullableString()
        parentTicketId = nullableInt64 { }
        assignedUserTeam = nullableString { }
    }

    private val createTicketEntities = createTicketEntitiesJuniferProto {
        accounts.addAll(listOf(ACCOUNT_LINK))
    }

    private val ticketLinks = ticketLinksJuniferProto { }

    val createAccountTicketResponseJuniferProto = createAccountTicketResponseJuniferProto {
        id = TICKET_ID
        ticketDefinitionCode = TICKET_DEFINITION_CODE
        ticketDefinitionName = TICKET_DEFINITION_NAME
        ticketPriority = TICKET_PRIORITY
        ticketStepCode = TICKET_STEP_CODE
        ticketStepName = TICKET_STEP_NAME
        status = createAccountTicketStatusJuniferProto.Open
        keyIdentifier = KEY_IDENTIFIER
        summary = SUMMARY
        createdDttm = CREATED_AT.toTimestamp()
        ticketEntities = createTicketEntities
        links = ticketLinks
    }

    private val createTicketEntitiesCustomerProto = createTicketEntitiesCustomerProto {
        accounts.addAll(listOf(ACCOUNT_LINK))
    }

    private val ticketLinksCustomerProto = ticketLinksCustomerProto {
        self = nullableString { }
        parentTicket = nullableString { }
    }

    val createAccountTicketResponseCustomerProto = createAccountTicketResponseCustomerProto {
        id = TICKET_ID
        ticketDefinitionCode = TICKET_DEFINITION_CODE
        ticketDefinitionName = TICKET_DEFINITION_NAME
        ticketPriority = TICKET_PRIORITY
        ticketStepCode = TICKET_STEP_CODE
        ticketStepName = TICKET_STEP_NAME
        status = CreateAccountTicketStatus.Open
        keyIdentifier = KEY_IDENTIFIER
        summary = SUMMARY
        description = nullableString { }
        createdDttm = CREATED_AT.toTimestamp()
        dueDttm = nullableTimestamp { }
        cancelDttm = nullableTimestamp { }
        ticketCancelReason = nullableString { }
        assignedUserTeam = nullableString { }
        stepStartDttm = nullableTimestamp { }
        stepScheduleDttm = nullableTimestamp { }
        stepDueDttm = nullableTimestamp { }
        parentTicketId = nullableInt64 { }
        ticketEntities = createTicketEntitiesCustomerProto
        links = ticketLinksCustomerProto


    }

    val updateAccountContactRequestJuniferProto = updateAccountContactRequestJuniferProto {
        accountId = ACCOUNT_ID.toString()
        contactId = CONTACT_ID.toString()
        title = "Mr".toNullableString()
        forename = "Joe".toNullableString()
        surname = "Doe"
        email = "<EMAIL>".toNullableString()
        dateOfBirth = nullableTimestamp { }
        phoneNumber1 = nullableString { }
        phoneNumber2 = nullableString { }
        phoneNumber3 = nullableString { }
        fromDttm = nullableTimestamp { }
        primary = nullableBoolean { }
        address = accountAddressJuniferProto {
            address1 = ""
            address2 = ""
            address3 = ""
            address4 = ""
            address5 = ""
            postcode = "F2G"
        }
        billDelivery = nullableString { }
        copyFromPreviousVersion = true
        receivePost = false
        receiveEmail = false
        receiveSMS = false
    }

    val updateAccountContactRequestCustomerProto = updateAccountContactRequestCustomerProto {
        accountId = ACCOUNT_ID.toString()
        contactId = CONTACT_ID.toString()
        title = "Mr".toNullableString()
        forename = "Joe".toNullableString()
        surname = "Doe"
        email = "<EMAIL>".toNullableString()
        address = accountAddressCustomerProto {
            postcode = "F2G"
        }
        receivePost = false
        receiveEmail = false
        receiveSMS = false
    }

    val editAccountReviewPeriodRequestProto = editAccountReviewPeriodRequest {
        accountId = ACCOUNT_ID
        reviewPeriodJuniferId = ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
        reason = "TEST"
        fromDttm = localDateTimeNow.toTimestamp()
        suppressDunningFl = true.toNullableBoolean()
        suppressBillingFl = false.toNullableBoolean()
        suppressPaymentCollectionFl = false.toNullableBoolean()
        description = "TEST".toNullableString()
    }

    val cancelAccountReviewPeriodRequestJuniferProto = cancelAccountReviewPeriodRequest {
        accountId = ACCOUNT_ID
        reviewPeriodJuniferId = ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
    }

    val createAccountReviewPeriodRequestJuniferProto = createAccountReviewPeriodRequest {
        accountId = ACCOUNT_ID
        reason = "TEST"
        fromDttm = localDateTimeNow.toTimestamp()
        toDttm = nullableTimestamp { }
        suppressDunningFl = true.toNullableBoolean()
        suppressBillingFl = false.toNullableBoolean()
        suppressPaymentCollectionFl = false.toNullableBoolean()
        description = "TEST".toNullableString()
    }

    val createAccountReviewPeriodResponseJuniferProto = createAccountReviewPeriodResponse {
        id = ACCOUNT_REVIEW_PERIOD_JUNIFER_ID + 1
    }

    val cancelNewAccountReviewPeriodRequestJuniferProto = cancelAccountReviewPeriodRequest {
        accountId = ACCOUNT_ID
        reviewPeriodJuniferId = ACCOUNT_REVIEW_PERIOD_JUNIFER_ID + 1
    }
}

object BillingAccountData {

    const val ID_1: Long = 1
    const val BILLING_ACCOUNT_ID: Long = 2
    const val CUSTOMER_SETTING_ID: Long = 1
    const val CUSTOMER_METADATA_ID: Long = 1
    const val CUSTOMER_ID: Long = 1
    const val PRODUCT_ACCOUNT_ID: Long = 1
    const val AGREEMENT_ID: Long = 1
    const val JUNIFER_PROPERTY_ID = 111L
    const val ACCOUNT_NUMBER = "123456"
    const val TEST_STRING = "TestString"
    const val METER_TIME_SWITCH_CODE = "1"
    const val SMSO_MARKET_PARTICIPANT = "VERM"
    const val STANDARD_SETTLEMENT_CONFIGURATION = "0003"
    const val METER_IDENTIFIER = "METER_IDENTIFIER"
    const val RATE_NAME = "rate-name"
    const val DAY_RATE = "Day"
    const val NIGHT_RATE = "Night"
    const val STANDARD_RATE = "Standard"

    val timeNow = now()
    val secondOfFebruary: LocalDateTime = of(2024, 2, 2, 0, 0, 0)

    val customer = Customer(
        id = CUSTOMER_ID,
        number = "someNumber",
        firstName = "firstName",
        lastName = "lastName",
        `class` = CustomerClass.CONSUMER_BUSINESS,
        type = CustomerType.BUSINESS,
        deleted = null,
        customerSettingId = CUSTOMER_SETTING_ID,
        customerMetadataId = CUSTOMER_METADATA_ID,
        createdAt = localDateTimeNow,
        updatedAt = now(),
    )

    val customerSetting = JooqCustomerSetting(
        id = CUSTOMER_SETTING_ID,
        marketingOptIn = false,
        smartMeterInterest = false,
        referralsEnabled = true,
        darkMode = false,
    )

    val billingAccount = JooqBillingAccount(
        id = BILLING_ACCOUNT_ID,
        currency = "GBP",
        fromDate = now(),
        toDate = now().plusDays(10),
        customerId = CUSTOMER_ID,
        name = "test",
        number = ACCOUNT_NUMBER,
        `class` = JooqBillingAccountClass.COST_STATEMENT,
        type = JooqBillingAccountType.CB_INVOICE,
        createdAt = now(),
        updatedAt = now(),
        deleted = null,
        closed = null,
        settingsId = ID_1
    )

    val billingAccountContact = JooqBillingAccountContact(
        id = ID_1,
        billingAccountId = BILLING_ACCOUNT_ID,
        contactId = BobsData.ID_1,
        fromDate = now(),
        toDate = now().plusDays(10),
        primary = true,
        deleted = null,
        createdAt = now(),
        updatedAt = now(),
        cancelled = null,
    )

    val billingAccountSettings = BillingAccountSettings(
        id = ID_1,
        billDeliveryMethod = BillDeliveryMethod.BOTH,
        billingCycle = BillingCycle.ANNUALLY,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null
    )

    val billingAccountSettingsWithNoTimeStamp = BillingAccountSettings(
        id = ID_1,
        billDeliveryMethod = BillDeliveryMethod.BOTH,
        billingCycle = BillingCycle.ANNUALLY,
    )

    val billingAccountModel = BillingAccount.fromJooq(billingAccount, null, billingAccountSettings)

    val billingContactRelation = JooqBillingAccountContact(
        id = 122L,
        billingAccountId = BILLING_ACCOUNT_ID,
        contactId = BobsData.contact.id,
        fromDate = now().minusDays(10),
        toDate = now().plusDays(10),
        primary = true
    )

    val address = Address(
        id = ID_1,
        address1 = TEST_STRING,
        address2 = TEST_STRING,
        address3 = TEST_STRING,
        postcode = TEST_STRING,
        countryCode = TEST_STRING,
        type = TEST_STRING,
        deleted = null,
        createdAt = timeNow,
        updatedAt = timeNow,
    )

    val jooqBillingAccountSettings = BillingAccountSetting(
        id = ID_1,
        billDeliveryMethod = energy.so.commons.model.enums.BillDeliveryMethod.BOTH,
        billingCycle = energy.so.commons.model.enums.BillingCycle.ANNUALLY,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null
    )

    val modelBillingAccount1 = BillingAccount(
        id = ID_1,
        currency = "GBP",
        from = nowLocalDateTime,
        to = nowLocalDateTime.plusDays(10),
        customerId = CUSTOMER_ID,
        name = "test",
        number = "123",
        `class` = JooqBillingAccountClass.COST_STATEMENT.toSiblingEnum(),
        type = JooqBillingAccountType.CB_INVOICE.toSiblingEnum(),
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null,
        closed = null,
        primaryContact = fromJooq(billingContactPojo, BobsData.address),
        settings = billingAccountSettings
    )

    val modelBillingAccountWithNoTimeStamp = BillingAccount(
        type = JooqBillingAccountType.CB_INVOICE.toSiblingEnum(),
        name = "test",
        number = "123",
        currency = "GBP",
        `class` = JooqBillingAccountClass.COST_STATEMENT.toSiblingEnum(),
        from = of(2024, 3, 3, 0, 0, 0),
        to = of(2024, 3, 3, 0, 0, 0).plusDays(10),
        customerId = CUSTOMER_ID,
        settings = billingAccountSettingsWithNoTimeStamp
    )

    val modelBillingAccount = BillingAccount.fromJooq(
        billingAccount,
        contact.copy(id = 24),
        billingAccountSettings
    )

    val agreement = JooqAgreement(
        fromDate = localDateTimeNow.withNano(0).withSecond(0).minusDays(2),
        toDate = localDateTimeNow.withNano(0).withSecond(0).plusDays(2),
        type = JooqAgreementType.FIXED_12_MONTHS,
        quoteId = 1L,
        createdAt = localDateTimeNow,
        updatedAt = now(),
        cancelled = false
    )

    val productAccount = JooqProductAccount(
        billingAccountId = BILLING_ACCOUNT_ID,
        type = JooqProductAccountType.ELECTRICITY,
        createdAt = now().withNano(0).withSecond(0),
        updatedAt = now().withNano(0).withSecond(0)
    )

    val productAccountAgreementRel = JooqProductAccountAgreementRel(
        id = 123L,
        productAccountId = PRODUCT_ACCOUNT_ID,
        agreementId = AGREEMENT_ID
    )

    val modelAgreement = Agreement(
        id = AGREEMENT_ID,
        type = AgreementType.FIXED_12_MONTHS,
        from = localDateTimeNow,
        to = localDateTimeNow.plusDays(2),
        cancelled = false,
        number = "123",
        quoteId = null,
        contents = null,
        deleted = null,
        createdAt = localDateTimeNow,
        updatedAt = now(),
        ordered = LocalDate.now()
    )

    val modelProductAccount = ModelProductAccount(
        id = PRODUCT_ACCOUNT_ID,
        type = ProductAccountType.ELECTRICITY,
        deleted = null,
        createdAt = now().withNano(0).withSecond(0),
        updatedAt = now().withNano(0).withSecond(0),
        billingAccount = modelBillingAccount,
        activeAgreements = listOf(modelAgreement),
        agreements = listOf(modelAgreement)
    )

    val customerMetadata = ModelsCustomerMetadata(
        id = CUSTOMER_METADATA_ID,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        lastAgreementRenew = nowLocalDateTime,
    )

    val addressGrpc = address {
        id = ID_1
        address1 = TEST_STRING.toNullableString()
        address2 = TEST_STRING.toNullableString()
        address3 = TEST_STRING.toNullableString()
        address4 = TEST_STRING.toNullableString()
        address5 = TEST_STRING.toNullableString()
        postcode = TEST_STRING.toNullableString()
        countryCode = TEST_STRING.toNullableString()
        type = TEST_STRING
        deleted = timeNow.toNullableTimestamp()
        createdAt = timeNow.toTimestamp()
        updatedAt = timeNow.toTimestamp()
    }

    private val propertyGrpc = property {
        id = ID_1
        type = PropertyType.FLAT
        address = addressGrpc
        deleted = timeNow.toNullableTimestamp()
        createdAt = timeNow.toTimestamp()
        updatedAt = timeNow.toTimestamp()
    }

    val assetMeterPoint = meterPoint {
        id = ID_1
        property = propertyGrpc
        identifier = METER_IDENTIFIER
        type = MeterPointType.MPAN
        supplyStartDate = timeNow.toTimestamp()
        changeOfTenancyFl = false
        ukProfileClass = UkProfileClass.DOMESTIC_ECONOMY_7
        ukGspGroup = TEST_STRING
        operationType = energy.so.assets.meterPoints.v2.OperationType.CREDIT
        readingFrequencyCode = TEST_STRING.toNullableString()
        serviceType = TEST_STRING.toNullableString()
        measurementType = MeasurementType.EXPORT
        dccServiceStatus = DccServiceStatus.ACTIVE
        deleted = timeNow.toNullableTimestamp()
        createdAt = timeNow.toTimestamp()
        updatedAt = timeNow.toTimestamp()
        meters.add(meter {
            id = ID_1
            identifier = METER_IDENTIFIER
            type = MeterType.CHECK
            registers.addAll(
                listOf(register {
                    id = ID_1
                    identifier = METER_IDENTIFIER.toNullableString()
                    type = TEST_STRING.toNullableString()
                    digits = 123
                    decimalPlaces = 5
                    rateName = TEST_STRING.toNullableString()
                    usage = 64L
                }, register {
                    id = ID_2
                    identifier = METER_IDENTIFIER.toNullableString()
                    type = TEST_STRING.toNullableString()
                    digits = 123
                    decimalPlaces = 5
                    rateName = TEST_STRING.toNullableString()
                    usage = 64L
                })
            )
        })
        meterPointElectricity = meterPointElectricity {
            id = ID_1
            energised = false
            lineLossFactorClassId = "1"
            measurementClass = TEST_STRING
            meterTimeSwitchCode = METER_TIME_SWITCH_CODE
            meterType = MeterType.CHECK
            smsoMarketParticipantCode = SMSO_MARKET_PARTICIPANT.toNullableString()
            standardSettlementConfigurationCode = STANDARD_SETTLEMENT_CONFIGURATION.toNullableString()
            deleted = timeNow.toNullableTimestamp()
            createdAt = timeNow.toTimestamp()
            updatedAt = timeNow.toTimestamp()
            mopMarketParticipant = TEST_STRING.toNullableString()
            distributionNetworkOperator = TEST_STRING.toNullableString()
        }
        meterPointGas = meterPointGas {
            id = ID_1
            designation = TEST_STRING.toNullableString()
            meterSerialNumber = "1".toNullableString()
            prepayDetected = true
            igtIdentifier = TEST_STRING.toNullableString()
            deleted = timeNow.toNullableTimestamp()
            createdAt = timeNow.toTimestamp()
            updatedAt = timeNow.toTimestamp()
        }
    }

    val mpanData = mpan {
        mpan = TEST_STRING
        address3 = TEST_STRING
        address5 = TEST_STRING
        address7 = TEST_STRING
        address8 = TEST_STRING
        address9 = TEST_STRING
        profileClassId = ID_1
        meterTimeswitchCode = ID_1
        lineLossFactorClassId = TEST_STRING
        gspGroupId = TEST_STRING
        standardSettlementConfigurationId = TEST_STRING
        meterTypeId = TEST_STRING
        meterSerialNumber = TEST_STRING
        smsoMarketParticipantId = TEST_STRING
        measurementClass = TEST_STRING
        energised = true
        dccServiceFlag = TEST_STRING
    }


    val meterIdToRegister = meterIdToRegister {
        meterId = ID_1
        registers.addAll(
            listOf(meterPointStructureRegister {
                id = ID_1
                rateName = RATE_NAME.toNullableString()
            })
        )
    }

    val multiMeterIdToRegister = meterIdToRegister {
        meterId = METER_ID
        registers.addAll(
            listOf(
                meterPointStructureRegister {
                    id = 1L
                    rateName = DAY_RATE.toNullableString()
                },
                meterPointStructureRegister {
                    id = 2L
                    rateName = NIGHT_RATE.toNullableString()
                },
            )
        )
    }

    val standardMeterIdToRegister = meterIdToRegister {
        meterId = METER_ID_2
        registers.add(
            meterPointStructureRegister {
                id = 1L
                rateName = STANDARD_RATE.toNullableString()
            }
        )
    }


    val meterIdToRegistersResponse = meterIdToRegistersResponse {
        response.add(meterIdToRegister)
    }

    val modelCustomer = CustomerModel(
        id = ID_1,
        number = ACCOUNT_NUMBER,
        createdAt = localDateTimeNow,
        firstName = "Joe",
        lastName = "Luck",
        deleted = null,
        updatedAt = localDateTimeNow,
        `class` = energy.so.customers.client.models.CustomerClass.CONSUMER_BUSINESS,
        type = energy.so.customers.client.models.CustomerType.BUSINESS,
        metadata = customerMetadata,
        customerSetting = customerSettings,
        primaryContact = contact,
    )

    val accountCreatedAt: LocalDateTime = of(2024, 1, 1, 1, 1, 1)
    val accountFromDt: LocalDate = LocalDate.of(2024, 1, 3)
    val accountToDt: LocalDate = accountFromDt.plusDays(10)
    val getAccountDetailsResponseProto = getAccountDetailsResponse {
        accountId = BILLING_ACCOUNT_ID
        accountNumber = ACCOUNT_NUMBER
        juniferAccountId = ID_1
        accountName = "test"
        balance = 5.0
        billingAccountAddress = accountDetailsAddress {
            address1 = "billingAddress1".toNullableString()
            address2 = "billingAddress2".toNullableString()
            address3 = "billingAddress3".toNullableString()
            countryCode = "billingCountryCode".toNullableString()
            postcode = "billingPostcode".toNullableString()
        }
        createdDttm = accountCreatedAt.toTimestamp()
        fromDt = accountFromDt.toTimestamp()
        toDt = accountToDt.toNullableTimestamp()
        supplyAddress = accountDetailsAddress {
            address1 = "supplyAddress1".toNullableString()
            address2 = "supplyAddress2".toNullableString()
            address3 = "supplyAddress3".toNullableString()
            countryCode = "supplyCountryCode".toNullableString()
            postcode = "supplyPostcode".toNullableString()
        }
        cancelled = false
        juniferPropertyId = JUNIFER_PROPERTY_ID
    }

    val getAccountDetailsWithoutAgreementResponseProto = getAccountDetailsResponse {
        accountId = BILLING_ACCOUNT_ID
        accountNumber = ACCOUNT_NUMBER
        juniferAccountId = ID_1
        accountName = "test"
        balance = 5.0
        billingAccountAddress = accountDetailsAddress {
            address1 = "billingAddress1".toNullableString()
            address2 = "billingAddress2".toNullableString()
            address3 = "billingAddress3".toNullableString()
            countryCode = "billingCountryCode".toNullableString()
            postcode = "billingPostcode".toNullableString()
        }
        createdDttm = accountCreatedAt.toTimestamp()
        fromDt = accountFromDt.toTimestamp()
        toDt = accountToDt.toNullableTimestamp()
        cancelled = false
    }

    val getAccountDetailsWithoutJuniferPropertyIdResponseProto = getAccountDetailsResponse {
        accountId = BILLING_ACCOUNT_ID
        accountNumber = ACCOUNT_NUMBER
        juniferAccountId = ID_1
        accountName = "test"
        balance = 5.0
        billingAccountAddress = accountDetailsAddress {
            address1 = "billingAddress1".toNullableString()
            address2 = "billingAddress2".toNullableString()
            address3 = "billingAddress3".toNullableString()
            countryCode = "billingCountryCode".toNullableString()
            postcode = "billingPostcode".toNullableString()
        }
        createdDttm = accountCreatedAt.toTimestamp()
        fromDt = accountFromDt.toTimestamp()
        toDt = accountToDt.toNullableTimestamp()
        supplyAddress = accountDetailsAddress {
            address1 = "supplyAddress1".toNullableString()
            address2 = "supplyAddress2".toNullableString()
            address3 = "supplyAddress3".toNullableString()
            countryCode = "supplyCountryCode".toNullableString()
            postcode = "supplyPostcode".toNullableString()
        }
        cancelled = false
    }

    val supplyAddressMpxnData = getMPXNsByMeterPointIdsResponse {
        mPXNList.add(mPXN {
            property = property {
                id = ID_1
                address = address {
                    id = ID_1
                    address1 = "supplyAddress1".toNullableString()
                    address2 = "supplyAddress2".toNullableString()
                    address3 = "supplyAddress3".toNullableString()
                    countryCode = "supplyCountryCode".toNullableString()
                    postcode = "supplyPostcode".toNullableString()
                }
            }
        })
    }
    val emptySupplyAddressMpxnData = getMPXNsByMeterPointIdsResponse {
        mPXNList.add(mPXN {
            property = property {
                id = ID_1
            }
        })
    }
    val emptyMpxnData = getMPXNsByMeterPointIdsResponse { }

    val olderModelAgreement = modelAgreement.copy(
        id = AGREEMENT_ID + 1,
        from = localDateTimeNow.minusDays(10),
        to = localDateTimeNow.minusDays(1)
    )

    val billingAccountTransactions = billingAccountTransactionsResponse {
        billingAccountTransaction.addAll(
            listOf(
                billingAccountTransaction {
                    id = ID_1
                    currency = "GBP"
                    reference = "ref"
                    createdDttm = localDateTimeNow.toTimestamp()
                    description = "test"
                    orderNo = 1
                    accountBalance = 10.0
                    status = BillingAccountTransaction.BillingAccountTransactionStatus.Rejected
                },
                billingAccountTransaction {
                    id = ID_1
                    currency = "GBP"
                    reference = "ref"
                    createdDttm = localDateTimeNow.toTimestamp()
                    description = "test"
                    orderNo = 0
                    accountBalance = 5.0
                    status = BillingAccountTransaction.BillingAccountTransactionStatus.Accepted
                }
            ))
    }
    val emptyBillingAccountTransactionsResponse = billingAccountTransactionsResponse { }

    val billingAccountAddress = Address(
        address1 = "billingAddress1",
        address2 = "billingAddress2",
        address3 = "billingAddress3",
        countryCode = "billingCountryCode",
        postcode = "billingPostcode",
        createdAt = localDateTimeNow,
        updatedAt = localDateTimeNow,
        type = "test"
    )

    val accountTicket1 = ticket {
        id = 1
        status = Status.Open
        keyIdentifier = "uSmart-IaC-54104"
        ticketDefinitionCode = "uSmartInstallAndCommission"
        summary = "test summary with date: ${now().plusDays(20).toLocalDate()}"
    }
    val accountTicket2 = accountTicket1.copy { id = 2; status = Status.Closed }

    val getAccountTicketsResponse = getTicketsForAccountNumberResponse {
        tickets.addAll(listOf(accountTicket1, accountTicket2))
    }
    val getAccountTicketsResponseNoneInDate = getTicketsForAccountNumberResponse {
        tickets.addAll(listOf(accountTicket1.copy {
            summary = "test summary with date: ${now().minusDays(999).toLocalDate()}"
        }, accountTicket2))
    }
}

object RemindersData {
    const val REMINDER_ID = 90L
    val TYPE = ReminderType.METER_READ
    val MONTHLY_FREQUENCY = ReminderFrequency.MONTHLY
    const val ALIGNMENT: Int = 5
    const val ACCOUNT_ID = 123L
    const val AGREEMENT_ID = 1L
    const val BILLING_ACCOUNT_ID = 789L
    const val CUSTOMER_ID = 789L
    const val CONTACT_ID = 709L
    const val ACTIVE = true

    val agreement = JooqAgreement(
        id = AGREEMENT_ID,
        type = JooqAgreementType.OPEN_ENDED,
        fromDate = localDateTimeNow.minusDays(1),
        toDate = localDateTimeNow.plusDays(1),
        number = "number",
        quoteId = 1,
        contents = oneAssetContents,
        updatedAt = now(),
        createdAt = localDateTimeNow
    )

    val customerSetting = JooqCustomerSetting(
        id = 1L,
        marketingOptIn = false,
        smartMeterInterest = false,
        referralsEnabled = true,
        darkMode = false,
    )

    val customerMetadata = JooqCustomerMetadata(
        id = 1L,
        brokerAgent = "test",
        psrRequestedAt = nowLocalDateTime,
        lastTicketAt = nowLocalDateTime,
        deleted = null,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime
    )

    val customer = CustomerModel(
        id = CUSTOMER_ID,
        firstName = "Joe",
        lastName = "Luck",
        `class` = CustomerClassModel.CONSUMER_BUSINESS,
        type = CustomerTypeModel.BUSINESS,
        number = "number",
        metadata = fromJooq(customerMetadata),
        customerSetting = fromJooq(customerSetting)
    )

    val contact = Contact(
        id = CONTACT_ID,
        firstName = "Joe",
        lastName = "Luck",
        email = "test@asd.c",
        type = ContactType.BUSINESS,
        title = Title.MR,
    )

    val customerContactPojo = CustomerContact(
        id = 1L,
        customerId = CUSTOMER_ID,
        contactId = CONTACT_ID,
        fromDate = localDateTimeNow.minusDays(10),
        toDate = localDateTimeNow.plusDays(10),
        primary = true
    )

    val billingAccountSettings = BillingAccountSettings(
        id = ID_1,
        billDeliveryMethod = BillDeliveryMethod.BOTH,
        billingCycle = BillingCycle.ANNUALLY,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
        deleted = null
    )

    val billingAccount = BillingAccount(
        id = BILLING_ACCOUNT_ID,
        type = BillingAccountType.PREPAY_ELECTRICITY,
        name = "Billing account name",
        number = "number",
        currency = "GBP",
        from = now().minusDays(1),
        to = now().plusDays(1),
        createdAt = now(),
        updatedAt = now(),
        deleted = null,
        customerId = CUSTOMER_ID,
        `class` = BillingAccountClass.COST_STATEMENT,
        primaryContact = null,
        settings = billingAccountSettings
    )

    val productAccount = ModelProductAccount(
        id = ACCOUNT_ID,
        type = JooqProductAccountType.ELECTRICITY.toSiblingEnum(),
        activeAgreements = listOf(Agreement.fromJooq(agreement)),
        billingAccount = billingAccount,
        createdAt = now(),
        updatedAt = now(),
        agreements = emptyList()
    )

    val jooqProductAccount = JooqProductAccount(
        id = ACCOUNT_ID,
        type = JooqProductAccountType.ELECTRICITY,
        billingAccountId = 1L,
        createdAt = now(),
        updatedAt = now()
    )

    val jooqProductAccountAgreementRel = JooqProductAccountAgreementRel(
        id = 123L,
        productAccountId = ACCOUNT_ID,
        agreementId = AGREEMENT_ID
    )

    val reminderData = Reminder(
        id = REMINDER_ID,
        billingAccount = billingAccount,
        type = TYPE,
        frequency = MONTHLY_FREQUENCY,
        alignment = ALIGNMENT,
        active = ACTIVE,
        createdAt = now(),
        updatedAt = now(),
    )

    val reminderData2 = Reminder(
        id = REMINDER_ID,
        billingAccount = billingAccount,
        type = TYPE,
        frequency = MONTHLY_FREQUENCY,
        alignment = ALIGNMENT,
        active = ACTIVE,
        lastSentAt = nowLocalDateTime,
        createdAt = nowLocalDateTime,
        updatedAt = nowLocalDateTime,
    )

    val updateReminderDto = UpdateReminderDto(
        accountId = ACCOUNT_ID,
        type = TYPE,
        frequency = ReminderFrequency.QUARTERLY,
        alignment = 1,
        active = true,
    )

    val createReminderDto = CreateReminderDto(
        billingAccountId = ACCOUNT_ID,
        type = TYPE,
        frequency = MONTHLY_FREQUENCY,
        alignment = ALIGNMENT,
        active = true
    )

    val searchReminderDto = searchReminderRequest {
        billingAccountId = ACCOUNT_ID
        type = TYPE.toSiblingEnum()
    }

    val createReminderRequest = createReminderRequest {
        accountId = ACCOUNT_ID
        type = TYPE.toSiblingEnum()
        frequency = MONTHLY_FREQUENCY.toSiblingEnum()
        alignment = ALIGNMENT.toNullableInt32()
        active = true
    }

    val updateReminderRequest = updateReminderRequest {
        accountId = ACCOUNT_ID
        type = TYPE.toSiblingEnum()
        frequency = ReminderFrequency.QUARTERLY.toSiblingEnum()
        alignment = 1.toNullableInt32()
        active = true.toNullableBoolean()
    }
}

object CustomerReferralData {

    val customerSettings = ModelsCustomerSetting(
        marketingOptIn = false,
        smartMeterInterest = false,
        referralsEnabled = false,
        darkMode = false,
    )

    val customerReferralJooq = CustomerReferral(
        id = 40,
        referredId = 1,
        refereeId = 1,
        credit = BigDecimal(100.********),
        status = ReferralStatu.PENDING,
        deleted = null
    )

    val cancellationJooq = energy.so.commons.model.tables.pojos.Cancellation(
        meterPointId = 1L,
        billingAccountId = 2L,
        reason = "reason",
        type = energy.so.commons.model.enums.CancellationType.METERPOINT,
        createdAt = of(2023, 3, 16, 16, 0, 0),
        updatedAt = of(2023, 3, 16, 16, 0, 0)
    )

    val customerReferral = energy.so.core.customers.server.models.CustomerReferral(
        id = 40,
        referredId = 1,
        refereeId = 1,
        credit = BigDecimal(100.********),
        status = energy.so.core.customers.server.models.ReferralStatus.PENDING
    )

    val cancellations = Cancellation(
        id = 1L,
        createdAt = of(2023, 3, 16, 16, 0, 0),
        updatedAt = of(2023, 3, 16, 16, 0, 0),
        reason = "reason",
        type = Cancellation.SwitchType.METERPOINT,
        meterPointId = 1L
    )

    val referralResponse = referralResponse {
        referrer = "John Doe"
        currentReferralFee = 100
    }
}

object CustomerSettingData {
    const val CUSTOMER_ID = 1L
    const val CUSTOMER_SETTING_ID = 123L

    val patchCustomerSettingRequest = patchCustomerSettingRequest {
        this.customerId = CUSTOMER_ID
        this.energySource = GrpcEnergySource.BIOMASS
    }

    val customerSettings = ModelsCustomerSetting(
        id = CUSTOMER_SETTING_ID,
        marketingOptIn = false,
        energySourceVote = EnergySource.BIOMASS,
        smartMeterInterest = false,
        referralsEnabled = false,
        darkMode = false,
        evType = EvType.ELECTRICITY,
        intendToBuyEv = false,
        evTariffMarketingConsent = false
    )

    val protoCustomerSettings = customerSettings.toProtobuf()
}

object CustomerPreferenceLogsData {
    const val CUSTOMER_ID = 1L
    val customerPreferenceLog = CustomerPreferenceLog(
        customerId = CUSTOMER_ID,
        type = PreferenceType.MARKETING_OPT_IN,
        preferenceLastCheckedAt = of(2023, 9, 12, 9, 0, 0)
    )
    val logGrpcRequest = preferenceCheckedLog {
        customerId = CUSTOMER_ID
        type = PreferenceTypeGrpc.MARKETING_OPT_IN
    }
}

object FeatureData {

    val VOUCHERS_FEATURE_NOT_ALLOWED_EXCEPTION_MESSAGE = "Feature not currently allowed: ${FeatureName.VOUCHERS}"

    val enabledVouchersFeatureProto = feature {
        name = FeatureName.VOUCHERS
        enabled = true
    }

    val disabledVouchersFeatureProto = feature {
        name = FeatureName.VOUCHERS
        enabled = false
    }

    val vouchersFeatureName = featureNameRequest {
        name = FeatureName.VOUCHERS
    }
}

object SyncData {
    val syncStatus = SyncStatus(
        id = ID_1,
        entityId = CustomersData.ID_1,
        transactionId = JUNIFER_CUSTOMER_ID.toString(),
        tableName = CUSTOMER.name
    )

    val jooqSyncStatus = SyncStatu(
        id = ID_1,
        entityId = CustomersData.ID_1,
        transactionId = JUNIFER_CUSTOMER_ID.toString(),
        tableName = CUSTOMER.name
    )
}

object AddressSync {
    val ADDRESS_1 = "test address1"
    val ADDRESS_2 = "test address2"
    val ADDRESS_3 = "test address3"
    val ADDRESS_4 = "test address4"
    val ADDRESS_5 = "test address5"
    val ADDRESS_6 = "test address6"
    val ADDRESS_7 = "test address7"
    val ADDRESS_8 = "test address8"
    val ADDRESS_9 = "test address9"
    val POSTCODE = "MK4 4TT"
    val COUNTRY_CODE = "GB"
    val ADDRESS_TYPE = "Residential"

    val createCustomerAddressSync = addressEntityRequest {
        addressEntity = addressEntity {
            address1 = ADDRESS_1.toNullableString()
            address2 = ADDRESS_2.toNullableString()
            address3 = ADDRESS_3.toNullableString()
            address4 = ADDRESS_4.toNullableString()
            address5 = ADDRESS_5.toNullableString()
            address6 = ADDRESS_6.toNullableString()
            address7 = ADDRESS_7.toNullableString()
            address8 = ADDRESS_8.toNullableString()
            address9 = ADDRESS_9.toNullableString()
            postcode = POSTCODE.toNullableString()
            countryCode = COUNTRY_CODE.toNullableString()
            type = ADDRESS_TYPE.toNullableString()
            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }

}

object AgreementSync {
    private val BILLING_ACCOUNT_ID = billingAccountId

    val createAgreementSync = agreementEntityRequest {
        agreementEntity = agreementEntity {
            type = "FIXED_12_MONTHS".toNullableString()
            from = now().minusDays(3L).toNullableTimestamp()
            to = now().plusDays(3L).toNullableTimestamp()
            number = "2".toNullableString()
            billable = true.toNullableBoolean()
            billingAccountId = BILLING_ACCOUNT_ID.toNullableInt64()
            productType = ProductAccountType.GAS.name.toNullableString()
            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }

}

object ContactSync {
    const val JUNIFER_CONTACT_ID = 123L
    const val JUNIFER_ACCOUNT_ID = 234L
    const val JUNIFER_ACCOUNT_NUMBER = "00456"
    val JUNIFER_ACCOUNT_FROM_DTTM = OffsetDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)
    const val FRESHDESK_CONTACT_ID = 345L
    const val JUNIFER_ACCOUNT_CORE_ID = 456L
    const val JUNIFER_CONTACT_CORE_ID = 567L
    const val EMAIL = "<EMAIL>"
    const val GOOGLE_EMAIL_GMAIL = "<EMAIL>"
    const val GOOGLE_EMAIL_GOOGLEMAIL = "<EMAIL>"
    const val MOBILE_PHONE = "**********"
    val otherEmails = listOf(
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    )
    val otherEmailsPlusGoogleMail = listOf(
        GOOGLE_EMAIL_GOOGLEMAIL,
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    )


    val createContactSync = contactEntityRequest {
        contactEntity = contactEntity {
            firstName = "Bob".toNullableString()
            lastName = "Smith".toNullableString()
            title = Title.MR.name.toNullableString()
            type = ContactType.BUSINESS.name.toNullableString()
            addressId = BobsData.addressPojo.id.toNullableInt64()
            syncTransactionId = JUNIFER_CONTACT_ID.toString().toNullableString()
        }

        operationType = OperationType.CREATE
    }

    val occupierCreateContactSync = createContactSync.copy {
        contactEntity = createContactSync.contactEntity.copy {
            lastName = "Occupier".toNullableString()
        }
    }

    val occupierDeleteContactSync = createContactSync.copy {
        operationType = OperationType.DELETE
    }

    val freshDeskContactSync = createContactSync.copy {
        contactEntity = createContactSync.contactEntity.copy {
            phoneNumber1 = PHONE_NUMBER1.toNullableString()
            phoneNumber2 = MOBILE_PHONE.toNullableString()
            email = EMAIL.toNullableString()
        }
    }

    val freshDeskContactSyncGoogleEmail = freshDeskContactSync.copy {
        contactEntity = freshDeskContactSync.contactEntity.copy {
            email = GOOGLE_EMAIL_GMAIL.toNullableString()
        }
    }

    val freshDeskContact = FreshdeskContactDto(
        id = FRESHDESK_CONTACT_ID,
        email = EMAIL,
        locale = "en",
        otherEmails = otherEmails,
        customFields = CustomFieldsDto(
            firstName = "Rupa",
            lastName = "Gini",
            juniferContactId = JUNIFER_CONTACT_ID
        )
    )

    val freshDeskContactGoogleEmail = freshDeskContact.copy(
        email = GOOGLE_EMAIL_GMAIL
    )

    val freshDeskContactPayload = FreshdeskContactRequestDto(
        phone = PHONE_NUMBER1,
        mobile = MOBILE_PHONE,
        name = "MR Bob Smith",
        otherEmails = otherEmails,
        email = EMAIL,
        customFields = CustomFieldsDto(
            firstName = "Bob",
            lastName = "Smith",
            supplyStartDate = JUNIFER_ACCOUNT_FROM_DTTM.toLocalDate(),
            accountLink = "https://nova.so.energy/customer/${JUNIFER_ACCOUNT_NUMBER}",
            juniferId = JUNIFER_ACCOUNT_ID,
            juniferAccount = JUNIFER_ACCOUNT_NUMBER.toLong(),
            juniferContactId = JUNIFER_CONTACT_ID,
        )
    )

    val freshDeskContactPayloadNoOtherEmails = freshDeskContactPayload.copy(
        otherEmails = emptyList()
    )

    val freshDeskContactPayloadGoogleEmail = freshDeskContactPayload.copy(
        otherEmails = listOf(GOOGLE_EMAIL_GOOGLEMAIL),
        email = GOOGLE_EMAIL_GMAIL
    )

    val freshDeskContactPayloadGoogleEmailAllOtherEmails = freshDeskContactPayload.copy(
        otherEmails = otherEmailsPlusGoogleMail,
        email = GOOGLE_EMAIL_GMAIL
    )

    val duplicateEmailErrorResponse = FreshdeskContactErrorResponseDto(
        errors = listOf(
            ErrorDto(
                field = "email",
                message = "It should be a unique value",
            )
        )
    )

    val unknownErrorResponse = FreshdeskContactErrorResponseDto(
        errors = listOf(
            ErrorDto(
                field = "name",
                message = "Error message",
            )
        )
    )

    val validBillingAccount = BillingAccountData.modelBillingAccount1.copy(
        id = JUNIFER_ACCOUNT_CORE_ID,
        number = JUNIFER_ACCOUNT_NUMBER,
        from = JUNIFER_ACCOUNT_FROM_DTTM.toLocalDateTime()
    )

    val closedBillingAccount = BillingAccountData.modelBillingAccount1.copy(
        id = JUNIFER_ACCOUNT_CORE_ID,
        number = JUNIFER_ACCOUNT_NUMBER,
        from = JUNIFER_ACCOUNT_FROM_DTTM.toLocalDateTime(),
        to = JUNIFER_ACCOUNT_FROM_DTTM.toLocalDateTime()
    )

    val billingAccountList = listOf(validBillingAccount, closedBillingAccount)

}

object BillingAccountContactSync {
    private val BILLING_ACCOUNT_ID = billingAccountId

    val creatBillingAccountContactSync = billingAccountContactEntityRequest {
        billingAccountContactEntity = billingAccountContactEntity {
            billingAccountId = BILLING_ACCOUNT_ID.toNullableInt64()
            contactId = ID_2.toNullableInt64()
            primary = true.toNullableBoolean()
            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }

    val billingAccountContact = JooqBillingAccountContact(
        billingAccountId = BILLING_ACCOUNT_ID,
        contactId = ID_2,
        primary = true,
    )
}

object BillingAccountSync {
    private val CUSTOMER_ID = BobsData.customerId
    val creatBillingAccountSync = billingAccountEntityRequest {
        billingAccountEntity = billingAccountEntity {
            customerId = CUSTOMER_ID.toNullableInt64()
            type = BillingAccountType.CR_INVOICE.name.toNullableString()
            name = "test".toNullableString()
            number = "578".toNullableString()
            class_ = BillingAccountClass.INVOICE.name.toNullableString()
            currency = "GBP".toNullableString()
            syncTransactionId = "2".toNullableString()
            settings = BillingSettingSync.accountSettingsSync
        }

        operationType = OperationType.CREATE
    }

    val createBillingAccountSync2 = billingAccountEntityRequest {
        billingAccountEntity = billingAccountEntity {
            customerId = CustomersData.ID_1.toNullableInt64()
            type = BillingAccountType.CR_INVOICE.name.toNullableString()
            name = "test".toNullableString()
            number = "578".toNullableString()
            class_ = BillingAccountClass.INVOICE.name.toNullableString()
            currency = "GBP".toNullableString()
            syncTransactionId = "2".toNullableString()
            settings = BillingSettingSync.accountSettingsSync
            cancelled = BillingAccountData.secondOfFebruary.toNullableTimestamp()
        }

        operationType = OperationType.CREATE
    }
}

object BillingSettingSync {
    val accountSettingsSync = billingAccountSettingEntity {
        billingCycle = BillingCycle.QUARTERLY_1ST_OF_MONTH.name.toNullableString()
        billDeliveryMethod = BillDeliveryMethod.EMAIL.name.toNullableString()
        receiveSms = false.toNullableBoolean()
        receiveEmail = false.toNullableBoolean()
        receivePost = false.toNullableBoolean()
    }
}

object CustomerContactSync {
    private val CUSTOMER_ID = BobsData.customerId

    val creatCustomerContactSync = customerContactEntityRequest {
        customerContactEntity = customerContactEntity {
            customerId = CUSTOMER_ID.toNullableInt64()
            contactId = ID_2.toNullableInt64()
            primary = false.toNullableBoolean()
            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }
}

object CustomerPropertyRelSync {
    private val CUSTOMER_ID = BobsData.customerId

    val creatCustomerPropertyRelSync = customerPropertyRelEntityRequest {
        customerPropertyRelEntity = customerPropertyRelEntity {
            customerId = CUSTOMER_ID.toNullableInt64()
            propertyId = PsrData.PROPERTY_ID1.toNullableInt64()

            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }
}

object PsrSync {
    val creatPsrSync = psrEntityRequest {
        psrEntity = psrEntity {
            name = "test".toNullableString()
            codeElec = "1".toNullableString()
            syncTransactionId = "2".toNullableString()
        }

        operationType = OperationType.CREATE
    }
}

object CustomerPsrRelSync {
    val creatCustomerPsrRelSync = customerPsrRelEntityRequest {
        customerPsrRelEntity = customerPsrRelEntity {
            customerId = 1L.toNullableInt64()
            psrId = 2L.toNullableInt64()
            propertyId = 3L.toString().toNullableString()
            syncTransactionId = "4".toNullableString()
        }

        operationType = OperationType.CREATE
    }
}


fun enrolmentPayload(
    marketingOptedIn: Boolean = true,
    quoteId: String? = "quote",
    extraAssistance: Boolean = true,
    supplyAddress: EnrolmentAddress = enrolmentAddress(),
    paymentMethod: EnrolmentPaymentMethod = enrolmentPaymentMethod(),
    contacts: Set<EnrolmentContactDetails> = setOf(
        enrolmentContactDetails(
            contactAddress = enrolmentAddress(
                careOf = "Contact Care of",
                address1 = "Contact address",
                address2 = "Contact Address 2",
                address3 = "Contact Address 3",
                postcode = "SE3 RFG",
                country = "GREAT_BRITAIN"
            ),
        )
    ),
    products: Set<EnrolmentProductDetails> = setOf(
        enrolmentProductDetails(type = energy.so.core.customers.server.models.ProductType.ELECTRICITY),
        enrolmentProductDetails(
            code = "GAS-RG-D",
            type = energy.so.core.customers.server.models.ProductType.GAS,
            meters = setOf(meter(identifier = "2123456")),
            consumptionEstimation = null
        )
    ),
    evType: EvType = EvType.ELECTRICITY,
    intendToBuyEv: Boolean = false,
    evTariffMarketingConsent: Boolean = false,
) = EnrolmentPayload(
    marketingOptedIn = marketingOptedIn,
    quoteId = quoteId,
    extraAssistance = extraAssistance,
    supplyAddress = supplyAddress,
    paymentMethod = paymentMethod,
    contacts = contacts,
    products = products,
    evType = evType,
    intendToBuyEv = intendToBuyEv,
    evTariffMarketingConsent = evTariffMarketingConsent
)

fun enrolmentAddress(
    careOf: String? = "Care of",
    address1: String? = "Some address",
    address2: String? = "Address 2",
    address3: String? = "Address 3",
    postcode: String? = "N9 RFG",
    country: String? = "GREAT_BRITAIN",
    deleted: Boolean? = null,
) = EnrolmentAddress(
    careOf = careOf,
    address1 = address1,
    address2 = address2,
    address3 = address3,
    postcode = postcode,
    country = country,
    deleted = deleted,
)

fun enrolmentPaymentMethod(
    type: String? = "direct_debit",
    bankAccountName: String? = BANK_ACCOUNT_NAME_ENCODED,
    bankAccountNumber: String? = BANK_ACCOUNT_NUMBER_ENCODED,
    bankAccountSortCode: String? = BANK_ACCOUNT_SORT_CODE_ENCODED,
    paymentDayOfMonth: Int? = 2,
    paymentAmount: String? = "345.65",
    seasonalPayment: Boolean? = true,
    deleted: Boolean? = null,
) = EnrolmentPaymentMethod(
    type = type,
    bankAccountName = bankAccountName,
    bankAccountNumber = bankAccountNumber,
    bankAccountSortCode = bankAccountSortCode,
    paymentDayOfMonth = paymentDayOfMonth,
    paymentAmount = paymentAmount,
    seasonalPayment = seasonalPayment,
    deleted = deleted,
)

fun enrolmentContactDetails(
    title: String? = "MR",
    firstName: String? = BobsData.contactFirstname,
    lastName: String? = BobsData.contactLastame,
    email: String? = BobsData.primaryContactEmail,
    birthdate: LocalDate? = LocalDate.of(2000, 1, 1),
    primaryContact: Boolean = true,
    communicationMethod: String? = "electronic",
    phone: String? = "*********",
    contactAddress: EnrolmentAddress? = enrolmentAddress(),
) = EnrolmentContactDetails(
    title = title,
    firstName = firstName,
    lastName = lastName,
    email = email,
    birthdate = birthdate,
    primaryContact = primaryContact,
    communicationMethod = communicationMethod,
    phone = phone,
    contactAddress = contactAddress,
)

fun enrolmentProductDetails(
    code: String? = "E2-RG-D",
    type: energy.so.core.customers.server.models.ProductType = energy.so.core.customers.server.models.ProductType.ELECTRICITY,
    meters: Set<Meter> = setOf(meter(identifier = "***************"), meter(identifier = "***************")),
    consumptionEstimation: ConsumptionEstimation? = consumptionEstimation(),
) = EnrolmentProductDetails(
    code = code,
    type = type,
    meters = meters,
    consumptionEstimation = consumptionEstimation,
)

fun meter(
    identifier: String? = "***************",
    measurementType: String? = "IMPORT",
) = Meter(
    identifier = identifier,
    measurementType = measurementType,
)

fun consumptionEstimation(
    consumption: Double? = 45.30,
    estimatedCost: Double? = 346.00,
    e7DaytimeConsumptionProportion: Double? = 4.0,
    period: String? = "month",
) = ConsumptionEstimation(
    consumption = consumption,
    estimatedCost = estimatedCost,
    e7DaytimeConsumptionProportion = e7DaytimeConsumptionProportion,
    period = ConsumptionEstimationPeriod.fromString(period!!),
)

fun brokerCreditCheck(
    broker: EnrolmentBroker = EnrolmentBroker.u_switch,
    createdAt: LocalDateTime = now(),
    updatedAt: LocalDateTime = now(),
    softSearchEnabledAt: LocalDateTime = enrolmentCreatedAt.minusMonths(1),
    hardSearchEnabledAt: LocalDateTime = enrolmentCreatedAt.minusMonths(1),
) = BrokerCreditCheck(
    broker = broker,
    softSearchEnabledAt = softSearchEnabledAt,
    hardSearchEnabledAt = hardSearchEnabledAt,
    createdAt = createdAt,
    updatedAt = updatedAt
)

fun registrationCreditCheck(
    accountId: Long = billingAccountId,
    number: String = BobsData.billingAccount.number!!,
    broker: EnrolmentBroker = EnrolmentBroker.u_switch,
    enrolmentType: EnrolmentType = EnrolmentType.NEW_ENROLMENT,
    created: LocalDateTime = enrolmentCreatedAt,
    from: LocalDateTime = enrolmentCreatedAt.plusDays(5),
    updated: LocalDateTime = enrolmentCreatedAt.plusMinutes(5),
    creditCheckStatus: CreditCheckStatu = CreditCheckStatu.FETCHED,
    scoreQs: Int? = null,
    scoreCa: Int? = null,
    matchLevelQs: MatchLevel? = null,
    firstName: String? = null,
    lastName: String? = null,
    depositDeadline: LocalDateTime? = null,
    depositAmount: BigDecimal? = null,
    retryCount: Short = 0,
) = RegistrationCreditCheck(
    billingAccountId = accountId,
    accountNumber = number,
    broker = broker,
    source = enrolmentType,
    enrolmentCreatedDt = created,
    billingAccountStartDt = from,
    updatedDt = updated,
    status = creditCheckStatus,
    aoScoreQs = scoreQs,
    aoScoreCa = scoreCa,
    matchLevelQs = matchLevelQs,
    firstName = firstName,
    lastName = lastName,
    depositDeadlineDt = depositDeadline,
    depositAmount = depositAmount,
    retryCount = retryCount
)

fun regCCAddress(
    accountId: Long = billingAccountId,
    buildingName: String? = "1 Sun Close",
    pc: String = "AL1 3GY",
) = RegistrationCreditCheckAddress(
    billingAccountId = accountId,
    buildingname = buildingName,
    postcode = pc

)

val registrationCommunicationRequest = CommunicationTemplateDto(
    communicationName = "Example Comms",
    attachments = emptyMap(),
    recipient = RecipientDto(
        name = "Recipient Name",
        email = "Recipient Email",
    ),
    customAttributes = emptyMap(),
    sendersEmail = SenderDto(
        name = "Sender example",
        email = "Sender email"
    ),
    bccRecipients = emptyList()
)

val depositCommunicationRequest = CommunicationTemplateDto(
    communicationName = "depositRequest",
    attachments = emptyMap(),
    recipient = RecipientDto(
        name = "Bob",
        email = "<EMAIL>",
    ),
    customAttributes = mapOf(
        CUSTOMER_NAME to "Bob",
        DEPOSIT_DEADLINE_DATE to "31/01/2025",
        DEPOSIT_DEADLINE_TIME to "12:00pm",
        DEPOSIT_AMOUNT to "252.00",
        ACCOUNT_NUMBER to "123",
        "email" to "<EMAIL>",
    ),
    bccRecipients = emptyList()
)

val activeBillingAccountItem = ActiveBillingAccountItem(
    billingAccountId = randomAlphabetic(10),
    juniferAccountId = randomAlphabetic(10),
    supplyStartDate = enrolmentCreatedAt.toLocalDate(),
    email = randomAlphabetic(10),
    firstName = randomAlphabetic(10),
    lastName = randomAlphabetic(10),
)