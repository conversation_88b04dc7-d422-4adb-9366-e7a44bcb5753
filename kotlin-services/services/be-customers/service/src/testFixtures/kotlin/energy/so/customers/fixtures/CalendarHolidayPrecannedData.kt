package energy.so.customers.fixtures

import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.v2.sync.OperationType
import energy.so.commons.v2.sync.syncResponse
import energy.so.core.customers.server.models.CalendarHoliday
import energy.so.customers.calendarholiday.v2.calendarHolidayList
import energy.so.customers.calendarholiday.v2.calendarHolidaysRequest
import energy.so.customers.sync.v2.calendarHolidayEntity
import energy.so.customers.sync.v2.calendarHolidayEntityRequest
import energy.so.customers.sync.v2.copy
import java.time.LocalDate
import energy.so.commons.model.tables.pojos.CalendarHoliday as JooqCalendarHoliday

object CalendarHolidayPrecannedData {
    const val ID_1 = 1L
    const val ID_2 = 2L
    const val ID_3 = 3L
    const val SYNC_TRANSACTION_ID = "0123456"
    val HOLIDAY_DATE = LocalDate.of(2023, 5, 1)
    val HOLIDAY_DATE2 = LocalDate.of(2023, 12, 1)
    val HOLIDAY_DATE3 = LocalDate.of(2024, 12, 1)
    val jooqCalendarHoliday = JooqCalendarHoliday(
        id = ID_1,
        holidayDate = HOLIDAY_DATE
    )
    val jooqCalendarHoliday2 = JooqCalendarHoliday(
        id = ID_2,
        holidayDate = HOLIDAY_DATE2
    )
    val jooqCalendarHoliday3 = JooqCalendarHoliday(
        id = ID_3,
        holidayDate = HOLIDAY_DATE3
    )

    val allCalendarHolidays = listOf(jooqCalendarHoliday, jooqCalendarHoliday2, jooqCalendarHoliday3)
        .map { CalendarHoliday.fromJooq(it) }
    val calendarHolidaysFrom2023 = listOf(jooqCalendarHoliday, jooqCalendarHoliday2)
        .map { CalendarHoliday.fromJooq(it) }

    val calendarHolidayRequest = calendarHolidaysRequest {
        year.add(2023)
    }

    val calendarHolidayListAll = calendarHolidayList {
        calendarHolidays.addAll(allCalendarHolidays.map { it.toCalendarHolidayProto() })
    }

    val calendarHolidayList2023 = calendarHolidayList {
        calendarHolidays.addAll(calendarHolidaysFrom2023.map { it.toCalendarHolidayProto() })
    }

    val invalidCalendarHolidayEntityRequestCreate = calendarHolidayEntityRequest {
        operationType = OperationType.CREATE
        calendarHolidayEntity = calendarHolidayEntity {}
    }

    val invalidCalendarHolidayEntityRequestPatch = calendarHolidayEntityRequest {
        operationType = OperationType.PATCH
        calendarHolidayEntity = calendarHolidayEntity {}
    }

    val invalidCalendarHolidayEntityRequestDelete = calendarHolidayEntityRequest {
        operationType = OperationType.DELETE
        calendarHolidayEntity = calendarHolidayEntity {}
    }

    val validCalendarHolidayEntityRequestCreate = calendarHolidayEntityRequest {
        operationType = OperationType.CREATE
        calendarHolidayEntity = calendarHolidayEntity {
            id = ID_1.toNullableInt64()
            syncTransactionId = SYNC_TRANSACTION_ID.toNullableString()
            holidayDate = HOLIDAY_DATE.toNullableTimestamp()
        }
    }

    val validCalendarHolidayEntityRequestPatch = validCalendarHolidayEntityRequestCreate.copy {
        operationType = OperationType.PATCH
    }

    val validCalendarHolidayEntityRequestDelete = validCalendarHolidayEntityRequestCreate.copy {
        operationType = OperationType.DELETE
    }

    val calendarHolidaySyncResponse = syncResponse {
        id = ID_1.toNullableInt64()
    }
}