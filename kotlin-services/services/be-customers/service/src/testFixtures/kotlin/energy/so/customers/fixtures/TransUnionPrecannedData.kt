package energy.so.customers.fixtures

import energy.so.core.customers.server.creditcheck.model.request.AutoSearchAddressLinks
import energy.so.core.customers.server.creditcheck.model.request.CreditCheckRequest
import energy.so.core.customers.server.creditcheck.model.request.MatchRequest
import energy.so.core.customers.server.creditcheck.model.request.RequestConfiguration
import energy.so.core.customers.server.creditcheck.model.response.CreditCheckData
import energy.so.core.customers.server.creditcheck.model.response.CreditCheckResponse
import energy.so.core.customers.server.transunion.config.TransUnionConfig
import energy.so.core.customers.server.transunion.model.request.TransUnionCreditCheckRequest
import energy.so.core.customers.server.transunion.model.request.TransUnionPasswordResetRequest
import energy.so.core.customers.server.transunion.model.response.Address
import energy.so.core.customers.server.transunion.model.response.Applicant
import energy.so.core.customers.server.transunion.model.response.Characteristic
import energy.so.core.customers.server.transunion.model.response.CharacteristicSet
import energy.so.core.customers.server.transunion.model.response.EnteredApplicant
import energy.so.core.customers.server.transunion.model.response.JobDetails
import energy.so.core.customers.server.transunion.model.response.Name
import energy.so.core.customers.server.transunion.model.response.TransUnionCreditCheckResponse
import energy.so.core.customers.server.transunion.model.response.TransUnionPasswordResetResponse
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Collections.singletonList
import org.apache.commons.lang3.RandomStringUtils.randomAlphabetic
import org.apache.commons.lang3.RandomUtils.nextBoolean
import org.apache.commons.lang3.RandomUtils.nextInt
import energy.so.core.customers.server.creditcheck.model.request.Address as CreditCheckAddressRequest
import energy.so.core.customers.server.creditcheck.model.request.Applicant as CreditCheckApplicantRequest
import energy.so.core.customers.server.creditcheck.model.request.Name as CreditCheckNameRequest
import energy.so.core.customers.server.creditcheck.model.response.Address as CreditCheckAddressResponse
import energy.so.core.customers.server.creditcheck.model.response.Name as CreditCheckNameResponse
import energy.so.core.customers.server.transunion.model.request.Address as TransUnionAddressRequest
import energy.so.core.customers.server.transunion.model.request.Applicant as TransUnionApplicantRequest
import energy.so.core.customers.server.transunion.model.request.AutoSearchAddressLinks as TransUnionAutoSearchAddressLinks
import energy.so.core.customers.server.transunion.model.request.MatchRequest as TransUnionMatchRequest
import energy.so.core.customers.server.transunion.model.request.Name as TransUnionNameRequest
import energy.so.core.customers.server.transunion.model.request.RequestConfiguration as TransUnionRequestConfiguration

/**
 * @created 23/10/2024
 * <AUTHOR>
 */

object TransUnionRequest {
    val transUnionConfig = TransUnionConfig("example.com", "example.com", "example", "example", "password1", "some_cluster", 1)

    private val APPLICANT_DOB = LocalDate.of(2023,2,1)

    private val addressLinks = TransUnionAutoSearchAddressLinks(
        enabled = nextBoolean(),
        maximumNumberOfAddressesToSearch = nextInt(),
    )

    private val requestConfiguration = TransUnionRequestConfiguration(
        autoSearchAddressLinks = addressLinks,
        excludeThirdPartyData = nextBoolean(),
        requestScore = nextBoolean(),
    )

    private val tuAddress = TransUnionAddressRequest(
        abodeNo = randomAlphabetic(10),
        buildingName = randomAlphabetic(10),
        buildingNo = randomAlphabetic(10),
        locality = randomAlphabetic(10),
        postCode = randomAlphabetic(10),
        postTown = randomAlphabetic(10),
        street1 = randomAlphabetic(10),
        street2 = randomAlphabetic(10),
        subLocality = randomAlphabetic(10),
    )

    private val tuName = TransUnionNameRequest(
        forename = randomAlphabetic(10),
        othername = randomAlphabetic(10),
        surname = randomAlphabetic(10),
        title = randomAlphabetic(10),
    )

    private val tuApplicant = TransUnionApplicantRequest(
        addresses = singletonList(tuAddress),
        dateOfBirth = APPLICANT_DOB,
        names = singletonList(tuName),
    )

    private val tuMatchRequest = TransUnionMatchRequest(
        applicants = singletonList(tuApplicant),
        purpose = randomAlphabetic(10)
    )

    val tuRequest = TransUnionCreditCheckRequest(
        matchRequest = tuMatchRequest,
        requestConfiguration = requestConfiguration,
    )
}

object PasswordReset {
   val newPassword = "123!xyZ_password"
   val transUnionPasswordResetRequest = TransUnionPasswordResetRequest(
       userName = "example\\example",
       currentPassword = "password1",
       newPassword = newPassword,
   )

    val tuPasswordResetSuccessResponse = TransUnionPasswordResetResponse(
        response = "Success"
    )
}

object TransUnionResponse {
    val APPLICANT_DOB = LocalDateTime.of(2024,1,1, 0,0,0)
    val tuSearchTime = LocalDateTime.of(2023,10,10,10,10,10)

    val tuAddress = Address(
        abodeNo = randomAlphabetic(10),
        buildingName = randomAlphabetic(10),
        buildingNo = randomAlphabetic(10),
        inputType = randomAlphabetic(10),
        locality = randomAlphabetic(10),
        postCode = randomAlphabetic(10),
        postTown = randomAlphabetic(10),
        street1 = randomAlphabetic(10),
        street2 = randomAlphabetic(10),
        subLocality = randomAlphabetic(10),
    )

    val tuName = Name(
        forename = randomAlphabetic(10),
        othername = randomAlphabetic(10),
        surname = randomAlphabetic(10),
        title = randomAlphabetic(10),
    )

    private val enteredApplicant = EnteredApplicant(
        addresses = singletonList(tuAddress),
        dateOfBirth = APPLICANT_DOB,
        names = singletonList(tuName),
    )

    val tuCharacterSet: CharacteristicSet = CharacteristicSet(
        numberOfCharacteristics = nextInt(),
        setName = "score",
        characteristics = singletonList(
            Characteristic(
                featureName = randomAlphabetic(10),
                serialNumber = nextInt(),
                value = randomAlphabetic(10),
            )
        )
    )

    val characterSet = CharacteristicSet(
        numberOfCharacteristics = 2,
        setName = "score",
        characteristics = listOf(
            Characteristic(
                featureName = "AOSCORE",
                serialNumber = 1185,
                value = "877",
            ),
            Characteristic(
                featureName = "reasonCodeSet",
                serialNumber = 1186,
                value = "110, 301, 302, 000",
            )
        )
    )

    val randomisedTuApplicant = Applicant(
        characteristicSets = singletonList(tuCharacterSet),
        dnaId = nextInt(),
        enteredApplicant = enteredApplicant,
        matchLevel = randomAlphabetic(10),
        numberOfCorrections = nextInt(),
        numberOfDisputes = nextInt(),
        totalNumberOfCharacteristics = nextInt(),
    )

    val tuApplicant = Applicant(
        characteristicSets = listOf(characterSet),
        dnaId = 12,
        enteredApplicant = enteredApplicant,
        matchLevel = "Individual",
        numberOfCorrections = 1,
        numberOfDisputes = 2,
        totalNumberOfCharacteristics = 500,
    )

    val randomizedTuResponse = TransUnionCreditCheckResponse(
        applicants = singletonList(randomisedTuApplicant),
        jobDetails = JobDetails(
            searchId = "UUID",
            searchTime = LocalDateTime.of(2023,10,10,10,10,10),
            yourReference = ""
        )
    )

    val tuResponse = TransUnionCreditCheckResponse(
        applicants = singletonList(tuApplicant),
        jobDetails = JobDetails(
            searchId = "UUID",
            searchTime = tuSearchTime,
            yourReference = ""
        )
    )

}

object CreditCheckRequest {
    val APPLICANT_DOB = LocalDate.of(2022, 2, 1)

    val ccAddressLinks = AutoSearchAddressLinks(
        enabled = nextBoolean(),
        maximumNumberOfAddressesToSearch = nextInt(),
    )

    val ccRequestConfiguration = RequestConfiguration(
        autoSearchAddressLinks = ccAddressLinks,
        excludeThirdPartyData = nextBoolean(),
        requestScore = nextBoolean(),
    )

    val ccAddress = CreditCheckAddressRequest(
        abodeNo = randomAlphabetic(10),
        buildingName = randomAlphabetic(10),
        buildingNo = randomAlphabetic(10),
        locality = randomAlphabetic(10),
        postCode = randomAlphabetic(10),
        postTown = randomAlphabetic(10),
        street1 = randomAlphabetic(10),
        street2 = randomAlphabetic(10),
        subLocality = randomAlphabetic(10),
    )

    val ccName = CreditCheckNameRequest(
        forename = randomAlphabetic(10),
        othername = randomAlphabetic(10),
        surname = randomAlphabetic(10),
        title = randomAlphabetic(10),
    )

    private val ccApplicant = CreditCheckApplicantRequest(
        addresses = singletonList(ccAddress),
        dateOfBirth = APPLICANT_DOB,
        names = singletonList(ccName),
    )

    val ccMatchRequest = MatchRequest(
        applicants = singletonList(ccApplicant),
        purpose = randomAlphabetic(10)
    )

    val ccRequest = CreditCheckRequest(
        matchRequest = ccMatchRequest,
        requestConfiguration = ccRequestConfiguration,
    )
}

object CreditCheckResponse {

    private val DATE_OF_BIRTH = LocalDate.of(2024,9,23)

    private val ccAddress = CreditCheckAddressResponse(
        abodeNo = randomAlphabetic(10),
        buildingName = randomAlphabetic(10),
        buildingNo = randomAlphabetic(10),
        inputType = randomAlphabetic(10),
        locality = randomAlphabetic(10),
        postCode = randomAlphabetic(10),
        postTown = randomAlphabetic(10),
        street1 = randomAlphabetic(10),
        street2 = randomAlphabetic(10),
        subLocality = randomAlphabetic(10),
    )

    private val ccName = CreditCheckNameResponse(
        title = randomAlphabetic(10),
        forename = randomAlphabetic(10),
        othername = randomAlphabetic(10),
        surname = randomAlphabetic(10),
    )

    private val ccData = CreditCheckData(
        dateOfBirth = DATE_OF_BIRTH,
        addresses = singletonList(ccAddress),
        names = singletonList(ccName),
        score = randomAlphabetic(10),
        totalNumOfCharacteristics = nextInt(),
        matchLevel = randomAlphabetic(10),
        reason = randomAlphabetic(10)
    )

    val ccResponse = CreditCheckResponse(
        success = nextBoolean(),
        failureReason = randomAlphabetic(10),
        data = singletonList(ccData),
        tuReference = randomAlphabetic(10),
    )
}