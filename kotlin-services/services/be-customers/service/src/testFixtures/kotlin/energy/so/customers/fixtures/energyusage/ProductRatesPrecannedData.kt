package energy.so.customers.fixtures.energyusage

import energy.so.ac.junifer.v1.meterpoint.meterPointTprInterval
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.core.customers.server.energyusage.MultiRateInterval
import energy.so.core.customers.server.energyusage.RatePeriod
import energy.so.customers.fixtures.agreement
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.MonthDay

const val JUNIFER_METERPOINT_ID = 693803L

const val JUNIFER_AGREEMENT_ID1 = 3919415L
val juniferAgreementId1StartDateTime = LocalDateTime.of(2023, 12, 1, 0, 0)
val juniferAgreementId1EndDateTime = LocalDateTime.of(2024, 3, 26, 0, 0)

const val JUNIFER_AGREEMENT_ID2 = 4044076L
val juniferAgreementId2StartDateTime = juniferAgreementId1EndDateTime
val juniferAgreementId2EndDateTime = LocalDateTime.of(2025, 3, 26, 0, 0)

const val JUNIFER_AGREEMENT_ID3 = 4638810L
val juniferAgreementId3StartDateTime = juniferAgreementId2EndDateTime
val juniferAgreementId3EndDateTime = LocalDateTime.of(2026, 3, 26, 0, 0)

val endOfTimeDateTime = LocalDateTime.of(9999, 1, 1, 0, 0)

const val CORE_METERPOINT_ID = 1L
const val CORE_AGREEMENT_ID1 = 2L
const val CORE_AGREEMENT_ID2 = 3L
const val CORE_AGREEMENT_ID3 = 4L

private const val DAY_RATE_NAME = "Day"
private const val DAY_TPRID = 40L
private const val NIGHT_RATE_NAME = "Night"
private const val NIGHT_TPRID = 193L

val intervalsProtoList = listOf(
    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID1
        agreementStartDate = juniferAgreementId1StartDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId1EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID2
        agreementStartDate = juniferAgreementId1EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId2EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 0
        endHour = 0
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = DAY_RATE_NAME
        ukTimePatternRegimeFk = 40
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 7
        startMinute = 30
        endHour = 0
        endMinute = 0
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 1
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 2
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 3
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 4
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 5
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 6
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },

    meterPointTprInterval {
        meterPointId = CORE_METERPOINT_ID
        agreementId = CORE_AGREEMENT_ID3
        agreementStartDate = juniferAgreementId2EndDateTime.toTimestamp()
        agreementEndDate = juniferAgreementId3EndDateTime.toNullableTimestamp()
        rateName = NIGHT_RATE_NAME
        ukTimePatternRegimeFk = 193
        dayOfTheWeekId = 7
        startDay = 1
        startMonth = 1
        endDay = 31
        endMonth = 12
        startHour = 0
        startMinute = 30
        endHour = 7
        endMinute = 30
    },
)

val multiRateIntervals = listOf(
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID1,
        agreementStartDate = juniferAgreementId1StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId1EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID2,
        agreementStartDate = juniferAgreementId2StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId2EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(0, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = DAY_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(7, 30),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = CORE_AGREEMENT_ID3,
        agreementStartDate = juniferAgreementId3StartDateTime.toLocalDate(),
        agreementEndDate = juniferAgreementId3EndDateTime.toLocalDate(),
        rateName = NIGHT_RATE_NAME,
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 30),
            endTime = LocalTime.of(7, 30),
        )
    )
)

val evTariffMultiRateIntervals = listOf(
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Peak",
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(5, 0),
            endTime = LocalTime.of(0, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.MONDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.TUESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.WEDNESDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.THURSDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.FRIDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.SATURDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
    MultiRateInterval(
        meterPointId = CORE_METERPOINT_ID,
        agreementId = agreement.id,
        agreementStartDate = agreement.from.toLocalDate(),
        agreementEndDate = agreement.to?.toLocalDate(),
        rateName = "SO EV Off-Peak",
        dayOfWeek = DayOfWeek.SUNDAY,
        rateActivePeriod = RatePeriod(
            startMonthDay = MonthDay.of(1, 1),
            endMonthDay = MonthDay.of(12, 31),
            startTime = LocalTime.of(0, 0),
            endTime = LocalTime.of(5, 0),
        )
    ),
)
