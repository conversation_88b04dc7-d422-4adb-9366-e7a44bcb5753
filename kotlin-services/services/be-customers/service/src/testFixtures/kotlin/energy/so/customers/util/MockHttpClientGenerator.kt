package energy.so.customers.util

import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class MockHttpClientGenerator(
    private val usFullUrl: Boolean = false,
    private val pathRegex: Regex,
    private val responseBody: String,
    private val responseCode: HttpStatusCode,
    private val responseHeaders: Headers = RESPONSE_HEADERS,
) {
    fun generate(): HttpClient {
        return HttpClient(generateMockEngine(usFullUrl)) {

            install(ContentNegotiation) {
                json(
                    Json {
                        isLenient = true
                        ignoreUnknownKeys = true
                    }
                )
            }

            defaultRequest {
                contentType(ContentType.Application.Json)
            }
        }
    }

    private fun generateByteReadChannel(responseObject: String): ByteReadChannel {
        return ByteReadChannel(responseObject.toByteArray(Charsets.UTF_8))
    }

    private fun generateMockEngine(useFullUrl: Boolean) = MockEngine { request ->
        val match: String = if (useFullUrl) request.url.toString() else request.url.encodedPath
        when {
            pathRegex.matches(match) -> {
                respond(
                    generateByteReadChannel(responseBody),
                    responseCode,
                    responseHeaders
                )
            }

            else -> error("Unhandled ${request.url.encodedPath}")
        }
    }

    companion object {
        private val RESPONSE_HEADERS = headersOf(
            "Content-Type" to listOf(ContentType.Application.Json.toString())
        )
    }
}
