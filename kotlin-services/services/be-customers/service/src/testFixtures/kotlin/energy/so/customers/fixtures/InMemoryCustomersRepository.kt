//package energy.so.customers.fixtures
//
//import energy.so.commons.model.enums.CustomerStatu
//import energy.so.commons.model.tables.pojos.Customer
//import energy.so.commons.model.tables.pojos.JuniferCustomer
//import energy.so.core.customers.server.database.repositories.CustomersRepository
//import energy.so.core.customers.server.database.models.UpsertCustomerDto
//import energy.so.customers.fixtures.CustomersData.INSTANT
//import java.time.Clock
//import java.time.ZoneOffset
//import org.jooq.exception.DataAccessException
//
//class InMemoryCustomerRepository(
//    val customers: MutableMap<Long, Customer> = mutableMapOf(),
//    // Customer / UserId
//    val customerLinks: MutableList<Pair<Customer, Long>> = mutableListOf(),
//    val clock: Clock = INSTANT,
//) : CustomersRepository {
//    override fun save(customer: Customer): Customer {
//        val customerToSave = customer
//            .assignKey()
//            .stampCreatedAt()
//            .assignStatus()
//        customers[customerToSave.id!!] = customerToSave
//        return customerToSave
//    }
//
//
//    override fun getById(id: Long): Customer? = customers[id]
//
//    private fun Customer.stampCreatedAt() = copy(
//        createdAt = clock.instant().atOffset(ZoneOffset.UTC),
//    )
//
//    private fun Customer.assignStatus() = copy(
//        status = status ?: CustomerStatu.PENDING,
//    )
//
//    private fun Customer.assignKey() = copy(
//        id = id ?: customers.keys.maxOrNull() ?: 1,
//    )
//}