package energy.so.customers.fixtures

import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.assets.meterPoints.v2.MeterPointSupplyStatus
import energy.so.assets.meterPoints.v2.meterPoint
import energy.so.assets.meterPoints.v2.meterPointHistory
import energy.so.assets.meterPoints.v2.meterPointsResponse
import energy.so.assets.properties.v2.address
import energy.so.assets.properties.v2.property
import energy.so.commons.grpc.clients.junifer.dtos.AccountNoteDto
import energy.so.commons.grpc.clients.junifer.dtos.AccountNoteType
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.enums.WarmHomeDiscountApplicationStatu
import energy.so.commons.model.enums.WarmHomeDiscountCriteriaType
import energy.so.commons.model.tables.pojos.WarmHomeDiscountApplicationStatusHistory
import energy.so.commons.model.tables.pojos.WarmHomeDiscountCriteria
import energy.so.commons.model.tables.pojos.WarmHomeDiscountCriteriaRel
import energy.so.core.customers.server.database.models.WarmHomeDiscountApplicationStatusInfo
import energy.so.core.customers.server.database.models.WhdCriteriaDto
import energy.so.core.customers.server.mappers.getCustomerStatus
import energy.so.core.customers.server.models.Agreement
import energy.so.core.customers.server.models.AgreementType
import energy.so.core.customers.server.models.ArkStatusHistory
import energy.so.core.customers.server.models.BillingAccount
import energy.so.core.customers.server.models.BillingAccountClass
import energy.so.core.customers.server.models.BillingAccountType
import energy.so.core.customers.server.models.CustomerDetails
import energy.so.core.customers.server.models.ProductAccount
import energy.so.core.customers.server.models.ProductAccountType
import energy.so.core.customers.server.models.WarmHomeDiscountApplication
import energy.so.core.customers.server.models.WarmHomeDiscountApplicationDto
import energy.so.core.customers.server.models.WarmHomeDiscountApplicationRequest
import energy.so.core.customers.server.services.WarmHomeDiscountService
import energy.so.core.customers.server.services.getCurrentApplicationYear
import energy.so.customers.fixtures.BillingAccountData.ACCOUNT_NUMBER
import energy.so.customers.fixtures.BillingAccountData.BILLING_ACCOUNT_ID
import energy.so.customers.fixtures.BobsData.ID_1
import energy.so.customers.fixtures.BobsData.billingAccountModel
import energy.so.customers.warmhomediscount.v2.addCoreGroupVulnerabilityRequest
import energy.so.customers.warmhomediscount.v2.addCoreGroupVulnerabilityResponse
import energy.so.customers.warmhomediscount.v2.closeNoEvidenceResponse
import energy.so.customers.warmhomediscount.v2.copy
import energy.so.customers.warmhomediscount.v2.createWarmHomeDiscountApplicationRequest
import energy.so.customers.warmhomediscount.v2.createWarmHomeDiscountApplicationResponse
import energy.so.customers.warmhomediscount.v2.criteria
import energy.so.customers.warmhomediscount.v2.criteriaResponse
import energy.so.customers.warmhomediscount.v2.getWarmHomeDiscountApplication
import energy.so.customers.warmhomediscount.v2.getWarmHomeDiscountApplicationRequest
import energy.so.customers.warmhomediscount.v2.getWarmHomeDiscountApplicationResponse
import energy.so.customers.warmhomediscount.v2.getWarmHomeDiscountApplicationsResponse
import energy.so.customers.warmhomediscount.v2.images
import energy.so.customers.warmhomediscount.v2.newAttributes
import energy.so.customers.warmhomediscount.v2.updateWarmHomeDiscountApplicationsRequest
import energy.so.customers.warmhomediscount.v2.updateWarmHomeDiscountApplicationsResponse
import energy.so.customers.warmhomediscount.v2.warmHomeDiscountApplication
import energy.so.customers.warmhomediscount.v2.warmHomeDiscountApplicationStatusInfo
import energy.so.customers.warmhomediscount.v2.warmHomeDiscountApplicationsRequest
import energy.so.customers.warmhomediscount.v2.warmHomeDiscountArkStatusHistory
import energy.so.customers.warmhomediscount.v2.warmHomeDiscountCustomerDetails
import energy.so.customers.warmhomediscount.v2.whdError
import energy.so.payments.v2.accountCredit
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import java.time.OffsetDateTime
import java.time.ZoneOffset
import org.jooq.JSON
import energy.so.commons.model.tables.pojos.WarmHomeDiscountApplication as JooqWarmHomeDiscountApplication

const val BOBS_FIRST_NAME = "Bob"
const val BOBS_LAST_NAME = "Burnquist"
const val BOBS_PHONE = "*********"
const val APPLICATION_RECEIVED_STATUS = "application_received"
const val PRIMARY_CRITERIA_ID = 38L
const val SECONDARY_CRITERIA_ID = 43L
const val BOBS_USER_ID = 134L
const val BOBS_APPLICATION_ID = 1L
const val ASSET_ID = 412L
const val BOBS_EMAIL = "<EMAIL>"
const val AGENT = "agent"
const val IMAGE_NAME = "IMAGE NAME"
const val IMAGE_URL = "IMAGE URL"
const val WARM_HOME_DISCOUNT_APPLICATION_ADDRESS = "123 Test, Test street, London"
const val WARM_HOME_DISCOUNT_APPLICATION_POSTCODE = "123 456"
const val WARM_HOME_DISCOUNT_APPLICATION_GSP_GROUP = "_N"
val APPLICATION_YEAR = getCurrentApplicationYear(LocalDate.now())
val DEADLINE_YEAR = run {
    val today = LocalDate.now()
    if (today.month > Month.MARCH) {
        today.year + 1
    } else {
        today.year
    }
}
val GET_FILE_REQUEST_DATE: LocalDate = LocalDate.of(2024, 8, 15)
val GET_FILE_REQUEST_DATE_BEFORE_MARCH: LocalDate = LocalDate.of(2024, 1, 15)

val bobsDateOfBirth = LocalDate.of(1988, 4, 2)
val now = LocalDateTime.now()

val bobsCreateWarmHomeDiscountApplicationRequest = createWarmHomeDiscountApplicationRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    userId = BOBS_USER_ID
    firstName = BOBS_FIRST_NAME
    lastName = BOBS_LAST_NAME
    dateOfBirth = bobsDateOfBirth.toTimestamp()
    phoneNumber = BOBS_PHONE
    primaryCriteriaId = PRIMARY_CRITERIA_ID
    secondaryCriteriaId = SECONDARY_CRITERIA_ID
    status = APPLICATION_RECEIVED_STATUS
    applicationYear = APPLICATION_YEAR
    email = BOBS_EMAIL
}

val bobsWarmHomeDiscountApplicationRequest = WarmHomeDiscountApplicationRequest(
    billingAccountId = BILLING_ACCOUNT_ID,
    userId = BOBS_USER_ID,
    firstName = BOBS_FIRST_NAME,
    lastName = BOBS_LAST_NAME,
    dateOfBirth = bobsDateOfBirth,
    phoneNumber = BOBS_PHONE,
    primaryCriteriaId = PRIMARY_CRITERIA_ID,
    secondaryCriteriaId = SECONDARY_CRITERIA_ID,
    status = APPLICATION_RECEIVED_STATUS,
    applicationYear = APPLICATION_YEAR,
    email = BOBS_EMAIL,
    communicationPreference = "email"
)

val bobsWarmHomeDiscountApplication = WarmHomeDiscountApplication(
    id = BOBS_APPLICATION_ID,
    billingAccountId = BILLING_ACCOUNT_ID,
    userId = BOBS_USER_ID,
    firstName = BOBS_FIRST_NAME,
    lastName = BOBS_LAST_NAME,
    dateOfBirth = bobsDateOfBirth,
    phoneNumber = BOBS_PHONE,
    applicationYear = APPLICATION_YEAR,
    primaryCriteriaId = PRIMARY_CRITERIA_ID,
    secondaryCriteriaId = SECONDARY_CRITERIA_ID,
    status = WarmHomeDiscountApplicationStatu.application_received,
    assignedAgent = null,
    billingAccountNumber = ACCOUNT_NUMBER,
    createdAt = now,
    updatedAt = now,
    email = BOBS_EMAIL,
    communicationPreference = "email"
)

val succesWarmHomeDiscountApplicationProto = warmHomeDiscountApplication {
    id = BOBS_APPLICATION_ID
    billingAccountId = BILLING_ACCOUNT_ID
    userId = BOBS_USER_ID
    firstName = BOBS_FIRST_NAME
    lastName = BOBS_LAST_NAME
    dateOfBirth = bobsDateOfBirth.toTimestamp()
    phoneNumber = BOBS_PHONE
    applicationYear = APPLICATION_YEAR
    primaryCriteriaId = PRIMARY_CRITERIA_ID
    secondaryCriteriaId = SECONDARY_CRITERIA_ID
    status = getCustomerStatus(APPLICATION_RECEIVED_STATUS)
    arkStatus = APPLICATION_RECEIVED_STATUS
    billingAccountNumber = ACCOUNT_NUMBER
    createdAt = now.toTimestamp()
    updatedAt = now.toTimestamp()
    email = BOBS_EMAIL
}

val bobsCreateWarmHomeDiscountApplicationResponse = createWarmHomeDiscountApplicationResponse {
    warmHomeDiscountApplication = succesWarmHomeDiscountApplicationProto
}

val warmDiscountHomeApplicationToBeSaved = JooqWarmHomeDiscountApplication(
    billingAccountId = BILLING_ACCOUNT_ID,
    userId = BOBS_USER_ID,
    firstName = BOBS_FIRST_NAME,
    lastName = BOBS_LAST_NAME,
    dateOfBirth = bobsDateOfBirth,
    phoneNumber = BOBS_PHONE,
    applicationYear = APPLICATION_YEAR,
    primaryCriteriaId = PRIMARY_CRITERIA_ID,
    secondaryCriteriaId = SECONDARY_CRITERIA_ID,
    status = WarmHomeDiscountApplicationStatu.application_received,
    billingAccountNumber = ACCOUNT_NUMBER,
    email = BOBS_EMAIL,
    communicationPreference = "email"
)

val warmDiscountHomeApplicationToBeSavedWithAddress = warmDiscountHomeApplicationToBeSaved.copy(
    address = WARM_HOME_DISCOUNT_APPLICATION_ADDRESS,
    postcode = WARM_HOME_DISCOUNT_APPLICATION_POSTCODE,
    gspGroup = WARM_HOME_DISCOUNT_APPLICATION_GSP_GROUP
)

val primaryCriteria = WarmHomeDiscountCriteria(
    id = PRIMARY_CRITERIA_ID,
    text = "test",
    type = WarmHomeDiscountCriteriaType.primary,
    ordinal = BigDecimal.ONE
)

val secondaryCriteria = WarmHomeDiscountCriteria(
    id = SECONDARY_CRITERIA_ID,
    text = "test",
    type = WarmHomeDiscountCriteriaType.secondary
)

val whdCriteriaRel = WarmHomeDiscountCriteriaRel(
    primaryCriteriaId = PRIMARY_CRITERIA_ID,
    secondaryCriteriaId = SECONDARY_CRITERIA_ID,
    ordinal = BigDecimal.ONE
)

val warmHomeDiscountApplicationStatusLogApplicationReceived = WarmHomeDiscountApplicationStatusHistory(
    id = 1,
    warmHomeDiscountApplicationId = BOBS_APPLICATION_ID,
    status = WarmHomeDiscountApplicationStatu.application_received,
    createdAt = now
)

val warmHomeDiscountApplicationStatusLogApplicationPaid = WarmHomeDiscountApplicationStatusHistory(
    id = 1,
    warmHomeDiscountApplicationId = BOBS_APPLICATION_ID,
    status = WarmHomeDiscountApplicationStatu.paid,
    createdAt = now
)

val warmHomeDiscountApplicationStatusLogApplicationClosed = WarmHomeDiscountApplicationStatusHistory(
    id = 1,
    warmHomeDiscountApplicationId = BOBS_APPLICATION_ID,
    status = WarmHomeDiscountApplicationStatu.closed_account,
    createdAt = now
)

val warmHomeDiscountApplicationStatusLogApplicationRejectedApproved = WarmHomeDiscountApplicationStatusHistory(
    id = 1,
    warmHomeDiscountApplicationId = BOBS_APPLICATION_ID,
    status = WarmHomeDiscountApplicationStatu.rejected_approved,
    createdAt = now
)

val warmHomeDiscountApplicationStatusLogApplicationNoEvidenceProvided = WarmHomeDiscountApplicationStatusHistory(
    id = 1,
    warmHomeDiscountApplicationId = BOBS_APPLICATION_ID,
    status = WarmHomeDiscountApplicationStatu.rejected_approved,
    createdAt = now
)

val getCriteriaResponse = criteriaResponse {
    criteria.addAll(listOf(criteria {
        id = 1
        text = "criteria1"
        secondaryWhdCriteria.addAll(listOf(criteria {
            id = 2
            text = "criteria2"
        }, criteria {
            id = 3
            text = "criteria3"
        }))
    }, criteria {
        id = 4
        text = "criteria4"
        secondaryWhdCriteria.addAll(listOf(criteria {
            id = 5
            text = "criteria5"
        }, criteria {
            id = 6
            text = "criteria6"
        }))
    }))
}

val getCriteriaDto = listOf(
    WhdCriteriaDto(
        id = 1,
        text = "criteria1",
        secondaryWhdCriteria = listOf(
            WhdCriteriaDto(id = 2, text = "criteria2"),
            WhdCriteriaDto(id = 3, text = "criteria3")
        )
    ),
    WhdCriteriaDto(
        id = 4,
        text = "criteria4",
        secondaryWhdCriteria = listOf(
            WhdCriteriaDto(id = 5, text = "criteria5"),
            WhdCriteriaDto(id = 6, text = "criteria6")
        )
    )
)

val getWarmHomeDiscountApplication = getWarmHomeDiscountApplication {
    id = BOBS_APPLICATION_ID
    billingAccountId = BILLING_ACCOUNT_ID
    userId = BOBS_USER_ID
    firstName = BOBS_FIRST_NAME
    lastName = BOBS_LAST_NAME
    dateOfBirth = bobsDateOfBirth.toTimestamp()
    phoneNumber = BOBS_PHONE
    email = BOBS_EMAIL
    applicationYear = APPLICATION_YEAR
    primaryCriteriaId = PRIMARY_CRITERIA_ID
    secondaryCriteriaId = SECONDARY_CRITERIA_ID
    status = APPLICATION_RECEIVED_STATUS
    billingAccountNumber = ACCOUNT_NUMBER
    createdAt = now.toTimestamp()
    updatedAt = now.toTimestamp()
    primaryWhdCriterionText = "primaryCriteria"
    secondaryWhdCriterionText = "secondaryCriteria"
    customerDetails = warmHomeDiscountCustomerDetails {}
    arkStatusHistory.add(warmHomeDiscountArkStatusHistory {
        arkStatus = APPLICATION_RECEIVED_STATUS
        createdAt = now.toTimestamp()
    })
}

val getWarmHomeDiscountApplicationsResponse = getWarmHomeDiscountApplicationsResponse {
    getApplication.add(getWarmHomeDiscountApplication)
}

val getWarmHomeDiscountApplicationsWithCustomerDetailsResponse = getWarmHomeDiscountApplicationsResponse {
    getApplication.add(
        getWarmHomeDiscountApplication.copy {
            customerDetails = warmHomeDiscountCustomerDetails {
                address = WARM_HOME_DISCOUNT_APPLICATION_ADDRESS
                postcode = WARM_HOME_DISCOUNT_APPLICATION_POSTCODE
            }
        }
    )
}

val getWarmHomeDiscountApplicationsWithCustomerDetailsResponseWithNoCustomerDetails =
    getWarmHomeDiscountApplicationsResponse {
        getApplication.add(
            getWarmHomeDiscountApplication.copy {
                customerDetails = warmHomeDiscountCustomerDetails {}
            }
        )
    }

val warmHomeDiscountApplicationsRequest = warmHomeDiscountApplicationsRequest {
    status = "application_received"
    skipAccountNumbers.add("1")
    numberOfApplications = 1

}

val warmHomeDiscountApplicationDto = WarmHomeDiscountApplicationDto(
    id = BOBS_APPLICATION_ID,
    accountId = BILLING_ACCOUNT_ID,
    userId = BOBS_USER_ID,
    firstName = BOBS_FIRST_NAME,
    familyName = BOBS_LAST_NAME,
    dateOfBirth = bobsDateOfBirth,
    phoneNumber = BOBS_PHONE,
    email = BOBS_EMAIL,
    applicationYear = APPLICATION_YEAR,
    primaryCriteriaId = PRIMARY_CRITERIA_ID,
    secondaryCriteriaId = SECONDARY_CRITERIA_ID,
    status = APPLICATION_RECEIVED_STATUS,
    billingAccountNumber = ACCOUNT_NUMBER,
    createdAt = now,
    updatedAt = now,
    assignedAgent = null,
    primaryWhdCriterionText = "primaryCriteria",
    secondaryWhdCriterionText = "secondaryCriteria",
    customerDetails = CustomerDetails(
        address = WARM_HOME_DISCOUNT_APPLICATION_ADDRESS,
        postcode = WARM_HOME_DISCOUNT_APPLICATION_POSTCODE,
        gspGroup = WARM_HOME_DISCOUNT_APPLICATION_GSP_GROUP
    ),
    arkStatusHistory = listOf(
        ArkStatusHistory(
            arkStatus = APPLICATION_RECEIVED_STATUS,
            arkStatusCreatedAt = now
        )
    )
)

val activeAgreement = Agreement(
    id = ID_1,
    type = AgreementType.OPEN_ENDED,
    from = LocalDateTime.now().minusDays(1),
    cancelled = false,
    number = "********",
    createdAt = LocalDateTime.now().minusDays(1),
    updatedAt = LocalDateTime.now().minusDays(1),
    deleted = null,
    quoteId = null,
    contents = JSON.valueOf(
        """
        {
            "productvariants":[
                {
                    "id":${ID_1},
                    "meterpoints":[
                        {
                            "id":"${ID_1}"
                        }
                    ]
                }
            ]
        }
    """.trimIndent()
    )
)

val agreementContentNoMeterpointsJSON = JSON.valueOf(
    """
        {
            "productvariants":[
                {
                    "id":${ID_1},
                    "meterpoints":[]
                }
            ]
        }
    """.trimIndent()
)

val productAccount = ProductAccount(
    id = ID_1,
    type = ProductAccountType.ELECTRICITY,
    deleted = null,
    createdAt = LocalDateTime.now().minusDays(1),
    updatedAt = LocalDateTime.now().minusDays(1),
    billingAccount = BillingAccount(
        id = BILLING_ACCOUNT_ID,
        type = BillingAccountType.CR_INVOICE,
        name = "${BOBS_FIRST_NAME} ${BOBS_LAST_NAME}",
        number = ACCOUNT_NUMBER,
        currency = "GBP",
        `class` = BillingAccountClass.INVOICE,
        from = LocalDateTime.now().minusDays(1),
        customerId = ID_1,
        settings = null
    ),
    activeAgreements = listOf(activeAgreement),
    agreements = listOf(activeAgreement),
)

val validGetWarmHomeDiscountApplicationRequest = getWarmHomeDiscountApplicationRequest {
    billingAccountId = BILLING_ACCOUNT_ID
    applicationYear = APPLICATION_YEAR
}

val successGetWarmHomeDiscountApplicationResponse = getWarmHomeDiscountApplicationResponse {
    warmHomeDiscountApplication = succesWarmHomeDiscountApplicationProto
}

val warmHomeDiscountApplicationStatusInfo = WarmHomeDiscountApplicationStatusInfo(
    total = 16,
    paid = 2,
    applicationReceived = 1,
    documentVerification = 1,
    noEvidenceProvided = 1,
    approvedAtAudit = 1,
    auditCandidate = 1,
    successful = 3,
    coreGroup = 1,
    sentForAudit = 1,
    closedAccount = 2,
    rejectedCredited = 1,
    rejectedApproved = 1,
)


val warmHomeDiscountApplicationStatusInfoProto = warmHomeDiscountApplicationStatusInfo {
    total = 16
    paid = 2
    applicationReceived = 1
    documentVerification = 1
    noEvidenceProvided = 1
    approvedAtAudit = 1
    auditCandidate = 1
    successful = 3
    coreGroup = 1
    sentForAudit = 1
    closedAccount = 2
    rejectedCredited = 1
    rejectedApproved = 1
}


val applicationListInCsv = """
            id,user_id,account_id,first_name,family_name,date_of_birth,phone_number,status,application_year,created_at,primary_whd_criterion_id,secondary_whd_criterion_id,junifer_account_number,agent,ark_status,ark_status_history,ark_status_updated_at,primary_criteria_text,secondary_criteria_text,address,postcode,scottish_region
            659,47348,1152,*REDACTED*,*REDACTED*,"November 11, 1991",**********,document verification,2022-2023,"01 Jun 22, 08:31",40,196,********,Nina Miller,document_verification,application_received,"01 Jun 22, 08:31",Income-based Jobseekers allowance,"You receive a pensioner premium, higher pensioner premium or enhanced pensioner premium","*Address1368*, Address2_1368, Address3_1368",EH12 7HL,true
        """.trimIndent()


val updateWarmHomeDiscountApplicationsReq = updateWarmHomeDiscountApplicationsRequest {
    accountId = BILLING_ACCOUNT_ID.toString()
    accountNumber = ACCOUNT_NUMBER
    accountNumbers.add(ACCOUNT_NUMBER)
    campaignYear = APPLICATION_YEAR
    newAttributes = newAttributes {
        firstName = BOBS_FIRST_NAME.toNullableString()
        familyName = BOBS_LAST_NAME.toNullableString()
        dateOfBirth = bobsDateOfBirth.toString().toNullableString()
        phoneNumber = BOBS_PHONE.toNullableString()
        firstCriteriaSelection = PRIMARY_CRITERIA_ID.toString().toNullableString()
        secondCriteriaSelection = SECONDARY_CRITERIA_ID.toString().toNullableString()
        agent = AGENT.toNullableString()
        status = "paid".toNullableString()
        arkStatus = "paid".toNullableString()
    }
}

val updateWarmHomeDiscountApplicationsApproved = updateWarmHomeDiscountApplicationsReq.copy {
    newAttributes = updateWarmHomeDiscountApplicationsReq.newAttributes.copy {
        status = "successful".toNullableString()
        arkStatus = "successful".toNullableString()
    }
}

val updateWarmHomeDiscountApplicationsRes = updateWarmHomeDiscountApplicationsResponse {
    successfulAccountNumbers.add(ACCOUNT_NUMBER)
}

val accountNoteDto = AccountNoteDto(
    id = 1,
    createdAt = OffsetDateTime.of(2022, 2, 2, 2, 2, 2, 2, ZoneOffset.UTC),
    accountId = 1,
    subject = "subject",
    summary = "summary",
    type = AccountNoteType.NOTE,
    content = "content"
)

val bobsAccountCredit = accountCredit {
    id = 1
    createdDttm = LocalDate.of(LocalDate.now().year, 8, 15).toNullableTimestamp()
    currencyISO = "GBP"
    grossAmount = 5.0
    netAmount = 4.0
    salesTaxAmount = (1.0).toNullableDouble()
    salesTax = "VAT".toNullableString()
    reason = "Warm Home Discount Credit"
    reference = ""
}

val closeNoEvidenceResponse = closeNoEvidenceResponse {
    successfulAccountNumbers.add(ACCOUNT_NUMBER)
}

val addCoreGroupVulnerabilityResponseComplete = addCoreGroupVulnerabilityResponse {
    successfulAccountNumbers.add(ACCOUNT_NUMBER)
    errors.add(whdError {
        accountNumbers.add(ACCOUNT_NUMBER + 1)
        name = CustomersData.GENERIC_STRING
        message = CustomersData.GENERIC_STRING
    })
}

val addCoreGroupVulnerabilityRequest = addCoreGroupVulnerabilityRequest {
    accountNumbers.addAll(listOf(ACCOUNT_NUMBER, ACCOUNT_NUMBER + 1))
}

val billingAccountWhd = billingAccountModel.copy(id = ID_1, number = ACCOUNT_NUMBER)

val meterPointsResponse = meterPointsResponse {
    meterPoints.add(meterPoint {
        property = property {
            id = ID_1
            address = address {
                id = ID_1
                address1 = "address1".toNullableString()
                address2 = "address2".toNullableString()
                address3 = "address3".toNullableString()
                postcode = "postcode".toNullableString()
            }
        }
        meterPointHistory = meterPointHistory {
            supplyStatus = MeterPointSupplyStatus.Registered
        }
    })
}

val meterPointsResponseWithDetailsNotRegistered = meterPointsResponse {
    meterPoints.add(meterPoint {
        property = property {
            id = ID_1
        }
        meterPointHistory = meterPointHistory {
            supplyStatus = MeterPointSupplyStatus.NotSupplied
        }
    })
}

val meterPointsResponseWithDetailsRegistered = meterPointsResponse {
    meterPoints.add(meterPoint {
        property = property {
            id = ID_1
        }
        meterPointHistory = meterPointHistory {
            supplyStatus = MeterPointSupplyStatus.Registered
        }
    })
}

val meterPointsResponseWithNoDetails = meterPointsResponse { }

val createAccountCoreGroupNoteRequest = createAccountNoteRequest {
    accountId = ID_1.toString()
    subject = WarmHomeDiscountService.NOTE_SUBJECT
    type = NoteType.Note
    summary = WarmHomeDiscountService.CORE_GROUP_ACCOUNT_NOTE_SUMMARY
    content = "${WarmHomeDiscountService.CORE_GROUP_ACCOUNT_NOTE_CONTENT_PREFIX} $ID_1"
}

val imageList = listOf(
    images {
        name = IMAGE_NAME
        url = IMAGE_URL
    }
)
