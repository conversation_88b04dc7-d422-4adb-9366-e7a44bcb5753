package energy.so.customers.fixtures

import energy.so.ac.junifer.v1.customers.consent
import energy.so.ac.junifer.v1.customers.customerConsents
import energy.so.commons.model.tables.pojos.MoveAccountsToDailyReadsConsent

object ConsentData {

    val aConsent = MoveAccountsToDailyReadsConsent(
        id = 1,
        juniferAccountNumber = "0045688",
        email = "<EMAIL>",
        mpxn = "***********",
        meterPointId = "65484",
        emailSent = false,
        currentConsent = null,
        consentAfter_30days = null,
        consentUpdatedToDefaultDaily = false
    )

    val dailyCustomerConsentMHHS =
        consent {
            setting = "Daily"
            consentDefinition = "MHHS Billing"
        }

    val dailyCustomerConsentMPRN =
        consent {
            setting = "Daily"
            consentDefinition = "MPRN Billing"
        }


    val hhCustomerConsentMHHS =
        consent {
            setting = "Half Hourly"
            consentDefinition = "MHHS Billing"
        }

    val hhCustomerConsentMPRN =
        consent {
            setting = "Half Hourly"
            consentDefinition = "MPRN Billing"
        }

    val monthlyCustomerConsentMHHS =
        consent {
            setting = "Monthly"
            consentDefinition = "MHHS Billing"
        }
}