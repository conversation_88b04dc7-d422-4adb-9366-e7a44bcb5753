package energy.so.customers.fixtures

import energy.so.core.customers.server.services.AccountPreferenceConstants.EMAIL_CONTACT_PREFERENCE
import energy.so.core.customers.server.services.AccountPreferenceConstants.INTEREST_CATEGORY
import energy.so.core.customers.server.services.AccountPreferenceConstants.MarketingPreference
import energy.so.core.customers.server.services.AccountPreferenceConstants.PHONE_CONTACT_PREFERENCE
import energy.so.customers.v2.preferences.PreferenceType
import energy.so.customers.v2.preferences.accountPreference
import energy.so.dotdigital.models.ChannelProperty
import energy.so.dotdigital.models.ChannelStatus
import energy.so.dotdigital.models.ContactPreference
import energy.so.dotdigital.models.ContactV2
import energy.so.dotdigital.models.ContactV3
import energy.so.dotdigital.models.CreateContactRequest
import energy.so.dotdigital.models.EmailProperties
import energy.so.dotdigital.models.EmailType
import energy.so.dotdigital.models.GetSubscriptionsResponse
import energy.so.dotdigital.models.Identifier
import energy.so.dotdigital.models.OptInStatus
import energy.so.dotdigital.models.OptInType
import energy.so.dotdigital.models.SmsProperties
import energy.so.dotdigital.models.UnsubscribeResponse
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

object PreferencesPrecannedData {

    const val EMAIL = "<EMAIL>"
    const val CONTACT_ID = 123456L
    val created = OffsetDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)
    val updated = OffsetDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)

    val testCreateContactRequest = CreateContactRequest(
        identifiers = Identifier(
            email = EMAIL,
        ),
        channelProperties = ChannelProperty(
            email = EmailProperties(
                emailType = EmailType.HTML,
                optInType = OptInType.DOUBLE,
                status = ChannelStatus.SUBSCRIBED,
            )
        )
    )

    val testUnsubscribedCreateContactRequest = CreateContactRequest(
        identifiers = Identifier(
            email = EMAIL,
        ),
        channelProperties = ChannelProperty(
            email = EmailProperties(
                emailType = EmailType.HTML,
                optInType = OptInType.DOUBLE,
                status = ChannelStatus.UNSUBSCRIBED,
            )
        )
    )

    val testContactV2 = ContactV2(
        id = CONTACT_ID.toInt(),
        email = EMAIL,
        optInType = "Double",
        emailType = "HTML",
        dataFields = null,
        status = "Subscribed"
    )

    val testContactV3 = ContactV3(
        contactId = CONTACT_ID,
        status = OptInStatus.SUBSCRIBED,
        created = LocalDateTime.now(),
        updated = LocalDateTime.now(),
        identifiers = Identifier(
            email = EMAIL,
        ),
        dataFields = null,
        channelProperties = ChannelProperty(
            email = EmailProperties(
                emailType = EmailType.HTML,
                optInType = OptInType.DOUBLE,
                status = ChannelStatus.SUBSCRIBED,
            )
        ),
        lists = null,
        preferences = null,
        consentRecords = null,
    )

    val testContactEmailSms = ContactV3(
        contactId = 12345L,
        status = OptInStatus.SUBSCRIBED,
        created = LocalDateTime.now(),
        updated = LocalDateTime.now(),
        identifiers = Identifier(
            email = EMAIL,
        ),
        dataFields = null,
        channelProperties = ChannelProperty(
            email = EmailProperties(
                emailType = EmailType.HTML,
                optInType = OptInType.DOUBLE,
                status = ChannelStatus.SUBSCRIBED,
            ),
            sms = SmsProperties(
                countryCode = "GB",
                status = ChannelStatus.SUBSCRIBED,
            )
        ),
        lists = null,
        preferences = null,
        consentRecords = null,
    )

    val testGetSubscriptionsResponse = GetSubscriptionsResponse(
        contact = ContactV2(
            id = CONTACT_ID.toInt(),
            email = EMAIL,
            optInType = "Double",
            emailType = "html",
            dataFields = null,
            status = "Subscribed",
        ),
        marketingPreferenceOptIns = emptyList(),
    )

    val testUnsubscribeResponse = UnsubscribeResponse(
        dateRemoved = OffsetDateTime.now(),
        reason = "Unsubscribed",
        suppressedContact = ContactV2(
            id = CONTACT_ID.toInt(),
            email = EMAIL,
            optInType = "Double",
            emailType = "html",
            dataFields = null,
            status = "Unsubscribed",
        )
    )

    val newsPreference = ContactPreference(
        id = 9356,
        publicName = MarketingPreference.ENERGY_NEWS.preferenceName,
        privateName = MarketingPreference.ENERGY_NEWS.preferenceName,
        isPreference = true,
        order = "0",
        isPublic = false,
        categoryId = 9356,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val solarPreference = ContactPreference(
        id = 9358,
        publicName = MarketingPreference.THINKING_ABOUT_SOLAR.preferenceName,
        privateName = MarketingPreference.THINKING_ABOUT_SOLAR.preferenceName,
        isPreference = true,
        order = "1",
        isPublic = false,
        categoryId = 9356,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val solarSavingPreference = ContactPreference(
        id = 9359,
        publicName = MarketingPreference.SOLAR_SAVING.preferenceName,
        privateName = MarketingPreference.SOLAR_SAVING.preferenceName,
        isPreference = true,
        order = "2",
        isPublic = false,
        categoryId = 9356,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val consideringEvPreference = ContactPreference(
        id = 9360,
        publicName = MarketingPreference.CONSIDERING_EV.preferenceName,
        privateName = MarketingPreference.CONSIDERING_EV.preferenceName,
        isPreference = true,
        order = "3",
        isPublic = false,
        categoryId = 9356,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val evInsightsPreference = ContactPreference(
        id = 9361,
        publicName = MarketingPreference.EV_INSIGHTS.preferenceName,
        privateName = MarketingPreference.EV_INSIGHTS.preferenceName,
        isPreference = true,
        order = "4",
        isPublic = false,
        categoryId = 9356,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val interestsCategory = ContactPreference(
        id = 9356,
        publicName = INTEREST_CATEGORY,
        privateName = INTEREST_CATEGORY,
        isPreference = false,
        order = "0",
        isPublic = true,
        categoryId = 0,
        preferenceCount = 5,
        created = created,
        lastModified = updated,
        preferences = listOf(
            newsPreference,
            solarPreference,
            solarSavingPreference,
            consideringEvPreference,
            evInsightsPreference,
        ),
    )

    val testPreference = ContactPreference(
        id = 5995,
        publicName = "Test Preference",
        privateName = "Test Preference",
        isPreference = true,
        order = "2",
        isPublic = true,
        categoryId = 0,
        preferenceCount = 0,
        created = created,
        lastModified = updated,
        isOptedIn = false,
    )

    val testGetPreferenceResponse = listOf(interestsCategory, testPreference)

    val evMarketingPreferenceGrpc = accountPreference {
        name = MarketingPreference.EV_INSIGHTS.preferenceName
        value = "true"
        type = PreferenceType.MARKETING_PREFERENCE
    }

    val solarMarketingPreferenceGrpc = accountPreference {
        name = MarketingPreference.SOLAR_SAVING.preferenceName
        value = "true"
        type = PreferenceType.MARKETING_PREFERENCE
    }

    val emailContactPreferenceGrpc = accountPreference {
        name = EMAIL_CONTACT_PREFERENCE
        value = "true"
        type = PreferenceType.CONTACT_PREFERENCE
    }

    val phoneContactPreferenceGrpc = accountPreference {
        name = PHONE_CONTACT_PREFERENCE
        value = "true"
        type = PreferenceType.CONTACT_PREFERENCE
    }

}