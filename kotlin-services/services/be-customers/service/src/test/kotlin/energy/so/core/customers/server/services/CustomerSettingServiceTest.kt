package energy.so.core.customers.server.services

import com.google.protobuf.Empty
import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.updateCustomerRequest
import energy.so.core.customers.server.database.repositories.CustomerPreferenceLogRepository
import energy.so.core.customers.server.database.repositories.CustomerSettingRepository
import energy.so.customers.client.models.EnergySource
import energy.so.customers.customersetting.v2.EvType
import energy.so.customers.customersetting.v2.PreferenceType
import energy.so.customers.customersetting.v2.patchCustomerSettingRequest
import energy.so.customers.customersetting.v2.preferenceCheckedLog
import energy.so.customers.fixtures.BobsData.contact
import energy.so.customers.fixtures.CustomerPreferenceLogsData.customerPreferenceLog
import energy.so.customers.fixtures.CustomerPreferenceLogsData.logGrpcRequest
import energy.so.customers.fixtures.CustomerSettingData.CUSTOMER_SETTING_ID
import energy.so.customers.fixtures.CustomerSettingData.customerSettings
import energy.so.customers.fixtures.CustomersData.CUSTOMER_ID
import energy.so.customers.fixtures.CustomersData.newCustomer
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import org.jooq.exception.IntegrityConstraintViolationException
import energy.so.customers.customersetting.v2.EnergySource as GrpcEnergySource

class CustomerSettingServiceTest : BehaviorSpec({

    val mockCustomerSettingRepository = mockk<CustomerSettingRepository>()
    val mockPreferenceLogRepository = mockk<CustomerPreferenceLogRepository>()
    val mockCustomersSyncClient = mockk<CustomersSyncClient>()
    val mockFeatureService = mockk<FeatureService>()
    val mockCustomerPreferencesService = mockk<CustomerPreferencesService>()

    val sut = CustomerSettingService(
        customerSettingRepository = mockCustomerSettingRepository,
        preferenceLogRepository = mockPreferenceLogRepository,
        customersSyncClient = mockCustomersSyncClient,
        featureService = mockFeatureService,
        customerPreferencesService = mockCustomerPreferencesService,
    )

    afterEach {
        confirmVerified(
            mockCustomerSettingRepository,
            mockCustomersSyncClient,
            mockPreferenceLogRepository,
            mockFeatureService,
            mockCustomerPreferencesService
        )
    }

    given("there is a customer with given id") {
        and("there is already a customerSetting entity saved for given customerId") {
            every { mockCustomerSettingRepository.findById(customerSettings.id!!) } returns customerSettings
            and("the request has marketingOptIn set") {
                val request = patchCustomerSettingRequest {
                    this.customerId = CUSTOMER_ID
                    this.energySource = GrpcEnergySource.HYDRO
                    this.marketingOptIn = true
                    this.smartMeterInterest = true
                    this.referralsEnabled = true
                    this.evType = EvType.ELECTRICITY
                    this.intendToBuyEv = false
                    this.evTariffMarketingConsent = false
                }
                val newSetting = customerSettings.copy(
                    energySourceVote = EnergySource.HYDRO,
                    marketingOptIn = true,
                    smartMeterInterest = true,
                    referralsEnabled = true,
                )
                val updateCustomerRequest = updateCustomerRequest {
                    this.id = request.customerId.toString()
                    this.marketingOptOut = !request.marketingOptIn
                }
                coEvery { mockCustomerSettingRepository.save(newSetting) } returns newSetting
                coEvery { mockCustomersSyncClient.updateCustomer(updateCustomerRequest) } returns Empty.getDefaultInstance()
                coJustRun { mockCustomerPreferencesService.subscribeContact(newCustomer.primaryContact!!.email!!) }

                `when`("::updateCustomerSetting") {
                    val response = sut.updateCustomerSetting(newCustomer, request)
                    then("return the updated CustomerSetting object") {
                        response shouldBe newSetting
                        verify { mockCustomerSettingRepository.findById(customerSettings.id!!) }
                        coVerify { mockCustomerSettingRepository.save(newSetting) }
                        coVerify { mockCustomersSyncClient.updateCustomer(updateCustomerRequest) }
                        coVerify { mockCustomerPreferencesService.subscribeContact(newCustomer.primaryContact!!.email!!) }
                    }
                }
            }

            and("the request doesn't have marketingOptIn set") {
                val request = patchCustomerSettingRequest {
                    this.customerId = CUSTOMER_ID
                    this.energySource = GrpcEnergySource.HYDRO
                    this.referralsEnabled = true
                }
                val newSetting = customerSettings.copy(
                    energySourceVote = EnergySource.HYDRO,
                    marketingOptIn = false,
                    referralsEnabled = true,
                )
                coEvery { mockCustomerSettingRepository.save(newSetting) } returns newSetting
                `when`("::updateCustomerSetting") {
                    val response = sut.updateCustomerSetting(newCustomer, request)
                    then("return the updated CustomerSetting object") {
                        response shouldBe newSetting
                        verify { mockCustomerSettingRepository.findById(customerSettings.id!!) }
                        coVerify { mockCustomerSettingRepository.save(newSetting) }
                    }
                }
            }
        }

        and("there isn't a customerSetting object saved for given customerId") {
            every { mockCustomerSettingRepository.findById(customerSettings.id!!) } returns null
            and("the request has marketingOptIn set") {
                val request = patchCustomerSettingRequest {
                    this.customerId = CUSTOMER_ID
                    this.energySource = GrpcEnergySource.HYDRO
                    this.marketingOptIn = true
                    this.smartMeterInterest = true
                    this.referralsEnabled = true
                    this.evType = EvType.ELECTRICITY
                    this.intendToBuyEv = false
                    this.evTariffMarketingConsent = false
                }
                val newSetting = customerSettings.copy(
                    id = null,
                    energySourceVote = EnergySource.HYDRO,
                    marketingOptIn = true,
                    smartMeterInterest = true,
                    referralsEnabled = true,
                )
                val updateCustomerRequest = updateCustomerRequest {
                    this.id = request.customerId.toString()
                    this.marketingOptOut = !request.marketingOptIn
                }
                coEvery { mockCustomerSettingRepository.save(newSetting) } returns newSetting.copy(id = CUSTOMER_SETTING_ID)
                coEvery { mockCustomersSyncClient.updateCustomer(updateCustomerRequest) } returns Empty.getDefaultInstance()
                coJustRun { mockCustomerPreferencesService.subscribeContact(newCustomer.primaryContact!!.email!!) }

                `when`("::updateCustomerSetting") {
                    val response = sut.updateCustomerSetting(newCustomer, request)
                    then("return the updated CustomerSetting object") {
                        response shouldBe newSetting.copy(id = CUSTOMER_SETTING_ID)
                        verify { mockCustomerSettingRepository.findById(customerSettings.id!!) }
                        coVerify { mockCustomerSettingRepository.save(newSetting) }
                        coVerify { mockCustomersSyncClient.updateCustomer(updateCustomerRequest) }
                        coVerify { mockCustomerPreferencesService.subscribeContact(newCustomer.primaryContact!!.email!!) }
                    }
                }
            }

            and("the request doesn't have marketingOptIn set") {
                val request = patchCustomerSettingRequest {
                    this.customerId = CUSTOMER_ID
                    this.energySource = GrpcEnergySource.HYDRO
                    this.referralsEnabled = true
                    this.evType = EvType.ELECTRICITY
                    this.intendToBuyEv = false
                    this.evTariffMarketingConsent = false
                }
                val newSetting = customerSettings.copy(
                    id = null,
                    energySourceVote = EnergySource.HYDRO,
                    marketingOptIn = false,
                    referralsEnabled = true,
                )
                coEvery { mockCustomerSettingRepository.save(newSetting) } returns newSetting.copy(id = CUSTOMER_SETTING_ID)
                `when`("::updateCustomerSetting") {
                    val response = sut.updateCustomerSetting(newCustomer, request)
                    then("return the updated CustomerSetting object") {
                        response shouldBe newSetting.copy(id = CUSTOMER_SETTING_ID)
                        verify { mockCustomerSettingRepository.findById(customerSettings.id!!) }
                        coVerify { mockCustomerSettingRepository.save(newSetting) }
                    }
                }
            }
        }
    }

    given("::upsertPreferenceCheckedLog") {

        val request = preferenceCheckedLog {
            customerId = CUSTOMER_ID
            type = PreferenceType.MARKETING_OPT_IN
        }

        `when`("run successfully") {
            justRun { mockPreferenceLogRepository.upsertLastChecked(any()) }
            sut.upsertPreferenceCheckedLog(request)
            coVerify { mockPreferenceLogRepository.upsertLastChecked(any()) }
        }

        `when`("repo throws") {
            coEvery { mockPreferenceLogRepository.upsertLastChecked(any()) } throws IntegrityConstraintViolationException(
                "FK missing"
            )
            shouldThrow<IntegrityConstraintViolationException> { sut.upsertPreferenceCheckedLog(request) }
            coVerify { mockPreferenceLogRepository.upsertLastChecked(any()) }
        }

    }

    given("::getLastLogDate") {
        `when`("a valid log is returned from repo") {
            coEvery {
                mockPreferenceLogRepository.findByCustomerIdAndType(
                    CUSTOMER_ID,
                    energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                )
            } returns customerPreferenceLog
            val result = sut.getLastLogDate(logGrpcRequest)
            then("a valid log should be returned") {
                result shouldBe LocalDateTime.of(2023, 9, 12, 9, 0, 0)
                coVerify {
                    mockPreferenceLogRepository.findByCustomerIdAndType(
                        CUSTOMER_ID,
                        energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                    )
                }
            }
        }

        `when`("a null is returned from repo") {
            coEvery {
                mockPreferenceLogRepository.findByCustomerIdAndType(
                    CUSTOMER_ID,
                    energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                )
            } returns null
            val result = sut.getLastLogDate(logGrpcRequest)
            then("the minimum date time should be returned") {
                result shouldBe LocalDateTime.MIN
                coVerify {
                    mockPreferenceLogRepository.findByCustomerIdAndType(
                        CUSTOMER_ID,
                        energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                    )
                }
            }
        }

        `when`("a lastCheckedAt field is null on the returned object") {
            coEvery {
                mockPreferenceLogRepository.findByCustomerIdAndType(
                    CUSTOMER_ID,
                    energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                )
            } returns customerPreferenceLog
                .copy(preferenceLastCheckedAt = null)

            shouldThrow<NullPointerException> { sut.getLastLogDate(logGrpcRequest) }

            then("a NullPointerException is thrown") {
                coVerify {
                    mockPreferenceLogRepository.findByCustomerIdAndType(
                        CUSTOMER_ID,
                        energy.so.commons.model.enums.PreferenceType.MARKETING_OPT_IN
                    )
                }
            }
        }
    }

    given("::getCustomerSetting") {
        `when`("a request to retrieve the customer setting is received") {

            every { mockCustomerSettingRepository.findById(CUSTOMER_SETTING_ID) } returns customerSettings
            coEvery { mockCustomerPreferencesService.getMarketingOptIn(contact.email!!) } returns true
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_23981_MARKETING_OPT_IN_FROM_DOT_DIGITAL) } returns true

            val setting = sut.getCustomerSetting(contact, CUSTOMER_SETTING_ID)

            then("the customer setting should be returned") {
                verify { mockCustomerSettingRepository.findById(CUSTOMER_SETTING_ID) }
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_23981_MARKETING_OPT_IN_FROM_DOT_DIGITAL) }
                coVerify { mockCustomerPreferencesService.getMarketingOptIn(contact.email!!) }

                setting shouldBe customerSettings.copy(marketingOptIn = true)
            }
        }
    }
})
