package energy.so.psr.handler

import energy.so.commons.NullableTimestamp
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.psr.dto.PsrCustomerService
import energy.so.psr.dto.PsrCustomerSettings
import energy.so.psr.dto.PsrCustomerVulnerability
import energy.so.psr.dto.PsrExtraDataField
import energy.so.psr.repository.PsrCustomerRepository
import energy.so.psr.repository.PsrExtraDataRepository
import energy.so.psr.service.PsrJuniferService
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter



class PsrCustomerHandler(
    private val psrCustomerRepository: PsrCustomerRepository,
    private val juniferService: PsrJuniferService,
    private val psrExtraDataRepository: PsrExtraDataRepository,
) {

    fun getCurrentPsrSettingsForCustomer(customerId: Long): PsrCustomerSettings {

        return psrCustomerRepository.getPsrSettingsForCustomer(customerId)
    }

    fun getFullPsrSettingsHistoryForCustomer(customerId: Long): PsrCustomerSettings {

        return psrCustomerRepository.getPsrSettingsForCustomer(customerId, true)
    }

    fun savePsrSettingsForCustomer(psrCustomerSettings: PsrCustomerSettings) {

        psrCustomerRepository.registerCustomerForVulnerabilities(
            psrCustomerSettings.customerId,
            psrCustomerSettings.customerVulnerabilities
        )
        psrCustomerRepository.registerCustomerForServices(
            psrCustomerSettings.customerId,
            psrCustomerSettings.customerServices
        )
    }

    suspend fun addCustomerVulnerabilitiesAndAssociatedServices(customerSettings: energy.so.customers.v2.psr.PsrCustomerSettings, propertyId: Long) {
        updateJunifer(customerSettings, propertyId)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        psrCustomerRepository.addPsrCustomer(customerSettings.customerId, true)
        val customerVulnerabilities = customerSettings.customerVulnerabilitiesList.map { eachVulnerability ->
            PsrCustomerVulnerability(
                vulnerabilityId = eachVulnerability.vulnerabilityId,
                enrolmentDate = LocalDateTime.now(),
                terminationDate = null,
                extraData = if (eachVulnerability.terminationDate.getValueOrNull() != null)
                    listOf(PsrExtraDataField(
                        fieldId = getExtraDataFieldId(eachVulnerability.vulnerabilityId), // get it based on eachVulnerability.vulnerabilityId
                        fieldName = "End Date",
                        fieldValue = eachVulnerability.terminationDate.getValueOrNull()?.toLocalDate()?.format(formatter) ?: "",
                        definitionDate = LocalDateTime.now(),
                        replacementDate = null,
                        fieldDesc = "End Date")
                    )
                else
                    listOf()
            )
        }.toList()

        psrCustomerRepository.run { registerCustomerForVulnerabilities(customerSettings.customerId, customerVulnerabilities) }

        val customerServices = customerSettings.customerServicesList.map {
            eachService -> PsrCustomerService(
                serviceId = eachService.serviceId,
                enrolmentDate = LocalDateTime.now(),
                terminationDate = null,
                extraData = eachService.extraDataList.map {
                    eachExtraData -> PsrExtraDataField(
                        fieldId = eachExtraData.fieldId,
                        fieldName = eachExtraData.fieldName,
                        fieldValue = eachExtraData.fieldValue,
                        definitionDate = LocalDateTime.now(),
                        replacementDate = null,
                        fieldDesc = ""
                    )
                }.toList()
            )
        }.toList()

        psrCustomerRepository.run { registerCustomerForServices(customerSettings.customerId, customerServices) }
    }

    private fun getExtraDataFieldId(vulnerabilityId: Long): Long {
        val extraData = psrExtraDataRepository.getTerminationDateExtraDataByVulnerabilityId(vulnerabilityId)
        return if (extraData?.extraDataId != null)
            extraData.extraDataId
        else
            throw RuntimeException("Psr ExtraData not found for vulnerability $vulnerabilityId")
    }

    private suspend fun updateJunifer(customerSettingsFromGateway: energy.so.customers.v2.psr.PsrCustomerSettings, propertyId: Long) {
        val customerSettingsFromDb = psrCustomerRepository.getPsrSettingsForCustomer(customerSettingsFromGateway.customerId)
        val vulnerabilitiesFromUI: Map<Long, NullableTimestamp> = customerSettingsFromGateway.customerVulnerabilitiesList.associate { it.vulnerabilityId to it.terminationDate }
        val vulnerabilityIdsFromUI = vulnerabilitiesFromUI.map { it.key }.toSet()
        val vulnerabilityIdsFromDb = customerSettingsFromDb.customerVulnerabilities.map { vulnerability -> vulnerability.vulnerabilityId }.toSet()
        val addToJunifer = vulnerabilityIdsFromUI - vulnerabilityIdsFromDb
        val deleteFromJunifer = vulnerabilityIdsFromDb - vulnerabilityIdsFromUI

        if(deleteFromJunifer.isNotEmpty()) {
            deleteFromJunifer.forEach { vulnerabilityId ->
                juniferService.deleteJuniferCustomerPsr(
                    customerId = customerSettingsFromGateway.customerId.toString(),
                    coreId = customerSettingsFromDb.customerVulnerabilities.find { it.vulnerabilityId == vulnerabilityId }?.coreId ?: 0L
                )
            }
        }

        if(addToJunifer.isNotEmpty()) {
            addToJunifer.forEach { vulnerabilityId ->
                juniferService.createJuniferCustomerPsr(
                    vulnerabilityId = vulnerabilityId,
                    propertyId = propertyId,
                    customerId = customerSettingsFromGateway.customerId.toString(),
                    endDate = vulnerabilitiesFromUI[vulnerabilityId]?.getValueOrNull()?.toLocalDateTime()
                )
            }
        }
    }
}