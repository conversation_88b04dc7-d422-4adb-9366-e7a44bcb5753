package energy.so.psr.service

import com.google.protobuf.NullValue
import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.createCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.deleteCustomerPsrRequest
import energy.so.commons.NullableTimestamp
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.utils.toNullableTimestamp

import energy.so.psr.dto.JuniferReference
import energy.so.psr.repository.PsrVulnerabilityRepository
import java.time.LocalDateTime
import kotlinx.serialization.json.Json

class PsrJuniferService(
    private val customersSyncClient: CustomersSyncClient,
    private val psrVulnerabilityRepository: PsrVulnerabilityRepository,
) {

    suspend fun createJuniferCustomerPsr(
        vulnerabilityId: Long,
        propertyId: Long,
        customerId: String,
        endDate: LocalDateTime? = null
    ) {
        val vul = psrVulnerabilityRepository.getById(vulnerabilityId)
        val juniferReference = Json.decodeFromString<Array<JuniferReference>>(vul?.juniferReference ?: "")
        if(juniferReference.isNotEmpty()) {
            customersSyncClient.createCustomerPsr(
                createCustomerPsrRequest {
                    this.customerId = customerId
                    this.fromDt = LocalDateTime.now().toNullableTimestamp()
                    this.toDt = endDate?.toNullableTimestamp() ?: NullableTimestamp.newBuilder().setNull(NullValue.NULL_VALUE).build()
                    this.utilityMarket = "UK Electricity"
                    this.propertyTblId = propertyId
                    this.consentFl = true.toNullableBoolean()
                    this.value = juniferReference[0].electricityValue.toString().padStart(2, '0')
                }
            )
        }
    }

    suspend fun deleteJuniferCustomerPsr(customerId: String, coreId: Long) {
        if(coreId == 0L) return // No need to delete if coreId is 0
        customersSyncClient.deleteCustomerPsr(deleteCustomerPsrRequest {
            this.customerId = customerId
            this.psrId = coreId
        })
    }

}