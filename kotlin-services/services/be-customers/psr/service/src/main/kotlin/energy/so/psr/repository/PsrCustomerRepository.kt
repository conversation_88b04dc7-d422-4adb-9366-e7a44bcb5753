package energy.so.psr.repository

import energy.so.commons.model.psr.tables.pojos.PsrCustomerExtraData
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_SERVICE_REGISTER
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_VULNERABILITY_REGISTER
import energy.so.commons.model.psr.tables.references.PSR_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_SERVICE
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_SERVICE_LINK
import energy.so.psr.dto.PsrCustomerService
import energy.so.psr.dto.PsrCustomerSettings
import energy.so.psr.dto.PsrCustomerVulnerability
import energy.so.psr.dto.PsrExtraDataField
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.Result
import org.jooq.impl.DSL
import org.springframework.transaction.annotation.Transactional
import kotlin.Long
import kotlin.jvm.java


open class PsrCustomerRepository(private val dsl: DSLContext) {

    fun customerHasAnyActiveVulnerability(customerId: Long): Boolean {

        val count = dsl.selectCount()
            .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
            .fetchOne(0, Int::class.java) ?: 0

        return count > 0
    }

    fun customerHasVulnerability(customerId: Long, vulnerabilityId: Long): Boolean {

        val count = dsl.selectCount()
            .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(vulnerabilityId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
            .fetchOne(0, Int::class.java) ?: 0

        return count > 0
    }

    fun customerHasService(customerId: Long, serviceId: Long): Boolean {

        val count = dsl.selectCount()
            .from(PSR_CUSTOMER_SERVICE_REGISTER)
            .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID.eq(serviceId))
            .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull)
            .fetchOne(0, Int::class.java) ?: 0

        return count > 0
    }

    fun getPsrSettingsForCustomer(customerId: Long, fetchHistory: Boolean = false): PsrCustomerSettings {

        val customerRecord: Result<Record> = dsl
            .select(*PSR_CUSTOMER.fields())
            .from(PSR_CUSTOMER)
            .where(PSR_CUSTOMER.CUSTOMER_ID.eq(customerId))
            .fetch()

        if(customerRecord.isEmpty()) {
            return PsrCustomerSettings(customerId, LocalDateTime.MIN, "", emptyList(), emptyList())
        }

        val firstRecord = customerRecord.first()
        val confirmationDate = firstRecord.get(PSR_CUSTOMER.LAST_CONFIRMATION)!!
        val communicationPreferences = firstRecord.get(PSR_CUSTOMER.COMMUNICATION_PREFERENCE)!!

        val vulnerabilities = getCustomerVulnerabilities(customerId, fetchHistory)
        val services = getCustomerServices(customerId, fetchHistory)

        return PsrCustomerSettings(customerId, confirmationDate.toLocalDateTime(), communicationPreferences, vulnerabilities, services)
    }

    fun terminateCustomerVulnerability(customerId: Long, vulnerabilityId: Long) {
        dsl.update(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .set(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.dataType))
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(vulnerabilityId))
            .execute()
        terminateIneligibleServicesForCustomer(customerId)
    }

    private fun terminateIneligibleServicesForCustomer(customerId: Long) {
        // Get all enrolled vulnerabilities
        val existingCustomerVulnerability = dsl.select(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID)
            .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
            .fetchInto(Long::class.java)

        if(existingCustomerVulnerability.isNotEmpty()) {
            // If enrolled vulnerabilities exists then dont cancel the services eligible for the enrolled vulnerabilities
            val eligibleServices = dsl.select(PSR_VULNERABILITY_SERVICE_LINK.SERVICE_ID)
                .from(PSR_VULNERABILITY_SERVICE_LINK)
                .where(PSR_VULNERABILITY_SERVICE_LINK.VULNERABILITY_ID.`in`(existingCustomerVulnerability))
                .fetchInto(Long::class.java)

            val validExtraDataIds = dsl.select(PSR_EXTRA_DATA.EXTRA_DATA_ID)
                .from(PSR_EXTRA_DATA)
                .where(PSR_EXTRA_DATA.VULNERABILITY_ID.`in`(existingCustomerVulnerability))
                .or(PSR_EXTRA_DATA.SERVICE_ID.`in`(eligibleServices))
                .fetchInto(Long::class.java)

            dsl.update(PSR_CUSTOMER_EXTRA_DATA)
                .set(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.dataType))
                .where(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.notIn(validExtraDataIds))
                .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
                .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                .execute()

            dsl.update(PSR_CUSTOMER_SERVICE_REGISTER)
                .set(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.dataType))
                .where(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID.notIn(eligibleServices))
                .and(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(customerId))
                .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull)
                .execute()

        } else {
            // If no enrolled vulnerabilities exists then cancel all the services associated with customer
            dsl.update(PSR_CUSTOMER_SERVICE_REGISTER)
                .set(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.dataType))
                .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(customerId))
                .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull)
                .execute()
            dsl.update(PSR_CUSTOMER_EXTRA_DATA)
                .set(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.dataType))
                .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
                .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                .execute()
        }
    }

    @Transactional
    open fun syncJuniferVulnerabilityToCustomer(customerId: Long, vulnerability: PsrCustomerVulnerability, coreId: Long, endDate: LocalDate?) {

        // Adding customer if does not exist or set currently enrolled = true to existing one
        addPsrCustomer(customerId, true)

        // Check if the customer already has this vulnerability
        if(customerHasVulnerability(customerId, vulnerability.vulnerabilityId)) {
            // If the customer already has this vulnerability, we will update it
            dsl.update(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                .set(PSR_CUSTOMER_VULNERABILITY_REGISTER.CORE_ID, coreId)
                .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
                .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(vulnerability.vulnerabilityId))
                .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
                .execute()

            // Update the end date extra data field if needed
            if(endDate != null) {
                dsl.update(PSR_CUSTOMER_EXTRA_DATA)
                    .set(PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE, endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                    .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
                    .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                    .and(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.`in`(
                        dsl.select(PSR_EXTRA_DATA.EXTRA_DATA_ID)
                            .from(PSR_EXTRA_DATA)
                            .where(PSR_EXTRA_DATA.VULNERABILITY_ID.eq(vulnerability.vulnerabilityId))
                    ))
                    .execute()
            }
        }
        else {
            // If the customer does not have this vulnerability, we will create a new one
            val vulnerabilityRecord = dsl.newRecord(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            vulnerabilityRecord.customerId = customerId
            vulnerabilityRecord.vulnerabilityId = vulnerability.vulnerabilityId
            vulnerabilityRecord.coreId = coreId
            vulnerabilityRecord.store()

            // Set the end date extra data field if needed
            val extraDataId = dsl.select(PSR_EXTRA_DATA.EXTRA_DATA_ID)
                .from(PSR_EXTRA_DATA)
                .where(PSR_EXTRA_DATA.VULNERABILITY_ID.eq(vulnerability.vulnerabilityId))
                .fetchOne(PSR_EXTRA_DATA.EXTRA_DATA_ID)
            if(extraDataId != null && endDate != null) {

                val extraDataRecord = dsl.newRecord(PSR_CUSTOMER_EXTRA_DATA)
                extraDataRecord.customerId = customerId
                extraDataRecord.extraDataId = extraDataId
                extraDataRecord.fieldValue = endDate.toString()
                extraDataRecord.store()
            }
        }
    }

    @Transactional
    open fun registerCustomerForVulnerabilities(customerId: Long, vulnerabilities: List<PsrCustomerVulnerability>) {

        // Adding customer if does not exist or set currently enrolled = true to existing one
        addPsrCustomer(customerId, true)

        // Unregister all vulnerabilities and any associated extra data for the given customer where the vulnerability is not in the provided list
        dsl.update(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .set(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.dataType))
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.notIn(vulnerabilities.map { it.vulnerabilityId }))
            .execute()
        dsl.update(PSR_CUSTOMER_EXTRA_DATA)
            .set(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.dataType))
            .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.`in`(
                dsl.select(PSR_EXTRA_DATA.EXTRA_DATA_ID)
                    .from(PSR_EXTRA_DATA)
                    .where(PSR_EXTRA_DATA.VULNERABILITY_ID.notIn(vulnerabilities.map { it.vulnerabilityId }))
                    .and(PSR_EXTRA_DATA.VULNERABILITY_ID.isNotNull)
            ))
            .execute()

        // Register the new vulnerabilities for the given customer
        vulnerabilities.forEach { vulnerability ->

            if(!customerHasVulnerability(customerId, vulnerability.vulnerabilityId)) {
                val vulnerabilityRecord = dsl.newRecord(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                vulnerabilityRecord.customerId = customerId
                vulnerabilityRecord.vulnerabilityId = vulnerability.vulnerabilityId
                vulnerabilityRecord.store()

                vulnerability.extraData.forEach { extraData ->
                    val extraDataRecord = dsl.newRecord(PSR_CUSTOMER_EXTRA_DATA)
                    extraDataRecord.customerId = customerId
                    extraDataRecord.extraDataId = extraData.fieldId
                    extraDataRecord.fieldValue = extraData.fieldValue
                    extraDataRecord.store()
                }
            }
        }
    }

    @Transactional
    open fun registerCustomerForServices(customerId: Long, services: List<PsrCustomerService>) {

        // Unregister all services and any associated extra data for the given customer where the service is not in the provided list
        dsl.update(PSR_CUSTOMER_SERVICE_REGISTER)
            .set(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.dataType))
            .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID.notIn(services.map { it.serviceId }))
            .execute()
        dsl.update(PSR_CUSTOMER_EXTRA_DATA)
            .set(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, DSL.currentTimestamp().cast(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.dataType))
            .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.`in`(
                dsl.select(PSR_EXTRA_DATA.EXTRA_DATA_ID)
                    .from(PSR_EXTRA_DATA)
                    .where(PSR_EXTRA_DATA.SERVICE_ID.notIn(services.map { it.serviceId }))
                    .and(PSR_EXTRA_DATA.SERVICE_ID.isNotNull)
            ))
            .execute()

        // Register the new services for the given customer
        services.forEach { service ->

            if(!customerHasService(customerId, service.serviceId)) {
                val serviceRecord = dsl.newRecord(PSR_CUSTOMER_SERVICE_REGISTER)
                serviceRecord.customerId = customerId
                serviceRecord.serviceId = service.serviceId
                serviceRecord.store()

                service.extraData.forEach { extraData ->
                    val extraDataRecord = dsl.newRecord(PSR_CUSTOMER_EXTRA_DATA)
                    extraDataRecord.customerId = customerId
                    extraDataRecord.extraDataId = extraData.fieldId
                    extraDataRecord.fieldValue = extraData.fieldValue
                    extraDataRecord.store()
                }
            } else if (service.extraData.isNotEmpty()) {
                service.extraData.forEach { data ->
                    val currentExtraData = this.getCustomerExtraDataById(data.fieldId, customerId)
                    if (currentExtraData != null && currentExtraData.fieldValue != data.fieldValue)
                        terminateAndInsertNewExtraData(data, customerId, currentExtraData.customerExtraDataId!!)

                }
            }
        }
    }

    fun addPsrCustomer(customerId: Long, currentlyEnrolled: Boolean, communicationPreference: String? = "") {
        val existingCount = dsl.selectCount()
            .from(PSR_CUSTOMER)
            .where(PSR_CUSTOMER.CUSTOMER_ID.eq(customerId))
            .fetchOne(0, Int::class.java) ?: 0

        if (existingCount == 0) {
            dsl.insertInto(PSR_CUSTOMER)
                .columns(
                    PSR_CUSTOMER.CUSTOMER_ID,
                    PSR_CUSTOMER.CURRENTLY_ENROLLED,
                    PSR_CUSTOMER.LAST_CONFIRMATION,
                    PSR_CUSTOMER.COMMUNICATION_PREFERENCE,
                )
                .values(
                    customerId,
                    currentlyEnrolled,
                    LocalDateTime.now().atOffset(ZoneOffset.UTC),
                    communicationPreference
                ).execute()
        } else {
            dsl.update(PSR_CUSTOMER)
                .set(PSR_CUSTOMER.CURRENTLY_ENROLLED, true)
                .set(PSR_CUSTOMER.LAST_CONFIRMATION, LocalDateTime.now().atOffset(ZoneOffset.UTC))
                .where(PSR_CUSTOMER.CUSTOMER_ID.eq(customerId))
                .execute()
        }
    }

    private fun extraData(groupRecords: List<Record>): List<PsrExtraDataField> {

        val extraData = mutableListOf<PsrExtraDataField>()

        groupRecords.forEach {
            val fieldId = it.get(PSR_EXTRA_DATA.EXTRA_DATA_ID)
            val fieldValue = it.get(PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE)
            val fieldNm = it.get(PSR_EXTRA_DATA.FIELD_NAME)
            val fieldDesc = it.get(PSR_EXTRA_DATA.FIELD_DESCRIPTION)
            val fieldDefinitionDate = it.get(PSR_CUSTOMER_EXTRA_DATA.DEFINITION_DATE)
            val fieldReplacementDate = it.get(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE)
            if(fieldValue != null) {
                extraData += PsrExtraDataField(
                    fieldId = fieldId!!.toLong(),
                    fieldName = fieldNm.toString(),
                    fieldDesc = fieldDesc.toString(),
                    fieldValue = fieldValue.toString(),
                    definitionDate = fieldDefinitionDate!!.toLocalDateTime(),
                    replacementDate = fieldReplacementDate?.toLocalDateTime()
                )
            }
        }

        return extraData
    }

    private fun getCustomerVulnerabilities(customerId: Long, fetchHistory: Boolean): List<PsrCustomerVulnerability> {

        val historyCondition: Condition = if(fetchHistory) {
            PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.isNotNull
        } else {
            PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNotNull
        }

        // Perform a single query joining vulnerabilities, account registration, and extra data.
        val records: Result<Record> = dsl
            .select(*PSR_VULNERABILITY.fields(), PSR_CUSTOMER_VULNERABILITY_REGISTER.ENROLMENT_DATE ,PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE, PSR_EXTRA_DATA.EXTRA_DATA_ID, PSR_EXTRA_DATA.FIELD_NAME, PSR_EXTRA_DATA.FIELD_DESCRIPTION, PSR_EXTRA_DATA.FIELD_TYPE, PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE, PSR_CUSTOMER_EXTRA_DATA.DEFINITION_DATE, PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, PSR_CUSTOMER_VULNERABILITY_REGISTER.CORE_ID)
            .from(PSR_VULNERABILITY)
            .innerJoin(PSR_CUSTOMER_VULNERABILITY_REGISTER)
            .on(PSR_VULNERABILITY.VULNERABILITY_ID.eq(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID)
                .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull()))
            .leftJoin(PSR_EXTRA_DATA)
            .on(PSR_EXTRA_DATA.VULNERABILITY_ID.eq(PSR_VULNERABILITY.VULNERABILITY_ID))
            .leftJoin(PSR_CUSTOMER_EXTRA_DATA)
            .on(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.eq(PSR_EXTRA_DATA.EXTRA_DATA_ID))
            .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull())
            .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID))
            .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(customerId))
            .fetch()

        // Group records by vulnerability id.
        val vulnerabilitiesMap = records.groupBy { record ->
            record.get(PSR_VULNERABILITY.VULNERABILITY_ID)
        }

        val vulnerabilities = mutableListOf<PsrCustomerVulnerability>()

        vulnerabilitiesMap.forEach { (_, groupRecords) ->
            // Use the first record in the group to build the vulnerability and enrolmentDate.
            val firstRecord = groupRecords.first()
            val vulnerabilityRecord = firstRecord.into(PSR_VULNERABILITY)
            val enrolmentDate: OffsetDateTime = firstRecord.get(PSR_CUSTOMER_VULNERABILITY_REGISTER.ENROLMENT_DATE)!!
            //This might be used in future, for now terminationDateFromExtraData will be used in the response
            val terminationDate: OffsetDateTime? = firstRecord.get(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE)
            val vulnerabilityLabel: String = firstRecord.get(PSR_VULNERABILITY.CCA_LABEL) ?: ""
            val coreId: Long = firstRecord.get(PSR_CUSTOMER_VULNERABILITY_REGISTER.CORE_ID) ?: 0L

            // Build the extra data list from all rows (filtering out null extra data rows).
            val vulnerabilityExtraData = <EMAIL>(groupRecords)
            val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            val endDateFieldValue = vulnerabilityExtraData.firstOrNull { vulExtraData -> vulExtraData.fieldName == "End Date" }
            val endDateFromExtraData = if(endDateFieldValue != null) {
                LocalDateTime.of(endDateFieldValue.fieldValue.let { LocalDate.parse(it, dateTimeFormatter) }, LocalTime.MAX)
            } else {
                null
            }
            vulnerabilities.add(PsrCustomerVulnerability(
                vulnerabilityId = vulnerabilityRecord.vulnerabilityId!!,
                enrolmentDate = enrolmentDate.toLocalDateTime(),
                terminationDate = endDateFromExtraData,
                extraData = vulnerabilityExtraData,
                label = vulnerabilityLabel,
                coreId = coreId
            ))
        }

        return vulnerabilities
    }

    private fun getCustomerServices(customerId: Long, fetchHistory: Boolean): List<PsrCustomerService> {

        val historyCondition: Condition = if(fetchHistory) {
            PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.isNotNull
        } else {
            PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNotNull
        }

        // Perform a single query joining services, account registration, and extra data.
        val serviceRecords: Result<Record> = dsl
            .select(*PSR_SERVICE.fields(), PSR_CUSTOMER_SERVICE_REGISTER.ENROLMENT_DATE ,PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE, PSR_EXTRA_DATA.EXTRA_DATA_ID, PSR_EXTRA_DATA.FIELD_NAME, PSR_EXTRA_DATA.FIELD_DESCRIPTION, PSR_EXTRA_DATA.FIELD_TYPE, PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE, PSR_CUSTOMER_EXTRA_DATA.DEFINITION_DATE, PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE)
            .from(PSR_SERVICE)
            .innerJoin(PSR_CUSTOMER_SERVICE_REGISTER)
            .on(PSR_SERVICE.SERVICE_ID.eq(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID)
                .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull()))
            .leftJoin(PSR_EXTRA_DATA)
            .on(PSR_EXTRA_DATA.SERVICE_ID.eq(PSR_SERVICE.SERVICE_ID))
            .leftJoin(PSR_CUSTOMER_EXTRA_DATA)
            .on(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.eq(PSR_EXTRA_DATA.EXTRA_DATA_ID))
            .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull())
            .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID))
            .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(customerId))
            .fetch()

        // Group records by service id.
        val servicesMap = serviceRecords.groupBy { record ->
            record.get(PSR_SERVICE.SERVICE_ID)
        }

        val services = mutableListOf<PsrCustomerService>()

        servicesMap.forEach { (_, groupRecords) ->
            // Use the first record in the group to build the vulnerability and enrolmentDate.
            val firstRecord = groupRecords.first()
            val serviceRecord = firstRecord.into(PSR_SERVICE)
            val enrolmentDate: OffsetDateTime = firstRecord.get(PSR_CUSTOMER_SERVICE_REGISTER.ENROLMENT_DATE)!!
            val terminationDate: OffsetDateTime? = firstRecord.get(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE)
            val serviceLabel: String = firstRecord.get(PSR_SERVICE.CCA_LABEL) ?: ""
            val serviceAvailableNow: Boolean = firstRecord.get(PSR_SERVICE.AVAILABLE_NOW) == true
            val serviceAlwaysOn: Boolean = firstRecord.get(PSR_SERVICE.ALWAYS_ON) == true
            // Build the extra data list from all rows (filtering out null extra data rows).
            val serviceExtraData = <EMAIL>(groupRecords)

            services.add(
                PsrCustomerService(
                    serviceRecord.serviceId!!,
                    enrolmentDate.toLocalDateTime(),
                    terminationDate?.toLocalDateTime(),
                    serviceExtraData,
                    label = serviceLabel,
                    availableNow = serviceAvailableNow,
                    alwaysOn = serviceAlwaysOn
                )
            )
        }

        return services
    }

    private fun getCustomerExtraDataById(id: Long, customerId: Long): PsrCustomerExtraData? =
        dsl.selectFrom(PSR_CUSTOMER_EXTRA_DATA)
            .where(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.eq(id))
            .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(customerId))
            .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull())
            .fetchOneInto(PsrCustomerExtraData::class.java)

    private fun terminateAndInsertNewExtraData(extraData: PsrExtraDataField, customerId: Long, currentExtraDataId: Long) {
        dsl.update(PSR_CUSTOMER_EXTRA_DATA)
            .set(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE, LocalDateTime.now().atOffset(ZoneOffset.UTC))
            .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_EXTRA_DATA_ID.eq(currentExtraDataId))
            .execute()

        dsl.insertInto(PSR_CUSTOMER_EXTRA_DATA)
            .columns(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID,
                PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID,
                PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE,
                PSR_CUSTOMER_EXTRA_DATA.DEFINITION_DATE)
            .values(customerId,
                extraData.fieldId,
                extraData.fieldValue,
                LocalDateTime.now().atOffset(ZoneOffset.UTC)
            ).execute()
    }
}
