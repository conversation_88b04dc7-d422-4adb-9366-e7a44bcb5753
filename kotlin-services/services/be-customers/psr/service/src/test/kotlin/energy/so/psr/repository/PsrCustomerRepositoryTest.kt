package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.pojos.PsrCustomer
import energy.so.commons.model.psr.tables.pojos.PsrCustomerExtraData
import energy.so.commons.model.psr.tables.pojos.PsrCustomerVulnerabilityRegister
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_SERVICE_REGISTER
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_VULNERABILITY_REGISTER
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_CATEGORY
import energy.so.commons.model.psr.tables.references.PSR_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_SERVICE
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_SERVICE_LINK
import energy.so.database.test.installDatabase
import energy.so.psr.dto.PsrCustomerVulnerability
import energy.so.psr.dto.PsrExtraDataField
import energy.so.psr.fixtures.PsrData.aPsrCustomerEntity
import energy.so.psr.fixtures.PsrData.aPsrCustomerExtraData
import energy.so.psr.fixtures.PsrData.aPsrCustomerServiceEntryList
import energy.so.psr.fixtures.PsrData.aPsrCustomerServiceRegister
import energy.so.psr.fixtures.PsrData.aPsrCustomerVulnerabilityRegister
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityCategoryEntity
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityEntity
import energy.so.psr.fixtures.PsrData.aPsrExtraDataEntity
import energy.so.psr.fixtures.PsrData.aPsrService
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityServiceLink
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.LocalDateTime

class PsrCustomerRepositoryTest: BehaviorSpec({


    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")
    val sut = PsrCustomerRepository(db)
    
    given("existing data in db") {
        val vulnerabilities = listOf(
            PsrCustomerVulnerability(
                vulnerabilityId = 1,
                terminationDate = null,
                enrolmentDate = LocalDateTime.now(),
                extraData = listOf(
                    PsrExtraDataField(
                        fieldId = 1,
                        fieldName = "vulnerability1",
                        fieldDesc = "vulnerability1 description",
                        fieldValue = "vulnerability1 value",
                        definitionDate = LocalDateTime.now(),
                        replacementDate = LocalDateTime.now().plusDays(10)
                    )
                )
            )
        )
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
        `when`("registerCustomerForVulnerabilities is invoked") {
            sut.registerCustomerForVulnerabilities(1, vulnerabilities)
            then("customer vulnerabilities are registered") {
                val psrCustomerVulnerabilityRegister = db.select(PSR_CUSTOMER_VULNERABILITY_REGISTER.asterisk())
                    .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                    .fetchInto(PsrCustomerVulnerabilityRegister::class.java)
                val psrCustomerExtraData = db.select(PSR_CUSTOMER_EXTRA_DATA.asterisk())
                    .from(PSR_CUSTOMER_EXTRA_DATA)
                    .fetchInto(PsrCustomerExtraData::class.java)
                val psrCustomer = db.select(PSR_CUSTOMER.asterisk())
                    .from(PSR_CUSTOMER)
                    .fetchInto(PsrCustomer::class.java)
                assertSoftly {
                    psrCustomerVulnerabilityRegister.size shouldBe 1
                    psrCustomerVulnerabilityRegister[0].customerId shouldBe 1
                    psrCustomerVulnerabilityRegister[0].vulnerabilityId shouldBe 1
                    psrCustomerVulnerabilityRegister[0].vulnerabilityRegisterId shouldBe 1
                    psrCustomerVulnerabilityRegister[0].enrolmentDate?.toLocalDate() shouldBe LocalDate.now()
                    psrCustomerExtraData.size shouldBe 1
                    psrCustomerExtraData[0].extraDataId shouldBe 1L
                    psrCustomerExtraData[0].customerId shouldBe 1L
                    psrCustomerExtraData[0].fieldValue shouldBe "vulnerability1 value"
                    psrCustomerExtraData[0].definitionDate?.toLocalDate() shouldBe LocalDate.now()
                    psrCustomer.size shouldBe 1
                }
            }
        }

        `when`("addSingleVulnerabilityToCustomer is invoked") {

            db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
            db.newRecord(PSR_VULNERABILITY_CATEGORY, aPsrVulnerabilityCategoryEntity).apply { save() }
            db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity).apply { save() }
            db.newRecord(PSR_EXTRA_DATA, aPsrExtraDataEntity).apply { save() }

            sut.syncJuniferVulnerabilityToCustomer(1, vulnerabilities[0], 1L, LocalDate.now())
            then("the vulnerability is registered and nothing gets terminated") {

                val terminatedVulnerability = db.select(PSR_CUSTOMER_VULNERABILITY_REGISTER.asterisk())
                    .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                    .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNotNull)
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(1))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(1))
                    .fetchInto(PsrCustomerVulnerabilityRegister::class.java)

                val newVulnerability = db.select(PSR_CUSTOMER_VULNERABILITY_REGISTER.asterisk())
                    .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                    .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(1))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(1))
                    .fetchInto(PsrCustomerVulnerabilityRegister::class.java)

                val replacedExtraData = db.select(PSR_CUSTOMER_EXTRA_DATA.asterisk())
                    .from(PSR_CUSTOMER_EXTRA_DATA)
                    .innerJoin(PSR_EXTRA_DATA).on(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.eq(PSR_EXTRA_DATA.EXTRA_DATA_ID).and(PSR_EXTRA_DATA.VULNERABILITY_ID.eq(1)))
                    .where(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNotNull)
                    .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(1))
                    .fetchInto(PsrCustomerExtraData::class.java)

                val newExtraData = db.select(PSR_CUSTOMER_EXTRA_DATA.asterisk())
                    .from(PSR_CUSTOMER_EXTRA_DATA)
                    .innerJoin(PSR_EXTRA_DATA).on(PSR_CUSTOMER_EXTRA_DATA.EXTRA_DATA_ID.eq(PSR_EXTRA_DATA.EXTRA_DATA_ID).and(PSR_EXTRA_DATA.VULNERABILITY_ID.eq(1)))
                    .where(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                    .and(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(1))
                    .fetchInto(PsrCustomerExtraData::class.java)

                assertSoftly {
                    terminatedVulnerability.size shouldBe 0
                    newVulnerability.size shouldBe 1
                    replacedExtraData.size shouldBe 0
                    newExtraData.size shouldBe 1
                }
            }
        }

        `when`("terminateCustomerVulnerability is invoked") {
            db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity.copy(customerId = 2)).apply { save() }
            db.newRecord(PSR_VULNERABILITY_CATEGORY, aPsrVulnerabilityCategoryEntity).apply { save() }
            db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity.copy(vulnerabilityId = 2)).apply { save() }
            db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity.copy(vulnerabilityId = 3)).apply { save() }
            db.newRecord(PSR_SERVICE, aPsrService.copy(serviceId = 2)).apply { save() }
            db.newRecord(PSR_SERVICE, aPsrService.copy(serviceId = 3)).apply { save() }
            db.newRecord(PSR_SERVICE, aPsrService.copy(serviceId = 4)).apply { save() }
            db.newRecord(PSR_CUSTOMER_VULNERABILITY_REGISTER, aPsrCustomerVulnerabilityRegister.copy(vulnerabilityRegisterId = 2, vulnerabilityId = 2, customerId = 2, terminationDate = null)).apply { save() }
            db.newRecord(PSR_CUSTOMER_VULNERABILITY_REGISTER, aPsrCustomerVulnerabilityRegister.copy(vulnerabilityRegisterId = 3, vulnerabilityId = 3, customerId = 2, terminationDate = null)).apply { save() }
            db.newRecord(PSR_CUSTOMER_SERVICE_REGISTER, aPsrCustomerServiceRegister.copy(serviceRegisterId = 1009, customerId = 2, serviceId = 2, terminationDate = null)).apply { save() }
            db.newRecord(PSR_CUSTOMER_SERVICE_REGISTER, aPsrCustomerServiceRegister.copy(serviceRegisterId = 1010, customerId = 2, serviceId = 4, terminationDate = null)).apply { save() }
            db.delete(PSR_VULNERABILITY_SERVICE_LINK).execute()
            db.newRecord(PSR_VULNERABILITY_SERVICE_LINK, aPsrVulnerabilityServiceLink.copy(vulnerabilityServiceId = 2001, vulnerabilityId = 2, serviceId = 2)).apply { save() }
            db.newRecord(PSR_VULNERABILITY_SERVICE_LINK, aPsrVulnerabilityServiceLink.copy(vulnerabilityServiceId = 2002, vulnerabilityId = 2, serviceId = 4)).apply { save() }
            db.newRecord(PSR_VULNERABILITY_SERVICE_LINK, aPsrVulnerabilityServiceLink.copy(vulnerabilityServiceId = 2003, vulnerabilityId = 3, serviceId = 2)).apply { save() }
            sut.terminateCustomerVulnerability(customerId = 2, vulnerabilityId = 2)
            then("customer's relevant vulnerabilities and services are terminated") {
                val vulnerability2Customer2Count = db.selectCount()
                    .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                    .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(2))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(2))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
                    .fetchOne(0, Int::class.java) ?: 0
                val vulnerability3Customer2Count = db.selectCount()
                    .from(PSR_CUSTOMER_VULNERABILITY_REGISTER)
                    .where(PSR_CUSTOMER_VULNERABILITY_REGISTER.CUSTOMER_ID.eq(2))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.VULNERABILITY_ID.eq(3))
                    .and(PSR_CUSTOMER_VULNERABILITY_REGISTER.TERMINATION_DATE.isNull)
                    .fetchOne(0, Int::class.java) ?: 0
                val service2Customer2Count = db.selectCount()
                    .from(PSR_CUSTOMER_SERVICE_REGISTER)
                    .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(2))
                    .and(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID.eq(2))
                    .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull)
                    .fetchOne(0, Int::class.java) ?: 0
                val service4Customer2Count = db.selectCount()
                    .from(PSR_CUSTOMER_SERVICE_REGISTER)
                    .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(2))
                    .and(PSR_CUSTOMER_SERVICE_REGISTER.SERVICE_ID.eq(4))
                    .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull)
                    .fetchOne(0, Int::class.java) ?: 0
                assertSoftly {
                    vulnerability2Customer2Count shouldBe 0
                    vulnerability3Customer2Count shouldBe 1
                    service2Customer2Count shouldBe 1
                    service4Customer2Count shouldBe 0
                }
            }
        }
    }

    given("PsrCustomerRepository") {
        db.newRecord(PSR_SERVICE, aPsrService.copy(serviceId = 5)).apply { save() }
        db.newRecord(PSR_SERVICE, aPsrService.copy(serviceId = 2)).apply { save() }
        db.newRecord(PSR_EXTRA_DATA, aPsrExtraDataEntity.copy(serviceId = 5, vulnerabilityId = null, extraDataId = 11)).apply { save() }
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
        db.newRecord(PSR_CUSTOMER_SERVICE_REGISTER, aPsrCustomerServiceRegister.copy(serviceRegisterId = 20, terminationDate = null, serviceId = 5)).apply { save() }
        db.newRecord(PSR_CUSTOMER_EXTRA_DATA, aPsrCustomerExtraData).apply { save() }
        `when`("registerCustomerForServices is invoked") {
            val oldExtraData = db.select(PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE).from(PSR_CUSTOMER_EXTRA_DATA)
                .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(1))
                .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                .fetchOneInto(PsrCustomerExtraData::class.java)!!

            val oldServiceCount = db.selectCount().from(PSR_CUSTOMER_SERVICE_REGISTER)
                .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(1))
                .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull).execute()

            sut.registerCustomerForServices(1, aPsrCustomerServiceEntryList)

            val expectedServiceCount = db.selectCount().from(PSR_CUSTOMER_SERVICE_REGISTER)
                .where(PSR_CUSTOMER_SERVICE_REGISTER.CUSTOMER_ID.eq(1))
                .and(PSR_CUSTOMER_SERVICE_REGISTER.TERMINATION_DATE.isNull())
                .fetchOne(0, Int::class.java);

            val expectedExtraData = db.select(PSR_CUSTOMER_EXTRA_DATA.FIELD_VALUE).from(PSR_CUSTOMER_EXTRA_DATA)
                .where(PSR_CUSTOMER_EXTRA_DATA.CUSTOMER_ID.eq(1))
                .and(PSR_CUSTOMER_EXTRA_DATA.REPLACEMENT_DATE.isNull)
                .fetchOneInto(PsrCustomerExtraData::class.java)!!

            then("it adds new services and updates services with new extra data if there are any") {
                oldExtraData.fieldValue shouldBe "Passphrase"
                expectedExtraData.fieldValue shouldBe "newphrase"
                oldServiceCount shouldBe 1
                expectedServiceCount shouldBe 2
            }
        }
    }
})