package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.database.test.installDatabase
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityEntity
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class PsrVulnerabilityRepositoryTest : BehaviorSpec({
    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")

    val sut = PsrVulnerabilityRepository(db)

    given("existing data in db") {
        db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity)
            .apply { save() }
        `when`("getByIds is invoked") {
            val response = sut.getByIds(listOf(1))
            then("list of vulnerabilities is returned") {
                response.size shouldBe 1
                response.first().vulnerabilityId shouldBe aPsrVulnerabilityEntity.vulnerabilityId
                response.first().vulnerabilityCategoryId shouldBe aPsrVulnerabilityEntity.vulnerabilityCategoryId
                response.first().displayOrder shouldBe aPsrVulnerabilityEntity.displayOrder
                response.first().customerLabel shouldBe aPsrVulnerabilityEntity.customerLabel
                response.first().customerLabelDesc shouldBe aPsrVulnerabilityEntity.customerLabelDesc
                response.first().ccaLabel shouldBe aPsrVulnerabilityEntity.ccaLabel
                response.first().ccaLabelDesc shouldBe aPsrVulnerabilityEntity.ccaLabelDesc
                response.first().juniferReference shouldBe aPsrVulnerabilityEntity.juniferReference
            }
        }
    }
})