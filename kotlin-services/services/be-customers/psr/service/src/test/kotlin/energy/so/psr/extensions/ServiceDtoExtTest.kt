package energy.so.psr.extensions

import com.google.protobuf.NullValue
import energy.so.commons.nullableString
import energy.so.customers.psr.fixtures.aServiceDto
import energy.so.customers.v2.psr.getServicesResponse
import energy.so.customers.v2.psr.serviceDetails
import energy.so.customers.v2.psr.serviceExtraItem
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class ServiceDtoExtTest : BehaviorSpec({
    given("A list of ServiceDto") {
        val serviceDto = aServiceDto
        `when`("Converted to GRPC response") {
            val sut = listOf(serviceDto).toGrpcResponse()
            then("It converts correctly")
            {
                sut shouldBe getServicesResponse {
                    services.add(serviceDetails {
                        id = 1
                        title = "Service"
                        label = "Service Label"
                        description = "Service Desc"
                        alwaysOn = true
                        availableNow = true
                        extraItems.add(serviceExtraItem {
                            id = 1
                            name = "Please input data"
                            description = "Data required"
                            type = "input"
                            required = true
                            displayOrder = 1
                            placeholder = nullableString { null_ = NullValue.NULL_VALUE }
                        })
                    })
                }
            }
        }
    }
})