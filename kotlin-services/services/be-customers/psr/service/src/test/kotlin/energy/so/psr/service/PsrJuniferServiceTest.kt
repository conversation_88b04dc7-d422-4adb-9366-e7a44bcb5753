package energy.so.psr.service

import com.google.protobuf.Empty
import energy.so.ac.junifer.v1.customers.CreateCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.CustomersSyncClient
import energy.so.ac.junifer.v1.customers.DeleteCustomerPsrRequest
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.model.psr.tables.pojos.PsrVulnerability
import energy.so.psr.repository.PsrVulnerabilityRepository
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDateTime

class PsrJuniferServiceTest : BehaviorSpec({
    val mockCustomersSyncClient = mockk<CustomersSyncClient>()
    val mockPsrVulnerabilityRepository = mockk<PsrVulnerabilityRepository>()

    given("Junifer psr mapping exists") {
        val request = slot<CreateCustomerPsrRequest>()
        val endDate = LocalDateTime.now().plusDays(2)
        every { mockPsrVulnerabilityRepository.getById(1L) } returns PsrVulnerability(juniferReference = "[{\"gasValue\":29,\"electricityValue\":29}]")
        coEvery { mockCustomersSyncClient.createCustomerPsr(capture(request)) } returns Empty.getDefaultInstance()
        val sut = PsrJuniferService(mockCustomersSyncClient, mockPsrVulnerabilityRepository)
        `when`("createJuniferCustomerPsr is invoked") {
            val response = sut.createJuniferCustomerPsr(1, 1, "1", endDate)
            then("psr is updated in junifer") {
                assertSoftly {
                    response shouldBe Unit
                    request.captured.customerId shouldBe "1"
                    request.captured.utilityMarket shouldBe "UK Electricity"
                    request.captured.propertyTblId shouldBe 1L
                    request.captured.consentFl shouldBe true.toNullableBoolean()
                    request.captured.toDt shouldBe endDate.toNullableTimestamp()
                    request.captured.value shouldBe "29"
                    verify { mockPsrVulnerabilityRepository.getById(1L) }
                    coVerify { mockCustomersSyncClient.createCustomerPsr(any()) }
                }
            }
        }
    }

    given("Junifer psr mapping does not exists") {
        every { mockPsrVulnerabilityRepository.getById(1L) } returns PsrVulnerability(juniferReference = "[]")
        val sut = PsrJuniferService(mockCustomersSyncClient, mockPsrVulnerabilityRepository)
        `when`("createJuniferCustomerPsr is invoked") {
            val response  = sut.createJuniferCustomerPsr(1, 1, "1")
            then("junifer is not updated") {
                assertSoftly {
                    response shouldBe Unit
                    verify { mockPsrVulnerabilityRepository.getById(1L) }
                }
            }
        }
    }

    given("delete Junifer psr mapping") {
        val request = slot<DeleteCustomerPsrRequest>()
        coEvery { mockCustomersSyncClient.deleteCustomerPsr(capture(request)) } returns Empty.getDefaultInstance()
        val sut = PsrJuniferService(mockCustomersSyncClient, mockPsrVulnerabilityRepository)
        `when`("deleteJuniferCustomerPsr is invoked") {
            val response  = sut.deleteJuniferCustomerPsr("1", 2)
            then("psr deleted from junifer") {
                assertSoftly {
                    response shouldBe Unit
                    request.captured.customerId shouldBe "1"
                    request.captured.psrId shouldBe 2
                    coVerify { mockCustomersSyncClient.deleteCustomerPsr(any()) }
                }
            }
        }
    }

})