package energy.so.psr.service

import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationDto
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.communications.v1.dtos.RecipientDto
import energy.so.psr.dto.PsrConfirmationEmailRequest
import energy.so.psr.dto.PsrInvitationEmailRequest
import energy.so.psr.fixtures.PsrData.aPsrCustomerSettings
import energy.so.psr.fixtures.PsrData.aPsrService
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityEntity
import energy.so.psr.handler.PsrCustomerHandler
import energy.so.psr.repository.PsrServiceRepository
import energy.so.psr.repository.PsrVulnerabilityRepository
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk

class PsrEmailServiceTest : BehaviorSpec({

    val mockPsrCustomerHandler = mockk<PsrCustomerHandler>()
    val mockClient = mockk<CommunicationClient>()
    val mockPsrVulnerabilityRepository = mockk<PsrVulnerabilityRepository>()
    val mockPsrServiceRepository = mockk<PsrServiceRepository>()

    val sut = PsrEmailService(
        mockPsrCustomerHandler,
        mockClient,
        mockPsrVulnerabilityRepository,
        mockPsrServiceRepository,
    )

    given("Psr invitation email") {
        val emailRequest = PsrInvitationEmailRequest(
            customerName = "John Smith",
            email = "<EMAIL>",
            templateName = "psr_invite",
            psrLink = "https://psr.invitations.org/psr",
            myAccountLink = "https://psr.invitations.org/account",
            accountNumber = "********"
        )
        val emailTemplateDto = CommunicationTemplateDto(
            communicationName = emailRequest.templateName,
            recipient = RecipientDto(
                name = emailRequest.customerName,
                email = emailRequest.email
            ),
            customAttributes = mapOf(
                "priority_services_register_link" to emailRequest.psrLink,
                "my_account" to emailRequest.myAccountLink,
                "first_name" to "John Smith",
                "account_number" to "********",
            )
        )
        coEvery { mockClient.sendCommunicationTemplate(emailTemplateDto) } returns CommunicationDto(1, 1)
        `when`("sendPsrInvitationEmail is invoked") {
            sut.sendPsrInvitationEmail(emailRequest)
            then("email request is sent") {
                assertSoftly {
                    coVerify { mockClient.sendCommunicationTemplate(emailTemplateDto) }
                }
            }
        }
    }

    given("Psr confirmation email with passphrase") {
        val emailRequest = PsrConfirmationEmailRequest(
            customerName = "John Smith",
            customerId = 1,
            email = "<EMAIL>",
            templateName = "psr_confirmation",
            myAccountLink = "https://psr.invitations.org/account",
            accountNumber = "********"
        )
        val emailTemplateDto = CommunicationTemplateDto(
            communicationName = emailRequest.templateName,
            recipient = RecipientDto(
                name = emailRequest.customerName,
                email = emailRequest.email
            ),
            customAttributes = mapOf(
                "my_account " to emailRequest.myAccountLink,
                "vulnerabilities" to "Pensionable age",
                "services" to "Priority emergency support",
                "extra_data_list" to "Your passphrase for in-person visits by us or our partners: - London Bridge is down",
                "first_name" to "John Smith",
                "account_number" to "********",
            )
        )
        coEvery { mockClient.sendCommunicationTemplate(emailTemplateDto) } returns CommunicationDto(1, 1)
        coEvery { mockPsrCustomerHandler.getCurrentPsrSettingsForCustomer(1) } returns aPsrCustomerSettings
        coEvery { mockPsrVulnerabilityRepository.getByIds(listOf(1)) } returns listOf(aPsrVulnerabilityEntity)
        coEvery { mockPsrServiceRepository.getByIds(listOf(1)) } returns listOf(aPsrService)
        coJustRun { mockClient.sendCommunicationTemplate(emailTemplateDto) }
        `when`("sendPsrInvitationEmail is invoked") {
            sut.sendPsrConfirmationEmail(emailRequest)
            then("email request is sent") {
                assertSoftly {
                    coVerify { mockClient.sendCommunicationTemplate(emailTemplateDto) }
                    coVerify { mockClient.sendCommunicationTemplate(emailTemplateDto) }
                    coVerify { mockPsrCustomerHandler.getCurrentPsrSettingsForCustomer(1) }
                    coVerify { mockPsrVulnerabilityRepository.getByIds(listOf(1)) }
                    coVerify { mockPsrServiceRepository.getByIds(listOf(1)) }
                }
            }
        }
    }
})
