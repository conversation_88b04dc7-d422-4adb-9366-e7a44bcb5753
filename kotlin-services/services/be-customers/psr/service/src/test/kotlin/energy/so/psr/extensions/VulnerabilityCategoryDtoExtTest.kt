package energy.so.psr.extensions

import energy.so.customers.psr.fixtures.aVulnerabilityCategory
import energy.so.customers.v2.psr.getVulnerabilityCategoriesResponse
import energy.so.customers.v2.psr.vulnerabilityCategory
import energy.so.customers.v2.psr.vulnerabilityDetails
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class VulnerabilityCategoryDtoExtTest : BehaviorSpec({
    given("A list of vulnerability categories") {
        val vulnerabilityCategories = listOf(aVulnerabilityCategory).toGrpcResponse()
        `when`("Converted to GRPC response") {
            val result = vulnerabilityCategories
            then("it converts the response correctly") {
                result shouldBe getVulnerabilityCategoriesResponse {
                    vulnerabilityCategory.add(vulnerabilityCategory {
                        id = 1
                        name = "Household"
                        description = "Who lives at the property"
                        vulnerabilities.add(
                            vulnerabilityDetails
                            {
                                id = 1
                                displayOrder = 1
                                label = "Pensioner"
                                description = "Above 65"
                                requiresEndDate = false
                            })
                    })
                }
            }
        }
    }
})