package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.references.PSR_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_SERVICE
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_CATEGORY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_SERVICE_LINK
import energy.so.customers.psr.fixtures.psrExtraData
import energy.so.customers.psr.fixtures.psrService
import energy.so.customers.psr.fixtures.psrVulnerability
import energy.so.customers.psr.fixtures.psrVulnerabilityCategory
import energy.so.customers.psr.fixtures.psrVulnerabilityServiceLink
import energy.so.database.test.installDatabase
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext

class PsrReferenceDataRepositoryTest : BehaviorSpec({
    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")
    val sut = PsrReferenceDataRepository(db)
    val srvc = PSR_SERVICE
    val link = PSR_VULNERABILITY_SERVICE_LINK
    val extra = PSR_EXTRA_DATA

    beforeTest {
        listOf(srvc, link, extra).forEach {
            db.truncate(it).restartIdentity().cascade().execute()
        }
    }

    given("::fetchAllServicesByVulnerabilityIds") {
        `when`("When fetching services based on vulnerability id's") {
            insertData(db)
            val result = sut.fetchAllServicesByVulnerabilityIds(listOf(1, 2))

            then("return the correct response") {
                result.size shouldBe 3
                result[0].serviceId shouldBe 1
                result[0].extraData.size shouldBe 3
                result[0].extraData[0].displayOrder shouldBe 1
                result[0].extraData[1].displayOrder shouldBe 2
                result[0].extraData[2].displayOrder shouldBe 3
                result[1].serviceId shouldBe 2
                result[1].extraData.size shouldBe 0

            }
        }
    }

    given("::fetchVulnerabilityByJuniferData") {
        db.newRecord(PSR_VULNERABILITY_CATEGORY, psrVulnerabilityCategory).apply { save() }
        db.newRecord(PSR_VULNERABILITY, psrVulnerability).apply { save() }
        `when`("fetchVulnerabilityByJuniferData is invoked") {
            val response = sut.fetchVulnerabilityByJuniferData(1,1)
            then("return the correct psr vulnerability is returned") {
                assertSoftly {
                    response?.juniferReference shouldBe "[{\"gasValue\":1,\"electricityValue\":1}]"
                }
            }
        }
    }


})

private fun insertData(db: DSLContext) {
    for (i in 1..3) {
        val key = i.toLong()
        db.newRecord(PSR_SERVICE, psrService.copy(serviceId = key))
            .apply { save() }
        db.newRecord(PSR_EXTRA_DATA, psrExtraData.copy(extraDataId = key, serviceId = 1, displayOrder = key.toInt()))
            .apply { save() }
    }

    db.newRecord(PSR_VULNERABILITY_SERVICE_LINK, psrVulnerabilityServiceLink).apply { save() }
    db.newRecord(
        PSR_VULNERABILITY_SERVICE_LINK,
        psrVulnerabilityServiceLink.copy(vulnerabilityServiceId = 2, serviceId = 2, displayOrder = 2)
    ).apply { save() }
    db.newRecord(
        PSR_VULNERABILITY_SERVICE_LINK,
        psrVulnerabilityServiceLink.copy(
            vulnerabilityServiceId = 3,
            vulnerabilityId = 2,
            serviceId = 3,
            displayOrder = 1
        )
    ).apply { save() }
}