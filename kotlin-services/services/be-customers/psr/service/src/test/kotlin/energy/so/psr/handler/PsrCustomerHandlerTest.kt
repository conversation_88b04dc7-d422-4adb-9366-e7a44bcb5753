package energy.so.psr.handler

import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.psr.dto.PsrCustomerSettings
import energy.so.psr.dto.PsrCustomerVulnerability
import energy.so.psr.extensions.toPsrCustomerSettings
import energy.so.psr.fixtures.PsrData.aPsrExtraDataEntity
import energy.so.psr.repository.PsrCustomerRepository
import energy.so.psr.repository.PsrExtraDataRepository
import energy.so.psr.service.PsrJuniferService
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime

class PsrCustomerHandlerTest : BehaviorSpec({
    val mockPsrCustomerRepository = mockk<PsrCustomerRepository>()
    val mockPsrExtraDataRepository = mockk<PsrExtraDataRepository>()
    val mockPsrJuniferService = mockk<PsrJuniferService>()
    val sut = PsrCustomerHandler(
        mockPsrCustomerRepository,
        mockPsrJuniferService,
        mockPsrExtraDataRepository
    )

    given("Vulnerability 1 from UI and Vulnerability 1 in DB") {
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1), 1)
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("junifer is not updated") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }

    given("Vulnerability 1 from UI and Vulnerability empty in DB") {
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any())} returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(any(), 1, "1") } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("junifer is updated with Vulnerability 1") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any())}
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(any(), 1, "1") }
                }
            }
        }
    }

    given("Vulnerability 1,2 from UI and Vulnerability 1 in DB") {
        clearMocks(mockPsrJuniferService)
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1,2), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(any(), 1, "1") } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked - ") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("junifer is updated with Vulnerability 2") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(2, 1, "1") }
                    coVerify(exactly = 0) { mockPsrJuniferService.createJuniferCustomerPsr(1, 1, "1") }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }

    given("Vulnerability 1,2,3 from UI and Vulnerability 1,2 in DB") {
        clearMocks(mockPsrJuniferService)
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1,2,3,4), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1,2), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(any(), 1, "1") } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("junifer is updated with Vulnerability 3,4") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(3, 1, "1") }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(4, 1, "1") }
                    coVerify(exactly = 0) { mockPsrJuniferService.createJuniferCustomerPsr(1, 1, "1") }
                    coVerify(exactly = 0) { mockPsrJuniferService.createJuniferCustomerPsr(2, 1, "1") }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }

    given("Vulnerability empty from UI and Vulnerability 1,2 in DB") {
        clearMocks(mockPsrJuniferService)
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1,2), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.deleteJuniferCustomerPsr( "1", 0) } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb

        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("Vulnerability 1,2 delete from junifer") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.deleteJuniferCustomerPsr("1", 0) }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }


    given("Vulnerability 4 from UI and Vulnerability 1,2,3 in DB") {
        clearMocks(mockPsrJuniferService)
        val endDate = LocalDateTime.now()
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(4), 1, endDate)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1,2,3), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.deleteJuniferCustomerPsr( "1", 0) } returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(4, 1, "1", endDate) } returns Unit
        coEvery { mockPsrExtraDataRepository.getTerminationDateExtraDataByVulnerabilityId(4) } returns aPsrExtraDataEntity.copy(vulnerabilityId = 4)
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("Vulnerability 4 added and 1,2,3 delete from junifer") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.deleteJuniferCustomerPsr("1", 0) }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(4, 1, "1", endDate) }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }


    given("Vulnerability 1 from UI and Vulnerability 1,2,3 in DB") {
        clearMocks(mockPsrJuniferService)
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1,2,3), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.deleteJuniferCustomerPsr( "1", 0) } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("Vulnerability 2,3 delete from junifer") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.deleteJuniferCustomerPsr("1", 0) }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }

    given("Vulnerability 3,4,5 from UI and Vulnerability 1,2,3 in DB") {
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(3,4,5), 1)
        val psrCustomerSettingsDb = createPsrCustomerSettings(listOf(1,2,3), 1)
        every { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) } returns Unit
        coEvery { mockPsrJuniferService.deleteJuniferCustomerPsr( "1", 0) } returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(4, 1, "1") } returns Unit
        coEvery { mockPsrJuniferService.createJuniferCustomerPsr(5, 1, "1") } returns Unit
        every { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) } returns psrCustomerSettingsDb
        `when`("addCustomerVulnerabilitiesAndAssociatedServices is invoked") {
            sut.addCustomerVulnerabilitiesAndAssociatedServices(psrCustomerSettingsGrpc, 1)
            then("Vulnerability 2,3 delete from junifer") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.addPsrCustomer(psrCustomerSettingsDb.customerId, true) }
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsDb.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsDb.customerId, any()) }
                    coVerify { mockPsrJuniferService.deleteJuniferCustomerPsr("1", 0) }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(5, 1, "1") }
                    coVerify { mockPsrJuniferService.createJuniferCustomerPsr(4, 1, "1") }
                    verify { mockPsrCustomerRepository.getPsrSettingsForCustomer(psrCustomerSettingsDb.customerId) }
                }
            }
        }
    }

    given("savePsrSettingsForCustomer") {
        val psrCustomerSettingsGrpc = createPsrCustomerSettingsGrpc(listOf(1), 1)
        every { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsGrpc.customerId, any()) } returns Unit
        every { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsGrpc.customerId, any()) } returns Unit
        `when`("savePsrSettingsForCustomer is invoked") {
            sut.savePsrSettingsForCustomer(psrCustomerSettingsGrpc.toPsrCustomerSettings())
            then("customerSettings is returned") {
                assertSoftly {
                    verify { mockPsrCustomerRepository.registerCustomerForVulnerabilities(psrCustomerSettingsGrpc.customerId, any()) }
                    verify { mockPsrCustomerRepository.registerCustomerForServices(psrCustomerSettingsGrpc.customerId, any()) }
                }
            }
        }
    }

})

private fun createPsrCustomerSettings(vulIds: List<Long>, custId: Long): PsrCustomerSettings {
    return PsrCustomerSettings(
        customerId = custId,
        lastConfirmed = LocalDateTime.now(),
        communicationPreferences = "email",
        customerVulnerabilities =
            vulIds.map { each -> PsrCustomerVulnerability(
                vulnerabilityId = each,
                enrolmentDate = LocalDateTime.now(),
                terminationDate = null,
                extraData = listOf()
            ) }.toList(),
        customerServices = listOf(),
    )
}

private fun createPsrCustomerSettingsGrpc(
    vulIds: List<Long>,
    customerId: Long,
    endDate: LocalDateTime? = null
): energy.so.customers.v2.psr.PsrCustomerSettings {
    val psrExtraData = energy.so.customers.v2.psr.PsrExtraDataField.newBuilder()
        .setFieldId(1)
        .setFieldName("Passphrase")
        .setFieldValue("Mirage")
        .build()
    val psrCustomerService = energy.so.customers.v2.psr.PsrCustomerService.newBuilder()
        .setServiceId(1)
        .setEnrolmentDate(LocalDate.now().toNullableTimestamp())
        .addExtraData(psrExtraData)
        .build()
    return energy.so.customers.v2.psr.PsrCustomerSettings.newBuilder()
        .setCustomerId(customerId)
        .addAllCustomerVulnerabilities(
            vulIds.map { each ->
                energy.so.customers.v2.psr.PsrCustomerVulnerability.newBuilder()
                    .setVulnerabilityId(each)
                    .setEnrolmentDate(LocalDate.now().toNullableTimestamp())
                    .setTerminationDate(endDate.toNullableTimestamp())
                    .build()
            }.toList()

        )
        .addCustomerServices(psrCustomerService)
        .build()
}

