package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.references.PSR_SERVICE
import energy.so.database.test.installDatabase
import energy.so.psr.fixtures.PsrData.aPsrService
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class PsrServiceRepositoryTest : BehaviorSpec({
    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")

    val sut = PsrServiceRepository(db)

    given("existing data in db") {
        db.newRecord(PSR_SERVICE, aPsrService)
            .apply { save() }
        `when`("getByIds is invoked") {
            val response = sut.getByIds(listOf(1))
            then("list of services is returned") {
                response.size shouldBe 1
                response.first().serviceId shouldBe aPsrService.serviceId
                response.first().customerLabel shouldBe aPsrService.customerLabel
                response.first().customerLabelDesc shouldBe aPsrService.customerLabelDesc
                response.first().ccaLabel shouldBe aPsrService.ccaLabel
                response.first().ccaLabelDesc shouldBe aPsrService.ccaLabelDesc
                response.first().alwaysOn shouldBe aPsrService.alwaysOn
                response.first().availableNow shouldBe aPsrService.availableNow
                response.first().ofgemRequired shouldBe aPsrService.ofgemRequired
            }
        }
    }
})