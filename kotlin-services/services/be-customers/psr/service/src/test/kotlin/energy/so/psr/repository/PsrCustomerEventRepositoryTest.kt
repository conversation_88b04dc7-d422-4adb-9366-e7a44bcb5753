package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER_EVENT
import energy.so.commons.model.psr.tables.references.PSR_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_CATEGORY
import energy.so.database.test.installDatabase
import energy.so.psr.fixtures.PsrData.aNonTerminationDatePsrExtraData
import energy.so.psr.fixtures.PsrData.aPsrCustomerEntity
import energy.so.psr.fixtures.PsrData.aPsrCustomerEvent
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityCategoryEntity
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityEntity
import energy.so.psr.fixtures.PsrData.aTerminationDatePsrExtraData
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

class PsrCustomerEventRepositoryTest : BehaviorSpec({
    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")

    val sut = PsrCustomerEventRepository(db)

    given("Record exists") {
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
        db.newRecord(PSR_CUSTOMER_EVENT, aPsrCustomerEvent).apply { save() }

        `when`("getLatestCustomerEventOfType is invoked") {
            val response = sut.getLatestCustomerEventOfType(1L, "EMAIL")

            then("latest event is returned") {
                response shouldNotBe null
                response?.eventType shouldBe aPsrCustomerEvent.eventType
                response?.eventName shouldBe aPsrCustomerEvent.eventName
            }
        }
    }

    given("Record does not exist") {
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }

        `when`("saveCustomerEvent is invoked") {
            sut.saveCustomerEvent(1, "TYPE", "Name")

            then("record exists") {
                val response = sut.getLatestCustomerEventOfType(1L, "TYPE")
                response shouldNotBe null
                response?.eventType shouldBe "TYPE"
                response?.eventName shouldBe "Name"
            }
        }
    }
})