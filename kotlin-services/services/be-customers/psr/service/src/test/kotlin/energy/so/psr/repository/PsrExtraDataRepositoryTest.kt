package energy.so.psr.repository

import energy.so.commons.extension.save
import energy.so.commons.model.psr.tables.references.PSR_CUSTOMER
import energy.so.commons.model.psr.tables.references.PSR_EXTRA_DATA
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY
import energy.so.commons.model.psr.tables.references.PSR_VULNERABILITY_CATEGORY
import energy.so.database.test.installDatabase
import energy.so.psr.fixtures.PsrData.aNonTerminationDatePsrExtraData
import energy.so.psr.fixtures.PsrData.aPsrCustomerEntity
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityCategoryEntity
import energy.so.psr.fixtures.PsrData.aPsrVulnerabilityEntity
import energy.so.psr.fixtures.PsrData.aTerminationDatePsrExtraData
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

class PsrExtraDataRepositoryTest : BehaviorSpec({
    val db = installDatabase(schemaOverride = "psr", migrationDirOverride = "classpath:db/migrations")

    val sut = PsrExtraDataRepository(db)

    given("Termination date exists") {
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
        db.newRecord(PSR_VULNERABILITY_CATEGORY, aPsrVulnerabilityCategoryEntity).apply { save() }
        db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity).apply { save() }
        db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity.copy(vulnerabilityId = 2)).apply { save() }
        `when`("getTerminationDateExtraDataByVulnerabilityId is invoked") {
            db.newRecord(PSR_EXTRA_DATA, aTerminationDatePsrExtraData)
                .apply { save() }
            val response = sut.getTerminationDateExtraDataByVulnerabilityId(1)
            then("extra date is returned") {
                response shouldNotBe null
                response?.vulnerabilityId shouldBe aTerminationDatePsrExtraData.vulnerabilityId
                response?.fieldName shouldBe aTerminationDatePsrExtraData.fieldName
            }
        }
    }

    given("Termination date does not exists") {
        db.newRecord(PSR_CUSTOMER, aPsrCustomerEntity).apply { save() }
        db.newRecord(PSR_VULNERABILITY_CATEGORY, aPsrVulnerabilityCategoryEntity).apply { save() }
        db.newRecord(PSR_VULNERABILITY, aPsrVulnerabilityEntity.copy(vulnerabilityId = 2)).apply { save() }
        db.newRecord(PSR_EXTRA_DATA, aNonTerminationDatePsrExtraData)
            .apply { save() }
        `when`("getTerminationDateExtraDataByVulnerabilityId is invoked") {
            val response = sut.getTerminationDateExtraDataByVulnerabilityId(2)
            then("no extra date is returned") {
                response shouldBe null
            }
        }
    }
})