package energy.so.customers.client.models

import energy.so.customers.sync.v2.CustomerSettingsEntity
import energy.so.customers.v2.CreateCustomerEnrolmentRequest
import java.time.LocalDateTime

/**
 * Customer model that contains all customer data. Contains mapping between [energy.so.commons.model.tables.pojos.Customer1]
 * and [Customer] and between [Customer] and [energy.so.customers.v2.customers.Customer]
 */
data class CustomerSetting(
    val id: Long? = null,
    @Deprecated("This field is no longer supported. To pull this value, use the AccountPreferencesService.")
    val marketingOptIn: Boolean,
    val darkMode: Boolean,
    val smartMeterInterest: Boolean = false,
    val referralsEnabled: Boolean = false,
    val energySourceVote: EnergySource? = null,
    val lastBannerShownAt: LocalDateTime? = null,
    val createdAt: LocalDateTime? = null,
    val deleted: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null,
    val isEligibleForMyNewAccount: Boolean = false,
    val evType: EvType? = null,
    val intendToBuyEv: Boolean? = null,
    val evTariffMarketingConsent: Boolean? = null,
) {

    companion object {

        fun fromCreateCustomerEnrolmentRequest(
            request: CreateCustomerEnrolmentRequest,
        ) = CustomerSetting(
            marketingOptIn = request.marketingOptedIn,
            referralsEnabled = true,
            smartMeterInterest = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            darkMode = false,
        )

        fun fromCustomerSettingsEntity(request: CustomerSettingsEntity) = CustomerSetting(
            marketingOptIn = if (request.marketingOptIn.hasValue()) request.marketingOptIn.value else true,
            referralsEnabled = true,
            smartMeterInterest = false,
            darkMode = false,
        )
    }
}

enum class EvType {
    HYBRID_ELECTRIC,
    ELECTRICITY,
    NO_EV
}

enum class EnergySource {
    HYDRO,
    WIND,
    BIOMASS,
    SOLAR,
}