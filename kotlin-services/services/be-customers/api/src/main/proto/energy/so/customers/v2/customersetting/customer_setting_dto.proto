syntax = "proto3";
package energy.so.customers.customersetting.v2;
option java_multiple_files = true;

import "google/protobuf/timestamp.proto";

enum EnergySource {
  HYDRO = 0;
  WIND = 1;
  BIOMASS = 2;
  SOLAR = 3;
}

enum PreferenceType {
  MARKETING_OPT_IN = 0;
  REFERRALS = 1;
  SMART_METER_INTEREST = 2;
  ENERGY_SOURCE_VOTE = 3;
}

enum EvType {
  HYBRID_ELECTRIC = 0;
  ELECTRICITY = 1;
  NO_EV = 2;
}

message PatchCustomerSettingRequest {
  int64 customerId = 1;
  optional EnergySource energySource = 2;
  // To update the customer marketing opt in, use AccountPreferences.UpdateAccountPreferences
  optional bool marketingOptIn = 3 [deprecated = true];
  optional bool smartMeterInterest = 4;
  optional bool referralsEnabled = 5;
  optional bool darkMode = 6;
  optional EvType evType = 7;
  optional bool intendToBuyEv = 8;
  optional bool evTariffMarketingConsent = 9;
}

message CustomerSetting {
  optional EnergySource energySource = 2;
  // To retrieve the customer marketing opt in, use AccountPreferences.GetAccountPreferences
  bool marketingOptIn = 3 [deprecated = true];
  bool smartMeterInterest = 4;
  bool referralsEnabled = 5;
  optional google.protobuf.Timestamp lastBannerShownAt = 6;
  bool darkMode = 7;
  bool isEligibleForMyNewAccount = 8;
  optional EvType evType = 9;
  optional bool intendToBuyEv = 10;
  optional bool evTariffMarketingConsent = 11;
}

message PreferenceCheckedLog {
  int64 customerId = 1;
  PreferenceType type = 2;
}

message PreferenceLogRequest {
  PreferenceCheckedLog log = 1;
}

message LastLogDateResponse {
  google.protobuf.Timestamp lastLogDate = 1;
}

message LastLogDateRequest {
  PreferenceCheckedLog log = 1;
}
