plugins {
    id("energy.so.be-commons-conventions-kotlin")
    id("energy.so.conventions.sonarqube")
    id("energy.so.conventions.jooq")
}

dependencies {
    implementation("org.springframework:spring-tx:5.3.9")
    implementation(project(":be-ac-junifer-api"))
    implementation(project(":be-identity-api"))
    implementation(project(":be-assets-api"))
    implementation(project(":be-communications-api"))
    implementation(project(":be-customers-api"))
    implementation(project(":be-products-api"))
    implementation(project(":be-financials-api"))
    implementation(project(":be-commons-logging"))
    implementation(project(":be-commons-json"))
    implementation("com.google.cloud:google-cloud-secretmanager:2.62.0")

    implementation(libs.ktor.serialization.kotlinx)
    implementation(libs.opentelemetry.annotations)

    testFixturesImplementation(project(":be-commons-database"))
    testFixturesImplementation(project(":be-assets-api"))
    testFixturesImplementation(project(":be-communications-api"))
    testFixturesImplementation(project(":be-customers-api"))
    testFixturesImplementation(project(":be-products-api"))
    testFixturesImplementation(project(":be-financials-api"))
    testFixturesImplementation("com.google.cloud:google-cloud-secretmanager:2.62.0")
}

tasks.named<energy.so.jooq.GenerateJooqTask>("generateJooq") {
    schemaName = "broker"
}