package energy.so.core.identity.server.controllers.dtos

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class RefreshTokenDtoTest {

    @Test
    fun `instance is created with the right parameters`() {
        val dto = RefreshTokenDto(refreshToken = REFRESH_TOKEN)

        assertThat(dto.refreshToken).isEqualTo(REFRESH_TOKEN)
    }

    companion object {
        const val REFRESH_TOKEN = "refresh token"
    }
}
