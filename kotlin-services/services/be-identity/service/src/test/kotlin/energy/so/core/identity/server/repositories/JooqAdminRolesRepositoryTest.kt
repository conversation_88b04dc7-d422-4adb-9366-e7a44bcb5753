package energy.so.core.identity.server.repositories

import energy.so.commons.extension.save
import energy.so.commons.model.tables.pojos.AdminRole
import energy.so.commons.model.tables.references.ADMIN_ROLES
import energy.so.commons.model.tables.references.USERS
import energy.so.core.identity.server.database.repositories.JooqAdminRolesRepository
import energy.so.core.identity.server.fixtures.PreCannedData.user
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe

class JooqAdminRolesRepositoryTest : ShouldSpec({
    val db = installDatabase()
    val subject = JooqAdminRolesRepository(db)

    context("::isUserAdmin") {
        should("return true given existing entry in admin_roles table for userId") {
            //GIVEN
            val userId = 123L
            db.newRecord(USERS, user.copy(id = userId)).apply { save() }
            db.newRecord(
                ADMIN_ROLES, AdminRole(
                    id = 1,
                    userId = userId,
                    rolename = "ADMIN"
                )
            ).apply { save() }

            //WHEN
            val response = subject.isUserAdmin(userId)

            //THEN
            response shouldBe true
        }

        should("return false given existing user but is not in admin_roles table") {
            //GIVEN
            val userId = 987L
            db.newRecord(USERS, user.copy(id = userId)).apply { save() }

            //WHEN
            val response = subject.isUserAdmin(userId)

            //THEN
            response shouldBe false
        }

        should("return false given invalid userId") {
            //GIVEN
            val userId = 10101L

            //WHEN
            val response = subject.isUserAdmin(userId)

            //THEN
            response shouldBe false
        }
    }
})
