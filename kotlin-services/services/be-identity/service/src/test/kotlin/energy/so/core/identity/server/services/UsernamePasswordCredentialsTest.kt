package energy.so.core.identity.server.services

import energy.so.core.identity.server.models.UsernamePasswordCredentials
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

internal class UsernamePasswordCredentialsTest {

    @Test
    fun `instance should be created with the right values`() {
        val usernamePasswordCredentials = UsernamePasswordCredentials(username = USERNAME, password = PASSWORD)

        usernamePasswordCredentials.run {
            Assertions.assertThat(username).isEqualTo(USERNAME)
            Assertions.assertThat(password).isEqualTo(PASSWORD)
        }
    }

    companion object {
        const val USERNAME = "bart.simpson"
        const val PASSWORD = "superPass1234!"
    }
}
