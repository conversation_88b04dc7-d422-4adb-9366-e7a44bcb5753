package energy.so.core.identity.server.services

import energy.so.core.identity.server.database.repositories.FeatureRepository
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.featureList
import energy.so.core.identity.server.models.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.server.plugins.NotFoundException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import energy.so.commons.model.enums.FeatureName as JooqFeatureName

class FeatureServiceTest : BehaviorSpec({
    val featureRepository = mockk<FeatureRepository>()
    val sut = FeatureService(featureRepository)

    given("::getAllFeatures") {
        and("features saved in db") {
            coEvery { featureRepository.getAllPermanentFeatures() } returns featureList

            `when`("getAllFeatures called") {
                val result = sut.getAllFeatures()

                then("list of features is returned") {
                    coVerify { featureRepository.getAllPermanentFeatures() }
                    result shouldBe featureList
                }
            }
        }

        and("no features saved in db") {
            coEvery { featureRepository.getAllPermanentFeatures() } returns emptyList()

            `when`("getAllFeatures called") {
                val result = sut.getAllFeatures()

                then("empty list is returned") {
                    coVerify { featureRepository.getAllPermanentFeatures() }
                    result shouldBe emptyList()
                }
            }
        }
    }

    given("::getFeature") {
        and("feature saved in db") {
            coEvery { featureRepository.getByName(JooqFeatureName.ENROLMENTS) } returns enrolmentFeature

            `when`("getFeature called") {
                val result = sut.getFeature(FeatureName.ENROLMENTS)

                then("correct feature is returned") {
                    coVerify { featureRepository.getByName(JooqFeatureName.ENROLMENTS) }
                    result shouldBe enrolmentFeature
                }
            }
        }

        and("feature not saved in db") {
            coEvery { featureRepository.getByName(JooqFeatureName.ENROLMENTS) } returns null

            `when`("getFeature called") {
                val result = sut.getFeature(FeatureName.ENROLMENTS)

                then("null is returned") {
                    coVerify { featureRepository.getByName(JooqFeatureName.ENROLMENTS) }
                    result shouldBe null
                }
            }
        }
    }

    given("::setFeature") {
        and("update successful") {
            coEvery { featureRepository.setFeature(JooqFeatureName.ENROLMENTS, true) } returns enrolmentFeature

            `when`("setFeature called") {
                val result = sut.setFeature(FeatureName.ENROLMENTS, true)
                then("correct feature is returned") {
                    coVerify { featureRepository.setFeature(JooqFeatureName.ENROLMENTS, true) }
                    result shouldBe enrolmentFeature
                }
            }
        }

        and("feature not saved in db") {
            coEvery { featureRepository.setFeature(JooqFeatureName.ENROLMENTS, true) } throws NotFoundException("not found")

            `when`("setFeature called") {
                shouldThrow<NotFoundException> {  sut.setFeature(FeatureName.ENROLMENTS, true) }
                then("throws") {
                    coVerify { featureRepository.setFeature(JooqFeatureName.ENROLMENTS, true) }
                }
            }
        }
    }
})
