package energy.so.core.identity.server.services.tokens

import java.time.ZonedDateTime
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/** <AUTHOR> 26/07/2022
 */
class TokenTest {

    @Test
    fun `given token when map to refresh token then return expected values`() {
        val userId = 12345L
        val sut = Token(
            userId = "12345",
            accessToken = "accessToken",
            refreshToken = "refreshToken",
            expiresAt = ZonedDateTime.now(),
            refreshTokenExpiresAt = ZonedDateTime.now(),
            groupId = UUID.randomUUID(),
            username = "<EMAIL>"
        )
        val refreshToken = sut.toRefreshToken()
        assertThat(refreshToken.groupId).isEqualTo(sut.groupId)
        assertThat(refreshToken.expiryDatetime).isEqualTo(sut.refreshTokenExpiresAt.toLocalDateTime())
        assertThat(refreshToken.cryptedToken).isEqualTo(sut.refreshToken)
        assertThat(refreshToken.userId).isEqualTo(userId)

    }
}