package energy.so.core.identity.server.consumers

import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.core.identity.server.fixtures.InMemoryUserRepository
import energy.so.core.identity.server.fixtures.PreCannedData.alicesUser
import energy.so.core.identity.server.fixtures.PreCannedData.alicesWebsiteUser
import energy.so.core.identity.server.fixtures.PreCannedData.frozenLocalDateTime
import energy.so.core.identity.server.mappers.toUser
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.datatest.withData
import io.kotest.extensions.clock.TestClock
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.maps.shouldBeEmpty
import java.time.ZoneId
import java.time.ZoneOffset
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class WebsiteUserLinkConsumerTest : ShouldSpec({

    val clock = TestClock(frozenLocalDateTime.toInstant(ZoneOffset.UTC), ZoneId.of("UTC"))
    lateinit var userRepository: InMemoryUserRepository
    lateinit var sut: WebsiteUserLinkConsumer

    beforeTest {
        userRepository = InMemoryUserRepository(clock = clock)
        sut = WebsiteUserLinkConsumer(
            userRepository = userRepository,
            projectName = "random project name",
            config = SubscriptionConfiguration(
                name = "random subscription name",
                topic = "random topic",
                key = "key", topicKey = "topicKey"
            ),
        )
    }

    context("::processMessage") {
        context("EventType is CREATED, UPDATED") inner@{
            withData(
                EventType.CREATED,
                EventType.CREATED,
            ) { eventType ->
                <EMAIL>("user with given website id does not exist, create a new user") {
                    // GIVEN
                    val publishedMessage = QueueMessage(
                        data = Json.encodeToString(alicesWebsiteUser),
                        eventType = eventType,
                    )

                    // WHEN
                    sut.processMessage(message = publishedMessage)

                    // THEN
                    userRepository.users.values.shouldContainExactly(
                        alicesWebsiteUser.toUser().copy(
                            id = userRepository.users.keys.maxOrNull(),
                            createdAt = frozenLocalDateTime,
                        )
                    )
                }

                <EMAIL>("user with given website id exists, update the user") {
                    // GIVEN
                    val alicesUserInService = userRepository.save(alicesUser)

                    val alicesAlteredWebsiteUser = alicesWebsiteUser.copy(
                        firstName = "Alicia",
                        lastName = "Smith",
                        encryptedPassword = "Alice changed her password but it's still plaintext!",
                    )

                    val expectedAlicesUserInService = with(alicesAlteredWebsiteUser) {
                        alicesUserInService.copy(
                            id = userRepository.users.keys.maxOrNull(),
                            createdAt = frozenLocalDateTime,
                            firstName = firstName,
                            lastName = lastName,
                            encryptedPassword = encryptedPassword,
                        )
                    }

                    val publishedMessage = QueueMessage(
                        data = Json.encodeToString(alicesAlteredWebsiteUser),
                        eventType = eventType,
                    )

                    // WHEN
                    sut.processMessage(message = publishedMessage)

                    // THEN
                    userRepository.users.values.shouldContainExactly(expectedAlicesUserInService)
                }
            }
        }
        context("Event type is DELETED") {
            should("not do anything") {
                // GIVEN
                val publishedMessage = QueueMessage(
                    data = Json.encodeToString(alicesWebsiteUser),
                    eventType = EventType.DELETED,
                )

                // WHEN
                sut.processMessage(publishedMessage)

                // THEN
                userRepository.users.shouldBeEmpty()
            }
        }
    }
})
