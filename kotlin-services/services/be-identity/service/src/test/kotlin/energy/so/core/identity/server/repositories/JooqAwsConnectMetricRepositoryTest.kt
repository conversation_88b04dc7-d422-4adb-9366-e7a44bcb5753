package energy.so.core.identity.server.repositories

import energy.so.commons.extension.save
import energy.so.commons.model.tables.references.AWS_CONNECT_METRICS
import energy.so.core.identity.server.database.repositories.JooqAwsConnectMetricsRepository
import energy.so.core.identity.server.fixtures.AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS
import energy.so.core.identity.server.fixtures.AWS_CONNECT_METRIC_JSON
import energy.so.core.identity.server.fixtures.connectMetricJooq
import energy.so.core.identity.server.fixtures.secondConnectMetricJooq
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqAwsConnectMetricRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val subject = JooqAwsConnectMetricsRepository(db)

    given("::save") {
        `when`("save called") {
            val result = subject.save(AWS_CONNECT_METRIC_JSON)

            then("record is in db") {
                val recordCount = db.selectFrom(AWS_CONNECT_METRICS)
                    .where(
                        AWS_CONNECT_METRICS.ID.eq(result.id)
                    ).count()

                recordCount shouldBe 1
            }
        }
    }

    given("::deleteOldestRecord") {
        and("metrics saved in db") {
            db.newRecord(AWS_CONNECT_METRICS, connectMetricJooq).apply { save() }
            db.newRecord(AWS_CONNECT_METRICS, secondConnectMetricJooq).apply { save() }

            `when`("deleteOldestRecord called") {
                subject.deleteOldRecords(AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS)

                then("only one record present in db") {
                    val correctRecordsPresent = db.selectFrom(AWS_CONNECT_METRICS)
                        .where(
                            AWS_CONNECT_METRICS.ID.eq(secondConnectMetricJooq.id)
                        )
                        .count()
                    val deletedRecordCount = db.selectFrom(AWS_CONNECT_METRICS)
                        .where(
                            AWS_CONNECT_METRICS.ID.eq(connectMetricJooq.id)
                        )
                        .count()
                    val allRecordsCount = db.selectFrom(AWS_CONNECT_METRICS)
                        .count()

                    correctRecordsPresent shouldBe 1
                    deletedRecordCount shouldBe 0
                    allRecordsCount shouldBe 1
                }
            }
        }

        and("no metrics saved in db") {
            `when`("deleteOldestRecord called") {
                subject.deleteOldRecords(AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS)

                then("no changes are made") {
                    val recordCount = db.selectFrom(AWS_CONNECT_METRICS)
                        .count()
                    recordCount shouldBe 0
                }
            }
        }
    }
})
