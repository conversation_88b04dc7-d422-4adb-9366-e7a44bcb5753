package energy.so.core.identity.server.controllers.grpc

import energy.so.core.identity.server.fixtures.PreCannedData.PASSWORD
import energy.so.core.identity.server.fixtures.PreCannedData.adminOneTimePasswordRequest
import energy.so.core.identity.server.services.AdminService
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.mockk

class AdminGrpcControllerTest : BehaviorSpec({

    val adminService = mockk<AdminService>()
    val sut = AdminGrpcController(adminService)

    given("admin one time password request") {

        and("call to generate one time password ok") {
            coEvery { adminService.generateOneTimePassword(adminOneTimePasswordRequest) } returns PASSWORD

            `when`("generate one time password") {
                val result = sut.generateOneTimePassword(adminOneTimePasswordRequest)

                then("password generated") {
                    result.password shouldBe PASSWORD
                }
            }
        }
    }

})
