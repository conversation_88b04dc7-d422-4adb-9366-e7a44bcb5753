package energy.so.core.identity.server.services.tokens

import energy.so.core.identity.server.database.repositories.RefreshTokensRepository
import energy.so.core.identity.server.fixtures.USER_ID
import energy.so.core.identity.server.fixtures.config
import energy.so.core.identity.server.fixtures.jwtDataForToken
import energy.so.core.identity.server.fixtures.verifier
import energy.so.core.identity.server.models.Feature
import energy.so.core.identity.server.models.FeatureName
import energy.so.core.identity.server.services.FeatureService
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.longs.shouldBeInRange
import io.kotest.matchers.longs.shouldNotBeInRange
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime.now

class JWTTokenServiceBehaviourSpecTest : BehaviorSpec({

    val sut = JWTTokenService(
        config = config,
        verifier = verifier,
        refreshTokensRepository = mockk<RefreshTokensRepository>(),
        featureService = mockk<FeatureService>(){
            every { getFeature(FeatureName.TMP_SO_22592_EXTENDED_APP_REFRESH_TOKEN) } returns Feature(
                id = 1L,
                name = FeatureName.TMP_SO_22592_EXTENDED_APP_REFRESH_TOKEN,
                enabled = true
            )
        }
    )

    given("::generateToken") {
        When("shortLived is true and host is null") {
            Then("refresh token expires shortRefreshTtlInSeconds after now") {
                val jwtDataToken = jwtDataForToken
                val token = sut.generateToken(jwtDataToken, true)

                verifyJwtDataToken(token.accessToken, jwtDataToken)
                token.userId shouldBe USER_ID
                val ttl = now().plusSeconds(config.shortRefreshTtlInSeconds)
                token.refreshTokenExpiresAt.toEpochSecond() shouldBeInRange  (ttl.minusSeconds(10).toEpochSecond())..(ttl.plusSeconds(10).toEpochSecond())
            }
        }

        When("shortLived is false and host is null") {
            Then("refresh token expiry is refreshTtl hours from now") {
                val jwtDataToken = jwtDataForToken
                val token = sut.generateToken(jwtDataToken, false)

                verifyJwtDataToken(token.accessToken, jwtDataToken)
                token.userId shouldBe USER_ID
                val ttl = now().plusHours(config.refreshTtlInHours)
                token.refreshTokenExpiresAt.toEpochSecond() shouldBeInRange (ttl.minusSeconds(10).toEpochSecond())..(ttl.plusSeconds(10).toEpochSecond())
            }
        }

        When("shortLived is false and host is not app") {
            Then("refresh token expires shortRefreshTtlInSeconds after now") {
                val jwtDataToken = jwtDataForToken
                val token = sut.generateToken(jwtDataToken, false, origin = "some.other.host")

                verifyJwtDataToken(token.accessToken, jwtDataToken)
                token.userId shouldBe USER_ID
                val ttl = now().plusMonths(config.appRefreshTtlInMonths)
                token.refreshTokenExpiresAt.toEpochSecond() shouldNotBeInRange (ttl.minusSeconds(10).toEpochSecond())..(ttl.plusSeconds(10).toEpochSecond())
            }
        }

        When("shortLived is false and host is app") {
            Then("refresh token expires in 6 months ") {
                val jwtDataToken = jwtDataForToken
                val token = sut.generateToken(jwtDataToken, false, origin = "capacitor://app.so.energy")

                verifyJwtDataToken(token.accessToken, jwtDataToken)
                token.userId shouldBe USER_ID
                val ttl = now().plusMonths(config.appRefreshTtlInMonths)
                token.refreshTokenExpiresAt.toEpochSecond() shouldBeInRange (ttl.minusSeconds(10).toEpochSecond())..(ttl.plusSeconds(10).toEpochSecond())
            }
        }
    }
})