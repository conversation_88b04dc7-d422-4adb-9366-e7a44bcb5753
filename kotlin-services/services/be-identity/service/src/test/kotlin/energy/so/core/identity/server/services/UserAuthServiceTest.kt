package energy.so.core.identity.server.services

import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.core.identity.server.commons.exceptions.AccountLockedException
import energy.so.core.identity.server.database.repositories.RefreshTokensRepository
import energy.so.core.identity.server.services.tokens.JWTTokenService
import energy.so.core.identity.server.utils.AuthUtils.loginMetadata
import energy.so.core.identity.server.utils.AuthUtils.sampleToken
import energy.so.core.identity.server.utils.AuthUtils.sampleUser
import energy.so.core.identity.server.utils.AuthUtils.sampleUserPrincipal
import energy.so.core.identity.server.utils.AuthUtils.sampleUsernamePasswordCredentials
import io.kotest.assertions.throwables.shouldThrow
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class UserAuthServiceTest {

    private val userService = mockk<UserService>()
    private val tokenService = mockk<JWTTokenService>()
    private val userPrincipalService = mockk<UserPrincipalService>()
    private val adminService = mockk<AdminService>()
    private val refreshTokenRepository = mockk<RefreshTokensRepository>(relaxed = true)

    private val userAuthService = UserAuthService(
        userService = userService,
        tokenService = tokenService,
        userPrincipalService = userPrincipalService,
        refreshTokenRepository = refreshTokenRepository,
        adminService = adminService
    )

    @BeforeEach
    fun beforeEach() {
        every { refreshTokenRepository.create(any()) } returnsArgument 0
    }

    @Test
    fun `when User is authenticated should return JWT token`() = runBlocking {
        coEvery { adminService.impersonateUser(any()) } returns null
        coEvery { userService.getVerifiedUser(any(), any()) } returns sampleUser
        every { tokenService.generateToken(any()) } returns sampleToken
        coEvery { userPrincipalService.getUserPrincipal(any()) } returns sampleUserPrincipal

        val token = userAuthService.authenticate(sampleUsernamePasswordCredentials, loginMetadata)

        coVerify { userPrincipalService.getUserPrincipal(sampleUser) }
        verify { tokenService.generateToken(sampleUserPrincipal) }
        coVerify { userService.getVerifiedUser(sampleUsernamePasswordCredentials, loginMetadata) }
        assertEquals(sampleToken, token)
    }

    @Test
    fun `when User is impersonated should return JWT token`() = runBlocking {
        coEvery { adminService.impersonateUser(any()) } returns sampleUser
        every { tokenService.generateToken(any()) } returns sampleToken
        coEvery { userPrincipalService.getUserPrincipal(any()) } returns sampleUserPrincipal

        val token = userAuthService.authenticate(sampleUsernamePasswordCredentials, loginMetadata)

        coVerify { userPrincipalService.getUserPrincipal(sampleUser) }
        verify { tokenService.generateToken(sampleUserPrincipal) }
        coVerify { adminService.impersonateUser(sampleUsernamePasswordCredentials) }
        assertEquals(sampleToken, token)
    }

    @Test
    fun `when user account is locked should throw FailedPreconditionGrpcException`() = runBlocking {
        coEvery { adminService.impersonateUser(any()) } returns null
        coEvery { userService.getVerifiedUser(any(), any()) } throws AccountLockedException()

        shouldThrow<FailedPreconditionGrpcException> {
            userAuthService.authenticate(
                sampleUsernamePasswordCredentials,
                loginMetadata
            )
        }

        coVerify { userService.getVerifiedUser(sampleUsernamePasswordCredentials, loginMetadata) }
    }

}
