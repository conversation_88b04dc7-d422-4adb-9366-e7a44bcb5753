package energy.so.core.identity.server.services

import energy.so.commons.model.tables.pojos.AdminPass
import energy.so.core.identity.server.commons.exceptions.UnauthorisedHttpException
import energy.so.core.identity.server.config.OneTimePasswordConfig
import energy.so.core.identity.server.database.repositories.AdminPassesRepository
import energy.so.core.identity.server.database.repositories.UserRepository
import energy.so.core.identity.server.fixtures.PreCannedData.adminOneTimePasswordRequest
import energy.so.core.identity.server.fixtures.PreCannedData.adminUpdateUserPasswordRequest
import energy.so.core.identity.server.models.PasswordConfig
import energy.so.core.identity.server.models.UsernamePasswordCredentials
import energy.so.core.identity.server.services.UsernamePasswordCredentialsTest.Companion.PASSWORD
import energy.so.core.identity.server.services.UsernamePasswordCredentialsTest.Companion.USERNAME
import energy.so.core.identity.server.utils.AuthUtils.sampleUser
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDateTime
import org.junit.jupiter.api.assertThrows

class AdminServiceTest : BehaviorSpec({

    val adminPassesRepository = mockk<AdminPassesRepository>()
    val userRepository = mockk<UserRepository>()

    val adminService = AdminService(
        adminPassesRepository = adminPassesRepository,
        userRepository = userRepository,
        passwordConfig = PasswordConfig(24L, "www.test.com", "passwordResetTemplate"),
        oneTimePasswordConfig = OneTimePasswordConfig(expireLimitMinutes = 5)
    )

    given("one time password request") {

        and("admin pass saved") {
            coEvery { adminPassesRepository.save(any()) } returns AdminPass()

            `when`("generate one time password") {
                val password = adminService.generateOneTimePassword(adminOneTimePasswordRequest)

                then("password generated") {
                    password shouldNotBe null
                }
            }
        }
    }

    given("UsernamePasswordCredentials") {
        val credentials = UsernamePasswordCredentials(
            username = USERNAME,
            password = PASSWORD
        )

        and("user by email exists") {
            every { userRepository.findByEmail(any()) } returns sampleUser

            and("exists not expired admin pass") {
                coEvery {
                    adminPassesRepository.findByUserIdAndTokenNotProcessed(
                        any(),
                        any(),
                    )
                } returns AdminPass(createdAt = LocalDateTime.now().minusMinutes(1))
                coEvery { adminPassesRepository.markAsProcessed(any()) } returns 1

                `when`("impersonate user") {
                    val result = adminService.impersonateUser(credentials)

                    then("user retrieved") {
                        result shouldBe sampleUser
                    }
                }
            }

            and("exists expired admin pass") {
                coEvery {
                    adminPassesRepository.findByUserIdAndTokenNotProcessed(
                        any(),
                        any(),
                    )
                } returns AdminPass(createdAt = LocalDateTime.now().minusMinutes(10))
                coEvery { adminPassesRepository.markAsProcessed(any()) } returns 1

                `when`("impersonate user") {

                    then("user retrieved") {
                        assertThrows<UnauthorisedHttpException> {
                            adminService.impersonateUser(
                                UsernamePasswordCredentials(
                                    username = USERNAME,
                                    password = PASSWORD
                                )
                            )
                        }
                    }
                }
            }

            and("no admin pass") {
                coEvery {
                    adminPassesRepository.findByUserIdAndTokenNotProcessed(
                        any(),
                        any(),
                    )
                } returns null

                `when`("impersonate user") {
                    val result = adminService.impersonateUser(
                        UsernamePasswordCredentials(
                            username = USERNAME,
                            password = PASSWORD
                        )
                    )

                    then("result is null") {
                        result shouldBe null
                    }
                }
            }
        }

        and("user with email does not exist") {
            every { userRepository.findByEmail(any()) } returns null

            `when`("impersonate user") {

                then("exception is thrown") {
                    assertThrows<UnauthorisedHttpException> {
                        adminService.impersonateUser(
                            UsernamePasswordCredentials(
                                username = USERNAME,
                                password = PASSWORD
                            )
                        )
                    }
                }
            }
        }
    }

    given("admin update user password request") {

        and("password updated in user table") {
            coJustRun { userRepository.setPassword(any(), any()) }

            `when`("update user password") {
                adminService.updateUserPassword(adminUpdateUserPasswordRequest)

                then("password updated") {
                    coVerify { userRepository.setPassword(any(), any()) }
                }
            }
        }
    }
})
