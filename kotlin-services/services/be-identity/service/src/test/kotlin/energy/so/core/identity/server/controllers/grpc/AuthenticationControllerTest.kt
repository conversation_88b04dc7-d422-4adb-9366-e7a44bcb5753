package energy.so.core.identity.server.controllers.grpc

import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnauthenticatedGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.grpc.utils.toZonedDateTime
import energy.so.commons.security.UserPrincipal
import energy.so.core.identity.server.commons.exceptions.UnauthorisedHttpException
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.authTokenRequest
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.lastSignInAt
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.loginMetaData
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.loginMetaDataWithOrigin
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.refreshTokenRequest
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.tokenResponse
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.userAuthenticationRequest
import energy.so.core.identity.server.fixtures.PreCannedData.user
import energy.so.core.identity.server.models.LoginMetadata
import energy.so.core.identity.server.models.UsernamePasswordCredentials
import energy.so.core.identity.server.services.UserAuthService
import energy.so.core.identity.server.services.UserService
import energy.so.core.identity.server.services.tokens.NotValidJWTException
import energy.so.core.identity.server.services.tokens.Token
import energy.so.core.identity.server.services.tokens.TokenService
import energy.so.users.v2.copy
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.server.auth.jwt.JWTPrincipal
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.ZonedDateTime
import java.util.UUID
import org.junit.jupiter.api.assertThrows

class AuthenticationControllerTest : BehaviorSpec({
    val userAuthService = mockk<UserAuthService>()
    val tokenService = mockk<TokenService<UserPrincipal, JWTPrincipal>>()
    val userService = mockk<UserService>()
    val authenticationController = AuthenticationController(userAuthService, tokenService, userService)
    val invalidMessage = "not valid"

    afterEach {
        confirmVerified(tokenService, userAuthService)
        clearMocks(tokenService, userAuthService)
    }

    given("::authenticateUser") {
        and("valid token") {
            val token = Token(
                userId = "userId",
                accessToken = "dummyAccessToken",
                refreshToken = "dummyRefreshToken",
                expiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                refreshTokenExpiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                groupId = UUID.randomUUID(),
                username = userAuthenticationRequest.username,
                lastLoginAt = lastSignInAt.toTimestamp().toZonedDateTime()

            )
            coEvery {
                userAuthService.authenticate(
                    UsernamePasswordCredentials(
                        userAuthenticationRequest.username,
                        userAuthenticationRequest.password
                    ),
                    LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                    userAuthenticationRequest.shortLivedRefreshToken
                )
            } returns token

            coEvery { userService.getUserByEmail(userAuthenticationRequest.username) } returns user.copy(
                lastSignInAt = lastSignInAt
            )

            `when`("call authenticateUser") {
                val authenticateUser = authenticationController.authenticateUser(userAuthenticationRequest)

                then("should to authenticateUser") {
                    assertSoftly {
                        authenticateUser shouldBe tokenResponse.copy {
                            lastLoginAt = lastSignInAt.toNullableTimestamp()
                        }
                        coVerify {
                            userAuthService.authenticate(
                                UsernamePasswordCredentials(
                                    userAuthenticationRequest.username,
                                    userAuthenticationRequest.password
                                ),
                                LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                                userAuthenticationRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
        and("valid token with app origin") {
            val token = Token(
                userId = "userId",
                accessToken = "dummyAccessToken",
                refreshToken = "dummyRefreshToken",
                expiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                refreshTokenExpiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                groupId = UUID.randomUUID(),
                username = userAuthenticationRequest.username,
                lastLoginAt = lastSignInAt.toTimestamp().toZonedDateTime()

            )
            coEvery {
                userAuthService.authenticate(
                    UsernamePasswordCredentials(
                        userAuthenticationRequest.username,
                        userAuthenticationRequest.password
                    ),
                    LoginMetadata(
                        loginMetaDataWithOrigin.sourceIp,
                        loginMetaDataWithOrigin.userAgent,
                        loginMetaDataWithOrigin.origin
                    ),
                    userAuthenticationRequest.shortLivedRefreshToken
                )
            } returns token

            coEvery { userService.getUserByEmail(userAuthenticationRequest.username) } returns user.copy(
                lastSignInAt = lastSignInAt
            )

            `when`("call authenticateUser") {
                val authenticateUser = authenticationController.authenticateUser(userAuthenticationRequest.copy {
                    this.loginMetadata = loginMetaDataWithOrigin
                })

                then("should to authenticateUser") {
                    assertSoftly {
                        authenticateUser shouldBe tokenResponse.copy {
                            lastLoginAt = lastSignInAt.toNullableTimestamp()
                        }
                        coVerify {
                            userAuthService.authenticate(
                                UsernamePasswordCredentials(
                                    userAuthenticationRequest.username,
                                    userAuthenticationRequest.password
                                ),
                                LoginMetadata(
                                    loginMetaDataWithOrigin.sourceIp,
                                    loginMetaDataWithOrigin.userAgent,
                                    loginMetaDataWithOrigin.origin
                                ),
                                userAuthenticationRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
        and("unauthorized auth") {
            coEvery {
                userAuthService.authenticate(
                    UsernamePasswordCredentials(
                        userAuthenticationRequest.username,
                        userAuthenticationRequest.password
                    ),
                    LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                    userAuthenticationRequest.shortLivedRefreshToken
                )
            } throws UnauthorisedHttpException()

            `when`("call authenticateUser") {
                then("unauthorisedHttpException thrown") {
                    assertSoftly {
                        shouldThrow<UnauthenticatedGrpcException> {
                            authenticationController.authenticateUser(userAuthenticationRequest)
                        }
                        coVerify {
                            userAuthService.authenticate(
                                UsernamePasswordCredentials(
                                    userAuthenticationRequest.username,
                                    userAuthenticationRequest.password
                                ),
                                LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                                userAuthenticationRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
        and("invalid auth") {
            coEvery {
                userAuthService.authenticate(
                    UsernamePasswordCredentials(
                        userAuthenticationRequest.username,
                        userAuthenticationRequest.password
                    ),
                    LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                    userAuthenticationRequest.shortLivedRefreshToken
                )
            } throws Exception()

            `when`("call authenticateUser") {
                then("UnknownGrpcException thrown") {
                    assertSoftly {
                        shouldThrow<UnknownGrpcException> {
                            authenticationController.authenticateUser(userAuthenticationRequest)
                        }
                        coVerify {
                            userAuthService.authenticate(
                                UsernamePasswordCredentials(
                                    userAuthenticationRequest.username,
                                    userAuthenticationRequest.password
                                ),
                                LoginMetadata(loginMetaData.sourceIp, loginMetaData.userAgent),
                                userAuthenticationRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
    }

    given("::refreshToken") {
        and("valid token") {
            val token = Token(
                userId = "userId",
                accessToken = "dummyAccessToken",
                refreshToken = "dummyRefreshToken",
                expiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                refreshTokenExpiresAt = ZonedDateTime.parse("2023-03-25T01:10:44.392Z"),
                groupId = UUID.randomUUID(),
                username = userAuthenticationRequest.username
            )
            coEvery {
                tokenService.refreshToken(
                    "dummyRefreshToken",
                    refreshTokenRequest.shortLivedRefreshToken
                )
            } returns token

            `when`("call refreshToken") {
                val authenticateUser = authenticationController.refreshToken(refreshTokenRequest)

                then("token refreshed") {
                    assertSoftly {
                        authenticateUser shouldBe tokenResponse
                        coVerify {
                            tokenService.refreshToken(
                                "dummyRefreshToken",
                                refreshTokenRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
        and("not authorized") {
            coEvery {
                tokenService.refreshToken(
                    "dummyRefreshToken",
                    refreshTokenRequest.shortLivedRefreshToken
                )
            } throws UnauthorisedHttpException()

            `when`("call refreshToken") {
                then("UnauthenticatedGrpcException thrown") {
                    assertSoftly {
                        shouldThrow<UnauthenticatedGrpcException> {
                            authenticationController.refreshToken(refreshTokenRequest)
                        }
                        coVerify {
                            tokenService.refreshToken(
                                "dummyRefreshToken",
                                refreshTokenRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
        and("unknown exception from tokenService") {
            coEvery {
                tokenService.refreshToken(
                    "dummyRefreshToken",
                    refreshTokenRequest.shortLivedRefreshToken
                )
            } throws Exception()

            `when`("call refreshToken") {
                then("UnknownGrpcException thrown") {
                    assertSoftly {
                        shouldThrow<UnknownGrpcException> {
                            authenticationController.refreshToken(refreshTokenRequest)
                        }
                        coVerify {
                            tokenService.refreshToken(
                                "dummyRefreshToken",
                                refreshTokenRequest.shortLivedRefreshToken
                            )
                        }
                    }
                }
            }
        }
    }

    given("::isTokenValid") {
        and("valid token") {
            coEvery { tokenService.isTokenValid(authTokenRequest.token) } returns true

            `when`("call isTokenValid") {
                val response = authenticationController.isTokenValid(authTokenRequest)

                then("return valid") {
                    assertSoftly {
                        response.valid shouldBe true
                        coVerify { tokenService.isTokenValid(authTokenRequest.token) }
                    }
                }
            }
        }

        and("invalid token") {
            coEvery { tokenService.isTokenValid(authTokenRequest.token) } returns false

            `when`("call isTokenValid") {
                val response = authenticationController.isTokenValid(authTokenRequest)

                then("return invalid") {
                    assertSoftly {
                        response.valid shouldBe false
                        coVerify { tokenService.isTokenValid(authTokenRequest.token) }
                    }
                }
            }
        }
    }

    given("::invalidateRefreshToken") {
        and("refresh token is deleted") {
            coEvery { tokenService.invalidateRefreshToken(authTokenRequest.token) } returns true

            `when`("call invalidateRefreshToken") {
                val response = authenticationController.invalidateRefreshToken(authTokenRequest)

                then("return success") {
                    assertSoftly {
                        response.valid shouldBe true
                        coVerify { tokenService.invalidateRefreshToken(authTokenRequest.token) }
                    }
                }
            }
        }

        and("no refresh token in db") {
            coEvery { tokenService.invalidateRefreshToken(authTokenRequest.token) } returns false

            `when`("call invalidateRefreshToken") {
                val response = authenticationController.invalidateRefreshToken(authTokenRequest)

                then("return success") {
                    assertSoftly {
                        response.valid shouldBe false
                        coVerify { tokenService.invalidateRefreshToken(authTokenRequest.token) }
                    }
                }
            }
        }

        and("error thrown by the service") {
            coEvery { tokenService.invalidateRefreshToken(authTokenRequest.token) } throws
                    NotValidJWTException(invalidMessage)

            `when`("call invalidateRefreshToken") {
                val response = assertThrows<InvalidArgumentGrpcException> {
                    authenticationController.invalidateRefreshToken(authTokenRequest)
                }

                then("throw InvalidArgumentGrpcException") {
                    assertSoftly {
                        response.localizedMessage shouldBe invalidMessage
                        coVerify { tokenService.invalidateRefreshToken(authTokenRequest.token) }
                    }
                }
            }
        }
    }
})
