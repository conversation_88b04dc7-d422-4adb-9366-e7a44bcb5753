package energy.so.core.identity.server.controllers

import com.google.protobuf.Empty
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.PasswordResetGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.core.identity.server.commons.exceptions.PasswordResetException
import energy.so.core.identity.server.controllers.dtos.TokenErrorCode
import energy.so.core.identity.server.dtos.PasswordResetRequestDto.Companion.toPasswordRequestDto
import energy.so.core.identity.server.dtos.ResetPasswordResponseDto
import energy.so.core.identity.server.exceptions.InvalidPasswordResetRequestException
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordInvalidTokenResponse
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordInvalidTokenResponseDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordRequest
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordRequestDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordResponseDto
import energy.so.core.identity.server.fixtures.PreCannedData.ID_1
import energy.so.core.identity.server.services.PasswordResetService
import energy.so.users.v2.passwordResetByEmailRequest
import energy.so.users.v2.passwordResetRequest
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk

class PasswordResetControllerTest : BehaviorSpec({
    val passwordResetService = mockk<PasswordResetService>()
    val sut = PasswordResetController(passwordResetService)

    given("::generateResetPasswordTokenAndSendThirdPartyInstructions") {
        and("the email exists") {
            val request = passwordResetByEmailRequest {
                email = "<EMAIL>"
            }
            coJustRun { passwordResetService.generatePasswordResetTokenAndSendThirdPartyInstructions(request.email) }
            `when`("generateResetPasswordTokenAndSendThirdPartyInstructions is called") {
                val result = sut.generateResetPasswordTokenAndSendThirdPartyInstructions(request)
                then("no exception is thrown") {
                    result shouldBe Empty.getDefaultInstance()
                }
            }
        }
        and("the email does not exist") {
            val request = passwordResetByEmailRequest {
                email = "<EMAIL>"
            }
            coEvery { passwordResetService.generatePasswordResetTokenAndSendThirdPartyInstructions(request.email) } throws EntityNotFoundException(
                "No user found with this email"
            )
            `when`("generateResetPasswordTokenAndSendThirdPartyInstructions is called") {
                then("EntityNotFoundException is thrown") {
                    shouldThrow<EntityNotFoundGrpcException> {
                        sut.generateResetPasswordTokenAndSendThirdPartyInstructions(
                            request
                        )
                    }
                }
            }
        }
    }

    given("::resetPassword") {
        val oldPassword = "abcdefG1"
        val newPassword = "abcdefG2"
        val request = passwordResetRequest {
            this.oldPassword = oldPassword.toNullableString()
            this.newPassword = newPassword.toNullableString()
            userId = ID_1.toNullableInt64()
        }

        and("the user exists") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } returns
                    ResetPasswordResponseDto(success = true)
            `when`("resetPassword is called") {
                val result = sut.resetPassword(request)
                then("success response is returned") {
                    result.success shouldBe true
                }
            }
        }

        and("request validation fails") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } throws
                    InvalidPasswordResetRequestException("Invalid email")

            `when`("resetPassword is called") {
                then("InvalidArgumentGrpcException is thrown") {
                    shouldThrow<InvalidArgumentGrpcException> {
                        sut.resetPassword(request)
                    }
                }
            }
        }

        and("the user does not exist") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } throws
                    EntityNotFoundException("No user found")

            `when`("resetPassword is called") {
                then("EntityNotFoundGrpcException is thrown") {
                    shouldThrow<EntityNotFoundGrpcException> {
                        sut.resetPassword(request)
                    }
                }
            }
        }

        and("fail to set new password") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } throws
                    BadRequestException(message = "Failed to set new user password")

            `when`("resetPassword is called") {
                then("InvalidArgumentGrpcException is thrown") {
                    shouldThrow<InvalidArgumentGrpcException> {
                        sut.resetPassword(request)
                    }
                }
            }
        }

        and("fail to reset password") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } throws
                    UnknownGrpcException(errorCategory = "999", errorCode = "2", message = "error")

            `when`("resetPassword is called") {
                then("UnknownGrpcException is thrown") {
                    shouldThrow<UnknownGrpcException> {
                        sut.resetPassword(request)
                    }
                }
            }
        }

        and("fail to reset password - token expired") {
            coEvery { passwordResetService.resetPassword(request.toPasswordRequestDto()) } throws
                    PasswordResetException(TokenErrorCode.TOKEN_EXPIRED, "")

            `when`("resetPassword is called") {
                then("PasswordResetGrpcException is thrown") {
                    shouldThrow<PasswordResetGrpcException> {
                        sut.resetPassword(request)
                    }
                    coVerify { passwordResetService.resetPassword(request.toPasswordRequestDto()) }
                }
            }
        }
    }

    given("::setNewPassword") {
        val newPasswordRequestDto = setNewPasswordRequestDto

        and("token is valid") {
            coEvery { passwordResetService.setNewPassword(newPasswordRequestDto) } returns setNewPasswordResponseDto

            `when`("setNewPassword is called") {
                val result = sut.setNewPassword(setNewPasswordRequest)

                then("no exception is thrown and response is successful") {
                    result.success shouldBe true
                }
            }
        }

        and("token is invalid") {
            coEvery { passwordResetService.setNewPassword(newPasswordRequestDto) } returns setNewPasswordInvalidTokenResponseDto

            `when`("setNewPassword is called") {
                val result = sut.setNewPassword(setNewPasswordRequest)

                then("no exception is thrown and response is unsuccessful") {
                    result shouldBe setNewPasswordInvalidTokenResponse
                }
            }
        }

        and("fail to set new password") {
            coEvery { passwordResetService.setNewPassword(newPasswordRequestDto) } throws
                    BadRequestException(message = "Failed to set new user password")

            `when`("resetPassword is called") {
                then("InvalidArgumentGrpcException is thrown") {
                    shouldThrow<InvalidArgumentGrpcException> {
                        sut.setNewPassword(setNewPasswordRequest)
                    }
                }
            }
        }
    }
})
