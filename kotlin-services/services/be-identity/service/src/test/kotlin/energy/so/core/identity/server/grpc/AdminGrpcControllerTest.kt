package energy.so.core.identity.server.grpc

import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.core.identity.server.controllers.grpc.AdminGrpcController
import energy.so.core.identity.server.fixtures.PreCannedData.adminUpdateUserPasswordRequest
import energy.so.core.identity.server.services.AdminService
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk

class AdminGrpcControllerTest : BehaviorSpec({

    val adminService = mockk<AdminService>()

    val adminController = AdminGrpcController(
        adminService = adminService,
    )

    given("admin update user password request") {

        and("update password on service ok") {
            coJustRun { adminService.updateUserPassword(any()) }

            `when`("update user password") {
                adminController.updateUserPassword(adminUpdateUserPasswordRequest)

                then("password updated") {
                    coVerify { adminService.updateUserPassword(any()) }
                }
            }
        }

        and("update password on service fails") {
            coEvery { adminService.updateUserPassword(any()) } throws Exception("error")

            `when`("update user password") {

                then("UnknownGrpcException") {
                    shouldThrow<UnknownGrpcException> {
                        adminController.updateUserPassword(adminUpdateUserPasswordRequest)

                    }
                    coVerify { adminService.updateUserPassword(any()) }
                }
            }
        }
    }
})