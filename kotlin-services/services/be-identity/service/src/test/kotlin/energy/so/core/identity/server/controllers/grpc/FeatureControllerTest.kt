package energy.so.core.identity.server.controllers.grpc

import com.google.protobuf.Empty
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.disabledEnrolmentFeatureProto
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.emptyFeatureListResponse
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeatureNameRequest
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeatureProto
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.featureList
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.getAllFeaturesResponse
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.setEnrolmentFeatureRequest
import energy.so.core.identity.server.models.FeatureName
import energy.so.core.identity.server.services.FeatureService
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.server.plugins.NotFoundException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk

class FeatureControllerTest : BehaviorSpec({
    val featureService = mockk<FeatureService>()
    val sut = FeatureController(featureService)

    given("::getAllFeatures") {
        and("feature list successfully returned from service") {
            coEvery { featureService.getAllFeatures() } returns featureList

            `when`("getAllFeatures called") {
                val result = sut.getAllFeatures(Empty.getDefaultInstance())

                then("result should be correctly returned") {
                    coVerify { featureService.getAllFeatures() }
                    result shouldBe getAllFeaturesResponse
                }
            }
        }

        and("empty feature list returned from service") {
            coEvery { featureService.getAllFeatures() } returns emptyList()

            `when`("getAllFeatures called") {
                val result = sut.getAllFeatures(Empty.getDefaultInstance())

                then("empty response is returned") {
                    coVerify { featureService.getAllFeatures() }
                    result shouldBe emptyFeatureListResponse
                }
            }
        }
    }

    given("::getFeature") {
        and("feature successfully returned from service") {
            coEvery { featureService.getFeature(FeatureName.ENROLMENTS) } returns enrolmentFeature

            `when`("getFeature called") {
                val result = sut.getFeature(enrolmentFeatureNameRequest)

                then("result should be correctly returned") {
                    coVerify { featureService.getFeature(FeatureName.ENROLMENTS) }
                    result shouldBe enrolmentFeatureProto
                }
            }
        }

        and("null returned from service") {
            coEvery { featureService.getFeature(FeatureName.ENROLMENTS) } returns null

            `when`("getFeature called") {
                val result = sut.getFeature(enrolmentFeatureNameRequest)

                then("response should be correctly returned") {
                    coVerify { featureService.getFeature(FeatureName.ENROLMENTS) }
                    result shouldBe disabledEnrolmentFeatureProto
                }
            }
        }
    }

    given("::setFeature") {
        and("feature successfully updated") {
            coEvery { featureService.setFeature(FeatureName.ENROLMENTS, true) } returns enrolmentFeature

            `when`("setFeature called") {
                val result = sut.setFeature(setEnrolmentFeatureRequest)

                then("result should be correctly returned") {
                    coVerify { featureService.getFeature(FeatureName.ENROLMENTS) }
                    result shouldBe enrolmentFeatureProto
                }
            }
        }

        and("service throws") {
            coEvery { featureService.setFeature(FeatureName.ENROLMENTS, true) } throws NotFoundException("did not find it")

            `when`("setFeature called") {
                shouldThrow<NotFoundException> {   sut.setFeature(setEnrolmentFeatureRequest) }
                then("controller throws") {
                    coVerify { featureService.setFeature(FeatureName.ENROLMENTS, true) }
                }
            }
        }
    }
})
