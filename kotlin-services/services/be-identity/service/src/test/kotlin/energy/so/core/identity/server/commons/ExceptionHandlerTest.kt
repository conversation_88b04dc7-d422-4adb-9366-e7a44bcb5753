package energy.so.core.identity.server.commons

import energy.so.core.identity.server.commons.exceptions.HttpException
import energy.so.core.identity.server.services.tokens.NotValidJWTException
import io.ktor.http.HttpStatusCode.Companion.BadRequest
import io.ktor.http.HttpStatusCode.Companion.InternalServerError
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class ExceptionHandlerTest {

    @Test
    fun `handle HttpException`() = runBlocking<Unit> {
        val errorDto = slot<ErrorDto>()
        val httpException = object : HttpException(BadRequest, ERROR_MESSAGE) {}
        val call = mockk<ApplicationCall>(relaxed = true)

        ExceptionHandler.handle(httpException, call)

        coVerify { call.respond(httpException.status, capture(errorDto)) }

        errorDto.captured.run {
            assertThat(statusCode).isEqualTo(BadRequest.value)
            assertThat(status).isEqualTo(BadRequest.description)
            assertThat(message).isEqualTo(ERROR_MESSAGE)
        }
    }

    @Test
    fun `handle JWTExceptions`() = runBlocking<Unit> {
        val errorDto = slot<ErrorDto>()
        val jwtException = NotValidJWTException(ERROR_MESSAGE)
        val call = mockk<ApplicationCall>(relaxed = true)

        ExceptionHandler.handle(jwtException, call)

        coVerify { call.respond(BadRequest, capture(errorDto)) }

        errorDto.captured.run {
            assertThat(statusCode).isEqualTo(BadRequest.value)
            assertThat(status).isEqualTo(BadRequest.description)
            assertThat(message).isEqualTo(ERROR_MESSAGE)
        }
    }

    @Test
    fun `unhandled exceptions are thrown as internal errors`() = runBlocking<Unit> {
        val errorDto = slot<ErrorDto>()
        val exception = Exception(ERROR_MESSAGE)
        val call = mockk<ApplicationCall>(relaxed = true)

        ExceptionHandler.handle(exception, call)

        coVerify { call.respond(InternalServerError, capture(errorDto)) }

        errorDto.captured.run {
            assertThat(statusCode).isEqualTo(InternalServerError.value)
            assertThat(status).isEqualTo(InternalServerError.description)
            assertThat(message).isEqualTo(ERROR_MESSAGE)
        }
    }

    @Test
    fun `unhandled exceptions with no message are thrown as internal errors with default message`() =
        runBlocking<Unit> {
            val errorDto = slot<ErrorDto>()
            val exception = Exception()
            val call = mockk<ApplicationCall>(relaxed = true)

            ExceptionHandler.handle(exception, call)

            coVerify { call.respond(InternalServerError, capture(errorDto)) }

            errorDto.captured.run {
                assertThat(statusCode).isEqualTo(InternalServerError.value)
                assertThat(status).isEqualTo(InternalServerError.description)
                assertThat(message).isEqualTo("An error occurred")
            }
        }

    companion object {
        const val ERROR_MESSAGE = "An error occurred"
    }
}
