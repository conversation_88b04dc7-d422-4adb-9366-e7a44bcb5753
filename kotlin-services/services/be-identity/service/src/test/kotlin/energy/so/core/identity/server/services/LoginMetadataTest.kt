package energy.so.core.identity.server.services

import energy.so.core.identity.server.models.LoginMetadata
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class LoginMetadataTest {

    @Test
    fun `instance should be created with the right values`() {
        val loginMetadata = LoginMetadata(sourceIp = SOURCE_IP, userAgent = USER_AGENT)

        loginMetadata.run {
            assertThat(sourceIp).isEqualTo(SOURCE_IP)
            assertThat(userAgent).isEqualTo(USER_AGENT)
        }
    }

    @Test
    fun `instance should be created with the right null values`() {
        val loginMetadata = LoginMetadata()

        loginMetadata.run {
            assertThat(sourceIp).isNull()
            assertThat(userAgent).isNull()
        }
    }

    companion object {
        const val SOURCE_IP = "************"
        const val USER_AGENT = "Mozilla user agent"
    }
}
