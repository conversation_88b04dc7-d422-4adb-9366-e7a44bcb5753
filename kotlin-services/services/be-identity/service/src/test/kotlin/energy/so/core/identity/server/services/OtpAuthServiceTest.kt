package energy.so.core.identity.server.services

import energy.so.commons.security.crypto.hashSha256
import energy.so.core.identity.server.commons.exceptions.UnauthorisedHttpException
import energy.so.core.identity.server.database.repositories.AdminPassesRepository
import energy.so.core.identity.server.database.repositories.RefreshTokensRepository
import energy.so.core.identity.server.database.repositories.UserRepository
import energy.so.core.identity.server.services.tokens.JWTTokenService
import energy.so.core.identity.server.utils.AuthUtils.SAMPLE_EMAIL
import energy.so.core.identity.server.utils.AuthUtils.SAMPLE_TOKEN
import energy.so.core.identity.server.utils.AuthUtils.adminPass
import energy.so.core.identity.server.utils.AuthUtils.loginMetadata
import energy.so.core.identity.server.utils.AuthUtils.sampleEmailTokenCredentials
import energy.so.core.identity.server.utils.AuthUtils.sampleToken
import energy.so.core.identity.server.utils.AuthUtils.sampleUser
import energy.so.core.identity.server.utils.AuthUtils.sampleUserPrincipal
import io.kotest.common.runBlocking
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

/** <AUTHOR> 14/07/2022
 */
internal class OtpAuthServiceTest {

    private val tokenService = mockk<JWTTokenService>()
    private val userRepository = mockk<UserRepository>()
    private val adminPassesRepository = mockk<AdminPassesRepository>()
    private val userPrincipalService = mockk<UserPrincipalService>()
    private val refreshTokenRepository = mockk<RefreshTokensRepository>(relaxed = true)

    private val sut = OtpAuthService(
        tokenService = tokenService,
        userRepository = userRepository,
        adminPassesRepository = adminPassesRepository,
        userPrincipalService = userPrincipalService,
        refreshTokenRepository = refreshTokenRepository
    )

    @BeforeEach
    fun beforeEach() {
        every { refreshTokenRepository.create(any()) } returnsArgument 0
    }

    @Test
    fun `when no user matching email, throw exception`() = runBlocking {
        // GIVEN
        every { userRepository.findByEmail(any()) } returns null

        // WHEN
        org.junit.jupiter.api.assertThrows<UnauthorisedHttpException> {
            sut.authenticate(sampleEmailTokenCredentials, loginMetadata)
        }

        // THEN
        verify { userRepository.findByEmail(SAMPLE_EMAIL) }
    }

    @Test
    fun `when no admin matching userId and token, throw exception`() = runBlocking {
        // GIVEN
        every { userRepository.findByEmail(any()) } returns sampleUser
        every { adminPassesRepository.findByUserIdAndToken(any(), any()) } returns null

        // WHEN
        org.junit.jupiter.api.assertThrows<UnauthorisedHttpException> {
            sut.authenticate(sampleEmailTokenCredentials, loginMetadata)
        }

        // THEN
        verify { userRepository.findByEmail(SAMPLE_EMAIL) }
        verify { adminPassesRepository.findByUserIdAndToken(sampleUser.id!!, hashSha256(SAMPLE_TOKEN)) }
    }

    @Test
    fun `when admin pass found, return token`() = runBlocking {
        // GIVEN
        every { userRepository.findByEmail(any()) } returns sampleUser
        every { adminPassesRepository.findByUserIdAndToken(any(), any()) } returns adminPass
        every { tokenService.generateToken(any()) } returns sampleToken
        coEvery { userPrincipalService.getUserPrincipal(any()) } returns sampleUserPrincipal

        // WHEN
        val result = sut.authenticate(
            sampleEmailTokenCredentials,
            loginMetadata
        )

        // THEN
        assertThat(result).isEqualTo(sampleToken)
        verify { userRepository.findByEmail(SAMPLE_EMAIL) }
        verify { adminPassesRepository.findByUserIdAndToken(sampleUser.id!!, hashSha256(SAMPLE_TOKEN)) }
    }

}