package energy.so.core.identity.server.mappers

import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeatureProto
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentJooqFeature
import energy.so.core.identity.server.models.Feature
import energy.so.core.identity.server.models.toProto
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class FeatureMapperTest : BehaviorSpec({

    given("jooq feature") {
        val jooqFeature = enrolmentJooqFeature

        `when`("Feature::fromJooq") {
            val result = Feature.fromJooq(jooqFeature)

            then("feature is correctly mapped") {
                result shouldBe enrolmentFeature
            }
        }
    }

    given("feature model") {
        val feature = enrolmentFeature

        `when`("Feature::toProto") {
            val result = feature.toProto()

            then("proto should be correctly mapped") {
                result shouldBe enrolmentFeatureProto
            }
        }
    }
})
