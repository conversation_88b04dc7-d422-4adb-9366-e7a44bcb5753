package energy.so.core.identity.server.controllers

import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.JuniferGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.core.identity.server.commons.server.TestHttpServerApplication
import energy.so.core.identity.server.controllers.dtos.AuthenticateMeterReadingDto
import energy.so.core.identity.server.services.meterReading.MeterReadingAuthenticationService
import energy.so.core.identity.server.services.meterReading.MeterReadingTokenService
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions

class AccountControllerTest : ShouldSpec({
    lateinit var testApplication: TestHttpServerApplication

    val mockMeterReadingTokenService = mockk<MeterReadingTokenService>()
    val mockAuthService = mockk<MeterReadingAuthenticationService>()
    val juniferAccNum: Long = 123
    val token = "token"

    val accountController = AccountController(
        authService = mockAuthService,
        tokenService = mockMeterReadingTokenService,
    )

    beforeTest {
        clearAllMocks()
        testApplication = TestHttpServerApplication { accountController(this) }
    }

    context("::authenticateMeterReadingAccount") {
        should("be able to authenticate account for meter reading submission and provide access token") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery { mockMeterReadingTokenService.generateToken(juniferAccNum) } returns token
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } returns true
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    Assertions.assertThat(status).isEqualTo(HttpStatusCode.OK)
                }

                // THEN
                coVerify { mockMeterReadingTokenService.generateToken(juniferAccNum) }
            }
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery { mockMeterReadingTokenService.generateToken(juniferAccNum) } returns token
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } returns true
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    Assertions.assertThat(status).isEqualTo(HttpStatusCode.OK)
                }

                // THEN
                coVerify { mockMeterReadingTokenService.generateToken(juniferAccNum) }
            }
        }
        should("return BadRequest when IllegalArgumentException occur") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } throws IllegalArgumentException()
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.BadRequest
                    bodyAsText() shouldContain "[IllegalArgumentException]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }

        should("return Unauthorized when user is not authorized") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } returns false
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.Unauthorized
                    bodyAsText() shouldContain "[Unauthorized]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }

        should("return BadRequest when JuniferGrpcException occurs") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } throws JuniferGrpcException()
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.BadRequest
                    bodyAsText() shouldContain "[JuniferGrpcException]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }

        should("return BadRequest when EntityNotFoundException") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } throws EntityNotFoundGrpcException("Not found")
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.BadRequest
                    bodyAsText() shouldContain "[EntityNotFoundGrpcException]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }

        should("return BadRequest when GrpcException") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } throws UnknownGrpcException("test", "test", "test")
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.BadRequest
                    bodyAsText() shouldContain "[GrpcException]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }

        should("return InternalServerError when other exception") {
            // GIVEN
            testApplication.runTest { client ->
                val authenticateMeterReadingDto = AuthenticateMeterReadingDto(
                    phoneNumber = "**********",
                    email = "<EMAIL>",
                    postcodeNumbers = null,
                    directDebitAmount = null,
                )
                coEvery {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                } throws Exception("test")
                val requestBody =
                    """
                        {
                        "phoneNumber" : "**********",
                        "email" : "<EMAIL>"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/authenticate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    status shouldBe HttpStatusCode.InternalServerError
                    bodyAsText() shouldContain "[Exception]"
                }

                // THEN
                coVerify {
                    mockAuthService.authenticateAccount(
                        juniferAccNum.toString(),
                        authenticateMeterReadingDto
                    )
                }
            }
        }
    }
    context("::validate") {
        should("be able to validate a meter reading submission token - true") {
            //GIVEN
            testApplication.runTest { client ->
                coEvery { mockMeterReadingTokenService.isValidToken(123L, "hashedToken") } returns true

                val requestBody =
                    """
                        {
                            "token": "hashedToken"
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/validate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    Assertions.assertThat(status).isEqualTo(HttpStatusCode.OK)
                }

                // THEN
                coVerify { mockMeterReadingTokenService.isValidToken(123, "hashedToken") }
            }
        }
        should("be able to validate a meter reading submission token - false") {
            //GIVEN
            testApplication.runTest { client ->
                coEvery { mockMeterReadingTokenService.isValidToken(juniferAccNum, token) } returns false

                val requestBody =
                    """
                        {
                            "token": $token
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/validate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    this.status shouldBe HttpStatusCode.Forbidden
                    bodyAsText() shouldContain "[ForbiddenHttpException]"
                }

                // THEN
                coVerify { mockMeterReadingTokenService.isValidToken(juniferAccNum, token) }
            }
        }
        should("fail with BadRequest if IllegalArgumentException") {
            //GIVEN
            testApplication.runTest { client ->
                val requestBody =
                    """
                        {
                            "token": $token
                        }
                    """.trimIndent()

                // WHEN
                client.post("/junifer-account/invalid-account-number/meter/validate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    this.status shouldBe HttpStatusCode.BadRequest
                    bodyAsText() shouldContain "[IllegalArgumentException]"
                }

                // THEN
                coVerify(exactly = 0) { mockMeterReadingTokenService.isValidToken(juniferAccNum, token) }
            }
        }
        should("fail with InternalServerError if other exception") {
            //GIVEN
            testApplication.runTest { client ->
                val requestBody =
                    """
                        {
                            "token": $token
                        }
                    """.trimIndent()

                coEvery {
                    mockMeterReadingTokenService.isValidToken(
                        juniferAccNum,
                        token
                    )
                } throws Exception("exception")

                // WHEN
                client.post("/junifer-account/$juniferAccNum/meter/validate") {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(requestBody)
                }.apply {
                    this.status shouldBe HttpStatusCode.InternalServerError
                    bodyAsText() shouldContain "[Exception]"
                }

                // THEN
                coVerify { mockMeterReadingTokenService.isValidToken(juniferAccNum, token) }
            }
        }
    }
})

