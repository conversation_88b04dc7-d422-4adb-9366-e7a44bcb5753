package energy.so.core.identity.server.consumers

import energy.so.commons.queues.config.SubscriptionConfiguration
import energy.so.commons.queues.models.EventType
import energy.so.commons.queues.models.QueueMessage
import energy.so.core.identity.server.aws.AwsConnectConfig
import energy.so.core.identity.server.services.AwsConnectMetricService
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk

class AwsConnectMetricRefreshConsumerTest : BehaviorSpec({

    val awsConnectMetricService = mockk<AwsConnectMetricService>()
    val awsConnectConfig = mockk<AwsConnectConfig>()

    val sut = AwsConnectMetricRefreshConsumer(
        awsConnectMetricService = awsConnectMetricService,
        awsConnectConfig = awsConnectConfig,
        projectName = "test",
        config = SubscriptionConfiguration(
            name = "test",
            topic = "topic",
            key = "key",
            topicKey = "topicKey"
        )
    )

    given("message is passed to trigger aws connect metric refresh consumer") {
        coJustRun { awsConnectMetricService.saveConnectMetrics() }
        every { awsConnectConfig.enabled } returns true
        every { awsConnectConfig.totalRunTime } returns 120L
        every { awsConnectConfig.timeBetweenCalls } returns 25L
        every { awsConnectConfig.numberOfRuns } returns 5

        `when`("process message") {
            sut.processMessage(
                QueueMessage(
                    data = "abc",
                    eventType = EventType.CREATED
                )
            )

            then("AwsConnectMetricRefreshConsumer") {
                coVerify(exactly = 5) { awsConnectMetricService.saveConnectMetrics() }
            }
        }
    }
})
