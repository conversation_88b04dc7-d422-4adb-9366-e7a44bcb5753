package energy.so.core.identity.server.repositories

import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.model.tables.pojos.PasswordResetToken
import energy.so.commons.model.tables.pojos.User
import energy.so.commons.model.tables.references.PASSWORD_RESET_TOKENS
import energy.so.commons.model.tables.references.USERS
import energy.so.commons.security.crypto.hashSha256
import energy.so.core.identity.server.commons.exceptions.PasswordResetException
import energy.so.core.identity.server.controllers.dtos.TokenErrorCode
import energy.so.core.identity.server.database.repositories.JooqUserRepository
import energy.so.core.identity.server.dtos.UpdateUserDto
import energy.so.core.identity.server.fixtures.PreCannedData.ID_1
import energy.so.core.identity.server.fixtures.PreCannedData.ID_2
import energy.so.core.identity.server.fixtures.PreCannedData.PASSWORD
import energy.so.core.identity.server.fixtures.PreCannedData.TEST_STRING
import energy.so.core.identity.server.fixtures.PreCannedData.alicesExternalId
import energy.so.core.identity.server.fixtures.PreCannedData.alicesUser
import energy.so.core.identity.server.fixtures.PreCannedData.alicesWebsiteUser
import energy.so.core.identity.server.fixtures.PreCannedData.frozenLocalDateTime
import energy.so.database.test.installDatabase
import energy.so.users.v2.copy
import energy.so.users.v2.patchUserRequest
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.time.LocalDateTime
import org.jooq.exception.DataAccessException
import kotlin.random.Random.Default.nextLong

internal class JooqUserRepositoryTest : ShouldSpec({
    val db = installDatabase()
    val subject = JooqUserRepository(db)
    val userId = nextLong()
    var user = User(email = "<EMAIL>", encryptedPassword = "egg", currentAccountId = 1L)
    val deletedUserId = userId + 1
    var deletedUser = User(
        email = "<EMAIL>",
        encryptedPassword = "egg",
        currentAccountId = 1L,
        deleted = frozenLocalDateTime
    )
    val patchUserRequest = patchUserRequest {
        id = userId
        currentAccountId = ID_1.toNullableInt64()
    }

    context("::findById") {
        context("given a user exists when find by id") {
            beforeTest {
                user = db.newRecord(USERS, user.copy(id = userId))
                    .apply { save() }.run { into(User()) }
            }
            should("then return expected") {
                val actual = subject.findById(userId)
                actual shouldBe user
            }
            should("then return null when id is not found") {
                val result = subject.findById(************)
                result shouldBe null
            }
        }
        context("given a deleted user exists when find by id") {
            beforeTest {
                db.newRecord(USERS, deletedUser.copy(id = deletedUserId))
                    .apply { save() }.run { into(User()) }
            }
            should("then return null when trying to find user") {
                val result = subject.findById(deletedUserId)
                result shouldBe null
            }
        }
    }
    context("::save") {
        should("given a user, save user to the database") {
            // GIVEN

            // WHEN
            val savedUser = subject.save(user)
            val userOnDb = subject.findById(savedUser.id!!)

            val userCopyWithDbGeneratedValues = user.copy(
                id = savedUser.id,
                createdAt = savedUser.createdAt,
                updatedAt = savedUser.updatedAt,
                currentAccountId = savedUser.currentAccountId
            )

            // THEN
            assertSoftly {
                savedUser shouldBe userOnDb
                userCopyWithDbGeneratedValues shouldBe userOnDb
            }
        }
    }

    context("::update user") {
        beforeTest {
            user = db.newRecord(USERS, user.copy(id = userId))
                .apply { save() }.run { into(User()) }
        }
        should("update user") {
            // GIVEN
            val updateUser = UpdateUserDto(
                id = userId,
                firstName = "fname",
                lastName = "lname",
                password = "pwd",
                email = "<EMAIL>"
            )

            // WHEN
            val response = subject.updateUser(updateUser)

            // THEN
            assertSoftly {
                response.id shouldBe userId
                response.firstName shouldBe "fname"
            }
        }
        should("fail as unknown user") {
            // GIVEN
            val updateUser = UpdateUserDto(
                id = userId + 1,
                firstName = "fname"
            )

            // WHEN / THEN
            shouldThrow<EntityNotFoundException> {
                subject.updateUser(updateUser)
            }
        }
    }

    context("::upsertWebsiteUser") {
        context("a user with the given (external) id exists") {
            should("update the user with the data from the given website user") {
                // GIVEN
                val savedAlicesUser = subject.save(alicesUser)

                val modifiedAlicesWebsiteUser = alicesWebsiteUser.copy(
                    firstName = "Alice's new first name",
                    lastName = "Alice's new last name",
                    encryptedPassword = "Alice has many times have we told you",
                    email = "<EMAIL>",
                )

                // WHEN
                val updatedUser = subject.upsertWebsiteUser(websiteUser = modifiedAlicesWebsiteUser)

                // THEN
                assertSoftly {
                    with(modifiedAlicesWebsiteUser) {
                        updatedUser.firstName shouldBe firstName
                        updatedUser.lastName shouldBe lastName
                        updatedUser.encryptedPassword shouldBe encryptedPassword
                        updatedUser.email shouldBe email
                    }
                    updatedUser shouldBe subject.findById(savedAlicesUser.id!!)
                }
            }
        }

        context("no user with the given (external) id exists") {
            should("create a new user with the data from the given website user") {
                // GIVEN / WHEN
                val savedUser = subject.upsertWebsiteUser(websiteUser = alicesWebsiteUser)

                // THEN
                assertSoftly {
                    with(alicesWebsiteUser) {
                        savedUser.firstName shouldBe firstName
                        savedUser.lastName shouldBe lastName
                        savedUser.email shouldBe email
                        savedUser.encryptedPassword shouldBe encryptedPassword
                        savedUser.externalId shouldBe id.toString()
                    }
                    savedUser shouldBe subject.findById(savedUser.id!!)
                }
            }
        }
    }

    context("::getUserByExternalId") {
        should("given an existing user with a matching external id when queried then return the user") {
            // GIVEN
            subject.save(alicesUser)

            // WHEN
            val returnedUser = subject.getUserByExternalId(alicesUser.externalId.toString())

            // THEN
            assertSoftly {
                returnedUser shouldNotBe null
                returnedUser!!.firstName shouldBe alicesUser.firstName
                returnedUser.lastName shouldBe alicesUser.lastName
                returnedUser.email shouldBe alicesUser.email
                returnedUser.encryptedPassword shouldBe alicesUser.encryptedPassword
                returnedUser.externalId shouldBe alicesUser.externalId.toString()
            }
        }

        should("given no existing user with no matching external id when queried then return null") {
            // GIVEN WHEN
            val returnedUser = subject.getUserByExternalId("this id does not exist!")

            // THEN
            returnedUser shouldBe null
        }

        should("given an existing deleted user with matching external id when queried then return null") {
            // GIVEN
            subject.save(deletedUser.copy(externalId = alicesExternalId))

            // WHEN
            val returnedUser = subject.getUserByExternalId(alicesExternalId)

            // THEN
            returnedUser shouldBe null
        }
    }

    context("::getUserByEmail") {
        should("given an existing user with a matching email when queried then return the user") {
            // GIVEN
            subject.save(alicesUser)

            // WHEN
            val returnedUser = subject.getUserByEmail(alicesUser.email!!)

            // THEN
            assertSoftly {
                returnedUser shouldNotBe null
                returnedUser!!.firstName shouldBe alicesUser.firstName
                returnedUser.lastName shouldBe alicesUser.lastName
                returnedUser.email shouldBe alicesUser.email
                returnedUser.encryptedPassword shouldBe alicesUser.encryptedPassword
                returnedUser.externalId shouldBe alicesUser.externalId.toString()
            }
        }

        should("given no existing user with no matching email when queried then return null") {
            // GIVEN WHEN
            val returnedUser = subject.getUserByEmail("this id does not exist!")

            // THEN
            returnedUser shouldBe null
        }
    }

    context("::markUserEmailAsOld") {
        should("given an existing user with a matching email when mark as old then updated") {
            // GIVEN
            val savedUser = subject.save(alicesUser)

            // WHEN
            subject.markUserEmailAsOld(savedUser.id!!)

            // THEN
            assertSoftly {
                val returnedUser = subject.findById(savedUser.id!!)
                returnedUser shouldNotBe null
                returnedUser?.email shouldBe "oldaccount" + savedUser.currentAccountId + alicesUser.email
            }
        }
    }

    context("::setPassword") {
        should("given an existing user when set password then updated") {
            // GIVEN
            val savedUser = subject.save(alicesUser)

            // WHEN
            subject.setPassword(savedUser.id!!, PASSWORD)

            // THEN
            assertSoftly {
                val returnedUser = subject.findById(savedUser.id!!)
                returnedUser shouldNotBe null
                returnedUser?.encryptedPassword shouldBe PASSWORD
            }
        }
    }

    context("::findByEmailAndEncryptedPassword") {
        context("a user with an email an password exists") {
            beforeTest {
                user = subject.save(user)
            }

            should("not appear in results when email doesn't match") {
                val result = subject.findByEmailAndEncryptedPassword("unknown", user.encryptedPassword!!)

                result shouldBe null
            }

            should("not appear in results when password doesn't match") {
                val result = subject.findByEmailAndEncryptedPassword(user.email!!, "unknown")

                result shouldBe null
            }

            should("appear in results when email and password match") {
                val result = subject.findByEmailAndEncryptedPassword(user.email!!, user.encryptedPassword!!)

                result shouldBe user
            }
        }
        context("a deleted user with an email an password exists") {
            beforeTest {
                deletedUser = subject.save(deletedUser)
            }
            should("not appear in results when email and password match but user is deleted") {
                val result =
                    subject.findByEmailAndEncryptedPassword(deletedUser.email!!, deletedUser.encryptedPassword!!)

                result shouldBe null
            }
        }
    }

    context("::findByEmail") {
        context("a user with an email exists") {
            beforeTest {
                user = subject.save(user)
            }

            should("not appear in results when email doesn't match") {
                val result = subject.findByEmail("unknown")

                result shouldBe null
            }

            should("appear in results when email does match") {
                val result = subject.findByEmail(user.email!!)

                result shouldBe user
            }
        }
        context("a deleted user with an email exists") {
            beforeTest {
                deletedUser = subject.save(deletedUser)
            }
            should("not appear in results when email does match but user is deleted") {
                val result = subject.findByEmail(deletedUser.email!!)

                result shouldBe null
            }
        }
    }

    context("::save") {
        should("persist the user") {
            user = subject.save(user)

            val result = db.selectFrom(USERS).fetchSingle().into(User::class.java)

            result shouldBe user
        }

        should("update the user if it already exists") {
            val newEmail = "<EMAIL>"
            user = subject.save(user)
            user = user.copy(email = newEmail)
            user = subject.save(user)

            val result = db.selectFrom(USERS).fetchSingle().into(User::class.java)

            assertSoftly {
                result shouldBe user
                result.email shouldBe newEmail
            }
        }
    }

    context("::savePasswordResetToken") {

        should("given a password reset token linked to a non-existent user when saved then throw an error") {
            // GIVEN WHEN THEN
            shouldThrow<DataAccessException> {
                subject.savePasswordResetToken(
                    PasswordResetToken(
                        userId = 999, // Does not exist on the DB, should violate foreign key constraint
                        token = "some hashed token".toByteArray(),
                        tokenExpiry = LocalDateTime.now(),
                    )
                )
            }
        }

        should("given a password reset token linked to a saved user when saved then return the saved user") {
            // GIVEN
            val savedUser = subject.save(user)

            // WHEN
            subject.savePasswordResetToken(
                PasswordResetToken(
                    userId = savedUser.id,
                    token = "some hashed token".toByteArray(),
                    tokenExpiry = LocalDateTime.now(),
                )
            )

            // THEN
            val recordCount = db.selectCount()
                .from(PASSWORD_RESET_TOKENS)
                .where(PASSWORD_RESET_TOKENS.USER_ID.eq(savedUser.id))
                .fetchOne(0, Int::class.java)

            recordCount shouldBe 1
        }
    }

    context("::changePassword") {

        val newPassword = hashSha256("password123")
        val savedToken = "some hashed token"
        var savedUser = user

        beforeTest {
            savedUser = subject.save(user)
            subject.savePasswordResetToken(
                PasswordResetToken(
                    userId = savedUser.id,
                    token = savedToken.toByteArray(),
                    tokenExpiry = LocalDateTime.now().plusHours(1),
                )
            )
        }

        should("given a valid reset token when the user resets their password then update the password") {
            // GIVEN WHEN
            subject.changePassword(savedToken, newPassword)

            // THEN
            var rowCount = db.selectCount()
                .from(USERS)
                .where(USERS.ENCRYPTED_PASSWORD.eq(newPassword))
                .fetchOne(0, Int::class.java)

            rowCount shouldBe 1

            rowCount = db.selectCount()
                .from(PASSWORD_RESET_TOKENS)
                .where(PASSWORD_RESET_TOKENS.TOKEN_CLAIMED_AT.isNull)
                .fetchOne(0, Int::class.java)

            // All reset tokens should be claimed
            rowCount shouldBe 0
        }

        should("given an invalid reset token when the user resets their password then throw an error") {
            // GIVEN WHEN
            val exception = shouldThrow<PasswordResetException> {
                subject.changePassword(
                    "this does not exist on the db!",
                    newPassword
                )
            }

            // THEN
            exception.errorCode shouldBe TokenErrorCode.NO_MATCHING_TOKEN
        }

        should("given an expired reset token when the user resets their password then throw an error") {
            // GIVEN
            val newToken = "token"

            // Save a new user and register an expired token
            val newUser = subject.save(user)
            subject.savePasswordResetToken(
                PasswordResetToken(
                    userId = newUser.id,
                    token = newToken.toByteArray(),
                    tokenExpiry = LocalDateTime.now().minusHours(1)
                )
            )

            // WHEN
            val exception = shouldThrow<PasswordResetException> { subject.changePassword(newToken, newPassword) }

            exception.errorCode shouldBe TokenErrorCode.TOKEN_EXPIRED
        }

        should("given a claimed token when claimed again then throw an error") {
            // GIVEN
            val secondPassword = hashSha256("a different password")

            // WHEN
            subject.changePassword(savedToken, newPassword)
            // Attempting to claim the token again should not work
            val exception = shouldThrow<PasswordResetException> { subject.changePassword(savedToken, secondPassword) }

            // THEN
            val rowCountFirst = db.selectCount()
                .from(USERS)
                .where(USERS.ENCRYPTED_PASSWORD.eq(newPassword))
                .fetchOne(0, Int::class.java)

            val rowCountSecond = db.selectCount()
                .from(USERS)
                .where(USERS.ENCRYPTED_PASSWORD.eq(secondPassword))
                .fetchOne(0, Int::class.java)

            assertSoftly {
                rowCountFirst shouldBe 1
                rowCountSecond shouldBe 0
                exception.errorCode shouldBe TokenErrorCode.NO_MATCHING_TOKEN
            }
        }

        should("given a claimed token when another pre-existing token is claimed then throw an error") {
            // GIVEN
            val token2 = "a different token"

            // We now have two unclaimed, valid tokens on the DB
            subject.savePasswordResetToken(
                PasswordResetToken(
                    userId = savedUser.id,
                    token = token2.toByteArray(),
                    tokenExpiry = LocalDateTime.now().plusHours(1),
                )
            )

            // WHEN
            subject.changePassword(savedToken, newPassword)

            // THEN
            val rowCount = db.selectCount()
                .from(PASSWORD_RESET_TOKENS)
                .where(PASSWORD_RESET_TOKENS.TOKEN.eq(token2.toByteArray()))
                .and(PASSWORD_RESET_TOKENS.TOKEN_CLAIMED_AT.isNull)
                .fetchOne(0, Int::class.java)

            // The second token should have its 'claimed_at' value set
            rowCount shouldBe 0
        }
    }

    context("::patchUser") {
        context("no user saved in db") {
            should("nothing happens when call patch user") {
                subject.patchUser(patchUserRequest)
            }
        }

        context("no user saved in db with corresponding id") {
            should("nothing happens, user should be the same when call patch user") {
                // GIVEN
                val save = subject.save(user)

                // WHEN
                subject.patchUser(patchUserRequest)

                //THEN
                subject.findById(user.id!!)?.copy(createdAt = save.createdAt, updatedAt = save.updatedAt) shouldBe save
            }
        }

        context("user saved in db with corresponding id") {
            context("no nullable data on request") {
                should("nothing happens, user should be the same when call patch user") {
                    // GIVEN
                    val save = subject.save(user)

                    // WHEN
                    subject.patchUser(patchUserRequest { id = userId })

                    // THEN
                    subject.findById(user.id!!) shouldBe save
                }
            }

            context("request has currentAccountId") {
                should("patch user when call patch user") {
                    // GIVEN
                    val save = subject.save(user)

                    // WHEN
                    subject.patchUser(patchUserRequest.copy { currentAccountId = ID_2.toNullableInt64() })

                    // THEN
                    subject.findById(user.id!!)
                        ?.copy(createdAt = save.createdAt, updatedAt = save.updatedAt) shouldBe save.copy(
                        currentAccountId = ID_2
                    )
                }
            }

            context("request has email") {
                should("patch user when call patch user") {
                    // GIVEN
                    val save = subject.save(user.copy(id = ID_1))

                    // WHEN
                    subject.patchUser(patchUserRequest {
                        id = ID_1
                        email = TEST_STRING.toNullableString()
                    })

                    // THEN
                    subject.findById(ID_1)?.copy(
                        createdAt = save.createdAt,
                        updatedAt = save.updatedAt
                    ) shouldBe save.copy(email = TEST_STRING)
                }
            }
        }
    }
})
