package energy.so.core.identity.server.services.meterReading

import energy.so.commons.model.tables.pojos.MeterReadingSubmissionToken
import energy.so.commons.security.crypto.hashSha256
import energy.so.commons.security.crypto.toBase64
import energy.so.core.identity.server.database.repositories.AccountRepository
import energy.so.core.identity.server.models.MeterReadingTokenConfig
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot

class HashedMeterReadingTokenServiceTest : ShouldSpec({
    val juniferId = 123L
    val mockRepository = mockk<AccountRepository>()
    val mockConfig = mockk<MeterReadingTokenConfig>()
    val tokenService = HashedMeterReadingTokenService(mockRepository, mockConfig)

    beforeTest {
        clearAllMocks()
    }

    context("::generateToken") {
        should("be able to generate token for given junifer accountId") {
            //GIVEN
            val meterReadingSubmissionTokenSlot = slot<MeterReadingSubmissionToken>()
            coEvery { mockConfig.keyLength } returns 64
            coEvery { mockConfig.iterations } returns 1000
            coEvery { mockConfig.expiryMins } returns 2
            coEvery { mockRepository.saveMeterReadingToken(any()) } returns Unit

            //WHEN
            val response = tokenService.generateToken(juniferId)

            //THEN
            assertSoftly {
                coVerify(exactly = 1) { mockRepository.saveMeterReadingToken(capture(meterReadingSubmissionTokenSlot)) }
                meterReadingSubmissionTokenSlot.captured.run {
                    juniferAccountId shouldBe juniferId
                }
                response shouldNotBe null
                response shouldNotBe ""
            }
        }
    }

    context("::isValidToken") {
        should("be able to validate token - Success") {
            //GIVEN
            val token =
                "MWQwYmFhNmI3MTM1ZGVjN2U4YjY2MzJmOTFmZmNlNGQ3OTU4MTc4MWI4NWMwZGM2MmU0MGEzMTg3MjZmN2U1ZTJhZWU3OT" +
                        "gxMDE5ZDFmNjcyZjFhZjA1NjY0MDFkNTY2NTY3OTdmYmU3NDdhNDRlOTcwYjJmMzRlNWNhZGI3ZmI="
            val hashedToken = hashSha256(token).toBase64()
            coEvery { mockRepository.isValidToken(juniferId, hashedToken) } returns true

            //WHEN
            val response = tokenService.isValidToken(juniferId, token)

            //THEN
            response shouldBe true

        }
        should("be able to validate token - Failure") {
            //GIVEN
            val token =
                "MWQwYmFhNmI3MTM1ZGVjN2U4YjY2MzJmOTFmZmNlNGQ3OTU4MTc4MWI4NWMwZGM2MmU0MGEzMTg3MjZmN2U1ZTJhZWU3OT" +
                        "gxMDE5ZDFmNjcyZjFhZjA1NjY0MDFkNTY2NTY3OTdmYmU3NDdhNDRlOTcwYjJmMzRlNWNhZGI3ZmI="
            val hashedToken = hashSha256(token).toBase64()
            coEvery { mockRepository.isValidToken(juniferId, hashedToken) } returns false

            //WHEN
            val response = tokenService.isValidToken(juniferId, token)

            //THEN
            response shouldBe false

        }
    }
})
