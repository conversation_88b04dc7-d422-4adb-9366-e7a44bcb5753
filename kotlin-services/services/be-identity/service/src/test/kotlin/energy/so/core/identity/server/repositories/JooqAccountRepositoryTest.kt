package energy.so.core.identity.server.repositories

import energy.so.commons.model.tables.references.METER_READING_SUBMISSION_TOKENS
import energy.so.core.identity.server.database.repositories.JooqAccountRepository
import energy.so.core.identity.server.fixtures.EXPIRED_METER_SUBMISSION_TOKEN
import energy.so.core.identity.server.fixtures.HASHED_TOKEN
import energy.so.core.identity.server.fixtures.INVALID_JUNIFER_ID
import energy.so.core.identity.server.fixtures.VALID_JUNIFER_ID
import energy.so.core.identity.server.fixtures.VALID_METER_SUBMISSION_TOKEN
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe

class JooqAccountRepositoryTest : ShouldSpec({
    val db = installDatabase()
    val subject = JooqAccountRepository(db)

    context("::saveMeterReadingToken") {
        should("be able to save meter reading submission access token") {
            //WHEN
            subject.saveMeterReadingToken(VALID_METER_SUBMISSION_TOKEN)

            //THEN
            val recordCount = db.selectCount()
                .from(METER_READING_SUBMISSION_TOKENS)
                .where(
                    METER_READING_SUBMISSION_TOKENS.JUNIFER_ACCOUNT_ID
                        .eq(VALID_METER_SUBMISSION_TOKEN.juniferAccountId)
                )
                .fetchOne(0, Int::class.java)

            recordCount shouldBe 1
        }
    }

    context("::isValidToken") {
        should("return true for valid token") {
            //GIVEN
            subject.saveMeterReadingToken(VALID_METER_SUBMISSION_TOKEN)

            //WHEN
            val response = subject.isValidToken(VALID_JUNIFER_ID, HASHED_TOKEN)

            //THEN
            response shouldBe true
        }

        should("return false for expired token") {
            //GIVEN
            subject.saveMeterReadingToken(EXPIRED_METER_SUBMISSION_TOKEN)

            //WHEN
            val response = subject.isValidToken(VALID_JUNIFER_ID, HASHED_TOKEN)

            //THEN
            response shouldBe false
        }

        should("return false for valid token but mismatched accountId") {
            //GIVEN
            subject.saveMeterReadingToken(VALID_METER_SUBMISSION_TOKEN)

            //WHEN
            val response = subject.isValidToken(INVALID_JUNIFER_ID, HASHED_TOKEN)

            //THEN
            response shouldBe false
        }


    }
})
