package energy.so.core.identity.server.controllers

import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.core.identity.server.commons.exceptions.PasswordResetException
import energy.so.core.identity.server.commons.exceptions.UnauthorisedHttpException
import energy.so.core.identity.server.commons.server.TestHttpServerApplication
import energy.so.core.identity.server.controllers.dtos.TokenErrorCode
import energy.so.core.identity.server.models.EmailTokenCredentials
import energy.so.core.identity.server.models.LoginMetadata
import energy.so.core.identity.server.services.OtpAuthService
import energy.so.core.identity.server.services.PasswordResetService
import energy.so.core.identity.server.services.UserAuthService
import energy.so.core.identity.server.services.tokens.Token
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode.Companion.BadRequest
import io.ktor.http.HttpStatusCode.Companion.NoContent
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.Unauthorized
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.time.ZonedDateTime
import java.time.ZonedDateTime.now
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat

class UserControllerKtTest : ShouldSpec({
    val mockAuthService = mockk<UserAuthService>()
    val mockOtpAuthService = mockk<OtpAuthService>()
    val mockPasswordResetService = mockk<PasswordResetService>()
    val userController = UserController(
        otpAuthService = mockOtpAuthService,
        passwordResetService = mockPasswordResetService,
    )
    val username = "<EMAIL>"
    val password = "mysupersecurepass"
    val body = """
        {
            "username": "<EMAIL>",
            "password": "mysupersecurepass",
            "shortLivedRefreshToken": false
        }
        """.trimIndent()
    lateinit var testApplication: TestHttpServerApplication
    val userAgent = "Mozilla user agent"
    val email = "<EMAIL>"
    val token = "token"
    val authenticateOtpDtoBody = """
        {
            "email": "$email",
            "token": "$token",
            "shortLivedRefreshToken": false
        }
        """.trimIndent()

    val resetPasswordDtoBody = """
        {
            "email": "$email"
        }
        """.trimIndent()

    val redeemPasswordTokenDtoBody = """
        {
            "passwordToken": "$token",
            "newPassword": "$password"
        }
        """.trimIndent()

    val expiredAt: ZonedDateTime = now()
    val refreshTokenExpiresAt: ZonedDateTime = now()

    val sampleToken = Token(
        userId = "12345",
        accessToken = "generated_access_token",
        refreshToken = "generated_refresh_token",
        expiresAt = expiredAt,
        refreshTokenExpiresAt = refreshTokenExpiresAt,
        groupId = UUID.randomUUID(),
        username = email
    )

    beforeTest {
        clearAllMocks()
        testApplication = TestHttpServerApplication { userController(this) }
    }

    context("/users/authenticate-otp") {
        should("authenticate-otp otp is valid") {
            //GIVEN
            val credentials = slot<EmailTokenCredentials>()
            val loginMetadataSlot = slot<LoginMetadata>()
            coEvery {
                mockOtpAuthService.authenticate(
                    capture(credentials), capture(loginMetadataSlot)
                )
            } returns sampleToken

            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/authenticate-otp"
                ) {
                    header("Content-Type", "application/json")
                    setBody(authenticateOtpDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe OK
                }
                credentials.captured.run {
                    assertThat(username).isEqualTo(username)
                    assertThat(password).isEqualTo(password)
                }
                loginMetadataSlot.captured.run {
                    assertThat(userAgent).isEqualTo(userAgent)
                }
            }
        }
        should("authenticate-otp otp is invalid") {
            //GIVEN
            coEvery {
                mockOtpAuthService.authenticate(
                    any(),
                    any(),
                    false,
                )
            } throws UnauthorisedHttpException("User unauthorised")

            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/authenticate-otp"
                ) {
                    header("Content-Type", "application/json")
                    setBody(authenticateOtpDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe Unauthorized
                }
            }
        }
    }
    context("/users/reset-password") {
        should("given a missing email when the password reset endpoint is called then return an error") {
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/reset-password"
                ) {
                    header("Content-Type", "application/json")
                    setBody("{}")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe BadRequest
                }
            }
        }
        should("given a valid email when the password reset endpoint is called then send a reset email") {
            //GIVEN
            coJustRun { mockPasswordResetService.resetPasswordByEmail(email) }
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/reset-password"
                ) {
                    header("Content-Type", "application/json")
                    setBody(resetPasswordDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe NoContent
                }
                coVerify { mockPasswordResetService.resetPasswordByEmail(email) }
            }
        }
        should("given a invalid email when the password reset endpoint is called then return an error") {
            //GIVEN
            coEvery {
                mockPasswordResetService.resetPasswordByEmail(email)
            } throws EntityNotFoundException("A user does not exist with the provided email address")
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/reset-password"
                ) {
                    header("Content-Type", "application/json")
                    setBody(resetPasswordDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe NotFound
                }
                coVerify { mockPasswordResetService.resetPasswordByEmail(email) }
            }
        }
    }
    context("/users/redeem-password-token") {
        should("given a valid redeem password token request when the endpoint is called then call the change password endpoint") {
            //GIVEN
            coJustRun { mockPasswordResetService.resetPassword(token, password) }
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/redeem-password-token"
                ) {
                    header("Content-Type", "application/json")
                    setBody(redeemPasswordTokenDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe OK
                }
                coVerify { mockPasswordResetService.resetPassword(token, password) }
            }
        }
        should("given an invalid redeem password token request when the endpoint is called then return an error") {
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/redeem-password-token"
                ) {
                    header("Content-Type", "application/json")
                    setBody("{}")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe BadRequest
                }
            }
        }
        should("given a valid password reset request when that reset fails then return an error code") {
            //GIVEN
            coEvery { mockPasswordResetService.resetPassword(token, password) }.throws(
                PasswordResetException(
                    TokenErrorCode.TOKEN_EXPIRED, "Oops"
                )
            )
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/users/redeem-password-token"
                ) {
                    header("Content-Type", "application/json")
                    setBody(redeemPasswordTokenDtoBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe BadRequest
                    response.bodyAsText().contains(TokenErrorCode.TOKEN_EXPIRED.name)
                }
                coVerify { mockPasswordResetService.resetPassword(token, password) }
            }
        }
    }
})

