package energy.so.core.identity.server.grpc

import com.google.protobuf.Empty
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.dtos.idRequestStr
import energy.so.core.identity.server.controllers.grpc.UsersGrpcController
import energy.so.core.identity.server.exceptions.FeatureNotAllowedException
import energy.so.core.identity.server.exceptions.InvalidEmailConfirmationTokenException
import energy.so.core.identity.server.exceptions.InvalidEmailException
import energy.so.core.identity.server.fixtures.PreCannedData.ID_1
import energy.so.core.identity.server.fixtures.PreCannedData.alicesUser
import energy.so.core.identity.server.fixtures.PreCannedData.createUserAliceRequest
import energy.so.core.identity.server.fixtures.PreCannedData.frozenLocalDateTime
import energy.so.core.identity.server.fixtures.PreCannedData.patchRequest
import energy.so.core.identity.server.fixtures.PreCannedData.userResponseAlice
import energy.so.core.identity.server.mappers.toUser
import energy.so.core.identity.server.services.UserService
import energy.so.users.v2.confirmEmailUpdateRequest
import energy.so.users.v2.copy
import energy.so.users.v2.externalIdRequest
import energy.so.users.v2.updateEmailRequest
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk

class UsersGrpcControllerTest : ShouldSpec({

    val userService = mockk<UserService>()
    lateinit var subject: UsersGrpcController

    beforeTest {
        subject = UsersGrpcController(userService)
    }

    // For these tests, we have frozen time at [frozenInstant], so our expected timestamp is always this.
    val expectedTimestamp = frozenLocalDateTime
    val expectedGrpcTimestamp = expectedTimestamp.toTimestamp()

    context("::createUser") {
        should("when we send the request create Alice, then Alice is stored/returned at expected timestamp with a new id") {
            //GIVEN
            val expectedAliceInService =
                alicesUser.copy(id = 1, createdAt = expectedTimestamp)
            val expectedUserResponse =
                userResponseAlice.copy { id = 1; createdAt = expectedGrpcTimestamp }
            coEvery { userService.createUser(createUserAliceRequest.toUser()) } returns expectedAliceInService

            //WHEN
            val createUserResponse = subject.createUser(createUserAliceRequest)

            //THEN
            assertSoftly {
                createUserResponse shouldBe expectedUserResponse
            }
        }

        should("rethrow InvalidEmailException") {
            val originalException = InvalidEmailException("email already exists")
            //GIVEN
            coEvery { userService.createUser(createUserAliceRequest.toUser()) } throws originalException

            //WHEN
            val rethrownException =
                shouldThrow<InvalidArgumentGrpcException> { subject.createUser(createUserAliceRequest) }

            //THEN
            rethrownException.message shouldBe "email already exists"
            rethrownException.cause shouldBe originalException
            rethrownException.errorCategory shouldBe ErrorCategories.CREATE_USER
            rethrownException.errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
        }

        should("rethrow generic exception") {
            val originalException = RuntimeException("some other stuff")
            //GIVEN
            coEvery { userService.createUser(createUserAliceRequest.toUser()) } throws originalException

            //WHEN
            val rethrownException = shouldThrow<RuntimeException> { subject.createUser(createUserAliceRequest) }

            //THEN
            rethrownException shouldBe originalException
        }


    }

    context("::getUserById") {
        should("get the user corresponding to the generated id") {
            // GIVEN
            val userId: Long = 1
            coEvery { userService.getUserById(userId) } returns alicesUser.copy(
                id = userId,
                createdAt = frozenLocalDateTime,
            )

            val expectedReturnedUser = userResponseAlice.copy {
                id = userId
                createdAt = expectedGrpcTimestamp
            }

            // WHEN
            val returnedUser = subject.getUserById(idRequest { id = userId })

            // THEN
            returnedUser shouldBe expectedReturnedUser
        }
        should("throw EntityNotFoundGrpcException when user with id is not found") {
            // GIVEN
            val userId: Long = 2
            coEvery { userService.getUserById(userId) } throws EntityNotFoundException("error")

            // WHEN THEN
            shouldThrow<EntityNotFoundGrpcException> {
                subject.getUserById(idRequest {
                    id = userId
                })
            }
        }
    }

    context("isUserAdmin") {
        should("return false when user is not admin") {
            //GIVEN
            val userId: Long = 1
            coEvery { userService.isUserAdmin(userId) } returns false

            //WHEN
            val response = subject.isUserAdmin(idRequest { id = userId })

            //THEN
            response.value shouldBe false
        }
        should("return true when user is admin") {
            //GIVEN
            val adminId: Long = 2
            coEvery { userService.isUserAdmin(adminId) } returns true

            //WHEN
            val response = subject.isUserAdmin(idRequest { id = adminId })

            //THEN
            response.value shouldBe true
        }
    }

    context("::getUserByExternalId") {
        should("given a getUserByExternalId call when called then pass the call to the service") {
            // GIVEN
            val externalId = "1"
            coEvery { userService.getUserByExternalId(externalId) } returns alicesUser.copy(
                id = 1L,
                externalId = externalId,
                createdAt = frozenLocalDateTime,
            )

            // WHEN
            val returnedUser = subject.getUserByExternalId(
                externalIdRequest { this.externalId = externalId })

            // THEN
            returnedUser shouldBe userResponseAlice.copy {
                id = returnedUser.id
                this.externalId = externalId
                createdAt = expectedGrpcTimestamp
            }
        }

        should("given a getUserByExternalId when the external ID does not exist then throw an exception") {
            // GIVEN
            val externalId = "this does neigh exist"
            coEvery { userService.getUserByExternalId(externalId) } throws EntityNotFoundException("error")
            // WHEN THEN
            shouldThrow<EntityNotFoundException> {
                subject.getUserByExternalId(externalIdRequest {
                    this.externalId = externalId
                })
            }
        }
    }

    context("::patchUser") {
        should("given patchUserRequest should return empty") {
            // GIVEN
            val request = patchRequest.copy { currentAccountId = ID_1.toNullableInt64() }
            coEvery { userService.patchUser(request) } returns Unit

            // WHEN
            val emptyResult = subject.patchUser(request)

            // THEN
            emptyResult shouldBe Empty.getDefaultInstance()
        }
    }

    context("::updateEmail") {
        should("given valid updateEmailRequest then return empty") {
            //GIVEN
            val userId = 1L
            val newEmail = "<EMAIL>"
            val request = updateEmailRequest {
                this.userId = userId
                this.newEmail = newEmail
            }
            coEvery { userService.updateEmail(request) } returns Unit

            //WHEN
            val response = subject.updateEmail(request)

            //THEN
            response shouldBe Empty.getDefaultInstance()
        }
        should("given invalid email should throw InvalidArgumentGrpcException") {
            //GIVEN
            val userId = 1L
            val newEmail = "<EMAIL>"
            val request = updateEmailRequest {
                this.userId = userId
                this.newEmail = newEmail
            }
            coEvery { userService.updateEmail(request) } throws InvalidEmailException("error")

            //WHEN-THEN
            shouldThrow<InvalidArgumentGrpcException> { subject.updateEmail(request) }
        }
        should("given disabled DIY_EMAIL_UPDATE feature flag should throw FeatureNotAllowedException") {
            //GIVEN
            val userId = 1L
            val newEmail = "<EMAIL>"
            val request = updateEmailRequest {
                this.userId = userId
                this.newEmail = newEmail
            }
            coEvery { userService.updateEmail(request) } throws FeatureNotAllowedException("error")

            //WHEN-THEN
            shouldThrow<FailedPreconditionGrpcException> { subject.updateEmail(request) }
        }

        should("given user not found should throw EntityNotFoundGrpcException") {
            //GIVEN
            val userId = 1L
            val newEmail = "<EMAIL>"
            val request = updateEmailRequest {
                this.userId = userId
                this.newEmail = newEmail
            }
            coEvery { userService.updateEmail(request) } throws EntityNotFoundException("error")

            //WHEN-THEN
            shouldThrow<EntityNotFoundGrpcException> { subject.updateEmail(request) }
            coVerify { userService.updateEmail(request) }
        }
    }

    context("::confirmEmailUpdate") {
        should("given valid confirmEmailUpdateRequest then return empty") {
            //GIVEN
            val userId = 1L
            val token = "validToken"
            val request = confirmEmailUpdateRequest {
                this.userId = userId
                this.token = token
            }
            coEvery { userService.confirmEmailUpdate(request) } returns Unit

            //WHEN
            val response = subject.confirmEmailUpdate(request)

            //THEN
            response shouldBe Empty.getDefaultInstance()
        }
        should("given invalid token should throw InvalidArgumentGrpcException") {
            //GIVEN
            val userId = 1L
            val token = "invalidToken"
            val request = confirmEmailUpdateRequest {
                this.userId = userId
                this.token = token
            }
            coEvery { userService.confirmEmailUpdate(request) } throws InvalidEmailConfirmationTokenException("error")

            //WHEN-THEN
            shouldThrow<InvalidArgumentGrpcException> { subject.confirmEmailUpdate(request) }
        }
    }

    context("::getUserByEmail") {
        should("given a getUserByEmail call when called then pass the call to the service") {
            // GIVEN
            val email = "r@test"
            coEvery { userService.getUserByEmail(email) } returns alicesUser.copy(
                id = ID_1,
                email = email,
                createdAt = frozenLocalDateTime,
            )

            // WHEN
            val returnedUser = subject.getUserByEmail(
                idRequestStr { id = email })

            // THEN
            returnedUser shouldBe userResponseAlice.copy {
                id = returnedUser.id
                this.email = email
                createdAt = expectedGrpcTimestamp
            }
        }

        should("given a getUserByEmail when the email does not exist then throw an exception") {
            // GIVEN
            val email = "this does not exist"
            coEvery { userService.getUserByEmail(email) } returns null
            // WHEN THEN
            shouldThrow<EntityNotFoundGrpcException> {
                subject.getUserByEmail(idRequestStr {
                    id = email
                })
            }
        }
    }

    context("::markUserEmailAsOld") {
        should("given a markUserEmailAsOld call when called then pass the call to the service") {
            // GIVEN
            val userId = ID_1
            coJustRun { userService.markUserEmailAsOld(userId) }

            // WHEN
            subject.markUserEmailAsOld(
                idRequest { id = userId })

            // THEN
            coVerify { userService.markUserEmailAsOld(userId) }
        }
    }

})
