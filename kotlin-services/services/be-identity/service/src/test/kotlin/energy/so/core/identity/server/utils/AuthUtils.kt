package energy.so.core.identity.server.utils

import energy.so.commons.model.tables.pojos.AdminPass
import energy.so.commons.model.tables.pojos.User
import energy.so.commons.security.UserPrincipal
import energy.so.commons.security.crypto.hashSha256
import energy.so.core.identity.server.models.EmailTokenCredentials
import energy.so.core.identity.server.models.LoginMetadata
import energy.so.core.identity.server.models.UsernamePasswordCredentials
import energy.so.core.identity.server.services.tokens.Token
import java.time.ZonedDateTime
import java.util.UUID

/** <AUTHOR> 14/07/2022 */
object AuthUtils {

    const val SAMPLE_USER_ID = "1234"
    const val SAMPLE_USERNAME = "sample-username"
    const val SAMPLE_EMAIL = "<EMAIL>"
    const val SAMPLE_PRIMARY_CONTACT_FIRST_NAME = "Sample First Name"
    const val SAMPLE_PRIMARY_CONTACT_LAST_NAME = "Sample Last Name"
    const val SAMPLE_TOKEN = "token"

    val sampleUsernamePasswordCredentials = UsernamePasswordCredentials(
        username = SAMPLE_USERNAME,
        password = "sample-password"
    )

    val sampleEmailTokenCredentials = EmailTokenCredentials(
        email = SAMPLE_EMAIL,
        token = SAMPLE_TOKEN
    )

    val loginMetadata = LoginMetadata(
        sourceIp = "127.0.0.1",
        userAgent = "Mozilla user agent"
    )

    val sampleUser = User(
        id = SAMPLE_USER_ID.toLong(),
        email = SAMPLE_EMAIL,
        encryptedPassword = "sample-encrypted-password",
    )

    val adminPass = AdminPass(
        id = 10,
        adminId = "2",
        userId = SAMPLE_USER_ID.toLong(),
        token = hashSha256(SAMPLE_TOKEN).toByteArray(),
    )

    val sampleToken = Token(
        userId = "12345",
        accessToken = "sample-access-token",
        refreshToken = "sample-refresh-token",
        expiresAt = ZonedDateTime.now().plusSeconds(7200000),
        refreshTokenExpiresAt = ZonedDateTime.now().plusSeconds(3600),
        groupId = UUID.randomUUID(),
        username = "<EMAIL>"
    )

    val sampleUserPrincipal = UserPrincipal(
        userId = SAMPLE_USER_ID,
        email = SAMPLE_EMAIL,
        firstName = SAMPLE_PRIMARY_CONTACT_FIRST_NAME,
        lastName = SAMPLE_PRIMARY_CONTACT_LAST_NAME,
        roles = setOf(),
    )

}