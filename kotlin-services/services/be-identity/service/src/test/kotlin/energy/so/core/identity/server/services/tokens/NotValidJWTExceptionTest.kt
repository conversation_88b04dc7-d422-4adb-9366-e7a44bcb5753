package energy.so.core.identity.server.services.tokens

import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

internal class NotValidJWTExceptionTest {

    @Test
    fun `exception is created with the right message`() {
        val exception = NotValidJWTException(ERROR_MESSAGE)

        Assertions.assertThat(exception.message).isEqualTo(ERROR_MESSAGE)
    }

    companion object {
        const val ERROR_MESSAGE = "An error has occurred"
    }
}
