package energy.so.core.identity.server.mappers

import energy.so.core.identity.server.dtos.SetNewPasswordRequestDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordRequest
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordRequestDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordResponseDto
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class PasswordResetMappersTest : BehaviorSpec({
    given("set new password request proto") {
        val setNewPasswordRequestProto = setNewPasswordRequest

        `when`("SetNewPasswordRequestDto::fromSetNewPasswordRequestProto") {
            val result = SetNewPasswordRequestDto.fromSetNewPasswordRequest(setNewPasswordRequestProto)

            then("dto should be correctly mapped") {
                result shouldBe setNewPasswordRequestDto
            }
        }
    }

    given("set new password response dto") {
        val setNewPasswordResponseDto = setNewPasswordResponseDto

        `when`("SetNewPasswordResponseDto::toSetNewPasswordProto") {
            val result = setNewPasswordResponseDto.toSetNewPasswordResponse()

            then("proto object should be correctly mapped") {
                result.success shouldBe true
            }
        }
    }
})
