package energy.so.core.identity.server.services

import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.commons.exceptions.dto.ErrorDto
import energy.so.commons.exceptions.grpc.GrpcException
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.pojos.PasswordResetToken
import energy.so.commons.model.tables.pojos.User
import energy.so.commons.security.verifiers.PasswordVerifier
import energy.so.commons.v2.dtos.idRequest
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationDto
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.core.identity.server.config.ThirdPartySignUpConfig
import energy.so.core.identity.server.controllers.dtos.TokenErrorCode
import energy.so.core.identity.server.database.repositories.UserRepository
import energy.so.core.identity.server.dtos.PasswordResetRequestDto
import energy.so.core.identity.server.exceptions.InvalidPasswordResetRequestException
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordBadRequestResponse
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordInvalidTokenResponseDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordRequestDto
import energy.so.core.identity.server.fixtures.AuthenticationCannedData.setNewPasswordResponseDto
import energy.so.core.identity.server.fixtures.PreCannedData
import energy.so.core.identity.server.fixtures.PreCannedData.GENERAL_ERROR_MESSAGE
import energy.so.core.identity.server.fixtures.PreCannedData.OLD_PASSWORD_INCORRECT_ERROR_MESSAGE
import energy.so.core.identity.server.fixtures.PreCannedData.user
import energy.so.core.identity.server.models.PasswordConfig
import energy.so.core.identity.server.validators.passwordResetValidators.PasswordResetEmailFormatNotValid
import energy.so.core.identity.server.validators.passwordResetValidators.PasswordResetRequestValidator
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.v2.customers.customer
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDateTime
import org.jooq.exception.DataAccessException
import org.junit.jupiter.api.assertThrows
import energy.so.core.identity.server.commons.exceptions.PasswordResetException as CommonPasswordResetException

class PasswordResetServiceTest : BehaviorSpec({

    val expiryHours = 24L
    val passwordResetTemplateName = "passwordResetTemplate"
    val thirdPartySignupTemplateName = "thirdPartySignupTemplateName"
    val resetUrl = "www.test.com"
    val setPasswordUrl = "www.set-password.com"
    val password = "password"
    val mockUserRepository: UserRepository = mockk()
    val mockCommsClient: CommunicationClient = mockk()
    val mockCustomerClient: CustomersClient = mockk()
    val mockPasswordVerifier: PasswordVerifier = mockk()
    val mockResetPasswordRequestValidator: PasswordResetRequestValidator = mockk()
    val service = PasswordResetService(
        mockUserRepository,
        mockCommsClient,
        PasswordConfig(expiryHours, resetUrl, passwordResetTemplateName),
        ThirdPartySignUpConfig(thirdPartySignupTemplateName, setPasswordUrl),
        mockPasswordVerifier,
        mockResetPasswordRequestValidator,
        mockCustomerClient,
    )

    afterEach {
        clearMocks(mockUserRepository)
    }

    given("::setNewPassword") {
        val setNewPasswordDto = setNewPasswordRequestDto

        and("successful response from change password") {
            every { mockUserRepository.changePassword(any(), any()) } returns Unit

            `when`("set new password") {
                val setNewPasswordResponse = service.setNewPassword(setNewPasswordDto)

                then("response should be successful") {
                    verify(exactly = 1) { mockUserRepository.changePassword(any(), any()) }

                    setNewPasswordResponse shouldBe setNewPasswordResponseDto
                }
            }
        }

        and("change password throws PasswordResetException") {
            every { mockUserRepository.changePassword(any(), any()) }.throws(
                CommonPasswordResetException(
                    TokenErrorCode.NO_MATCHING_TOKEN,
                    "No matching or unclaimed password reset token"
                )
            )

            `when`("set new password") {
                then("response should be false") {
                    val setNewPasswordResponse = service.setNewPassword(setNewPasswordDto)
                    verify(exactly = 1) { mockUserRepository.changePassword(any(), any()) }
                    setNewPasswordResponse shouldBe setNewPasswordInvalidTokenResponseDto
                }
            }
        }

        and("change password throws DataAccessException") {
            every { mockUserRepository.changePassword(any(), any()) }.throws(
                DataAccessException("")
            )
            `when`("set new password") {
                then("response should be false") {
                    val setNewPasswordResponse = service.setNewPassword(setNewPasswordDto)
                    verify(exactly = 1) { mockUserRepository.changePassword(any(), any()) }
                    setNewPasswordResponse shouldBe setNewPasswordBadRequestResponse
                }
            }
        }
    }

    given("::generatePasswordResetToken") {

        val testEmail = "<EMAIL>"
        val testUser = User(
            id = 666,
            email = testEmail
        )

        and("given an email that does not exist in the repository") {
            every { mockUserRepository.findByEmail(testEmail) } returns null

            `when`("a reset is requested") {
                shouldThrow<EntityNotFoundException> { service.resetPasswordByEmail(testEmail) }

                then("throw an error") {
                    verify { mockUserRepository.findByEmail(testEmail) }
                }
            }
        }

        and("an email that exists in the repository") {
            every { mockUserRepository.findByEmail(testEmail) } returns testUser
            justRun { mockUserRepository.savePasswordResetToken(any()) }
            coJustRun { mockCommsClient.sendCommunicationTemplate(any()) }

            `when`("when a reset is requested") {
                service.resetPasswordByEmail(testEmail)

                then("process the request") {
                    val savedToken = slot<PasswordResetToken>()
                    val sentTemplate = slot<CommunicationTemplateDto>()

                    verify { mockUserRepository.findByEmail(testEmail) }
                    verify { mockUserRepository.savePasswordResetToken(capture(savedToken)) }
                    coVerify { mockCommsClient.sendCommunicationTemplate(capture(sentTemplate)) }

                    assertSoftly {
                        val generatedAt = savedToken.captured.tokenGeneratedAt
                        savedToken.captured.userId shouldBe testUser.id
                        savedToken.captured.token shouldNotBe null
                        savedToken.captured.tokenClaimedAt shouldBe null
                        generatedAt shouldNotBe null
                        savedToken.captured.tokenExpiry shouldBe generatedAt!!.plusHours(expiryHours)

                        sentTemplate.captured.communicationName shouldBe passwordResetTemplateName
                        sentTemplate.captured.recipient.name shouldBe ""
                        sentTemplate.captured.recipient.email shouldBe testEmail
                        sentTemplate.captured.customAttributes["password_reset_url"] shouldContain resetUrl
                    }
                }
            }
        }
    }

    given("::generatePasswordResetTokenAndSendThirdPartyInstructions") {
        val testEmail = "<EMAIL>"
        val testUser = User(
            id = 666,
            email = testEmail,
            currentAccountId = 1L,
        )

        and("given an email that does not exist in the repository") {
            every { mockUserRepository.findByEmail(testEmail) } returns null

            `when`("a third party signup is requested") {
                shouldThrow<EntityNotFoundException> {
                    service.generatePasswordResetTokenAndSendThirdPartyInstructions(
                        testEmail
                    )
                }

                then("throw an error") {
                    verify { mockUserRepository.findByEmail(testEmail) }
                }
            }
        }

        and("email exists in the repository") {
            // GIVEN
            every { mockUserRepository.findByEmail(testEmail) } returns testUser
            justRun { mockUserRepository.savePasswordResetToken(any()) }
            coJustRun { mockCommsClient.sendCommunicationTemplate(any()) }

            coEvery {
                mockCustomerClient.getCustomerByBillingAccountId(idRequest {
                    id = testUser.currentAccountId!!
                })
            } returns customer {
                firstName = "Alice"
            }

            // WHEN
            `when`("when a reset is requested") {
                service.generatePasswordResetTokenAndSendThirdPartyInstructions(testEmail)

                then("process the request") {
                    val savedToken = mutableListOf<PasswordResetToken>()
                    val sentTemplate = mutableListOf<CommunicationTemplateDto>()

                    verify { mockUserRepository.findByEmail(testEmail) }
                    verify { mockUserRepository.savePasswordResetToken(capture(savedToken)) }
                    coVerify { mockCommsClient.sendCommunicationTemplate(capture(sentTemplate)) }
                    coVerify {
                        mockCustomerClient.getCustomerByBillingAccountId(idRequest {
                            id = testUser.currentAccountId!!
                        })
                    }

                    assertSoftly {
                        val generatedAt = savedToken.last().tokenGeneratedAt
                        savedToken.last().userId shouldBe testUser.id
                        savedToken.last().token shouldNotBe null
                        savedToken.last().tokenClaimedAt shouldBe null
                        generatedAt shouldNotBe null
                        savedToken.last().tokenExpiry shouldBe generatedAt!!.plusHours(expiryHours)

                        sentTemplate.last().communicationName shouldBe thirdPartySignupTemplateName
                        sentTemplate.last().recipient.name shouldBe ""
                        sentTemplate.last().recipient.email shouldBe testEmail
                        sentTemplate.last().customAttributes["forename"] shouldBe "Alice"
                    }
                }
            }
        }
    }

    given("::redeemPasswordResetToken") {

        and("a valid password and token") {
            // GIVEN
            val validToken =
                "YWNlNjA3ZGUwZTNmODMwZjg4Mzg4NzgwMGQ0ZDI2OGE4YTY2ODg0ODFlMjljOTNmZDRjZGMxZDc2M2NhM2JlOTBmMDdlMzFiZWUwMDg3MTkwMjMyN2M1YTgzNjA0MTI5NTEzMDk1NjY3NjBmZGIyYzhkZGJjNTZjMzg1MDk1NmU="
            justRun { mockUserRepository.changePassword(any(), any()) }

            // WHEN
            `when`("when redeemed") {
                service.resetPassword(validToken, password)

                then("begin the repository redeem process") {
                    val tokenCapture = slot<String>()
                    val passwordCapture = slot<String>()
                    verify { mockUserRepository.changePassword(capture(tokenCapture), capture(passwordCapture)) }
                    assertSoftly {
                        tokenCapture.captured shouldNotBe null
                        tokenCapture.captured shouldNotBe validToken // This should be hashed now
                        passwordCapture.captured shouldNotBe null
                        passwordCapture.captured shouldNotBe password // This should be hashed now
                    }
                }
            }
        }
    }

    given("ByteArray.toHex") {
        and("an array of bytes") {
            val bytes = "my hex bytes".toByteArray()

            `when`("converted to hex") {
                val result = bytes.toHex()

                then("return a hexadecimal string") {
                    result shouldBe "6d7920686578206279746573"
                }
            }
        }
    }

    given("::ResetPassword") {
        and("reset password by setting old and new password flow") {
            val oldPassword = "abcdefG1"
            val newPassword = "abcdefG2"
            val userId = 1L
            val user = user.copy(id = userId, encryptedPassword = oldPassword)
            val passwordResetRequestDto =
                PasswordResetRequestDto(oldPassword, newPassword, userId = userId)

            and("old password from request matches password stored in DB") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                coEvery { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                coEvery { mockUserRepository.changePassword(any(), any()) } returns Unit
                coEvery { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) } returns true

                and("user is locked") {
                    val lockeduser = user.copy(locked = LocalDateTime.now(), failedAttempts = 5)
                    coEvery { mockUserRepository.findById(userId) } returns lockeduser
                    coEvery { mockUserRepository.save(user) } returns user

                    `when`("resetPassword") {
                        val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)

                        then("verify method call and response") {
                            verify { mockUserRepository.findById(userId) }
                            verify { mockUserRepository.save(user) }
                            verify { mockUserRepository.savePasswordResetToken(any()) }
                            verify { mockUserRepository.changePassword(any(), any()) }
                            verify { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) }
                            resetPasswordResponse.success shouldBe true
                        }
                    }
                }

                and("user is not locked") {
                    coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                    coEvery { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                    coEvery { mockUserRepository.changePassword(any(), any()) } returns Unit
                    coEvery { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) } returns true
                    coEvery { mockUserRepository.findById(userId) } returns user
                    `when`("resetPassword") {
                        val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)

                        then("verify method call and response") {
                            verify { mockUserRepository.findById(userId) }
                            verify { mockUserRepository.savePasswordResetToken(any()) }
                            verify { mockUserRepository.changePassword(any(), any()) }
                            verify { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) }
                            resetPasswordResponse.success shouldBe true
                        }
                    }
                }
            }


            and("old password from request does not match password stored in DB") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.findById(userId) } returns user
                every { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) } returns false

                `when`("resetPassword") {
                    then("response should be false") {
                        val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)
                        verify(exactly = 0) { mockUserRepository.savePasswordResetToken(any()) }
                        verify(exactly = 0) { mockUserRepository.changePassword(any(), any()) }
                        verify { mockUserRepository.findById(userId) }
                        verify { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) }
                        resetPasswordResponse.success shouldBe false
                        resetPasswordResponse.error shouldBe OLD_PASSWORD_INCORRECT_ERROR_MESSAGE
                    }
                }
            }

            and("change password results in exception") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.findById(userId) } returns user
                every { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                every { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) } returns true
                every { mockUserRepository.changePassword(any(), any()) } throws DataAccessException("Exception")

                `when`("resetPassword") {
                    then("response should be false") {
                        val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)
                        verify { mockUserRepository.savePasswordResetToken(any()) }
                        verify { mockUserRepository.changePassword(any(), any()) }
                        verify { mockUserRepository.findById(userId) }
                        verify { mockPasswordVerifier.verify(oldPassword, user.encryptedPassword!!) }
                        resetPasswordResponse.success shouldBe false
                        resetPasswordResponse.error shouldBe GENERAL_ERROR_MESSAGE
                    }
                }
            }

            and("user is not found by id") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.findById(userId) } returns null

                `when`("resetPassword") {
                    then("throw EntityNotFoundException") {
                        assertThrows<EntityNotFoundException> {
                            service.resetPassword(passwordResetRequestDto)
                        }
                    }
                }
            }
        }

        and("reset password by sending token through email") {
            val email = PreCannedData.EMAIL
            val passwordResetRequestDto =
                PasswordResetRequestDto(email = email)
            val grpcException = GrpcException.fromErrorDto(
                Class.forName("energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException"),
                ErrorDto.newInstanceWithoutNulls()
            )

            and("user is found by email") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                every { mockUserRepository.changePassword(any(), any()) } returns Unit
                coEvery { mockCommsClient.sendCommunicationTemplate(any()) } returns CommunicationDto(1L, 1L)

                and("user is locked") {
                    every { mockUserRepository.findByEmail(user.email!!) } returns user.copy(
                        locked = LocalDateTime.now(),
                        failedAttempts = 5
                    )
                    coEvery { mockUserRepository.save(user) } returns user

                    `when`("resetPassword") {
                        then("verify method call and response") {
                            val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)
                            verify { mockUserRepository.findByEmail(user.email!!) }
                            verify { mockUserRepository.save(user) }
                            verify { mockUserRepository.savePasswordResetToken(any()) }
                            verify(exactly = 0) { mockUserRepository.changePassword(any(), any()) }
                            resetPasswordResponse.success shouldBe true
                        }
                    }
                }

                and("user is not locked") {
                    coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                    every { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                    every { mockUserRepository.changePassword(any(), any()) } returns Unit
                    coEvery { mockCommsClient.sendCommunicationTemplate(any()) } returns CommunicationDto(1L, 1L)
                    every { mockUserRepository.findByEmail(user.email!!) } returns user

                    `when`("resetPassword") {
                        then("verify method call and response") {
                            val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)
                            verify { mockUserRepository.findByEmail(user.email!!) }
                            verify { mockUserRepository.savePasswordResetToken(any()) }
                            verify(exactly = 0) { mockUserRepository.changePassword(any(), any()) }
                            resetPasswordResponse.success shouldBe true
                        }
                    }
                }
            }

            and("email cannot be sent") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.findByEmail(user.email!!) } returns user
                every { mockUserRepository.savePasswordResetToken(any()) } returns Unit
                every { mockUserRepository.changePassword(any(), any()) } returns Unit
                coEvery { mockCommsClient.sendCommunicationTemplate(any()) } throws grpcException

                `when`("resetPassword") {
                    then("response should be false") {
                        val resetPasswordResponse = service.resetPassword(passwordResetRequestDto)
                        verify { mockUserRepository.findByEmail(user.email!!) }
                        verify { mockUserRepository.savePasswordResetToken(any()) }
                        verify(exactly = 0) { mockUserRepository.changePassword(any(), any()) }
                        resetPasswordResponse.success shouldBe false
                        resetPasswordResponse.error shouldBe GENERAL_ERROR_MESSAGE
                    }
                }
            }

            and("user is not found by email") {
                coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns validNel()
                every { mockUserRepository.findByEmail(user.email!!) } returns null

                `when`("resetPassword") {
                    then("throw entityNotFound exception") {
                        assertThrows<EntityNotFoundException> {
                            service.resetPassword(passwordResetRequestDto)
                        }
                    }
                }
            }
        }

        and("request validation fails") {
            val email = PreCannedData.INVALID_FORMAT_EMAIL
            val passwordResetRequestDto =
                PasswordResetRequestDto(email = email)

            coEvery { mockResetPasswordRequestValidator.validate(passwordResetRequestDto) } returns
                    PasswordResetEmailFormatNotValid().invalidNel()

            `when`("resetPassword") {
                then("throw InvalidPasswordResetRequestException exception") {
                    assertThrows<InvalidPasswordResetRequestException> {
                        service.resetPassword(passwordResetRequestDto)
                    }
                }
            }
        }
    }
})
