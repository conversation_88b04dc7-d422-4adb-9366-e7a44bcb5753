package energy.so.core.identity.server.services.tokens

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import energy.so.core.identity.server.exceptions.InvalidEmailConfirmationTokenException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.ZonedDateTime
import java.util.Date

class EmailConfirmationTokenServiceTest : BehaviorSpec({

    val config = EmailConfirmationJwtTokenConfig(
        secret = "top-secret",
        version = "1.0.0",
        ttlInHours = 3600L,
        issuer = "sample-issuer",
    )
    val verifier = JWT.require(Algorithm.HMAC256(config.secret)).withIssuer(config.issuer).build()
    val sut = EmailConfirmationTokenService(config, verifier)

    given("valid userId and valid email") {
        val userId = 1L
        val newEmail = "<EMAIL>"
        `when`("::generateToken") {
            val token = sut.generateToken(userId, newEmail)
            then("token should be valid and contain claims") {
                JWT.decode(token).claims["email"]?.asString() shouldBe newEmail
                JWT.decode(token).subject shouldBe userId.toString()
            }
        }
    }

    given("generated token and is valid and belongs to given userId and email is present") {
        val userId = 123L
        val email = "<EMAIL>"
        val token = sut.generateToken(userId, email)
        `when`("::verifyToken") {
            val response = sut.verifyToken(userId, token)
            then("throws no error") {
                response shouldBe Unit
            }
        }
    }

    given("generated token but userId does not match") {
        val userId = 123L
        val anotherUserId = 321L
        val email = "<EMAIL>"
        val token = sut.generateToken(userId, email)
        `when`("::verifyToken") {
            then("should throw InvalidEmailConfirmationTokenException") {
                shouldThrow<InvalidEmailConfirmationTokenException> { sut.verifyToken(anotherUserId, token) }
            }
        }
    }

    given("corrupted token") {
        val userId = 123L
        val token = "someCorruptedToken"
        `when`("::verifyToken") {
            then("should throw InvalidEmailConfirmationTokenException") {
                shouldThrow<InvalidEmailConfirmationTokenException> { sut.verifyToken(userId, token) }
            }
        }
    }

    given("token generated with another algorithm") {
        val userId = 123L
        val email = "<EMAIL>"
        val token = generateTokenWithAnotherAlgorithm(config, userId, email, ZonedDateTime.now().plusHours(300))
        `when`("::verifyToken") {
            then("should throw InvalidEmailConfirmationTokenException") {
                shouldThrow<InvalidEmailConfirmationTokenException> { sut.verifyToken(userId, token) }
            }
        }
    }

    given("valid token with valid claims") {
        val userId = 123L
        val email = "<EMAIL>"
        val token = generateTokenWithAnotherAlgorithm(config, userId, email, ZonedDateTime.now().plusHours(300))
        `when`("::getClaimFromToken for existing claim") {
            val response = sut.getClaimFromToken("email", token)
            then("should return email") {
                response shouldBe email
            }
        }
        `when`("::getClaimFromToken for non existing claim") {
            then("should throw InvalidEmailConfirmationTokenException") {
                shouldThrow<InvalidEmailConfirmationTokenException> {
                    sut.getClaimFromToken(
                        "anotherNonExistingClaim",
                        token
                    )
                }
            }
        }
    }
})

private fun generateTokenWithAnotherAlgorithm(
    config: EmailConfirmationJwtTokenConfig,
    userId: Long,
    newEmail: String,
    tokenExpiry: ZonedDateTime,
): String =
    Algorithm.HMAC512(config.secret)
        .let {
            val builder = JWT.create()
                .withIssuer(config.issuer)
                .withSubject(userId.toString())
                .withClaim(CLAIM_EMAIL, newEmail)
                .withExpiresAt(Date.from(tokenExpiry.toInstant()))
            builder.sign(it)
        }
