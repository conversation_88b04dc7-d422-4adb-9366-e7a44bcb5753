package energy.so.core.identity.server.mappers

import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.core.identity.server.fixtures.PreCannedData.alicesAccountId
import energy.so.core.identity.server.fixtures.PreCannedData.alicesUser
import energy.so.core.identity.server.fixtures.PreCannedData.alicesWebsiteUser
import energy.so.core.identity.server.fixtures.PreCannedData.createUserAliceRequest
import energy.so.users.v2.copy
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import kotlin.random.Random.Default.nextLong

class UserMapperTest : ShouldSpec({

    context("CreateUserRequest::mapToUser") {
        should("returned User should have matching values") {
            // GIVEN
            val request = createUserAliceRequest.copy { accountId = alicesAccountId.toNullableInt64() }

            // / WHEN / THEN
            request.toUser() shouldBe alicesUser
        }
    }

    context("WebsiteUser::toUser") {
        should("return a User with matching fields") {
            // GIVEN / WHEN / THEN
            with(alicesWebsiteUser.toUser()) {
                assertSoftly {
                    email shouldBe alicesWebsiteUser.email
                    encryptedPassword shouldBe alicesWebsiteUser.encryptedPassword
                    firstName shouldBe alicesWebsiteUser.firstName
                    lastName shouldBe alicesWebsiteUser.lastName
                    externalId shouldBe alicesWebsiteUser.id.toString()
                    createdAt shouldBe alicesWebsiteUser.createdAt
                    updatedAt shouldBe alicesWebsiteUser.updatedAt
                }
            }
        }
    }
    context("WebsiteUser::toUpdateUserDto") {
        should("return an UpdateUserDto with matching fields") {
            // GIVEN
            val alicesUserId = nextLong()

            // WHEN / THEN
            with(alicesWebsiteUser.toUpdateUserDto(id = alicesUserId)) {
                assertSoftly {
                    id shouldBe alicesUserId
                    firstName shouldBe alicesWebsiteUser.firstName
                    lastName shouldBe alicesWebsiteUser.lastName
                    password shouldBe alicesWebsiteUser.encryptedPassword
                }
            }
        }
    }
})
