package energy.so.core.identity.server.validators

import arrow.core.invalidNel
import energy.so.core.identity.server.dtos.PasswordResetRequestDto
import energy.so.core.identity.server.validators.passwordResetValidators.CorrectEmailOrPasswordsValidator
import energy.so.core.identity.server.validators.passwordResetValidators.NewPasswordFormatNotValid
import energy.so.core.identity.server.validators.passwordResetValidators.OldPasswordFormatNotValid
import energy.so.core.identity.server.validators.passwordResetValidators.PasswordResetEmailFormatNotValid
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import kotlin.test.fail

class CorrectEmailOrPasswordsValidatorTest : BehaviorSpec({

    val correctEmailOrPasswordsValidator = CorrectEmailOrPasswordsValidator()
    val userId = 1L
    val correctNewPassword = "123456Ab"

    given("PasswordResetRequestDto with userId not null") {
        and("new password with incorrect format") {
            val passwordResetRequestDto =
                PasswordResetRequestDto(userId = userId, newPassword = "abc")

            `when`("validate request") {
                val response = correctEmailOrPasswordsValidator.validate(passwordResetRequestDto)

                then("response should be new password format not valid") {
                    response.toString() shouldBe NewPasswordFormatNotValid().invalidNel().toString()
                }
            }
        }

        and("old password is blank") {
            val passwordResetRequestDto =
                PasswordResetRequestDto(userId = userId, newPassword = correctNewPassword, oldPassword = " ")

            `when`("validate request") {
                val response = correctEmailOrPasswordsValidator.validate(passwordResetRequestDto)

                then("response should be old password format not valid") {
                    response.toString() shouldBe OldPasswordFormatNotValid().invalidNel().toString()
                }
            }
        }

        and("new and old password correctly formatted") {
            val passwordResetRequestDto =
                PasswordResetRequestDto(
                    userId = userId,
                    newPassword = correctNewPassword,
                    oldPassword = correctNewPassword
                )

            `when`("validate request") {
                then("should not throw any validation exception") {
                    try {
                        correctEmailOrPasswordsValidator.validate(passwordResetRequestDto)
                    } catch (e: Exception) {
                        fail("No exception expected, got $e")
                    }
                }
            }
        }
    }

    given("PasswordResetRequestDto with null userId") {
        and("email is not correctly formatted") {
            val passwordResetRequestDto =
                PasswordResetRequestDto(email = "xyz")

            `when`("validate request") {
                val response = correctEmailOrPasswordsValidator.validate(passwordResetRequestDto)

                then("response should be email format not valid") {
                    response.toString() shouldBe PasswordResetEmailFormatNotValid().invalidNel().toString()
                }
            }
        }

        and("email correctly formatted") {
            val passwordResetRequestDto =
                PasswordResetRequestDto(email = "<EMAIL>")

            `when`("validate request") {
                then("should not throw any validation exception") {
                    try {
                        correctEmailOrPasswordsValidator.validate(passwordResetRequestDto)
                    } catch (e: Exception) {
                        fail("No exception expected, got $e")
                    }
                }
            }
        }
    }
})
