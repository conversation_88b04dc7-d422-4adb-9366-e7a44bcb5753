package energy.so.core.identity.server.services

import energy.so.commons.model.tables.pojos.RefreshToken
import energy.so.core.identity.server.database.repositories.RefreshTokensRepository
import energy.so.core.identity.server.models.LoginMetadata
import energy.so.core.identity.server.models.UsernamePasswordCredentials
import energy.so.core.identity.server.services.tokens.Token
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.ZonedDateTime.now
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class AbstractAuthServiceTest {

    companion object {
        private const val ACCESS_TOKEN = "access_token"
        private const val REFRESH_TOKEN = "refresh_token"
        private const val SHORT_LIVED_REFRESH_TTL = 1800L
        private const val REFRESH_TTL = 36000L
        private val GROUP_ID = UUID.randomUUID()
        private const val USER_ID = 12345L
        private val expiresAtValue = now()
        private fun generateToken(refreshTtlInSeconds: Long = REFRESH_TTL) = Token(
            userId = USER_ID.toString(),
            accessToken = ACCESS_TOKEN,
            refreshToken = REFRESH_TOKEN,
            expiresAt = expiresAtValue,
            refreshTokenExpiresAt = expiresAtValue.plusSeconds(refreshTtlInSeconds),
            groupId = GROUP_ID,
            username = "username"
        )
    }

    private val credentials = mockk<UsernamePasswordCredentials>()
    private val loginMetadata = mockk<LoginMetadata>()
    private val refreshTokenSlot = slot<RefreshToken>()
    private val savedRefreshToken = mockk<RefreshToken>()

    private val refreshTokenRepository = mockk<RefreshTokensRepository> {
        every { create(capture(refreshTokenSlot)) } returns savedRefreshToken
    }
    private val authService = TestAuthService(refreshTokenRepository = refreshTokenRepository)

    @Test
    fun `it should authenticate user and store refresh token`() = runBlocking<Unit> {
        val token = authService.authenticate(credentials, loginMetadata)
        assertThat(token).isEqualTo(generateToken())

        verify { refreshTokenRepository.create(any()) }
        val refreshTokenExpiresAt = now().plusSeconds(REFRESH_TTL)

        refreshTokenSlot.captured.run {
            assertThat(userId).isEqualTo(USER_ID)
            assertThat(groupId).isEqualTo(GROUP_ID)
            assertThat(expiryDatetime).isStrictlyBetween(
                refreshTokenExpiresAt.toLocalDateTime().minusSeconds(10),
                refreshTokenExpiresAt.toLocalDateTime().plusSeconds(10),
            )
        }
    }

    @Test
    fun `when shortLived is true, authenticates user with short ttl and stores the refresh token`() =
        runBlocking<Unit> {
            val token = authService.authenticate(credentials, loginMetadata, true)
            assertThat(token).isEqualTo(generateToken(SHORT_LIVED_REFRESH_TTL))

            verify { refreshTokenRepository.create(any()) }
            val refreshTokenExpiresAt = now().plusSeconds(SHORT_LIVED_REFRESH_TTL)

            refreshTokenSlot.captured.run {
                assertThat(userId).isEqualTo(USER_ID)
                assertThat(groupId).isEqualTo(GROUP_ID)
                assertThat(expiryDatetime).isStrictlyBetween(
                    refreshTokenExpiresAt.toLocalDateTime().minusSeconds(10),
                    refreshTokenExpiresAt.toLocalDateTime().plusSeconds(10),
                )
            }
        }

    inner class TestAuthService(refreshTokenRepository: RefreshTokensRepository) :
        AbstractAuthService<UsernamePasswordCredentials>(refreshTokenRepository) {
        override suspend fun generateToken(
            credentials: UsernamePasswordCredentials,
            loginMetadata: LoginMetadata,
            shortLivedRefreshToken: Boolean,
        )
                : Token =
            if (shortLivedRefreshToken)
                generateToken(SHORT_LIVED_REFRESH_TTL)
            else
                generateToken()
    }

}
