package energy.so.core.identity.server.services

import energy.so.commons.model.tables.pojos.User
import io.kotest.common.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

/** <AUTHOR> 14/07/2022
 */
internal class DefaultUserPrincipalServiceTest {

    private val sut = DefaultUserPrincipalService()

    @Test
    fun `given user when get user principal then return user principal`() = runBlocking {
        val userId = 123L
        val user = User(
            id = userId,
            email = "<EMAIL>",
            encryptedPassword = "password",
            firstName = SAMPLE_PRIMARY_CONTACT_FIRST_NAME,
            lastName = SAMPLE_PRIMARY_CONTACT_LAST_NAME
        )

        val actual = sut.getUserPrincipal(user)
        assertThat(actual.userId).isEqualTo(userId.toString())
        assertThat(actual.email).isEqualTo(user.email)
        assertThat(actual.firstName).isEqualTo(SAMPLE_PRIMARY_CONTACT_FIRST_NAME)
        assertThat(actual.lastName).isEqualTo(SAMPLE_PRIMARY_CONTACT_LAST_NAME)
        assertThat(actual.roles).containsExactlyInAnyOrder(SAMPLE_ROLE_NAME)
    }

    @Test
    fun `given contact not found when get user principal then set empty string `() = runBlocking {
        val userId = 123L
        val user = User(id = userId, email = "<EMAIL>", encryptedPassword = "password")

        val actual = sut.getUserPrincipal(user)
        assertThat(actual.userId).isEqualTo(userId.toString())
        assertThat(actual.email).isEqualTo(user.email)
        assertThat(actual.firstName).isEmpty()
        assertThat(actual.lastName).isEmpty()
        assertThat(actual.roles).containsExactlyInAnyOrder(SAMPLE_ROLE_NAME)
    }

    @Test
    fun `given roles empty when get user principal then return user principal with empty roles`() = runBlocking {
        val userId = 123L
        val user = User(
            id = userId,
            email = "<EMAIL>",
            encryptedPassword = "password",
            firstName = SAMPLE_PRIMARY_CONTACT_FIRST_NAME,
            lastName = SAMPLE_PRIMARY_CONTACT_LAST_NAME
        )

        val actual = sut.getUserPrincipal(user)
        assertThat(actual.userId).isEqualTo(userId.toString())
        assertThat(actual.email).isEqualTo(user.email)
        assertThat(actual.firstName).isEqualTo(SAMPLE_PRIMARY_CONTACT_FIRST_NAME)
        assertThat(actual.lastName).isEqualTo(SAMPLE_PRIMARY_CONTACT_LAST_NAME)
        assertThat(actual.roles).isEmpty()
    }

    companion object {
        private const val SAMPLE_ROLE_NAME = "test name"
        private const val SAMPLE_PRIMARY_CONTACT_FIRST_NAME = "Sample First Name"
        private const val SAMPLE_PRIMARY_CONTACT_LAST_NAME = "Sample Last Name"
    }
}