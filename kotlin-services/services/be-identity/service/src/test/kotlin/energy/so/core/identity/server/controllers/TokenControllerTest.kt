package energy.so.core.authentication.server.controllers

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import energy.so.commons.security.UserPrincipal
import energy.so.core.identity.server.commons.server.TestHttpServerApplication
import energy.so.core.identity.server.controllers.TokenController
import energy.so.core.identity.server.controllers.dtos.RefreshTokenDtoTest
import energy.so.core.identity.server.services.tokens.TokenService
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.auth.jwt.JWTPrincipal
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.Instant
import java.util.Date

class TokenControllerTest : ShouldSpec({

    lateinit var testApplication: TestHttpServerApplication
    val mockTokenService = mockk<TokenService<UserPrincipal, JWTPrincipal>>()

    val tokenController = TokenController(mockTokenService)

    val refreshToken = "refresh token"
    val refreshTokenBody =
        """
            {
                "refresh_token": "${RefreshTokenDtoTest.REFRESH_TOKEN}"
            }
        """.trimIndent()
    val accessToken = Algorithm.HMAC256("simple_secret")
        .let {
            JWT.create()
                .withIssuer("localhost")
                .withClaim("email", "<EMAIL>")
                .withClaim("firstName", "John")
                .withClaim("lastName", "Doe")
                .withClaim("roles", listOf("admin"))
                .withClaim("version", "1.1.2")
                .withClaim("sub", "3531")
                .withExpiresAt(Date.from(Instant.now().plusSeconds(3600)))
                .sign(it)
        }

    beforeTest {
        clearAllMocks()
        testApplication = TestHttpServerApplication { tokenController(this) }
    }


    context("/tokens/validate") {
        should("validate endpoint should pass as the token is valid") {
            //GIVEN
            coEvery { mockTokenService.isTokenValid(accessToken) } returns true

            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/validate"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.OK
                    coVerify { mockTokenService.isTokenValid(accessToken) }
                }
            }
        }
        should("token is not valid for validate endpoint so it returns unauthorized") {
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/validate"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    val expiredToken = Algorithm.HMAC256("simple_secret")
                        .let {
                            JWT.create()
                                .withIssuer("localhost")
                                .withClaim("email", "<EMAIL>")
                                .withClaim("firstName", "John")
                                .withClaim("lastName", "Doe")
                                .withClaim("roles", listOf("admin"))
                                .withClaim("version", "1.1.2")
                                .withClaim("sub", "3531")
                                .withExpiresAt(Date.from(Instant.now().minusSeconds(3600)))
                                .sign(it)
                        }
                    header(HttpHeaders.Authorization, "Bearer $expiredToken")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.Unauthorized
                }
            }
        }
    }
    context("/tokens/validate-if-present") {
        should("validate-if-present endpoint should pass as the token is valid") {
            //GIVEN
            coEvery { mockTokenService.isTokenValid(accessToken) } returns true
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/validate-if-present"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    header(HttpHeaders.Authorization, "Bearer $accessToken")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.OK
                    coVerify { mockTokenService.isTokenValid(accessToken) }
                }
            }
        }
        should("token is not valid for validate-if-present endpoint so it returns unauthorized") {
            //GIVEN
            coEvery { mockTokenService.isTokenValid(any()) } returns false
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/validate-if-present"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    val expiredToken = Algorithm.HMAC256("simple_secret")
                        .let {
                            JWT.create()
                                .withIssuer("localhost")
                                .withClaim("email", "<EMAIL>")
                                .withClaim("firstName", "John")
                                .withClaim("lastName", "Doe")
                                .withClaim("roles", listOf("admin"))
                                .withClaim("version", "1.1.2")
                                .withClaim("sub", "3531")
                                .withExpiresAt(Date.from(Instant.now().minusSeconds(3600)))
                                .sign(it)
                        }
                    header(HttpHeaders.Authorization, "Bearer ${expiredToken}")
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.Forbidden
                    coVerify { mockTokenService.isTokenValid(any()) }
                }
            }
        }
        should("no token is sent for validate-if-present endpoint so it returns OK") {
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/validate-if-present"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.OK
                }
            }
        }
    }
    context("/tokens/logout") {
        should("valid refreshToken sent for destruction returns OK status") {
            //GIVEN
            coEvery { mockTokenService.invalidateRefreshToken(refreshToken) } returns true
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/logout"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(refreshTokenBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.OK
                }
            }
        }
        should("invalid refreshToken sent for destruction returns Forbidden status") {
            //GIVEN
            coEvery { mockTokenService.invalidateRefreshToken(refreshToken) } returns false
            //WHEN
            testApplication.runTest {
                val response = client.post(
                    urlString = "/tokens/logout"
                ) {
                    header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    setBody(refreshTokenBody)
                }
                // THEN
                assertSoftly {
                    response.status shouldBe HttpStatusCode.Forbidden
                }
            }
        }
    }
})

