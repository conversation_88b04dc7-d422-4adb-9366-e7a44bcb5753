package energy.so.core.identity.server.repositories

import energy.so.commons.model.tables.pojos.AdminPass
import energy.so.commons.model.tables.pojos.User
import energy.so.commons.model.tables.references.ADMIN_PASSES
import energy.so.commons.model.tables.references.USERS
import energy.so.commons.security.crypto.hashSha256
import energy.so.core.identity.server.database.repositories.JooqAdminPassesRepository
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDateTime

class JooqAdminPassesRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val subject = JooqAdminPassesRepository(db)

    val userId = 2L
    val token = hashSha256("abcedfgh-1233456")

    val user = User(
        id = userId,
        email = "<EMAIL>",
        encryptedPassword = "password"
    )
    val adminPass = AdminPass(
        id = 10,
        adminId = "555",
        userId = userId,
        token = token.toByteArray(),
        createdAt = LocalDateTime.of(2000, 1, 1, 1, 1, 1, 0),
        updatedAt = LocalDateTime.of(2000, 1, 1, 1, 1, 1, 0),
    )

    given("AdminPass record with matching user id does not exist") {

        `when`("find by user id and token") {
            val result = subject.findByUserIdAndToken(100, "invalidToken")

            then("return null") {
                result shouldBe null

            }
        }
    }

    given("AdminPass record with matching user id exists") {
        db.newRecord(USERS, user).apply { store() }
        val savedAdminPass =
            db.newRecord(
                ADMIN_PASSES, adminPass.copy(
                    createdAt = LocalDateTime.now().plusMinutes(1),
                    processed = false
                )
            )
                .apply { store() }
                .run { into(AdminPass()) }

        `when`("find by user id and token") {
            val result = subject.findByUserIdAndToken(userId, token)

            then("return admin pass") {
                result shouldBe savedAdminPass
            }
        }
    }

    given("AdminPass record with matching user id and token exists not processed") {
        db.newRecord(USERS, user).apply { store() }
        val savedAdminPass =
            db.newRecord(
                ADMIN_PASSES, adminPass.copy(
                    processed = false
                )
            )
                .apply { store() }
                .run { into(AdminPass()) }


        `when`("find by user id and token not processed") {
            val result = subject.findByUserIdAndTokenNotProcessed(userId, token)

            then("return admin pass") {
                result shouldBe savedAdminPass
            }
        }
    }

    given("AdminPass record with matching user id and token exists and is processed") {
        db.newRecord(USERS, user).apply { store() }
        db.newRecord(
            ADMIN_PASSES, adminPass.copy(
                processed = true
            )
        )
            .apply { store() }
            .run { into(AdminPass()) }


        `when`("find by user id and token not processed") {
            val result = subject.findByUserIdAndTokenNotProcessed(userId, token)

            then("return null") {
                result shouldBe null
            }
        }
    }

    given("no AdminPass record with matching user id exists") {
        db.newRecord(USERS, user).apply { store() }
        db.newRecord(
            ADMIN_PASSES, adminPass.copy(
                processed = false
            )
        )
            .apply { store() }
            .run { into(AdminPass()) }


        `when`("find by user id and token not processed") {
            val result = subject.findByUserIdAndTokenNotProcessed(1234, token)

            then("return null") {
                result shouldBe null
            }
        }
    }

    given("no AdminPass record with matching token exists") {
        db.newRecord(USERS, user).apply { store() }
        db.newRecord(
            ADMIN_PASSES, adminPass.copy(
                processed = false
            )
        )
            .apply { store() }
            .run { into(AdminPass()) }


        `when`("find by user id and token not processed") {
            val result = subject.findByUserIdAndTokenNotProcessed(userId, "invalidToken")

            then("return null") {
                result shouldBe null
            }
        }
    }

    given("data to save admin pass") {
        db.newRecord(USERS, user).apply { store() }

        `when`("save admin pass") {
            val result = subject.save(adminPass)

            then("admin pass is saved") {
                result shouldBe adminPass
            }
        }
    }

    given("existing AdminPass") {
        db.newRecord(USERS, user).apply { store() }
        val savedAdminPass =
            db.newRecord(
                ADMIN_PASSES, adminPass.copy(
                    createdAt = LocalDateTime.now().plusMinutes(1),
                    processed = false
                )
            )
                .apply { store() }
                .run { into(AdminPass()) }

        `when`("mark as processed") {
            val result = subject.markAsProcessed(savedAdminPass)

            then("admin pass should be marked as processed") {
                result shouldBe 1
            }
        }
    }
})
