package energy.so.core.identity.server.validators

import arrow.core.invalidNel
import energy.so.commons.model.tables.pojos.User
import energy.so.core.identity.server.database.repositories.UserRepository
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.fail

class UniqueCorrectEmailValidatorTest : BehaviorSpec({

    val userRepository = mockk<UserRepository>()
    val uniqueCorrectEmailValidator = UniqueCorrectEmailValidator(userRepository)
    val sampleCustomerEmail = "<EMAIL>"
    val sampleUserEncryptedPassword = "EncryptedSamplePassword123"
    val sampleUser = User(
        id = 0L,
        email = sampleCustomerEmail,
        encryptedPassword = sampleUserEncryptedPassword,
    )

    given("email with incorrect format") {
        val email = "abc"

        `when`("validate email") {
            val response = uniqueCorrectEmailValidator.validate(email)

            then("response should be email not valid") {
                response.toString() shouldBe EmailFormatNotValidError().invalidNel().toString()
            }
        }
    }

    given("email already taken") {
        coEvery { userRepository.findByEmail(sampleCustomerEmail) } returns sampleUser

        `when`("validate email") {
            val response = uniqueCorrectEmailValidator.validate(sampleCustomerEmail)

            then("response should be email already taken") {
                response.toString() shouldBe EmailNotUniqueError().invalidNel().toString()
            }
        }
    }

    given("email not taken and correctly formatted") {
        val validEmail = "<EMAIL>"
        coEvery { userRepository.findByEmail(validEmail) } returns null

        `when`("validate email") {
            then("should not throw any validation exception") {
                try {
                    uniqueCorrectEmailValidator.validate(validEmail)
                } catch (e: Exception) {
                    fail("No exception expected, got $e")
                }
            }
        }
    }

})
