package energy.so.core.identity.server.commons.server

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import energy.so.commons.httpserver.features.TracingHttpServer
import energy.so.core.identity.server.commons.ExceptionHandler
import io.ktor.client.HttpClient
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.plugins.forwardedheaders.XForwardedHeaders
import io.ktor.server.plugins.statuspages.StatusPages
import io.ktor.server.routing.Routing
import io.ktor.server.testing.ApplicationTestBuilder
import io.ktor.server.testing.testApplication

class TestHttpServerApplication(
    private val routes: Routing.() -> Unit,
) {

    fun runTest(test: suspend ApplicationTestBuilder.(HttpClient) -> Unit) {
        testApplication {
            install(ContentNegotiation) { json() }
            install(TracingHttpServer)
            install(Authentication) {
                jwt {
                    verifier(
                        JWT.require(Algorithm.HMAC256("simple_secret")).withIssuer("localhost").build()
                    )
                    validate { credential -> JWTPrincipal(credential.payload) }
                }
            }
            install(XForwardedHeaders)
            install(StatusPages) {
                exception<Throwable> { call, exception -> ExceptionHandler.handle(exception, call) }
            }
            routing(routes)
            test(createClient {
                install(io.ktor.client.plugins.contentnegotiation.ContentNegotiation) {
                    json()
                }
            })
        }
    }
}
