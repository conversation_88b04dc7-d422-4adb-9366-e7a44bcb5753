package energy.so.core.identity.server.services.tokens

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.algorithms.Algorithm.HMAC256
import energy.so.commons.security.UserPrincipal
import energy.so.core.identity.server.database.repositories.RefreshTokensRepository
import energy.so.core.identity.server.fixtures.HMAC256_SECRET
import energy.so.core.identity.server.fixtures.INVALID_REFRESH_TOKEN
import energy.so.core.identity.server.fixtures.ISSUER
import energy.so.core.identity.server.fixtures.USER_ID
import energy.so.core.identity.server.fixtures.config
import energy.so.core.identity.server.fixtures.jwtDataForToken
import energy.so.core.identity.server.fixtures.verifier
import energy.so.core.identity.server.models.Feature
import energy.so.core.identity.server.models.FeatureName
import energy.so.core.identity.server.services.FeatureService
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime.now
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class JWTTokenServiceTest {

    private val refreshTokensRepository = mockk<RefreshTokensRepository>()
    private val featureService = mockk<FeatureService>(){
        every { getFeature(FeatureName.TMP_SO_22592_EXTENDED_APP_REFRESH_TOKEN) } returns Feature(
            id = 1L,
            name = FeatureName.TMP_SO_22592_EXTENDED_APP_REFRESH_TOKEN,
            enabled = false
        )
    }
    private val tokenService =
        JWTTokenService(
            config = config,
            verifier = verifier,
            refreshTokensRepository = refreshTokensRepository,
            featureService = featureService
        )

    @Test
    fun `when shortLived is true, refresh token expires shortRefreshTtlInSeconds after now`() {
        val jwtDataToken = jwtDataForToken
        val token = tokenService.generateToken(
            tokenData = jwtDataToken,
            shortLivedRefreshToken = true,
        )

        verifyJwtDataToken(token.accessToken, jwtDataToken)
        assertThat(token.userId).isEqualTo(USER_ID)
        val ttl = now().plusSeconds(config.shortRefreshTtlInSeconds)
        assertThat(token.refreshTokenExpiresAt)
            .isBetween(ttl.minusSeconds(10), ttl.plusSeconds(10))
    }

    @Test
    fun `when shortLived is false, refresh token expiry is refreshTtl hours from now `() {
        val jwtDataTokenData = jwtDataForToken

        val token = tokenService.generateToken(
            tokenData = jwtDataTokenData,
            shortLivedRefreshToken = false,
        )

        verifyJwtDataToken(token.accessToken, jwtDataTokenData)
        assertThat(token.userId).isEqualTo(USER_ID)
        val ttl = now().plusHours(config.refreshTtlInHours)
        assertThat(token.refreshTokenExpiresAt)
            .isBetween(ttl.minusSeconds(10), ttl.plusSeconds(10))
    }

    @Test
    fun `when token is expired should invalidate token`() {
        val jwtDataTokenData = jwtDataForToken

        val tokenServiceWithExpiredToken =
            JWTTokenService(config.copy(ttlInHours = -1L, refreshTtlInHours = -1L), verifier, refreshTokensRepository, featureService)

        val expiredToken = tokenServiceWithExpiredToken.generateToken(
            tokenData = jwtDataTokenData
        )

        assertThat(expiredToken.refreshTokenExpiresAt).isBefore(now())
        assertFalse {
            tokenServiceWithExpiredToken.isTokenValid(expiredToken.accessToken)
        }
    }

    @Test
    fun `when signature of the token is invalid should invalidate token`() {
        val jwtDataTokenData = jwtDataForToken

        val token = tokenService.generateToken(
            tokenData = jwtDataTokenData
        )

        val tokenWithInvalidSignature = token.copy(accessToken = token.accessToken + "Extra")

        assertFalse {
            tokenService.isTokenValid(tokenWithInvalidSignature.accessToken)
        }
    }

    @Test
    fun `when token payload differs from the one signed should invalidate token`() {
        val jwtDataTokenData = jwtDataForToken

        val sourceToken = tokenService.generateToken(
            tokenData = jwtDataTokenData
        )

        // taking the signature part of JWT token
        val sourceTokenSignature = sourceToken.accessToken.split(".")[2]

        val tokenWithDifferentPayload = tokenService.generateToken(
            tokenData = jwtDataTokenData.copy(email = "<EMAIL>")
        )

        val tokenWithDifferentPayloadSplit = tokenWithDifferentPayload.accessToken.split(".")

        assertFalse {
            tokenService.isTokenValid(
                listOf(
                    tokenWithDifferentPayloadSplit[0],
                    tokenWithDifferentPayloadSplit[1],
                    sourceTokenSignature
                ).joinToString(".")
            )
        }
    }

    @Test
    fun `when token is valid should validate token`() {
        val jwtDataTokenData = jwtDataForToken

        val token = tokenService.generateToken(
            tokenData = jwtDataTokenData
        )

        assertTrue {
            tokenService.isTokenValid(token.accessToken)
        }
    }

    @Test
    fun `when token is refreshed it should generate the new ones with the same data`() {
        val jwtDataToken = jwtDataForToken

        val generatedToken = tokenService.generateToken(jwtDataToken)

        every { refreshTokensRepository.replace(any(), any()) } returns 1

        val token = tokenService.refreshToken(generatedToken.refreshToken)

        verifyJwtDataToken(token.accessToken, jwtDataToken)
    }

    @Test
    fun `wrong token cannot be decoded, then an NotValidJWTException is thrown`() {
        val exception = assertThrows<NotValidJWTException> {
            tokenService.refreshToken(INVALID_REFRESH_TOKEN)
        }

        assertThat(exception.message).isEqualTo("Refresh token is not valid")
    }

    @Test
    fun `when token is refreshed and shortLived is true, refresh token expires shortRefreshTtlInSeconds after now`() {
        every { refreshTokensRepository.replace(any(), any()) } returns 1

        val jwtDataToken = jwtDataForToken
        val generatedToken = tokenService.generateToken(jwtDataToken)
        val token = tokenService.refreshToken(generatedToken.refreshToken, true)

        verifyJwtDataToken(token.accessToken, jwtDataToken)
        assertThat(token.userId).isEqualTo(USER_ID)
        val ttl = now().plusSeconds(config.shortRefreshTtlInSeconds)
        assertThat(token.refreshTokenExpiresAt)
            .isBetween(ttl.minusSeconds(10), ttl.plusSeconds(10))
    }

    @Test
    fun `when token is refreshed and shortLived is false, refresh token expiry is refreshTtl hours from now `() {
        every { refreshTokensRepository.replace(any(), any()) } returns 1

        val jwtDataToken = jwtDataForToken
        val generatedToken = tokenService.generateToken(jwtDataToken)
        val token = tokenService.refreshToken(generatedToken.refreshToken, false)

        verifyJwtDataToken(token.accessToken, jwtDataToken)
        assertThat(token.userId).isEqualTo(USER_ID)
        val ttl = now().plusHours(config.refreshTtlInHours)
        assertThat(token.refreshTokenExpiresAt)
            .isBetween(ttl.minusSeconds(10), ttl.plusSeconds(10))
    }

    @Test
    fun `when token is valid, calls repository to delete it and returns true`() {
        // GIVEN
        val jwtDataToken = jwtDataForToken
        val generatedToken = tokenService.generateToken(jwtDataToken)

        every { refreshTokensRepository.delete(any()) } returns 1

        // WHEN
        val result = tokenService.invalidateRefreshToken(generatedToken.refreshToken)

        // THEN
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `when token is invalid (does not exist on the db), returns false`() {
        // GIVEN
        val jwtDataToken = jwtDataForToken
        val generatedToken = tokenService.generateToken(jwtDataToken)

        every { refreshTokensRepository.delete(any()) } returns 0

        // WHEN
        val result = tokenService.invalidateRefreshToken(generatedToken.refreshToken)

        // THEN
        assertThat(result).isEqualTo(false)


    }
}

fun verifyJwtDataToken(jwtDataToken: String, data: UserPrincipal) {
    jwtDataToken.verifyWith {
        JWT.require(it)
            .withIssuer(ISSUER)
            .withClaim("email", data.email)
            .withClaim("firstName", data.firstName)
            .withClaim("lastName", data.lastName)
            .withClaim("version", config.version)
            .withArrayClaim("roles", *data.roles.toTypedArray())
            .build()
    }
}

private fun String.verifyWith(extra: Map<String, String>? = null, buildVerifier: (a: Algorithm) -> JWTVerifier) {
    buildVerifier(HMAC256(HMAC256_SECRET)).verify(this)

    // Verify does not support map values so we check this one manually
    extra?.also {
        JWT.decode(this)
            .getClaim("extra")
            .asMap()
            .let { assertThat(it).isEqualTo(extra) }
    }
}