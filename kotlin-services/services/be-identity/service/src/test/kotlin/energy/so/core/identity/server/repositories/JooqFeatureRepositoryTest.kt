package energy.so.core.identity.server.repositories

import energy.so.commons.extension.save
import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.model.tables.references.FEATURE
import energy.so.core.identity.server.database.repositories.JooqFeatureRepository
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enrolmentJooqFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.paymentsJooqFeature
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.permanentFeatureList
import energy.so.core.identity.server.models.FeatureName
import energy.so.database.test.installDatabase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.server.plugins.NotFoundException

class JooqFeatureRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val subject = JooqFeatureRepository(db)

    given("::getAllFeatures") {
        and("features saved in db") {
            `when`("getAllFeatures called") {
                val result = subject.getAllPermanentFeatures()

                then("return list of features saved in db") {
                    result.sortedBy { it.id } shouldBe permanentFeatureList
                }
            }
        }

        and("no features saved in db") {
            `when`("getAllFeatures called") {
                val result = subject.getAllPermanentFeatures()

                then("return empty list") {
                    result shouldBe emptyList()
                }
            }
        }
    }

    given("::getByName") {
        and("features saved in db") {
            db.newRecord(FEATURE, enrolmentJooqFeature).apply { save() }
            db.newRecord(FEATURE, paymentsJooqFeature).apply { save() }

            `when`("getByName called") {
                val result = subject.getByName(FeatureName.ENROLMENTS.toSiblingEnum())

                then("return correct feature from db") {
                    result shouldBe enrolmentFeature
                }
            }
        }

        and("no features saved in db") {
            `when`("getByName called") {
                val result = subject.getByName(FeatureName.ENROLMENTS.toSiblingEnum())

                then("return null") {
                    result shouldBe null
                }
            }
        }
    }

    given("::setFeature") {
        and("feature saved in db") {
            db.newRecord(FEATURE, enrolmentJooqFeature).apply { save() }
            `when`("setFeature called with true") {
                val result = subject.setFeature(FeatureName.ENROLMENTS.toSiblingEnum(), true)

                then("return correct feature from db") {
                    result.enabled shouldBe true
                    result.name shouldBe FeatureName.ENROLMENTS.toSiblingEnum()
                }
            }
        }

        and("feature saved in db") {
            db.newRecord(FEATURE, enrolmentJooqFeature).apply { save() }
            `when`("setFeature called with false") {
                val result = subject.setFeature(FeatureName.ENROLMENTS.toSiblingEnum(), false)

                then("return correct feature from db") {
                    result.enabled shouldBe false
                    result.name shouldBe FeatureName.ENROLMENTS.toSiblingEnum()
                }
            }
        }

        and("no features saved in db") {
            `when`("setFeature called") {
              shouldThrow<NotFoundException> {  subject.setFeature(FeatureName.ENROLMENTS.toSiblingEnum(), true) }
                then("throws") {
                }
            }
        }
    }
})
