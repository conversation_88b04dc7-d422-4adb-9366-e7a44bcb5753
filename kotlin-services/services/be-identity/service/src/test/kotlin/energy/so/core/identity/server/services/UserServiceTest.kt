package energy.so.core.identity.server.services

import arrow.core.Validated
import arrow.core.invalidNel
import arrow.core.validNel
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.model.tables.pojos.User
import energy.so.commons.security.verifiers.PasswordVerifier
import energy.so.commons.v2.dtos.idRequest
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationDto
import energy.so.core.identity.server.commons.exceptions.AccountLockedException
import energy.so.core.identity.server.commons.exceptions.UnauthorisedHttpException
import energy.so.core.identity.server.dtos.UpdateUserDto
import energy.so.core.identity.server.exceptions.FeatureNotAllowedException
import energy.so.core.identity.server.exceptions.InvalidEmailConfirmationTokenException
import energy.so.core.identity.server.exceptions.InvalidEmailException
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.DIY_EMAIL_UPDATE_FEATURE_NOT_ALLOWED_EXCEPTION_MESSAGE
import energy.so.core.identity.server.fixtures.FeaturePrecannedData.enabledDiyEmailUpdateFeature
import energy.so.core.identity.server.fixtures.InMemoryAdminRolesRepository
import energy.so.core.identity.server.fixtures.InMemoryUserRepository
import energy.so.core.identity.server.fixtures.PreCannedData
import energy.so.core.identity.server.fixtures.PreCannedData.ID_1
import energy.so.core.identity.server.fixtures.PreCannedData.alicesAccountId
import energy.so.core.identity.server.fixtures.PreCannedData.alicesUser
import energy.so.core.identity.server.fixtures.PreCannedData.patchRequest
import energy.so.core.identity.server.fixtures.PreCannedData.user
import energy.so.core.identity.server.models.FeatureName
import energy.so.core.identity.server.models.LoginMetadata
import energy.so.core.identity.server.models.PasswordConfig
import energy.so.core.identity.server.models.SignIn
import energy.so.core.identity.server.models.UsernamePasswordCredentials
import energy.so.core.identity.server.services.tokens.EmailConfirmationTokenService
import energy.so.core.identity.server.validators.EmailFormatNotValidError
import energy.so.core.identity.server.validators.EmailNotUniqueError
import energy.so.core.identity.server.validators.EmailValidator
import energy.so.customers.billingaccounts.v2.billingAccount
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.billingaccounts.BillingAccountsClient
import energy.so.users.v2.confirmEmailUpdateRequest
import energy.so.users.v2.copy
import energy.so.users.v2.patchUserRequest
import energy.so.users.v2.updateEmailRequest
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.assertThrows

class UserServiceTest : ShouldSpec({

    val instant = Clock.fixed(Instant.EPOCH, ZoneOffset.UTC)
    var passwordVerifier: PasswordVerifier
    val expiryHours = 24L
    val resetUrl = "www.test.com"
    val passwordResetTemplateName = "passwordResetTemplate"
    val sampleCustomerUsername = "username"
    val sampleCustomerPassword = "SamplePassword123"
    val sampleCustomerIp = "127.0.0.1"
    val sampleCustomer2ndIp = "*********"
    val userAgent = "Mozilla user agent"
    val sampleUserEncryptedPassword = "EncryptedSamplePassword123"
    val maxSignInAttempts = 5
    val signIn = SignIn(maxSignInAttempts = maxSignInAttempts)
    var verifyPassword: Boolean
    var currentLocalDateTime = LocalDateTime.now()

    var userService: UserService = mockk()

    var sampleUser: User? = null
    val userRepository = InMemoryUserRepository(clock = instant)
    val adminRolesRepository = InMemoryAdminRolesRepository()
    val emailValidator = mockk<EmailValidator>()
    val billingAccountClient = mockk<BillingAccountsClient>()
    val communicationClient = mockk<CommunicationClient>()
    val customerClient = mockk<CustomersClient>()
    val emailConfirmationTokenService = mockk<EmailConfirmationTokenService>()
    val featureService = mockk<FeatureService>()

    val sampleCredentials = UsernamePasswordCredentials(sampleCustomerUsername, sampleCustomerPassword)
    val loginMetadata = LoginMetadata(sourceIp = sampleCustomerIp, userAgent = userAgent)

    beforeAny {
        currentLocalDateTime = LocalDateTime.now()
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } answers { currentLocalDateTime }

        verifyPassword = true
        passwordVerifier = mockk {
            every {
                verify(sampleCustomerPassword, sampleUserEncryptedPassword)
            } answers { verifyPassword }
        }

        sampleUser = User(
            id = 0L,
            email = sampleCustomerUsername,
            encryptedPassword = sampleUserEncryptedPassword,
        )
        userRepository.save(sampleUser!!)

        coEvery { billingAccountClient.getDefaultBillingAccountIdForUserId(any()) } answers {
            billingAccount {
                id = 10L
            }
        }

        userService = UserService(
            userRepository,
            adminRolesRepository,
            passwordVerifier,
            signIn,
            emailValidator,
            emailConfirmationTokenService,
            communicationClient,
            billingAccountClient,
            customerClient,
            featureService,
            PasswordConfig(expiryHours, resetUrl, passwordResetTemplateName),
        )
    }

    context("::createUser") {
        context("the email is valid and is not already taken") {
            should("create user successfully") {
                // GIVEN
                val expectedAlicesUserResponse = alicesUser.copy(
                    id = 1,
                    createdAt = PreCannedData.frozenLocalDateTime,
                )
                coEvery { emailValidator.validate(alicesUser.email!!) } returns validNel()

                // WHEN
                val alicesUserResponse = userService.createUser(alicesUser)

                // THEN
                assertSoftly {
                    alicesUserResponse.email shouldBe expectedAlicesUserResponse.email
                    alicesUserResponse.firstName shouldBe expectedAlicesUserResponse.firstName
                    alicesUserResponse.lastName shouldBe expectedAlicesUserResponse.lastName
                }
            }
        }
        context("the email is already taken") {
            should("throw InvalidEmailException") {
                // GIVEN
                val expectedAlicesUserResponse = alicesUser.copy(
                    id = 1,
                    createdAt = PreCannedData.frozenLocalDateTime,
                    email = "<EMAIL>"
                )
                coEvery { emailValidator.validate(alicesUser.email!!) } returns EmailNotUniqueError().invalidNel()

                // WHEN //THEN
                shouldThrow<InvalidEmailException> { userService.createUser(alicesUser) }
            }
        }
        context("the email is not valid") {
            should("throw InvalidEmailException") {
                // GIVEN
                val expectedAlicesUserResponse = alicesUser.copy(
                    id = 1,
                    createdAt = PreCannedData.frozenLocalDateTime,
                    email = "email"
                )
                coEvery { emailValidator.validate(alicesUser.email!!) } returns EmailFormatNotValidError().invalidNel()

                // WHEN //THEN
                shouldThrow<InvalidEmailException> { userService.createUser(alicesUser) }
            }
        }
    }

    context("::getUserById") {
        context("a user with matching id exists in the repository") {
            val userId = userRepository.save(alicesUser).id!!

            should("retrieve the user corresponding to the generated id") {
                // GIVEN
                val expectedAlicesUserResponse = alicesUser.copy(
                    id = userId,
                    createdAt = PreCannedData.frozenLocalDateTime,
                )

                // WHEN
                val returnedUser = userService.getUserById(id = userId)

                // THEN
                returnedUser shouldBe expectedAlicesUserResponse
            }
        }
        context("no user exists in the db") {
            should("throw EntityNotFoundException") {
                // GIVEN
                val invalidUserId = 1234L

                // WHEN
                val exception = shouldThrow<EntityNotFoundException> {
                    userService.getUserById(invalidUserId)
                }

                // THEN
                exception.message shouldBe EntityNotFoundException(id = invalidUserId, clazz = User::class).message
            }
        }
    }

    context("::isUserAdmin") {
        context("a user with matching id exists in the repository") {
            context("user is not admin") {
                should("return false") {
                    // GIVEN
                    val expected = false
                    val userId = userRepository.save(alicesUser.copy(currentAccountId = alicesAccountId)).id!!

                    // WHEN
                    val response = userService.isUserAdmin(id = userId)

                    // THEN
                    response shouldBe expected
                }
            }
            context("user is admin") {
                should("return true") {
                    //GIVEN
                    val expected = true
                    val userId = userRepository.save(alicesUser.copy(id = 2L, currentAccountId = alicesAccountId)).id!!

                    // WHEN
                    val response = userService.isUserAdmin(id = userId)

                    // THEN
                    response shouldBe expected
                }
            }
        }
        context("no user exists in the db") {
            should("throw EntityNotFoundException") {
                // GIVEN
                val invalidUserId = 1234L

                // WHEN
                val exception = shouldThrow<EntityNotFoundException> {
                    userService.getUserById(invalidUserId)
                }

                // THEN
                exception.message shouldBe EntityNotFoundException(id = invalidUserId, clazz = User::class).message
            }
        }
    }

    context("::updateUser") {
        should("update user successfully") {

            val user = userRepository.save(alicesUser)

            val updateUser = UpdateUserDto(
                id = user.id!!,
                firstName = PreCannedData.FIRST_NAME,
                lastName = PreCannedData.LAST_NAME,
                password = user.encryptedPassword,
                email = user.email
            )

            val returnedUser = userService.updateUser(updateUser)

            assertSoftly {
                returnedUser.firstName shouldBe PreCannedData.FIRST_NAME
                returnedUser.lastName shouldBe PreCannedData.LAST_NAME
                returnedUser.encryptedPassword shouldBe alicesUser.encryptedPassword
                returnedUser.email shouldBe alicesUser.email
            }
        }

        should("fail as unknown user") {

            val user = UpdateUserDto(
                id = alicesAccountId.inc(),
                firstName = PreCannedData.FIRST_NAME
            )

            shouldThrow<EntityNotFoundException> { userService.updateUser(user) }
        }
    }

    context("::getUserByExternalId") {
        should("given an existing user in the repository when queried with a matching external id then return the user") {
            // GIVEN
            userRepository.save(alicesUser)

            // WHEN
            val returnedUser = userService.getUserByExternalId(alicesUser.externalId.toString())

            // THEN
            assertSoftly {
                returnedUser!!.firstName shouldBe alicesUser.firstName
                returnedUser.lastName shouldBe alicesUser.lastName
                returnedUser.encryptedPassword shouldBe alicesUser.encryptedPassword
                returnedUser.email shouldBe alicesUser.email
                returnedUser.externalId shouldBe alicesUser.externalId.toString()
            }
        }

        should("given an existing user in the repository when queried with a mismatching external id then return null") {
            // GIVEN
            userRepository.save(alicesUser)

            // WHEN
            val returnedUser = userService.getUserByExternalId("I do not exist!")

            // THEN
            returnedUser shouldBe null
        }

        should("when user not found should not authorise sign in attempt") {
            val nonExistingCredentials = UsernamePasswordCredentials("nonExistingUsername", sampleCustomerPassword)
            // GIVEN WHEN
            val ex = shouldThrow<UnauthorisedHttpException> {
                userService.getVerifiedUser(nonExistingCredentials, loginMetadata)
            }

            // THEN
            assertUnauthorisedExceptionValues(ex)
        }

        should("when invalid user credentials should not authorise sign in attempt") {
            // GIVEN
            verifyPassword = false

            // WHEN
            val ex = assertThrows<UnauthorisedHttpException> {
                userService.getVerifiedUser(sampleCredentials, loginMetadata)
            }

            // THEN
            assertUnauthorisedExceptionValues(ex)
            verifyFailedSignIn(userRepository.findByEmail(sampleCustomerUsername)!!)
        }

        should("when user locked, don't unlock before 5 mins, throws AccountLockedException") {
            // GIVEN
            userRepository.save(sampleUser!!.copy(locked = currentLocalDateTime.minusMinutes(2)))

            // WHEN
            val ex = shouldThrow<AccountLockedException> {
                userService.getVerifiedUser(sampleCredentials, loginMetadata)
            }

            // THEN
            assertSoftly {
                HttpStatusCode.Unauthorized
                ex.message shouldBe "account locked"
            }
        }

        should("when user locked, after 5 mins, unlock and let sign in") {
            // GIVEN
            userRepository.save(sampleUser!!.copy(locked = currentLocalDateTime.minusMinutes(10)))

            // WHEN
            userService.getVerifiedUser(sampleCredentials, loginMetadata)

            // THEN
            verifySuccessfulSignIn(
                userRepository.findByEmail(sampleCustomerUsername)!!,
                1,
                sampleCustomerIp,
                currentLocalDateTime
            )
        }

        should("when maximum failed sign in attempts reached should not authorise and lock account") {
            verifyPassword = false

            // GIVEN WHEN
            (1..maxSignInAttempts).forEach { attempt ->
                sampleUser = if (attempt != maxSignInAttempts) {
                    val ex = shouldThrow<UnauthorisedHttpException> {
                        userService.getVerifiedUser(sampleCredentials, loginMetadata)
                    }
                    assertUnauthorisedExceptionValues(ex)
                    verifyFailedSignIn(userRepository.findByEmail(sampleCustomerUsername)!!, attempt)
                } else {
                    shouldThrow<AccountLockedException> {
                        userService.getVerifiedUser(sampleCredentials, loginMetadata)
                    }
                    verifyFailedSignIn(userRepository.findByEmail(sampleCustomerUsername)!!, attempt)
                }
            }

            // THEN
            val user = verifyFailedSignIn(userRepository.findByEmail(sampleCustomerUsername)!!, maxSignInAttempts)
            user.locked shouldNotBe null
        }

        should("when valid user credentials and account not locked should authorise sign in") {
            // GIVEN WHEN
            userService.getVerifiedUser(sampleCredentials, loginMetadata)

            // THEN
            verifySuccessfulSignIn(
                userRepository.findByEmail(sampleCustomerUsername)!!,
                1,
                sampleCustomerIp,
                currentLocalDateTime
            )
        }

        should("when valid user signs multiple times should update sign in-related fields of User model") {

            val firstSignInCurrentZonedDateTime = currentLocalDateTime
            userService.getVerifiedUser(sampleCredentials, loginMetadata.copy(sourceIp = sampleCustomer2ndIp))

            sampleUser = verifySuccessfulSignIn(
                user = userRepository.findByEmail(sampleCustomerUsername)!!,
                expectedSignInCount = 1,
                expectedIpAddress = sampleCustomer2ndIp,
                currentLocalDateTime = currentLocalDateTime
            )

            userService.getVerifiedUser(sampleCredentials, loginMetadata)

            val user = verifySuccessfulSignIn(
                userRepository.findByEmail(sampleCustomerUsername)!!,
                expectedSignInCount = 2,
                sampleCustomerIp,
                currentLocalDateTime
            )
            user.run {
                Assertions.assertThat(lastSignInAt).isAfterOrEqualTo(firstSignInCurrentZonedDateTime)
                lastSignInIp shouldBe sampleCustomer2ndIp
            }
        }
    }

    context("::patchUser") {
        context("given no user in db") {
            should("when patch user then nothing happens") {
                userService.patchUser(patchRequest)
            }
        }

        context("given user saved in db") {
            val user1 = user.copy(id = ID_1, currentAccountId = ID_1)
            userRepository.save(user1)

            should("given no data on request when patch user than user is not changed") {
                // GIVEN
                val request1 = patchUserRequest { id = ID_1 }

                // WHEN
                userService.patchUser(request1)

                // THEN
                userService.getUserById(ID_1) shouldBe user1
            }

            should("given current account id on request when patch user than user is changed") {
                // GIVEN - WHEN
                userService.patchUser(patchRequest.copy { currentAccountId = ID_1.toNullableInt64() })

                // THEN
                userService.getUserById(ID_1) shouldBe user1
            }
        }
    }

    context("updateEmail") {
        context("diyEmailUpdate feature flag is enabled") {
            coEvery { featureService.getFeature(FeatureName.DIY_EMAIL_UPDATE) } returns enabledDiyEmailUpdateFeature
            context("given email is valid") {
                val newEmail = "<EMAIL>"
                coEvery { emailValidator.validate(newEmail) } returns Validated.validNel()
                context("given there is a user with given userId on request") {
                    val userId = userRepository.save(alicesUser).id!!
                    context("user has a valid billingAccount") {
                        coEvery {
                            billingAccountClient.getBillingAccountById(idRequest {
                                id = alicesUser.currentAccountId!!
                            }).number
                        } returns "1234"
                        context("user has valid customer") {
                            coEvery {
                                customerClient.getCustomerByBillingAccountId(idRequest {
                                    id = alicesUser.currentAccountId!!
                                }).firstName
                            } returns "Alice"
                            context("communicationTemplate is sent successfully") {
                                //GIVEN
                                coEvery {
                                    emailConfirmationTokenService.generateToken(
                                        userId,
                                        newEmail
                                    )
                                } returns "validToken"
                                coEvery { communicationClient.sendCommunicationTemplate(any()) } returns CommunicationDto(
                                    1,
                                    1
                                )
                                val request = updateEmailRequest {
                                    this.userId = userId
                                    this.newEmail = newEmail
                                }

                                //WHEN
                                val response = userService.updateEmail(request)

                                //THEN
                                response shouldBe Unit
                            }
                            context("communicationTemplate throws error") {
                                coEvery {
                                    emailConfirmationTokenService.generateToken(
                                        userId,
                                        newEmail
                                    )
                                } returns "validToken"
                                coEvery { communicationClient.sendCommunicationTemplate(any()) } throws Exception("some exception")
                                val request = updateEmailRequest {
                                    this.userId = userId
                                    this.newEmail = newEmail
                                }

                                //WHEN-THEN
                                shouldThrow<Exception> { userService.updateEmail(request) }
                            }
                        }
                        context("user does not have a customer") {
                            //GIVEN
                            coEvery {
                                customerClient.getCustomerByBillingAccountId(idRequest {
                                    id = alicesUser.currentAccountId!!
                                })
                            } throws EntityNotFoundException("error")
                            val request = updateEmailRequest {
                                this.userId = userId
                                this.newEmail = newEmail
                            }
                            //WHEN-THEN
                            shouldThrow<EntityNotFoundException> { userService.updateEmail(request) }

                        }
                    }
                    context("user does not have a billingAccount") {
                        //GIVEN
                        coEvery {
                            billingAccountClient.getBillingAccountById(idRequest {
                                id = alicesUser.currentAccountId!!
                            }).number
                        } throws EntityNotFoundException("billingAccount was not found")
                        val request = updateEmailRequest {
                            this.userId = userId
                            this.newEmail = newEmail
                        }

                        //WHEN-THEN
                        shouldThrow<EntityNotFoundException> { userService.updateEmail(request) }

                    }
                }
                context("there is no user with given userId on request") {
                    //GIVEN
                    val userId = 101010L
                    val request = updateEmailRequest {
                        this.userId = userId
                        this.newEmail = newEmail
                    }

                    //WHEN-THEN
                    shouldThrow<EntityNotFoundException> { userService.updateEmail(request) }

                }
            }
            context("given email is not valid") {
                //GIVEN
                val invalidEmail = "AAA"
                coEvery { emailValidator.validate(invalidEmail) } returns EmailFormatNotValidError().invalidNel()
                val request = updateEmailRequest {
                    this.userId = 123L
                    this.newEmail = invalidEmail
                }

                //WHEN-THEN
                shouldThrow<RuntimeException> { userService.updateEmail(request) }
            }
            context("given email is taken") {
                //GIVEN
                val takenEmail = "AAA"
                coEvery { emailValidator.validate(takenEmail) } returns EmailNotUniqueError().invalidNel()
                val request = updateEmailRequest {
                    this.userId = 123L
                    this.newEmail = takenEmail
                }

                //WHEN-THEN
                shouldThrow<RuntimeException> { userService.updateEmail(request) }
            }
        }
        context("diyEmailUpdate feature flag is disabled") {
            coEvery { featureService.getFeature(FeatureName.DIY_EMAIL_UPDATE) } returns enabledDiyEmailUpdateFeature.copy(
                enabled = false
            )
            //GIVEN
            val request = updateEmailRequest {
                this.userId = 123L
                this.newEmail = "<EMAIL>"
            }

            //WHEN-THEN
            val exception = shouldThrow<FeatureNotAllowedException> { userService.updateEmail(request) }
            exception.message shouldBe DIY_EMAIL_UPDATE_FEATURE_NOT_ALLOWED_EXCEPTION_MESSAGE
        }
    }

    context("::confirmEmailUpdate") {
        context("user exists and email is successfully updated") {
            val userId = userRepository.save(alicesUser).id!!
            context("token is valid") {
                //GIVEN
                val token = "validToken"
                val email = "<EMAIL>"
                val request = confirmEmailUpdateRequest {
                    this.userId = userId
                    this.token = token
                }
                coEvery { emailConfirmationTokenService.verifyToken(userId, token) } returns Unit
                coEvery { emailConfirmationTokenService.getClaimFromToken("email", token) } returns email
                //WHEN
                val response = userService.confirmEmailUpdate(request)

                //THEN
                response shouldBe Unit
                userRepository.findById(userId)!!.email shouldBe email
            }
            context("token is not valid") {
                //GIVEN
                val token = "invalidToken"
                val request = confirmEmailUpdateRequest {
                    this.userId = userId
                    this.token = token
                }
                coEvery {
                    emailConfirmationTokenService.verifyToken(
                        userId,
                        token
                    )
                } throws InvalidEmailConfirmationTokenException("invalid token")

                //WHEN-THEN
                shouldThrow<InvalidEmailConfirmationTokenException> { userService.confirmEmailUpdate(request) }
            }
        }
    }

    context("::getUserByEmail") {
        should("given an existing user in the repository when queried with a matching email then return the user") {
            // GIVEN
            userRepository.save(alicesUser)

            // WHEN
            val returnedUser = userService.getUserByEmail(alicesUser.email!!)

            // THEN
            assertSoftly {
                returnedUser!!.firstName shouldBe alicesUser.firstName
                returnedUser.lastName shouldBe alicesUser.lastName
                returnedUser.encryptedPassword shouldBe alicesUser.encryptedPassword
                returnedUser.email shouldBe alicesUser.email
                returnedUser.externalId shouldBe alicesUser.externalId.toString()
            }
        }

        should("given an existing user in the repository when queried with a mismatching email then return null") {
            // GIVEN
            userRepository.save(alicesUser)

            // WHEN
            val returnedUser = userService.getUserByEmail("I do not exist!")

            // THEN
            returnedUser shouldBe null
        }
    }

    context("::markUserEmailAsOld") {
        should("given an existing user in the repository when mark email as old then ok") {
            // GIVEN
            val savedUser = userRepository.save(alicesUser)

            // WHEN
            userService.markUserEmailAsOld(savedUser.id!!)

            // THEN
            assertSoftly {
                val returnedUser = userRepository.findById(savedUser.id!!)
                returnedUser?.email shouldBe "oldaccount" + alicesUser.email
            }
        }
    }
})

private fun assertUnauthorisedExceptionValues(ex: UnauthorisedHttpException) {
    ex.status shouldBe HttpStatusCode.Unauthorized
    ex.message shouldBe "Unauthorised"
}

private fun verifyFailedSignIn(user: User, expectedFailedAttempts: Int = 1): User {
    return user.apply {
        failedAttempts shouldBe expectedFailedAttempts
    }
}

private fun verifySuccessfulSignIn(
    user: User,
    expectedSignInCount: Int = 1,
    expectedIpAddress: String,
    currentLocalDateTime: LocalDateTime,
): User {
    return user.apply {
        assertSoftly {
            expectedSignInCount shouldBe signInCount
            // Ugh...
            if (failedAttempts != null) {
                failedAttempts shouldBe 0
            }
            currentSignInIp shouldBe expectedIpAddress
            Assertions.assertThat(currentSignInAt).isAfterOrEqualTo(currentLocalDateTime)
        }
    }
}
