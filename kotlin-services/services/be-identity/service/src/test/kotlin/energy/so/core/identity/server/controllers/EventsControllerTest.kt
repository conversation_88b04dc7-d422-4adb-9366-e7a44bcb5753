package energy.so.core.identity.server.controllers

import energy.so.core.identity.server.JUNIFER_EVENTS_BASIC_AUTH
import energy.so.core.identity.server.commons.BasicAuthVerifier
import energy.so.core.identity.server.commons.ExceptionHandler
import energy.so.core.identity.server.controllers.EventsController.eventsController
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpStatusCode
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.UserIdPrincipal
import io.ktor.server.auth.basic
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.plugins.statuspages.StatusPages
import io.ktor.server.routing.Routing
import io.ktor.server.testing.ApplicationTestBuilder
import io.ktor.server.testing.testApplication
import org.koin.core.module.Module
import org.koin.dsl.module
import org.koin.ktor.plugin.Koin

class EventsControllerTest : BehaviorSpec({

    val testUsername = "test"
    val testPassword = "test"
    val testVerifier = BasicAuthVerifier(mapOf(testUsername to testPassword))

    val moduleList: ApplicationTestBuilder.() -> Unit = {
        install(Authentication) {
            basic(JUNIFER_EVENTS_BASIC_AUTH) {
                realm = "Access to junifer events endpoints"
                validate { credentials ->
                    if (testVerifier.verify(credentials.name, credentials.password)) {
                        UserIdPrincipal(credentials.name)
                    } else {
                        null
                    }
                }
            }
        }
        install(Routing) {
            eventsController()
        }
    }

    given("the junifer events endpoint protected by BASIC authentication") {
        `when`("the endpoint is called with valid headers") {

            var response: HttpResponse? = null
            withBaseTestApplication(module { }, moduleList) {
                response = client.get("/junifer/events/auth") {
                    header("Authorization", "Basic dGVzdDp0ZXN0")
                }
            }

            then("the call should succeed") {
                response!!.status shouldBe HttpStatusCode.OK
            }
        }

        `when`("the endpoint is called with invalid headers") {

            var response: HttpResponse? = null
            withBaseTestApplication(module { }, moduleList) {
                response = client.get("/junifer/events/auth") {
                    header("Authorization", "Basic crap")
                }
            }

            then("the call should not succeed") {
                response!!.status shouldBe HttpStatusCode.Unauthorized
            }
        }
    }
})

fun <R> withBaseTestApplication(
    koinModules: Module,
    moduleList: ApplicationTestBuilder.() -> Unit = { },
    test: suspend ApplicationTestBuilder.() -> R,
) {
    testApplication {
        install(ContentNegotiation) { json() }
        install(StatusPages) {
            exception<Throwable> { call, e -> ExceptionHandler.handle(e, call) }
        }
        koinModules.let {
            install(Koin) {
                modules(it)
            }
        }
        moduleList()
        test()
    }
}