package energy.so.core.identity.server.services.meterReading

import energy.so.ac.junifer.v1.accounts.AccountsClient
import energy.so.ac.junifer.v1.accounts.account
import energy.so.ac.junifer.v1.accounts.getAccountByNumberRequest
import energy.so.commons.v2.dtos.idRequest
import energy.so.core.identity.server.fixtures.CORE_BILLING_ACCOUNT_ID
import energy.so.core.identity.server.fixtures.MATCHED_DIRECT_DEBT_AMT
import energy.so.core.identity.server.fixtures.MATCHED_EMAIL
import energy.so.core.identity.server.fixtures.MATCHED_PHONE_NUMBER
import energy.so.core.identity.server.fixtures.MATCHED_POSTCODE_NUMBERS
import energy.so.core.identity.server.fixtures.VALID_JUNIFER_ACC_NUMBER
import energy.so.core.identity.server.fixtures.emailDirectDebtAmtAuthDto
import energy.so.core.identity.server.fixtures.emailPhNumAuthDto
import energy.so.core.identity.server.fixtures.emailPostcodeAuthDto
import energy.so.core.identity.server.fixtures.phoneDirectDebtAmtAuthDto
import energy.so.core.identity.server.fixtures.phonePostcodeAuthDto
import energy.so.core.identity.server.fixtures.postcodeDirectDebtAmtAuthDto
import energy.so.core.identity.server.fixtures.postcodeDirectDebtSeasonalAmtAuthDto
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.v2.customers.address
import energy.so.customers.v2.customers.contact
import energy.so.customers.v2.customers.customer
import energy.so.payments.client.v1.PaymentsClient
import energy.so.payments.v2.paymentSchedule
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk

class MeterReadingAuthenticateServiceTest : BehaviorSpec({
    val mockCustomersClient = mockk<CustomersClient>()
    val mockPaymentsClient = mockk<PaymentsClient>()
    val mockAccountsClient = mockk<AccountsClient>()
    val authenticateService =
        MeterReadingAuthenticationService(mockCustomersClient, mockPaymentsClient, mockAccountsClient)

    given("successful request with email and phone number") {

        and("call to get customer by billing account id ok") {
            coEvery {
                mockAccountsClient.getAccountByJuniferAccountNumber(
                    getAccountByNumberRequest {
                        juniferAccountNumber = VALID_JUNIFER_ACC_NUMBER
                    }
                )
            } returns account { id = CORE_BILLING_ACCOUNT_ID.toString() }

            coEvery {
                mockCustomersClient.getCustomerByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
            } returns customer {
                currentAccountId = CORE_BILLING_ACCOUNT_ID
                primaryContact = contact {
                    email = MATCHED_EMAIL
                    phoneNumber1 = MATCHED_PHONE_NUMBER
                    address = address {
                        postcode = MATCHED_POSTCODE_NUMBERS
                    }
                }
            }

            coEvery {
                mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
            } returns paymentSchedule {
                amount = MATCHED_DIRECT_DEBT_AMT.toDouble()
            }

            `when`("::authenticateAccount") {

                then("match email and phone number") {
                    val response = authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, emailPhNumAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match email and postcode") {
                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, emailPostcodeAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match phone and postcode") {
                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, phonePostcodeAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match direct debit amount and postcode") {
                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, postcodeDirectDebtAmtAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                    coVerify {
                        mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match direct debit amount and phone number") {
                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, phoneDirectDebtAmtAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                    coVerify {
                        mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match direct debit amount and email") {
                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, emailDirectDebtAmtAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                    coVerify {
                        mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }

                then("match postcode and direct debit seasonal amount") {
                    coEvery {
                        mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
                    } returns paymentSchedule {
                        amount = MATCHED_DIRECT_DEBT_AMT.toDouble()
                        seasonalPaymentFl = true
                    }

                    val response =
                        authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, postcodeDirectDebtSeasonalAmtAuthDto)

                    response shouldBe true
                    coVerify {
                        mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                    coVerify {
                        mockPaymentsClient.getCurrentPaymentScheduleByBillingAccountId(idRequest {
                            id = CORE_BILLING_ACCOUNT_ID
                        })
                    }
                }
            }
        }
    }

    given("unsuccessful request") {
        `when`("::authenticateAccount") {
            then("mismatch email") {
                coEvery {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
                } returns customer {
                    primaryContact = contact {
                        email = "email"
                        phoneNumber1 = MATCHED_PHONE_NUMBER
                        address = address {
                            postcode = MATCHED_POSTCODE_NUMBERS
                        }
                    }
                }

                val response = authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, emailPhNumAuthDto)

                response shouldBe false
                coVerify {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                        id = CORE_BILLING_ACCOUNT_ID
                    })
                }
            }

            then("mismatch phone number") {
                coEvery {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
                } returns customer {
                    primaryContact = contact {
                        email = MATCHED_EMAIL
                        phoneNumber1 = "phone number"
                        address = address {
                            postcode = MATCHED_POSTCODE_NUMBERS
                        }
                    }
                }

                val response = authenticateService.authenticateAccount(VALID_JUNIFER_ACC_NUMBER, emailPhNumAuthDto)

                response shouldBe false
                coVerify {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                        id = CORE_BILLING_ACCOUNT_ID
                    })
                }
            }

            then("mismatch postcode") {
                coEvery {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest { id = CORE_BILLING_ACCOUNT_ID })
                } returns customer {
                    primaryContact = contact {
                        email = MATCHED_EMAIL
                        phoneNumber1 = MATCHED_PHONE_NUMBER
                        address = address {
                            postcode = "postcode"
                        }
                    }
                }

                val response = authenticateService.authenticateAccount(
                    VALID_JUNIFER_ACC_NUMBER,
                    emailPostcodeAuthDto
                )

                response shouldBe false
                coVerify {
                    mockCustomersClient.getCustomerByBillingAccountId(idRequest {
                        id = CORE_BILLING_ACCOUNT_ID
                    })
                }
            }
//TODO(SO-10329): to be updated

//            then("mismatch direct debit") {
//                coEvery {
//                    mockCustomersClient.getCustomerDetailsByAccountNumber(idRequest)
//                } returns matchedCustomerDetailsResponse
//                coEvery {
//                    mockPaymentsClient.getPaymentSchedulePeriods(v1IdRequest)
//                } returns directDebtAmtResponseMismatched
//                coEvery {
//                    mockPaymentsClient.getPaymentSchedulePeriods(IdRequest.getDefaultInstance())
//                } returns directDebtAmtResponseMismatched
//
//                val response = authenticateService.authenticateAccount(
//                    VALID_JUNIFER_ACC_NUMBER,
//                    emailDirectDebtAmtAuthDto
//                )
//
//                response shouldBe false
//                coVerify { mockCustomersClient.getCustomerDetailsByAccountNumber(idRequest) }
//                coVerify { mockPaymentsClient.getPaymentSchedulePeriods(v1IdRequest) }
//            }
        }
    }
})
