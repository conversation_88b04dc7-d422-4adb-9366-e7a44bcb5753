package energy.so.core.identity.server.services

import aws.sdk.kotlin.services.connect.ConnectClient
import energy.so.core.identity.server.aws.AwsConnectConfig
import energy.so.core.identity.server.database.repositories.AwsConnectMetricsRepository
import energy.so.core.identity.server.fixtures.AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS
import energy.so.core.identity.server.fixtures.AWS_CONNECT_METRICS_RESPONSE_JSON
import energy.so.core.identity.server.fixtures.AWS_CONNECT_METRIC_JOOQ_ENTITY
import energy.so.core.identity.server.fixtures.AWS_CONNECT_QUEUES
import energy.so.core.identity.server.fixtures.AWS_CURRENT_METRICS_RESPONSE
import energy.so.core.identity.server.fixtures.GET_CURRENT_METRIC_DATA_REQUEST
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.mockk

class AwsConnectMetricServiceTest : BehaviorSpec({

    val awsConnectMetricsRepository = mockk<AwsConnectMetricsRepository>()
    val awsConnectClient = mockk<ConnectClient>()
    val awsConnectConfig = mockk<AwsConnectConfig>()

    val awsConnectMetricService = AwsConnectMetricService(
        awsConnectMetricsRepository = awsConnectMetricsRepository,
        awsConnectClient = awsConnectClient,
        awsConnectConfig = awsConnectConfig
    )

    given("save aws connect metrics") {
        coEvery { awsConnectConfig.getCurrentMetricDataRequest() } returns GET_CURRENT_METRIC_DATA_REQUEST
        coEvery { awsConnectConfig.queues } returns AWS_CONNECT_QUEUES
        coEvery { awsConnectConfig.recordTtlSeconds } returns AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS

        and("metrics present") {
            coEvery { awsConnectClient.getCurrentMetricData(GET_CURRENT_METRIC_DATA_REQUEST) } returns AWS_CURRENT_METRICS_RESPONSE
            coEvery { awsConnectMetricsRepository.save(AWS_CONNECT_METRICS_RESPONSE_JSON) } returns AWS_CONNECT_METRIC_JOOQ_ENTITY
            coJustRun { awsConnectMetricsRepository.deleteOldRecords(AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS) }

            `when`("saveConnectMetrics") {
                awsConnectMetricService.saveConnectMetrics()

                then("save is called") {
                    coVerify { awsConnectClient.getCurrentMetricData(GET_CURRENT_METRIC_DATA_REQUEST) }
                    coVerify { awsConnectMetricsRepository.deleteOldRecords(AWS_CONNECT_CONFIG_RECORD_TTL_SECONDS) }
                    coVerify { awsConnectMetricsRepository.save(AWS_CONNECT_METRICS_RESPONSE_JSON) }
                }
            }
        }
    }

})
