package energy.so.core.identity.server.repositories

import energy.so.commons.model.tables.pojos.RefreshToken
import energy.so.commons.model.tables.references.REFRESH_TOKENS
import energy.so.core.identity.server.database.repositories.JooqRefreshTokenRepository
import energy.so.database.test.installDatabase
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockkStatic
import java.time.LocalDateTime
import java.util.UUID
import org.jooq.Records.mapping
import org.junit.jupiter.api.DisplayName

@DisplayName("JooqRefreshTokenRepository")
class JooqRefreshTokenRepositoryTest : ShouldSpec({
    val db = installDatabase()
    val subject = JooqRefreshTokenRepository(db)

    val tokenExpiry = LocalDateTime.of(1, 1, 1, 1, 1)
    val refreshToken = RefreshToken(
        groupId = UUID.randomUUID(),
        expiryDatetime = tokenExpiry,
    )

    context("::create") {
        should("persists the RefreshToken") {

            mockkStatic(LocalDateTime::class)
            every { LocalDateTime.now() } answers
                    { LocalDateTime.of(2022, 6, 30, 15, 0, 0) }

            val result = subject.create(refreshToken)
            val resultFromDb = db.selectFrom(REFRESH_TOKENS)
                .where(REFRESH_TOKENS.GROUP_ID.eq(refreshToken.groupId))
                .fetchOne(mapping(::RefreshToken))

            assertSoftly {
                result.groupId shouldBe refreshToken.groupId
                result.expiryDatetime shouldBe refreshToken.expiryDatetime
                result.createdAt shouldNotBe null
                result.updatedAt shouldNotBe null
                result shouldBe resultFromDb
            }
            clearAllMocks()
        }
    }

    context("::replace") {
        should("return 0 if the token does not exist") {
            val result = subject.replace(refreshToken, tokenExpiry)

            result shouldBe 0
        }

        context("if a token does exist") {
            val oldToken = refreshToken
            beforeTest {
                subject.create(refreshToken)
            }

            should("return value 1 (as in, 1 row updated) and update token in database with new expiry") {
                val newToken = refreshToken.copy(expiryDatetime = oldToken.expiryDatetime?.plusDays(1))

                val result = subject.replace(newToken, oldToken.expiryDatetime!!)
                val existingTokenInDb = db.selectFrom(REFRESH_TOKENS)
                    .where(REFRESH_TOKENS.GROUP_ID.eq(oldToken.groupId))
                    .fetchOne(mapping(::RefreshToken))

                assertSoftly {
                    result shouldBe 1
                    existingTokenInDb?.expiryDatetime shouldBe newToken.expiryDatetime
                    existingTokenInDb?.updatedAt shouldNotBe null
                }
            }
        }
    }
    context("::delete") {
        context("token does not exist") {
            // THEN
            subject.delete(refreshToken) shouldBe 0
        }

        context("token exists") {
            // GIVEN
            subject.create(refreshToken)

            // WHEN - THEN
            subject.delete(refreshToken) shouldBe 1
        }

    }
})
