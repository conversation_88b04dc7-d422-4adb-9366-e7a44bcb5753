Feature: Token Validate
  Background:
    * url url

  Sc<PERSON>rio: Testing token validate endpoint with a valid Authorization header param
    Given path '/tokens/validate'
    And header Authorization = 'dsgsadgerrfsadfasdfgderg'
    When method POST
    Then status 200

  Scenario: Testing token validate endpoint with missing Authorization header param
    Given path '/tokens/validate'
    When method POST
    Then status 401

  Scenario: Testing token validate-if-present endpoint with a valid Authorization header param
    Given path '/tokens/validate-if-present'
    And header Authorization = 'dsgsadgerrfsadfasdfgderg'
    When method POST
    Then status 200

  Scenario: Testing token validate-if-present endpoint with missing Authorization header param
    Given path '/tokens/validate-if-present'
    When method POST
    Then status 200