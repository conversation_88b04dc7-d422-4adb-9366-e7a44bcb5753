Feature: Token Refresh
  Background:
    * url url

  Sc<PERSON>rio: Testing token refresh endpoint with valid refresh_token
    Given path '/tokens/refresh'
    And request { refresh_token: 'refresh_token_value'}
    When method POST
    Then status 200
    And match $ contains {accessToken:"#notnull"}
    And match $ contains {refreshToken:"#notnull"}
    And match $ contains {expiresAt:"#notnull"}

  Scenario: Testing token refresh endpoint with missing refresh_token
    Given path '/tokens/refresh'
    When method POST
    Then status 401