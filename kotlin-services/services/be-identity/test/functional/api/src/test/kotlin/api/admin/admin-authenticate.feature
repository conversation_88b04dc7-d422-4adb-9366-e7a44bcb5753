Feature: Admin Authenticate
  Background:
    * url url

  Scenario: Testing admin authentication endpoint with valid username and password pair
    Given path '/admins/authenticate'
    And header Content-Type = 'application/json'
    And request { username: '<PERSON>' , password: 'secret'}
    When method POST
    Then status 200
    And match $ contains {accessToken:"#notnull"}
    And match $ contains {refreshToken:"#notnull"}
    And match $ contains {expiresAt:"#notnull"}

  Scenario: Testing admin authentication endpoint with invalid username and password pair
    Given path '/admins/authenticate'
    And header Content-Type = 'application/json'
    And request { username: 'InvalidUser' , password: 'InvalidPassword'}
    When method POST
    Then status 400


  Scenario: Testing admin authentication endpoint with missing username and password pair
    Given path '/admins/authenticate'
    And header Content-Type = 'application/json'
    When method POST
    Then status 500