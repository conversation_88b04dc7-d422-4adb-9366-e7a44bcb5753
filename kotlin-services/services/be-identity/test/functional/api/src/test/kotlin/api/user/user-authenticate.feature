Feature: User Authenticate
  Background:
    * url url

  Scenario: Testing user authentication endpoint with valid username and password pair
    Given path '/users/authenticate'
    And request { username: 'User' , password: 'secret'}
    When method POST
    Then status 200
    And match $ contains {accessToken:"#notnull"}
    And match $ contains {refreshToken:"#notnull"}
    And match $ contains {expiresAt:"#notnull"}

  Scenario: Testing user authentication endpoint with invalid username and password pair
    Given path '/users/authenticate'
    And header Content-Type = 'application/json'
    And request { username: 'InvalidUser' , password: 'InvalidPassword'}
    When method POST
    Then status 400


  Scenario: Testing user authentication endpoint with missing username and password pair
    Given path '/users/authenticate'
    And header Content-Type = 'application/json'
    When method POST
    Then status 500