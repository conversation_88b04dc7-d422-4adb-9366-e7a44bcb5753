plugins {
    id("energy.so.be-commons-conventions-jib")
    kotlin("jvm")
    java
}

jib {
    from {
        image = "flyway/flyway:8.5.5"
    }
    container {
        entrypoint = listOf("flyway", "-locations=filesystem:./sql", "-connectRetries=1", "migrate")
    }
    extraDirectories {
        paths {
            path {
                setFrom(project(":be-comms-hub").projectDir.resolve("src/main/resources/db/migration"))
                into = "/flyway/sql"
                excludes.add("*.xml")
            }
        }
    }
}