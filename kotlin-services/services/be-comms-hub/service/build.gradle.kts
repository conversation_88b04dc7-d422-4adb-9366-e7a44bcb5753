plugins {
    id("energy.so.be-commons-conventions-microservice")
    id("energy.so.conventions.jooq")
    id("energy.so.conventions.spring")
}

dependencies {
    implementation(libs.kafka.avro.serde)

    implementation(project(":be-commons-kafka"))
    implementation(project(":be-email-client"))
    implementation(project(":be-comms-hub-api"))
    implementation(project(":be-customers-api"))
    implementation(project(":be-tickets-api"))
    implementation(project(":be-communications-api"))
    implementation(project(":be-commons-storage"))
    implementation("org.springframework.kafka:spring-kafka")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.17.0")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-avro:2.17.0")
    implementation(libs.jackson.datatype.jsr310)
    implementation(libs.ktor.serialization.kotlinx)
    implementation(libs.ktor.serialization)
    implementation("org.springframework.boot:spring-boot-starter-quartz")
    implementation("org.quartz-scheduler:quartz")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")

    testImplementation("org.springframework.kafka:spring-kafka-test") {
        exclude("ch.qos.logback", "logback-classic")
        exclude("ch.qos.logback", "logback-core")
    }

    testFixturesImplementation(project(":be-email-client"))
    testFixturesImplementation(libs.kafka.avro.serde)
    testFixturesImplementation(project(":be-comms-hub-api"))
    testFixturesImplementation(project(":be-customers-api"))
    testFixturesImplementation(project(":be-tickets-api"))
    testFixturesImplementation(project(":be-commons-database"))
    testFixturesImplementation(project(":be-communications-api"))
    testFixturesImplementation(libs.ktor.serialization.kotlinx)
    testFixturesImplementation(libs.ktor.serialization)
    testFixturesImplementation(libs.jooq.core)
}

application {
    mainClass.set("energy.so.comms.hub.CommsHubServer.kt")
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            listOf(
                "src/main/kotlin/energy/so/comms/hub/CommsHubConfiguration.kt",
                "src/main/kotlin/energy/so/comms/hub/config/**",
                "src/main/kotlin/energy/so/comms/hub/extensions/**",
                "src/main/kotlin/energy/so/comms/hub/common/models/awsconnect/CTREvent.kt"
            )
        )
    }
}
