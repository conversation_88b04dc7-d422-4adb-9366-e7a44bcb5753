package energy.so.comms.hub.cases_v2.extensions

import energy.so.comms.hub.cases.extensions.AWS_CONNECT_SOURCE
import energy.so.comms.hub.cases_v2.integrations.models.Case
import energy.so.comms.hub.cases_v2.integrations.models.CaseFieldValue
import energy.so.comms.hub.cases_v2.integrations.models.FieldValue
import energy.so.customers.v2.customers.Contact
import energy.so.customers.v2.customers.Customer
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset

class CaseExtensionsTest : BehaviorSpec({

    given("a case with complete fields and a customer") {
        val caseId = "case-123"
        val createdDateTime = LocalDateTime.of(2023, 1, 1, 12, 0)
        val fields = mapOf(
            "source" to CaseFieldValue(FieldValue(stringValue = "Phone")),
            "assigned_agent_name" to CaseFieldValue(FieldValue(stringValue = "Agent Smith")),
            "assigned_queue_name" to CaseFieldValue(FieldValue(stringValue = "Support Team")),
            "complaint_type" to CaseFieldValue(FieldValue(stringValue = "Billing")),
            "status" to CaseFieldValue(FieldValue(stringValue = "Open")),
            "complaint_status" to CaseFieldValue(FieldValue(stringValue = "Complaint Raised")),
            "complaint_subject" to CaseFieldValue(FieldValue(stringValue = "Billing Issue")),
            "complaint_subject_2" to CaseFieldValue(FieldValue(stringValue = "Incorrect Charge")),
            "complaints_pref_cust_contact_meth" to CaseFieldValue(FieldValue(stringValue = "Email")),
            "complaints_goodwill_amount" to CaseFieldValue(FieldValue(doubleValue = 100.0)),
            "account_number" to CaseFieldValue(FieldValue(stringValue = "ACC123456")),
            "complaint_ombudsman_status_decision_issued" to CaseFieldValue(FieldValue(booleanValue = true)),
            "last_updated_datetime" to CaseFieldValue(FieldValue(stringValue = "2025-05-18T10:08:43.629001529Z"))
        )
        
        val case = Case(
            externalId = caseId,
            templateId = "template-1",
            createdDateTime = createdDateTime,
            fields = fields
        )
        
        val customer = Customer.newBuilder()
            .setId(123L)
            .setFirstName("John")
            .setLastName("Doe")
            .setPrimaryContact(
                Contact.newBuilder()
                    .setEmail("<EMAIL>")
                    .build()
            )
            .build()
        
        `when`("converting to ExternalComplaint") {
            val complaint = case.toExternalComplaint(customer,false)
            
            then("all fields should be mapped correctly") {
                complaint.shouldBeDeleted shouldBe false
                complaint.complaintId shouldBe caseId
                complaint.complaintTicketIdStr shouldBe caseId
                complaint.ombudsmanCase shouldBe true
                complaint.agentName shouldBe "Agent Smith"
                complaint.team shouldBe "Support Team"
                complaint.agentEmail shouldBe null
                complaint.contactEmail shouldBe "<EMAIL>"
                complaint.channel shouldBe "Phone"
                complaint.type shouldBe "Billing"
                complaint.dateRaised shouldBe createdDateTime.toInstant(ZoneOffset.UTC)
                complaint.ofgemMappedComplaintCategory shouldBe "Billing Issue"
                complaint.internalComplaintCategory shouldBe "Incorrect Charge"
                complaint.complaintsCustPrefContactMeth shouldBe "Email"
                complaint.complaintsGoodwillAmount shouldBe "100"
                complaint.billingAccountNumber shouldBe "ACC123456"
                complaint.source shouldBe AWS_CONNECT_SOURCE
                complaint.d1SignpostingCommsSent shouldBe false
                complaint.d56SignpostingCommsSent shouldBe false
                complaint.deadlockCommsSent shouldBe false
                complaint.createdAt shouldBe createdDateTime.toInstant(ZoneOffset.UTC)
                complaint.status shouldBe "Complaint open"
                complaint.subStatus shouldBe "Under investigation"
                complaint.updatedAt shouldBe Instant.parse("2025-05-18T10:08:43.629001529Z")
            }
        }
    }
    
    given("a case with minimal fields and no customer") {
        val caseId = "case-123"
        val createdDateTime = LocalDateTime.of(2023, 1, 1, 12, 0)
        val fields = mapOf(
            "source" to CaseFieldValue(FieldValue(stringValue = "Phone")),
            "assigned_agent_name" to CaseFieldValue(FieldValue(stringValue = "Agent Smith")),
            "status" to CaseFieldValue(FieldValue(stringValue = "Open"))
        )
        
        val case = Case(
            externalId = caseId,
            templateId = "template-1",
            createdDateTime = createdDateTime,
            fields = fields
        )
        
        `when`("converting to ExternalComplaint") {
            val complaint = case.toExternalComplaint(null, false)
            
            then("it should handle null customer gracefully") {
                complaint.complaintId shouldBe caseId
                complaint.agentName shouldBe "Agent Smith"
                complaint.contactEmail shouldBe null
                complaint.dateRaised shouldBe createdDateTime.toInstant(ZoneOffset.UTC)
            }
        }
    }
    
    given("a case with empty fields") {
        val caseId = "case-123"
        val createdDateTime = LocalDateTime.of(2023, 1, 1, 12, 0)
        
        val case = Case(
            externalId = caseId,
            templateId = "template-1",
            createdDateTime = createdDateTime,
            fields = mapOf(
                "source" to CaseFieldValue(FieldValue(stringValue = "Phone")),
            )
        )
        
        `when`("converting to ExternalComplaint") {
            val complaint = case.toExternalComplaint(null,false)
            
            then("it should provide default values for missing fields") {
                complaint.complaintId shouldBe caseId
                complaint.agentName shouldBe null
                complaint.team shouldBe null
                complaint.type shouldBe null
                complaint.category1 shouldBe null
            }
        }
    }

    given("a complaint case") {
        val caseId = "case-123"
        val createdDateTime = LocalDateTime.of(2023, 1, 1, 12, 0)
        val originalCreationDate = "2025-04-23T11:45:06.254338342Z"

        val fields = mapOf(
            "source" to CaseFieldValue(FieldValue(stringValue = "Phone")),
            "original_creation_date" to CaseFieldValue(FieldValue(stringValue = originalCreationDate))
        )

        val case = Case(
            externalId = caseId,
            templateId = "template-1",
            createdDateTime = createdDateTime,
            fields = fields
        )

        `when`("converting to ExternalComplaint") {
            val complaint = case.toExternalComplaint(null, false)

            then("creation date and date raised are mapped correctly") {
                complaint.createdAt shouldBe  createdDateTime.toInstant(ZoneOffset.UTC)
                complaint.dateRaised shouldBe  Instant.parse(originalCreationDate)
            }
        }
    }

    given("a case which has unassigned values") {
        val caseId = "case-123"
        val createdDateTime = LocalDateTime.of(2023, 1, 1, 12, 0)

        val fields = mapOf(
            "source" to CaseFieldValue(FieldValue(stringValue = "-")),
            "complaint_subject" to CaseFieldValue(FieldValue(stringValue = "-")),
        )

        val case = Case(
            externalId = caseId,
            templateId = "template-1",
            createdDateTime = createdDateTime,
            fields = fields
        )

        `when`("converting to ExternalComplaint") {
            val complaint = case.toExternalComplaint(null, false)

            then("unassigned values are mapped to null") {
                complaint.channel shouldBe  null
                complaint.ofgemMappedComplaintCategory shouldBe  null
            }
        }
    }
})