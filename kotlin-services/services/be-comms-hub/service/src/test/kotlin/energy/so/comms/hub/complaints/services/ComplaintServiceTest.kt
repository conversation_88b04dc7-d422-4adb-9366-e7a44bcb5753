package energy.so.comms.hub.complaints.services

import com.google.protobuf.Empty
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.comms.hub.communications.config.CommunicationTemplate
import energy.so.comms.hub.complaints.database.repositories.ComplaintRepository
import energy.so.comms.hub.common.database.repositories.OutboxItemRepository
import energy.so.comms.hub.complaints.extensions.toModel
import energy.so.comms.hub.fixtures.CommunicationPrecannedData.CUSTOMER_ID
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.ACCOUNT_ID
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.COMPLAINT_ID
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.OUTBOX_ITEM_ID
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.billingAccount
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildTestComplaintMetadata
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildTestExternalComplaint
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.closedComplaintModel
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.complaintFilter
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.complaintSummary
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.customer
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.d1EmailOutboxItem
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.d56EmailOutboxItem
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.emptyComplaintFilter
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.complaintModel
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.overdueEmailOutboxItem
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.protoComplaintFilter
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.protoComplaintSummary
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.recentEmailOutboxItem
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.updateComplaintData
import energy.so.comms.hub.communications.integrations.clients.FreshdeskClient
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus
import energy.so.comms.hub.complaints.models.Complaint
import energy.so.comms.hub.common.models.OutboxItem
import energy.so.comms.hub.common.models.OutboxOperationStatus
import energy.so.comms.hub.common.models.SendEmailData
import energy.so.comms.hub.common.models.UpdateComplaintData
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationDto
import energy.so.customers.calendarholiday.v2.calendarHoliday
import energy.so.customers.calendarholiday.v2.calendarHolidayList
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.billingaccounts.BlockingBillingAccountsClient
import energy.so.customers.client.v2.calendarholiday.BlockingCalendarHolidayClient
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.unmockkStatic
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import kotlinx.serialization.json.Json
import energy.so.comms.hub.v1.complaint.ComplaintDto.ComplaintFilter as ProtoComplaintFilter

class ComplaintServiceTest : BehaviorSpec({

    val mockOutboxItemRepository = mockk<OutboxItemRepository>()
    val mockComplaintRepository = mockk<ComplaintRepository>()
    val mockFreshdeskClient = mockk<FreshdeskClient>()
    val mockCommunicationClient = mockk<CommunicationClient>()
    val mockCalendarHolidayClient = mockk<BlockingCalendarHolidayClient>()
    val mockMetadataService = mockk<ComplaintMetadataService>()
    val mockCustomerClient = mockk<CustomersClient>()
    val mockBillingAccountClient = mockk<BlockingBillingAccountsClient>()

    val sut = ComplaintService(
        initialDelay = 1000,
        maxRetries = 5,
        complaintRepository = mockComplaintRepository,
        freshdeskClient = mockFreshdeskClient,
        outboxItemRepository = mockOutboxItemRepository,
        communicationClient = mockCommunicationClient,
        calendarHolidayClient = mockCalendarHolidayClient,
        communicationTemplates = CommunicationTemplate(
            "d1_communication",
            "d56_communication",
            "recent_complaint_communication",
            "overdue_complaint_communication"
        ),
        metadataService = mockMetadataService,
        customerClient = mockCustomerClient,
        billingAccountsClient = mockBillingAccountClient,
    )

    afterTest { clearAllMocks() }

    given("a new complaint is received") {

        and("is marked to be deleted") {
            val complaint = buildTestExternalComplaint(shouldBeRemoved = true)
            every {
                mockComplaintRepository.deleteComplaintByExternalId(complaint.complaintTicketId.toString())
            } returns (1)

            every {
                mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString())
            } returns (Complaint(updatedAt = LocalDateTime.of(2023,1,1,0,0,0)))

            `when`("the complaint is processed") {
                sut.processComplaint(complaint)

                then("the complaint is removed") {
                    verify {
                        mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString())
                        mockComplaintRepository.deleteComplaintByExternalId(complaint.complaintTicketId.toString())
                    }
                }
            }
        }

        and("is not the latest version of the complaint") {
            val complaint = buildTestExternalComplaint()

            every {
                mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString())
            } returns (Complaint(updatedAt = LocalDateTime.of(2025,1,1,0,0,0)))

            `when`("the complaint is processed") {
                sut.processComplaint(complaint)

                then("the complaint is not saved") {
                    verify {
                        mockComplaintRepository.saveComplaint(any()) wasNot Called
                        mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString() )
                    }
                }
            }
        }
    }

    given("update on complaint is received and status changed to closed") {

        val now = LocalDateTime.of(2022, 1, 1, 1, 1, 1)
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns now

        val complaint = buildTestExternalComplaint(ComplaintStatus.COMPLAINT_CLOSED.value)
        val complaintMetadata = buildTestComplaintMetadata()
        val resolvedStatusComplaint =
            complaint.toModel(complaintMetadata)
                .copy(
                    status = ComplaintStatus.COMPLAINT_CLOSED.value,
                    closedAt = now
                )

        every { mockMetadataService.resolveComplaintMetadata(complaint.contactEmail) } returns complaintMetadata
        every { mockComplaintRepository.saveComplaint(resolvedStatusComplaint) } returns Complaint()

        every { mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString()) } returns Complaint(
            status = ComplaintStatus.OMBUDSMAN_COMPLAINT.value
        )

        `when`("the complaint is saved") {
            sut.saveComplaint(complaint)

            then("the complaint should be saved to the repository and closedAt time shall be set to now") {
                verify { mockComplaintRepository.saveComplaint(resolvedStatusComplaint) }
            }

        }
        unmockkStatic(LocalDateTime::class)
    }

    given("new complaint created with status closed") {
        val now = LocalDateTime.of(2022, 1, 1, 1, 1, 1)
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns now

        val complaint = buildTestExternalComplaint(ComplaintStatus.COMPLAINT_CLOSED.value)
        val complaintMetadata = buildTestComplaintMetadata()
        val resolvedStatusComplaint =
            complaint.toModel(complaintMetadata)
                .copy(
                    status = ComplaintStatus.COMPLAINT_CLOSED.value,
                    closedAt = now
                )

        every { mockMetadataService.resolveComplaintMetadata(complaint.contactEmail) } returns complaintMetadata
        every { mockComplaintRepository.saveComplaint(resolvedStatusComplaint) } returns Complaint()

        every { mockComplaintRepository.getComplaintByExternalId(complaint.complaintTicketId.toString()) } returns null

        `when`("the complaint is saved") {
            sut.saveComplaint(complaint)

            then("the complaint should be saved to the repository and closedAt time shall be set to now") {
                verify { mockComplaintRepository.saveComplaint(resolvedStatusComplaint) }
            }

        }
        unmockkStatic(LocalDateTime::class)
    }

    given("pending outbox items") {

        and("pending outbox item is SEND_EMAIL type") {

            and("d1 email sending ok") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(d1EmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery {
                    mockCommunicationClient.sendCommunicationTemplate(match { template ->
                        template.communicationName == "d1_communication"
                    })
                } returns CommunicationDto(1, 1)
                val capturingSlot = slot<UpdateComplaintData>()
                every {
                    mockComplaintRepository.updateComplaintComms(
                        COMPLAINT_ID, capture(capturingSlot)
                    )
                } returns OUTBOX_ITEM_ID
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be sent, complaint should be updated and new outbox item should be created") {
                        coVerify { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) }
                        coVerify { mockBillingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                        coVerify {
                            mockCommunicationClient.sendCommunicationTemplate(match { template ->
                                template.communicationName == "d1_communication"
                            })
                        }
                        verify {
                            mockComplaintRepository.updateComplaintComms(
                                COMPLAINT_ID, capture(capturingSlot)
                            )
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                            )
                        }
                        assertSoftly {
                            capturingSlot.captured.ticketId shouldBe updateComplaintData.ticketId
                            capturingSlot.captured.complaintD1LetterSent shouldBe true
                            capturingSlot.captured.complaintD56LetterSent shouldBe false
                            capturingSlot.captured.complaintRecentLetterSent shouldBe false
                            capturingSlot.captured.complaintOverdueLetterSent shouldBe false
                        }
                    }
                }
            }

            and("recent email sending ok") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(recentEmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery {
                    mockCommunicationClient.sendCommunicationTemplate(match { template ->
                        template.communicationName == "d1_communication"
                    })
                } returns CommunicationDto(1, 1)
                val capturingSlot = slot<UpdateComplaintData>()
                every {
                    mockComplaintRepository.updateComplaintComms(
                        COMPLAINT_ID, capture(capturingSlot)
                    )
                } returns OUTBOX_ITEM_ID
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be sent, complaint should be updated and new outbox item should be created") {
                        coVerify { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) }
                        coVerify { mockBillingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                        coVerify {
                            mockCommunicationClient.sendCommunicationTemplate(match { template ->
                                template.communicationName == "d1_communication"
                            })
                        }
                        verify {
                            mockComplaintRepository.updateComplaintComms(
                                COMPLAINT_ID, capture(capturingSlot)
                            )
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                            )
                        }
                        assertSoftly {
                            capturingSlot.captured.ticketId shouldBe updateComplaintData.ticketId
                            capturingSlot.captured.complaintD1LetterSent shouldBe false
                            capturingSlot.captured.complaintD56LetterSent shouldBe false
                            capturingSlot.captured.complaintRecentLetterSent shouldBe true
                            capturingSlot.captured.complaintOverdueLetterSent shouldBe false
                        }
                    }
                }
            }

            and("d56 email sending ok") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(d56EmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery {
                    mockCommunicationClient.sendCommunicationTemplate(match { template ->
                        template.communicationName == "d56_communication"
                    })
                } returns CommunicationDto(1, 1)
                val capturingSlot = slot<UpdateComplaintData>()
                every {
                    mockComplaintRepository.updateComplaintComms(
                        COMPLAINT_ID, capture(capturingSlot)
                    )
                } returns OUTBOX_ITEM_ID
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be sent, complaint should be updated and new outbox item should be created") {
                        coVerify { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) }
                        coVerify { mockBillingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                        coVerify {
                            mockCommunicationClient.sendCommunicationTemplate(match { template ->
                                template.communicationName == "d56_communication"
                            })
                        }
                        verify {
                            mockComplaintRepository.updateComplaintComms(
                                COMPLAINT_ID, capture(capturingSlot)
                            )
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                            )
                        }
                        assertSoftly {
                            capturingSlot.captured.ticketId shouldBe updateComplaintData.ticketId
                            capturingSlot.captured.complaintD1LetterSent shouldBe false
                            capturingSlot.captured.complaintD56LetterSent shouldBe true
                            capturingSlot.captured.complaintRecentLetterSent shouldBe false
                            capturingSlot.captured.complaintOverdueLetterSent shouldBe false
                        }
                    }
                }
            }

            and("overdue email sending ok") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(overdueEmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery {
                    mockCommunicationClient.sendCommunicationTemplate(match { template ->
                        template.communicationName == "d56_communication"
                    })
                } returns CommunicationDto(1, 1)
                val capturingSlot = slot<UpdateComplaintData>()
                every {
                    mockComplaintRepository.updateComplaintComms(
                        COMPLAINT_ID, capture(capturingSlot)
                    )
                } returns OUTBOX_ITEM_ID
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be sent, complaint should be updated and new outbox item should be created") {
                        coVerify { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) }
                        coVerify { mockBillingAccountClient.getBillingAccountById(idRequest { id = ACCOUNT_ID }) }
                        coVerify {
                            mockCommunicationClient.sendCommunicationTemplate(match { template ->
                                template.communicationName == "d56_communication"
                            })
                        }
                        verify {
                            mockComplaintRepository.updateComplaintComms(
                                COMPLAINT_ID, capture(capturingSlot)
                            )
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                            )
                        }
                        assertSoftly {
                            capturingSlot.captured.ticketId shouldBe updateComplaintData.ticketId
                            capturingSlot.captured.complaintD1LetterSent shouldBe false
                            capturingSlot.captured.complaintD56LetterSent shouldBe false
                            capturingSlot.captured.complaintRecentLetterSent shouldBe false
                            capturingSlot.captured.complaintOverdueLetterSent shouldBe true
                        }
                    }
                }
            }

            and("email sending fails the first time") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(d1EmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery { mockCommunicationClient.sendCommunicationTemplate(any()) } throws Exception("cannot send email") andThen CommunicationDto(
                    1, 1
                )
                val capturingSlot = slot<UpdateComplaintData>()
                every {
                    mockComplaintRepository.updateComplaintComms(
                        COMPLAINT_ID, capture(capturingSlot)
                    )
                } returns OUTBOX_ITEM_ID
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be retried, complaint should be updated and new outbox item should be created") {
                        coVerify(exactly = 2) { mockCommunicationClient.sendCommunicationTemplate(any()) }
                        verify {
                            mockComplaintRepository.updateComplaintComms(
                                COMPLAINT_ID, capture(capturingSlot)
                            )
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.SUCCESS
                            )
                        }
                        assertSoftly {
                            capturingSlot.captured.ticketId shouldBe updateComplaintData.ticketId
                            capturingSlot.captured.complaintD1LetterSent shouldBe updateComplaintData.complaintD1LetterSent
                            capturingSlot.captured.complaintD56LetterSent shouldBe updateComplaintData.complaintD56LetterSent
                        }
                    }
                }
            }

            and("email sending fails every time") {
                every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
                every { mockOutboxItemRepository.fetchPendingOutboxItems() } returns listOf(d1EmailOutboxItem)
                every { mockComplaintRepository.getComplaintById(COMPLAINT_ID) } returns complaintModel
                coEvery { mockCustomerClient.getCustomerById(idRequest { id = CUSTOMER_ID }) } returns customer
                every {
                    mockBillingAccountClient.getBillingAccountById(idRequest {
                        id = ACCOUNT_ID
                    })
                } returns billingAccount
                coEvery { mockCommunicationClient.sendCommunicationTemplate(any()) } throws Exception("cannot send email")
                every {
                    mockOutboxItemRepository.updateOutboxItemStatus(
                        OUTBOX_ITEM_ID, OutboxOperationStatus.FAILURE
                    )
                } returns OUTBOX_ITEM_ID

                `when`("process pending outbox items") {

                    sut.processPendingOutboxItems()

                    then("email should be retried and outbox item should be marked as failed") {
                        coVerify(exactly = 5) { mockCommunicationClient.sendCommunicationTemplate(any()) }
                        verify(exactly = 0) {
                            mockComplaintRepository.updateComplaintComms(any(), any())
                        }
                        verify {
                            mockOutboxItemRepository.updateOutboxItemStatus(
                                OUTBOX_ITEM_ID, OutboxOperationStatus.FAILURE
                            )
                        }
                    }
                }
            }
        }
    }

    given("one working day old complaint having created_at same as date_raised") {

        `when`("process complaints") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(complaintModel)
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            sut.processComplaints()

            then("new SEND_EMAIL outbox item entry is created with shouldSendD1Communication true") {
                verify {
                    mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                        LocalDate.now().minusDays(56),
                        listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                    )
                }
                verify { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                assertSoftly {
                    val sendEmailData =
                        capturingSlot.captured.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                    sendEmailData?.shouldSendD1Communication shouldBe true
                    sendEmailData?.shouldSendD56Communication shouldBe false
                }
            }
        }

        and("current day is holiday") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList {
                calendarHolidays.add(calendarHoliday {
                    holidayDate = LocalDate.now().toTimestamp()
                })
            }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(complaintModel)
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            `when`("process complaints") {

                sut.processComplaints()

                then("no entry is saved in outbox table") {
                    verify {
                        mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                            LocalDate.now().minusDays(56),
                            listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                        )
                    }
                    verify(exactly = 0) { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                }
            }
        }
    }

    given("56 days old complaint having created_at same as date_raised") {

        `when`("process complaints") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(
                complaintModel.copy(
                    dateRaised = LocalDateTime.now().minusDays(56),
                    createdAt = LocalDateTime.now().minusDays(56)
                )
            )
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            sut.processComplaints()

            then("new SEND_EMAIL outbox item entry is created with shouldSendD56Communication true") {
                verify {
                    mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                        LocalDate.now().minusDays(56),
                        listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                    )
                }
                verify { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                assertSoftly {
                    val sendEmailData =
                        capturingSlot.captured.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                    sendEmailData?.shouldSendD1Communication shouldBe false
                    sendEmailData?.shouldSendD56Communication shouldBe true
                }
            }
        }
    }

    given("one working day old complaint having created_at one day apart from date_raised") {

        `when`("process complaints") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(complaintModel.copy(createdAt = LocalDateTime.now()))
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            sut.processComplaints()

            then("new SEND_EMAIL outbox item entry is created with shouldSendD1Communication true") {
                verify {
                    mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                        LocalDate.now().minusDays(56),
                        listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                    )
                }
                verify { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                assertSoftly {
                    val sendEmailData =
                        capturingSlot.captured.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                    sendEmailData?.shouldSendD1Communication shouldBe true
                    sendEmailData?.shouldSendD56Communication shouldBe false
                    sendEmailData?.shouldSendRecentCommunication shouldBe false
                    sendEmailData?.shouldSendOverdueCommunication shouldBe false
                }
            }
        }

        and("current day is holiday") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList {
                calendarHolidays.add(calendarHoliday {
                    holidayDate = LocalDate.now().toTimestamp()
                })
            }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(complaintModel)
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            `when`("process complaints") {

                sut.processComplaints()

                then("no entry is saved in outbox table") {
                    verify {
                        mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                            LocalDate.now().minusDays(56),
                            listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                        )
                    }
                    verify(exactly = 0) { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                }
            }
        }
    }

    given("complaint created with date_raised 5 days in the past") {

        `when`("process complaints") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(
                complaintModel.copy(
                    createdAt = LocalDateTime.now(),
                    dateRaised = LocalDateTime.now().minusDays(5)
                )
            )
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            sut.processComplaints()

            then("new SEND_EMAIL outbox item entry is created with shouldSendRecentCommunication true") {
                verify {
                    mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                        LocalDate.now().minusDays(56),
                        listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                    )
                }
                verify { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                assertSoftly {
                    val sendEmailData =
                        capturingSlot.captured.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                    sendEmailData?.shouldSendD1Communication shouldBe false
                    sendEmailData?.shouldSendD56Communication shouldBe false
                    sendEmailData?.shouldSendRecentCommunication shouldBe true
                    sendEmailData?.shouldSendOverdueCommunication shouldBe false
                }
            }
        }
    }

    given("complaint created with date_raised 60 days in the past") {

        `when`("process complaints") {
            every { mockCalendarHolidayClient.getAllCalendarHolidays(Empty.getDefaultInstance()) } returns calendarHolidayList { }
            every {
                mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                    LocalDate.now().minusDays(56),
                    listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                )
            } returns listOf(
                complaintModel.copy(
                    createdAt = LocalDateTime.now(),
                    dateRaised = LocalDateTime.now().minusDays(60)
                )
            )
            val capturingSlot = slot<OutboxItem>()
            every {
                mockOutboxItemRepository.saveOutboxItem(
                    capture(capturingSlot)
                )
            } returns d1EmailOutboxItem

            sut.processComplaints()

            then("new SEND_EMAIL outbox item entry is created with shouldSendOverdueCommunication true") {
                verify {
                    mockComplaintRepository.fetchComplaintsByRaiseDateAndStatus(
                        LocalDate.now().minusDays(56),
                        listOf("Complaint open", "Complaint reopened", "Ombudsman complaint")
                    )
                }
                verify { mockOutboxItemRepository.saveOutboxItem(capture(capturingSlot)) }
                assertSoftly {
                    val sendEmailData =
                        capturingSlot.captured.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                    sendEmailData?.shouldSendD1Communication shouldBe false
                    sendEmailData?.shouldSendD56Communication shouldBe false
                    sendEmailData?.shouldSendRecentCommunication shouldBe false
                    sendEmailData?.shouldSendOverdueCommunication shouldBe true
                }
            }
        }
    }

    given("complaints exist and no filters applied") {

        `when`("get complaint summary") {
            every {
                mockComplaintRepository.getComplaintSummary(
                    emptyComplaintFilter
                )
            } returns complaintSummary

            val complaintSummary = sut.getComplaintsSummary(ProtoComplaintFilter.getDefaultInstance())

            then("summary returned") {
                verify {
                    mockComplaintRepository.getComplaintSummary(emptyComplaintFilter)
                }

                complaintSummary shouldBe protoComplaintSummary
            }
        }
    }

    given("complaints exist and filters applied") {

        `when`("get complaint summary") {
            every {
                mockComplaintRepository.getComplaintSummary(
                    complaintFilter
                )
            } returns complaintSummary

            val complaintSummary = sut.getComplaintsSummary(protoComplaintFilter)

            then("summary returned") {
                verify {
                    mockComplaintRepository.getComplaintSummary(complaintFilter)
                }

                complaintSummary shouldBe protoComplaintSummary
            }
        }
    }

    given("an account is checked for existing complaints") {
        `when`("complaints exist") {

            every { mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID) } returns listOf(complaintModel)

            val result = sut.accountHasComplaint(ACCOUNT_ID)

            then("true should be returned") {
                verify { mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID) }

                result shouldBe true
            }
        }

        `when`("only closed complaints exist") {

            every {
                mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID)
            } returns listOf(closedComplaintModel)

            val result = sut.accountHasComplaint(ACCOUNT_ID)

            then("false should be returned") {
                verify { mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID) }

                result shouldBe false
            }
        }

        `when`("complaints do not exist") {

            every { mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID) } returns emptyList()

            val result = sut.accountHasComplaint(ACCOUNT_ID)

            then("false should be returned") {
                verify { mockComplaintRepository.getComplaintsByBillingAccount(ACCOUNT_ID) }

                result shouldBe false
            }
        }
    }
})
