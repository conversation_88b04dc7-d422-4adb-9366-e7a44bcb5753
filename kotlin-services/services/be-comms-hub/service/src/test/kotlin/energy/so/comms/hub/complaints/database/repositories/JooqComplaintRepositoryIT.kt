package energy.so.comms.hub.complaints.database.repositories

import energy.so.commons.model.tables.pojos.Complaint
import energy.so.commons.model.tables.references.COMPLAINT
import energy.so.commons.model.tables.references.OUTBOX
import energy.so.comms.hub.complaints.extensions.toModel
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.ACCOUNT_ID
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.ACCOUNT_NUMBER
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildComplaintsForSummary
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildDateIntervalFilter
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildTestComplaint2
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildTestComplaintMetadata
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.buildTestExternalComplaint
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.emptyComplaintFilter
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.updateComplaintData
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.updateComplaintDataWithD56Sent
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.updateComplaintDataWithOverdueComplaintSent
import energy.so.comms.hub.fixtures.ComplaintPrecannedData.updateComplaintDataWithRecentComplaintSent
import energy.so.comms.hub.fixtures.FreshdeskPrecannedData.TICKET_ID
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus
import energy.so.comms.hub.complaints.models.ComplaintDetails
import energy.so.comms.hub.complaints.models.ComplaintFilter
import energy.so.comms.hub.complaints.models.DateInterval
import energy.so.comms.hub.common.database.repositories.JooqOutboxItemRepository
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.date.shouldBeBefore
import io.kotest.matchers.date.shouldBeWithin
import io.kotest.matchers.should
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import kotlinx.coroutines.delay
import energy.so.comms.hub.complaints.models.Complaint as ComplaintDto

class JooqComplaintRepositoryTest : BehaviorSpec({

    val db = installDatabase(truncateOnRootOnly = true)
    val outboxRepository = JooqOutboxItemRepository(db)
    val sut = JooqComplaintRepository(db, outboxRepository)

    given("a valid complaint with to save") {
        `when`("saved") {

            db.fetchCount(COMPLAINT) shouldBe 0

            sut.saveComplaint(buildTestExternalComplaint().toModel(buildTestComplaintMetadata()))

            then("the communication should be saved to the database") {
                db.fetchCount(COMPLAINT) shouldBe 1
            }
        }
    }

    given("a valid complaint with invalid goodwill amount to save") {
        val externalComplaint = buildTestExternalComplaint()
        externalComplaint.complaintsGoodwillAmount = "123 euro"
        `when`("saved") {

            db.fetchCount(COMPLAINT) shouldBe 0

            sut.saveComplaint(externalComplaint.toModel(buildTestComplaintMetadata()))

            then("the communication should be saved to the database") {
                db.fetchCount(COMPLAINT) shouldBe 1
            }
        }
    }


    given("existing complaint") {
        val testComplaint = buildTestExternalComplaint()
        val firstSavedComplaint = sut.saveComplaint(testComplaint.toModel(buildTestComplaintMetadata()))
        db.fetchCount(COMPLAINT) shouldBe 1
        firstSavedComplaint.agentEmail shouldBe testComplaint.agentEmail

        `when`("save the complaint update") {
            val complaintUpdate = buildTestComplaint2()

            val savedResult = sut.saveComplaint(complaintUpdate)

            then("the complaint should be updated") {
                savedResult shouldNotBe null
                savedResult.agentEmail shouldBe complaintUpdate.agentEmail
                savedResult.details shouldBe complaintUpdate.details
                savedResult.customerId shouldBe buildTestComplaintMetadata().customerId
                savedResult.agentEmail shouldBe complaintUpdate.agentEmail
                savedResult.agentName shouldBe complaintUpdate.agentName
                savedResult.ombudsmanCase shouldBe complaintUpdate.ombudsmanCase
                savedResult.team shouldBe complaintUpdate.team
                savedResult.channel shouldBe complaintUpdate.channel
                savedResult.type shouldBe complaintUpdate.type
                savedResult.status shouldBe complaintUpdate.status
                savedResult.subStatus shouldBe complaintUpdate.subStatus
                savedResult.d1SignpostingCommsSent shouldBe false
                savedResult.d56SignpostingCommsSent shouldBe false
                savedResult.deadlockCommsSent shouldBe false
                savedResult.ofgemMappedComplaintCategory shouldBe complaintUpdate.ofgemMappedComplaintCategory
                savedResult.internalComplaintCategory shouldBe complaintUpdate.internalComplaintCategory
                savedResult.complaintsGoodwillAmount shouldBe complaintUpdate.complaintsGoodwillAmount
                savedResult.category1 shouldBe complaintUpdate.category1
                savedResult.category2 shouldBe complaintUpdate.category2
                savedResult.category3 shouldBe complaintUpdate.category3

                db.fetchCount(COMPLAINT) shouldBe 1
            }
        }

        `when`("update complaint d1 comms and save outbox item") {
            val id = sut.updateComplaintComms(firstSavedComplaint.id!!, updateComplaintData)

            then("the complaint should be updated") {
                id shouldNotBe null
                val updatedComplaint = sut.getComplaintById(id)
                updatedComplaint shouldNotBe null

                updatedComplaint?.d1SignpostingCommsSent shouldBe true
                assertDateIsRecent(updatedComplaint?.sendDateOfD1SignpostingComms)

                updatedComplaint?.d56SignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfD56SignpostingComms shouldBe null
                updatedComplaint?.recentSignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfRecentSignpostingComms shouldBe null
                updatedComplaint?.overdueSignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfOverdueSignpostingComms shouldBe null
                updatedComplaint?.deadlockCommsSent shouldBe false
            }
        }

        `when`("update complaint recent comms flag") {
            val id = sut.updateComplaintComms(
                firstSavedComplaint.id!!,
                updateComplaintDataWithRecentComplaintSent
            )

            then("the complaint should be updated and new outbox item should be created") {
                val updatedComplaint = sut.getComplaintById(id)
                updatedComplaint shouldNotBe null

                updatedComplaint?.recentSignpostingCommsSent shouldBe true
                assertDateIsRecent(updatedComplaint?.sendDateOfRecentSignpostingComms)

                updatedComplaint?.d1SignpostingCommsSent shouldBe true
                updatedComplaint?.sendDateOfD1SignpostingComms shouldNotBe null
                updatedComplaint?.d56SignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfD56SignpostingComms shouldBe null
                updatedComplaint?.overdueSignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfOverdueSignpostingComms shouldBe null
                updatedComplaint?.deadlockCommsSent shouldBe false
            }
        }

        `when`("update complaint overdue comms flag") {
            val id = sut.updateComplaintComms(
                firstSavedComplaint.id!!,
                updateComplaintDataWithOverdueComplaintSent
            )

            then("the complaint should be updated and new outbox item should be created") {
                val updatedComplaint = sut.getComplaintById(id)
                updatedComplaint shouldNotBe null

                updatedComplaint?.overdueSignpostingCommsSent shouldBe true
                assertDateIsRecent(updatedComplaint?.sendDateOfOverdueSignpostingComms)

                updatedComplaint?.recentSignpostingCommsSent shouldBe true
                updatedComplaint?.sendDateOfRecentSignpostingComms shouldNotBe null
                updatedComplaint?.d1SignpostingCommsSent shouldBe true
                updatedComplaint?.sendDateOfD1SignpostingComms shouldNotBe null
                updatedComplaint?.d56SignpostingCommsSent shouldBe false
                updatedComplaint?.sendDateOfD56SignpostingComms shouldBe null
                updatedComplaint?.deadlockCommsSent shouldBe false
            }
        }


        `when`("update d56 comms flag") {
            val timeBeforeD56Update = LocalDateTime.now()
            delay(1000) // ensure different timestamps between saves

            val id = sut.updateComplaintComms(
                firstSavedComplaint.id!!,
                updateComplaintDataWithD56Sent
            )

            then("the complaint should be updated and new outbox item should be created") {
                val updatedComplaint = sut.getComplaintById(id)
                updatedComplaint shouldNotBe null

                updatedComplaint?.let {
                    it.d56SignpostingCommsSent shouldBe true
                    assertDateIsRecent(it.sendDateOfD56SignpostingComms)

                    it.overdueSignpostingCommsSent shouldBe true
                    it.sendDateOfOverdueSignpostingComms!! shouldBeBefore timeBeforeD56Update

                    it.recentSignpostingCommsSent shouldBe true
                    it.sendDateOfRecentSignpostingComms!! shouldBeBefore timeBeforeD56Update

                    it.d1SignpostingCommsSent shouldBe true
                    it.sendDateOfD1SignpostingComms!! shouldBeBefore timeBeforeD56Update

                    it.deadlockCommsSent shouldBe false
                }
            }
        }

        `when`("update complaint comms") {
            val id = sut.updateComplaintComms(firstSavedComplaint.id!!, updateComplaintData)

            then("the complaint should be updated and new outbox item should be created") {
                id shouldNotBe null
            }
        }

        `when`("get complaint by id") {
            val complaint = sut.getComplaintById(firstSavedComplaint.id!!)

            then("complaint should be returned") {
                complaint shouldNotBe null
                complaint?.id shouldBe firstSavedComplaint.id
            }
        }

        `when`("get complaint by external id") {
            val complaint = sut.getComplaintByExternalId(firstSavedComplaint.externalIdentifier!!)

            then("complaint should be returned") {
                complaint shouldNotBe null
                complaint?.id shouldBe firstSavedComplaint.id
            }
        }

        `when`("2 complaints are searched for with a matching billing account ID") {
            sut.saveComplaint(
                buildTestExternalComplaint(ComplaintStatus.COMPLAINT_CLOSED.value, 90L).toModel(
                    buildTestComplaintMetadata()
                )
            )
            val results = sut.getComplaintsByBillingAccount(ACCOUNT_ID)

            then("the complaint should be returned") {
                results.size shouldBe 2
                results[0].details!!.billingAccountIds.contains(ACCOUNT_ID) shouldBe true
                results[1].details!!.billingAccountIds.contains(ACCOUNT_ID) shouldBe true
            }
        }

        `when`("a complaint is searched for with a non matching billing account ID") {

            val results = sut.getComplaintsByBillingAccount(ACCOUNT_NUMBER.toLong())

            then("no complaints should be returned") {
                results.size shouldBe 0
            }
        }

        `when`("delete complaint") {

            val itemsCount = db.fetchCount(COMPLAINT)

            sut.deleteComplaintByExternalId(testComplaint.complaintTicketId.toString())

            then("the complaint is deleted") {
                db.fetchCount(COMPLAINT) shouldBe itemsCount - 1
                db.selectFrom(COMPLAINT).where(COMPLAINT.EXTERNAL_IDENTIFIER.eq(testComplaint.complaintTicketId.toString())).fetchOne() shouldBe null
            }
        }
    }

    given("existing complaint with raise date within last 56 days and created in the same day") {
        val savedComplaint = db.insertInto(
            COMPLAINT,
            COMPLAINT.ID,
            COMPLAINT.D1_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.D56_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.DEADLOCK_COMMS_SENT,
            COMPLAINT.DATE_RAISED,
            COMPLAINT.CREATED_AT,
            COMPLAINT.STATUS
        ).values(
            TICKET_ID,
            false,
            false,
            false,
            LocalDateTime.now().minusDays(10),
            LocalDateTime.now().minusDays(10),
            "Complaint open"
        )
            .returning().fetchOneInto(Complaint::class.java)


        `when`("fetch complaints by raise date and status") {

            val result =
                sut.fetchComplaintsByRaiseDateAndStatus(LocalDate.now().minusDays(56), listOf("Complaint open"))

            then("complaints should be retrieved") {
                result.size shouldBe 1
                result[0].id shouldBe savedComplaint?.id
            }
        }
    }

    given("existing complaint with raise date within last 56 days and created in different day") {
        val savedComplaint = db.insertInto(
            COMPLAINT,
            COMPLAINT.ID,
            COMPLAINT.D1_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.D56_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.DEADLOCK_COMMS_SENT,
            COMPLAINT.DATE_RAISED,
            COMPLAINT.CREATED_AT,
            COMPLAINT.STATUS
        ).values(
            TICKET_ID,
            false,
            false,
            false,
            LocalDateTime.now().minusDays(10),
            LocalDateTime.now().minusDays(8),
            "Complaint open"
        )
            .returning().fetchOneInto(Complaint::class.java)


        `when`("fetch complaints by raise date and status") {

            val result =
                sut.fetchComplaintsByRaiseDateAndStatus(LocalDate.now().minusDays(56), listOf("Complaint open"))

            then("complaints should be retrieved") {
                result.size shouldBe 1
                result[0].id shouldBe savedComplaint?.id
            }
        }
    }

    given("existing complaint with raise date older than 56 days and created more recent") {
        val savedComplaint = db.insertInto(
            COMPLAINT,
            COMPLAINT.ID,
            COMPLAINT.D1_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.D56_SIGNPOSTING_COMMS_SENT,
            COMPLAINT.DEADLOCK_COMMS_SENT,
            COMPLAINT.DATE_RAISED,
            COMPLAINT.CREATED_AT,
            COMPLAINT.STATUS
        ).values(
            TICKET_ID,
            false,
            false,
            false,
            LocalDateTime.now().minusDays(60),
            LocalDateTime.now().minusDays(10),
            "Complaint open"
        )
            .returning().fetchOneInto(Complaint::class.java)


        `when`("fetch complaints by raise date and status") {

            val result =
                sut.fetchComplaintsByRaiseDateAndStatus(LocalDate.now().minusDays(56), listOf("Complaint open"))

            then("complaints should be retrieved") {
                result.size shouldBe 1
                result[0].id shouldBe savedComplaint?.id
            }
        }
    }

    given("closed complaint exists") {
        val complaint = ComplaintDto(
            externalIdentifier = "100",
            dateRaised = LocalDateTime.now().minusDays(10),
            closedAt = LocalDateTime.now().minusDays(3),
            status = "Complaint closed",
            details = ComplaintDetails()
        )

        sut.saveComplaint(complaint)

        `when`("summary is requested with no filters") {

            val summary = sut.getComplaintSummary(emptyComplaintFilter)

            then("Age average computation shall take into consideration closedAt column") {
                summary shouldNotBe null
                summary.total shouldBe 1
                summary.ageAverage shouldBe 7
            }
        }

    }

    given("complaint with future date raised") {
        val complaint = ComplaintDto(
            externalIdentifier = "100",
            dateRaised = LocalDateTime.now().plusDays(10),
            status = "Complaint open",
            details = ComplaintDetails()
        )

        sut.saveComplaint(complaint)

        `when`("summary is requested with no filters") {

            val summary = sut.getComplaintSummary(emptyComplaintFilter)

            then("Age average computation shall take into consideration the current date") {
                summary shouldNotBe null
                summary.total shouldBe 1
                summary.ageAverage shouldBe 0
            }
        }

    }

    given("complaints exist in database") {
        buildComplaintsForSummary().forEach(sut::saveComplaint)
        db.fetchCount(COMPLAINT) shouldBe 4

        `when`("summary is requested with no filters") {

            val summary = sut.getComplaintSummary(emptyComplaintFilter)

            then("full summary should be retrieved") {
                summary shouldNotBe null
                summary.total shouldBe 4
                summary.ageAverage shouldBe 17
                summary.goodwillAmountTotal shouldBe 561
                summary.category1.name shouldBe "Category 1"
                summary.category1.count shouldBe 3
                summary.category2.name shouldBe "Category 2"
                summary.category2.count shouldBe 3
                summary.category3.name shouldBe "Category 3"
                summary.category3.count shouldBe 2
            }
        }

        `when`("summary is requested with filters") {

            val ageProfiles = listOf(
                buildDateIntervalFilter(0, 15),
                buildDateIntervalFilter(28, 32)
            )

            val filters = ComplaintFilter(
                dateRaised = DateInterval(null, null),
                category1List = listOf("Category 1"),
                category2List = listOf("Category 2"),
                category3List = listOf("Category 3"),
                statusList = listOf("Complaint open"),
                subStatusList = listOf("Under investigation"),
                channelList = listOf("Phone"),
                typeList = listOf("Legal"),
                ofgemCategoryList = listOf("Payment issues"),
                internalCategoryList = listOf("Payment declined"),
                ageProfileList = ageProfiles,
                agentList = listOf("Mia"),
                teamList = listOf("HR")
            )

            val summary = sut.getComplaintSummary(filters)

            then("full summary should be retrieved") {
                summary shouldNotBe null
                summary.total shouldBe 2
                summary.ageAverage shouldBe 21
                summary.goodwillAmountTotal shouldBe 175
                summary.category1.name shouldBe "Category 1"
                summary.category1.count shouldBe 2
                summary.category2.name shouldBe "Category 2"
                summary.category2.count shouldBe 2
                summary.category3.name shouldBe "Category 3"
                summary.category3.count shouldBe 2
            }
        }

        `when`("summary is requested with invalid filters") {

            val filters = emptyComplaintFilter.copy(category3List = listOf("This is not a category"))

            val summary = sut.getComplaintSummary(filters)

            then("full summary should be retrieved") {
                summary shouldNotBe null
                summary.total shouldBe 0
                summary.ageAverage shouldBe 0
                summary.goodwillAmountTotal shouldBe 0
                summary.category1.name shouldBe ""
                summary.category1.count shouldBe 0
                summary.category2.name shouldBe ""
                summary.category2.count shouldBe 0
                summary.category3.name shouldBe ""
                summary.category3.count shouldBe 0
            }
        }
    }
})

private fun assertDateIsRecent(date: LocalDateTime?) {
    date shouldNotBe null
    date should { b ->
        b?.shouldBeWithin(
            Duration.ofSeconds(1),
            LocalDateTime.now()
        )
    }
}
