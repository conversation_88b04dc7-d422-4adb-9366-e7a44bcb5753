package energy.so.comms.hub.complaints.services

import com.google.protobuf.Empty
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.logging.TraceableLogging
import energy.so.commons.v2.dtos.idRequest
import energy.so.comms.hub.common.database.repositories.OutboxItemRepository
import energy.so.comms.hub.common.models.OutboxItem
import energy.so.comms.hub.common.models.OutboxOperationStatus
import energy.so.comms.hub.common.models.OutboxOperationType
import energy.so.comms.hub.common.models.SendEmailData
import energy.so.comms.hub.common.models.UpdateComplaintData
import energy.so.comms.hub.communications.config.CommunicationTemplate
import energy.so.comms.hub.communications.integrations.clients.FreshdeskClient
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus.COMPLAINT_OPEN
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus.COMPLAINT_REOPENED
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus.OMBUDSMAN_COMPLAINT
import energy.so.comms.hub.complaints.database.repositories.ComplaintRepository
import energy.so.comms.hub.complaints.extensions.toComplaintFilter
import energy.so.comms.hub.complaints.extensions.toModel
import energy.so.comms.hub.complaints.extensions.toProtoComplaintSummary
import energy.so.comms.hub.complaints.models.Complaint
import energy.so.comms.hub.v1.complaint.ComplaintDto.ComplaintFilter
import energy.so.comms.hub.v1.complaint.ComplaintDto.ComplaintSummary
import energy.so.communications.v1.CommunicationClient
import energy.so.communications.v1.dtos.CommunicationTemplateDto
import energy.so.communications.v1.dtos.RecipientDto
import energy.so.customers.billingaccounts.v2.BillingAccount
import energy.so.customers.client.v2.CustomersClient
import energy.so.customers.client.v2.billingaccounts.BlockingBillingAccountsClient
import energy.so.customers.client.v2.calendarholiday.BlockingCalendarHolidayClient
import energy.so.generated.comms.hub.ExternalComplaint
import java.time.Instant
import kotlinx.coroutines.delay
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.JSON
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import kotlin.math.pow

@Component
class ComplaintService(
    @Value("\${outbox.max-retries}") private val maxRetries: Int,
    @Value("\${outbox.initial-delay}") private val initialDelay: Long,
    private val outboxItemRepository: OutboxItemRepository,
    private val freshdeskClient: FreshdeskClient,
    private val calendarHolidayClient: BlockingCalendarHolidayClient,
    private val communicationClient: CommunicationClient,
    private val communicationTemplates: CommunicationTemplate,
    private val complaintRepository: ComplaintRepository,
    private val metadataService: ComplaintMetadataService,
    private val customerClient: CustomersClient,
    private val billingAccountsClient: BlockingBillingAccountsClient,
) {

    private val logger = TraceableLogging.logger {}

    fun processComplaint(complaint: ExternalComplaint) {
        val complaintTicketId =
            if (complaint.complaintTicketId != null) complaint.complaintTicketId.toString() else complaint.complaintTicketIdStr

        if (!isLatestComplaint(complaintTicketId, complaint.updatedAt)) {
            return
        }

        if (complaint.shouldBeDeleted) {

            logger.info("[::ComplaintService] Deleting complaint with id $complaintTicketId")
            complaintRepository.deleteComplaintByExternalId(complaintTicketId)
        } else {
            saveComplaint(complaint)
        }
    }

    fun saveComplaint(complaint: ExternalComplaint) {
        val metadata = complaint.contactEmail?.let { metadataService.resolveComplaintMetadata(it) }
        val resolvedComplaint = resolveStatusChanges(complaint.toModel(metadata))
        complaintRepository.saveComplaint(resolvedComplaint)
    }

    fun isLatestComplaint(externalId: String, newComplaintUpdatedAt: Instant?): Boolean {
        if (newComplaintUpdatedAt == null) {
            logger.info("[::ComplaintService] Complaint with external id : $externalId was received but updated at not set")
            return true
        }

        val newComplaintUpdatedDateTime = LocalDateTime.ofInstant(newComplaintUpdatedAt, ZoneId.of("UTC"))
        complaintRepository.getComplaintByExternalId(externalId)?.let {
            if (it.updatedAt == null) {
                logger.error("[::ComplaintService] Updated at not set for existing complaint with external id : $externalId")
                return true
            }
            if (newComplaintUpdatedDateTime > it.updatedAt) {
                return true
            }

            logger.info("[::ComplaintService] Complaint with external id : $externalId and updated at: $newComplaintUpdatedDateTime is not latest complaint")
            return false
        }
        return true
    }

    fun processComplaints() {
        val today = LocalDate.now()
        val fiftySixthDay = today.minusDays(56)
        val holidayDates = calendarHolidayClient.getAllCalendarHolidays(request = Empty.getDefaultInstance())
            .calendarHolidaysList.map { it.holidayDate.toLocalDate() }

        val complaints =
            complaintRepository.fetchComplaintsByRaiseDateAndStatus(
                fiftySixthDay,
                listOf(COMPLAINT_OPEN.value, COMPLAINT_REOPENED.value, OMBUDSMAN_COMPLAINT.value)
            )

        // Filter complaints with date_raised on the same day as created_at or one day apart
        val sameDayComplaints = complaints.filter { complaint ->
            val dateRaised = complaint.dateRaised?.toLocalDate() ?: return@filter false
            val createdAtDate = complaint.createdAt?.toLocalDate()

            dateRaised >= fiftySixthDay && (
                dateRaised == createdAtDate ||
                    ChronoUnit.DAYS.between(dateRaised, createdAtDate) == 1L
                )
        }

        // Filter complaints with date_raised in the past (more than 2 days and within 56 days)
        val recentRaisedComplaints = complaints.filter { complaint ->
            val dateRaised = complaint.dateRaised?.toLocalDate() ?: return@filter false
            val createdAtDate = complaint.createdAt!!.toLocalDate()

            val daysBetween = ChronoUnit.DAYS.between(dateRaised, createdAtDate)

            dateRaised >= fiftySixthDay &&
                dateRaised != createdAtDate &&
                (daysBetween > 1 || (daysBetween == 1L && createdAtDate != today)) &&
                complaint.recentSignpostingCommsSent == false
        }

        // Filter complaints with date_raised older than 56 days
        val olderComplaints = complaints.filter { complaint ->
            val dateRaised = complaint.dateRaised?.toLocalDate() ?: return@filter false
            dateRaised < fiftySixthDay
        }

        processSameDayComplaints(sameDayComplaints, holidayDates, today)
        processRecentRaisedComplaints(recentRaisedComplaints)
        processOlderComplaints(olderComplaints)
    }

    fun getComplaintsSummary(filters: ComplaintFilter): ComplaintSummary {
        return complaintRepository.getComplaintSummary(filters.toComplaintFilter()).toProtoComplaintSummary()
    }

    suspend fun processPendingOutboxItems() {
        val pendingOutboxItems = outboxItemRepository.fetchPendingOutboxItems()
        pendingOutboxItems.forEach {
            when (it.type) {
                OutboxOperationType.SEND_EMAIL -> sendEmail(it)
                else -> {}
            }
        }
    }

    fun accountHasComplaint(billingAccountId: Long): Boolean =
        complaintRepository.getComplaintsByBillingAccount(billingAccountId)
            .any { complaint -> complaint.status != ComplaintStatus.COMPLAINT_CLOSED.value }

    private fun processSameDayComplaints(
        complaints: List<Complaint>,
        holidayDates: List<LocalDate>,
        today: LocalDate?,
    ) {
        complaints.forEach { complaint ->
            val createdDate = complaint.dateRaised!!.toLocalDate()
            val nextWorkingDay = nextWorkingDay(createdDate, holidayDates)
            val fiftySixthCalendarDay = fiftySixthCalendarDay(createdDate)

            val sendNextWorkingDayEmail = nextWorkingDay == today
            val sendFiftySixthCalendarDayEmail = fiftySixthCalendarDay == today

            if (sendNextWorkingDayEmail || sendFiftySixthCalendarDayEmail) {
                saveEmailOutboxItem(
                    complaint = complaint,
                    shouldSendD1Communication = sendNextWorkingDayEmail,
                    shouldSendD56Communication = sendFiftySixthCalendarDayEmail,
                    shouldSendRecentCommunication = false,
                    shouldSendOverdueCommunication = false,
                )
            }
        }
    }

    private fun nextWorkingDay(date: LocalDate, holidays: List<LocalDate>): LocalDate {
        var nextDay = date.plusDays(1)
        while (!isWorkingDay(nextDay, holidays)) {
            nextDay = nextDay.plusDays(1)
        }
        return nextDay
    }

    private fun fiftySixthCalendarDay(date: LocalDate): LocalDate {
        return date.plusDays(56)
    }

    private fun isWorkingDay(date: LocalDate, holidays: List<LocalDate>): Boolean {
        return date.dayOfWeek.value < 6 && !holidays.contains(date)
    }

    private fun processRecentRaisedComplaints(complaints: List<Complaint>) {
        complaints.forEach { complaint ->
            saveEmailOutboxItem(
                complaint = complaint,
                shouldSendD1Communication = false,
                shouldSendD56Communication = false,
                shouldSendRecentCommunication = true,
                shouldSendOverdueCommunication = false,
            )
        }
    }

    private fun processOlderComplaints(complaints: List<Complaint>) {
        complaints.forEach { complaint ->
            saveEmailOutboxItem(
                complaint = complaint,
                shouldSendD1Communication = false,
                shouldSendD56Communication = false,
                shouldSendRecentCommunication = false,
                shouldSendOverdueCommunication = true,
            )
        }
    }

    private fun saveEmailOutboxItem(
        complaint: Complaint,
        shouldSendD1Communication: Boolean,
        shouldSendD56Communication: Boolean,
        shouldSendRecentCommunication: Boolean,
        shouldSendOverdueCommunication: Boolean,
    ) {
        val emailData = SendEmailData(
            complaintId = complaint.id!!,
            ticketId = complaint.externalIdentifier!!,
            email = complaint.details?.contactEmail,
            shouldSendD1Communication = shouldSendD1Communication,
            shouldSendD56Communication = shouldSendD56Communication,
            shouldSendRecentCommunication = shouldSendRecentCommunication,
            shouldSendOverdueCommunication = shouldSendOverdueCommunication,
        )
        val outboxItem = OutboxItem(
            type = OutboxOperationType.SEND_EMAIL,
            payload = JSON.json(Json.encodeToString(emailData)),
            status = OutboxOperationStatus.PENDING
        )
        outboxItemRepository.saveOutboxItem(outboxItem)
    }

    private fun resolveStatusChanges(complaint: Complaint): Complaint {
        if (complaint.status == ComplaintStatus.COMPLAINT_CLOSED.value) {
            val existingComplaint =
                complaint.externalIdentifier?.let { complaintRepository.getComplaintByExternalId(it) }

            existingComplaint?.let {
                if (existingComplaint.status != ComplaintStatus.COMPLAINT_CLOSED.value) {
                    return complaint.copy(closedAt = LocalDateTime.now())
                }
            } ?: return complaint.copy(closedAt = LocalDateTime.now())
        }
        return complaint
    }

    private suspend fun sendEmail(outboxItem: OutboxItem) {
        repeat(maxRetries) { attempt ->
            var currentTemplate: String? = null
            try {
                val sendEmailData = outboxItem.payload?.data()?.let { Json.decodeFromString<SendEmailData>(it) }
                sendEmailData?.let {
                    currentTemplate = when {
                        it.shouldSendD1Communication == true -> communicationTemplates.d1Communication
                        it.shouldSendD56Communication == true -> communicationTemplates.d56Communication
                        // todo(SO-20883) Change the below communications to the corresponding templates when they are ready
                        it.shouldSendRecentCommunication == true -> communicationTemplates.d1Communication
                        it.shouldSendOverdueCommunication == true -> communicationTemplates.d56Communication
                        else -> null
                    }

                    currentTemplate?.let { template ->
                        // todo(SO-20883): for now we don't have the templates for recent and overdue emails
                        if (template in listOf(
                                communicationTemplates.d1Communication,
                                communicationTemplates.d56Communication
                            )
                        ) {
                            sendCommunication(it, template)
                        }
                        complaintRepository.updateComplaintComms(
                            it.complaintId,
                            UpdateComplaintData(
                                ticketId = it.ticketId,
                                complaintD1LetterSent = it.shouldSendD1Communication,
                                complaintD56LetterSent = it.shouldSendD56Communication,
                                complaintRecentLetterSent = it.shouldSendRecentCommunication,
                                complaintOverdueLetterSent = it.shouldSendOverdueCommunication
                            )
                        )
                    }

                    outboxItemRepository.updateOutboxItemStatus(outboxItem.id!!, OutboxOperationStatus.SUCCESS)
                    return
                }
            } catch (e: Exception) {
                if (attempt < maxRetries - 1) {
                    val delayDuration = initialDelay * 2.0.pow(attempt).toLong()
                    delay(delayDuration)
                } else {
                    logger.error {
                        "[::complaintSignposting] Failed to send email with template $currentTemplate for outbox item ${outboxItem.id} after $maxRetries attempts " +
                            "with message ${e.message}"
                    }
                }
            }
        }
        outboxItemRepository.updateOutboxItemStatus(outboxItem.id!!, OutboxOperationStatus.FAILURE)
    }

    private suspend fun sendCommunication(
        sendEmailData: SendEmailData,
        template: String,
    ) {
        val complaint = complaintRepository.getComplaintById(sendEmailData.complaintId)
        val customerId = complaint?.customerId
        val customer = customerId?.let { customerClient.getCustomerById(idRequest { id = customerId }) }
        val name = customer?.firstName ?: ""
        val billingAccounts = complaint?.details?.billingAccountIds?.map {
            billingAccountsClient.getBillingAccountById(
                idRequest {
                    id = it
                }
            )
        }
        val accountNumber = findAccountNumberByCustomerId(billingAccounts, customerId)
        val email = sendEmailData.email

        email?.let {
            val customAttributes = mutableMapOf(
                "customer_name" to name,
                "account_number" to (accountNumber ?: ""),
                "destination_address" to email
            )
            logger.info { "[::complaintSignposting] Sending complaint email with template $template..." }
            communicationClient.sendCommunicationTemplate(
                CommunicationTemplateDto(
                    communicationName = template,
                    recipient = RecipientDto(
                        name = name,
                        email = email,
                    ),
                    customAttributes = customAttributes
                )
            )
        }
    }

    private fun findAccountNumberByCustomerId(billingAccounts: List<BillingAccount>?, customerId: Long?): String? {
        return customerId?.let {
            billingAccounts?.filter { billingAccount -> billingAccount.customerId == it }
                ?.map { it.number }
                ?.firstOrNull()
        }
    }
}
