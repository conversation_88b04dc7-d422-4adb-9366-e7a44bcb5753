package energy.so.comms.hub.common.models

import energy.so.commons.grpc.extensions.toSiblingEnum
import energy.so.commons.model.enums.OutboxOperationStatu
import energy.so.commons.model.tables.pojos.Outbox
import kotlinx.serialization.Serializable
import org.jooq.JSON
import java.time.LocalDateTime

class OutboxItem(
    val id: Long? = null,
    val type: OutboxOperationType? = null,
    val payload: JSON? = null,
    val status: OutboxOperationStatus? = null,
    val createdAt: LocalDateTime? = null,
) {

    companion object {
        fun fromOutbox(outbox: Outbox) = OutboxItem(
            id = outbox.id,
            type = outbox.type?.toSiblingEnum<OutboxOperationType>(),
            status = outbox.status?.toSiblingEnum<OutboxOperationStatus>(),
            payload = outbox.payload,
            createdAt = outbox.createdAt,
        )
    }
}

enum class OutboxOperationType {
    SEND_EMAIL,
}

enum class OutboxOperationStatus {
    PENDING,
    SUCCESS,
    FAILURE
}

fun OutboxItem.toJooq(): Outbox {
    return Outbox(
        id = this.id,
        type = this.type?.toSiblingEnum<energy.so.commons.model.enums.OutboxOperationType>(),
        payload = this.payload,
        status = this.status?.toSiblingEnum<OutboxOperationStatu>(),
        createdAt = this.createdAt
    )
}

@Serializable
class SendEmailData(
    val complaintId: Long,
    val ticketId: String,
    val email: String? = null,
    val shouldSendD1Communication: Boolean? = false,
    val shouldSendD56Communication: Boolean? = false,
    val shouldSendRecentCommunication: Boolean? = false,
    val shouldSendOverdueCommunication: Boolean? = false,
) {
    companion object {}
}

@Serializable
class UpdateComplaintData(
    val ticketId: String,
    val complaintD1LetterSent: Boolean? = false,
    val complaintD56LetterSent: Boolean? = false,
    val complaintRecentLetterSent: Boolean? = false,
    val complaintOverdueLetterSent: Boolean? = false,
) {
    companion object {}
}
