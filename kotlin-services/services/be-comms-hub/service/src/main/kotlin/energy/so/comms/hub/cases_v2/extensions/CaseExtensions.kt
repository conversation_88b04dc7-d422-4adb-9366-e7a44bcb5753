package energy.so.comms.hub.cases_v2.extensions

import energy.so.comms.hub.cases.extensions.AWS_CONNECT_SOURCE
import energy.so.comms.hub.cases_v2.integrations.models.Case
import energy.so.comms.hub.cases_v2.integrations.models.CaseFieldValue
import energy.so.comms.hub.cases_v2.integrations.models.FieldValue
import energy.so.comms.hub.communications.integrations.models.freshdesk.UNASSIGNED_CONNECT_VALUE
import energy.so.comms.hub.communications.integrations.models.freshdesk.FreshdeskCommunication.Companion.setStatusAndSubStatus
import energy.so.customers.v2.customers.Customer
import energy.so.generated.comms.hub.ExternalComplaint
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset

fun Case.toExternalComplaint(customer: Customer?, shouldBeDeleted: Boolean): ExternalComplaint {
    val fields = this.fields?.mapValues { field -> field.value.handleUnassignedConnectValue() }
    return ExternalComplaint.newBuilder()
        .setShouldBeDeleted(shouldBeDeleted)
        .setComplaintId(externalId)
        .setComplaintTicketIdStr(externalId)
        .setOmbudsmanCase(fields?.get("complaint_ombudsman_status_decision_issued")?.value?.booleanValue)
        .setAgentName(fields?.get("assigned_agent_name")?.value?.stringValue)
        .setTeam(fields?.get("assigned_queue_name")?.value?.stringValue)
        .setAgentEmail(null)
        .setContactEmail(customer?.primaryContact?.email)
        .setType(fields?.get("complaint_type")?.value?.stringValue)
        .setDateRaised(this.getRaisedDate())
        .setCreatedAt(this.getCreationDate())
        .setUpdatedAt(this.getUpdateAt())
        .setStatusAndSubStatus(this)
        .setChannel(fields?.get("source")?.value?.stringValue)
        .setD1SignpostingCommsSent(false)
        .setD56SignpostingCommsSent(false)
        .setDeadlockCommsSent(false)
        .setOfgemMappedComplaintCategory(fields?.get("complaint_subject")?.value?.stringValue)
        .setInternalComplaintCategory(fields?.get("complaint_subject_2")?.value?.stringValue)
        .setComplaintsCustPrefContactMeth(fields?.get("complaints_pref_cust_contact_meth")?.value?.stringValue)
        .setComplaintsGoodwillAmount(fields?.get("complaints_goodwill_amount")?.value?.doubleValue?.toLong().toString())
        .setBillingAccountNumber(fields?.get("account_number")?.value?.stringValue)
        .setSource(AWS_CONNECT_SOURCE)
        .build()
}

fun CaseFieldValue?.handleUnassignedConnectValue(): CaseFieldValue? {
    val stringValue = this?.value?.stringValue
    return when (stringValue) {
        null -> this
        UNASSIGNED_CONNECT_VALUE -> CaseFieldValue(value = FieldValue(stringValue = null))
        else -> this
    }
}

private fun Case.getCreationDate(): Instant {
    return createdDateTime.toInstant(ZoneOffset.UTC)
}

private fun Case.getRaisedDate(): Instant {
    val originalCreationDate = fields?.get("original_creation_date")?.value?.stringValue

    return if (originalCreationDate != null)
        Instant.parse(originalCreationDate) else
        createdDateTime.toInstant(ZoneOffset.UTC)
}

private fun Case.getUpdateAt(): Instant? {
    val lastUpdatedDatetime = fields?.get("last_updated_datetime")?.value?.stringValue

    return if (lastUpdatedDatetime != null)
        Instant.parse(lastUpdatedDatetime) else
        Instant.now(Clock.systemUTC())
}

private fun ExternalComplaint.Builder.setStatusAndSubStatus(case: Case): ExternalComplaint.Builder {
    return setStatusAndSubStatus(
        case.fields?.get("status")?.value?.stringValue,
        case.fields?.get("complaint_status")?.value?.stringValue,
        case.fields?.get("complaint_proposed_resolution_status")?.value?.stringValue,
        case.fields?.get("complaint_ombudsman_status")?.value?.stringValue
    )
}
