package energy.so.comms.hub.complaints.database.repositories

import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.tables.records.ComplaintRecord
import energy.so.commons.model.tables.references.COMPLAINT
import energy.so.commons.model.tables.references.COMPLAINT_SEARCH
import energy.so.comms.hub.common.database.repositories.OutboxItemRepository
import energy.so.comms.hub.common.models.UpdateComplaintData
import energy.so.comms.hub.complaints.extensions.toJooq
import energy.so.comms.hub.complaints.models.Complaint
import energy.so.comms.hub.complaints.models.ComplaintFilter
import energy.so.comms.hub.complaints.models.ComplaintSummary
import energy.so.comms.hub.complaints.models.TopCategory
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.DatePart
import org.jooq.TableField
import org.jooq.UpdateSetMoreStep
import org.jooq.impl.DSL
import org.jooq.impl.DSL.and
import org.jooq.impl.DSL.avg
import org.jooq.impl.DSL.coalesce
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.least
import org.jooq.impl.DSL.now
import org.jooq.impl.DSL.or
import org.jooq.impl.DSL.sum
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalDateTime
import energy.so.commons.model.tables.pojos.Complaint as JooqComplaint

@Component
class JooqComplaintRepository(
    private val dslContext: DSLContext,
    private val outboxItemRepository: OutboxItemRepository,
) : ComplaintRepository {
    override fun saveComplaint(complaint: Complaint): Complaint {
        val jooqEntity = complaint.toJooq()
        val jooqComplaint = dslContext.insertInto(COMPLAINT)
            .set(dslContext.newRecord(COMPLAINT, jooqEntity))
            .onConflict(COMPLAINT.EXTERNAL_IDENTIFIER)
            .doUpdate()
            .set(COMPLAINT.UPDATED_AT, jooqEntity.updatedAt)
            .set(COMPLAINT.CREATED_AT, jooqEntity.createdAt)
            .set(COMPLAINT.DATE_RAISED, jooqEntity.dateRaised)
            .set(COMPLAINT.DETAILS, jooqEntity.details)
            .set(COMPLAINT.CUSTOMER_ID, jooqEntity.customerId)
            .set(COMPLAINT.AGENT_EMAIL, jooqEntity.agentEmail)
            .set(COMPLAINT.AGENT_NAME, jooqEntity.agentName)
            .set(COMPLAINT.OMBUDSMAN_CASE, jooqEntity.ombudsmanCase)
            .set(COMPLAINT.TEAM, jooqEntity.team)
            .set(COMPLAINT.CHANNEL, jooqEntity.channel)
            .set(COMPLAINT.TYPE, jooqEntity.type)
            .set(COMPLAINT.STATUS, jooqEntity.status)
            .set(COMPLAINT.SUB_STATUS, jooqEntity.subStatus)
            .set(COMPLAINT.DEADLOCK_COMMS_SENT, jooqEntity.deadlockCommsSent)
            .set(COMPLAINT.OFGEM_MAPPED_COMPLAINT_CATEGORY, jooqEntity.ofgemMappedComplaintCategory)
            .set(COMPLAINT.INTERNAL_COMPLAINT_CATEGORY, jooqEntity.internalComplaintCategory)
            .set(COMPLAINT.COMPLAINTS_GOODWILL_AMOUNT, jooqEntity.complaintsGoodwillAmount)
            .set(COMPLAINT.CATEGORY1, jooqEntity.category1)
            .set(COMPLAINT.CATEGORY2, jooqEntity.category2)
            .set(COMPLAINT.CATEGORY3, jooqEntity.category3)
            .set(COMPLAINT.CLOSED_AT, jooqEntity.closedAt)
            .returning()
            .fetchOneInto(JooqComplaint::class.java)
            ?: throw IllegalStateException(
                "Unable to save complaint with id ${complaint.externalIdentifier} of type ${complaint.type} to database"
            )

        return Complaint.fromJooq(jooqComplaint)
    }

    override fun deleteComplaintByExternalId(externalId: String): Int {
        return dslContext.delete(COMPLAINT)
            .where(COMPLAINT.EXTERNAL_IDENTIFIER.eq(externalId))
            .execute()
    }

    override fun updateComplaintComms(id: Long, updateComplaintData: UpdateComplaintData): Long {
        applyComplaintUpdates(id, updateComplaintData, dslContext)
        return id
    }

    private fun applyComplaintUpdates(id: Long, updateComplaintData: UpdateComplaintData, dslContext: DSLContext) {
        val updateStep = dslContext.update(COMPLAINT)

        if (updateComplaintData.complaintD1LetterSent == true) {
            updateStep.set(COMPLAINT.D1_SIGNPOSTING_COMMS_SENT, updateComplaintData.complaintD1LetterSent)
            updateStep.set(COMPLAINT.SEND_DATE_OF_D1_SIGNPOSTING_COMMS, LocalDateTime.now())
        }
        if (updateComplaintData.complaintD56LetterSent == true) {
            updateStep.set(COMPLAINT.D56_SIGNPOSTING_COMMS_SENT, updateComplaintData.complaintD56LetterSent)
            updateStep.set(COMPLAINT.SEND_DATE_OF_D56_SIGNPOSTING_COMMS, LocalDateTime.now())
        }
        if (updateComplaintData.complaintRecentLetterSent == true) {
            updateStep.set(COMPLAINT.RECENT_SIGNPOSTING_COMMS_SENT, updateComplaintData.complaintRecentLetterSent)
            updateStep.set(COMPLAINT.SEND_DATE_OF_RECENT_SIGNPOSTING_COMMS, LocalDateTime.now())
        }
        if (updateComplaintData.complaintOverdueLetterSent == true) {
            updateStep.set(COMPLAINT.OVERDUE_SIGNPOSTING_COMMS_SENT, updateComplaintData.complaintOverdueLetterSent)
            updateStep.set(COMPLAINT.SEND_DATE_OF_OVERDUE_SIGNPOSTING_COMMS, LocalDateTime.now())
        }

        (updateStep as UpdateSetMoreStep<*>).where(COMPLAINT.ID.eq(id)).execute()
    }

    override fun fetchComplaintsByRaiseDateAndStatus(fromDate: LocalDate, statuses: List<String>): List<Complaint> =
        dslContext.selectFrom(COMPLAINT)
            .where(
                field("DATE({0})", LocalDate::class.java, COMPLAINT.DATE_RAISED)
                    .greaterOrEqual(fromDate)
            )
            .or(
                field("DATE({0})", LocalDate::class.java, COMPLAINT.DATE_RAISED)
                    .lessThan(fromDate)
                    .and(COMPLAINT.CREATED_AT.greaterOrEqual(fromDate.atStartOfDay()))
                    .and(COMPLAINT.OVERDUE_SIGNPOSTING_COMMS_SENT.eq(false))
            )
            .and(COMPLAINT.STATUS.`in`(statuses))
            .fetchInto(JooqComplaint::class.java)
            .map { Complaint.fromJooq(it) }

    override fun getComplaintById(id: Long) =
        dslContext.selectFrom(COMPLAINT).where(COMPLAINT.ID.eq(id)).fetchOneInto(JooqComplaint::class.java)
            ?.let { Complaint.fromJooq(it) }

    override fun getComplaintByExternalId(externalIdentifier: String) =
        dslContext.selectFrom(COMPLAINT).where(COMPLAINT.EXTERNAL_IDENTIFIER.eq(externalIdentifier)).fetchOneInto(
            JooqComplaint::class.java
        )
            ?.let { Complaint.fromJooq(it) }

    override fun getComplaintSummary(filters: ComplaintFilter): ComplaintSummary {
        val condition = createComplaintCondition(filters)
        val values = dslContext.select(
            sum(COMPLAINT.COMPLAINTS_GOODWILL_AMOUNT).`as`("goodwillAmountTotal"),
            avg(
                DSL.extract(
                    coalesce(COMPLAINT.CLOSED_AT, now()).sub(least(now(), COMPLAINT.DATE_RAISED)),
                    DatePart.DAY
                )
                    .cast(Long::class.java)
            ).`as`("ageAverage"),
            count().`as`("total")
        )
            .from(COMPLAINT)
            .where(condition)
            .fetchOne()

        return ComplaintSummary(
            category1 = getDataForCategory(COMPLAINT.CATEGORY1, condition),
            category2 = getDataForCategory(COMPLAINT.CATEGORY2, condition),
            category3 = getDataForCategory(COMPLAINT.CATEGORY3, condition),
            goodwillAmountTotal = values?.getValue("goodwillAmountTotal", Long::class.java) ?: 0,
            ageAverage = values?.getValue("ageAverage", Long::class.java) ?: 0,
            total = values?.getValue("total", Long::class.java) ?: 0
        )
    }

    override fun getComplaintsByBillingAccount(id: Long): List<Complaint> = dslContext
        .selectFrom(COMPLAINT_SEARCH(id.toString()))
        .fetchInto(JooqComplaint::class.java)
        .map { Complaint.fromJooq(it) }

    private fun getDataForCategory(fieldName: TableField<ComplaintRecord, String?>, defaultCondition: Condition) =
        dslContext.select(fieldName.`as`("name"), count().`as`("count"))
            .from(COMPLAINT)
            .where(defaultCondition.and(fieldName.isNotNull()))
            .groupBy(fieldName)
            .orderBy(count().desc())
            .limit(1)
            .fetchOne()?.let {
                TopCategory(
                    name = it.getValue("name", String::class.java),
                    count = it.getValue("count", Long::class.java)
                )
            } ?: TopCategory(
            name = "",
            count = 0
        )

    private fun createComplaintCondition(filters: ComplaintFilter): Condition {
        val list = mutableListOf<Condition>()

        if (filters.statusList.isNotEmpty()) {
            list.add(COMPLAINT.STATUS.`in`(filters.statusList))
        }

        if (filters.subStatusList.isNotEmpty()) {
            list.add(COMPLAINT.SUB_STATUS.`in`(filters.subStatusList))
        }

        if (filters.channelList.isNotEmpty()) {
            list.add(COMPLAINT.CHANNEL.`in`(filters.channelList))
        }

        if (filters.typeList.isNotEmpty()) {
            list.add(COMPLAINT.TYPE.`in`(filters.typeList))
        }

        if (filters.category1List.isNotEmpty()) {
            list.add(COMPLAINT.CATEGORY1.`in`(filters.category1List))
        }

        if (filters.category2List.isNotEmpty()) {
            list.add(COMPLAINT.CATEGORY2.`in`(filters.category2List))
        }

        if (filters.category3List.isNotEmpty()) {
            list.add(COMPLAINT.CATEGORY3.`in`(filters.category3List))
        }

        if (filters.internalCategoryList.isNotEmpty()) {
            list.add(COMPLAINT.INTERNAL_COMPLAINT_CATEGORY.`in`(filters.internalCategoryList))
        }

        if (filters.ofgemCategoryList.isNotEmpty()) {
            list.add(COMPLAINT.OFGEM_MAPPED_COMPLAINT_CATEGORY.`in`(filters.ofgemCategoryList))
        }

        if (filters.agentList.isNotEmpty()) {
            list.add(COMPLAINT.AGENT_NAME.`in`(filters.agentList))
        }

        if (filters.teamList.isNotEmpty()) {
            list.add(COMPLAINT.TEAM.`in`(filters.teamList))
        }

        val ageProfilesConditions = mutableListOf<Condition>()

        filters.ageProfileList.forEach { ageProfile ->
            val intervalConditions = mutableListOf<Condition>()

            ageProfile.lt?.let {
                intervalConditions.add(and(COMPLAINT.DATE_RAISED.lt(ageProfile.lt.toLocalDateTime())))
            }
            ageProfile.gt?.let {
                intervalConditions.add(and(COMPLAINT.DATE_RAISED.gt(ageProfile.gt.toLocalDateTime())))
            }

            if (intervalConditions.isNotEmpty()) {
                ageProfilesConditions.add(and(intervalConditions))
            }
        }

        if (ageProfilesConditions.isNotEmpty()) {
            list.add(or(ageProfilesConditions))
        }

        filters.dateRaised.lt?.let { list.add(COMPLAINT.DATE_RAISED.lt(filters.dateRaised.lt.toLocalDateTime())) }
        filters.dateRaised.gt?.let { list.add(COMPLAINT.DATE_RAISED.gt(filters.dateRaised.gt.toLocalDateTime())) }

        return and(list)
    }
}
