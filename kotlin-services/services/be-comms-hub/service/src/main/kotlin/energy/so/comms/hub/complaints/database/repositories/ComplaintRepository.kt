package energy.so.comms.hub.complaints.database.repositories

import energy.so.comms.hub.common.models.UpdateComplaintData
import energy.so.comms.hub.complaints.models.Complaint
import energy.so.comms.hub.complaints.models.ComplaintFilter
import energy.so.comms.hub.complaints.models.ComplaintSummary
import java.time.LocalDate

interface ComplaintRepository {

    fun saveComplaint(complaint: Complaint): Complaint

    fun deleteComplaintByExternalId(externalId: String): Int

    fun updateComplaintComms(id: Long, updateComplaintData: UpdateComplaintData): Long

    fun fetchComplaintsByRaiseDateAndStatus(fromDate: LocalDate, statuses: List<String>): List<Complaint>

    fun getComplaintById(id: Long): Complaint?

    fun getComplaintByExternalId(externalIdentifier: String): Complaint?

    fun getComplaintSummary(filters: ComplaintFilter): ComplaintSummary

    fun getComplaintsByBillingAccount(id: Long): List<Complaint>
}
