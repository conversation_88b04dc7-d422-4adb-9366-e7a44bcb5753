package energy.so.comms.hub.fixtures

import com.google.protobuf.Timestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.comms.hub.complaints.extensions.toModel
import energy.so.comms.hub.communications.integrations.models.freshdesk.ComplaintStatus
import energy.so.comms.hub.communications.integrations.models.freshdesk.FRESHDESK_SOURCE
import energy.so.comms.hub.complaints.models.Complaint
import energy.so.comms.hub.complaints.models.ComplaintDetails
import energy.so.comms.hub.complaints.models.ComplaintFilter
import energy.so.comms.hub.complaints.models.ComplaintMetadata
import energy.so.comms.hub.complaints.models.ComplaintSummary
import energy.so.comms.hub.complaints.models.DateInterval
import energy.so.comms.hub.common.models.OutboxItem
import energy.so.comms.hub.common.models.OutboxOperationStatus
import energy.so.comms.hub.common.models.OutboxOperationType
import energy.so.comms.hub.common.models.SendEmailData
import energy.so.comms.hub.complaints.models.TopCategory
import energy.so.comms.hub.common.models.UpdateComplaintData
import energy.so.comms.hub.v1.complaint.complaintFilter
import energy.so.comms.hub.v1.complaint.complaintSummary
import energy.so.comms.hub.v1.complaint.dateInterval
import energy.so.comms.hub.v1.complaint.topCategory
import energy.so.customers.billingaccounts.v2.billingAccount
import energy.so.customers.v2.customers.customer
import energy.so.generated.comms.hub.ExternalComplaint
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.JSON

object ComplaintPrecannedData {

    const val OUTBOX_ITEM_ID = 1L
    const val COMPLAINT_ID = 1L
    const val ACCOUNT_ID = 1L
    const val CUSTOMER_FIRST_NAME = "Shelby"
    const val ACCOUNT_NUMBER = "********"

    private const val TICKET_ID = 80L
    private const val CONTACT_EMAIL = "<EMAIL>"
    private const val AGENT_NAME = "Mia"
    private const val CHANNEL = "Phone"
    private const val AGENT_EMAIL = "<EMAIL>"
    private const val COMPLAINT_TYPE = "Legal"
    private const val COMPLAINT_CATEGORY1 = "Category 1"
    private const val COMPLAINT_CATEGORY2 = "Category 2"
    private const val COMPLAINT_CATEGORY3 = "Category 3"
    private const val COMPLAINT_SUBJECT = "Payment issues"
    private const val COMPLAINT_INTERNAL_SUBJECT = "Payment declined"
    private const val COMPLAINT_CUST_PREF_CONTACT_METH = "Phone"
    private const val COMPLAINT_GOODWILL_AMOUNT = "123.25"
    private const val CUSTOMER_ID = 1L
    private const val EMAIL = "<EMAIL>"
    private const val STATUS = "Ombudsman complaint"
    private const val SUBSTATUS = "Under investigation"
    private const val CATEGORY1 = "Billing"
    private const val CATEGORY2 = "Tariffs - Fixed"
    private const val CATEGORY3 = "Quote Requested/Provided"
    private const val TYPE = "OS"
    private const val INTERNAL_CATEGORY = "Not Available"
    private const val OFGEM_CATEGORY = "Feed-In Tariffs (B10)"
    private const val AGENT = "Mia"
    private const val TEAM = "HR"
    private const val BILLING_ACCOUNT_NUMBER = "12345"

    private val dateRaisedLessThan = OffsetDateTime.of(2024, 1, 1, 1, 1, 1, 1, ZoneOffset.UTC)
    private val ageProfile1LessThan = OffsetDateTime.of(2024, 3, 1, 1, 1, 1, 1, ZoneOffset.UTC)
    private val ageProfile1GreaterThan = OffsetDateTime.of(2023, 1, 1, 1, 1, 1, 1, ZoneOffset.UTC)
    private val COMPLAINT_START_DATE = Instant.now()
    private val COMPLAINT_CREATED_AT = LocalDateTime.of(2024,11,11,10,23,1).toInstant(ZoneOffset.UTC)
    private val COMPLAINT_UPDATED_AT = LocalDateTime.of(2024,11,11,10,23,1).toInstant(ZoneOffset.UTC)

    fun buildTestExternalComplaint(
        complaintStatus: String = "Complaint open",
        ticketId: Long = TICKET_ID,
        shouldBeRemoved: Boolean = false,
    ): ExternalComplaint =
        ExternalComplaint.newBuilder().apply {
            complaintId = ticketId.toString()
            shouldBeDeleted = shouldBeRemoved
            complaintTicketId = ticketId
            team = TEAM
            agentName = AGENT_NAME
            agentEmail = AGENT_EMAIL
            contactEmail = CONTACT_EMAIL
            channel = CHANNEL
            type = COMPLAINT_TYPE
            dateRaised = COMPLAINT_START_DATE
            ombudsmanCase = false
            status = complaintStatus
            subStatus = "Under investigation"
            d1SignpostingCommsSent = null
            d56SignpostingCommsSent = null
            deadlockCommsSent = null
            category1 = COMPLAINT_CATEGORY1
            category2 = COMPLAINT_CATEGORY2
            category3 = COMPLAINT_CATEGORY3
            ofgemMappedComplaintCategory = COMPLAINT_SUBJECT
            internalComplaintCategory = COMPLAINT_INTERNAL_SUBJECT
            complaintsCustPrefContactMeth = COMPLAINT_CUST_PREF_CONTACT_METH
            complaintsGoodwillAmount = COMPLAINT_GOODWILL_AMOUNT
            billingAccountNumber = BILLING_ACCOUNT_NUMBER
            source = FRESHDESK_SOURCE
            createdAt = COMPLAINT_CREATED_AT
            updatedAt = COMPLAINT_UPDATED_AT
        }.build()

    fun buildTestComplaint2(
        ticketId: Long = TICKET_ID,
        metadata: ComplaintMetadata = buildTestComplaintMetadata(),
    ): Complaint =
        ExternalComplaint.newBuilder().apply {
            complaintId = ticketId.toString()
            complaintTicketId = ticketId
            team = null
            agentName = AGENT_NAME + "V2"
            agentEmail = AGENT_EMAIL + "V2"
            contactEmail = CONTACT_EMAIL + "V2"
            channel = CHANNEL + "V2"
            type = COMPLAINT_TYPE + "V2"
            dateRaised = COMPLAINT_START_DATE.minus(Duration.ofDays(1))
            ombudsmanCase = false
            status = "null" + "V2"
            subStatus = "null" + "V2"
            d1SignpostingCommsSent = true
            d56SignpostingCommsSent = null
            deadlockCommsSent = null
            category1 = COMPLAINT_CATEGORY1 + "V2"
            category2 = COMPLAINT_CATEGORY2 + "V2"
            category3 = COMPLAINT_CATEGORY3 + "V2"
            ofgemMappedComplaintCategory = COMPLAINT_SUBJECT + "V2"
            internalComplaintCategory = COMPLAINT_INTERNAL_SUBJECT + "V2"
            complaintsCustPrefContactMeth = COMPLAINT_CUST_PREF_CONTACT_METH + "V2"
            complaintsGoodwillAmount = "523.23"
            billingAccountNumber = BILLING_ACCOUNT_NUMBER
        }.build().toModel(metadata)

    val expectedComplaintModel = Complaint(
        details = ComplaintDetails(
            complaintId = TICKET_ID.toString(),
            contactEmail = CONTACT_EMAIL,
            complaintsCustPrefContactMeth = COMPLAINT_CUST_PREF_CONTACT_METH,
            billingAccountIds = listOf(ACCOUNT_ID),
            billingAccountNumber = "000$BILLING_ACCOUNT_NUMBER"
        ),
        externalIdentifier = TICKET_ID.toString(),
        customerId = CUSTOMER_ID,
        ombudsmanCase = false,
        team = TEAM,
        agentEmail = AGENT_EMAIL,
        agentName = AGENT_NAME,
        channel = CHANNEL,
        type = COMPLAINT_TYPE,
        dateRaised = LocalDateTime.ofInstant(COMPLAINT_START_DATE, ZoneId.of("UTC")),
        status = "Complaint open",
        subStatus = "Under investigation",
        d1SignpostingCommsSent = false,
        d56SignpostingCommsSent = false,
        deadlockCommsSent = false,
        ofgemMappedComplaintCategory = COMPLAINT_SUBJECT,
        internalComplaintCategory = COMPLAINT_INTERNAL_SUBJECT,
        complaintsGoodwillAmount = COMPLAINT_GOODWILL_AMOUNT.toBigDecimal(),
        category1 = COMPLAINT_CATEGORY1,
        category2 = COMPLAINT_CATEGORY2,
        category3 = COMPLAINT_CATEGORY3,
    )

    fun buildTestComplaintMetadata(): ComplaintMetadata =
        ComplaintMetadata(customerId = CUSTOMER_ID, billingAccountIds = listOf(ACCOUNT_ID))

    private val sendD1EmailData = SendEmailData(
        complaintId = COMPLAINT_ID,
        ticketId = TICKET_ID.toString(),
        email = EMAIL,
        shouldSendD1Communication = true,
        shouldSendD56Communication = false
    )

    private val sendD56EmailData = SendEmailData(
        complaintId = COMPLAINT_ID,
        ticketId = TICKET_ID.toString(),
        email = EMAIL,
        shouldSendD1Communication = false,
        shouldSendD56Communication = true
    )

    private val sendRecentEmailData = SendEmailData(
        complaintId = COMPLAINT_ID,
        ticketId = TICKET_ID.toString(),
        email = EMAIL,
        shouldSendRecentCommunication = true,
    )

    private val sendOverdueEmailData = SendEmailData(
        complaintId = COMPLAINT_ID,
        ticketId = TICKET_ID.toString(),
        email = EMAIL,
        shouldSendOverdueCommunication = true,
    )

    val updateComplaintData = UpdateComplaintData(
        ticketId = TICKET_ID.toString(),
        complaintD1LetterSent = true,
    )

    val updateComplaintDataWithD56Sent = UpdateComplaintData(
        ticketId = TICKET_ID.toString(),
        complaintD56LetterSent = true,
    )


    val updateComplaintDataWithRecentComplaintSent = UpdateComplaintData(
        ticketId = TICKET_ID.toString(),
        complaintRecentLetterSent = true,
    )

    val updateComplaintDataWithOverdueComplaintSent = UpdateComplaintData(
        ticketId = TICKET_ID.toString(),
        complaintOverdueLetterSent = true
    )

    val d1EmailOutboxItem = OutboxItem(
        id = OUTBOX_ITEM_ID,
        type = OutboxOperationType.SEND_EMAIL,
        payload = JSON.json(Json.encodeToString(sendD1EmailData)),
        status = OutboxOperationStatus.PENDING,
    )

    val d56EmailOutboxItem = OutboxItem(
        id = OUTBOX_ITEM_ID,
        type = OutboxOperationType.SEND_EMAIL,
        payload = JSON.json(Json.encodeToString(sendD56EmailData)),
        status = OutboxOperationStatus.PENDING,
    )

    val recentEmailOutboxItem = OutboxItem(
        id = OUTBOX_ITEM_ID,
        type = OutboxOperationType.SEND_EMAIL,
        payload = JSON.json(Json.encodeToString(sendRecentEmailData)),
        status = OutboxOperationStatus.PENDING,
    )

    val overdueEmailOutboxItem = OutboxItem(
        id = OUTBOX_ITEM_ID,
        type = OutboxOperationType.SEND_EMAIL,
        payload = JSON.json(Json.encodeToString(sendOverdueEmailData)),
        status = OutboxOperationStatus.PENDING,
    )

    val complaintModel = Complaint(
        id = COMPLAINT_ID,
        externalIdentifier = TICKET_ID.toString(),
        details = ComplaintDetails(contactEmail = EMAIL, billingAccountIds = listOf(ACCOUNT_ID)),
        d1SignpostingCommsSent = false,
        d56SignpostingCommsSent = false,
        recentSignpostingCommsSent = false,
        overdueSignpostingCommsSent = false,
        status = "Complaint open",
        customerId = CUSTOMER_ID,
        dateRaised = LocalDateTime.now().minusDays(1),
        createdAt = LocalDateTime.now().minusDays(1)
    )

    val closedComplaintModel = Complaint(
        id = COMPLAINT_ID,
        externalIdentifier = TICKET_ID.toString(),
        details = ComplaintDetails(contactEmail = EMAIL, billingAccountIds = listOf(ACCOUNT_ID)),
        d1SignpostingCommsSent = false,
        d56SignpostingCommsSent = false,
        recentSignpostingCommsSent = false,
        overdueSignpostingCommsSent = false,
        status = ComplaintStatus.COMPLAINT_CLOSED.value,
        customerId = CUSTOMER_ID,
        dateRaised = LocalDateTime.now().minusDays(1),
        createdAt = LocalDateTime.now().minusDays(1)
    )

    val customer = customer {
        firstName = CUSTOMER_FIRST_NAME
        currentAccountId = ACCOUNT_ID
    }

    val billingAccount = billingAccount {
        number = ACCOUNT_NUMBER
        customerId = CUSTOMER_ID
    }

    fun buildComplaintsForSummary(): List<Complaint> {
        val list = mutableListOf<Complaint>()
        val defaultComplaintValues = buildTestExternalComplaint()

        defaultComplaintValues.dateRaised = Instant.now().minus(Duration.ofDays(30))
        list.add(defaultComplaintValues.toModel(buildTestComplaintMetadata()))

        defaultComplaintValues.dateRaised = Instant.now().minus(Duration.ofDays(13))
        defaultComplaintValues.complaintsGoodwillAmount = "52"
        defaultComplaintValues.complaintTicketId = 123L
        list.add(defaultComplaintValues.toModel(buildTestComplaintMetadata()))

        defaultComplaintValues.dateRaised = Instant.now().minus(Duration.ofDays(13))
        defaultComplaintValues.complaintsGoodwillAmount = "152"
        defaultComplaintValues.category3 = "Random category 3"
        defaultComplaintValues.complaintTicketId = 133L
        list.add(defaultComplaintValues.toModel(buildTestComplaintMetadata()))

        defaultComplaintValues.dateRaised = Instant.now().minus(Duration.ofDays(14))
        defaultComplaintValues.complaintTicketId = 132L
        defaultComplaintValues.complaintsGoodwillAmount = "234"
        defaultComplaintValues.category1 = "Another category 1"
        defaultComplaintValues.category2 = "Another category 2"
        defaultComplaintValues.category3 = "Another category 3"
        list.add(defaultComplaintValues.toModel(buildTestComplaintMetadata()))

        return list
    }

    fun buildDateIntervalFilter(ltDaysInPast: Long, gtDaysInPast: Long): DateInterval {
        val ltSeconds = LocalDateTime.now().minusDays(ltDaysInPast).toEpochSecond(ZoneOffset.UTC)
        val gtSeconds = LocalDateTime.now().minusDays(gtDaysInPast).toEpochSecond(ZoneOffset.UTC)

        return DateInterval(
            lt = Timestamp.newBuilder().setSeconds(ltSeconds).build(),
            gt = Timestamp.newBuilder().setSeconds(gtSeconds).build()
        )
    }

    val emptyComplaintFilter = ComplaintFilter(
        dateRaised = DateInterval(null, null),
        ageProfileList = emptyList(),
        statusList = emptyList(),
        subStatusList = emptyList(),
        channelList = emptyList(),
        typeList = emptyList(),
        category1List = emptyList(),
        category2List = emptyList(),
        category3List = emptyList(),
        internalCategoryList = emptyList(),
        ofgemCategoryList = emptyList(),
        agentList = emptyList(),
        teamList = emptyList()
    )

    val complaintFilter = ComplaintFilter(
        dateRaised = DateInterval(dateRaisedLessThan.toTimestamp(), null),
        ageProfileList = listOf(DateInterval(ageProfile1LessThan.toTimestamp(), ageProfile1GreaterThan.toTimestamp())),
        statusList = listOf(STATUS),
        subStatusList = listOf(SUBSTATUS),
        category1List = listOf(CATEGORY1),
        category2List = listOf(CATEGORY2),
        category3List = listOf(CATEGORY3),
        channelList = listOf(CHANNEL),
        typeList = listOf(TYPE),
        internalCategoryList = listOf(INTERNAL_CATEGORY),
        ofgemCategoryList = listOf(OFGEM_CATEGORY),
        agentList = listOf(AGENT),
        teamList = listOf(TEAM)
    )

    val protoComplaintFilter = complaintFilter {
        dateRaised = dateInterval {
            lt = dateRaisedLessThan.toTimestamp()
        }
        ageProfiles.add(dateInterval
        {
            lt = ageProfile1LessThan.toTimestamp()
            gt = ageProfile1GreaterThan.toTimestamp()
        })
        status.add(STATUS)
        subStatus.add(SUBSTATUS)
        channel.add(CHANNEL)
        type.add(TYPE)
        category1.add(CATEGORY1)
        category2.add(CATEGORY2)
        category3.add(CATEGORY3)
        ofgemCategory.add(OFGEM_CATEGORY)
        internalCategory.add(INTERNAL_CATEGORY)
        agent.add(AGENT)
        team.add(TEAM)
    }

    val complaintSummary = ComplaintSummary(
        category1 = TopCategory("Category 1", 1),
        category2 = TopCategory("Category 2", 2),
        category3 = TopCategory("Category 3", 3),
        goodwillAmountTotal = 120,
        total = 10,
        ageAverage = 14
    )

    val protoComplaintSummary = complaintSummary {
        category1 = topCategory {
            name = "Category 1"
            count = 1
        }
        category2 = topCategory {
            name = "Category 2"
            count = 2
        }
        category3 = topCategory {
            name = "Category 3"
            count = 3
        }
        goodwillAmountTotal = 120
        total = 10
        ageAverage = 14
    }
}