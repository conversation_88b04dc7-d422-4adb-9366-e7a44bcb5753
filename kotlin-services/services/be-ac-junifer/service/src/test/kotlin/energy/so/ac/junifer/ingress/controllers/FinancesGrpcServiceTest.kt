package energy.so.ac.junifer.ingress.controllers

import com.google.protobuf.Empty
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.juniferPaymentSchedulePeriod
import energy.so.ac.junifer.fixtures.CustomerPrecannedData
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.JUNIFER_PAYMENT_SCHEDULE_ID
import energy.so.ac.junifer.fixtures.activeSeasonalDefinitionProtoResponse
import energy.so.ac.junifer.fixtures.activeSeasonalDefinitionResponse
import energy.so.ac.junifer.fixtures.createPaymentSchedulePeriodRequest
import energy.so.ac.junifer.fixtures.stopPaymentSchedulePeriodRequest
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.finances.toCreatePaymentSchedulePeriod
import energy.so.ac.junifer.ingress.models.finances.toStopPaymentSchedulePeriod
import energy.so.ac.junifer.ingress.services.finances.JuniferFinancesService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.finances.copy
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk

class FinancesGrpcServiceTest : BehaviorSpec({

    val mockJuniferFinancesService = mockk<JuniferFinancesService>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val financesGrpcService = FinancesGrpcService(mockJuniferFinancesService, inMemoryMapper)

    afterEach {
        confirmVerified(mockJuniferFinancesService)
        clearMocks(mockJuniferFinancesService)
    }

    given("valid postcode") {
        val emptyRequest = Empty.getDefaultInstance()
        coEvery { mockJuniferFinancesService.getActiveSeasonalDefinition() } returns activeSeasonalDefinitionResponse

        `when`("call getGspGroupIdsByPostcode") {
            val actualResponse = financesGrpcService.getActiveSeasonalDefinition(emptyRequest)

            then("should be able to call getGspsByPostcode on JuniferAssestsService") {
                actualResponse shouldBe activeSeasonalDefinitionProtoResponse
                coVerify { mockJuniferFinancesService.getActiveSeasonalDefinition() }
            }
        }
    }

    given("::stopPaymentSchedulePeriod") {
        and("there is a mapping between account ids") {
            val accountId = stopPaymentSchedulePeriodRequest.accountId.toString()
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                CustomerPrecannedData.JUNIFER_CUSTOMER_ID.toString(),
                accountId
            )
            val juniferAccountId =
                inMemoryMapper.getJuniferId(EntityIdentifier.BILLING_ACCOUNT, accountId)!!.toLong()
            and("there is a mapping between payment schedule ids") {
                val coreId = stopPaymentSchedulePeriodRequest.id.toString()
                inMemoryMapper.createCoreMapping(
                    EntityIdentifier.PAYMENT_SCHEDULE,
                    JUNIFER_PAYMENT_SCHEDULE_ID.toString(),
                    coreId
                )
                val juniferId = inMemoryMapper.getJuniferId(EntityIdentifier.PAYMENT_SCHEDULE, coreId)
                and("valid stopPaymentSchedulePeriodRequest") {
                    val grpcRequest = stopPaymentSchedulePeriodRequest
                    val request = grpcRequest.toStopPaymentSchedulePeriod(juniferAccountId)
                    coJustRun { mockJuniferFinancesService.stopPaymentSchedulePeriod(juniferId!!, request) }
                    `when`("stopPaymentSchedulePeriod is called") {
                        val response = financesGrpcService.stopPaymentSchedulePeriod(grpcRequest)
                        then("response should be Empty") {
                            response shouldBe Empty.getDefaultInstance()
                            coVerify { mockJuniferFinancesService.stopPaymentSchedulePeriod(juniferId!!, request) }
                        }
                    }
                }
                and("invalid stopPaymentSchedulePeriodRequest") {
                    val invalidGrpcRequest = stopPaymentSchedulePeriodRequest.copy { frequency = "Something bad" }
                    val invalidRequest = invalidGrpcRequest.toStopPaymentSchedulePeriod(juniferAccountId)

                    coEvery {
                        mockJuniferFinancesService.stopPaymentSchedulePeriod(
                            juniferId!!,
                            invalidRequest
                        )
                    } throws JuniferException("NotFound", "error", "error")

                    `when`("stopPaymentSchedulePeriod is called") {
                        then("should throw EntityNotFoundGrpcException") {
                            shouldThrow<EntityNotFoundGrpcException> {
                                financesGrpcService.stopPaymentSchedulePeriod(
                                    invalidGrpcRequest
                                )
                            }
                            coVerify {
                                mockJuniferFinancesService.stopPaymentSchedulePeriod(
                                    juniferId!!,
                                    invalidRequest
                                )
                            }
                        }
                    }
                }
            }
            and("there is no mapping between payment schedule ids") {
                val request = stopPaymentSchedulePeriodRequest.copy { id = 999 }
                `when`("stopPaymentSchedulePeriod is called") {
                    then("should throw FailedPreconditionGrpcException") {
                        shouldThrow<FailedPreconditionGrpcException> {
                            financesGrpcService.stopPaymentSchedulePeriod(
                                request
                            )
                        }
                    }
                }
            }
        }
        and("there is no mapping between account ids") {
            inMemoryMapper.clear()
            val request = stopPaymentSchedulePeriodRequest

            and("valid data is given") {
                `when`("stopPaymentSchedulePeriod is called") {
                    then("should throw FailedPreconditionGrpcException") {
                        shouldThrow<FailedPreconditionGrpcException> {
                            financesGrpcService.stopPaymentSchedulePeriod(
                                request
                            )
                        }
                    }
                }
            }
        }
    }

    given("createPaymentSchedulePeriod") {
        and("there is a mapping between account ids") {
            val grpcRequest = createPaymentSchedulePeriodRequest
            val accountId = grpcRequest.accountId.toString()
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                CustomerPrecannedData.JUNIFER_CUSTOMER_ID.toString(),
                accountId
            )
            val juniferAccountId =
                inMemoryMapper.getJuniferId(EntityIdentifier.BILLING_ACCOUNT, accountId)!!.toLong()
            and("call to create payment schedule period to junifer ok") {
                val request = grpcRequest.toCreatePaymentSchedulePeriod(juniferAccountId)
                coEvery {
                    mockJuniferFinancesService.createPaymentSchedulePeriod(
                        request
                    )
                } returns juniferPaymentSchedulePeriod

                `when`("create payment schedule period") {
                    val response = financesGrpcService.createPaymentSchedulePeriod(grpcRequest)

                    then("payment schedule period is created and mapping is created") {
                        response.id shouldBe juniferPaymentSchedulePeriod.id.toString()
                        coVerify {
                            mockJuniferFinancesService.createPaymentSchedulePeriod(
                                request
                            )
                        }

                        val juniferId = inMemoryMapper.getJuniferId(
                            EntityIdentifier.PAYMENT_SCHEDULE,
                            grpcRequest.corePaymentScheduleId.toString()
                        )
                        juniferId shouldBe response.id
                    }
                }
            }
        }
    }
})
