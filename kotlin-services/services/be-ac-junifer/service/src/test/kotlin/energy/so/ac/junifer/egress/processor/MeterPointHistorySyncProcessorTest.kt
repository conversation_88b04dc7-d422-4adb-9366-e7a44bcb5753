package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointSupplyStatusRepository
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPAN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPRN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.METER_POINT_HISTORY_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.createMpanMeterPointHistorySync
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.createMprnMeterPointHistorySync
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.meterPointHistorySyncResponse
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.mpanSupplyStatusEvent
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.mprnSupplyStatusEvent
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMpan
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMpanSupplyStatus
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMprn
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMprnSupplyStatus
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.updateMpanMeterPointHistorySync
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.updateMprnMeterPointHistorySync
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT_HISTORY_ELECTRICITY
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT_HISTORY_GAS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.model.enums.EventType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class MeterPointHistorySyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterPointSupplyStatusRepository = mockk<JuniferMeterPointSupplyStatusRepository>()
    val mockMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val meterPointHistorySyncProcessor =
        MeterPointHistorySyncProcessor(
            mockMapper,
            mockMeterPointSupplyStatusRepository,
            mockMeterPointRepository,
            mockSyncClient
        )

    afterEach {
        confirmVerified(mockMapper, mockMeterPointSupplyStatusRepository, mockSyncClient)
    }

    given("Invalid sync event type") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointHistorySyncProcessor.process(
                        mpanSupplyStatusEvent.copy(eventType = EventType.ACCOUNT_CONTACT)
                    )
                }
            }
        }
    }

    given("Missing reference") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointHistorySyncProcessor.process(mpanSupplyStatusEvent.copy(reference = null))
                }
            }
        }
    }

    given("no existing mapped meter point history and an existing junifer meter point mpan supply status") {

        every {
            mockMapper.getCoreId(
                METER_POINT_HISTORY_ELECTRICITY,
                JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
            )
        } returns null

        `when`("a mpan supply status event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns
                    testJuniferMpanSupplyStatus

            every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()

            coEvery { mockSyncClient.syncMeterPointHistoryEntity(createMpanMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_HISTORY_ELECTRICITY,
                    JUNIFER_MPAN_SUPPLY_STATUS_ID.toString(),
                    METER_POINT_HISTORY_ID.toString()
                )
            }

            meterPointHistorySyncProcessor.process(mpanSupplyStatusEvent)

            then("a new meter point history should be created") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString(),
                        METER_POINT_HISTORY_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncMeterPointHistoryEntity(createMpanMeterPointHistorySync) }
            }
        }

        `when`("a mpan supply status delete event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns
                    testJuniferMpanSupplyStatus.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(createMpanMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_HISTORY_ELECTRICITY,
                    JUNIFER_MPAN_SUPPLY_STATUS_ID.toString(),
                    METER_POINT_HISTORY_ID.toString()
                )
            }

            meterPointHistorySyncProcessor.process(mpanSupplyStatusEvent)

            then("a new meter point history should be created with delete at timestamp") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString(),
                        METER_POINT_HISTORY_ID.toString()
                    )
                }
                coVerify {
                    mockSyncClient.syncMeterPointHistoryEntity(createMpanMeterPointHistorySync)
                }
            }
        }
    }

    given("an existing mapped meter point history and an existing junifer meter point mpan supply status") {

        every {
            mockMapper.getCoreId(
                METER_POINT_HISTORY_ELECTRICITY,
                JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
            )
        } returns METER_POINT_HISTORY_ID.toString()

        `when`("a mpan supply status event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns
                    testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(any()) } returns
                    meterPointHistorySyncResponse


            meterPointHistorySyncProcessor.process(mpanSupplyStatusEvent)

            then("meter point history should be patched") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify { mockSyncClient.syncMeterPointHistoryEntity(updateMpanMeterPointHistorySync) }
            }
        }

        `when`("a mpan supply status delete event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns
                    testJuniferMpanSupplyStatus.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(updateMpanMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse


            meterPointHistorySyncProcessor.process(mpanSupplyStatusEvent)

            then("meter point history should be deleted") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_ELECTRICITY,
                        JUNIFER_MPAN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify {
                    mockSyncClient.syncMeterPointHistoryEntity(updateMpanMeterPointHistorySync)
                }
            }
        }
    }

    given("no existing mapped meter point history and an existing junifer meter point mprn supply status") {

        every {
            mockMapper.getCoreId(
                METER_POINT_HISTORY_GAS,
                JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        `when`("a mprn supply status event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns
                    testJuniferMprnSupplyStatus
            every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn

            coEvery { mockSyncClient.syncMeterPointHistoryEntity(createMprnMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_HISTORY_GAS,
                    JUNIFER_MPRN_SUPPLY_STATUS_ID.toString(),
                    METER_POINT_HISTORY_ID.toString()
                )
            }

            meterPointHistorySyncProcessor.process(mprnSupplyStatusEvent)

            then("a new meter point history should be created") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString(),
                        METER_POINT_HISTORY_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncMeterPointHistoryEntity(createMprnMeterPointHistorySync) }
            }
        }

        `when`("a mprn supply status delete event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns
                    testJuniferMprnSupplyStatus.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(createMprnMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_HISTORY_GAS,
                    JUNIFER_MPRN_SUPPLY_STATUS_ID.toString(),
                    METER_POINT_HISTORY_ID.toString()
                )
            }

            meterPointHistorySyncProcessor.process(mprnSupplyStatusEvent)

            then("a new meter point history should be deleted") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString(),
                        METER_POINT_HISTORY_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                coVerify {
                    mockSyncClient.syncMeterPointHistoryEntity(createMprnMeterPointHistorySync)
                }
            }
        }
    }

    given("an existing mapped meter point history and an existing junifer meter point mprn supply status") {

        every {
            mockMapper.getCoreId(
                METER_POINT_HISTORY_GAS,
                JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
            )
        } returns METER_POINT_HISTORY_ID.toString()

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        `when`("a mprn supply status event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns
                    testJuniferMprnSupplyStatus
            every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(any()) } returns
                    meterPointHistorySyncResponse


            meterPointHistorySyncProcessor.process(mprnSupplyStatusEvent)

            then("meter point history should be patched") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                coVerify { mockSyncClient.syncMeterPointHistoryEntity(updateMprnMeterPointHistorySync) }
            }
        }

        `when`("a mprn supply status delete event is generated") {
            every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns
                    testJuniferMprnSupplyStatus.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn
            coEvery { mockSyncClient.syncMeterPointHistoryEntity(updateMprnMeterPointHistorySync) } returns
                    meterPointHistorySyncResponse


            meterPointHistorySyncProcessor.process(mprnSupplyStatusEvent)

            then("meter point history should be deleted") {
                verify {
                    mockMapper.getCoreId(
                        METER_POINT_HISTORY_GAS,
                        JUNIFER_MPRN_SUPPLY_STATUS_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                coVerify {
                    mockSyncClient.syncMeterPointHistoryEntity(updateMprnMeterPointHistorySync)
                }
            }
        }
    }
}) {
    override fun isolationMode() = IsolationMode.InstancePerTest

}
