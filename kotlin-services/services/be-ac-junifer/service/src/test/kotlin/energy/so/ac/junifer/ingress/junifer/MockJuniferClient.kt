package energy.so.ac.junifer.ingress.junifer

import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpResponseValidator
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.HttpRequest
import io.ktor.client.request.header
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.URLProtocol
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.KotlinxSerializationConverter
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json

object MockJuniferClient {

    val juniferConfig = JuniferConfig(
        protocol = "http",
        host = "localhost",
        port = 8080,
        apiVersion = "v1",
        connectionTimeout = 1000,
        requestTimeout = 1000,
        username = "user",
        apiKey = "key",
    )

    @OptIn(ExperimentalSerializationApi::class)
    fun createMockHttpClient(mockEngine: HttpClientEngine): HttpClient {
        return HttpClient(mockEngine) {
            install(ContentNegotiation) {
                json(
                    Json {
                        isLenient = true
                        ignoreUnknownKeys = true
                        explicitNulls = false
                    }
                )
                register(
                    ContentType.Any, KotlinxSerializationConverter(
                        Json {
                            prettyPrint = true
                            isLenient = true
                            ignoreUnknownKeys = true
                        }
                    )
                )
            }

            HttpResponseValidator {
                validateResponse { JuniferResponseValidator.validate(it, juniferConfig.apiVersion) }
            }

            defaultRequest {
                url {
                    protocol = when (juniferConfig.protocol.uppercase()) {
                        "HTTPS" -> URLProtocol.HTTPS
                        else -> URLProtocol.HTTP
                    }
                }

                host = juniferConfig.host
                port = juniferConfig.port
                header(X_JUNIFER_X_USERNAME_HEADER_NAME, juniferConfig.username)
                header(X_JUNIFER_X_APIKEY_HEADER_NAME, juniferConfig.apiKey)
                contentType(ContentType.Application.Json)
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    fun createRetryingMockClient(
        mockEngine: HttpClientEngine,
        shouldRetry: HttpRequestRetry.ShouldRetryContext.(HttpRequest, HttpResponse) -> Boolean,
        maxRetries: Int = 10,
    ): HttpClient = createMockHttpClient(mockEngine).config {
        install(HttpRequestRetry) {
            retryIf(maxRetries = maxRetries, block = shouldRetry)
            delayMillis { 0 }
        }
    }
}