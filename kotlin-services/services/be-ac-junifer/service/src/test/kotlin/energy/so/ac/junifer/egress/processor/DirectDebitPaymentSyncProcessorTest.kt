package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentRepository
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.DIRECT_DEBIT_PAYMENT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.JUNIFER_GO_CARDLESS_DD_COLLECTION_ID
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.JUNIFER_PAYMENT_REQUEST_ATTEMPT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.createDirectDebitPaymentSync
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.deleteDirectDebitPaymentSync
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.directDebitPaymentSyncEvent
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.directDebitPaymentSyncResponse
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.paymentRequestAttemptSyncEvent
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferGocardlessDDCollection
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferPaymentRequestAttempt
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.updateDirectDebitPaymentSync
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.JUNIFER_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.PAYMENT_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.DIRECT_DEBIT
import energy.so.ac.junifer.mapping.EntityIdentifier.DIRECT_DEBIT_PAYMENT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class DirectDebitPaymentSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentRepository = mockk<JuniferPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val directDebitPaymentProcessor =
        DirectDebitPaymentSyncProcessor(mockMapper, mockPaymentRepository, mockSyncClient, 2, 1)

    afterEach {
        confirmVerified(mockMapper, mockPaymentRepository, mockSyncClient)
    }

    given("no existing mapped directDebitPayment and an existing junifer directDebitPayment") {

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT,
                JUNIFER_DIRECT_DEBIT_ID.toString()
            )
        } returns DIRECT_DEBIT_ID.toString()

        every { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns testJuniferGocardlessDDCollection
        every { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns JUNIFER_PAYMENT_REQUEST_ID
        every { mockPaymentRepository.getJuniferAccountIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns JUNIFER_ACCOUNT_ID

        `when`("a directDebitPayment event is generated") {

            coEvery { mockSyncClient.syncDirectDebitPaymentEntity(createDirectDebitPaymentSync) } returns directDebitPaymentSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    DIRECT_DEBIT_PAYMENT,
                    JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString(),
                    DIRECT_DEBIT_PAYMENT_ID.toString()
                )
            }

            directDebitPaymentProcessor.process(directDebitPaymentSyncEvent)

            then("a new directDebitPayment should be created") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT_PAYMENT, JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }

                verify { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify { mockPaymentRepository.getJuniferAccountIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        DIRECT_DEBIT_PAYMENT,
                        JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString(),
                        DIRECT_DEBIT_PAYMENT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncDirectDebitPaymentEntity(createDirectDebitPaymentSync) }
            }
        }
    }

    given("no existing mapped directDebitPayment and an existing junifer directDebitPayment with no succesful attempts") {
        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns null

        every { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns testJuniferGocardlessDDCollection
        every { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns null

        `when`("a directDebitPayment event is generated") {

            directDebitPaymentProcessor.process(directDebitPaymentSyncEvent)

            then("nothing should be synched") {
                verify {
                    mockMapper.getCoreId(
                        DIRECT_DEBIT_PAYMENT,
                        JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
                    )
                }
                verify { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
            }
        }
    }

    given("an existing mapped directDebitPayment and an existing junifer directDebitPayment") {

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns DIRECT_DEBIT_PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT,
                JUNIFER_DIRECT_DEBIT_ID.toString()
            )
        } returns DIRECT_DEBIT_ID.toString()

        every { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns testJuniferGocardlessDDCollection
        every { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns JUNIFER_PAYMENT_REQUEST_ID
        every { mockPaymentRepository.getJuniferAccountIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } returns JUNIFER_ACCOUNT_ID

        `when`("a directDebitPayment event is generated") {

            coEvery { mockSyncClient.syncDirectDebitPaymentEntity(updateDirectDebitPaymentSync) } returns directDebitPaymentSyncResponse

            directDebitPaymentProcessor.process(directDebitPaymentSyncEvent)

            then("directDebitPayment should be patched") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT_PAYMENT, JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify { mockPaymentRepository.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                verify { mockPaymentRepository.getJuniferAccountIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                coVerify { mockSyncClient.syncDirectDebitPaymentEntity(updateDirectDebitPaymentSync) }
            }
        }
    }

    given("an existing mapped directDebitPayment and existing junifer directDebitPayment to be deleted") {

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns DIRECT_DEBIT_PAYMENT_ID.toString()
        justRun {
            mockMapper.deleteMappingByJuniferId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        }

        every { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) } throws EntityNotFoundException(
            "DD not found"
        )

        `when`("a directDebitPayment event is generated") {

            coEvery { mockSyncClient.syncDirectDebitPaymentEntity(deleteDirectDebitPaymentSync) } returns directDebitPaymentSyncResponse

            directDebitPaymentProcessor.process(directDebitPaymentSyncEvent)

            then("directDebitPayment should be deleted") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT_PAYMENT, JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()) }
                verify {
                    mockMapper.deleteMappingByJuniferId(
                        DIRECT_DEBIT_PAYMENT,
                        JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
                    )
                }
                verify { mockPaymentRepository.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID) }
                coVerify { mockSyncClient.syncDirectDebitPaymentEntity(deleteDirectDebitPaymentSync) }
            }
        }
    }

    given("an existing mapped directDebitPayment and a payment request attempt update with failed status") {

        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns DIRECT_DEBIT_PAYMENT_ID.toString()
        justRun {
            mockMapper.deleteMappingByJuniferId(
                DIRECT_DEBIT_PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        }

        every { mockPaymentRepository.getPaymentRequestAttempt(JUNIFER_PAYMENT_REQUEST_ATTEMPT_ID) } returns testJuniferPaymentRequestAttempt.copy(
            status = "Failed"
        )
        every {
            mockPaymentRepository.getGoCardlessDDCollectionByPaymentRequestAttempt(
                JUNIFER_PAYMENT_REQUEST_ATTEMPT_ID
            )
        } returns testJuniferGocardlessDDCollection

        `when`("a payment request attempt event is generated") {

            coEvery { mockSyncClient.syncDirectDebitPaymentEntity(deleteDirectDebitPaymentSync) } returns directDebitPaymentSyncResponse

            directDebitPaymentProcessor.process(paymentRequestAttemptSyncEvent)

            then("directDebitPayment should be deleted") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT_PAYMENT, JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()) }
                verify {
                    mockMapper.deleteMappingByJuniferId(
                        DIRECT_DEBIT_PAYMENT,
                        JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
                    )
                }
                verify { mockPaymentRepository.getPaymentRequestAttempt(JUNIFER_PAYMENT_REQUEST_ATTEMPT_ID) }
                verify {
                    mockPaymentRepository.getGoCardlessDDCollectionByPaymentRequestAttempt(
                        JUNIFER_PAYMENT_REQUEST_ATTEMPT_ID
                    )
                }
                coVerify { mockSyncClient.syncDirectDebitPaymentEntity(deleteDirectDebitPaymentSync) }
            }
        }
    }
})
