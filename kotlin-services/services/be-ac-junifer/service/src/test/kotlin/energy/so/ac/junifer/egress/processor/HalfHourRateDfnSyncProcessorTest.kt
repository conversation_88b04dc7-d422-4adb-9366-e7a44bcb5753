package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferHalfHourRateDfnRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.PRODUCT_RATE_ID
import energy.so.ac.junifer.fixtures.ProductPrecannedData.productSyncEvent
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testHalfHourRateDfn
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testHalfHourRateDfnItemModel
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnCreateSyncRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnItemCreateSyncRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnItemPatchSyncRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnItemSyncEvent
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnPatchSyncRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.halfHourRateDfnSyncEvent
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.hhRateSyncResponse
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.v2.sync.OperationType
import energy.so.products.client.v2.SyncClient
import energy.so.products.sync.v2.halfHourRateDfn
import energy.so.products.sync.v2.halfHourRateDfnEntityRequest
import energy.so.products.sync.v2.halfHourRateDfnItem
import energy.so.products.sync.v2.halfHourRateDfnItemEntityRequest
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class HalfHourRateDfnSyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockHalfHourRateDfnRepository = mockk<JuniferHalfHourRateDfnRepository>()
    val mockProductsSyncClient = mockk<SyncClient>()

    val hhRateProcessor =
        HalfHourRateDfnSyncProcessor(mockMapper, mockHalfHourRateDfnRepository, mockProductsSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockHalfHourRateDfnRepository, mockProductsSyncClient)
        clearMocks(mockMapper, mockHalfHourRateDfnRepository, mockProductsSyncClient)
    }

    given("sync event with null reference") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    hhRateProcessor.process(halfHourRateDfnSyncEvent.copy(reference = null))
                }.let {
                    it.message shouldBe "Cannot process sync event without a reference"
                }
            }
        }
    }

    given("invalid sync event type") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    hhRateProcessor.process(productSyncEvent)
                }.let {
                    it.message shouldBe "Unsupported EventType PRODUCT_BUNDLE_DEFINITION"
                }
            }
        }
    }

    given("correct HALF_HOUR_RATE_DFN sync event") {
        and("no HALF_HOUR_RATE_DFN core mapping") {
            and("HALF_HOUR_RATE_DFN corresponding to reference not found in db") {
                coEvery { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) } returns null
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) } throws EntityNotFoundException("Not Found")

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnSyncEvent)

                    then("sync not called") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) }
                        coVerify(exactly = 0) { mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(any()) }
                    }
                }
            }

            and("HALF_HOUR_RATE_DFN corresponding to reference found in db") {
                coEvery { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) } returns null
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) } returns testHalfHourRateDfn
                coEvery { mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(any()) } returns hhRateSyncResponse
                coJustRun {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        JUNIFER_HALF_HOUR_RATE_DFN_ID.toString(),
                        HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                    )
                }

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnSyncEvent)

                    then("CREATED sync event") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) }
                        coVerify {
                            mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(
                                halfHourRateDfnCreateSyncRequest
                            )
                        }

                        coVerify {
                            mockMapper.createCoreMapping(
                                EntityIdentifier.HALF_HOUR_RATE_DFN,
                                JUNIFER_HALF_HOUR_RATE_DFN_ID.toString(),
                                HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                            )
                        }
                    }
                }
            }
        }

        and("HALF_HOUR_RATE_DFN core mapping") {
            and("HALF_HOUR_RATE_DFN corresponding to reference not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        ID_1.toString()
                    )
                } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) } throws EntityNotFoundException("Not Found")
                coEvery { mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(any()) } returns hhRateSyncResponse

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnSyncEvent)

                    then("DELETE sync event, mapping deleted") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) }
                        coVerify {
                            mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(
                                halfHourRateDfnEntityRequest {
                                    halfHourRateDfnEntity = halfHourRateDfn {
                                        id = HALF_HOUR_RATE_DFN_ITEM_ID.toNullableInt64()
                                    }
                                    operationType = OperationType.DELETE
                                }
                            )
                        }

                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }

            and("HALF_HOUR_RATE_DFN corresponding to reference found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        ID_1.toString()
                    )
                } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) } returns testHalfHourRateDfn
                coEvery { mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(any()) } returns hhRateSyncResponse

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnSyncEvent)

                    then("UPDATED sync event, no new mapping created") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfn(ID_1) }
                        coVerify {
                            mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(
                                halfHourRateDfnPatchSyncRequest
                            )
                        }

                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }
    }

    given("correct HALF_HOUR_RATE_DFN_ITEM sync event") {
        and("no HALF_HOUR_RATE_DFN_ITEM core mapping") {
            and("HALF_HOUR_RATE_DFN_ITEM corresponding to reference not found in db") {
                coEvery { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) } returns null
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) } throws EntityNotFoundException("Not Found")

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnItemSyncEvent)

                    then("sync not called") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) }
                        coVerify(exactly = 0) { mockProductsSyncClient.syncHalfHourRateDfnEntityRequest(any()) }
                    }
                }
            }

            and("HALF_HOUR_RATE_DFN_ITEM corresponding to reference found in db") {

                and("HALF_HOUR_RATE_DFN mapping doesn't exist") {
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                            ID_1.toString()
                        )
                    } returns null
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.HALF_HOUR_RATE_DFN,
                            JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                        )
                    } returns null
                    coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) } returns testHalfHourRateDfnItemModel
                    coEvery { mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(any()) } returns hhRateSyncResponse

                    `when`("call process") {

                        then("should throw SyncDelayedException") {
                            shouldThrow<SyncDelayedException> { hhRateProcessor.process(halfHourRateDfnItemSyncEvent) }

                            coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) }
                            coVerify {
                                mockMapper.getCoreId(
                                    EntityIdentifier.HALF_HOUR_RATE_DFN,
                                    JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                                )
                            }
                            coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) }
                            coVerify(exactly = 0) {
                                mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(
                                    halfHourRateDfnItemCreateSyncRequest
                                )
                            }

                            coVerify(exactly = 0) {
                                mockMapper.createCoreMapping(
                                    any(),
                                    any(),
                                    any()
                                )
                            }
                        }
                    }
                }

                and("HALF_HOUR_RATE_DFN mapping exists") {
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                            ID_1.toString()
                        )
                    } returns null
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.HALF_HOUR_RATE_DFN,
                            JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                        )
                    } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                    coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) } returns testHalfHourRateDfnItemModel
                    coEvery { mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(any()) } returns hhRateSyncResponse
                    coJustRun {
                        mockMapper.createCoreMapping(
                            EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                            JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID.toString(),
                            HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                        )
                    }
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.PRODUCT_RATE,
                            testHalfHourRateDfnItemModel.rateNameFk.toString()
                        )
                    } returns PRODUCT_RATE_ID.toString()

                    `when`("call process") {
                        hhRateProcessor.process(halfHourRateDfnItemSyncEvent)
                        then("CREATED sync event, new mapping created") {

                            coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) }
                            coVerify {
                                mockMapper.getCoreId(
                                    EntityIdentifier.HALF_HOUR_RATE_DFN,
                                    JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                                )
                            }
                            coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) }
                            coVerify {
                                mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(
                                    halfHourRateDfnItemCreateSyncRequest
                                )
                            }

                            coVerify {
                                mockMapper.createCoreMapping(
                                    EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                                    JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID.toString(),
                                    HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                                )
                            }
                            coVerify {
                                mockMapper.getCoreId(
                                    EntityIdentifier.PRODUCT_RATE,
                                    testHalfHourRateDfnItemModel.rateNameFk.toString()
                                )
                            }
                        }
                    }
                }
            }
        }

        and("HALF_HOUR_RATE_DFN_ITEM core mapping") {
            and("HALF_HOUR_RATE_DFN_ITEM corresponding to reference not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                        ID_1.toString()
                    )
                } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) } throws EntityNotFoundException("Not Found")
                coEvery { mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(any()) } returns hhRateSyncResponse

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnItemSyncEvent)

                    then("DELETE sync event, mapping deleted") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) }
                        coVerify {
                            mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(
                                halfHourRateDfnItemEntityRequest {
                                    halfHourRateDfnItemEntity = halfHourRateDfnItem {
                                        id = HALF_HOUR_RATE_DFN_ITEM_ID.toNullableInt64()
                                    }
                                    operationType = OperationType.DELETE
                                }
                            )
                        }

                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }

            and("HALF_HOUR_RATE_DFN_ITEM corresponding to reference found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                        ID_1.toString()
                    )
                } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                    )
                } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        testHalfHourRateDfnItemModel.rateNameFk.toString()
                    )
                } returns PRODUCT_RATE_ID.toString()
                coEvery { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) } returns testHalfHourRateDfnItemModel
                coEvery { mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(any()) } returns hhRateSyncResponse

                `when`("call process") {
                    hhRateProcessor.process(halfHourRateDfnItemSyncEvent)

                    then("UPDATED sync event, no new mapping created") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, ID_1.toString()) }
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.HALF_HOUR_RATE_DFN,
                                JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                            )
                        }
                        coVerify { mockHalfHourRateDfnRepository.getHalfHourRateDfnItem(ID_1) }
                        coVerify {
                            mockProductsSyncClient.syncHalfHourRateDfnItemEntityRequest(
                                halfHourRateDfnItemPatchSyncRequest
                            )
                        }
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_RATE,
                                testHalfHourRateDfnItemModel.rateNameFk.toString()
                            )
                        }
                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }
    }
})
