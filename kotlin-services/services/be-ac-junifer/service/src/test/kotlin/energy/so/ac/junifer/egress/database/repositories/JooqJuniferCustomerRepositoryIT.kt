@file:Suppress("SameParameterValue")

package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMER
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMERCLASS
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMERPROPERTY
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMERTYPE
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferCustomerRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferCustomerRepository(db)

    given("Customer exists in DB") {
        insertCustomer(1, db, "name1")

        `when`("Customer is queried") {
            val customer = repo.findCustomer(1)

            then("Customer is returned") {
                customer shouldNotBe null
                customer!!
                customer.id shouldBe 1
                customer.name shouldBe "name1"
            }
        }
    }

    given("Customer doesn't exist in DB") {

        `when`("Customer is queried") {
            val type = repo.findCustomer(1)

            then("Customer should not be returned") {
                type shouldBe null
            }
        }
    }

    given("Customer Type exists in DB") {
        insertCustomerType(1, db, "type1")

        `when`("Customer Type is queried") {
            val customerType = repo.findCustomerType(1)

            then("Customer Type is returned") {
                customerType shouldNotBe null
                customerType!!
                customerType.id shouldBe 1
                customerType.internalkey shouldBe "type1"
            }
        }
    }

    given("Customer Type doesn't exist in DB") {

        `when`("Customer Type is queried") {
            val customerType = repo.findCustomerType(1)

            then("Customer Type should not be returned") {
                customerType shouldBe null
            }
        }
    }

    given("Customer Class exists in DB") {
        insertCustomerClass(1, db, "class1")

        `when`("Customer Class is queried") {
            val customerClass = repo.findCustomerClass(1)

            then("Customer Class is returned") {
                customerClass shouldNotBe null
                customerClass!!
                customerClass.id shouldBe 1
                customerClass.internalkey shouldBe "class1"
            }
        }
    }

    given("Customer Class doesn't exist in DB") {

        `when`("Customer Class is queried") {
            val customerClass = repo.findCustomerClass(1)

            then("Customer Class should not be returned") {
                customerClass shouldBe null
            }
        }
    }

    given("CustomerProperty record is inserted") {

        insertCustomerProperty(db)

        `when`("the CustomerProperty is queried") {

            val returnedJuniferCustomerProperty =
                repo.getCustomerProperty(CustomerPropertyPrecannedData.JUNIFER_CUSTOMER_PROPERTY_ID)

            then("the CustomerProperty should be returned") {
                returnedJuniferCustomerProperty shouldNotBe null
            }
        }
    }
})

private fun insertCustomer(id: Long, db: DSLContext, name: String) {
    db.insertInto(JUNIFER__CUSTOMER).columns(
        JUNIFER__CUSTOMER.ID,
        JUNIFER__CUSTOMER.NAME,
    ).values(id, name).execute()
}

private fun insertCustomerType(id: Long, db: DSLContext, key: String) {
    db.insertInto(JUNIFER__CUSTOMERTYPE).columns(
        JUNIFER__CUSTOMERTYPE.ID, JUNIFER__CUSTOMERTYPE.INTERNALKEY
    ).values(id, key).execute()
}

private fun insertCustomerClass(id: Long, db: DSLContext, key: String) {
    db.insertInto(JUNIFER__CUSTOMERCLASS).columns(
        JUNIFER__CUSTOMERCLASS.ID, JUNIFER__CUSTOMERCLASS.INTERNALKEY
    ).values(id, key).execute()
}

fun insertCustomerProperty(db: DSLContext) {
    db.insertInto(JUNIFER__CUSTOMERPROPERTY).columns(
        JUNIFER__CUSTOMERPROPERTY.ID,
        JUNIFER__CUSTOMERPROPERTY.CUSTOMERFK,
        JUNIFER__CUSTOMERPROPERTY.PROPERTYTBLFK
    ).values(
        CustomerPropertyPrecannedData.JUNIFER_CUSTOMER_PROPERTY_ID,
        CustomerPsrPrecannedData.JUNIFER_CUSTOMER_ID,
        CustomerPropertyPrecannedData.JUNIFER_PROPERTY_ID.toLong()
    ).execute()
}