package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.CustomerPrecannedData.enrollCustomerProto
import energy.so.ac.junifer.ingress.models.customers.JuniferEnrollCustomerRequest
import energy.so.commons.grpc.utils.toLocalDate
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferEnrollCustomerResponseTest : BehaviorSpec({
    given("customer to enroll") {
        val proto = enrollCustomerProto
        `when`("call fromProto method") {
            val result = JuniferEnrollCustomerRequest.toRequest(proto)
            then("result match") {
                result.senderReference shouldBe proto.senderReference
                result.reference shouldBe proto.reference
                result.billingEntityCode shouldBe proto.billingEntityCode
                result.marketingOptOutFl shouldBe proto.marketingOptOutFl
                result.submittedSource shouldBe proto.submittedSource
                result.changeOfTenancyFl shouldBe proto.changeOfTenancyFl
                result.contacts[0].title shouldBe proto.contactsList[0].title
                result.contacts[0].forename shouldBe proto.contactsList[0].forename
                result.contacts[0].surname shouldBe proto.contactsList[0].surname
                result.contacts[0].billingMethod shouldBe proto.contactsList[0].billingMethod
                result.contacts[0].email shouldBe proto.contactsList[0].email
                result.contacts[0].phone1 shouldBe proto.contactsList[0].phone1
                result.contacts[0].phone2 shouldBe proto.contactsList[0].phone2
                result.contacts[0].phone3 shouldBe proto.contactsList[0].phone3
                result.contacts[0].dateOfBirth shouldBe proto.contactsList[0].dateOfBirth.toLocalDate()
                result.contacts[0].primaryContact shouldBe proto.contactsList[0].primaryContact
                result.contacts[0].address.address1 shouldBe proto.contactsList[0].address.address1
                result.contacts[0].address.address2 shouldBe proto.contactsList[0].address.address2
                result.contacts[0].address.address3 shouldBe proto.contactsList[0].address.address3
                result.contacts[0].address.town shouldBe proto.contactsList[0].address.town
                result.contacts[0].address.postcode shouldBe proto.contactsList[0].address.postcode
                result.contacts[0].address.countryCode shouldBe proto.contactsList[0].address.countryCode
                result.supplyAddress.careOf shouldBe proto.supplyAddress.careOf
                result.supplyAddress.address1 shouldBe proto.supplyAddress.address1
                result.supplyAddress.address2 shouldBe proto.supplyAddress.address2
                result.supplyAddress.address3 shouldBe proto.supplyAddress.address3
                result.supplyAddress.town shouldBe proto.supplyAddress.town
                result.supplyAddress.postcode shouldBe proto.supplyAddress.postcode
                result.supplyAddress.countryCode shouldBe proto.supplyAddress.countryCode
                result.directDebitInfo?.bankAccountSortCode shouldBe proto.directDebitInfo.bankAccountSortCode
                result.directDebitInfo?.bankAccountNumber shouldBe proto.directDebitInfo.bankAccountNumber
                result.directDebitInfo?.bankAccountName shouldBe proto.directDebitInfo.bankAccountName
                result.directDebitInfo?.regularPaymentAmount shouldBe proto.directDebitInfo.regularPaymentAmount
                result.directDebitInfo?.regularPaymentDayOfMonth shouldBe proto.directDebitInfo.regularPaymentDayOfMonth
                result.directDebitInfo?.seasonalPaymentFl shouldBe proto.directDebitInfo.seasonalPaymentFl
                result.electricityProduct?.previousSupplier shouldBe proto.electricityProduct.previousSupplier
                result.electricityProduct?.previousTariff shouldBe proto.electricityProduct.previousTariff
                result.electricityProduct?.reference shouldBe proto.electricityProduct.reference
                result.electricityProduct?.newConnectionFl shouldBe proto.electricityProduct.newConnectionFl
                result.electricityProduct?.productCode shouldBe proto.electricityProduct.productCode
                result.electricityProduct?.mpans!![0].mpanCore shouldBe proto.electricityProduct.mpansList[0].mpanCore
                result.electricityProduct?.mpans!![0].measurementType shouldBe proto.electricityProduct.mpansList[0].measurementType
                result.gasProduct?.previousSupplier shouldBe proto.gasProduct.previousSupplier
                result.gasProduct?.previousTariff shouldBe proto.gasProduct.previousTariff
                result.gasProduct?.reference shouldBe proto.gasProduct.reference
                result.gasProduct?.productCode shouldBe proto.gasProduct.productCode
                result.gasProduct?.mprns!![0].mprn shouldBe proto.gasProduct.mprnsList[0].mprn
                result.gasProduct?.domesticLargeSupplyPointFl shouldBe proto.gasProduct.domesticLargeSupplyPointFl
                result.gasEstimate?.estimatedCost shouldBe proto.gasEstimate.estimatedCost
                result.gasEstimate?.estimatedSaving shouldBe proto.gasEstimate.estimatedSaving
                result.gasEstimate?.consumption shouldBe proto.gasEstimate.consumption
                result.gasEstimate?.previousCost shouldBe proto.gasEstimate.previousCost
                result.gasEstimate?.period shouldBe proto.gasEstimate.period
                result.electricityEstimate?.estimatedCost shouldBe proto.electricityEstimate.estimatedCost
                result.electricityEstimate?.estimatedSaving shouldBe proto.electricityEstimate.estimatedSaving
                result.electricityEstimate?.consumption shouldBe proto.electricityEstimate.consumption
                result.electricityEstimate?.previousCost shouldBe proto.electricityEstimate.previousCost
                result.electricityEstimate?.e7DaytimeConsumptionProportion shouldBe proto.electricityEstimate.e7DaytimeConsumptionProportion
                result.electricityEstimate?.period shouldBe proto.electricityEstimate.period
            }
        }
    }
})
