package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILL_DIRTY_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILL_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.testJuniferBill
import energy.so.ac.junifer.fixtures.BillPrecannedData.testJuniferBillDirty
import energy.so.ac.junifer.fixtures.BillPrecannedData.testJuniferClearedBillDirty
import energy.so.commons.model.tables.references.JUNIFER__BILL
import energy.so.commons.model.tables.references.JUNIFER__BILLDIRTY
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferBillRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferBillRepository(db)

    given("Bill exists in DB") {
        insertBill(db)

        `when`("Bill is queried") {
            val bill = repo.getBill(JUNIFER_BILL_ID)

            then("Bill is returned") {

                with(bill) {
                    this shouldNotBe null
                    id shouldBe testJuniferBill.id
                    accountfk shouldBe testJuniferBill.accountfk
                    accounttransactionfk shouldBe testJuniferBill.accounttransactionfk
                    billperiodfk shouldBe testJuniferBill.billperiodfk
                    billedfromdttm shouldBe testJuniferBill.billedfromdttm
                    billedtodttm shouldBe testJuniferBill.billedtodttm
                    billrequestfk shouldBe testJuniferBill.billrequestfk
                    description shouldBe testJuniferBill.description
                    versionnumber shouldBe testJuniferBill.versionnumber
                    createddttm shouldBe testJuniferBill.createddttm
                    createdusertblfk shouldBe testJuniferBill.createdusertblfk
                    issuedt shouldBe testJuniferBill.issuedt
                    duedt shouldBe testJuniferBill.duedt
                    netamount?.toDouble() shouldBe testJuniferBill.netamount?.toDouble()
                    grossamount?.toDouble() shouldBe testJuniferBill.grossamount?.toDouble()
                    currencyfk shouldBe testJuniferBill.currencyfk
                    salestaxamount shouldBe testJuniferBill.salestaxamount
                    status shouldBe testJuniferBill.status
                    draftreason shouldBe testJuniferBill.draftreason
                    accepteddttm shouldBe testJuniferBill.accepteddttm
                    acceptedbyusertblfk shouldBe testJuniferBill.acceptedbyusertblfk
                    lessthanminimumfl shouldBe testJuniferBill.lessthanminimumfl
                    supersededfl shouldBe testJuniferBill.supersededfl
                    supersededbybillfk shouldBe testJuniferBill.supersededbybillfk
                    finalfl shouldBe testJuniferBill.finalfl
                    disallowreversionfl shouldBe testJuniferBill.disallowreversionfl
                    lastshownaccounttransactionfk shouldBe testJuniferBill.lastshownaccounttransactionfk
                    comments shouldBe testJuniferBill.comments
                    reference shouldBe testJuniferBill.reference
                    deletefl shouldBe testJuniferBill.deletefl
                    versionno shouldBe testJuniferBill.versionno
                    partitionid shouldBe testJuniferBill.partitionid
                }
            }
        }
    }

    given("Bill Dirty exists in DB") {
        insertBillDirty(db)

        `when`("Bill Dirty is queried") {
            val billDirty = repo.getBillDirty(JUNIFER_BILL_ID)

            then("Bill Dirty is returned") {
                billDirty shouldNotBe null
                with(billDirty) {
                    this?.id?.shouldBe(JUNIFER_BILL_DIRTY_ID)
                    this?.billfk?.shouldBe(JUNIFER_BILL_ID)
                }
            }
        }
    }

    given("Cleared Bill Dirty exists in DB") {
        insertClearedBillDirty(db)

        `when`("Bill Dirty is queried") {
            val billDirty = repo.getBillDirty(JUNIFER_BILL_ID)

            then("Bill Dirty should not be returned") {
                billDirty shouldBe null
            }
        }
    }
})

fun insertBill(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__BILL, testJuniferBill))
}

fun insertBillDirty(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__BILLDIRTY, testJuniferBillDirty))
}

fun insertClearedBillDirty(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__BILLDIRTY, testJuniferClearedBillDirty))
}
