package energy.so.ac.junifer.admin.services

import energy.so.ac.junifer.admin.database.repositories.ResyncRepository
import energy.so.ac.junifer.config.SyncOptimizationConfig
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class EventTriggerManipulatorTest : BehaviorSpec({
    val mockSyncOptimizationConfig = mockk<SyncOptimizationConfig>()
    val mockResyncRepo = mockk<ResyncRepository>(relaxed = true)

    val sut = EventTriggerManipulator(mockSyncOptimizationConfig, mockResyncRepo)

    afterEach {
        confirmVerified(mockResyncRepo, mockResyncRepo)
        clearMocks(mockResyncRepo, mockResyncRepo)
    }

    given("Optimized data sync config") {
        `when`("optimizedDataSync is enabled") {
            every { mockSyncOptimizationConfig.optimizedDataSync } returns true

            sut.init()

            then("correct mock trigger function is called") {
                verify(exactly = 0) { mockResyncRepo.createEventTriggerFunction() }
                verify { mockResyncRepo.createMockEventTriggerFunction() }
            }
        }

        `when`("optimizedDataSync is disabled") {
            every { mockSyncOptimizationConfig.optimizedDataSync } returns false

            sut.init()

            then("correct real trigger function is called") {
                verify { mockResyncRepo.createEventTriggerFunction() }
                verify(exactly = 0) { mockResyncRepo.createMockEventTriggerFunction() }
            }
        }
    }
})
