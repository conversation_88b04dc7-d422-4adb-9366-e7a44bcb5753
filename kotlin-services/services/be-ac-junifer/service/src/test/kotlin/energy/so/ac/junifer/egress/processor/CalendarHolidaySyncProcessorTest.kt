package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferCalendarHolidayRepository
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.CALENDAR_HOLIDAY_ID
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.JUNIFER_CALENDAR_HOLIDAY_ID
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.calendarHolidaySyncEvent
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.calendarHolidaySyncEventNoReference
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.calendarHolidaySyncResponse
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.createCalendarHolidaySync
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.deleteCalendarHolidaySync
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.testCalendarholiday
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.updateCalendarHolidaySync
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class CalendarHolidaySyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockCalendarHolidayRepository = mockk<JuniferCalendarHolidayRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val calendarHolidaySyncProcessor =
        CalendarHolidaySyncProcessor(mockMapper, mockCalendarHolidayRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockCalendarHolidayRepository, mockSyncClient)
        clearAllMocks()
    }

    given("event with null reference") {
        `when`("a calendar_holiday event is generated") {
            then("throw Illegal state exception") {
                assertThrows<IllegalStateException> {
                    calendarHolidaySyncProcessor.process(calendarHolidaySyncEventNoReference)
                }
            }
        }
    }

    given("no mapping exists for reference") {
        and("getCalendarHoliday throws entity not found") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.CALENDAR_HOLIDAY,
                    JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                )
            } returns null
            coEvery { mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID) } throws EntityNotFoundException(
                ""
            )

            `when`("a calendar_holiday event is generated") {
                calendarHolidaySyncProcessor.process(calendarHolidaySyncEvent)

                then("nothing happens") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.CALENDAR_HOLIDAY,
                                JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                            )
                            mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID)
                        }
                        coVerify(exactly = 0) {
                            mockSyncClient.syncCalendarHoliday(any())
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }

        and("getCalendarHoliday returns corresponding junifer calendarHoliday") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.CALENDAR_HOLIDAY,
                    JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                )
            } returns null
            coEvery { mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID) } returns testCalendarholiday
            coEvery { mockSyncClient.syncCalendarHoliday(createCalendarHolidaySync) } returns calendarHolidaySyncResponse
            coJustRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.CALENDAR_HOLIDAY,
                    JUNIFER_CALENDAR_HOLIDAY_ID.toString(),
                    CALENDAR_HOLIDAY_ID.toString()
                )
            }

            `when`("a calendar_holiday event is generated") {
                calendarHolidaySyncProcessor.process(calendarHolidaySyncEvent)

                then("call sync client to create calendar holiday and create mapping") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.CALENDAR_HOLIDAY,
                                JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                            )
                            mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID)
                            mockSyncClient.syncCalendarHoliday(createCalendarHolidaySync)
                            mockMapper.createCoreMapping(
                                EntityIdentifier.CALENDAR_HOLIDAY,
                                JUNIFER_CALENDAR_HOLIDAY_ID.toString(),
                                CALENDAR_HOLIDAY_ID.toString()
                            )
                        }
                    }
                }
            }
        }
    }

    given("mapping exists for reference") {
        and("getCalendarHoliday throws entity not found") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.CALENDAR_HOLIDAY,
                    JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                )
            } returns CALENDAR_HOLIDAY_ID.toString()
            coEvery { mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID) } throws EntityNotFoundException(
                ""
            )
            coEvery { mockSyncClient.syncCalendarHoliday(deleteCalendarHolidaySync) } returns calendarHolidaySyncResponse

            `when`("a calendar_holiday event is generated") {
                calendarHolidaySyncProcessor.process(calendarHolidaySyncEvent)

                then("call sync client to delete calendar holiday") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.CALENDAR_HOLIDAY,
                                JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                            )
                            mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID)
                            mockSyncClient.syncCalendarHoliday(deleteCalendarHolidaySync)
                        }
                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }

        and("getCalendarHoliday returns corresponding junifer calendarHoliday") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.CALENDAR_HOLIDAY,
                    JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                )
            } returns CALENDAR_HOLIDAY_ID.toString()
            coEvery { mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID) } returns testCalendarholiday
            coEvery { mockSyncClient.syncCalendarHoliday(updateCalendarHolidaySync) } returns calendarHolidaySyncResponse

            `when`("a calendar_holiday event is generated") {
                calendarHolidaySyncProcessor.process(calendarHolidaySyncEvent)

                then("call sync client to update calendar holiday, no new mapping created") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.CALENDAR_HOLIDAY,
                                JUNIFER_CALENDAR_HOLIDAY_ID.toString()
                            )
                            mockCalendarHolidayRepository.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID)
                            mockSyncClient.syncCalendarHoliday(updateCalendarHolidaySync)
                        }
                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }
    }
})
