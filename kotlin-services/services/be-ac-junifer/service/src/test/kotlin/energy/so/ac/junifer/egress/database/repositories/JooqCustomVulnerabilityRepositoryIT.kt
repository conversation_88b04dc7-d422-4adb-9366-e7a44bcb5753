package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.CUSTOM_VULN_ID
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.juniferCustomVulnerability
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.ukVulnCustTypeDfn
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.ukVulnCustomer
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.ukVulnCustomerTypeInst
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTOMER
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTOMERTYPEINST
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTTYPEDFN
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.assertThrows

class JooqCustomVulnerabilityRepositoryIT : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferCustomVulnerabilityRepository(db)

    given("::findJuniferCustomVulnerability") {
        and("no data saved in db") {
            `when`("call findJuniferCustomVulnerability") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                    }
                }
            }
        }

        and("no custom vulnerability with given id") {
            db.executeInsert(db.newRecord(JUNIFER__UKVULNCUSTOMERTYPEINST, ukVulnCustomerTypeInst))

            `when`("call findJuniferCustomVulnerability") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.findJuniferCustomVulnerability(ID_1)
                    }
                }
            }
        }

        and("custom vulnerability with given id and right relations") {
            db.executeInsert(db.newRecord(JUNIFER__UKVULNCUSTOMER, ukVulnCustomer))
            db.executeInsert(db.newRecord(JUNIFER__UKVULNCUSTTYPEDFN, ukVulnCustTypeDfn))
            db.executeInsert(db.newRecord(JUNIFER__UKVULNCUSTOMERTYPEINST, ukVulnCustomerTypeInst))

            `when`("call findJuniferCustomVulnerability") {
                val customVulnerability = repo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                then("throw EntityNotFoundException") {
                    customVulnerability shouldBe juniferCustomVulnerability
                }
            }
        }
    }
})
