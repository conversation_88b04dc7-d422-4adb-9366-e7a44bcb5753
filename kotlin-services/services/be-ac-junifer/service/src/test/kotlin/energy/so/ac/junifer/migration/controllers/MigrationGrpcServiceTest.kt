package energy.so.ac.junifer.migration.controllers

import com.google.protobuf.Empty
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk

class MigrationGrpcServiceTest : BehaviorSpec({


    val mockJuniferMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val mockSyncEventRepository = mockk<SyncEventRepository>()
    val sut = MigrationGrpcService(mockJuniferMeterPointRepository, mockSyncEventRepository)

    afterEach {
        clearMocks(mockJuniferMeterPointRepository)
    }

    given("A list of meter points exist in Junifer__meter_point table") {
        val emptyRequest = Empty.getDefaultInstance()
        val meterPointIds = listOf(123L, 456L, 789L)
        coEvery { mockJuniferMeterPointRepository.getAllMeterPointIds() } returns meterPointIds
        coEvery { mockSyncEventRepository.batchInsertEvents(any()) } returns Unit

        `when`("call copyMeterReadings") {
            sut.copyMeterReadings(emptyRequest)

            then("should be able to insert correct number of sync events") {
                coVerify(exactly = 1) { mockSyncEventRepository.batchInsertEvents(any()) }
            }
        }
    }

})
