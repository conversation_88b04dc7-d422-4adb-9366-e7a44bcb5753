package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferRenewAccountRequest
import energy.so.ac.junifer.v1.accounts.renewAccountRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferRenewAccountRequestMapper : BehaviorSpec({

    given("renew account request") {
        val request = renewAccountRequest {
            electricityProductCode = "123"
            electricitySupplyProductSubType = "type"
            electricityStartDate = "2023-05-12"
            gasProductCode = "456"
            gasSupplyProductSubType = "type"
            gasStartDate = "2023-05-12"
            creditExitFee = false
        }

        `when`("map to junifer renew account request") {
            val juniferRequest = JuniferRenewAccountRequest.fromRenewAccountRequest(request)

            then("return a corresponding JuniferRenewAccountRequest") {
                assertSoftly {
                    juniferRequest.electricityProductCode shouldBe request.electricityProductCode
                    juniferRequest.electricitySupplyProductSubType shouldBe request.electricitySupplyProductSubType
                    juniferRequest.electricityStartDate shouldBe request.electricityStartDate
                    juniferRequest.gasProductCode shouldBe request.gasProductCode
                    juniferRequest.gasSupplyProductSubType shouldBe request.gasSupplyProductSubType
                    juniferRequest.gasStartDate shouldBe request.gasStartDate
                    juniferRequest.creditExitFee shouldBe request.creditExitFee
                }
            }
        }
    }
})
