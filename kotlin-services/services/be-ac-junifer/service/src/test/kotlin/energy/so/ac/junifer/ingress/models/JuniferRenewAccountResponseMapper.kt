package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferRenewAccount
import energy.so.ac.junifer.ingress.models.accounts.JuniferRenewAccountResponse
import energy.so.ac.junifer.ingress.models.accounts.toResponse
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferRenewAccountResponseMapper : BehaviorSpec({

    given("junifer renew account response") {
        val juniferResponse = JuniferRenewAccountResponse(
            listOf(
                JuniferRenewAccount(
                    accountNumber = "123",
                    elecProductBundleId = 1065L,
                    gasProductBundleId = 1066L
                )
            )
        )

        `when`("map to junifer renew account response") {
            val response = juniferResponse.toResponse()

            then("return a corresponding JuniferRenewAccountRequest") {
                assertSoftly {
                    response.renewalsList[0].accountNumber shouldBe juniferResponse.results!![0].accountNumber
                    response.renewalsList[0].elecProductBundleId.value shouldBe juniferResponse.results!![0].elecProductBundleId
                    response.renewalsList[0].gasProductBundleId.value shouldBe juniferResponse.results!![0].gasProductBundleId
                }
            }
        }
    }
})
