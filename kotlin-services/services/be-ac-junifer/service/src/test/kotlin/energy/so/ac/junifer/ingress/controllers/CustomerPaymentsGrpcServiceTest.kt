package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.GO_CARDLESS_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.JUNIFER_GO_CARDLESS_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.directDebitResponse
import energy.so.ac.junifer.fixtures.paymentMethodResponse
import energy.so.ac.junifer.fixtures.paymentMethodsResponse
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.customerpayments.toProtoResponse
import energy.so.ac.junifer.ingress.models.finances.toProtoResponse
import energy.so.ac.junifer.ingress.services.JuniferAccountService
import energy.so.ac.junifer.ingress.services.customerpayments.JuniferCustomerPaymentsService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.ac.junifer.v1.finances.createGoCardlessDirectDebitRequest
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.v2.dtos.idRequest
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk

class CustomerPaymentsGrpcServiceTest : BehaviorSpec({

    val mockCustomerPaymentsService = mockk<JuniferCustomerPaymentsService>()
    val mockAccountService = mockk<JuniferAccountService>()
    val mockMapper = mockk<EntityMapper>()

    val sut = CustomerPaymentsGrpcService(mockCustomerPaymentsService, mockAccountService, mockMapper)

    afterEach {
        confirmVerified(mockCustomerPaymentsService, mockAccountService, mockMapper)
    }

    given("valid create go cardless direct debit data") {
        val request = createGoCardlessDirectDebitRequest {
            accountName = "John Doe"
            accountNumber = "********"
            sortCode = "12-11-22"
            accountId = "1"
            coreDirectDebitId = 12
            corePaymentMethodId = 12
        }
        coEvery {
            mockCustomerPaymentsService.createGoCardlessDirectDebit(request)
        } returns directDebitResponse
        coEvery {
            mockAccountService.getPaymentMethods(request.accountId)
        } returns paymentMethodsResponse
        coEvery {
            mockMapper.getJuniferId(
                EntityIdentifier.BILLING_ACCOUNT,
                request.accountId
            )
        } returns request.accountId

        `when`("call createGoCardlessDirectDebit method") {
            val response = sut.createGoCardlessDirectDebit(request)
            then("go cardless direct debit created") {
                coVerify {
                    mockCustomerPaymentsService.createGoCardlessDirectDebit(request)
                }
                coVerify { mockAccountService.getPaymentMethods(request.accountId) }
                coVerify { mockMapper.getJuniferId(EntityIdentifier.BILLING_ACCOUNT, request.accountId) }
                response.paymentMethod shouldBe paymentMethodResponse.toProtoResponse(directDebitResponse.toProtoResponse())
            }
        }
    }

    given("failing create go cardless direct debit data") {
        val request = createGoCardlessDirectDebitRequest {
            accountName = "John Doe"
            accountNumber = "********"
            sortCode = "12-11-22"
            accountId = "1"
            coreDirectDebitId = 12
            corePaymentMethodId = 12
        }
        coEvery {
            mockCustomerPaymentsService.createGoCardlessDirectDebit(request)
        } throws JuniferException(
            errorCode = "AccountNotFound",
            errorSeverity = "ERROR",
            errorDescription = "Account with given id does not exist",
        )
        coEvery { mockMapper.createCoreMapping(any(), any(), any()) } returns Unit
        coEvery {
            mockMapper.getJuniferId(
                EntityIdentifier.BILLING_ACCOUNT,
                request.accountId
            )
        } returns request.accountId

        `when`("call createGoCardlessDirectDebit method") {
            then("grpc exception thrown") {
                shouldThrow<EntityNotFoundGrpcException> { sut.createGoCardlessDirectDebit(request) }
                coVerify {
                    mockCustomerPaymentsService.createGoCardlessDirectDebit(request)
                }
                coVerify { mockMapper.getJuniferId(EntityIdentifier.BILLING_ACCOUNT, request.accountId) }
            }
        }
    }

    given("go cardless direct debit id") {
        val juniferId = JUNIFER_GO_CARDLESS_DIRECT_DEBIT_ID.toString()
        val coreId = GO_CARDLESS_DIRECT_DEBIT_ID.toString()
        coEvery {
            mockMapper.getJuniferId(
                EntityIdentifier.DIRECT_DEBIT,
                coreId
            )
        } returns juniferId
        coEvery {
            mockCustomerPaymentsService.getGoCardlessDirectDebit(juniferId)
        } returns directDebitResponse

        `when`("call getGoCardlessDirectDebit method") {
            val response = sut.getGoCardlessDirectDebit(idRequest { id = coreId.toLong() })
            then("go cardless direct debit retrieved") {
                coVerify {
                    mockCustomerPaymentsService.getGoCardlessDirectDebit(juniferId)
                }
                coVerify { mockMapper.getJuniferId(EntityIdentifier.DIRECT_DEBIT, coreId) }
                response shouldBe directDebitResponse.toProtoResponse()
            }
        }
    }

    given("failing get go cardless direct debit data") {
        val juniferId = JUNIFER_GO_CARDLESS_DIRECT_DEBIT_ID.toString()
        val coreId = GO_CARDLESS_DIRECT_DEBIT_ID.toString()
        coEvery {
            mockMapper.getJuniferId(
                EntityIdentifier.DIRECT_DEBIT,
                coreId
            )
        } returns juniferId
        coEvery {
            mockCustomerPaymentsService.getGoCardlessDirectDebit(juniferId)
        } throws JuniferException(
            errorCode = "AccountNotFound",
            errorSeverity = "ERROR",
            errorDescription = "Account with given id does not exist",
        )

        `when`("call getGoCardlessDirectDebit method") {
            then("grpc exception thrown") {
                shouldThrow<EntityNotFoundGrpcException> {
                    sut.getGoCardlessDirectDebit(idRequest {
                        id = coreId.toLong()
                    })
                }
                coVerify {
                    mockCustomerPaymentsService.getGoCardlessDirectDebit(juniferId)
                }
                coVerify {
                    mockMapper.getJuniferId(
                        EntityIdentifier.DIRECT_DEBIT,
                        coreId
                    )
                }
            }
        }
    }
})
