package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

const val CREATE_ACCOUNT_CONTACT_SQL = "/sql/customer/create_account_contact.sql"

class JooqJuniferAccountRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferAccountRepository(db)

    given("an existing junifer account and account contact") {

        val contactSql = SqlLoader.loadFromStream(getSql(CREATE_ACCOUNT_CONTACT_SQL))
        val expiredContactId = 818336L
        val currentContactId = 854298L
        val accountId = 691791L

        `when`("the account is queried via the current account contact") {

            db.execute(contactSql)
            val account = repo.getAccountFromAccountContact(currentContactId)

            then("the account should be returned") {
                account shouldNotBe null
                account!!.id shouldBe accountId
            }
        }

        `when`("the account is queried via the expired account contact") {

            db.execute(contactSql)
            val account = repo.getAccountFromAccountContact(expiredContactId)

            then("null should be returned") {
                account shouldBe null
            }
        }
    }
})
