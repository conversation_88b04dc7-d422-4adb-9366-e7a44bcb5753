package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferCancelAccountRegistrationRequest
import energy.so.ac.junifer.v1.accounts.cancelAccountRegistrationRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCancelAccountRegistrationRequestTest : BehaviorSpec({

    given("cancel account registration request") {
        val request = cancelAccountRegistrationRequest {
            reason = "reason"
            communicationFl = false
        }

        `when`("map to junifer cancel account registration request") {
            val juniferRequest = JuniferCancelAccountRegistrationRequest.fromCancelAccountRegistrationRequest(
                request
            )

            then("return a corresponding JuniferCancelAccountRegistrationRequest") {
                assertSoftly {
                    juniferRequest.reason shouldBe request.reason
                    juniferRequest.communicationFl shouldBe request.communicationFl
                }
            }
        }
    }
})
