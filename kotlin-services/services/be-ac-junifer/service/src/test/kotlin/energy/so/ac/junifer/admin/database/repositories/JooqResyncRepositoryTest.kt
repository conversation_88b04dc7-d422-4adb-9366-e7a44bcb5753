package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.egress.database.repositories.getSql
import energy.so.commons.database.SqlLoader
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.jooq.DSLContext

class JooqResyncRepositoryTest : BehaviorSpec({

    val dslContext = mockk<DSLContext>()
    val sut = JooqResyncRepository(dslContext, "test")

    given("development environment active") {
        every { dslContext.execute(SqlLoader.loadFromStream(getSql(CREATE_MOCK_EVENT_TRIGGER_FUNCTION_SQL))) } returns 1
        every { dslContext.execute(SqlLoader.loadFromStream(getSql(CREATE_EVENT_TRIGGER_FUNCTION_SQL))) } returns 1

        `when`("call create mock event trigger") {
            sut.createMockEventTriggerFunction()

            then("database query executed") {
                verify { dslContext.execute(SqlLoader.loadFromStream(getSql(CREATE_MOCK_EVENT_TRIGGER_FUNCTION_SQL))) }
            }
        }

        `when`("call create event trigger") {
            sut.createEventTriggerFunction()

            then("database query executed") {
                verify { dslContext.execute(SqlLoader.loadFromStream(getSql(CREATE_EVENT_TRIGGER_FUNCTION_SQL))) }
            }
        }
    }
})
