package energy.so.ac.junifer.egress.housekeeping

import energy.so.ac.junifer.config.EventTypeToJuniferTableMap
import energy.so.ac.junifer.config.JobReloadConfig
import energy.so.ac.junifer.egress.database.repositories.EventTypeToReloadRepository
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import energy.so.commons.model.enums.EventType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class ReloaderJobTest : BehaviorSpec({

    isolationMode = IsolationMode.InstancePerTest

    val evenTypeMap = EventTypeToJuniferTableMap()
    val mockSyncRepository = mockk<SyncEventRepository>()
    val mockTypeRepository = mockk<EventTypeToReloadRepository>()
    val limit = 0
    val batchSize = 3
    val minDelayMillisBetweenReloads = 10000L
    val delayMillisBetweenBatches = 10L
    val config = JobReloadConfig(limit, batchSize, delayMillisBetweenBatches, minDelayMillisBetweenReloads)
    val eventTypeToProcess1 = EventType.CUSTOMER
    val eventTypeToProcess2 = EventType.ADDRESS
    val tableName = "junifer__Customer"
    val paginationList = listOf(Pair(1L, 3L), Pair(4L, 6L))
    val idList1 = listOf(1L, 2L, 3L)
    val idList2 = listOf(4L, 5L, 6L)

    val sut = ReloaderJob(mockSyncRepository, mockTypeRepository, config, evenTypeMap)

    given("eventTypes with valid junifer table mappings present") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen eventTypeToProcess2 andThen null
        every { mockTypeRepository.getPaginationForTable(any(), batchSize) } returns paginationList
        every { mockTypeRepository.fetchIdsForBatch(any(), any()) } returns idList1 andThen idList2
        justRun { mockSyncRepository.insertEvents(any(), any()) }

        `when`("Job runs") {
            sut.run()
            then("reloader keeps looping though reloadable eventTypes until it finds any, and then fetches and reloads records for syncing") {
                verify(exactly = 3) { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) }
                verify(exactly = 2) { mockTypeRepository.getPaginationForTable(any(), any()) }
                verify(exactly = 4) { mockTypeRepository.fetchIdsForBatch(any(), any()) }
                verify(exactly = 4) { mockSyncRepository.insertEvents(any(), any()) }
            }
        }
    }

    given("no reloadable eventTypes present in the DB") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns null

        `when`("Job runs") {
            sut.run()
            then("process quits") {
                verify(exactly = 1) { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) }
                verify(exactly = 0) { mockTypeRepository.getPaginationForTable(any(), any()) }
                verify(exactly = 0) { mockTypeRepository.fetchIdsForBatch(any(), any()) }
                verify(exactly = 0) { mockSyncRepository.insertEvents(any(), any()) }

            }
        }
    }

    given("reloadable eventType present, but no records associated with it") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen null
        every { mockTypeRepository.getPaginationForTable(tableName, batchSize) } returns emptyList()

        `when`("Job runs") {
            sut.run()
            then("iteration quits") {
                verify(exactly = 2) { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) }
                verify(exactly = 1) { mockTypeRepository.getPaginationForTable(any(), any()) }
                verify(exactly = 0) { mockTypeRepository.fetchIdsForBatch(any(), any()) }
                verify(exactly = 0) { mockSyncRepository.insertEvents(any(), any()) }
            }
        }
    }

    given("reloadable eventType present, but retrieving ids fails") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen null
        every { mockTypeRepository.getPaginationForTable(tableName, batchSize) } throws Exception("sg went wrong")

        `when`("Job runs") {
            sut.run()
            then("iteration quits, but no exception is thrown")
        }
    }

    given("reloadable eventType present, but reloading ids fails") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen null
        every { mockTypeRepository.getPaginationForTable(tableName, batchSize) } returns paginationList
        every { mockSyncRepository.insertEvents(any(), eventTypeToProcess1) } throws Exception("sg went wrong")


        `when`("Job runs") {
            sut.run()
            then("iteration quits, but no exception is thrown")
        }
    }

    given("retrieving eventTypes fails") {
        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } throws Exception("sg went wrong")

        `when`("Job runs") {
            shouldThrow<Exception> { sut.run() }
            then("exception is not caught")
        }
    }

    given("number of records to be reloaded is larger than limit") {
        val newLimit = 4
        val otherConfig = JobReloadConfig(newLimit, batchSize, delayMillisBetweenBatches, minDelayMillisBetweenReloads)
        val job = ReloaderJob(mockSyncRepository, mockTypeRepository, otherConfig, evenTypeMap)

        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen null
        every { mockTypeRepository.getPaginationForTable(tableName, batchSize) } returns paginationList
        every { mockTypeRepository.fetchIdsForBatch(any(), any()) } returns idList1
        justRun { mockSyncRepository.insertEvents(any(), eventTypeToProcess1) }

        `when`("Job runs") {
            job.run()
            then("insertEvents is only called (limit / batch_size) times") {
                verify(exactly = 2) { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) }
                verify(exactly = 1) { mockTypeRepository.getPaginationForTable(any(), any()) }
                verify(exactly = 1) { mockTypeRepository.fetchIdsForBatch(any(), any()) }
                verify(exactly = 1) { mockSyncRepository.insertEvents(any(), any()) }
            }
        }
    }

    given("batch size is larger than limit") {
        val newLimit = 4
        val newBatchSize = 5
        val otherConfig =
            JobReloadConfig(newLimit, newBatchSize, delayMillisBetweenBatches, minDelayMillisBetweenReloads)
        val job = ReloaderJob(mockSyncRepository, mockTypeRepository, otherConfig, evenTypeMap)

        every { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) } returns eventTypeToProcess1 andThen null
        every { mockTypeRepository.getPaginationForTable(tableName, newBatchSize) } returns paginationList
        every { mockTypeRepository.fetchIdsForBatch(any(), any()) } returns idList1
        justRun { mockSyncRepository.insertEvents(any(), eventTypeToProcess1) }

        `when`("Job runs") {
            job.run()
            then("no records processed") {
                verify(exactly = 2) { mockTypeRepository.getOneEventTypeToProcess(minDelayMillisBetweenReloads) }
                verify(exactly = 1) { mockTypeRepository.getPaginationForTable(any(), any()) }
                verify(exactly = 0) { mockTypeRepository.fetchIdsForBatch(any(), any()) }
                verify(exactly = 0) { mockSyncRepository.insertEvents(any(), any()) }
            }
        }
    }
})
