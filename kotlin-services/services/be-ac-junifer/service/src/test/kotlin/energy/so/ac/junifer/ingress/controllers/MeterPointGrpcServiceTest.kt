package energy.so.ac.junifer.ingress.controllers

import com.google.protobuf.NullValue
import energy.so.ac.junifer.egress.database.repositories.JooqJuniferConsentRepository
import energy.so.ac.junifer.egress.database.repositories.JooqMeterPointTprIntervalRepository
import energy.so.ac.junifer.egress.database.repositories.pojo.MeterPointTprInterval
import energy.so.ac.junifer.fixtures.CORE_AGREEMENT_ID1
import energy.so.ac.junifer.fixtures.CORE_AGREEMENT_ID2
import energy.so.ac.junifer.fixtures.CORE_AGREEMENT_ID3
import energy.so.ac.junifer.fixtures.CORE_METERPOINT_ID
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.JUNIFER_AGREEMENT_ID1
import energy.so.ac.junifer.fixtures.JUNIFER_AGREEMENT_ID2
import energy.so.ac.junifer.fixtures.JUNIFER_AGREEMENT_ID3
import energy.so.ac.junifer.fixtures.JUNIFER_METERPOINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.intervals
import energy.so.ac.junifer.fixtures.intervalsProtoList
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.meterpoint.ReadFrequency
import energy.so.ac.junifer.v1.meterpoint.meterPointTprIntervalsRequest
import energy.so.ac.junifer.v1.meterpoint.meterPointTprIntervalsResponse
import energy.so.ac.junifer.v1.meterpoint.readFrequencyConsentRequest
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.tables.pojos.Junifer_Consent
import energy.so.commons.nullableTimestamp
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

class MeterPointGrpcServiceTest : BehaviorSpec({

    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val mockConsentRepository = mockk<JooqJuniferConsentRepository>()
    val mockTprIntervalRepository = mockk<JooqMeterPointTprIntervalRepository>()

    val sut = MeterPointGrpcService(inMemoryMapper, mockConsentRepository, mockTprIntervalRepository)

    afterEach {
        confirmVerified(mockConsentRepository)
    }

    given("a request to obtain the current meterpoint consent") {

        inMemoryMapper.createCoreMapping(
            EntityIdentifier.METER_POINT,
            JUNIFER_METER_POINT_ID.toString(),
            METER_POINT_ID.toString()
        )

        `when`("a daily consent exists") {

            every { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) } returns Junifer_Consent(
                id = 1L,
                setting = "Daily"
            )

            val response = sut.getMeterPointReadFrequency(readFrequencyConsentRequest { meterPointId = METER_POINT_ID })

            then("the ID should be mapped and the response returned") {
                verify { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) }
                response.readFrequency shouldBe ReadFrequency.DAILY
            }
        }

        `when`("a half hourly consent exists") {

            every { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) } returns Junifer_Consent(
                id = 1L,
                setting = "Half Hourly"
            )

            val response = sut.getMeterPointReadFrequency(readFrequencyConsentRequest { meterPointId = METER_POINT_ID })

            then("the ID should be mapped and the response returned") {
                verify { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) }
                response.readFrequency shouldBe ReadFrequency.HALF_HOURLY
            }
        }

        `when`("a monthly consent exists") {
            every { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) } returns Junifer_Consent(
                id = 1L,
                setting = "Monthly"
            )

            val response = sut.getMeterPointReadFrequency(readFrequencyConsentRequest { meterPointId = METER_POINT_ID })

            then("the ID should be mapped and the response returned") {
                verify { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) }
                response.readFrequency shouldBe ReadFrequency.MONTHLY
            }
        }

        `when`("no consent exists") {
            every { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) } returns null

            val response = sut.getMeterPointReadFrequency(readFrequencyConsentRequest { meterPointId = METER_POINT_ID })

            then("the ID should be mapped and the response returned") {
                verify { mockConsentRepository.findReadFrequencyConsent(JUNIFER_METER_POINT_ID) }
                response.readFrequency shouldBe ReadFrequency.MONTHLY
            }
        }
    }

    given("a request to find TPR intervals for a meterpoint") {
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.METER_POINT,
            JUNIFER_METERPOINT_ID.toString(),
            CORE_METERPOINT_ID.toString()
        )
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.AGREEMENT,
            JUNIFER_AGREEMENT_ID1.toString(),
            CORE_AGREEMENT_ID1.toString()
        )
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.AGREEMENT,
            JUNIFER_AGREEMENT_ID2.toString(),
            CORE_AGREEMENT_ID2.toString()
        )
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.AGREEMENT,
            JUNIFER_AGREEMENT_ID3.toString(),
            CORE_AGREEMENT_ID3.toString()
        )

        val requestStartDateTime = LocalDate.of(2024,3,1).atStartOfDay()
        val requestEndDateTime = LocalDate.of(2025,5,1).atStartOfDay()

        `when`("a request is made") {
            and("data is available") {
                every {
                    mockTprIntervalRepository.getMeterPointTprIntervals(
                        JUNIFER_METERPOINT_ID,
                        requestStartDateTime,
                        requestEndDateTime
                    )
                } returns intervals

                then("mapped data is returned") {
                    val response = sut.getMeterPointTprIntervals(
                        meterPointTprIntervalsRequest {
                            meterPointId = CORE_METERPOINT_ID
                            startTimestamp = requestStartDateTime.toTimestamp()
                            endTimestamp = requestEndDateTime.toTimestamp()
                        }
                    )
                    response shouldBe meterPointTprIntervalsResponse {
                        intervals.addAll(intervalsProtoList)
                    }
                }
            }

            and("data for active so flex agreement is returned") {
                every {
                    mockTprIntervalRepository.getMeterPointTprIntervals(
                        JUNIFER_METERPOINT_ID,
                        requestStartDateTime,
                        requestEndDateTime
                    )
                } returns listOf(
                    MeterPointTprInterval(
                        JUNIFER_METER_POINT_ID,
                        JUNIFER_AGREEMENT_ID1,
                        requestStartDateTime,
                        LocalDate.of(9999, 1, 1).atStartOfDay(),
                        "Day",
                        1,
                        1,
                        1,
                        1,
                        1,
                        1,
                        1,
                        1,
                        1,
                        1
                    )
                )

                then("end date is mapped to null") {
                    val response = sut.getMeterPointTprIntervals(
                        meterPointTprIntervalsRequest {
                            meterPointId = CORE_METERPOINT_ID
                            startTimestamp = requestStartDateTime.toTimestamp()
                            endTimestamp = requestEndDateTime.toTimestamp()
                        }
                    )
                    response.intervalsList[0].agreementEndDate shouldBe nullableTimestamp {
                        null_ = NullValue.NULL_VALUE
                    }
                }
            }

            and("data is not available") {
                every {
                    mockTprIntervalRepository.getMeterPointTprIntervals(
                        JUNIFER_METERPOINT_ID,
                        requestStartDateTime,
                        requestEndDateTime
                    )
                } returns emptyList()

                then("empty response is returned") {
                    val response = sut.getMeterPointTprIntervals(
                        meterPointTprIntervalsRequest {
                            meterPointId = CORE_METERPOINT_ID
                            startTimestamp = requestStartDateTime.toTimestamp()
                            endTimestamp = requestEndDateTime.toTimestamp()
                        }
                    )
                    response shouldBe meterPointTprIntervalsResponse { }
                }
            }

            and("meterpoint can't be mapped") {
                inMemoryMapper.deleteMappingByJuniferId(
                    EntityIdentifier.METER_POINT,
                    JUNIFER_METERPOINT_ID.toString()
                )

                then("error is thrown") {
                    shouldThrow<IllegalStateException> {
                        sut.getMeterPointTprIntervals(
                            meterPointTprIntervalsRequest {
                                meterPointId = CORE_METERPOINT_ID
                                startTimestamp = requestStartDateTime.toTimestamp()
                                endTimestamp = requestEndDateTime.toTimestamp()
                            }
                        )
                    }
                }
            }

            and("agreement can't be mapped") {
                inMemoryMapper.deleteMappingByCoreId(
                    EntityIdentifier.AGREEMENT,
                    CORE_AGREEMENT_ID1.toString()
                )
                then("error is thrown") {
                    shouldThrow<IllegalStateException> {
                        sut.getMeterPointTprIntervals(
                            meterPointTprIntervalsRequest {
                                meterPointId = CORE_METERPOINT_ID
                                startTimestamp = requestStartDateTime.toTimestamp()
                                endTimestamp = requestEndDateTime.toTimestamp()
                            }
                        )
                    }
                }
            }
        }
    }
})
