package energy.so.ac.junifer.mapping

import energy.so.commons.model.tables.references.ENTITY_MAPPINGS
import energy.so.database.test.installDatabase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext

class DatabaseEntityMapperTest : BehaviorSpec({

    val db = installDatabase()
    val entityMapper = DatabaseEntityMapper(db)

    val coreId = "666"
    val juniferId = "6969"
    val entityIdentifier = EntityIdentifier.CUSTOMER

    given("core ID and junifer ID") {

        `when`("create mapping") {
            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            then("mappings created") {
                entityMapper.getCoreId(entityIdentifier, juniferId) shouldBe coreId
                entityMapper.getJuniferId(entityIdentifier, coreId) shouldBe juniferId
            }
        }
    }

    given("existing mapping") {
        `when`("get core id for matching junifer id") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            val returnedCoreId = entityMapper.getCoreId(entityIdentifier, juniferId)

            then("core id returned") {
                returnedCoreId shouldBe coreId
            }
        }

        `when`("get core id for non matching junifer id") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            val returnedCoreId = entityMapper.getCoreId(entityIdentifier, "")

            then("null returned") {
                returnedCoreId shouldBe null
            }
        }

        `when`("get core id for non matching core id") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            val returnedJuniferId = entityMapper.getJuniferId(entityIdentifier, "")

            then("null returned") {
                returnedJuniferId shouldBe null
            }
        }

        `when`("a duplicate mapping is created") {

            createMapping(coreId, juniferId, entityIdentifier, db)
            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            then("nothing should happen") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 1
            }
        }

        `when`("a mapping with a matching juniferId is created") {

            createMapping(coreId, juniferId, entityIdentifier, db)
            shouldThrow<IllegalStateException> { entityMapper.createCoreMapping(entityIdentifier, juniferId, "789") }

            then("an exception should be thrown") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 1
            }
        }

        `when`("a mapping with a matching coreId is created") {

            createMapping(coreId, juniferId, entityIdentifier, db)
            shouldThrow<IllegalStateException> { entityMapper.createCoreMapping(entityIdentifier, "789", coreId) }

            then("an exception should be thrown") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 1
            }
        }
    }

    given("two separate mappings for a juniferId and coreId") {
        `when`("a mapping with a matching juniferId is created") {

            createMapping(coreId, "123", entityIdentifier, db)
            createMapping("456", juniferId, entityIdentifier, db)

            shouldThrow<IllegalStateException> { entityMapper.createCoreMapping(entityIdentifier, "789", coreId) }

            then("an exception should be thrown") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 2
            }
        }

        `when`("a mapping with a matching coreId is created") {

            createMapping(coreId, "123", entityIdentifier, db)
            createMapping("456", juniferId, entityIdentifier, db)

            shouldThrow<IllegalStateException> { entityMapper.createCoreMapping(entityIdentifier, juniferId, "789") }

            then("an exception should be thrown") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 2
            }
        }

        `when`("a mapping with a matching coreId and juniferId is created") {

            createMapping(coreId, "123", entityIdentifier, db)
            createMapping("456", juniferId, entityIdentifier, db)

            shouldThrow<IllegalStateException> { entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId) }

            then("an exception should be thrown") {
                db.fetchCount(ENTITY_MAPPINGS) shouldBe 2
            }
        }
    }

    given("a mapping to delete") {
        `when`("an existing mapping is deleted by its coreId") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            entityMapper.deleteMappingByCoreId(entityIdentifier, coreId)

            then("the mapping should be deleted") {
                entityMapper.getJuniferId(entityIdentifier, coreId) shouldBe null
                entityMapper.getCoreId(entityIdentifier, juniferId) shouldBe null
            }
        }

        `when`("an existing mapping is deleted by its juniferId") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            entityMapper.deleteMappingByJuniferId(entityIdentifier, juniferId)

            then("the mapping should be deleted") {
                entityMapper.getJuniferId(entityIdentifier, coreId) shouldBe null
                entityMapper.getCoreId(entityIdentifier, juniferId) shouldBe null
            }
        }
    }
})

fun createMapping(coreId: String, juniferId: String, entityType: EntityIdentifier, dslContext: DSLContext) =
    dslContext.insertInto(
        ENTITY_MAPPINGS, ENTITY_MAPPINGS.ENTITY_TYPE, ENTITY_MAPPINGS.JUNIFER_ID, ENTITY_MAPPINGS.CORE_ID
    ).values(ENTITY_JUNIFER_PREFIX + entityType.name, juniferId, coreId).execute()
