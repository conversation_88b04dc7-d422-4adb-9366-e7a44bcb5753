package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPsrRepository
import energy.so.ac.junifer.fixtures.PsrPrecannedData.PSR_ID
import energy.so.ac.junifer.fixtures.PsrPrecannedData.UK_VULN_CUST_PSR_DFN_ID
import energy.so.ac.junifer.fixtures.PsrPrecannedData.createPsrSync
import energy.so.ac.junifer.fixtures.PsrPrecannedData.psrSyncEvent
import energy.so.ac.junifer.fixtures.PsrPrecannedData.psrSyncResponse
import energy.so.ac.junifer.fixtures.PsrPrecannedData.testJuniferPsr
import energy.so.ac.junifer.fixtures.PsrPrecannedData.updatePsrSync
import energy.so.ac.junifer.mapping.EntityIdentifier.PSR
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import energy.so.customers.sync.v2.PsrEntityRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

class PsrSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPsrRepo = mockk<JuniferPsrRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val psrProcessor = PsrSyncProcessor(mockMapper, mockPsrRepo, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPsrRepo, mockSyncClient)
    }

    given("no existing mapped psr and an existing junifer psr") {

        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) } returns null
        every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr

        `when`("a psr event is generated") {

            coEvery { mockSyncClient.syncPsrEntity(createPsrSync) } returns psrSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    PSR,
                    UK_VULN_CUST_PSR_DFN_ID.toString(),
                    PSR_ID.toString()
                )
            }

            psrProcessor.process(psrSyncEvent)

            then("a new psr should be created") {
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) }
                verify { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        PSR,
                        UK_VULN_CUST_PSR_DFN_ID.toString(),
                        PSR_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPsrEntity(createPsrSync) }
            }
        }

        `when`("expirable value is calculated") {
            val psrEntityRequestSlot = slot<PsrEntityRequest>()
            coEvery { mockSyncClient.syncPsrEntity(capture(psrEntityRequestSlot)) } returns psrSyncResponse
            justRun { mockMapper.createCoreMapping(any(), any(), any()) }

            and("description does not contain words temporary or families") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = "something generic"
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe false
                }
            }
            and("description contains word temporary") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = "some Temporary issues"
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe true
                }
            }
            and("description contains word families") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = "problems with Families"
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe true
                }
            }
            and("description contains words life changes") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = "Life changes problems temporary families"
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe false
                }
            }
            and("description contains words hospital recovery") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = "post hospital recovery problems temporary families"
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe false
                }
            }
            and("description is empty") {
                every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
                    description = ""
                )
                psrProcessor.process(psrSyncEvent)

                then("expirable value is correct") {
                    verifyMockkCalls(mockMapper, mockPsrRepo, mockSyncClient)
                    psrEntityRequestSlot.captured.psrEntity.expirable.value shouldBe false
                }
            }
        }


    }

    given("an existing mapped psr and an existing junifer psr") {

        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) } returns PSR_ID.toString()
        every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr

        `when`("a psr event is generated") {

            coEvery { mockSyncClient.syncPsrEntity(updatePsrSync) } returns psrSyncResponse


            psrProcessor.process(psrSyncEvent)

            then("existing psr should be patched") {
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) }
                verify { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) }
                coVerify { mockSyncClient.syncPsrEntity(updatePsrSync) }
            }
        }
    }

    given("an existing mapped psr and existing junifer psr to be deleted") {

        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) } returns PSR_ID.toString()
        every { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) } returns testJuniferPsr.copy(
            deletefl = "Y"
        )

        `when`("a psr event referencing to a deleted UkVulnCustPsr is generated") {

            coEvery { mockSyncClient.syncPsrEntity(any()) } returns psrSyncResponse

            psrProcessor.process(psrSyncEvent)

            then("existing psr should be deleted") {
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_DFN_ID.toString()) }
                verify { mockPsrRepo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID) }
                coVerify { mockSyncClient.syncPsrEntity(any()) }
            }
        }
    }

})

private fun verifyMockkCalls(mockMapper: EntityMapper, mockPsrRepo: JuniferPsrRepository, mockSyncClient: SyncClient) {
    verify { mockMapper.getCoreId(any(), any()) }
    verify { mockPsrRepo.getUkVulnCustPsrDfn(any()) }
    verify { mockMapper.createCoreMapping(any(), any(), any()) }
    coVerify { mockSyncClient.syncPsrEntity(any()) }
}
