package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.BillPrecannedData.BILL_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLine
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLinesResponse
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.bills.BillFile
import energy.so.ac.junifer.ingress.models.bills.GetBillResponse
import energy.so.ac.junifer.ingress.services.bills.JuniferBillsService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.v2.dtos.IdRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.nio.charset.Charset
import org.junit.jupiter.api.assertThrows

class BillsGrpcServiceTest : BehaviorSpec({

    val mockJuniferBillsService = mockk<JuniferBillsService>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val billsGrpcService = BillsGrpcService(mockJuniferBillsService, inMemoryMapper)

    afterEach {
        confirmVerified(mockJuniferBillsService)
        clearMocks(mockJuniferBillsService)
    }

    given("invalid bill id") {
        val billFiles: List<BillFile> = listOf(BillFile(888), BillFile(999))
        val response = GetBillResponse(billFiles)
        coEvery { mockJuniferBillsService.getBillFileByBillId(any()) } returns response

        `when`("id mapper throws exception") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILL, "321", "123")
            and("getBillFileIdByBillId is called") {
                then("it should throw an exception") {
                    assertThrows<FailedPreconditionGrpcException> {
                        billsGrpcService.getBillFileIdByBillId(IdRequest.newBuilder().setId(555).build())
                    }
                }
            }
        }
    }

    given("valid bill id") {
        val billFiles: List<BillFile> = listOf(BillFile(888), BillFile(999))
        val response = GetBillResponse(billFiles)
        inMemoryMapper.createCoreMapping(EntityIdentifier.BILL, "321", "123")
        coEvery { mockJuniferBillsService.getBillFileByBillId(any()) } returns response

        `when`("getBillFileIdByBillId is called") {
            val actualResponse = billsGrpcService.getBillFileIdByBillId(IdRequest.newBuilder().setId(123).build())

            then("should be able to call getBillFileByBillId on JuniferBillsService") {
                actualResponse.billFileId shouldBe billFiles.first().id
                coVerify { mockJuniferBillsService.getBillFileByBillId(321) }
            }
        }

        and("call to junifer throws exception") {
            coEvery {
                mockJuniferBillsService.getBillFileByBillId(any())
            } throws JuniferException("GET bills", "error", "error desc")
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILL, "321", "123")
            `when`("getBillFileIdByBillId is called") {
                then("it should throw exception") {
                    assertThrows<UnknownGrpcException> {
                        billsGrpcService.getBillFileIdByBillId(IdRequest.newBuilder().setId(123).build())
                    }

                    coVerify {
                        mockJuniferBillsService.getBillFileByBillId(
                            321
                        )
                    }
                }
            }
        }
    }

    given("valid bill file") {
        val response = "FileContent".toByteArray(Charset.defaultCharset())
        coEvery { mockJuniferBillsService.downloadBillFile(any()) } returns response

        `when`("downloadBillFile is called") {
            val actualResponse = billsGrpcService.downloadBillFile(IdRequest.newBuilder().setId(123).build())

            then("should be able to call downloadBillFile on JuniferBillsService") {
                actualResponse.file.toByteArray() shouldBe response
                coVerify { mockJuniferBillsService.downloadBillFile(123) }
            }
        }
    }

    given("junifer throws exception when downloading file") {
        coEvery {
            mockJuniferBillsService.downloadBillFile(any())
        } throws JuniferException("GET billFiles ", "error", "error desc")
        `when`("junifer throws exception") {
            then("downloadBillFile should throw exception") {
                assertThrows<UnknownGrpcException> {
                    billsGrpcService.downloadBillFile(IdRequest.newBuilder().setId(123).build())
                }
                coVerify { mockJuniferBillsService.downloadBillFile(123) }
            }
        }
    }

    given("::getBillLines") {
        and("invalid bill id") {
            and("id mapper throws exception") {
                `when`("getBillLines is called") {
                    then("it should throw an exception") {
                        assertThrows<FailedPreconditionGrpcException> {
                            billsGrpcService.getBillLines(IdRequest.newBuilder().setId(555).build())
                        }
                    }
                }
            }
        }

        and("valid bill id") {
            val response = listOf(billLine)
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILL, BILL_ID.toString(), BILL_ID.toString())
            coEvery { mockJuniferBillsService.getBillLines(BILL_ID) } returns response

            `when`("getBillLines is called") {
                val actualResponse = billsGrpcService.getBillLines(IdRequest.newBuilder().setId(BILL_ID).build())

                then("should be able to call getBillLines on HttpJuniferBillsService") {
                    actualResponse shouldBe billLinesResponse
                    coVerify { mockJuniferBillsService.getBillLines(BILL_ID) }
                }
            }

            and("call to junifer throws exception") {
                coEvery { mockJuniferBillsService.getBillLines(any()) } throws
                        JuniferException("GET billLines", "error", "error desc")
                inMemoryMapper.createCoreMapping(
                    EntityIdentifier.BILL, BILL_ID.toString(), BILL_ID.toString()
                )

                `when`("getBillLines is called") {
                    then("it should throw exception") {
                        assertThrows<UnknownGrpcException> {
                            billsGrpcService.getBillLines(IdRequest.newBuilder().setId(BILL_ID).build())
                        }
                        coVerify { mockJuniferBillsService.getBillLines(BILL_ID) }
                    }
                }
            }
        }
    }
})
