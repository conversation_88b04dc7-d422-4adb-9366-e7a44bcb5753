package energy.so.ac.junifer.ingress.services.bills

import energy.so.ac.junifer.fixtures.BillPrecannedData.billLine
import energy.so.ac.junifer.fixtures.juniferBillFileContent
import energy.so.ac.junifer.fixtures.juniferBillLines
import energy.so.ac.junifer.fixtures.juniferBillResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import java.nio.charset.Charset
import kotlinx.serialization.json.Json

class HttpJuniferBillsServiceTest : BehaviorSpec({

    val httpJuniferBillsService = HttpJuniferBillsService(
        MockJuniferClient.juniferConfig,
        getMockHttpClient(),
        false,
    )

    val httpJuniferBillsServiceShadow = HttpJuniferBillsService(
        MockJuniferClient.juniferConfig,
        getMockHttpClientShadow(),
        true,
    )


    given("A bill id") {
        `when`("getBillFileByBillId is called") {
            val actualResponse = httpJuniferBillsService.getBillFileByBillId(123)
            then("should be able to return the billFileId") {
                actualResponse.billFiles.first().id shouldBe 708320
            }
        }
    }

    given("A bill file") {
        `when`("downloadBillFile is called") {
            val actualResponse = httpJuniferBillsService.downloadBillFile(888)
            then("should return the file content") {
                actualResponse shouldBe juniferBillFileContent.toByteArray(Charset.defaultCharset())
            }
        }
    }

    given("A billId") {
        `when`("getBillLines is called") {
            val actualResponse = httpJuniferBillsService.getBillLines(888)
            then("should return the billLines content") {
                actualResponse shouldBe listOf(billLine)
            }
        }
    }

    given("A billId when shadow enabled") {
        `when`("getBillLines is called") {
            val actualResponse = httpJuniferBillsServiceShadow.getBillLines(888)
            then("should return the billLines content") {
                actualResponse shouldBe listOf(billLine)
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Get && request.url.fullPath.contains("billBreakdownLines")) {
            respond(
                juniferBillLines,
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("bills")) {
            respond(
                ByteReadChannel(juniferBillResponse.toByteArray(Charsets.UTF_8)),
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("billFiles")) {
            respond(
                ByteReadChannel(juniferBillFileContent.toByteArray(Charsets.UTF_8)),
                HttpStatusCode.OK
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}

private fun getMockHttpClientShadow(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Get && request.url.fullPath.contains("billBreakdownLines")) {
            respond(
                juniferBillLines,
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/octet-stream")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}