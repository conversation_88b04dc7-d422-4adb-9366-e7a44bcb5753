package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentPlanRepository
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.createPaymentPlanSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.deletePaymentPlanSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanCreateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanDeleteSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanSyncResponse
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanUpdateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlan
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.updatePaymentPlanSync
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_METHOD
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_PLAN
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentPlanSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentPlanRepository = mockk<JuniferPaymentPlanRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val paymentPlanProcessor = PaymentPlanSyncProcessor(mockMapper, mockPaymentPlanRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPaymentPlanRepository, mockSyncClient)
    }

    given("no existing mapped paymentPlan and an existing junifer paymentPlan") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN,
                JUNIFER_PAYMENT_PLAN_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns PAYMENT_METHOD_ID.toString()

        every { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) } returns testJuniferPaymentPlan

        `when`("a paymentPlan event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanEntity(createPaymentPlanSync) } returns paymentPlanSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN,
                    JUNIFER_PAYMENT_PLAN_ID.toString(),
                    PAYMENT_PLAN_ID.toString()
                )
            }

            paymentPlanProcessor.process(paymentPlanCreateSyncEvent)

            then("a new paymentPlan should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN, JUNIFER_PAYMENT_PLAN_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        PAYMENT_PLAN,
                        JUNIFER_PAYMENT_PLAN_ID.toString(),
                        PAYMENT_PLAN_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentPlanEntity(createPaymentPlanSync) }
            }
        }
    }

    given("an existing mapped paymentPlan and an existing junifer paymentPlan") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN,
                JUNIFER_PAYMENT_PLAN_ID.toString()
            )
        } returns PAYMENT_PLAN_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns PAYMENT_METHOD_ID.toString()

        every { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) } returns testJuniferPaymentPlan

        `when`("a paymentPlan event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanEntity(updatePaymentPlanSync) } returns paymentPlanSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN,
                    JUNIFER_PAYMENT_PLAN_ID.toString(),
                    PAYMENT_PLAN_ID.toString()
                )
            }

            paymentPlanProcessor.process(paymentPlanUpdateSyncEvent)

            then("a new paymentPlan should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN, JUNIFER_PAYMENT_PLAN_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) }
                coVerify { mockSyncClient.syncPaymentPlanEntity(updatePaymentPlanSync) }
            }
        }
    }

    given("an existing mapped paymentPlan and existing junifer paymentPlan to be deleted") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN,
                JUNIFER_PAYMENT_PLAN_ID.toString()
            )
        } returns PAYMENT_PLAN_ID.toString()

        every { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) } throws EntityNotFoundException("PaymentPlan not found")

        `when`("a paymentPlan event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanEntity(deletePaymentPlanSync) } returns paymentPlanSyncResponse

            paymentPlanProcessor.process(paymentPlanDeleteSyncEvent)

            then("a new paymentPlan should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN, JUNIFER_PAYMENT_PLAN_ID.toString()) }
                verify { mockPaymentPlanRepository.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID) }
                coVerify { mockSyncClient.syncPaymentPlanEntity(deletePaymentPlanSync) }
            }
        }
    }

})
