package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.juniferEstimatedUsageResponse
import energy.so.ac.junifer.ingress.models.assets.toResponse
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferEstimatedUsageResponseTest : BehaviorSpec({

    given("JuniferEstimatedUsageResponse") {
        val response = juniferEstimatedUsageResponse.copy(
            usageType = "ANNUAL_QUANTITY",
            meterPointType = "MPRN"
        )

        `when`("map to estimated usage proto") {
            val proto = response.toResponse()

            then("estimated usage is mapped") {
                assertSoftly {
                    proto.usageType shouldBe "ANNUAL_QUANTITY"
                    proto.meterPointType shouldBe "MPRN"
                }
            }
        }
    }
})
