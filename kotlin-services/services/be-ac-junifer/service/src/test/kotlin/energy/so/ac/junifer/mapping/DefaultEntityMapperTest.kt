package energy.so.ac.junifer.mapping

import energy.so.commons.redis.JedisRedisClient
import energy.so.commons.redis.RedisConnectionConfig
import energy.so.database.test.installDatabase
import energy.so.database.test.installRedis
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class DefaultEntityMapperTest : BehaviorSpec({

    val db = installDatabase()
    val databaseEntityMapper = DatabaseEntityMapper(db)

    val redis = installRedis()
    val redisClient = JedisRedisClient(
        RedisConnectionConfig(
            host = redis.host,
            port = redis.firstMappedPort
        )
    )
    val redisEntityMapper = RedisEntityMapper(redisClient)
    val defaultEntityMapper = DefaultEntityMapper(databaseEntityMapper, redisEntityMapper, true)

    val coreId = "666"
    val juniferId = "6969"
    val entityIdentifier = EntityIdentifier.CUSTOMER

    given("all needed data") {

        `when`("create core mapping") {

            defaultEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            then("data saved in database and redis") {
                databaseEntityMapper.getCoreId(entityIdentifier, juniferId) shouldBe coreId
                redisEntityMapper.getCoreId(entityIdentifier, juniferId) shouldBe coreId
            }
        }
    }

    given("existing data in redis and database") {
        defaultEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

        `when`("get core id") {
            val result = defaultEntityMapper.getCoreId(entityIdentifier, juniferId)

            then("coreId retrieved") {
                result shouldBe coreId
            }
        }

        `when`("get junifer id") {
            val result = defaultEntityMapper.getJuniferId(entityIdentifier, coreId)

            then("juniferId retrieved") {
                result shouldBe juniferId
            }
        }
    }

    given("existing data in redis") {
        redisEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

        `when`("get core id") {
            val result = defaultEntityMapper.getCoreId(entityIdentifier, juniferId)

            then("coreId retrieved") {
                result shouldBe coreId
            }
        }

        `when`("get junifer id") {
            val result = defaultEntityMapper.getJuniferId(entityIdentifier, coreId)

            then("juniferId retrieved") {
                result shouldBe juniferId
            }
        }
    }

    given("existing data in database only") {
        databaseEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

        `when`("get core id") {
            val result = defaultEntityMapper.getCoreId(entityIdentifier, juniferId)

            then("coreId retrieved") {
                result shouldBe coreId
            }
        }

        `when`("get junifer id") {
            val result = defaultEntityMapper.getJuniferId(entityIdentifier, coreId)

            then("juniferId retrieved") {
                result shouldBe juniferId
            }
        }
    }

    given("a mapping to delete") {
        `when`("an existing mapping is deleted by its coreId") {

            defaultEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            defaultEntityMapper.deleteMappingByCoreId(entityIdentifier, coreId)

            then("the mapping should be deleted") {
                defaultEntityMapper.getJuniferId(entityIdentifier, coreId) shouldBe null
                defaultEntityMapper.getCoreId(entityIdentifier, juniferId) shouldBe null
            }
        }

        `when`("an existing mapping is deleted by its juniferId") {

            defaultEntityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            defaultEntityMapper.deleteMappingByJuniferId(entityIdentifier, juniferId)

            then("the mapping should be deleted") {
                defaultEntityMapper.getJuniferId(entityIdentifier, coreId) shouldBe null
                defaultEntityMapper.getCoreId(entityIdentifier, juniferId) shouldBe null
            }
        }
    }
})
