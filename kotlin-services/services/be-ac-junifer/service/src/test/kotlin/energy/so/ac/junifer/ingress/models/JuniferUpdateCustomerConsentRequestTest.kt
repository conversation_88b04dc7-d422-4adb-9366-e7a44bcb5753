package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData
import energy.so.ac.junifer.ingress.models.customers.JuniferUpdateCustomerConsentRequest
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDate
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferUpdateCustomerConsentRequestTest : BehaviorSpec({

    given("updateCustomerConsentRequest") {
        val request = CustomerConsentPrecannedData.updateCustomerConsentRequest

        `when`("call fromUpdateCustomerConsentRequest method") {
            val result = JuniferUpdateCustomerConsentRequest.fromUpdateCustomerConsentRequest(request)

            then("result match") {
                result.consentDefinition shouldBe request.consentDefinition
                result.setting shouldBe request.setting
                result.fromDt shouldBe request.fromDt.getValueOrNull()?.toLocalDate()
                result.toDt shouldBe request.toDt.getValueOrNull()?.toLocalDate()
            }
        }
    }
})
