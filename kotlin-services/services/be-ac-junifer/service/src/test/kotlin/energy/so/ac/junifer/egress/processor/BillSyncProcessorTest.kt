package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferBillRepository
import energy.so.ac.junifer.fixtures.BillPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.BILLING_ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.BILLING_PERIOD_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.BILL_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILLING_PERIOD_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILL_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.billSyncEvent
import energy.so.ac.junifer.fixtures.BillPrecannedData.billSyncResponse
import energy.so.ac.junifer.fixtures.BillPrecannedData.createBillSyncConsolidated
import energy.so.ac.junifer.fixtures.BillPrecannedData.deleteBillSync
import energy.so.ac.junifer.fixtures.BillPrecannedData.testJuniferBill
import energy.so.ac.junifer.fixtures.BillPrecannedData.testJuniferBillDirty
import energy.so.ac.junifer.fixtures.BillPrecannedData.updateBillSync
import energy.so.ac.junifer.mapping.EntityIdentifier.BILL
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT_TRANSACTION
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_PERIOD
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.billings.v2.sync.BillEntityRequest
import energy.so.commons.NullableBoolean
import energy.so.commons.NullableInt64
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

class BillSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockBillRepository = mockk<JuniferBillRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val billProcessor = BillSyncProcessor(mockMapper, mockBillRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockBillRepository, mockSyncClient)
    }

    given("no existing mapped bill and an existing junifer bill") {

        every { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) } returns null

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns BILLING_PERIOD_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns BILLING_ACCOUNT_TRANSACTION_ID.toString()

        every { mockBillRepository.getBill(JUNIFER_BILL_ID) } returns testJuniferBill.copy(
            accounttransactionfk = null,
            billperiodfk = null
        )
        every { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) } returns 1
        every { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) } returns testJuniferBillDirty

        val billEntityRequest = slot<BillEntityRequest>()

        `when`("a bill event is generated") {

            coEvery { mockSyncClient.syncBillEntity(any()) } returns billSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILL, JUNIFER_BILL_ID.toString(), BILL_ID.toString()
                )
            }

            billProcessor.process(billSyncEvent)

            then("a new bill should be created") {
                verify { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockBillRepository.getBill(JUNIFER_BILL_ID) }
                verify { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        BILL, JUNIFER_BILL_ID.toString(), BILL_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillEntity(capture(billEntityRequest)) }

                billEntityRequest.captured.run {
                    billEntity.billingAccountTransactionId shouldBe NullableInt64.getDefaultInstance()
                    billEntity.billingPeriodId shouldBe NullableInt64.getDefaultInstance()
                    billEntity.consolidatedBillFl shouldBe NullableBoolean.getDefaultInstance()
                }
            }
        }
    }

    given("no existing mapped bill and an existing consolidated junifer bill") {

        every { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) } returns null

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns BILLING_PERIOD_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns BILLING_ACCOUNT_TRANSACTION_ID.toString()

        every { mockBillRepository.getBill(JUNIFER_BILL_ID) } returns testJuniferBill
        every { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) } returns 2
        every { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) } returns testJuniferBillDirty

        `when`("a bill event is generated") {

            coEvery { mockSyncClient.syncBillEntity(createBillSyncConsolidated) } returns billSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILL, JUNIFER_BILL_ID.toString(), BILL_ID.toString()
                )
            }

            billProcessor.process(billSyncEvent)

            then("a new bill should be created") {
                verify { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_PERIOD, JUNIFER_BILLING_PERIOD_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        BILLING_ACCOUNT_TRANSACTION,
                        JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID.toString()
                    )
                }
                verify { mockBillRepository.getBill(JUNIFER_BILL_ID) }
                verify { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) }
                verify { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        BILL, JUNIFER_BILL_ID.toString(), BILL_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillEntity(createBillSyncConsolidated) }
            }
        }
    }

    given("an existing mapped bill and an existing junifer bill") {

        every {
            mockMapper.getCoreId(
                BILL, JUNIFER_BILL_ID.toString()
            )
        } returns BILL_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns BILLING_PERIOD_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns BILLING_ACCOUNT_TRANSACTION_ID.toString()

        every { mockBillRepository.getBill(JUNIFER_BILL_ID) } returns testJuniferBill
        every { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) } returns 1
        every { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) } returns testJuniferBillDirty

        `when`("a bill event is generated") {

            coEvery { mockSyncClient.syncBillEntity(updateBillSync) } returns billSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILL, JUNIFER_BILL_ID.toString(), BILL_ID.toString()
                )
            }

            billProcessor.process(billSyncEvent)

            then("bill should be patched") {
                verify { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_PERIOD, JUNIFER_BILLING_PERIOD_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        BILLING_ACCOUNT_TRANSACTION,
                        JUNIFER_BILLING_ACCOUNT_TRANSACTION_ID.toString()
                    )
                }
                verify { mockBillRepository.getBill(JUNIFER_BILL_ID) }
                verify { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) }
                verify { mockBillRepository.getBillDirty(JUNIFER_BILL_ID) }
                coVerify { mockSyncClient.syncBillEntity(updateBillSync) }
            }
        }
    }

    given("an existing mapped bill and existing junifer bill to be deleted") {

        every {
            mockMapper.getCoreId(
                BILL, JUNIFER_BILL_ID.toString()
            )
        } returns BILL_ID.toString()
        every { mockBillRepository.getBill(JUNIFER_BILL_ID) } throws EntityNotFoundException("Bill not found")
        every { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) } returns 1

        `when`("a bill event is generated") {

            coEvery { mockSyncClient.syncBillEntity(deleteBillSync) } returns billSyncResponse

            billProcessor.process(billSyncEvent)

            then("bill should be deleted") {
                verify { mockMapper.getCoreId(BILL, JUNIFER_BILL_ID.toString()) }
                verify { mockBillRepository.getBill(JUNIFER_BILL_ID) }
                verify { mockBillRepository.getConsolidatedBillingPeriodCount(JUNIFER_BILLING_PERIOD_ID) }
                coVerify { mockSyncClient.syncBillEntity(deleteBillSync) }
            }
        }
    }
})
