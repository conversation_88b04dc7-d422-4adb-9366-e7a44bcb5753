package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.admin.database.repositories.AccountRepository
import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferAccountRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.accountContactSyncEvent
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.accountSyncEvent
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.accountSyncResponse
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.createAccountSync
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.deleteAccountSync
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.patchAccountSync
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.testAccountSummary
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.testJuniferAccount
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class BillingAccountSyncProcessorTest : BehaviorSpec({

    isolationMode = IsolationMode.InstancePerTest

    val mockMapper = mockk<EntityMapper>()
    val mockAccountRepo = mockk<JuniferAccountRepository>()
    val mockSyncClient = mockk<SyncClient>()
    val mockAccountRepository = mockk<AccountRepository>()

    val processor = BillingAccountSyncProcessor(
        mockMapper,
        mockAccountRepo,
        mockSyncClient,
        false,
        "production",
        mockAccountRepository
    )
    val billingAccountProcessorOptimizedSyncEnabled =
        BillingAccountSyncProcessor(mockMapper, mockAccountRepo, mockSyncClient, true, "any", mockAccountRepository)


    given("a mapped customer and no mapped billing account or setting") {

        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns CUSTOMER_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns null
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) } returns testAccountSummary

        `when`("an account event is generated") {

            coEvery { mockSyncClient.syncBillingAccountEntity(createAccountSync) } returns accountSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.BILLING_ACCOUNT,
                    JUNIFER_ACCOUNT_ID.toString(),
                    BILLING_ACCOUNT_ID.toString()
                )
            }

            processor.process(accountSyncEvent)

            then("a billing account should be created and a billing account setting should be created") {
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.BILLING_ACCOUNT,
                        JUNIFER_ACCOUNT_ID.toString(),
                        BILLING_ACCOUNT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillingAccountEntity(createAccountSync) }
            }
        }

        `when`("an account contact event is generated") {

            shouldThrow<SyncDelayedException> { processor.process(accountContactSyncEvent) }

            then("a sync delayed exception should be thrown") {
                verify { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
            }
        }
    }

    given("a mapped customer and a mapped billing account") {

        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns CUSTOMER_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) } returns testAccountSummary
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                BILLING_ACCOUNT_ID.toString()
            )
        }

        `when`("an account event is generated") {
            coEvery { mockSyncClient.syncBillingAccountEntity(patchAccountSync) } returns accountSyncResponse

            processor.process(accountSyncEvent)

            then("the billing account should be patched and the billing account setting should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
                coVerify { mockSyncClient.syncBillingAccountEntity(patchAccountSync) }
            }
        }

        `when`("an account contact event is generated") {
            coEvery { mockSyncClient.syncBillingAccountEntity(patchAccountSync) } returns accountSyncResponse

            processor.process(accountContactSyncEvent)

            then("the billing account setting should be patched") {

                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
            }
        }
    }

    given("a mapped customer and a mapped billing account, and a deleted junifer account") {

        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns CUSTOMER_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } throws EntityNotFoundException("Account not found")
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns null

        `when`("an account event is generated") {

            coEvery { mockSyncClient.syncBillingAccountEntity(deleteAccountSync) } returns accountSyncResponse

            processor.process(accountSyncEvent)

            then("the billing account should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) }
                coVerify { mockSyncClient.syncBillingAccountEntity(deleteAccountSync) }
            }
        }

        `when`("an account contact event is generated") {

            processor.process(accountContactSyncEvent)

            then("nothing should update") {
                verify { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) }
            }
        }
    }

    given("a mapped customer and a mapped billing account, and a flagged deleted junifer account") {

        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns CUSTOMER_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) } returns testAccountSummary
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns testJuniferAccount.copy(
            deletefl = BOOLEAN_TRUE,
            cancelfl = BOOLEAN_TRUE
        )
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                BILLING_ACCOUNT_ID.toString()
            )
        }

        `when`("an account event is generated") {

            coEvery { mockSyncClient.syncBillingAccountEntity(any()) } returns accountSyncResponse
            processor.process(accountSyncEvent)

            then("the billing account should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
                coVerify { mockSyncClient.syncBillingAccountEntity(any()) }
            }
        }

        // Currently, if an account contact is updated for an account flagged as deleted, we will still update the settings.
        `when`("an account contact event is generated") {
            coEvery { mockSyncClient.syncBillingAccountEntity(any()) } returns accountSyncResponse
            processor.process(accountContactSyncEvent)

            then("the billing account setting should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
            }
        }
    }

    given("No customer in core") {

        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } returns testJuniferAccount

        `when`("an account event is generated") {

            shouldThrow<SyncDelayedException> { processor.process(accountSyncEvent) }

            then("the event should be delayed") {
                verify { mockMapper.getCoreId(any(), any()) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Billing Account not in CORE") {
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns null
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns testJuniferAccount

        `when`("an account contact event is generated") {

            shouldThrow<SyncDelayedException> { processor.process(accountContactSyncEvent) }

            then("Event should be delayed") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("a mapped customer and a mapped billing account on nonprod env") {
        every {
            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
        } returns CUSTOMER_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) } returns testJuniferAccount
        every { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) } returns testAccountSummary
        every { mockAccountRepository.updateAccountId(JUNIFER_ACCOUNT_ID, BILLING_ACCOUNT_ID) } returns mockk()
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                BILLING_ACCOUNT_ID.toString()
            )
        }

        `when`("an account event is generated") {
            coEvery { mockSyncClient.syncBillingAccountEntity(patchAccountSync) } returns accountSyncResponse
            billingAccountProcessorOptimizedSyncEnabled.process(accountSyncEvent)

            then("the billing account should be patched and the billing account setting should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getById(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepository.updateAccountId(JUNIFER_ACCOUNT_ID, BILLING_ACCOUNT_ID) }
                coVerify { mockSyncClient.syncBillingAccountEntity(patchAccountSync) }
            }
        }

        `when`("an account contact event is generated") {
            coEvery { mockSyncClient.syncBillingAccountEntity(patchAccountSync) } returns accountSyncResponse
            billingAccountProcessorOptimizedSyncEnabled.process(accountContactSyncEvent)

            then("the billing account setting should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockAccountRepo.getAccountFromAccountContact(JUNIFER_ACCOUNT_CONTACT_ID) }
                verify { mockAccountRepo.getBillingAccountInfo(JUNIFER_ACCOUNT_ID) }
            }
        }
    }
})
