package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentPlanPaymentRepository
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.createPaymentPlanPaymentSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.deletePaymentPlanPaymentSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentCreateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentDeleteSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentSyncResponse
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentUpdateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlanPayment
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.updatePaymentPlanPaymentSync
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_PLAN
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_PLAN_PAYMENT
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentPlanPaymentSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentPlanPaymentRepository = mockk<JuniferPaymentPlanPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val paymentPlanPaymentProcessor =
        PaymentPlanPaymentSyncProcessor(mockMapper, mockPaymentPlanPaymentRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPaymentPlanPaymentRepository, mockSyncClient)
    }

    given("no existing mapped paymentPlanPayment and an existing junifer paymentPlanPayment") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT,
                JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN,
                JUNIFER_PAYMENT_PLAN_ID.toString()
            )
        } returns PAYMENT_PLAN_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) } returns testJuniferPaymentPlanPayment

        `when`("a paymentPlanPayment event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentEntity(createPaymentPlanPaymentSync) } returns paymentPlanPaymentSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN_PAYMENT,
                    JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString(),
                    PAYMENT_PLAN_PAYMENT_ID.toString()
                )
            }

            paymentPlanPaymentProcessor.process(paymentPlanPaymentCreateSyncEvent)

            then("a new paymentPlanPayment should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN_PAYMENT, JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_PLAN, JUNIFER_PAYMENT_PLAN_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        PAYMENT_PLAN_PAYMENT,
                        JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString(),
                        PAYMENT_PLAN_PAYMENT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentPlanPaymentEntity(createPaymentPlanPaymentSync) }
            }
        }
    }

    given("an existing mapped paymentPlanPayment and an existing junifer paymentPlanPayment") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT,
                JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN,
                JUNIFER_PAYMENT_PLAN_ID.toString()
            )
        } returns PAYMENT_PLAN_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) } returns testJuniferPaymentPlanPayment

        `when`("a paymentPlanPayment event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentEntity(updatePaymentPlanPaymentSync) } returns paymentPlanPaymentSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN_PAYMENT,
                    JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString(),
                    PAYMENT_PLAN_PAYMENT_ID.toString()
                )
            }

            paymentPlanPaymentProcessor.process(paymentPlanPaymentUpdateSyncEvent)

            then("a new paymentPlanPayment should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN_PAYMENT, JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_PLAN, JUNIFER_PAYMENT_PLAN_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) }
                coVerify { mockSyncClient.syncPaymentPlanPaymentEntity(updatePaymentPlanPaymentSync) }
            }
        }
    }

    given("an existing mapped paymentPlanPayment and existing junifer paymentPlanPayment to be deleted") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT,
                JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_ID.toString()

        every { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) } throws EntityNotFoundException(
            "PaymentPlanPayment not found"
        )

        `when`("a paymentPlanPayment event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentEntity(deletePaymentPlanPaymentSync) } returns paymentPlanPaymentSyncResponse

            paymentPlanPaymentProcessor.process(paymentPlanPaymentDeleteSyncEvent)

            then("a new paymentPlanPayment should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN_PAYMENT, JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()) }
                verify { mockPaymentPlanPaymentRepository.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID) }
                coVerify { mockSyncClient.syncPaymentPlanPaymentEntity(deletePaymentPlanPaymentSync) }
            }
        }
    }


})
