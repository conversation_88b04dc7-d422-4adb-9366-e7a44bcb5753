package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.exceptions.SyncFixedDateDelayException
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_MPAN_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.JUNIFER_MPRN_CONFIG_PERIOD_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.JUNIFER_MPRN_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.MAM_MARKET_PARTICIPANT_FK
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.METER_POINT_GAS_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.createMeterPointGasSync
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.juniferMamMarketParticipant
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.meterPointGasSyncResponse
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.mprnConfigPeriodSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.mprnSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.patchMeterPointGasSync
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.patchMeterPointGasSyncForMprnConfigPeriod
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.testJuniferMprn
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.testJuniferMprnConfigPeriod
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT_GAS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.enums.EventType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

class MeterPointGasElectricitySyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val meterPointGasSyncProcessor =
        MeterPointGasSyncProcessor(mockMapper, mockMeterPointRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockMeterPointRepository, mockSyncClient)
    }

    given("Invalid sync event type") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointGasSyncProcessor.process(
                        mprnSyncEvent.copy(eventType = EventType.ACCOUNT_CONTACT)
                    )
                }
            }
        }
    }

    given("Missing reference") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointGasSyncProcessor.process(mprnSyncEvent.copy(reference = null))
                }
            }
        }
    }

    given("no existing mapped meter point gas and an existing junifer meter point gas") {

        every { mockMapper.getCoreId(METER_POINT_GAS, JUNIFER_MPRN_ID.toString()) } returns null
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) } returns MeterPointGasPrecannedData.testJuniferMeter
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) } returns juniferMamMarketParticipant

        `when`("an Mprn event is generated") {

            coEvery { mockSyncClient.syncMeterPointGasEntity(createMeterPointGasSync) } returns meterPointGasSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_GAS,
                    JUNIFER_MPRN_ID.toString(),
                    METER_POINT_GAS_ID.toString()
                )
            }

            meterPointGasSyncProcessor.process(mprnSyncEvent)

            then("a new meter point gas should be created") {
                verify { mockMapper.getCoreId(METER_POINT_GAS, JUNIFER_MPRN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_GAS,
                        JUNIFER_MPRN_ID.toString(),
                        METER_POINT_GAS_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncMeterPointGasEntity(createMeterPointGasSync) }
            }
        }


    }

    given("an existing mapped meter point gas and an existing junifer meter point gas") {

        every {
            mockMapper.getCoreId(
                METER_POINT_GAS,
                JUNIFER_MPRN_ID.toString()
            )
        } returns METER_POINT_GAS_ID.toString()
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) } returns MeterPointGasPrecannedData.testJuniferMeter
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) } returns juniferMamMarketParticipant

        `when`("an Mprn event is generated") {

            coEvery { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSync) } returns meterPointGasSyncResponse

            meterPointGasSyncProcessor.process(mprnSyncEvent)

            then("a new meter point gas should be patched") {
                verify { mockMapper.getCoreId(METER_POINT_GAS, JUNIFER_MPRN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) }
                coVerify { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSync) }
            }
        }

        `when`("an MprnConfigPeriod event is generated") {
            every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod
            every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns testJuniferMprnConfigPeriod
            every { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) } returns juniferMamMarketParticipant
            every { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) } returns MeterPointGasPrecannedData.testJuniferMeter

            coEvery { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSyncForMprnConfigPeriod) } returns meterPointGasSyncResponse


            meterPointGasSyncProcessor.process(mprnConfigPeriodSyncEvent)

            then("a new meter point gas should be created") {
                verify { mockMapper.getCoreId(METER_POINT_GAS, JUNIFER_MPRN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }
                verify { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(JUNIFER_METER_POINT_ID) }

                coVerify { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSyncForMprnConfigPeriod) }
            }
        }

        `when`("an MprnConfigPeriod event is generated which is not the current one") {
            and("MprnConfigPeriod is in the past") {
                every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod
                every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns testJuniferMprnConfigPeriod.copy(
                    id = 2342343
                )

                every { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) } returns juniferMamMarketParticipant

                coEvery { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSyncForMprnConfigPeriod) } returns meterPointGasSyncResponse


                meterPointGasSyncProcessor.process(mprnConfigPeriodSyncEvent)

                then("a new meter point gas should be created") {
                    verify { mockMapper.getCoreId(METER_POINT_GAS, JUNIFER_MPRN_ID.toString()) }
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }
                    verify { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) }

                    coVerify { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSyncForMprnConfigPeriod) }
                }
            }
            and("MprnConfigPeriod is in the future") {
                every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod.copy(
                    fromdt = LocalDate.now().plusDays(14)
                )
                every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns testJuniferMprnConfigPeriod.copy(
                    id = 2342343
                )

                every { mockMeterPointRepository.getMarketParticipant(MAM_MARKET_PARTICIPANT_FK) } returns juniferMamMarketParticipant

                coEvery { mockSyncClient.syncMeterPointGasEntity(patchMeterPointGasSyncForMprnConfigPeriod) } returns meterPointGasSyncResponse


                then("should throw SyncFixedDateDelayException") {
                    shouldThrow<SyncFixedDateDelayException> {
                        meterPointGasSyncProcessor.process(
                            mprnConfigPeriodSyncEvent
                        )
                    }

                    verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }
                }
            }
        }
    }

    given("an MprnConfigPeriod event for an non existent MprnConfigPeriod in db") {
        every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } throws EntityNotFoundException(
            ""
        )

        `when`("MprnConfigPeriod event is generated") {
            meterPointGasSyncProcessor.process(mprnConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MprnConfigPeriod event for an non current MprnConfigPeriod in db") {
        every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns null

        `when`("MprnConfigPeriod event is generated") {
            meterPointGasSyncProcessor.process(mprnConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }
})
