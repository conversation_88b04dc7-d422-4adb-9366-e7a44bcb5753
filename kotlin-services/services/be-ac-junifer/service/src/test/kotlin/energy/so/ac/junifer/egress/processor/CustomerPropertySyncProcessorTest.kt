package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferCustomerRepository
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.CUSTOMER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.JUNIFER_CUSTOMER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.JUNIFER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.createCustomerPropertyRelSync
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.customerPropertySyncEvent
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.customerPropertySyncResponse
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.testJuniferCustomerProperty
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.updateCustomerPropertyRelSync
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER_PROPERTY
import energy.so.ac.junifer.mapping.EntityIdentifier.PROPERTY
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class CustomerPropertySyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val juniferCustomerRepository = mockk<JuniferCustomerRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val customerPropertyProcessor = CustomerPropertySyncProcessor(mockMapper, juniferCustomerRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, juniferCustomerRepository, mockSyncClient)
    }

    given("no existing mapped CustomerPropertyRel and an existing junifer UkVulnCustPsr") {

        every { mockMapper.getCoreId(CUSTOMER_PROPERTY, JUNIFER_CUSTOMER_PROPERTY_ID.toString()) } returns null
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID.toString()

        every { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) } returns testJuniferCustomerProperty

        `when`("a CustomerProperty event is generated") {

            coEvery { mockSyncClient.syncCustomerPropertyRelEntity(createCustomerPropertyRelSync) } returns customerPropertySyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_PROPERTY,
                    JUNIFER_CUSTOMER_PROPERTY_ID.toInt().toString(),
                    CUSTOMER_PROPERTY_ID.toString()
                )
            }

            customerPropertyProcessor.process(customerPropertySyncEvent)

            then("a new CustomerProperty should be created") {
                verify { mockMapper.getCoreId(CUSTOMER_PROPERTY, JUNIFER_CUSTOMER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }


                verify {
                    mockMapper.createCoreMapping(
                        CUSTOMER_PROPERTY,
                        JUNIFER_CUSTOMER_PROPERTY_ID.toInt().toString(),
                        CUSTOMER_PROPERTY_ID.toString()
                    )
                }

                verify { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) }
                coVerify { mockSyncClient.syncCustomerPropertyRelEntity(createCustomerPropertyRelSync) }
            }
        }
    }

    given("existing mapped CustomerPropertyRel and an existing junifer UkVulnCustPsr") {

        every {
            mockMapper.getCoreId(
                CUSTOMER_PROPERTY,
                JUNIFER_CUSTOMER_PROPERTY_ID.toString()
            )
        } returns CUSTOMER_PROPERTY_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID.toString()

        every { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) } returns testJuniferCustomerProperty


        `when`("a CustomerProperty event is generated") {

            coEvery { mockSyncClient.syncCustomerPropertyRelEntity(updateCustomerPropertyRelSync) } returns customerPropertySyncResponse

            customerPropertyProcessor.process(customerPropertySyncEvent)

            then("existing CustomerProperty should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_PROPERTY, JUNIFER_CUSTOMER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }

                verify { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) }

                coVerify { mockSyncClient.syncCustomerPropertyRelEntity(updateCustomerPropertyRelSync) }
            }
        }
    }

    given("existing mapped CustomerPropertyRel and an existing junifer UkVulnCustPsr to be deleted") {

        every {
            mockMapper.getCoreId(
                CUSTOMER_PROPERTY,
                JUNIFER_CUSTOMER_PROPERTY_ID.toString()
            )
        } returns CUSTOMER_PROPERTY_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID.toString()
        every { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) } returns testJuniferCustomerProperty.copy(
            deletefl = "Y"
        )

        `when`("a CustomerProperty event is generated") {

            coEvery { mockSyncClient.syncCustomerPropertyRelEntity(any()) } returns customerPropertySyncResponse

            customerPropertyProcessor.process(customerPropertySyncEvent)

            then("existing customerProperty should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_PROPERTY, JUNIFER_CUSTOMER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }

                verify { juniferCustomerRepository.getCustomerProperty(JUNIFER_CUSTOMER_PROPERTY_ID) }

                coVerify { mockSyncClient.syncCustomerPropertyRelEntity(any()) }
            }
        }
    }

})
