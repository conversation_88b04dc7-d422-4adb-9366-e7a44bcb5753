package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlan
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlanPaymentTransaction
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTPLANPAYMENTTXN
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferPaymentPlanPaymentTransactionRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferPaymentPlanPaymentTransactionRepository(db)

    given("PaymentPlan exists in DB") {
        insertPaymentPlan(db)
        insertPaymentPlanPayment(db)
        insertPaymentPlanPaymentTransaction(db)

        `when`("PaymentPlan is queried") {
            val paymentPlanPaymentTransaction = repo.getPaymentPlanPaymentTransaction(
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
            )

            then("PaymentPlan is returned") {
                with(paymentPlanPaymentTransaction) {
                    this shouldNotBe null
                    id shouldBe testJuniferPaymentPlanPaymentTransaction.id
                    paymentplanpaymentfk shouldBe testJuniferPaymentPlanPaymentTransaction.paymentplanpaymentfk
                    transactionallocationfk shouldBe testJuniferPaymentPlanPaymentTransaction.transactionallocationfk
                    amount?.toDouble() shouldBe testJuniferPaymentPlanPaymentTransaction.amount?.toDouble()
                    reference shouldBe testJuniferPaymentPlan.reference
                    deletefl shouldBe testJuniferPaymentPlan.deletefl
                    versionno shouldBe testJuniferPaymentPlan.versionno
                    partitionid shouldBe testJuniferPaymentPlan.partitionid
                }
            }
        }
    }

})

fun insertPaymentPlanPaymentTransaction(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__PAYMENTPLANPAYMENTTXN, testJuniferPaymentPlanPaymentTransaction))
}


