package energy.so.ac.junifer.mapping

import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData
import energy.so.commons.redis.JedisRedisClient
import energy.so.commons.redis.RedisConnectionConfig
import energy.so.database.test.installRedis
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class RedisJuniferCacheTest : BehaviorSpec({

    val redis = installRedis()
    val redisClient = JedisRedisClient(
        RedisConnectionConfig(
            host = redis.host,
            port = redis.firstMappedPort
        )
    )

    val endpointIdentifier = EndpointIdentifier.GET_ESTIMATED_USAGE
    val encodedRequestParams = EstimatedUsagePrecannedData.encodedEstimatedUsageRequestParams
    val encodedJuniferResponse = EstimatedUsagePrecannedData.cachedJuniferEstimatedUsageResponse
    val expirationTime = 1800L

    val juniferCache = RedisJuniferCache(redisClient, true, true)

    given("encoded request params and junifer response") {

        `when`("cache response") {
            juniferCache.cacheResponse(endpointIdentifier, encodedRequestParams, encodedJuniferResponse, expirationTime)

            then("response is saved to cache") {
                juniferCache.getCachedResponse(endpointIdentifier, encodedRequestParams) shouldBe encodedJuniferResponse
            }
        }
    }
})
