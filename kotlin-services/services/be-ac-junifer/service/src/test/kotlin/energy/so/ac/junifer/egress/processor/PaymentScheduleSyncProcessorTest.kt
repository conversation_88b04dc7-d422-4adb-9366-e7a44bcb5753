package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentScheduleRepository
import energy.so.ac.junifer.egress.database.repositories.pojo.JuniferFrequency
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.FREQUENCY
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.JUNIFER_FREQUENCY_INST_ID
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.JUNIFER_PAYMENT_SCHEDULE_ID
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.JUNIFER_SEASONAL_DFN_ID
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.PAYMENT_SCHEDULE_ID
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.createPaymentScheduleSync
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.deletePaymentScheduleSync
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.patchPaymentScheduleSync
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.paymentScheduleSyncEvent
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.paymentScheduleSyncResponse
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.testFrequencyInst
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.testPaySchedulePeriod
import energy.so.ac.junifer.fixtures.PaymentSchedulePrecannedData.testSeasonalDfn
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.PAYMENT_ID
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.JUNIFER_SCHEDULED_PAYMENT_ID
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.SCHEDULED_PAYMENT_ID
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.createScheduledPaymentSync
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.createScheduledPaymentSyncNullPaymentId
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.deleteScheduledPaymentSync
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.patchScheduledPaymentSync
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.scheduledPaymentSyncEvent
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.scheduledPaymentSyncResponse
import energy.so.ac.junifer.fixtures.ScheduledPaymentPrecannedData.testScheduleItem
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentScheduleSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockRepo = mockk<JuniferPaymentScheduleRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = PaymentScheduleSyncProcessor(mockMapper, mockRepo, mockSyncClient, 2, 2)

    afterEach {
        confirmVerified(mockMapper, mockRepo, mockSyncClient)
    }

    given("no existing mapped payment schedule, a mapped payment method and an existing junifer schedule") {

        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns null
        every { mockRepo.getPaymentScheduleFrequency(JUNIFER_FREQUENCY_INST_ID) } returns JuniferFrequency(
            FREQUENCY,
            testFrequencyInst
        )
        every { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) } returns testPaySchedulePeriod
        every { mockRepo.getPaymentScheduleSeasonalDfnById(JUNIFER_SEASONAL_DFN_ID) } returns testSeasonalDfn

        `when`("a payment schedule event is generated") {

            coEvery { mockSyncClient.syncPaymentScheduleEntity(createPaymentScheduleSync) } returns paymentScheduleSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PAYMENT_SCHEDULE,
                    JUNIFER_PAYMENT_SCHEDULE_ID.toString(),
                    PAYMENT_SCHEDULE_ID.toString()
                )
            }

            processor.process(paymentScheduleSyncEvent)

            then("a new payment schedule should be created") {

                verify { mockRepo.getPaymentScheduleFrequency(JUNIFER_FREQUENCY_INST_ID) }
                verify { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) }
                verify { mockRepo.getPaymentScheduleSeasonalDfnById(JUNIFER_SEASONAL_DFN_ID) }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PAYMENT_SCHEDULE,
                        JUNIFER_PAYMENT_SCHEDULE_ID.toString(),
                        PAYMENT_SCHEDULE_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentScheduleEntity(createPaymentScheduleSync) }
            }
        }
    }

    given("an existing mapped payment schedule and payment method and an existing junifer schedule") {

        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every { mockRepo.getPaymentScheduleFrequency(JUNIFER_FREQUENCY_INST_ID) } returns JuniferFrequency(
            FREQUENCY,
            testFrequencyInst
        )
        every { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) } returns testPaySchedulePeriod
        every { mockRepo.getPaymentScheduleSeasonalDfnById(JUNIFER_SEASONAL_DFN_ID) } returns testSeasonalDfn

        `when`("a payment schedule event is generated") {

            coEvery { mockSyncClient.syncPaymentScheduleEntity(patchPaymentScheduleSync) } returns paymentScheduleSyncResponse

            processor.process(paymentScheduleSyncEvent)

            then("the payment schedule should be updated") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleFrequency(JUNIFER_FREQUENCY_INST_ID) }
                verify { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) }
                verify { mockRepo.getPaymentScheduleSeasonalDfnById(JUNIFER_SEASONAL_DFN_ID) }
                coVerify { mockSyncClient.syncPaymentScheduleEntity(patchPaymentScheduleSync) }
            }
        }
    }

    given("an existing mapped payment schedule and payment method, and an existing deleted junifer schedule") {

        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) } throws EntityNotFoundException("Payment schedule period not found")

        `when`("a payment schedule event is generated") {

            coEvery { mockSyncClient.syncPaymentScheduleEntity(deletePaymentScheduleSync) } returns paymentScheduleSyncResponse

            processor.process(paymentScheduleSyncEvent)

            then("the payment schedule should be deleted") {

                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
                }
                verify { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) }
                coVerify { mockSyncClient.syncPaymentScheduleEntity(deletePaymentScheduleSync) }
            }
        }
    }

    given("an existing mapped payment schedule and payment method, and no existing junifer schedule") {

        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) } throws EntityNotFoundException("Oof")

        `when`("a payment schedule event is generated") {

            coEvery { mockSyncClient.syncPaymentScheduleEntity(deletePaymentScheduleSync) } returns paymentScheduleSyncResponse

            processor.process(paymentScheduleSyncEvent)

            then("the payment schedule should be deleted") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
                }
                verify { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) }
                coVerify { mockSyncClient.syncPaymentScheduleEntity(deletePaymentScheduleSync) }
            }
        }
    }

    given("an existing mapped payment schedule and no mapped billing account") {

        every {
            mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) } returns testPaySchedulePeriod

        `when`("a payment schedule event is generated") {

            shouldThrow<SyncDelayedException> { processor.process(paymentScheduleSyncEvent) }

            then("the processor should back off") {

                verify { mockRepo.getPaymentSchedulePeriodById(JUNIFER_PAYMENT_SCHEDULE_ID) }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString())
                }
            }
        }
    }

    given("no existing mapped payment schedule or scheduled payment, and an existing junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns null
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } returns testScheduleItem

        `when`("a scheduled payment event is generated") {

            shouldThrow<AutoDiscardableException> { processor.process(scheduledPaymentSyncEvent) }

            then("the processor should back off") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
            }
        }
    }

    given("no existing mapped payment schedule or payment and a mapped payment schedule, and an existing junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns null
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } returns testScheduleItem
        coEvery { mockSyncClient.syncScheduledPaymentEntity(createScheduledPaymentSyncNullPaymentId) } returns scheduledPaymentSyncResponse

        `when`("a scheduled payment event is generated") {
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.SCHEDULED_PAYMENT,
                    JUNIFER_SCHEDULED_PAYMENT_ID.toString(),
                    SCHEDULED_PAYMENT_ID.toString()
                )
            }

            processor.process(scheduledPaymentSyncEvent)

            then("a scheduled payment should be created") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
                coVerify { mockSyncClient.syncScheduledPaymentEntity(createScheduledPaymentSyncNullPaymentId) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.SCHEDULED_PAYMENT,
                        JUNIFER_SCHEDULED_PAYMENT_ID.toString(),
                        SCHEDULED_PAYMENT_ID.toString()
                    )
                }
            }
        }
    }

    given("an existing mapped payment schedule and no mapped scheduled payment, and an existing junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
        } returns PAYMENT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns null
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } returns testScheduleItem

        `when`("a scheduled payment event is generated") {

            coEvery { mockSyncClient.syncScheduledPaymentEntity(createScheduledPaymentSync) } returns scheduledPaymentSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.SCHEDULED_PAYMENT,
                    JUNIFER_SCHEDULED_PAYMENT_ID.toString(),
                    SCHEDULED_PAYMENT_ID.toString()
                )
            }

            processor.process(scheduledPaymentSyncEvent)

            then("a scheduled payment should be created") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.SCHEDULED_PAYMENT,
                        JUNIFER_SCHEDULED_PAYMENT_ID.toString(),
                        SCHEDULED_PAYMENT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncScheduledPaymentEntity(createScheduledPaymentSync) }
            }
        }
    }

    given("an existing mapped payment schedule and scheduled payment, and an existing junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
        } returns PAYMENT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns SCHEDULED_PAYMENT_ID.toString()
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } returns testScheduleItem

        `when`("a scheduled payment event is generated") {

            coEvery { mockSyncClient.syncScheduledPaymentEntity(patchScheduledPaymentSync) } returns scheduledPaymentSyncResponse

            processor.process(scheduledPaymentSyncEvent)

            then("a scheduled payment should be updated") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
                coVerify { mockSyncClient.syncScheduledPaymentEntity(patchScheduledPaymentSync) }
            }
        }
    }

    given("an existing mapped payment schedule and scheduled payment, and an existing deleted junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
        } returns PAYMENT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns SCHEDULED_PAYMENT_ID.toString()
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } throws EntityNotFoundException("Payment schedule not found")

        `when`("a scheduled payment event is generated") {

            coEvery { mockSyncClient.syncScheduledPaymentEntity(deleteScheduledPaymentSync) } returns scheduledPaymentSyncResponse

            processor.process(scheduledPaymentSyncEvent)

            then("a scheduled payment should be deleted") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
                coVerify { mockSyncClient.syncScheduledPaymentEntity(deleteScheduledPaymentSync) }
            }
        }
    }

    given("an existing mapped payment schedule and scheduled payment, and no existing junifer scheduled payment") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT_SCHEDULE, JUNIFER_PAYMENT_SCHEDULE_ID.toString())
        } returns PAYMENT_SCHEDULE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString())
        } returns PAYMENT_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
        } returns SCHEDULED_PAYMENT_ID.toString()
        every { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) } throws EntityNotFoundException("Oops")

        `when`("a scheduled payment event is generated") {

            coEvery { mockSyncClient.syncScheduledPaymentEntity(deleteScheduledPaymentSync) } returns scheduledPaymentSyncResponse

            processor.process(scheduledPaymentSyncEvent)

            then("a scheduled payment should be deleted") {
                verify {
                    mockMapper.getCoreId(EntityIdentifier.SCHEDULED_PAYMENT, JUNIFER_SCHEDULED_PAYMENT_ID.toString())
                }
                verify { mockRepo.getPaymentScheduleItemById(JUNIFER_SCHEDULED_PAYMENT_ID) }
                coVerify { mockSyncClient.syncScheduledPaymentEntity(deleteScheduledPaymentSync) }
            }
        }
    }
})
