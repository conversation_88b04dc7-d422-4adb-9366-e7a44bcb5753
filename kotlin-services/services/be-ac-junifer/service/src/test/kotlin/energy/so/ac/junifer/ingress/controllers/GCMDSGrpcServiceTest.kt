package energy.so.ac.junifer.ingress.controllers

import com.google.protobuf.NullValue
import energy.so.ac.junifer.config.JuniferCacheConfig
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.fixtures.GCMDSPrecannedData.testGasGcmdsResponse
import energy.so.ac.junifer.fixtures.GCMDSPrecannedData.testGcmdsResponse
import energy.so.ac.junifer.fixtures.GCMDSPrecannedData.testGetTimeSeriesRequest
import energy.so.ac.junifer.fixtures.InMemoryJuniferCache
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMeterPoint
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testJuniferMpanAsset
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testJuniferMprnAsset
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSConstants.ACTIVE_IMPORT_STREAM
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSDatasource
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSMarketType
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSTimeSeriesRequestParams
import energy.so.ac.junifer.ingress.models.gcmds.toRequest
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EndpointIdentifier
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.gcmds.UnitOfMeasure
import energy.so.commons.grpc.extensions.toNullableDouble
import energy.so.commons.nullableDouble
import energy.so.users.v2.FeatureName
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.springframework.http.ResponseEntity

class GCMDSGrpcServiceTest : BehaviorSpec({

    val mockGCMDSDatasource = mockk<GCMDSDatasource>()
    val mockFeatureService = mockk<FeatureService>()
    val mockMeterPointRepo = mockk<JuniferMeterPointRepository>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val inMemoryJuniferCache = InMemoryJuniferCache()

    val sut = GCMDSGrpcService(
        mockGCMDSDatasource,
        inMemoryMapper,
        mockFeatureService,
        inMemoryJuniferCache,
        JuniferCacheConfig(
            enabled = true,
            getEstimatedUsageExpirationTime = 10L,
            getMeterPointStructureExpirationTime = 10L,
            getMeterReadingsExpirationTime = 10L,
            getMeterIdToRegistersExpirationTime = 10L,
            getGcmdsListingsExpirationTime = 10L
        ),
        mockMeterPointRepo,
    )

    afterEach {
        confirmVerified(mockGCMDSDatasource, mockFeatureService, mockMeterPointRepo)
    }

    given("a request to retrieve listings") {

        inMemoryMapper.createCoreMapping(
            EntityIdentifier.METER_POINT,
            JUNIFER_METER_POINT_ID.toString(),
            METER_POINT_ID.toString()
        )

        every { mockMeterPointRepo.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepo.getAssetType(JUNIFER_METER_POINT_ID) } returns testJuniferMpanAsset

        and("the cache feature is enabled") {

            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true

            `when`("the request is cached") {

                val encodedParams = Json.encodeToString(
                    GCMDSTimeSeriesRequestParams.fromProto(
                        testGetTimeSeriesRequest,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                )

                val encodedResponse = Json.encodeToString(testGcmdsResponse)

                inMemoryJuniferCache.cacheResponse(
                    EndpointIdentifier.GCMDS_INTERVAL_TIME_SERIES,
                    encodedParams,
                    encodedResponse,
                    10
                )

                val response = sut.getIntervalTimeSeries(testGetTimeSeriesRequest)

                then("the cached result should be returned") {

                    inMemoryJuniferCache.clear()

                    coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                    verify { mockMeterPointRepo.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepo.getAssetType(JUNIFER_METER_POINT_ID) }
                    verify(exactly = 0) {
                        mockGCMDSDatasource.getTimeSeries(
                            testGetTimeSeriesRequest.toRequest(
                                marketType = GCMDSMarketType.MPAN,
                                meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                            )
                        )
                    }

                    response.streamsCount shouldBe 1
                    response.streamsList.forEach {
                        it.id shouldBe ACTIVE_IMPORT_STREAM
                        it.uom shouldBe UnitOfMeasure.KWH
                        it.intervalsCount shouldBe 48
                        it.getIntervals(0) shouldBe 1.0.toNullableDouble()
                        it.getIntervals(1) shouldBe 2.0.toNullableDouble()
                        it.getIntervals(46) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                        it.getIntervals(47) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                    }
                }
            }

            `when`("the request is not cached") {

                every {
                    mockGCMDSDatasource.getTimeSeries(
                        testGetTimeSeriesRequest.toRequest(
                            marketType = GCMDSMarketType.MPAN,
                            meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                        )
                    )
                } returns ResponseEntity.ok(testGcmdsResponse)

                val response = sut.getIntervalTimeSeries(testGetTimeSeriesRequest)

                then("GCMDS should be queried, and the result cached") {
                    coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                    verify { mockMeterPointRepo.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepo.getAssetType(JUNIFER_METER_POINT_ID) }
                    verify {
                        mockGCMDSDatasource.getTimeSeries(
                            testGetTimeSeriesRequest.toRequest(
                                marketType = GCMDSMarketType.MPAN,
                                meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                            )
                        )
                    }

                    response.streamsCount shouldBe 1
                    response.streamsList.forEach {
                        it.id shouldBe ACTIVE_IMPORT_STREAM
                        it.uom shouldBe UnitOfMeasure.KWH
                        it.intervalsCount shouldBe 48
                        it.getIntervals(0) shouldBe 1.0.toNullableDouble()
                        it.getIntervals(1) shouldBe 2.0.toNullableDouble()
                        it.getIntervals(46) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                        it.getIntervals(47) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                    }
                }
            }
        }
    }

    given("a request to retrieve gas listings") {

        inMemoryJuniferCache.clear()

        inMemoryMapper.createCoreMapping(
            EntityIdentifier.METER_POINT,
            JUNIFER_METER_POINT_ID.toString(),
            METER_POINT_ID.toString()
        )

        every { mockMeterPointRepo.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepo.getAssetType(JUNIFER_METER_POINT_ID) } returns testJuniferMprnAsset
        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true

        `when`("the request is not cached") {

            every {
                mockGCMDSDatasource.getTimeSeries(
                    testGetTimeSeriesRequest.toRequest(
                        marketType = GCMDSMarketType.MPRN,
                        meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                    )
                )
            } returns ResponseEntity.ok(testGasGcmdsResponse.copy())

            val response = sut.getIntervalTimeSeries(testGetTimeSeriesRequest)

            then("GCMDS should be queried, and the result cached") {
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                verify { mockMeterPointRepo.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepo.getAssetType(JUNIFER_METER_POINT_ID) }
                verify {
                    mockGCMDSDatasource.getTimeSeries(
                        testGetTimeSeriesRequest.toRequest(
                            marketType = GCMDSMarketType.MPRN,
                            meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                        )
                    )
                }

                response.streamsCount shouldBe 1
                response.streamsList.forEach {
                    it.id shouldBe ACTIVE_IMPORT_STREAM
                    it.uom shouldBe UnitOfMeasure.KWH
                    it.intervalsCount shouldBe 48
                    it.getIntervals(0) shouldBe 11.14.toNullableDouble() // M3 converted to kWh
                    it.getIntervals(1) shouldBe 22.27.toNullableDouble()
                    it.getIntervals(46) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                    it.getIntervals(47) shouldBe nullableDouble { null_ = NullValue.NULL_VALUE }
                }
            }
        }
    }
})
