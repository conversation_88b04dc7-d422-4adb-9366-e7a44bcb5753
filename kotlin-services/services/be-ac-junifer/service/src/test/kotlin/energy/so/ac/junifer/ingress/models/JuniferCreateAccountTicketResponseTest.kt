package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountTicketResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountTicketResponse
import energy.so.ac.junifer.ingress.models.accounts.toProtoResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCreateAccountTicketResponseTest : BehaviorSpec({

    given("junifer create account ticket response") {
        val juniferResponse = juniferCreateAccountTicketResponse

        `when`("map to create account ticket response") {
            val response = juniferResponse.toProtoResponse()

            then("return a corresponding CreateAccountTicketResponse") {
                response shouldBe createAccountTicketResponse
            }
        }
    }
})
