package energy.so.ac.junifer.admin.controller

import energy.so.ac.junifer.admin.controllers.AdminController
import energy.so.ac.junifer.admin.services.ResyncService
import energy.so.ac.junifer.fixtures.AdminPrecannedData.resyncAccountsRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.mockk

class AdminControllerTest : BehaviorSpec({

    val mockResyncService = mockk<ResyncService>()

    val sut = AdminController(mockResyncService)

    given("request to resync a list of accounts") {
        and("service responds successfully") {
            coJustRun { mockResyncService.resyncAccounts(any()) }

            `when`("resyncAccounts is called") {
                then("accounts resync started") {
                    sut.resyncAccounts(resyncAccountsRequest)
                }
            }
        }

        and("service throws exception") {
            coEvery { mockResyncService.resyncAccounts(any()) } throws Exception("oops")

            `when`("resyncAccounts is called") {
                then("nothing happens") {
                    sut.resyncAccounts(resyncAccountsRequest)
                }
            }
        }
    }
})
