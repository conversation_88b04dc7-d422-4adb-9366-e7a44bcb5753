package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.accountAgreementsResponse
import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.juniferGetAccountAgreementsResponse1
import energy.so.ac.junifer.ingress.models.accountAgreements.toProtoResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferGetAccountAgreementsResponseTest : BehaviorSpec({

    given("junifer get account agreements response") {
        val juniferResponse = juniferGetAccountAgreementsResponse1

        `when`("map to proto response") {
            val result = juniferResponse.toProtoResponse()

            then("return a corresponding proto") {
                result shouldBe accountAgreementsResponse
            }
        }
    }
})
