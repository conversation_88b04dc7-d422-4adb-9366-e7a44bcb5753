package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JooqJuniferTicketHistoryRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.JUNIFER_TICKET_HISTORY_ID
import energy.so.ac.junifer.fixtures.JUNIFER_TICKET_ID
import energy.so.ac.junifer.fixtures.TICKET_ENTITY_ID
import energy.so.ac.junifer.fixtures.TICKET_HISTORY_ENTITY_ID
import energy.so.ac.junifer.fixtures.createTicketHistoryEntityRequest
import energy.so.ac.junifer.fixtures.deleteTicketHistoryEntityRequest
import energy.so.ac.junifer.fixtures.juniferTicketHistory
import energy.so.ac.junifer.fixtures.juniferTicketHistoryUpdated
import energy.so.ac.junifer.fixtures.ticketHistorySyncResponse
import energy.so.ac.junifer.fixtures.updateTicketHistoryEntityRequest
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.SyncEvent
import energy.so.tickets.client.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify

class TicketHistorySyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockJooqJuniferTicketHistoryRepository = mockk<JooqJuniferTicketHistoryRepository>()
    val mockTicketSyncClient = mockk<SyncClient>()

    val sut = TicketHistorySyncProcessor(
        mockMapper,
        mockTicketSyncClient,
        mockJooqJuniferTicketHistoryRepository
    )

    given("invalid TicketHistory Sync Event") {
        `when`("reference is null") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent())
                }.message shouldBe "Cannot process sync event without a reference"
            }
        }
        `when`("event is unsupported") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent(reference = "123", eventType = EventType.ACCOUNT))
                }.message shouldBe "Unsupported EventType ${EventType.ACCOUNT}"
            }
        }
    }
    given("a valid TicketHistory Sync Event") {
        `when`("junifer ticketHistory is created") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET_HISTORY,
                    JUNIFER_TICKET_HISTORY_ID.toString()
                )
            } returns null
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) } returns juniferTicketHistory
            coEvery {
                mockTicketSyncClient.syncTicketHistoryEntity(
                    createTicketHistoryEntityRequest
                )
            } returns ticketHistorySyncResponse
            every {
                mockMapper.createCoreMapping(
                    EntityIdentifier.TICKET_HISTORY,
                    JUNIFER_TICKET_HISTORY_ID.toString(),
                    TICKET_HISTORY_ENTITY_ID.toString()
                )
            } just Runs

            then("sync ticketHistory entity successfully") {
                sut.process(
                    SyncEvent(
                        reference = JUNIFER_TICKET_HISTORY_ID.toString(),
                        eventType = EventType.TICKET_HISTORY
                    )
                )
//                verify { mockMapper.getCoreId(EntityIdentifier.TICKET_HISTORY, JUNIFER_TICKET_HISTORY_ID.toString()) }
//                verify { mockMapper.getCoreId(EntityIdentifier.TICKET, JUNIFER_TICKET_ID.toString()) }

                verify { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketHistoryEntity(
                        createTicketHistoryEntityRequest
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.TICKET_HISTORY,
                        JUNIFER_TICKET_HISTORY_ID.toString(),
                        TICKET_HISTORY_ENTITY_ID.toString()
                    )
                }
            }
        }
        `when`("junifer ticket is not syncronized ") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET_HISTORY,
                    JUNIFER_TICKET_HISTORY_ID.toString()
                )
            } returns null
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns null
            every { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) } returns juniferTicketHistory

            then("throw SyncDelayedException") {
                shouldThrow<SyncDelayedException> {
                    sut.process(
                        SyncEvent(
                            reference = JUNIFER_TICKET_HISTORY_ID.toString(),
                            eventType = EventType.TICKET_HISTORY
                        )
                    )
                }
            }
        }
        `when`("junifer ticketHistory is updated") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET_HISTORY,
                    JUNIFER_TICKET_HISTORY_ID.toString()
                )
            } returns TICKET_HISTORY_ENTITY_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) } returns juniferTicketHistoryUpdated
            coEvery {
                mockTicketSyncClient.syncTicketHistoryEntity(
                    updateTicketHistoryEntityRequest
                )
            } returns ticketHistorySyncResponse

            then("sync ticketHistory entity successfully") {
                sut.process(
                    SyncEvent(
                        reference = JUNIFER_TICKET_HISTORY_ID.toString(),
                        eventType = EventType.TICKET_HISTORY
                    )
                )

                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET_HISTORY,
                        JUNIFER_TICKET_HISTORY_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString()
                    )
                }
                verify { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketHistoryEntity(
                        updateTicketHistoryEntityRequest
                    )
                }
            }
        }
        `when`("junifer ticketHistory is deleted") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET_HISTORY,
                    JUNIFER_TICKET_HISTORY_ID.toString()
                )
            } returns TICKET_HISTORY_ENTITY_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) } returns null
            coEvery {
                mockTicketSyncClient.syncTicketHistoryEntity(
                    deleteTicketHistoryEntityRequest
                )
            } returns ticketHistorySyncResponse
            every {
                mockMapper.deleteMappingByCoreId(
                    EntityIdentifier.TICKET_HISTORY,
                    TICKET_HISTORY_ENTITY_ID.toString()
                )
            } just Runs

            then("sync ticketHistory entity successfully") {
                sut.process(
                    SyncEvent(
                        reference = JUNIFER_TICKET_HISTORY_ID.toString(),
                        eventType = EventType.TICKET_HISTORY
                    )
                )
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET_HISTORY,
                        JUNIFER_TICKET_HISTORY_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString()
                    )
                }
                verify { mockJooqJuniferTicketHistoryRepository.findById(JUNIFER_TICKET_HISTORY_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketHistoryEntity(
                        deleteTicketHistoryEntityRequest
                    )
                }
                verify {
                    mockMapper.deleteMappingByCoreId(
                        EntityIdentifier.TICKET_HISTORY,
                        TICKET_HISTORY_ENTITY_ID.toString()
                    )
                }


            }
        }
    }

})
