package energy.so.ac.junifer.admin

import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.springframework.context.annotation.ConditionContext
import org.springframework.core.type.AnnotatedTypeMetadata

class OptimizedSyncEnvironmentTest : BehaviorSpec({

    val mockContext = mockk<ConditionContext>()
    val mockTypeMetadata = mockk<AnnotatedTypeMetadata>()
    val sut = OptimizedSyncEnvironment()

    afterEach {
        clearMocks(mockContext, mockTypeMetadata)
        confirmVerified(mockContext, mockTypeMetadata)
    }

    given("non prod environment") {
        and("optimized sync enabled") {
            `when`("matches method is called") {
                every { mockContext.environment.getProperty("environment.environment") } returns "test"
                every { mockContext.environment.getProperty("sync.optimization.optimized-data-sync") } returns "true"

                then("returns true") {
                    val result = sut.matches(mockContext, mockTypeMetadata)

                    assertSoftly { result shouldBe true }
                    verify { mockContext.environment.getProperty("environment.environment") }
                }
            }
        }

        and("optimized sync disabled") {
            `when`("matches method is called") {
                every { mockContext.environment.getProperty("environment.environment") } returns "test"
                every { mockContext.environment.getProperty("sync.optimization.optimized-data-sync") } returns "false"

                then("returns false") {
                    val result = sut.matches(mockContext, mockTypeMetadata)

                    assertSoftly { result shouldBe false }
                    verify { mockContext.environment.getProperty("environment.environment") }
                }
            }
        }
    }

    given("production environment") {
        and("optimized sync enabled") {
            `when`("matches method is called") {
                every { mockContext.environment.getProperty("environment.environment") } returns "production"
                every { mockContext.environment.getProperty("sync.optimization.optimized-data-sync") } returns "true"

                then("returns false") {
                    val result = sut.matches(mockContext, mockTypeMetadata)

                    assertSoftly { result shouldBe false }
                    verify { mockContext.environment.getProperty("environment.environment") }
                }
            }
        }

        and("optimized sync disabled") {
            `when`("matches method is called") {
                every { mockContext.environment.getProperty("environment.environment") } returns "production"
                every { mockContext.environment.getProperty("sync.optimization.optimized-data-sync") } returns "false"

                then("returns false") {
                    val result = sut.matches(mockContext, mockTypeMetadata)

                    assertSoftly { result shouldBe false }
                    verify { mockContext.environment.getProperty("environment.environment") }
                }
            }
        }
    }
})
