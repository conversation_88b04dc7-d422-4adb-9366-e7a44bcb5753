package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterRegisterRepository
import energy.so.ac.junifer.egress.database.repositories.maxRetryCount
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData.METER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_CONFIG_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_TYPE_CODE
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_TYPE_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.REGISTER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.createRegisterSync
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.createRegisterWithRateNameSync
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.deleteRegisterSync
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.patchRegisterSync
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.patchRegisterSyncWithNullType
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.registerConfigSyncEvent
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.registerSyncEvent
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.registerSyncResponse
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.testJuniferRegister
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.testJuniferRegisterConfig
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals

class RegisterSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockRegisterRepo = mockk<JuniferMeterRegisterRepository>()
    val mockSyncClient = mockk<SyncClient>()
    val mockFeatureService = mockk<FeatureService> {
        coEvery { isFeatureEnabled(FeatureName.TMP_SO_17157_RATE_NAME_SYNC) } returns false
    }

    val registerProcessor = RegisterSyncProcessor(
        mockMapper,
        mockRegisterRepo,
        mockSyncClient,
        maxRetryCount,
        maxRetryCount,
        mockFeatureService,
    )

    afterEach {
        confirmVerified(mockMapper, mockRegisterRepo, mockSyncClient)
    }

    given("a mapped meter and no mapped register, and an existing junifer register and meter") {

        every { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) } returns JUNIFER_REGISTER_TYPE_CODE
        every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

        `when`("a register event is generated") {
            every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
            coEvery { mockSyncClient.syncRegisterEntity(createRegisterSync) } returns registerSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.REGISTER,
                    JUNIFER_REGISTER_ID.toString(),
                    REGISTER_ID.toString()
                )
            }

            registerProcessor.process(registerSyncEvent)

            then("a register should be created") {

                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.REGISTER,
                        JUNIFER_REGISTER_ID.toString(),
                        REGISTER_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncRegisterEntity(createRegisterSync) }
            }
        }

        `when`("a delete register event is generated") {
            every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister.copy(deletefl = BOOLEAN_TRUE)
            coEvery { mockSyncClient.syncRegisterEntity(any()) } returns registerSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.REGISTER,
                    JUNIFER_REGISTER_ID.toString(),
                    REGISTER_ID.toString()
                )
            }

            registerProcessor.process(registerSyncEvent)

            then("a register should be created") {

                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.REGISTER,
                        JUNIFER_REGISTER_ID.toString(),
                        REGISTER_ID.toString()
                    )
                }
                coVerify {
                    mockSyncClient.syncRegisterEntity(withArg {
                        it.registerEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
                verify(exactly = 0) { mockRegisterRepo.getRateNameByMeterRegisterId(JUNIFER_REGISTER_ID) }
            }
        }

        `when`("a register config event is generated") {
            every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
            registerProcessor.process(registerConfigSyncEvent)

            then("nothing should be created or patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                // No create / patch calls should be made here
            }
        }
    }

    given("no mapped meter and no mapped register, and an existing junifer register and meter") {

        every { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns null
        every { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) } returns JUNIFER_REGISTER_TYPE_CODE
        every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
        every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

        `when`("a register event is generated") {

            // When the meter is queried from the mapper (and is not mapped), this should be thrown
            shouldThrow<SyncDelayedException> { registerProcessor.process(registerSyncEvent) }

            then("the event should error and nothing should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
            }
        }

        `when`("a register config event is generated") {

            registerProcessor.process(registerConfigSyncEvent)

            then("nothing should be created or patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                // No create / patch calls should be made here
            }
        }
    }

    given("a mapped meter and a mapped register, and an existing junifer register and meter") {
        and("registerTypeCode exists") {
            every {
                mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
            } returns REGISTER_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.METER,
                    JUNIFER_METER_ID.toString()
                )
            } returns METER_ID.toString()
            every { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) } returns JUNIFER_REGISTER_TYPE_CODE
            every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
            every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

            `when`("a register event is generated") {
                every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
                coEvery { mockSyncClient.syncRegisterEntity(patchRegisterSync) } returns registerSyncResponse

                registerProcessor.process(registerSyncEvent)

                then("the register should be patched") {
                    verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                    verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                    verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                    verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                    coVerify { mockSyncClient.syncRegisterEntity(patchRegisterSync) }
                }
            }

            `when`("a register event is generated") {
                every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister.copy(deletefl = BOOLEAN_TRUE)
                coEvery { mockSyncClient.syncRegisterEntity(any()) } returns registerSyncResponse

                registerProcessor.process(registerSyncEvent)

                then("the delete register should be patched") {
                    verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                    verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                    verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                    verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                    coVerify {
                        mockSyncClient.syncRegisterEntity(withArg {
                            it.registerEntity.deleted.getValueOrNull()?.toLocalDateTime()
                                ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                                .truncatedTo(ChronoUnit.MINUTES)
                        })
                    }
                    coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17157_RATE_NAME_SYNC) }
                }
            }

            `when`("a register config event is generated") {
                every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
                coEvery { mockSyncClient.syncRegisterEntity(patchRegisterSync) } returns registerSyncResponse

                registerProcessor.process(registerConfigSyncEvent)

                then("the register should be patched") {
                    verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                    verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                    verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                    verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                    coVerify { mockSyncClient.syncRegisterEntity(patchRegisterSync) }
                    coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17157_RATE_NAME_SYNC) }
                }
            }
        }

        and("getRegisterTypeCode not called because meterregistertypefk is null") {
            every {
                mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
            } returns REGISTER_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.METER,
                    JUNIFER_METER_ID.toString()
                )
            } returns METER_ID.toString()
            every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
            every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig.copy(
                meterregistertypefk = null
            )

            `when`("a register config event is generated") {
                every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
                coEvery {
                    mockSyncClient.syncRegisterEntity(patchRegisterSyncWithNullType)
                } returns registerSyncResponse

                registerProcessor.process(registerConfigSyncEvent)

                then("the register should be patched") {
                    verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                    verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                    verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                    coVerify {
                        mockSyncClient.syncRegisterEntity(patchRegisterSyncWithNullType)
                    }
                }
            }
        }
    }

    given("a mapped register and no mapped meter, and an existing junifer register and meter") {

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns REGISTER_ID.toString()
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns null
        every { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) } returns JUNIFER_REGISTER_TYPE_CODE
        every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
        every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

        `when`("a register event is generated") {

            coEvery { mockSyncClient.syncRegisterEntity(patchRegisterSync) } returns registerSyncResponse

            registerProcessor.process(registerSyncEvent)

            // We only check that the meter exists when we create the register
            then("the register should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                coVerify { mockSyncClient.syncRegisterEntity(patchRegisterSync) }
            }
        }
    }

    given("a mapped meter and a mapped register, and no existing junifer register and an existing register config") {

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns REGISTER_ID.toString()
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } throws EntityNotFoundException("Register not found")
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

        `when`("a register event is generated") {

            coEvery { mockSyncClient.syncRegisterEntity(deleteRegisterSync) } returns registerSyncResponse

            registerProcessor.process(registerSyncEvent)

            then("the register should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                coVerify { mockSyncClient.syncRegisterEntity(deleteRegisterSync) }
            }
        }

        `when`("a register config event is generated") {

            registerProcessor.process(registerConfigSyncEvent)

            then("nothing should be deleted") {
                verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } // No exception here, a clean return
            }
        }
    }

    given("a mapped meter and a mapped register, and an existing junifer register and no existing register config") {
        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns REGISTER_ID.toString()
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns null
        every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } throws EntityNotFoundException(
            "Register config not found"
        )

        `when`("a register event is generated for sync event which is first time claimed") {

            shouldThrow<AutoDiscardableException> { registerProcessor.process(registerSyncEvent) }

            then("mocks are called expected") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
            }
        }

        `when`("a register event is generated for sync event which is claimed multiple times") {

            shouldThrow<AutoDiscardableException> { registerProcessor.process(registerSyncEvent.copy(retryCount = maxRetryCount - 1)) }

            then("mocks are called expected") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
            }
        }

        `when`("a register event is generated for sync event which is claimed maximum times") {
            val exception = shouldThrow<AutoDiscardableException> {
                registerProcessor.process(registerSyncEvent.copy(retryCount = maxRetryCount))
            }

            then("mocks are called expected") {
                assertEquals(true, exception.canDiscard)
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
            }
        }

        `when`("a register config event is generated") {

            registerProcessor.process(registerConfigSyncEvent)

            then("the event should error and nothing should be patched") {
                verify { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) }
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17157_RATE_NAME_SYNC) }
            }
        }
    }

    given("a register sync event is processed") {
        every { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) } returns JUNIFER_REGISTER_TYPE_CODE
        every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
        every { mockRegisterRepo.getRegisterConfigPeriod(JUNIFER_REGISTER_CONFIG_ID) } returns testJuniferRegisterConfig

        `when`("and TMP_SO_17157_RATE_NAME_SYNC flag is enabled") {
            every { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegister
            every { mockRegisterRepo.getRateNameByMeterRegisterId(JUNIFER_REGISTER_ID) } returns "standard"
            every { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) } returns testJuniferRegisterConfig
            coEvery { mockSyncClient.syncRegisterEntity(createRegisterWithRateNameSync) } returns registerSyncResponse
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17157_RATE_NAME_SYNC) } returns true
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.REGISTER,
                    JUNIFER_REGISTER_ID.toString(),
                    REGISTER_ID.toString()
                )
            }

            registerProcessor.process(registerSyncEvent)

            then("a register should be created with mapped rate name") {
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockRegisterRepo.getRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID) }
                verify { mockRegisterRepo.getRegisterTypeCode(JUNIFER_REGISTER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.REGISTER,
                        JUNIFER_REGISTER_ID.toString(),
                        REGISTER_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncRegisterEntity(createRegisterWithRateNameSync) }
                verify { mockRegisterRepo.getRateNameByMeterRegisterId(JUNIFER_REGISTER_ID) }
            }
        }
    }
})
