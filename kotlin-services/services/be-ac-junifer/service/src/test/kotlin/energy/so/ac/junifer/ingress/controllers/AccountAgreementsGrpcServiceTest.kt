package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.accountAgreementsResponse
import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.juniferGetAccountAgreementsResponse
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.services.accountAgreements.JuniferAccountAgreementsService
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.v2.dtos.idRequest
import energy.so.users.v2.FeatureName
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class AccountAgreementsGrpcServiceTest : BehaviorSpec({

    val mockAccountAgreementsService = mockk<JuniferAccountAgreementsService>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val mockFeatureClient = mockk<FeatureService>()

    val sut = AccountAgreementsGrpcService(mockAccountAgreementsService, inMemoryMapper, mockFeatureClient)

    afterTest {
        confirmVerified(mockAccountAgreementsService)
        clearMocks(mockAccountAgreementsService)
    }

    given("account id") {
        val juniferAccountId = "1"
        val billingAccountId = "100"
        val juniferProductAssetId = "1"
        val productAssetId = "100"
        val juniferAgreementId = "1"
        val agreementId = "1"

        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, juniferProductAssetId, productAssetId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.AGREEMENT, juniferAgreementId, agreementId)
            coEvery { mockFeatureClient.isFeatureEnabled(FeatureName.TMP_SO_20774_ADD_CONTRACTED_TO_DT_IN_AGREEMENTS) } returns true
            and("call to junifer returns response") {
                val juniferResponse = juniferGetAccountAgreementsResponse
                coEvery { mockAccountAgreementsService.getAccountAgreements(juniferAccountId) } returns juniferResponse

                `when`("get Account Agreements") {
                    val result = sut.getAccountAgreements(idRequest { id = billingAccountId.toLong() })

                    then("account agreements returned") {
                        result shouldBe accountAgreementsResponse
                        coVerify { mockAccountAgreementsService.getAccountAgreements(juniferAccountId) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountAgreementsService.getAccountAgreements(juniferAccountId)
                } throws JuniferException("NotFound", "", "")

                `when`("get account agreements") {

                    then("no account agreements are returned") {
                        assertThrows<EntityNotFoundGrpcException> {
                            sut.getAccountAgreements(
                                idRequest { id = billingAccountId.toLong() }
                            )
                        }

                        coVerify { mockAccountAgreementsService.getAccountAgreements(juniferAccountId) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            and("call to junifer returns response") {
                val juniferResponse = juniferGetAccountAgreementsResponse
                coEvery { mockAccountAgreementsService.getAccountAgreements(juniferAccountId) } returns juniferResponse

                `when`("get account agreements") {
                    assertThrows<FailedPreconditionGrpcException> {
                        sut.getAccountAgreements(
                            idRequest { id = billingAccountId.toLong() }
                        )
                    }

                    then("no account agreements are returned") {
                        coVerify(exactly = 0) { mockAccountAgreementsService.getAccountAgreements(juniferAccountId) }
                    }
                }
            }
        }
    }
})
