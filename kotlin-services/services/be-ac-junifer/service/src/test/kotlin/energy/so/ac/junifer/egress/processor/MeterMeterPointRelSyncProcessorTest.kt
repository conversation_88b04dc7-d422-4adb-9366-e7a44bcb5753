package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.JUNIFER_METER_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.JUNIFER_METER_METER_POINT_REL_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.METER_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.METER_METER_POINT_REL_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.createMeterMeterPointRelSync
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.deleteMeterMeterPointRelSync
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.meterMeterPointRelChildSyncEvent
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.meterMeterPointRelSyncEvent
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.meterMeterPointRelSyncResponse
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.testJuniferMeterMeterPointRel
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.updateMeterMeterPointRelSync
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.REGISTER_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.METER
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_METER_POINT_REL
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT
import energy.so.ac.junifer.mapping.EntityIdentifier.REGISTER
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class MeterMeterPointRelSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterPointRepo = mockk<JuniferMeterPointRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = MeterMeterPointRelSyncProcessor(mockMapper, mockMeterPointRepo, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockMeterPointRepo, mockSyncClient)
    }

    given("a mapped meter, meterpoint, register and no existing rel") {

        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) } returns REGISTER_ID.toString()
        every { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) } returns null
        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_PHY_TIME_SERIES event is received") {

            coEvery { mockSyncClient.syncMeterMeterPointRelEntity(createMeterMeterPointRelSync) } returns meterMeterPointRelSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_METER_POINT_REL,
                    JUNIFER_METER_METER_POINT_REL_ID.toString(),
                    METER_METER_POINT_REL_ID.toString()
                )
            }

            processor.process(meterMeterPointRelSyncEvent)

            then("the relationship should be created") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_METER_POINT_REL,
                        JUNIFER_METER_METER_POINT_REL_ID.toString(),
                        METER_METER_POINT_REL_ID.toString()
                    )
                }
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
                coVerify { mockSyncClient.syncMeterMeterPointRelEntity(createMeterMeterPointRelSync) }
            }
        }
    }

    given("a mapped meter, meterpoint, register and an existing rel") {

        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) } returns REGISTER_ID.toString()
        every {
            mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString())
        } returns METER_METER_POINT_REL_ID.toString()
        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_PHY_TIME_SERIES event is received") {

            coEvery { mockSyncClient.syncMeterMeterPointRelEntity(updateMeterMeterPointRelSync) } returns meterMeterPointRelSyncResponse

            processor.process(meterMeterPointRelSyncEvent)

            then("the relationship should be updated") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
                coVerify { mockSyncClient.syncMeterMeterPointRelEntity(updateMeterMeterPointRelSync) }
            }
        }
    }


    given("a unmapped meter") {
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns null
        every { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) } returns null
        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_PHY_TIME_SERIES event is received") {

            shouldThrow<SyncDelayedException> { processor.process(meterMeterPointRelSyncEvent) }

            then("an exception should be thrown") {
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
            }
        }
    }

    given("a mapped meter and an unmapped meterpoint") {
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns null
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) } returns null
        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_PHY_TIME_SERIES event is received") {

            shouldThrow<SyncDelayedException> { processor.process(meterMeterPointRelSyncEvent) }

            then("an exception should be thrown") {
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
            }
        }
    }

    given("a mapped meter, meterpoint and un unmapped register") {
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) } returns null
        every { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) } returns null
        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_PHY_TIME_SERIES event is received") {

            shouldThrow<SyncDelayedException> { processor.process(meterMeterPointRelSyncEvent) }

            then("an exception should be thrown") {
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
            }
        }
    }

    given("the meterpoint rel row was deleted") {

        every { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) } returns null

        `when`("a METER_POINT_PHY_TIME_SERIES event is received and a mapped rel does not exists") {

            every {
                mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString())
            } returns null

            processor.process(meterMeterPointRelSyncEvent)

            then("the processor should do nothing") {
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
            }
        }

        `when`("a METER_POINT_PHY_TIME_SERIES event is received and a mapped rel exists") {

            every {
                mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString())
            } returns METER_METER_POINT_REL_ID.toString()
            coEvery { mockSyncClient.syncMeterMeterPointRelEntity(deleteMeterMeterPointRelSync) } returns meterMeterPointRelSyncResponse

            processor.process(meterMeterPointRelSyncEvent)

            then("the processor should delete the existing row") {
                verify { mockMeterPointRepo.getMeterMeterPointRel(JUNIFER_METER_METER_POINT_REL_ID) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                coVerify { mockSyncClient.syncMeterMeterPointRelEntity(deleteMeterMeterPointRelSync) }
            }
        }
    }

    given("a mapped meter, meterpoint, register and an existing meterMeterRel") {
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) } returns REGISTER_ID.toString()
        every {
            mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString())
        } returns METER_METER_POINT_REL_ID.toString()
        every { mockMeterPointRepo.getMeterPointPhyTimeSeriesByMeterPointTimeSeriesId(JUNIFER_METER_METER_POINT_REL_ID) } returns testJuniferMeterMeterPointRel

        `when`("a METER_POINT_TIME_SERIES event is received") {
            coEvery { mockSyncClient.syncMeterMeterPointRelEntity(updateMeterMeterPointRelSync) } returns meterMeterPointRelSyncResponse
            processor.process(meterMeterPointRelChildSyncEvent)

            then("the relationship should be updated") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(METER, JUNIFER_METER_ID.toString()) }
                verify { mockMapper.getCoreId(REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(METER_METER_POINT_REL, JUNIFER_METER_METER_POINT_REL_ID.toString()) }
                verify {
                    mockMeterPointRepo.getMeterPointPhyTimeSeriesByMeterPointTimeSeriesId(
                        JUNIFER_METER_METER_POINT_REL_ID
                    )
                }
                coVerify { mockSyncClient.syncMeterMeterPointRelEntity(updateMeterMeterPointRelSync) }
            }
        }
    }

    given("no existing meterMeterRel") {
        every { mockMeterPointRepo.getMeterPointPhyTimeSeriesByMeterPointTimeSeriesId(JUNIFER_METER_METER_POINT_REL_ID) } returns null

        `when`("a METER_POINT_TIME_SERIES event is received") {
            processor.process(meterMeterPointRelChildSyncEvent)

            then("the processor should do nothing") {
                verify {
                    mockMeterPointRepo.getMeterPointPhyTimeSeriesByMeterPointTimeSeriesId(
                        JUNIFER_METER_METER_POINT_REL_ID
                    )
                }
            }
        }
    }
})
