package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferConsentRepository
import energy.so.ac.junifer.egress.database.repositories.JuniferPsrRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.CUSTOMER_PSR_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.JUNIFER_CONSENT_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.JUNIFER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.PROPERTY_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.PSR_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.UK_VULN_CUST_PSR_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.consentSyncEvent
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.createCustomerPsrRelSync
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.customerPsrSyncEvent
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.customerPsrSyncResponse
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.testJuniferConsent
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.testJuniferCustomer
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.testJuniferUkVulnCustPsr
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.updateCustomerPsrRelSync
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER_PSR
import energy.so.ac.junifer.mapping.EntityIdentifier.PROPERTY
import energy.so.ac.junifer.mapping.EntityIdentifier.PSR
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.customers.client.v2.sync.SyncClient
import energy.so.customers.sync.v2.copy
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.assertThrows

class CustomerPsrSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockCustomerPsrRepo = mockk<JuniferPsrRepository>()
    val mockSyncClient = mockk<SyncClient>()
    val mockConsentRepo = mockk<JuniferConsentRepository>()

    val customerPsrProcessor =
        CustomerPsrSyncProcessor(mockMapper, mockCustomerPsrRepo, mockConsentRepo, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockCustomerPsrRepo, mockSyncClient, mockConsentRepo)
        clearMocks(mockMapper, mockCustomerPsrRepo, mockSyncClient, mockConsentRepo)
    }

    given("no existing mapped CustomerPsrRel and an existing junifer UkVulnCustPsr") {

        every { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) } returns null
        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) } returns PSR_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID
        every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
        every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr
        every { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferConsent

        `when`("a customerPsr event is generated") {

            coEvery { mockSyncClient.syncCustomerPsrRelEntity(createCustomerPsrRelSync) } returns customerPsrSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_PSR,
                    UK_VULN_CUST_PSR_ID.toInt().toString(),
                    CUSTOMER_PSR_ID.toInt().toString()
                )
            }

            customerPsrProcessor.process(customerPsrSyncEvent)

            then("a new customerPsr should be created") {
                verify { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }
                verify { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) }
                verify { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        CUSTOMER_PSR,
                        UK_VULN_CUST_PSR_ID.toInt().toString(),
                        CUSTOMER_PSR_ID.toInt().toString()
                    )
                }
                coVerify { mockSyncClient.syncCustomerPsrRelEntity(createCustomerPsrRelSync) }
            }
        }
    }

    given("existing mapped CustomerPsrRel and an existing junifer UkVulnCustPsr") {

        every { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) } returns CUSTOMER_PSR_ID.toString()
        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) } returns PSR_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID
        every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
        every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr
        every { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferConsent

        `when`("a customerPsr event is generated") {

            coEvery { mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSync) } returns customerPsrSyncResponse

            customerPsrProcessor.process(customerPsrSyncEvent)

            then("existing customerPsr should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }
                verify { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) }
                verify { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }

                coVerify { mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSync) }
            }
        }
    }

    given("existing mapped CustomerPsrRel and non existing junifer UkVulnCustPsr") {
        val updateCustomerPsrRelSyncConsentFalse = updateCustomerPsrRelSync.copy {
            customerPsrRelEntity = customerPsrRelEntity.copy { consentFl = false.toNullableBoolean() }
        }
        every { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) } returns CUSTOMER_PSR_ID.toString()
        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) } returns PSR_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID
        every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
        every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr
        every { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns null

        `when`("a customerPsr event is generated") {

            coEvery { mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSyncConsentFalse) } returns customerPsrSyncResponse

            customerPsrProcessor.process(customerPsrSyncEvent)

            then("existing customerPsr should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }
                verify { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) }
                verify { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }

                coVerify {
                    mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSyncConsentFalse)
                }
            }
        }
    }

    given("existing mapped CustomerPsrRel and an existing junifer UkVulnCustPsr to be deleted") {

        every { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) } returns CUSTOMER_PSR_ID.toString()
        every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) } returns PSR_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID
        every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
        every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr.copy(
            deletefl = "Y"
        )
        every { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferConsent

        `when`("a customerPsr event is generated") {

            coEvery { mockSyncClient.syncCustomerPsrRelEntity(any()) } returns customerPsrSyncResponse

            customerPsrProcessor.process(customerPsrSyncEvent)

            then("existing customerPsr should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }
                verify { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) }
                verify { mockConsentRepo.findByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) }

                coVerify { mockSyncClient.syncCustomerPsrRelEntity(any()) }
            }
        }
    }

    given("CONSENT event_type") {
        and("no consent for entity ukVulnCustPsr corresponding to reference found") {
            coEvery { mockConsentRepo.findById(JUNIFER_CONSENT_ID) } returns null

            `when`("CONSENT event_type generated") {
                customerPsrProcessor.process(consentSyncEvent)

                then("processing is skipped") {
                    coVerify { mockConsentRepo.findById(JUNIFER_CONSENT_ID) }
                    coVerify(exactly = 0) {
                        mockCustomerPsrRepo.getUkVulnCustPsr(any())
                        mockConsentRepo.findByUkVulnCustPsrId(any())
                        mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(any())
                        mockMapper.getCoreId(any(), any())
                        mockSyncClient.syncCustomerPsrRelEntity(any())
                    }
                }
            }
        }
        and("consent, and customerPsr found but no customerPsr mapping exists") {
            every { mockConsentRepo.findById(JUNIFER_CONSENT_ID) } returns testJuniferConsent
            every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
            every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr
            every { mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString()) } returns null

            `when`("CONSENT event_type generated") {
                then("throw SyncDelayedException") {
                    assertThrows<SyncDelayedException> {
                        customerPsrProcessor.process(consentSyncEvent)
                    }
                    coVerify {
                        mockConsentRepo.findById(JUNIFER_CONSENT_ID)
                        mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID)
                        mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString())
                    }
                    coVerify(exactly = 0) { mockSyncClient.syncCustomerPsrRelEntity(any()) }
                }
            }
        }
        and("consent, psr, and customerPsr and customerPsr mapping exists") {
            every { mockConsentRepo.findById(JUNIFER_CONSENT_ID) } returns testJuniferConsent
            every { mockConsentRepo.findByUkVulnCustPsrId(testJuniferUkVulnCustPsr.id!!) } returns testJuniferConsent
            every { mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID) } returns testJuniferCustomer
            every { mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID) } returns testJuniferUkVulnCustPsr
            every { mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString()) } returns PSR_ID.toString()
            every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
            every { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) } returns PROPERTY_ID
            every {
                mockMapper.getCoreId(
                    CUSTOMER_PSR,
                    UK_VULN_CUST_PSR_ID.toString()
                )
            } returns CUSTOMER_PSR_ID.toString()
            coEvery { mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSync) } returns customerPsrSyncResponse

            `when`("CONSENT event_type generated") {
                customerPsrProcessor.process(consentSyncEvent)

                then("customerPsr corresponding to consent should be patched") {
                    coVerify {
                        mockConsentRepo.findById(JUNIFER_CONSENT_ID)
                        mockCustomerPsrRepo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID)
                        mockCustomerPsrRepo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID)
                        mockMapper.getCoreId(CUSTOMER_PSR, UK_VULN_CUST_PSR_ID.toString())
                        mockMapper.getCoreId(PSR, UK_VULN_CUST_PSR_ID.toString())
                        mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString())
                        mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString())
                        mockConsentRepo.findByUkVulnCustPsrId(testJuniferUkVulnCustPsr.id!!)
                        mockSyncClient.syncCustomerPsrRelEntity(updateCustomerPsrRelSync)
                    }
                }
            }
        }
    }
})
