package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferBillRepository
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.BILLING_PERIOD_ID
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.JUNIFER_BILLING_PERIOD_ID
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.billingPeriodSyncEvent
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.billingPeriodSyncResponse
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.createBillingPeriodSync
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.deleteBillingPeriodSync
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.testJuniferBillingPeriod
import energy.so.ac.junifer.fixtures.BillingPeriodPrecannedData.updateBillingPeriodSync
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_PERIOD
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class BillingPeriodSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockBillRepository = mockk<JuniferBillRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val billingPeriodProcessor =
        BillingPeriodSyncProcessor(mockMapper, mockBillRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockBillRepository, mockSyncClient)
    }

    given("no existing mapped billingPeriod and an existing junifer billingPeriod") {

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) } returns testJuniferBillingPeriod

        `when`("a billingPeriod event is generated") {

            coEvery { mockSyncClient.syncBillingPeriodEntity(createBillingPeriodSync) } returns billingPeriodSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILLING_PERIOD,
                    JUNIFER_BILLING_PERIOD_ID.toString(),
                    BILLING_PERIOD_ID.toString()
                )
            }

            billingPeriodProcessor.process(billingPeriodSyncEvent)

            then("a new billingPeriod should be created") {
                verify { mockMapper.getCoreId(BILLING_PERIOD, JUNIFER_BILLING_PERIOD_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        BILLING_PERIOD,
                        JUNIFER_BILLING_PERIOD_ID.toString(),
                        BILLING_PERIOD_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillingPeriodEntity(createBillingPeriodSync) }
            }
        }
    }

    given("an existing mapped billingPeriod and an existing junifer billingPeriod") {

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns BILLING_PERIOD_ID.toString()
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) } returns testJuniferBillingPeriod

        `when`("a billingPeriod event is generated") {

            coEvery { mockSyncClient.syncBillingPeriodEntity(updateBillingPeriodSync) } returns billingPeriodSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILLING_PERIOD,
                    JUNIFER_BILLING_PERIOD_ID.toString(),
                    BILLING_PERIOD_ID.toString()
                )
            }

            billingPeriodProcessor.process(billingPeriodSyncEvent)

            then("billingPeriod should be patched") {
                verify { mockMapper.getCoreId(BILLING_PERIOD, JUNIFER_BILLING_PERIOD_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) }
                coVerify { mockSyncClient.syncBillingPeriodEntity(updateBillingPeriodSync) }
            }
        }
    }

    given("an existing mapped billingPeriod and existing junifer billingPeriod to be deleted") {

        every {
            mockMapper.getCoreId(
                BILLING_PERIOD,
                JUNIFER_BILLING_PERIOD_ID.toString()
            )
        } returns BILLING_PERIOD_ID.toString()
        every { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) } throws EntityNotFoundException("Billing period not found")

        `when`("a billingPeriod event is generated") {

            coEvery { mockSyncClient.syncBillingPeriodEntity(deleteBillingPeriodSync) } returns billingPeriodSyncResponse

            billingPeriodProcessor.process(billingPeriodSyncEvent)

            then("billingPeriod should be deleted") {
                verify { mockMapper.getCoreId(BILLING_PERIOD, JUNIFER_BILLING_PERIOD_ID.toString()) }
                verify { mockBillRepository.getBillingPeriod(JUNIFER_BILLING_PERIOD_ID) }
                coVerify { mockSyncClient.syncBillingPeriodEntity(deleteBillingPeriodSync) }
            }
        }
    }

})
