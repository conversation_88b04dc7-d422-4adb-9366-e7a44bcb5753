package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.CustomerPrecannedData
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.generic.createMappingRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe


class GenericGrpcServiceTest : BehaviorSpec({

    val inMemoryMapper = InMemoryJuniferCoreIdMapper()

    val sut = GenericGrpcService(inMemoryMapper)

    given("customer create mapping") {
        `when`("call createCustomerMapping") {
            sut.createCoreMapping(createMappingRequest {
                juniferId = CustomerPrecannedData.JUNIFER_CUSTOMER_ID.toString()
                coreId = CustomerPrecannedData.CORE_CUSTOMER_ID.toString()
                entityType = energy.so.ac.junifer.v1.generic.EntityIdentifier.CUSTOMER
            })
            then("mapping created") {
                inMemoryMapper.getCoreId(
                    EntityIdentifier.CUSTOMER,
                    CustomerPrecannedData.JUNIFER_CUSTOMER_ID.toString()
                ) shouldBe CustomerPrecannedData.CORE_CUSTOMER_ID.toString()
                inMemoryMapper.getJuniferId(
                    EntityIdentifier.CUSTOMER,
                    CustomerPrecannedData.CORE_CUSTOMER_ID.toString()
                ) shouldBe CustomerPrecannedData.JUNIFER_CUSTOMER_ID.toString()
            }
        }
    }
})
