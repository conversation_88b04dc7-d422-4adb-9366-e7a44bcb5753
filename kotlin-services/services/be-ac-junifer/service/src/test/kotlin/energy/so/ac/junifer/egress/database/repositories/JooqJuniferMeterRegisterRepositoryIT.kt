package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.config.BOOLEAN_FALSE
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.commons.model.tables.references.JUNIFER__METERREGISTERCONFIGPERIOD
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.OffsetDateTime
import org.jooq.DSLContext

class JooqJuniferMeterRegisterRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferMeterRegisterRepository(db)

    given("multiple register configs on the same dates with different versions") {

        val from = OffsetDateTime.now().minusYears(1)
        val to = OffsetDateTime.now().plusYears(1)

        insertRegisterConfig(db, 1, 1, from, to)
        insertRegisterConfig(db, 2, 2, from, to)
        insertRegisterConfig(db, 3, 3, from, to)
        insertRegisterConfig(db, 4, 4, from, to)

        `when`("the the register config is queried") {

            val returnedConfig = repo.getRegisterConfigPeriodForRegister(JUNIFER_REGISTER_ID)

            then("the config with the highest version should be returned") {
                returnedConfig.versionno shouldBe 4
            }
        }
    }
})

private fun insertRegisterConfig(
    db: DSLContext,
    id: Long,
    version: Int,
    fromdttm: OffsetDateTime,
    todttm: OffsetDateTime,
) {
    db.insertInto(JUNIFER__METERREGISTERCONFIGPERIOD).columns(
        JUNIFER__METERREGISTERCONFIGPERIOD.ID,
        JUNIFER__METERREGISTERCONFIGPERIOD.METERREGISTERFK,
        JUNIFER__METERREGISTERCONFIGPERIOD.FROMDTTM,
        JUNIFER__METERREGISTERCONFIGPERIOD.TODTTM,
        JUNIFER__METERREGISTERCONFIGPERIOD.DELETEFL,
        JUNIFER__METERREGISTERCONFIGPERIOD.VERSIONNO,
    ).values(id, JUNIFER_REGISTER_ID, fromdttm, todttm, BOOLEAN_FALSE, version).execute()
}
