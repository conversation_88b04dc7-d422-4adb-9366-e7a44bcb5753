package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.customers.JuniferCreateCustomerVulnerabilityRequest
import energy.so.ac.junifer.v1.customers.createCustomerVulnerabilityRequest
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toNullableTimestamp
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate

class JuniferCreateCustomerVulnerabilityRequestTest : BehaviorSpec({

    given("create customer vulnerability request") {
        val request = createCustomerVulnerabilityRequest {
            customerId = "1"
            id = 1
            propertyTblId = 1
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
        }

        `when`("map to junifer create customer vulnerability request") {
            val juniferRequest = JuniferCreateCustomerVulnerabilityRequest.fromCreateCustomerVulnerabilityRequest(
                request
            )

            then("return a corresponding JuniferCreateCustomerVulnerabilityRequest") {
                assertSoftly {
                    juniferRequest.id shouldBe request.id
                    juniferRequest.propertyTblId shouldBe request.propertyTblId
                    juniferRequest.fromDate shouldBe request.fromDt.getValueOrNull()?.toLocalDate()
                    juniferRequest.toDate shouldBe request.toDt.getValueOrNull()?.toLocalDate()
                }
            }
        }
    }
})
