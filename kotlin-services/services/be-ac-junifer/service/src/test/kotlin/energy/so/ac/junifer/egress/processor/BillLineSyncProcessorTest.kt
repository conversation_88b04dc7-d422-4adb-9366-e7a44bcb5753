package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferBillRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.CORE_BILL_ID
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.CORE_BILL_LINE_ID
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.JUNIFER_BILL_ID
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.JUNIFER_BILL_LINE_ID
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.billLineNoReferenceSyncEvent
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.billLineSyncEvent
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.billLineSyncResponse
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.createSyncBillLineEntityRequest
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.deleteSyncBillLineEntityRequest
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.juniferBillLine
import energy.so.ac.junifer.fixtures.BillLinesPrecannedData.updateSyncBillLineEntityRequest
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class BillLineSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockBillRepository = mockk<JuniferBillRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val billLineSyncProcessor = BillLinesSyncProcessor(mockMapper, mockBillRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockBillRepository, mockSyncClient)
        clearAllMocks()
    }

    given("event with null reference") {
        `when`("a bill_line event is generated") {
            then("throw Illegal state exception") {
                assertThrows<IllegalStateException> {
                    billLineSyncProcessor.process(billLineNoReferenceSyncEvent)
                }
            }
        }
    }

    given("no mapping exists for reference") {
        and("getBillLine throws entity not found") {
            coEvery { mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString()) } returns null
            coEvery { mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID) } throws EntityNotFoundException("")

            `when`("a bill_line event is generated") {
                billLineSyncProcessor.process(billLineSyncEvent)

                then("nothing happens") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString())
                            mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID)
                        }
                        coVerify(exactly = 0) {
                            mockSyncClient.syncBillLineEntity(any())
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }

        and("getBillLine returns corresponding junifer billBreakdownLine") {
            coEvery { mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString()) } returns null
            coEvery { mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID) } returns juniferBillLine
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL,
                    JUNIFER_BILL_ID.toString()
                )
            } returns CORE_BILL_ID.toString()
            coEvery { mockSyncClient.syncBillLineEntity(createSyncBillLineEntityRequest) } returns billLineSyncResponse
            coJustRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.BILL_LINE,
                    JUNIFER_BILL_LINE_ID.toString(),
                    CORE_BILL_LINE_ID.toString()
                )
            }

            `when`("a bill_line event is generated") {
                billLineSyncProcessor.process(billLineSyncEvent)

                then("call sync client to create bill line and create mapping") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString())
                            mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID)
                            mockMapper.getCoreId(EntityIdentifier.BILL, JUNIFER_BILL_ID.toString())
                            mockSyncClient.syncBillLineEntity(createSyncBillLineEntityRequest)
                            mockMapper.createCoreMapping(
                                EntityIdentifier.BILL_LINE,
                                JUNIFER_BILL_LINE_ID.toString(),
                                CORE_BILL_LINE_ID.toString()
                            )
                        }
                    }
                }
            }
        }
    }

    given("mapping exists for reference") {
        and("getBillLine throws entity not found") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL_LINE,
                    JUNIFER_BILL_LINE_ID.toString()
                )
            } returns CORE_BILL_LINE_ID.toString()
            coEvery { mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID) } throws EntityNotFoundException("")
            coEvery { mockSyncClient.syncBillLineEntity(deleteSyncBillLineEntityRequest) } returns billLineSyncResponse

            `when`("a bill_line event is generated") {
                billLineSyncProcessor.process(billLineSyncEvent)

                then("call sync client to delete bill line") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString())
                            mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID)
                            mockSyncClient.syncBillLineEntity(deleteSyncBillLineEntityRequest)
                        }
                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }

        and("getBillLine returns corresponding junifer billBreakdownLine") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL_LINE,
                    JUNIFER_BILL_LINE_ID.toString()
                )
            } returns CORE_BILL_LINE_ID.toString()
            coEvery { mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID) } returns juniferBillLine
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL,
                    JUNIFER_BILL_ID.toString()
                )
            } returns CORE_BILL_ID.toString()
            coEvery { mockSyncClient.syncBillLineEntity(updateSyncBillLineEntityRequest) } returns billLineSyncResponse

            `when`("a bill_line event is generated") {
                billLineSyncProcessor.process(billLineSyncEvent)

                then("call sync client to update bill line, no new mapping created") {
                    assertSoftly {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString())
                            mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID)
                            mockMapper.getCoreId(EntityIdentifier.BILL, JUNIFER_BILL_ID.toString())
                            mockSyncClient.syncBillLineEntity(updateSyncBillLineEntityRequest)
                        }
                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }

        and("no bill mapping found") {
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL_LINE,
                    JUNIFER_BILL_LINE_ID.toString()
                )
            } returns CORE_BILL_LINE_ID.toString()
            coEvery { mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID) } returns juniferBillLine
            coEvery {
                mockMapper.getCoreId(
                    EntityIdentifier.BILL,
                    JUNIFER_BILL_ID.toString()
                )
            } returns null

            `when`("a bill_line event is generated") {
                then("SyncDelayedException, no new mapping created") {
                    assertSoftly {
                        assertThrows<SyncDelayedException> { billLineSyncProcessor.process(billLineSyncEvent) }
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.BILL_LINE, JUNIFER_BILL_LINE_ID.toString())
                            mockBillRepository.getBillLine(JUNIFER_BILL_LINE_ID)
                            mockMapper.getCoreId(EntityIdentifier.BILL, JUNIFER_BILL_ID.toString())
                            mockMapper.getCoreId(
                                EntityIdentifier.BILL,
                                JUNIFER_BILL_ID.toString()
                            )
                        }
                        coVerify(exactly = 0) {
                            mockSyncClient.syncBillLineEntity(any())
                            mockMapper.createCoreMapping(any(), any(), any())
                        }
                    }
                }
            }
        }
    }
})
