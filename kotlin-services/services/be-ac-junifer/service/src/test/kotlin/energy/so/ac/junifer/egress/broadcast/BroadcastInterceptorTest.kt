package energy.so.ac.junifer.egress.broadcast

import energy.so.ac.junifer.egress.broadcast.BroadcastInterceptor.BroadcastAwareListener
import energy.so.ac.junifer.egress.broadcast.BroadcastInterceptor.BroadcastedClientCallListener
import energy.so.commons.session.SessionManager
import energy.so.commons.session.Tracer.BRANCH_ID_KEY
import io.grpc.CallOptions
import io.grpc.Channel
import io.grpc.ClientCall
import io.grpc.Metadata
import io.grpc.Metadata.ASCII_STRING_MARSHALLER
import io.grpc.MethodDescriptor
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class BroadcastInterceptorTest {

    private val serviceVariantsResolver = mockk<ServiceVariantsResolver>()
    private val next = mockk<Channel>(relaxed = true)
    private val method = mockk<MethodDescriptor<String, String>>()
    private val callOptions = mockk<CallOptions>()
    private val listener = mockk<ClientCall.Listener<String>>()

    private val interceptor = BroadcastInterceptor(serviceVariantsResolver)

    @BeforeEach
    fun setup() {
        mockkObject(SessionManager)
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `should forward call to service variant`() {
        // GIVEN
        val grpcMessage = "a message"
        val metadata = Metadata().apply {
            put(Metadata.Key.of("someHeader", ASCII_STRING_MARSHALLER), "someValue")
        }

        val mainCall = mockk<ClientCall<String, String>>(relaxUnitFun = true)
        val secondCall = mockk<ClientCall<String, String>>(relaxUnitFun = true)
        every { next.newCall<String, String>(any(), any()) } returns mainCall andThen secondCall
        every { next.authority() } returns "be-tickets.staging.svc.local:50051"

        val serviceVariant = "so-1234"
        every {
            serviceVariantsResolver.findAllVariants("be-tickets.staging.svc.local:50051")
        } returns setOf(serviceVariant)

        // WHEN
        val clientCall = interceptor.interceptCall(method = method, callOptions = callOptions, next = next)

        // THEN
        verify(exactly = 2) {
            next.newCall(method, callOptions)
        }

        // WHEN
        clientCall.start(listener, metadata)

        // THEN
        verify { mainCall.start(match { it is BroadcastAwareListener }, metadata) }
        verify {
            secondCall.start(
                match { it is BroadcastedClientCallListener },
                match {
                    it.keys().size == 2 && it.get(
                        Metadata.Key.of(
                            BRANCH_ID_KEY,
                            ASCII_STRING_MARSHALLER
                        )
                    ) == serviceVariant
                }
            )
        }

        // WHEN
        clientCall.sendMessage(grpcMessage)

        // THEN
        verify { mainCall.sendMessage(grpcMessage) }
        verify { secondCall.sendMessage(grpcMessage) }

        // WHEN
        clientCall.request(2)

        // THEN
        verify { mainCall.request(2) }
        verify { secondCall.request(2) }
    }
}
