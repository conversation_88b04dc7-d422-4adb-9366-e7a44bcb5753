package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferCancelAccountRegistrationResponse
import energy.so.ac.junifer.ingress.models.accounts.Link
import energy.so.ac.junifer.ingress.models.accounts.MeterPoint
import energy.so.ac.junifer.ingress.models.accounts.SuccessfulCancellation
import energy.so.ac.junifer.ingress.models.accounts.UnsuccessfulCancellation
import energy.so.ac.junifer.ingress.models.accounts.toResponse
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCancelAccountRegistrationResponseTest : BehaviorSpec({

    given("junifer cancel account registration response") {
        val meterPointId = 1L
        val successfulCancellation = SuccessfulCancellation(listOf(MeterPoint(meterPointId, Link("self"))))
        val unsuccessfulCancellation =
            UnsuccessfulCancellation("reason", listOf(MeterPoint(meterPointId, Link("self"))))
        val juniferResponse = JuniferCancelAccountRegistrationResponse(
            listOf(unsuccessfulCancellation),
            listOf(successfulCancellation),
            listOf(unsuccessfulCancellation)
        )

        `when`("map to cancel account registration") {
            val response = juniferResponse.toResponse()

            then("return a corresponding CancelAccountRegistration") {
                assertSoftly {
                    response.successfulCancellationsCount shouldBe 1
                    response.unsuccessfulCancellationsCount shouldBe 1
                    response.skippedCancellationsCount shouldBe 1
                    response.successfulCancellationsList[0].meterPointsCount shouldBe 1
                    response.unsuccessfulCancellationsList[0].meterPointsCount shouldBe 1
                    response.unsuccessfulCancellationsList[0].meterPointsCount shouldBe 1
                    response.successfulCancellationsList[0].meterPointsList[0].id shouldBe meterPointId
                    response.successfulCancellationsList[0].meterPointsList[0].links.self shouldBe "self"
                    response.skippedCancellationsList[0].meterPointsCount shouldBe 1
                    response.skippedCancellationsList[0].meterPointsCount shouldBe 1
                }
            }
        }
    }
})
