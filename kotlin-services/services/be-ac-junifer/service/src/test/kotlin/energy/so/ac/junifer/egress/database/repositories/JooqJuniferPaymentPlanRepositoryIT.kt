package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlan
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTPLAN
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferPaymentPlanRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferPaymentPlanRepository(db)

    given("PaymentPlan exists in DB") {
        insertPaymentPlan(db)

        `when`("PaymentPlan is queried") {
            val paymentPlan = repo.getPaymentPlan(JUNIFER_PAYMENT_PLAN_ID)

            then("PaymentPlan is returned") {
                with(paymentPlan) {
                    this shouldNotBe null
                    id shouldBe testJuniferPaymentPlan.id
                    accountfk shouldBe testJuniferPaymentPlan.accountfk
                    currencyfk shouldBe testJuniferPaymentPlan.currencyfk
                    status shouldBe testJuniferPaymentPlan.status
                    collecttype shouldBe testJuniferPaymentPlan.collecttype
                    paymentmethodfk shouldBe testJuniferPaymentPlan.paymentmethodfk
                    createdusertblfk shouldBe testJuniferPaymentPlan.createdusertblfk
                    createddttm shouldBe testJuniferPaymentPlan.createddttm
                    reference shouldBe testJuniferPaymentPlan.reference
                    deletefl shouldBe testJuniferPaymentPlan.deletefl
                    versionno shouldBe testJuniferPaymentPlan.versionno
                    partitionid shouldBe testJuniferPaymentPlan.partitionid
                }
            }
        }
    }


})

fun insertPaymentPlan(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__PAYMENTPLAN, testJuniferPaymentPlan))
}