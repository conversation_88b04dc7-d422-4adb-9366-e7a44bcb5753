package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.assets.JuniferCancelMeterPointRegistrationRequest
import energy.so.ac.junifer.v1.assets.cancelMeterPointRegistrationRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCancelMeterPointRegistrationRequestTest : BehaviorSpec({

    given("cancel meter point registration request") {
        val request = cancelMeterPointRegistrationRequest {
            reason = "reason"
            communicationFl = false
        }

        `when`("map to junifer cancel meter point registration") {
            val juniferRequest = JuniferCancelMeterPointRegistrationRequest.fromCancelMeterPointRegistrationRequest(
                request
            )

            then("return a corresponding JuniferCancelMeterPointRegistrationRequest") {
                assertSoftly {
                    juniferRequest.reason shouldBe request.reason
                    juniferRequest.communicationFl shouldBe request.communicationFl
                }
            }
        }
    }
})
