package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.JUNIFER_METERPOINT_ID
import energy.so.ac.junifer.fixtures.intervals
import energy.so.ac.junifer.fixtures.juniferAgreementId1StartDateTime
import energy.so.ac.junifer.fixtures.juniferAgreementId3StartDatetime
import energy.so.ac.junifer.fixtures.setupDb
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqMeterPointTprIntervalRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repository = JooqMeterPointTprIntervalRepository(db)

    given("::getMeterPointTprIntervals") {
        setupDb(db)

        `when`("query is run") {
            val results = repository.getMeterPointTprIntervals(
                JUNIFER_METERPOINT_ID,
                juniferAgreementId1StartDateTime,
                juniferAgreementId3StartDatetime
            )
            then("results are as expected") {
                results shouldBe intervals
            }
        }
    }
})
