package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.exceptions.SyncFixedDateDelayException
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_MPAN_CONFIG_PERIOD_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_MPAN_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_UK_DISTRIBUTION_AREA_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.METER_POINT_ELECTRICITY_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.createMeterPointElectricitySync
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.juniferMarketParticipant
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.juniferUkMeasurementClass
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.juniferUkMeterTimeSwitchClass
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.juniferUkStdSettlementConfig
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.juniferUklinelossfactorclass
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.meterPointElectricitySyncResponse
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.mpanConfigPeriodSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.mpanSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.patchMeterPointElectricitySync
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.patchMeterPointElectricitySyncForMpanConfigPeriod
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.patchMeterPointElectricitySyncWithUnknownMeterType
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.testJuniferMpan
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.testJuniferMpanConfigPeriod
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.testJuniferUkDistributionArea
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.testJuniferMeter
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.testJuniferMeterType
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_TYPE_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT_ELECTRICITY
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.enums.EventType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

class MeterPointElectricityElectricitySyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val meterPointElectricitySyncProcessor =
        MeterPointElectricitySyncProcessor(mockMapper, mockMeterPointRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockMeterPointRepository, mockSyncClient)
    }

    given("Invalid sync event type") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointElectricitySyncProcessor.process(
                        mpanSyncEvent.copy(eventType = EventType.ACCOUNT_CONTACT)
                    )
                }
            }
        }
    }

    given("Missing reference") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterPointElectricitySyncProcessor.process(mpanSyncEvent.copy(reference = null))
                }
            }
        }
    }

    given("no existing mapped meter point electricity and an existing junifer meter point electricity") {

        every { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) } returns null
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) } returns testJuniferUkDistributionArea
        every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
        every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
        every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
        every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
        every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
        every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
        every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

        `when`("an Mpan event is generated") {

            coEvery { mockSyncClient.syncMeterPointElectricityEntity(createMeterPointElectricitySync) } returns meterPointElectricitySyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT_ELECTRICITY,
                    JUNIFER_MPAN_ID.toString(),
                    METER_POINT_ELECTRICITY_ID.toString()
                )
            }

            meterPointElectricitySyncProcessor.process(mpanSyncEvent)

            then("a new meter point electricity should be created") {
                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }

                verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                verify { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT_ELECTRICITY,
                        JUNIFER_MPAN_ID.toString(),
                        METER_POINT_ELECTRICITY_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncMeterPointElectricityEntity(createMeterPointElectricitySync) }
            }
        }
    }

    given("an existing mapped meter point electricity and an existing junifer meter point electricity") {

        every {
            mockMapper.getCoreId(
                METER_POINT_ELECTRICITY,
                JUNIFER_MPAN_ID.toString()
            )
        } returns METER_POINT_ELECTRICITY_ID.toString()
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) } returns testJuniferUkDistributionArea
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
        every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
        every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
        every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
        every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
        every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
        every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

        `when`("an Mpan event is generated") {

            coEvery { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySync) } returns meterPointElectricitySyncResponse

            meterPointElectricitySyncProcessor.process(mpanSyncEvent)

            then("a new meter point electricity should be patched") {
                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                verify { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) }
                coVerify { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySync) }
            }
        }

        `when`("an MpanConfigPeriod event is generated") {
            every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod

            every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
            every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
            every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
            every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
            every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
            every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
            every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

            coEvery { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySyncForMpanConfigPeriod) } returns meterPointElectricitySyncResponse


            meterPointElectricitySyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("a new meter point electricity should be created") {
                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) }

                coVerify {
                    mockSyncClient.syncMeterPointElectricityEntity(
                        patchMeterPointElectricitySyncForMpanConfigPeriod
                    )
                }
            }
        }

        `when`("an MpanConfigPeriod delete event is generated") {
            every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod.copy(
                deletefl = BOOLEAN_TRUE
            )
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod.copy(
                deletefl = BOOLEAN_TRUE
            )

            every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
            every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
            every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
            every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
            every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
            every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
            every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

            coEvery { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySync) } returns meterPointElectricitySyncResponse


            meterPointElectricitySyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("a new meter point electricity should be deleted") {
                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) }

                coVerify { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySync) }
            }
        }

        `when`("an MpanConfigPeriod event is generated which is not the current one") {
            and("MpanConfigPeriod event is in the past") {
                every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod
                every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod.copy(
                    id = 2342343
                )

                every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
                every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
                every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
                every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
                every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
                every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
                every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

                coEvery {
                    mockSyncClient.syncMeterPointElectricityEntity(
                        patchMeterPointElectricitySyncForMpanConfigPeriod
                    )
                } returns meterPointElectricitySyncResponse


                meterPointElectricitySyncProcessor.process(mpanConfigPeriodSyncEvent)

                then("a new meter point electricity should be created") {
//                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
//                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                    verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                    verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                    verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                    verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                    verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                    verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                    verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                    verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) }

                    coVerify {
                        mockSyncClient.syncMeterPointElectricityEntity(
                            patchMeterPointElectricitySyncForMpanConfigPeriod
                        )
                    }
                }
            }
            and("MpanConfigPeriod event is in the future") {
                every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod.copy(
                    fromdt = LocalDate.now().plusDays(14)
                )
                every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod.copy(
                    id = 2342343,
                )

                every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
                every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
                every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
                every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
                every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
                every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter
                every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

                coEvery {
                    mockSyncClient.syncMeterPointElectricityEntity(
                        patchMeterPointElectricitySyncForMpanConfigPeriod
                    )
                } returns meterPointElectricitySyncResponse

                then("should throw SyncFixedDateDelayException") {

                    shouldThrow<SyncFixedDateDelayException> {
                        meterPointElectricitySyncProcessor.process(
                            mpanConfigPeriodSyncEvent
                        )
                    }
                    verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                }
            }
        }
    }

    given("an MpanConfigPeriod event for an non existent MpanConfigPeriod in db") {
        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } throws EntityNotFoundException(
            ""
        )

        `when`("MpanConfigPeriod event is generated") {
            meterPointElectricitySyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MpanConfigPeriod event for an non current MpanConfigPeriod in db") {
        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns null

        `when`("MpanConfigPeriod event is generated") {
            meterPointElectricitySyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an existing mapped meter point electricity and an existing junifer meter point electricity with null meterType") {
        every {
            mockMapper.getCoreId(
                METER_POINT_ELECTRICITY,
                JUNIFER_MPAN_ID.toString()
            )
        } returns METER_POINT_ELECTRICITY_ID.toString()
        every { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) } returns METER_POINT_ID.toString()
        every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) } returns testJuniferUkDistributionArea
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) } returns juniferUklinelossfactorclass
        every { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) } returns juniferUkMeasurementClass
        every { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) } returns testJuniferMeter.copy(
            metertypefk = null
        )
        every { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) } returns juniferUkMeterTimeSwitchClass
        every { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) } returns juniferMarketParticipant
        every { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) } returns juniferUkStdSettlementConfig
        every { mockMeterPointRepository.getMeterType(JUNIFER_METER_TYPE_ID) } returns testJuniferMeterType

        `when`("an Mpan event is generated") {

            coEvery { mockSyncClient.syncMeterPointElectricityEntity(patchMeterPointElectricitySyncWithUnknownMeterType) } returns meterPointElectricitySyncResponse

            meterPointElectricitySyncProcessor.process(mpanSyncEvent)

            then("a new meter point electricity should be patched") {
                verify { mockMapper.getCoreId(METER_POINT_ELECTRICITY, JUNIFER_MPAN_ID.toString()) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkDistributionArea(JUNIFER_UK_DISTRIBUTION_AREA_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkLineLossFactorClass(MeterPointElectricityPrecannedData.UK_LINE_LOSS_FACTOR_CLASS_FK) }
                verify { mockMeterPointRepository.getUkMeasurementClass(MeterPointElectricityPrecannedData.UK_MEASUREMENT_CLASS_FK) }
                verify { mockMeterPointRepository.getMeterByMeterPointId(MeterPointGasPrecannedData.JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getUkMeterTimeSwitchClass(MeterPointElectricityPrecannedData.UK_METER_TIME_SWITCH_CLASS_FK) }
                verify { mockMeterPointRepository.getMarketParticipant(MeterPointElectricityPrecannedData.MOP_MARKET_PARTICIPANT_FK) }
                verify { mockMeterPointRepository.getUkStdSettlementConfig(MeterPointElectricityPrecannedData.UK_STD_SETTLEMENT_CONFIG_FK) }
                coVerify {
                    mockSyncClient.syncMeterPointElectricityEntity(
                        patchMeterPointElectricitySyncWithUnknownMeterType
                    )
                }
            }
        }
    }
})
