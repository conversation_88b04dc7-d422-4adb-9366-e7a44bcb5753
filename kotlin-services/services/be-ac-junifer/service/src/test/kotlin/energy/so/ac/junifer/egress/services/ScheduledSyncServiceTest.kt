package energy.so.ac.junifer.egress.services

import energy.so.ac.junifer.config.SyncConfig
import energy.so.ac.junifer.egress.database.repositories.EventTypeStatusRepository
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import energy.so.ac.junifer.egress.database.repositories.exponentialBackoffMultiplierInSeconds
import energy.so.ac.junifer.egress.database.repositories.maxEvents
import energy.so.ac.junifer.egress.database.repositories.maxLockTimeInSeconds
import energy.so.ac.junifer.egress.database.repositories.maxRetryCount
import energy.so.ac.junifer.egress.database.repositories.pid
import energy.so.ac.junifer.egress.processor.SyncProcessor
import energy.so.ac.junifer.egress.processor.SyncProcessorFactory
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.EventTypeStatu
import energy.so.commons.model.tables.pojos.SyncEvent
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.assertEquals

class ScheduledSyncServiceTest : ShouldSpec({

    val mockSyncEventRepository = mockk<SyncEventRepository>()
    val mockEventTypeStatusRepository = mockk<EventTypeStatusRepository>()
    val mockProcessor = mockk<SyncProcessor>()
    val mockFeatureService = mockk<FeatureService>()

    SyncProcessorFactory.apply {
        processorMap = mapOf(EventType.METER to listOf(mockProcessor))
    }

    val syncConfig = SyncConfig(
        pid = pid,
        maxEvents = maxEvents,
        maxLockTimeInSeconds = maxLockTimeInSeconds,
        exponentialBackoffMultiplierInSeconds = exponentialBackoffMultiplierInSeconds,
        maxRetryCount = maxRetryCount
    )

    val sut =
        ScheduledSyncService(mockSyncEventRepository, mockEventTypeStatusRepository, syncConfig, mockFeatureService)

    afterTest {
        confirmVerified(mockSyncEventRepository, mockProcessor)
        clearMocks(mockSyncEventRepository, mockProcessor)
    }

    context("::performSync") {
        should("given a new event when a sync is performed then the event processor should be called") {
            // GIVEN
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns false

            val syncEvent1 = SyncEvent(id = 1, eventType = EventType.METER, retryCount = 1)
            val syncEvent2 = SyncEvent(id = 2, eventType = EventType.METER, retryCount = 1)
            val syncEvent3 = SyncEvent(id = 3, eventType = EventType.METER, retryCount = 1)

            every { mockEventTypeStatusRepository.getEnabledEvents() } returns listOf(EventTypeStatu(EventType.METER))

            every { mockSyncEventRepository.getSyncEvents(EventType.METER) } returns listOf(
                syncEvent1,
                syncEvent2,
                syncEvent3
            )
            justRun { mockSyncEventRepository.archiveSyncEvent(any()) }
            coJustRun { mockProcessor.process(any()) }

            // WHEN
            sut.performSync()

            // THEN
            verify { mockSyncEventRepository.getSyncEvents(EventType.METER) }

            verify(exactly = 3) {
                mockSyncEventRepository.archiveSyncEvent(withArg {
                    it.eventType shouldBe EventType.METER
                    it.retryCount shouldBe 1
                })
            }

            coVerify { mockProcessor.process(syncEvent1) }
            coVerify { mockProcessor.process(syncEvent2) }
            coVerify { mockProcessor.process(syncEvent3) }
        }

        should("given an unrecognised event type when a sync is performed then log an exception") {
            // GIVEN
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns false

            val eventWithNoProcessor = SyncEvent(id = 1, eventType = EventType.MPAN, retryCount = 1) //  no processor

            every { mockEventTypeStatusRepository.getEnabledEvents() } returns listOf(EventTypeStatu(EventType.METER))

            every { mockSyncEventRepository.getSyncEvents(EventType.METER) } returns listOf(
                eventWithNoProcessor
            )
            justRun { mockSyncEventRepository.update(any()) }

            // WHEN
            sut.performSync()

            // THEN
            verify { mockSyncEventRepository.getSyncEvents(EventType.METER) }

            verify {
                mockSyncEventRepository.update(withArg {
                    it.id shouldBe eventWithNoProcessor.id
                    1 shouldBe eventWithNoProcessor.retryCount
                })
            }
        }

        should("given pause on error enabled and no event types are enabled when a sync is performed then No event processors should be called") {
            // GIVEN
            val syncEvent1 = SyncEvent(id = 1, eventType = EventType.METER, retryCount = 1)
            val syncEvent2 = SyncEvent(id = 2, eventType = EventType.METER, retryCount = 1)
            val syncEvent3 = SyncEvent(id = 3, eventType = EventType.METER, retryCount = 1)

            every { mockEventTypeStatusRepository.getEnabledEvents() } returns emptyList()
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns true

            // WHEN
            sut.performSync()

            // THEN
            verify { mockEventTypeStatusRepository.getEnabledEvents() }
            verify(exactly = 0) {
                mockSyncEventRepository.archiveSyncEvent(withArg {
                    it.eventType shouldBe EventType.METER
                    it.retryCount shouldBe 1
                })
            }

            coVerify(exactly = 0) { mockProcessor.process(syncEvent1) }
            coVerify(exactly = 0) { mockProcessor.process(syncEvent2) }
            coVerify(exactly = 0) { mockProcessor.process(syncEvent3) }
        }

        should("given pause on error enabled when a sync is failed with error then a new record in event type status table should be created for that event") {
            // GIVEN
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns false
            val syncEvent = SyncEvent(id = 1, eventType = EventType.METER, retryCount = 1)

            every { mockSyncEventRepository.getSyncEvents(EventType.METER) } returns listOf(syncEvent)
            every { mockEventTypeStatusRepository.getEnabledEvents() } returns listOf(EventTypeStatu(EventType.METER))
            every { mockEventTypeStatusRepository.save(any()) } returns EventTypeStatu()
            justRun { mockSyncEventRepository.update(any()) }
            coEvery { mockProcessor.process(any()) } throws Exception("Test Exception")
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns true

            // WHEN
            sut.performSync()

            // THEN
            verify { mockSyncEventRepository.getSyncEvents(EventType.METER) }
            verify { mockEventTypeStatusRepository.getEnabledEvents() }
            verify {
                mockEventTypeStatusRepository.save(withArg {
                    it.eventType shouldBe EventType.METER
                    it.enabled shouldBe false
                    it.updatedDttm shouldNotBe null
                })
            }
            verify {
                mockSyncEventRepository.update(withArg {
                    assertEquals(EventType.METER, it.eventType)
                    assertEquals(1, it.retryCount)
                })
            }
            coVerify { mockProcessor.process(any()) }
        }

        should("given a new event when a sync is performed then the event processor should be called") {
            // GIVEN
            every { mockEventTypeStatusRepository.shouldPauseOnError(any()) } returns false

            val syncEvent1 = SyncEvent(id = 1, eventType = EventType.METER, retryCount = 1)
            val syncEvent2 = SyncEvent(id = 2, eventType = EventType.METER, retryCount = 1)
            val syncEvent3 = SyncEvent(id = 3, eventType = EventType.METER, retryCount = 1)

            every { mockEventTypeStatusRepository.getEnabledEvents() } returns listOf(EventTypeStatu(EventType.METER))

            every { mockSyncEventRepository.getSyncEvents(EventType.METER) } returns listOf(
                syncEvent1,
                syncEvent2,
                syncEvent3
            )
            justRun { mockSyncEventRepository.archiveSyncEvent(any()) }
            coJustRun { mockProcessor.process(any()) }

            // WHEN
            sut.performSync()

            // THEN
            verify { mockSyncEventRepository.getSyncEvents(EventType.METER) }

            verify(exactly = 3) {
                mockSyncEventRepository.archiveSyncEvent(withArg {
                    it.eventType shouldBe EventType.METER
                    it.retryCount shouldBe 1
                })
            }

            coVerify { mockProcessor.process(syncEvent1) }
            coVerify { mockProcessor.process(syncEvent2) }
            coVerify { mockProcessor.process(syncEvent3) }
        }
    }
}) {
    override fun isolationMode() = IsolationMode.InstancePerTest
}
