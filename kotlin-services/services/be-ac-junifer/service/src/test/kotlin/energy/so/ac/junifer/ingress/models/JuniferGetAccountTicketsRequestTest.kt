package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.getTicketsForAccountNumberRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferGetAccountTicketsRequest
import energy.so.ac.junifer.ingress.models.accounts.JuniferGetAccountTicketsRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferGetAccountTicketsRequestTest : BehaviorSpec({

    given("get account's tickets request") {
        val request = getTicketsForAccountNumberRequest

        `when`("map to junifer get account tickets request") {
            val juniferRequest = JuniferGetAccountTicketsRequest.fromProtoRequest(request)

            then("return a corresponding JuniferCreateAccountTicketRequest") {
                juniferRequest shouldBe juniferGetAccountTicketsRequest
            }
        }
    }
})