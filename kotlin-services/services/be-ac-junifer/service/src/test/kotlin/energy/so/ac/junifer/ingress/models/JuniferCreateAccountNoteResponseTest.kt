package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountNoteResponse
import energy.so.ac.junifer.ingress.models.accounts.NoteType
import energy.so.ac.junifer.ingress.models.accounts.toResponse
import energy.so.commons.grpc.utils.toTimestamp
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDateTime
import java.util.Locale

class JuniferCreateAccountNoteResponseTest : BehaviorSpec({

    given("junifer create account note response") {
        val juniferResponse = JuniferCreateAccountNoteResponse(
            id = 1L,
            createdDttm = LocalDateTime.now(),
            subject = "subject",
            type = NoteType.NOTE,
            summary = "summary",
            content = "content",
        )

        `when`("map to create account note") {
            val response = juniferResponse.toResponse()

            then("return a corresponding CreateAccountNote") {
                assertSoftly {
                    response.id shouldBe juniferResponse.id
                    response.createdDttm shouldBe juniferResponse.createdDttm.toTimestamp()
                    response.subject shouldBe juniferResponse.subject
                    response.type.name.lowercase(Locale.getDefault()) shouldBe juniferResponse.type.name.lowercase(
                        Locale.getDefault()
                    )
                    response.summary shouldBe juniferResponse.summary
                    response.content.value shouldBe juniferResponse.content
                }
            }
        }
    }
})
