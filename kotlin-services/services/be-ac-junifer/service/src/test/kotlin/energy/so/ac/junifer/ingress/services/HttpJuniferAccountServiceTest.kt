package energy.so.ac.junifer.ingress.services

import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.BILLING_ACCOUNT_NUMBER
import energy.so.ac.junifer.fixtures.AccountPrecannedData
import energy.so.ac.junifer.fixtures.AccountPrecannedData.FREQUENCY
import energy.so.ac.junifer.fixtures.AccountPrecannedData.FREQUENCY_MULTIPLE
import energy.so.ac.junifer.fixtures.AccountPrecannedData.SUGGESTED_PAYMENT_AMOUNT
import energy.so.ac.junifer.fixtures.AccountPrecannedData.applyExitFeesRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.cancelAccountReviewPeriodRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountRepaymentRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountReviewPeriodRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountTicketRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.getTicketsByJuniferAccountNumberRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.getTicketsForAccountNumberRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountRepaymentMockResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountRepaymentResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountReviewPeriodMockResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountTicketMockResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountTicketResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferGetAccountTicketsMockResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.updateAccountContactRequest
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.paymentMethodResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import energy.so.ac.junifer.ingress.models.accounts.Status
import energy.so.ac.junifer.v1.accounts.CancelAccountRegistrationRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.accountSuggestedPaymentAmountRequest
import energy.so.ac.junifer.v1.accounts.cancelAccountRegistrationRequest
import energy.so.ac.junifer.v1.accounts.createAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.meterPointChangeOfModeRequest
import energy.so.ac.junifer.v1.accounts.renewAccountRequest
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.JuniferGrpcException
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableInt32
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import java.time.LocalDateTime
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.assertThrows

class HttpJuniferAccountServiceTest : BehaviorSpec({

    val sut = HttpJuniferAccountService(MockJuniferClient.juniferConfig, getMockHttpClient())

    given("create account credit request") {
        val accountId = "1"
        val request: CreateAccountCreditRequest = createAccountCreditRequest {
            grossAmountInPounds = 100.0
            salesTaxName = "tax name"
            accountCreditReasonName = "reason"
            reference = "ref"
            description = "description"
        }

        `when`("create account credit") {
            sut.createAccountCredit(accountId, request)

            then("credit account created")
        }
    }

    given("get account credits") {
        val accountId = "1"

        `when`("get account credits") {
            sut.getAccountCredits(accountId)

            then("account credits requested")
        }
    }

    given("cancel account credit ") {
        val accountCreditId = "1"

        `when`("create account credit") {
            sut.cancelAccountCredit(accountCreditId)

            then("account credit deleted")
        }
    }


    given("cancel account registration request") {
        val accountId = "1"
        val request: CancelAccountRegistrationRequest = cancelAccountRegistrationRequest {
            reason = "reason"
            communicationFl = false
        }

        `when`("cancel account registration") {
            sut.cancelAccountRegistration(accountId, request)

            then("account registration cancelled")
        }
    }

    given("create account note request") {
        val juniferAccountId = "100"
        val request: CreateAccountNoteRequest = createAccountNoteRequest {
            accountId = juniferAccountId
            subject = "subject"
            type = NoteType.Note
            summary = "summary"
            content = "content"
        }

        `when`("create account note") {
            sut.createAccountNote(juniferAccountId, request)

            then("note account created")
        }
    }

    given("create account ticket request") {
        val juniferAccountId = "100"
        val request = createAccountTicketRequest

        `when`("create account ticket") {
            sut.createAccountTicket(juniferAccountId, request)

            then("account ticket created")
        }
    }

    given("create account ticket for account number request") {
        val juniferAccountNumber = "0123493"
        val request = createAccountTicketRequest

        `when`("create account ticket for account number") {
            val response = sut.createAccountTicketForAccountNumber(juniferAccountNumber, request)

            then("account ticket created") {
                response.id shouldBe juniferCreateAccountTicketResponse.id
                response.ticketDefinitionCode shouldBe juniferCreateAccountTicketResponse.ticketDefinitionCode
                response.ticketDefinitionName shouldBe juniferCreateAccountTicketResponse.ticketDefinitionName
                response.ticketPriority shouldBe juniferCreateAccountTicketResponse.ticketPriority
            }
        }
    }

    given("create account repayment request") {
        val juniferAccountId = "100"
        val request = createAccountRepaymentRequest

        `when`("create account repayment") {
            val result = sut.createAccountRepayment(juniferAccountId, request)

            then("account repayment created") {
                result shouldBe juniferCreateAccountRepaymentResponse
            }
        }
    }

    given("update account contact request") {
        val juniferAccountId = "100"
        val juniferContactId = "200"
        val request = updateAccountContactRequest

        `when`("update account contact") {
            sut.updateAccountContact(juniferAccountId, juniferContactId, request)

            then("account contact updated")
        }
    }

    given("create account review period request") {
        val juniferAccountId = 100L
        val request = createAccountReviewPeriodRequest

        `when`("create account review period for account id") {
            sut.createAccountReviewPeriod(juniferAccountId, request)

            then("account review period created")
        }
    }

    given("cancel account review period request") {
        val juniferAccountId = 100L
        val request = cancelAccountReviewPeriodRequest

        `when`("create account review period for account id") {
            sut.cancelAccountReviewPeriod(juniferAccountId, request.reviewPeriodJuniferId)

            then("account review period cancelled")
        }
    }

    given("junifer account number") {
        val juniferAccountNumber = "3045566"

        `when`("create get account by junifer number") {
            val juniferResponse = sut.getAccountByNumber(juniferAccountNumber)

            then("account is returned") {
                juniferResponse.id shouldBe 12L
            }
        }
    }

    given("customer id") {
        val customerId = "1"

        `when`("get accounts for customer id") {
            val juniferResponse = sut.getAccountsForCustomerId(customerId)

            then("account is returned") {
                juniferResponse.results!![0].id shouldBe 12L
            }
        }
    }

    given("junifer account id") {
        val juniferAccountId = "100"

        `when`("get payment schedule periods for account id") {
            val paymentSchedulePeriods = sut.getPaymentSchedulePeriods(juniferAccountId)

            then("payment schedule periods are returned") {
                paymentSchedulePeriods shouldNotBe null
                paymentSchedulePeriods.results.size shouldBe 1
            }
        }

        `when`("get payment methods for account id") {
            val paymentMethods = sut.getPaymentMethods(juniferAccountId)

            then("payment methods are returned") {
                paymentMethods shouldNotBe null
                paymentMethods.results.size shouldBe 1
            }
        }
    }

    given("renew account request and junifer account id") {
        val juniferAccountId = "100"
        val request = renewAccountRequest {
            electricityProductCode = "123"
            electricitySupplyProductSubType = "type"
            electricityStartDate = "2023-05-12"
        }

        `when`("renew account") {
            val renewAccount = sut.renewAccount(juniferAccountId, request)

            then("account is renewed") {
                renewAccount shouldNotBe null
                renewAccount.results?.size shouldBe 1
                renewAccount.results!![0].accountNumber shouldBe "123"
                renewAccount.results!![0].elecProductBundleId shouldBe 106628
            }
        }
    }

    given("account's suggested payment amount request") {
        val juniferAccountId = "100"
        val request = accountSuggestedPaymentAmountRequest {
            frequency = FREQUENCY.toNullableString()
            frequencyMultiple = FREQUENCY_MULTIPLE.toNullableInt32()
            ignoreWarnings = true.toNullableBoolean()
        }

        `when`("get account's suggested payment amount") {
            val response = sut.getAccountSuggestedPaymentAmount(juniferAccountId, request)

            then("account's suggested payment amount is retrieved") {
                response.frequency shouldBe FREQUENCY
                response.frequencyMultiple shouldBe FREQUENCY_MULTIPLE
                response.suggestedPaymentAmount shouldBe SUGGESTED_PAYMENT_AMOUNT.toDouble()
            }
        }
    }

    given("a junifer account") {
        `when`("the account is retrieved by its ID") {
            val response = sut.getAccount(JUNIFER_ACCOUNT_ID)
            then("return the account") {
                response.id shouldBe JUNIFER_ACCOUNT_ID
            }
        }
    }

    given("apply change of tenancy exit fees request") {
        val juniferAccountId = "100"
        val request = applyExitFeesRequest

        `when`("apply change of tenancy exit fees") {
            sut.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request)

            then("exit fees applied")
        }
    }

    given("get tickets for an account request") {
        val juniferAccountId = "100"

        `when`("the account has two open tickets, two closed") {
            and("one of each open and closed tickets are missing a ticketStepCode") {
                val request = getTicketsForAccountNumberRequest
                val tickets = sut.getTicketsForAccountNumber(juniferAccountId.toLong(), request)

                then("the ticket is returned") {
                    tickets.results.size shouldBe 4

                    with(tickets.results) {
                        this[0].id shouldBe AccountPrecannedData.TICKET_ID
                        this[0].status shouldBe Status.OPEN
                        this[0].ticketStepCode shouldNotBe null

                        this[1].id shouldBe AccountPrecannedData.TICKET_ID + 1
                        this[1].status shouldBe Status.CLOSED
                        this[1].ticketStepCode shouldNotBe null

                        this[2].id shouldBe AccountPrecannedData.TICKET_ID + 2
                        this[2].status shouldBe Status.OPEN
                        this[2].ticketStepCode shouldBe null

                        this[3].id shouldBe AccountPrecannedData.TICKET_ID + 3
                        this[3].status shouldBe Status.CLOSED
                        this[3].ticketStepCode shouldBe null
                    }
                }
            }
        }
    }

    given("get tickets by junifer account number request") {
        val juniferAccountNumber = "********"

        `when`("the account has two open tickets, two closed") {
            and("one of each open and closed tickets are missing a ticketStepCode") {
                val request = getTicketsByJuniferAccountNumberRequest
                val tickets = sut.getTicketsByJuniferAccountNumber(juniferAccountNumber, request)

                then("the ticket is returned") {
                    tickets.results.size shouldBe 4

                    with(tickets.results) {
                        this[0].id shouldBe AccountPrecannedData.TICKET_ID
                        this[0].status shouldBe Status.OPEN
                        this[0].ticketStepCode shouldNotBe null

                        this[1].id shouldBe AccountPrecannedData.TICKET_ID + 1
                        this[1].status shouldBe Status.CLOSED
                        this[1].ticketStepCode shouldNotBe null

                        this[2].id shouldBe AccountPrecannedData.TICKET_ID + 2
                        this[2].status shouldBe Status.OPEN
                        this[2].ticketStepCode shouldBe null

                        this[3].id shouldBe AccountPrecannedData.TICKET_ID + 3
                        this[3].status shouldBe Status.CLOSED
                        this[3].ticketStepCode shouldBe null
                    }
                }
            }
        }
    }

    given("update mode to smart payg for an account") {
        `when`("send change to payg mode request") {
            val request = listOf(meterPointChangeOfModeRequest {
                meterPointIdentifier = "********"
                executionDttm = LocalDateTime.now().toTimestamp()
                productReference = "SPAYG"
            }, meterPointChangeOfModeRequest {
                meterPointIdentifier = "6134713"
                executionDttm = LocalDateTime.now().toTimestamp()
                productReference = "SPAYG"
            })
            val response = sut.updateModeToSmartPayAsYouGo(
                BILLING_ACCOUNT_NUMBER,
                request
            )

            then("response is created successfully") {
                response shouldNotBe null
            }
        }

        `when`("request has already been made for this account") {
            val request = listOf(meterPointChangeOfModeRequest {
                meterPointIdentifier = "********"
                executionDttm = LocalDateTime.now().toTimestamp()
                productReference = "SPAYG"
            })

            val failsWith = assertThrows<JuniferGrpcException> {
                sut.updateModeToSmartPayAsYouGo(
                    "1234",
                    request
                )
            }
            then("we should receive a duplicate request exception") {
                failsWith shouldNotBe null
                failsWith.errorCode shouldBe ErrorCodes.COMMONS_DUPLICATE_REQUEST
                failsWith.message shouldBe "request already exists for account number: 1234"
            }
        }
    }

})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        when {
            request.method == HttpMethod.Get && request.url.fullPath.endsWith("/accounts/$JUNIFER_ACCOUNT_ID") -> {
                respond(
                    "{\"id\": \"$JUNIFER_ACCOUNT_ID\", \"type\": \"CB-INVOICE\", \"number\": \"3045566\", \"currency\": \"GBP\"," +
                            "\"name\": \"account123\" }",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("enrolAdditionalAccount") -> {
                respond(
                    "{ \"accountId\": $JUNIFER_ACCOUNT_ID }",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
            // triggerCreditToPayg
            request.method == HttpMethod.Post && request.url.fullPath.contains("${BILLING_ACCOUNT_NUMBER}/triggerCreditToPayg") -> {
                respond(
                    "{ \"accounts/accountNumber\": $BILLING_ACCOUNT_NUMBER }",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("accountNumber/1234/triggerCreditToPayg") -> {
                respond(
                    ByteReadChannel(
                        Json.encodeToString(
                            JuniferError.serializer(),
                            JuniferError("", "", "")
                        )
                    ),
                    HttpStatusCode.Conflict,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Delete && request.url.fullPath.contains("accountCredits") -> {
                respond(
                    "No Content",
                    HttpStatusCode.NoContent,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Delete && request.url.fullPath.contains("cancelAccountReviewPeriod") -> {
                respond(
                    "No Content",
                    HttpStatusCode.NoContent,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("accountReviewPeriods") -> {
                respond(
                    juniferCreateAccountReviewPeriodMockResponse,
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("note") -> {
                respond(
                    "{ \"id\": \"12\", \"createdDttm\": \"2017-06-22T11:40:30.000\", " +
                            "\"acceptedDttm\": \"2017-06-22T11:40:30.000\"," +
                            "\"currencyISO\": \"GBP\", \"grossAmount\": \"5\", \"netAmount\": \"4\", \"salesTax\": \"VAT\", " +
                            "\"salesTaxAmount\": \"1\", \"reason\": \"reason\", \"reference\": \"reference\"}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("note") -> {
                respond(
                    "{ \"id\": \"12\", \"createdDttm\": \"2017-06-22T11:40:30.000\"," +
                            "\"subject\": \"General Note\", \"type\": \"Email\", \"summary\": \"summary\"}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("tickets") -> {
                respond(
                    juniferCreateAccountTicketMockResponse,
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("tickets") -> {
                respond(
                    juniferGetAccountTicketsMockResponse,
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("repayments") -> {
                respond(
                    juniferCreateAccountRepaymentMockResponse,
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("cancelRegistration") -> {
                respond(
                    "{\"unsuccessfulCancellations\":[{\"reason\":\"reason\",\"meterPoints\":[{\"id\":106628," +
                            "\"links\":{\"self\":\"http://127.0.0.1:43002/rest/v1/meterPoints/106628\"}},{\"id\":106629," +
                            "\"links\":{\"self\":\"http://127.0.0.1:43002/rest/v1/meterPoints/106629\"}}]}]," +
                            "\"successfulCancellations\":[{\"meterPoints\":[{\"id\":102344," +
                            "\"links\":{\"self\":\"http://127.0.0.1:43002/rest/v1/meterPoints/102344\"}}]}]}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post && request.url.fullPath.contains("renewal") -> {
                respond(
                    "{ \"results\": [{\"accountNumber\":\"123\",\"elecProductBundleId\":106628}]}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Post -> {
                respond(
                    "",
                    HttpStatusCode.NoContent,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("customers") -> {
                respond(
                    "{\"results\": [{\"id\": \"12\", \"type\": \"CB-INVOICE\", \"number\": \"3045566\", \"currency\": \"GBP\"," +
                            "\"name\": \"account123\" }]}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("paymentSchedulePeriods") -> {
                respond(
                    "{\"results\": [{\"id\": \"12\", \"fromDt\": \"2015-01-27\", \"frequencyMultiple\": \"1\", \"frequency\": \"Monthly\"," +
                            "\"frequencyAlignmentDt\": \"2015-02-07\", \"nextPaymentDt\": \"2015-02-07\", \"amount\": \"80.00\"," +
                            "\"seasonalPaymentFl\": false, \"createdDttm\": \"2015-01-23T11:40:30.000\"," +
                            "\"links\":{\"self\":\"http://127.0.0.1:43002/rest/v1/paymentSchedulePeriods/1\"} }]}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("paymentMethods") -> {
                respond(
                    "{\n" +
                            "  \"results\": [{" +
                            "  \"id\": ${paymentMethodResponse.id},\n" +
                            "  \"paymentMethodType\": \"${paymentMethodResponse.paymentMethodType}\",\n" +
                            "  \"status\": \"${paymentMethodResponse.status}\",\n" +
                            "  \"createdDttm\": \"${paymentMethodResponse.createdDttm}\",\n" +
                            "  \"fromDttm\": \"${paymentMethodResponse.fromDttm}\",\n" +
                            "  \"toDttm\": \"${paymentMethodResponse.toDttm}\",\n" +
                            "  \"defaultStatus\": \"${paymentMethodResponse.defaultStatus}\",\n" +
                            "  \"links\": {\n" +
                            "    \"self\": \"http://127.0.0.1:43002/rest/v1/paymentMethods/${paymentMethodResponse.id}\",\n" +
                            "    \"directDebit\": \"http://127.0.0.1:43002/rest/v1/directDebits/1\",\n" +
                            "    \"account\": \"http://127.0.0.1:43002/rest/v1/accounts/1\"\n" +
                            "  }\n" +
                            "}]}",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get && request.url.fullPath.contains("suggestedPaymentAmount") -> {
                respond(
                    "{\"frequency\": \"Monthly\", \"frequencyMultiple\": 123, \"suggestedPaymentAmount\": 100 }",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Get -> {
                respond(
                    "{\"id\": \"12\", \"type\": \"CB-INVOICE\", \"number\": \"3045566\", \"currency\": \"GBP\"," +
                            "\"name\": \"account123\" }",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

            request.method == HttpMethod.Put -> {
                respond(
                    "",
                    HttpStatusCode.NoContent,
                )
            }

            else -> {
                respond(
                    content = ByteReadChannel(
                        Json.encodeToString(
                            JuniferError.serializer(),
                            JuniferError("", "", "")
                        )
                    ),
                    status = HttpStatusCode.BadRequest,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        }
    })

}
