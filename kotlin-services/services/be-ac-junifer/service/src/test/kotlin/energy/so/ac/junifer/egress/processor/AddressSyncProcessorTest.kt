package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferAddressRepository
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.ADDRESS_ID
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.ADDRESS_TYPE_ID
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.JUNIFER_ADDRESS_ID
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.addressSyncResponse
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.createAssetAddressSync
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.createCustomerAddressSync
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.customerAddressSyncEvent
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.testAddressType
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.testJuniferCustomerAddress
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.updateAssetAddressSync
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.updateCustomerAddressSync
import energy.so.ac.junifer.mapping.EntityIdentifier.ASSET_ADDRESS
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER_ADDRESS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class AddressSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val juniferAddressRepository = mockk<JuniferAddressRepository>()
    val mockCustomerSyncClient = mockk<SyncClient>()
    val mockAssetSyncClient = mockk<energy.so.assets.api.v2.SyncClient>()

    val customerAddressProcessor =
        AddressSyncProcessor(mockMapper, juniferAddressRepository, mockCustomerSyncClient, mockAssetSyncClient)

    afterEach {
        confirmVerified(mockMapper, juniferAddressRepository, mockCustomerSyncClient)
    }

    given("no existing mapped customer/asset address  and an existing junifer customerAddress") {

        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns null
        every { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns null
        every { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) } returns testJuniferCustomerAddress
        every { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) } returns testAddressType


        `when`("a customerAddress event is generated") {

            coEvery { mockCustomerSyncClient.syncAddressEntity(createCustomerAddressSync) } returns addressSyncResponse
            coEvery { mockAssetSyncClient.syncAddressEntity(createAssetAddressSync) } returns addressSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_ADDRESS,
                    JUNIFER_ADDRESS_ID.toString(),
                    ADDRESS_ID.toString()
                )
            }
            justRun {
                mockMapper.createCoreMapping(
                    ASSET_ADDRESS,
                    JUNIFER_ADDRESS_ID.toString(),
                    ADDRESS_ID.toString()
                )
            }

            customerAddressProcessor.process(customerAddressSyncEvent)

            then("a new customerAddress should be created") {
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) }
                verify { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        CUSTOMER_ADDRESS,
                        JUNIFER_ADDRESS_ID.toString(),
                        ADDRESS_ID.toString()
                    )
                }

                verify {
                    mockMapper.createCoreMapping(
                        ASSET_ADDRESS,
                        JUNIFER_ADDRESS_ID.toString(),
                        ADDRESS_ID.toString()
                    )
                }

                coVerify { mockCustomerSyncClient.syncAddressEntity(createCustomerAddressSync) }
                coVerify { mockAssetSyncClient.syncAddressEntity(createAssetAddressSync) }
            }
        }
    }

    given("an existing mapped customerAddress and an existing junifer customerAddress") {

        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) } returns testJuniferCustomerAddress
        every { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) } returns testAddressType

        `when`("a customerAddress event is generated") {

            coEvery { mockCustomerSyncClient.syncAddressEntity(updateCustomerAddressSync) } returns addressSyncResponse
            coEvery { mockAssetSyncClient.syncAddressEntity(updateAssetAddressSync) } returns addressSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_ADDRESS,
                    JUNIFER_ADDRESS_ID.toString(),
                    ADDRESS_ID.toString()
                )
            }

            customerAddressProcessor.process(customerAddressSyncEvent)

            then("customerAddress should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) }
                verify { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) }

                coVerify { mockCustomerSyncClient.syncAddressEntity(updateCustomerAddressSync) }
                coVerify { mockAssetSyncClient.syncAddressEntity(updateAssetAddressSync) }
            }
        }
    }

    given("an existing mapped customerAddress and existing junifer customerAddress to be deleted") {

        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) } returns testAddressType
        every { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) } returns testJuniferCustomerAddress.copy(
            deletefl = BOOLEAN_TRUE
        )

        `when`("a customerAddress event is generated") {

            coEvery { mockCustomerSyncClient.syncAddressEntity(any()) } returns addressSyncResponse
            coEvery { mockAssetSyncClient.syncAddressEntity(any()) } returns addressSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_ADDRESS,
                    JUNIFER_ADDRESS_ID.toString(),
                    ADDRESS_ID.toString()
                )
            }

            customerAddressProcessor.process(customerAddressSyncEvent)

            then("customerAddress should be deleted") {
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { juniferAddressRepository.getAddress(JUNIFER_ADDRESS_ID) }
                verify { juniferAddressRepository.findAddressType(ADDRESS_TYPE_ID) }
                coVerify { mockCustomerSyncClient.syncAddressEntity(any()) }
                coVerify { mockAssetSyncClient.syncAddressEntity(any()) }
            }
        }
    }
})
