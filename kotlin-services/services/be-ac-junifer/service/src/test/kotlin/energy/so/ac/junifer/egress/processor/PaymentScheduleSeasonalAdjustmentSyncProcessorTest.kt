package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentScheduleRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.fixtures.SeasonalAdjustmentPrecannedData
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.checkUnnecessaryStub
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentScheduleSeasonalAdjustmentSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentScheduleRepository = mockk<JuniferPaymentScheduleRepository>()
    val mockFeatureService = mockk<FeatureService>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = PaymentScheduleSeasonalAdjustmentSyncProcessor(
        mockMapper,
        mockPaymentScheduleRepository,
        mockSyncClient,
    )

    afterEach {
        confirmVerified(mockMapper, mockPaymentScheduleRepository, mockSyncClient)
        checkUnnecessaryStub(mockMapper, mockPaymentScheduleRepository, mockSyncClient)
        clearAllMocks()
    }

    given("non-existing junifer Payment Schedule Seasonal Definition") {

        coEvery {
            mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(
                SeasonalAdjustmentPrecannedData.deleteSchedSeasonalAdjustmentSync
            )
        } returns SeasonalAdjustmentPrecannedData.schedSeasonalAdjustmentSyncResponse

        `when`("Payment Schedule Seasonal Definition mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns SeasonalAdjustmentPrecannedData.SEASONAL_ADJUSTMENT_ID.toString()

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } throws EntityNotFoundException(
                "Could not find a payment schedule seasonal definition with id " +
                        "${SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID}"
            )

            processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnSyncEvent)

            then("core entry should be deleted") {
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                coVerify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                coVerify(exactly = 1) {
                    mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(
                        SeasonalAdjustmentPrecannedData.deleteSchedSeasonalAdjustmentSync
                    )
                }
            }
        }

        `when`("Payment Schedule Seasonal Definition not mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns null

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } throws EntityNotFoundException(
                "Could not find a payment schedule seasonal definition with id " +
                        "${SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID}"
            )

            processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnSyncEvent)

            then("no core entry sync should happen") {
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                coVerify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                coVerify(exactly = 0) { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(any()) }
            }
        }
    }

    given("existing junifer Payment Schedule Seasonal Definition") {

        `when`("Payment Schedule Seasonal Definition not mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns null

            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString(),
                    SeasonalAdjustmentPrecannedData.SEASONAL_ADJUSTMENT_ID.toString()
                )
            }

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfn

            coEvery { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(SeasonalAdjustmentPrecannedData.createSchedSeasonalAdjustmentSync) } returns SeasonalAdjustmentPrecannedData.schedSeasonalAdjustmentSyncResponse

            processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnSyncEvent)

            then("new core entry should be created") {
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString(),
                        SeasonalAdjustmentPrecannedData.SEASONAL_ADJUSTMENT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(SeasonalAdjustmentPrecannedData.createSchedSeasonalAdjustmentSync) }
            }
        }

        `when`("Payment Schedule Seasonal Definition mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            coEvery {
                mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(
                    SeasonalAdjustmentPrecannedData.patchSchedSeasonalAdjustmentSync
                )
            } returns SeasonalAdjustmentPrecannedData.schedSeasonalAdjustmentSyncResponse

            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns SeasonalAdjustmentPrecannedData.SEASONAL_ADJUSTMENT_ID.toString()

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfn

            processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnSyncEvent)

            then("core entry should be patched") {
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(SeasonalAdjustmentPrecannedData.patchSchedSeasonalAdjustmentSync) }
            }
        }
    }

    given("non-existing junifer Payment Schedule Seasonal Definition for Item") {

        `when`("associated Payment Schedule Seasonal Definition not mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns null

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfn

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ITEM_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfnItemHighSeason

            shouldThrow<AutoDiscardableException> { processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnItemSyncEvent) }

            then("the processor should back off") {
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ITEM_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                verify(exactly = 0) { mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemsByDfnId(any()) }
                coVerify(exactly = 0) { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(any()) }
            }
        }
    }

    given("existing junifer Payment Schedule Seasonal Definition for Item") {

        coEvery {
            mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(
                SeasonalAdjustmentPrecannedData.patchSchedSeasonalAdjustmentItemSync
            )
        } returns SeasonalAdjustmentPrecannedData.schedSeasonalAdjustmentSyncResponse

        `when`("associated Payment Schedule Seasonal Definition mapped in core") {
            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_17065_SEASONAL_ADJUSTMENT_SYNC) } returns true
            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ITEM_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfnItemHighSeason

            every {
                mockMapper.getCoreId(
                    EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                )
            } returns SeasonalAdjustmentPrecannedData.SEASONAL_ADJUSTMENT_ID.toString()

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } returns SeasonalAdjustmentPrecannedData.testSeasonalDfn

            every {
                mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemsByDfnId(
                    SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                )
            } returns listOf(
                SeasonalAdjustmentPrecannedData.testSeasonalDfnItemHighSeason,
                SeasonalAdjustmentPrecannedData.testSeasonalDfnItemNotHighSeason
            )

            processor.process(SeasonalAdjustmentPrecannedData.seasonalDfnItemSyncEvent)

            then("existing seasonal adjustment should be updated") {
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                verify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemById(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ITEM_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PAYMENT_SCHEDULE_SEASONAL_ADJUSTMENT,
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID.toString()
                    )
                }
                coVerify {
                    mockPaymentScheduleRepository.getPaymentScheduleSeasonalDfnItemsByDfnId(
                        SeasonalAdjustmentPrecannedData.JUNIFER_SEASONAL_DFN_ID
                    )
                }
                coVerify { mockSyncClient.syncPaymentSchedSeasonalAdjustmentEntity(SeasonalAdjustmentPrecannedData.patchSchedSeasonalAdjustmentItemSync) }
            }
        }
    }
})
