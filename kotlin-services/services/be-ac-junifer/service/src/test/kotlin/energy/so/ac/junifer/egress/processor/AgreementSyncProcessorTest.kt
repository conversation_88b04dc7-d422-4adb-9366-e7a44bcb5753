package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferAgreementRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.CORE_AGREEMENT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.CORE_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.CORE_METER_POINT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.CORE_PRODUCT_VARIAN_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_AGREEMENT_TYPE_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_PRODUCT_BUNDLE_DFN_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_PRODUCT_BUNDLE_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_PRODUCT_DFN_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_PRODUCT_ID
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.JUNIFER_PRODUCT_TYPE
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.agreementSyncResponse
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.createAgreementSync
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.deleteAgreementSync
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.invalidEvent
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.juniferAgreementType
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.juniferProduct
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.juniferProductBundle
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.juniferProductBundleDfnWithType
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.juniferProductBundleDfnWithoutType
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.patchAgreementSync
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.productBundleSyncEvent
import energy.so.ac.junifer.fixtures.AgreementPrecannedData.productSyncEvent
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.assertEquals

class AgreementSyncProcessorTest : BehaviorSpec({

    isolationMode = IsolationMode.InstancePerTest

    val mockMapper = mockk<EntityMapper>()
    val mockRepo = mockk<JuniferAgreementRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = AgreementSyncProcessor(mockMapper, mockSyncClient, mockRepo)

    given("No existing ProductBundle but Agreement in core") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(deleteAgreementSync) } returns agreementSyncResponse

        `when`("ProductBundle event") {
            processor.process(productBundleSyncEvent)

            then("Agreement should be deleted") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.AGREEMENT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                coVerify { mockSyncClient.syncAgreementEntity(deleteAgreementSync) }
            }
        }
    }


    given("No existing ProductBundle and Agreement not in core") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null

        `when`("ProductBundle event") {
            processor.process(productBundleSyncEvent)

            then("Should ignore event") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.AGREEMENT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }


    given("Agreement exists but no AgreementType") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns null

        `when`("ProductBundle event") {
            shouldThrow<SyncDelayedException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Agreement does not exist and no number") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle.copy(number = null)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null

        `when`("ProductBundle event") {
            val exception = shouldThrow<AutoDiscardableException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                coVerify { mockSyncClient wasNot Called }
                assertEquals(true, exception.canDiscard)
            }
        }
    }

    given("Agreement exists but no core BillingAccount") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns null

        `when`("ProductBundle event") {
            shouldThrow<SyncDelayedException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.BILLING_ACCOUNT,
                        JUNIFER_ACCOUNT_ID.toString()
                    )
                }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Agreement exists but no ProductDfn") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns null

        `when`("ProductBundle event") {
            shouldThrow<SyncDelayedException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.BILLING_ACCOUNT,
                        JUNIFER_ACCOUNT_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Agreement exists but no core MeterPoint") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns JUNIFER_PRODUCT_TYPE
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null

        `when`("ProductBundle event") {
            shouldThrow<SyncDelayedException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.BILLING_ACCOUNT,
                        JUNIFER_ACCOUNT_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                verify { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                        JUNIFER_PRODUCT_DFN_ID,
                        JUNIFER_PRODUCT_BUNDLE_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString()
                    )
                }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Agreement exists but no core ProductDfn") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns JUNIFER_PRODUCT_TYPE
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()

        `when`("ProductBundle event") {
            shouldThrow<SyncDelayedException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {

                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.BILLING_ACCOUNT,
                        JUNIFER_ACCOUNT_ID.toString()
                    )
                }
                verify { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                verify { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                        JUNIFER_PRODUCT_DFN_ID,
                        JUNIFER_PRODUCT_BUNDLE_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString()
                    )
                }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("ProductBundle exists but no core Agreement") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns JUNIFER_PRODUCT_TYPE
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(createAgreementSync) } returns agreementSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
            )
        }

        `when`("ProductBundle event") {
            processor.process(productBundleSyncEvent)

            then("Agreement should be created") {
                coVerify { mockSyncClient.syncAgreementEntity(createAgreementSync) }

                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
                    )
                }
            }
        }
    }

    given("ProductBundle exists and core Agreement") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns JUNIFER_PRODUCT_TYPE
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(patchAgreementSync) } returns agreementSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
            )
        }

        `when`("ProductBundle event") {
            processor.process(productBundleSyncEvent)

            then("Agreement should be updated") {
                coVerify { mockSyncClient.syncAgreementEntity(patchAgreementSync) }

                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
                    )
                }
            }
        }
    }

    given("Agreement exists") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductById(JUNIFER_PRODUCT_ID) } returns juniferProduct
        every { mockRepo.findProductBundleDfnByProductId(JUNIFER_PRODUCT_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns CORE_AGREEMENT_ID.toString()
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns JUNIFER_PRODUCT_TYPE
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(patchAgreementSync) } returns agreementSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
            )
        }

        `when`("Product event") {
            processor.process(productSyncEvent)

            then("Agreement should be updated") {
                coVerify { mockSyncClient.syncAgreementEntity(patchAgreementSync) }

                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
                    )
                }
            }
        }
    }

    given("Product does not exist") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductById(JUNIFER_PRODUCT_ID) } returns null

        `when`("Product event") {
            shouldThrow<SyncDelayedException> { processor.process(productSyncEvent) }

            then("Event should be delayed") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("ProductBundle does not exist in CORE") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleDfnByProductId(JUNIFER_PRODUCT_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductById(JUNIFER_PRODUCT_ID) } returns juniferProduct
        every { mockMapper.getCoreId(EntityIdentifier.AGREEMENT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) } returns null

        `when`("Product event") {
            shouldThrow<SyncDelayedException> { processor.process(productSyncEvent) }

            then("Event should be delayed") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("No reference") {
        `when`("Product Bundle event") {
            shouldThrow<IllegalStateException> { processor.process(invalidEvent) }

            then("Event processing should error") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("ProductType invalid for Agreement") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns "INVALID"
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(createAgreementSync) } returns agreementSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
            )
        }

        `when`("ProductBundle event") {
            shouldThrow<IllegalStateException> { processor.process(invalidEvent) }

            then("Event should error out") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("ProductType missing on Product linked Agreement") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithoutType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns juniferAgreementType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_BILLING_ACCOUNT_ID.toString()
        every { mockRepo.findAgreementProductTypeByProductId(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns "INVALID"
        every { mockRepo.findProductDfnIdsForProductBundleId(JUNIFER_PRODUCT_BUNDLE_ID) } returns listOf(
            JUNIFER_PRODUCT_DFN_ID
        )
        every {
            mockRepo.findMeterPointsForDfnIdAndProductBundleId(
                JUNIFER_PRODUCT_DFN_ID,
                JUNIFER_PRODUCT_BUNDLE_ID
            )
        } returns listOf(JUNIFER_METER_POINT_ID)
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns CORE_PRODUCT_VARIAN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns CORE_METER_POINT_ID.toString()
        coEvery { mockSyncClient.syncAgreementEntity(createAgreementSync) } returns agreementSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString(), CORE_AGREEMENT_ID.toString()
            )
        }

        `when`("ProductBundle event") {
            processor.process(productBundleSyncEvent)

            then("Event should complete without no update") {
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Agreement does not exists and invalid ProductBundleDfnFK") {
        every { mockRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns juniferProductBundleDfnWithType
        every { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns juniferProductBundle.copy(
            productbundledfnfk = -99
        )
        every {
            mockMapper.getCoreId(
                EntityIdentifier.AGREEMENT,
                JUNIFER_PRODUCT_BUNDLE_ID.toString()
            )
        } returns null
        every { mockRepo.findAgreementTypeById(JUNIFER_AGREEMENT_TYPE_ID) } returns null

        `when`("ProductBundle event") {
            val exception = shouldThrow<AutoDiscardableException> { processor.process(productBundleSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.AGREEMENT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString()
                    )
                }
                coVerify { mockSyncClient wasNot Called }
                assertEquals(true, exception.canDiscard)
            }
        }
    }
})
