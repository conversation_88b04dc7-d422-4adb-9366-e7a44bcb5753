package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.juniferNote
import energy.so.commons.extension.save
import energy.so.commons.model.tables.references.JUNIFER__NOTE
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqJuniferNoteRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val sut = JooqJuniferNoteRepository(db)

    given("Note exists in DB") {
        val expectedJuniferNote = juniferNote
        db.newRecord(JUNIFER__NOTE, expectedJuniferNote).apply { save() }

        `when`("Note is queried") {
            val juniferNote = sut.findById(1L)
            then("Note is returned") {
                juniferNote shouldBe expectedJuniferNote
            }
        }
    }
    given("Note doesn't exist in DB") {
        `when`("Note is queried") {
            then("return null") {
                sut.findById(2L) shouldBe null
            }
        }
    }
})
