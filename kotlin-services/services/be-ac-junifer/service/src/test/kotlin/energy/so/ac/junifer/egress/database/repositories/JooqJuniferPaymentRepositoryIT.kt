package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.JUNIFER_ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.JUNIFER_GO_CARDLESS_DD_COLLECTION_ID
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferAccount
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferAccountTransaction
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferGocardlessDDCollection
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferPayment
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferPaymentRequest
import energy.so.ac.junifer.fixtures.DirectDebitPaymentPrecannedData.testJuniferPaymentRequestAttempt
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.JUNIFER_DD_INSTRUCTION_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.JUNIFER_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.SETUP_REQUEST_TYPE
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.testJuniferDirectDDInstruction
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.testJuniferDirectDebit
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_DELETE_FL
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_PARTITION_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_PENDING_DEFAULT_FL
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_REFERENCE
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_STATUS
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_TYPE_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_TYPE_INTERNAL_KEY
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_VERSION_NO
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.testJuniferPaymentmethod
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.testJuniferPaymentmethodType
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.pojos.Junifer_Paymentrequestattempt
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNT
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNTTRANSACTION
import energy.so.commons.model.tables.references.JUNIFER__GOCARDLESSDDCOLLECTION
import energy.so.commons.model.tables.references.JUNIFER__GOCARDLESSDDINSTRUCTION
import energy.so.commons.model.tables.references.JUNIFER__GOCARDLESSDIRECTDEBIT
import energy.so.commons.model.tables.references.JUNIFER__PAYMENT
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTMETHOD
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTMETHODTYPE
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTREQUEST
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTREQUESTATTEMPT
import energy.so.database.test.installDatabase
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferPaymentRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferPaymentRepository(db)

    given("GoCardlessDDCollection exists in DB") {
        insertGoCardlessDDCollection(db)

        `when`("GoCardlessDDCollection is queried") {
            val juniferGocardlessddcollection = repo.getGoCardlessDDCollection(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID)

            then("GoCardlessDDCollection is returned") {

                with(juniferGocardlessddcollection) {
                    this shouldNotBe null
                    id shouldBe testJuniferGocardlessDDCollection.id
                    paymentrequestattemptfk shouldBe testJuniferGocardlessDDCollection.paymentrequestattemptfk
                    gocardlessdirectdebitfk shouldBe testJuniferGocardlessDDCollection.gocardlessdirectdebitfk
                    bacsprocessingdt shouldBe testJuniferGocardlessDDCollection.bacsprocessingdt
                    referralcode shouldBe testJuniferGocardlessDDCollection.referralcode
                    successplusfl shouldBe testJuniferGocardlessDDCollection.successplusfl
                    reference shouldBe testJuniferGocardlessDDCollection.reference
                    deletefl shouldBe testJuniferGocardlessDDCollection.deletefl
                    versionno shouldBe testJuniferGocardlessDDCollection.versionno
                    partitionid shouldBe testJuniferGocardlessDDCollection.partitionid
                }
            }
        }
    }

    given("Payment, PaymentRequest, PaymentRequestAttempt and GoCardlessDDCollection exist in DB") {
        `when`("Payment is queried by GO CARDLESS DD COLLECTION ID") {
            insertPayment(db)
            insertPaymentRequestAttempt(db)
            insertPaymentRequest(db)
            insertGoCardlessDDCollection(db)

            val juniferPayment = repo.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID)

            then("Payment ID is returned") {
                juniferPayment shouldBe testJuniferPayment.paymentrequestfk
            }
        }

        `when`("multiple payment request attempts exist for the same payment request") {
            insertPayment(db)
            insertPaymentRequestAttempt(db)
            insertPaymentRequestAttempt(
                db, testJuniferPaymentRequestAttempt.copy(
                    status = "Failed",
                    id = (testJuniferPaymentRequestAttempt.id?.plus(1))
                )
            )
            insertPaymentRequest(db)
            insertGoCardlessDDCollection(db)

            val juniferPayment = repo.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID)

            then("only the successful payment should be returned") {
                juniferPayment shouldBe testJuniferPayment.paymentrequestfk
            }
        }

        `when`("no successful payment request attempt exists for a payment request") {
            insertPayment(db)
            insertPaymentRequestAttempt(
                db, testJuniferPaymentRequestAttempt.copy(
                    status = "Failed",
                )
            )
            insertPaymentRequestAttempt(
                db, testJuniferPaymentRequestAttempt.copy(
                    status = "Failed",
                    id = (testJuniferPaymentRequestAttempt.id?.plus(1))
                )
            )
            insertPaymentRequest(db)
            insertGoCardlessDDCollection(db)

            val juniferPayment = repo.getPaymentRequestIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID)

            then("nothing should be returned") {
                juniferPayment shouldBe null
            }
        }
    }

    given("Account, PaymentMethod and GoCardlessDDCollection exist in DB") {
        insertAccount(db)
        insertPaymentMethod(db)
        insertGoCardlessDirectDebit(db)
        insertGoCardlessDDCollection(db)

        `when`("Account ID is queried by GO CARDLESS DD COLLECTION ID") {
            val juniferAccountId = repo.getJuniferAccountIdFromCollectionId(JUNIFER_GO_CARDLESS_DD_COLLECTION_ID)
            then("Account ID is returned") {
                juniferAccountId shouldBe testJuniferAccount.id
            }
        }
    }

    given("Account and Account Transaction exist in DB") {
        insertAccount(db)
        insertAccountTransaction(db)

        `when`("Account is queried by Account Transaction ID") {
            val juniferAccount = repo.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID)

            then("Account is returned") {

                with(juniferAccount) {
                    this shouldNotBe null
                    id shouldBe testJuniferAccount.id
                    billingentityfk shouldBe testJuniferAccount.billingentityfk
                    accounttypefk shouldBe testJuniferAccount.accounttypefk
                    number shouldBe testJuniferAccount.number
                    name shouldBe testJuniferAccount.name
                    createddttm shouldBe testJuniferAccount.createddttm
                    createdusertblfk shouldBe testJuniferAccount.createdusertblfk
                    reference shouldBe testJuniferAccount.reference
                    deletefl shouldBe testJuniferAccount.deletefl
                    versionno shouldBe testJuniferAccount.versionno
                    partitionid shouldBe testJuniferAccount.partitionid
                }
            }
        }
    }

    given("Go Cardless Direct Debit exists in DB") {
        insertGoCardlessDirectDebit(db)

        `when`("Go Cardless Direct Debit is queried by ID") {
            val juniferGocardlessdirectdebit = repo.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID)

            then("Go Cardless Direct Debit is returned") {

                with(juniferGocardlessdirectdebit) {
                    this shouldNotBe null
                    id shouldBe testJuniferDirectDebit.id
                    reference shouldBe testJuniferDirectDebit.reference
                    deletefl shouldBe testJuniferDirectDebit.deletefl
                    versionno shouldBe testJuniferDirectDebit.versionno
                    partitionid shouldBe testJuniferDirectDebit.partitionid
                }
            }
        }
    }

    given("Go Cardless Direct Debit Instruction not saved in db") {

        `when`("Go Cardless Direct Debit Instruction is queried by ID") {
            then("should throw EntityNotFoundException") {

                shouldThrow<EntityNotFoundException> { repo.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) }
            }
        }
    }

    given("Go Cardless Direct Debit Instruction saved in db") {
        insertGoCardlessDDInstruction(db)

        `when`("Go Cardless Direct Debit Instruction is queried by ID") {
            val juniferGoCardlessDDInstruction = repo.getGoCardlessDDInstruction(testJuniferDirectDDInstruction.id!!)

            then("Go Cardless Direct Debit Instruction is returned") {

                with(juniferGoCardlessDDInstruction) {
                    this shouldNotBe null
                    gocardlessdirectdebitfk shouldBe JUNIFER_DIRECT_DEBIT_ID
                    requesttype shouldBe SETUP_REQUEST_TYPE
                }
            }
        }
    }

    given("Go Cardless Direct Debit Instruction and Go Cardless Direct Debit exist in DB") {
        insertGoCardlessDDInstruction(db)
        insertGoCardlessDirectDebit(db)

        `when`("Go Cardless Direct Debit Instruction  is queried by Go Cardless Direct Debit ID") {
            val juniferGoCardlessDirectDebit = repo.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID)


            then("Go Cardless Direct Debit is returned") {

                with(juniferGoCardlessDirectDebit!!) {
                    this shouldNotBe null
                    id shouldBe juniferGoCardlessDirectDebit.id
                    reference shouldBe juniferGoCardlessDirectDebit.reference
                    deletefl shouldBe juniferGoCardlessDirectDebit.deletefl
                    versionno shouldBe juniferGoCardlessDirectDebit.versionno
                    partitionid shouldBe juniferGoCardlessDirectDebit.partitionid
                }
            }
        }
    }

    given("Payment Method exists in DB") {
        insertPaymentMethod(db)

        `when`("Junifer Payment Method is queried by ID") {
            val juniferPaymentMethod = repo.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID)

            then("Junifer Payment Method is returned") {

                with(juniferPaymentMethod) {
                    this shouldNotBe null
                    id shouldBe JUNIFER_PAYMENT_METHOD_ID
                    paymentmethodtypefk shouldBe PAYMENT_METHOD_TYPE_ID
                    accountfk shouldBe BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
                    status shouldBe PAYMENT_METHOD_STATUS
                    pendingdefaultfl shouldBe PAYMENT_METHOD_PENDING_DEFAULT_FL
                    reference shouldBe PAYMENT_METHOD_REFERENCE
                    deletefl shouldBe PAYMENT_METHOD_DELETE_FL
                    versionno shouldBe PAYMENT_METHOD_VERSION_NO
                    partitionid shouldBe PAYMENT_METHOD_PARTITION_ID
                }
            }
        }
    }

    given("Payment Method does not exist in DB") {
        `when`("Junifer Payment Method is queried by ID") {
            shouldThrow<EntityNotFoundException> { repo.getPaymentMethodById(0L) }
        }
    }

    given("Payment Method Type exists in DB") {
        insertPaymentMethodType(db)

        `when`("Junifer Payment Method is queried by ID") {
            val juniferPaymentMethodType = repo.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID)

            then("Junifer Payment Method Type is returned") {

                with(juniferPaymentMethodType) {
                    this shouldNotBe null
                    id shouldBe PAYMENT_METHOD_TYPE_ID
                    internalkey shouldBe PAYMENT_METHOD_TYPE_INTERNAL_KEY
                }
            }
        }
    }

    given("Payment Method Type does not exist in DB") {
        `when`("Junifer Payment Method Type is queried by ID") {
            shouldThrow<EntityNotFoundException> { repo.getPaymentMethodTypeById(0L) }
        }
    }
})

fun insertPaymentMethod(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__PAYMENTMETHOD,
            testJuniferPaymentmethod
        )
    )
}

fun insertPaymentMethodType(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__PAYMENTMETHODTYPE,
            testJuniferPaymentmethodType
        )
    )
}

fun insertGoCardlessDDCollection(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__GOCARDLESSDDCOLLECTION,
            testJuniferGocardlessDDCollection
        )
    )
}

fun insertPayment(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__PAYMENT,
            testJuniferPayment
        )
    )
}

fun insertPaymentRequest(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__PAYMENTREQUEST,
            testJuniferPaymentRequest
        )
    )
}

fun insertPaymentRequestAttempt(
    db: DSLContext,
    attempt: Junifer_Paymentrequestattempt = testJuniferPaymentRequestAttempt,
) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__PAYMENTREQUESTATTEMPT,
            attempt,
        )
    )
}

fun insertAccount(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__ACCOUNT,
            testJuniferAccount
        )
    )
}

fun insertAccountTransaction(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__ACCOUNTTRANSACTION,
            testJuniferAccountTransaction
        )
    )
}

fun insertGoCardlessDirectDebit(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__GOCARDLESSDIRECTDEBIT,
            testJuniferDirectDebit
        )
    )
}

fun insertGoCardlessDDInstruction(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__GOCARDLESSDDINSTRUCTION,
            testJuniferDirectDDInstruction
        )
    )
}
