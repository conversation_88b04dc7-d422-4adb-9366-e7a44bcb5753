package energy.so.ac.junifer.egress.housekeeping

import energy.so.ac.junifer.config.JobArchiveCompletedConfig
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class ArchiveCompletedJobTest : BehaviorSpec({
    isolationMode = IsolationMode.InstancePerTest

    val mockSyncRepository = mockk<SyncEventRepository>()
    val limit = 100
    val batchSize = 50
    val noOfDays = 10L
    val delayMillisBetweenBatches = 10L
    val config = JobArchiveCompletedConfig(limit, batchSize, delayMillisBetweenBatches, noOfDays)

    val sut = ArchiveCompletedJob(mockSyncRepository, config)

    given("ArchiveCompletedJo::run ") {
        and("archive process failed") {
            coEvery {
                mockSyncRepository.archiveCompletedEventsOlderThan(
                    noOfDays,
                    batchSize
                )
            } throws IllegalStateException()

            `when`("job run") {
                then("job fails") {
                    assertThrows<IllegalStateException> { sut.run() }
                    coVerify {
                        mockSyncRepository.archiveCompletedEventsOlderThan(
                            noOfDays,
                            batchSize
                        )
                    }
                }
            }
        }

        and("archive process succeeded") {
            coEvery {
                mockSyncRepository.archiveCompletedEventsOlderThan(
                    noOfDays,
                    batchSize
                )
            } returns batchSize

            `when`("job run") {
                then("job fails") {
                    sut.run()
                    coVerify(exactly = limit / batchSize) {
                        mockSyncRepository.archiveCompletedEventsOlderThan(
                            noOfDays,
                            batchSize
                        )
                    }
                }
            }
        }
    }
})
