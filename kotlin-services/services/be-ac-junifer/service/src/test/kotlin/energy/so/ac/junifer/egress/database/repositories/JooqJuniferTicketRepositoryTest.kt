package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.juniferTicket
import energy.so.commons.extension.save
import energy.so.commons.model.tables.references.JUNIFER__TICKET
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqJuniferTicketRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val sut = JooqJuniferTicketRepository(db)

    given("Ticket exists in DB") {
        val expectedJuniferTicket = juniferTicket
        db.newRecord(JUNIFER__TICKET, expectedJuniferTicket).apply { save() }

        `when`("Ticket is queried") {
            val juniferTicket = sut.findById(1L)
            then("Ticket is returned") {
                juniferTicket shouldBe expectedJuniferTicket
            }
        }
    }
    given("Ticket doesn't exist in DB") {
        `when`("Ticket is queried") {
            then("return null") {
                sut.findById(2L) shouldBe null
            }
        }
    }
})
