package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.LocalDate

const val CREATE_GAS_VARIANT_SQL = "/sql/products/create_gas_product_variant.sql"
const val CREATE_ELEC_VARIANT_SQL = "/sql/products/create_elec_product_variant.sql"

class JooqJuniferProductVariantRepositoryTest : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferProductVariantRepository(db)

    given("an existing gas variant") {

        // Creates a gas product variant with ProductDfn id of 54
        val gasVariantSql = SqlLoader.loadFromStream(getSql(CREATE_GAS_VARIANT_SQL))
        val existingProductDfnId = 54L

        `when`("the product definition id is queried from the recurring item definition ID") {

            db.execute(gasVariantSql)
            val productDfnId = repo.getProductDfnIdFromRecurringItemDfn(2350)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the recurring item definition amount ID") {

            db.execute(gasVariantSql)
            val productDfnId = repo.getProductDfnIdFromRecurringItemDfnAmount(2347)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the service rating rate ID") {

            db.execute(gasVariantSql)
            val productDfnId = repo.getProductDfnIdFromServiceRatingRate(470)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the meter service definition ID") {

            db.execute(gasVariantSql)
            val productDfnId = repo.getProductDfnIdFromMeterServiceDfn(471)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the productBundleDfn is queried by id") {

            db.execute(gasVariantSql)
            val productBundleDfn = repo.getProductBundleDfnById(53)

            then("the product bundle dfn should be returned") {
                productBundleDfn.id shouldBe 53
            }
        }

        `when`("the product definition id is queried from the price plan row dimension") {

            db.execute(gasVariantSql)
            val productDfnId = repo.getProductDfnIdFromPricePlanRowDimension(2347)

            then("the product definition id should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition ids are queried from the product bundle definition") {

            db.execute(gasVariantSql)
            val productDfnIds = repo.getProductDfnIdsForProductBundleDfn(53)

            then("the product definition id list should be returned") {
                productDfnIds.size shouldBe 1
                productDfnIds[0] shouldBe existingProductDfnId
            }
        }

        `when`("the rate is queried") {

            db.execute(gasVariantSql)
            val rate = repo.getProductDfnsById(existingProductDfnId)[0]

            then("return a valid rate") {
                rate.productDfnId shouldBe existingProductDfnId
                rate.productBundleDfnId shouldBe 53
                rate.fromDt!!.toLocalDate() shouldBe LocalDate.of(2015, 10, 26)
                rate.toDt!!.toLocalDate() shouldBe LocalDate.of(9999, 1, 1)
                rate.utilityType shouldBe "G"
                rate.ukGspGroupCode shouldBe "_A"
                rate.productType shouldBe "Gas Supply (Res)"
                rate.variantType shouldBe "GAS_SINGLE"
                rate.productBundleDfnName shouldBe "Gas | SOAR | 151116"
                rate.directDebitFlag shouldBe "Y"
                rate.deleteFlag shouldBe "N"
                rate.rate1 shouldBe BigDecimal("2.6188050000000000")
                rate.rate2 shouldBe null
                rate.dailyStandingCharge shouldBe BigDecimal("21.0000000000000000")
                rate.onlineBillCreditAmt shouldBe BigDecimal("0E-16")
                rate.dualFuelCreditAmt shouldBe BigDecimal("0E-16")
                rate.earlyTerminationCharge shouldBe BigDecimal("4.9980000000000000")
            }
        }
    }

    given("an existing elec variant") {

        // Creates an elec product variant with ProductDfn id of 55
        val elecVariantSql = SqlLoader.loadFromStream(getSql(CREATE_ELEC_VARIANT_SQL))
        val existingProductDfnId = 55L

        `when`("the product definition id is queried from the recurring item definition ID") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromRecurringItemDfn(2437)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the recurring item definition amount ID") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromRecurringItemDfnAmount(2431)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the MPAN product item definition ID") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromMpanProductItemDfn(471)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the MPAN product item definition period ID") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromMpanProductItemDfnPeriod(470)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition id is queried from the MPAN product item definition rate ID") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromMpanProductItemDfnRate(2345)

            then("the product definition ID should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the productBundleDfn is queried by id") {

            db.execute(elecVariantSql)
            val productBundleDfn = repo.getProductBundleDfnById(54)

            then("the product bundle dfn should be returned") {
                productBundleDfn.id shouldBe 54
            }
        }

        `when`("the product definition id is queried from the price plan row dimension") {

            db.execute(elecVariantSql)
            val productDfnId = repo.getProductDfnIdFromPricePlanRowDimension(2402)

            then("the product definition id should be returned") {
                productDfnId shouldBe existingProductDfnId
            }
        }

        `when`("the product definition ids are queried from the product bundle definition") {

            db.execute(elecVariantSql)
            val productDfnIds = repo.getProductDfnIdsForProductBundleDfn(54)

            then("the product definition id list should be returned") {
                productDfnIds.size shouldBe 1
                productDfnIds[0] shouldBe existingProductDfnId
            }
        }

        `when`("the rate is queried") {

            db.execute(elecVariantSql)
            val rate = repo.getProductDfnsById(existingProductDfnId)[0]

            then("return a valid rate") {
                rate.productDfnId shouldBe existingProductDfnId
                rate.productBundleDfnId shouldBe 54
                rate.fromDt!!.toLocalDate() shouldBe LocalDate.of(2015, 10, 1)
                rate.toDt!!.toLocalDate() shouldBe LocalDate.of(9999, 1, 1)
                rate.utilityType shouldBe "E"
                rate.ukGspGroupCode shouldBe "_A"
                rate.productType shouldBe "Electricity Supply (Res)"
                rate.variantType shouldBe "ELECTRICITY_SINGLE"
                rate.productBundleDfnName shouldBe "Electricity 1-Rate | SOAR | 151116"
                rate.directDebitFlag shouldBe "Y"
                rate.deleteFlag shouldBe "N"
                rate.rate1 shouldBe BigDecimal("10.2049500000000000")
                rate.rate2 shouldBe null
                rate.dailyStandingCharge shouldBe BigDecimal("21.0000000000000000")
                rate.onlineBillCreditAmt shouldBe BigDecimal("0E-16")
                rate.dualFuelCreditAmt shouldBe BigDecimal("0E-16")
                rate.earlyTerminationCharge shouldBe BigDecimal("4.9980000000000000")
            }
        }
    }
})

fun getSql(resource: String) =
    JooqJuniferProductVariantRepositoryTest::class.java.getResourceAsStream(resource)
        ?: throw IllegalStateException("Could not find resource $resource")
