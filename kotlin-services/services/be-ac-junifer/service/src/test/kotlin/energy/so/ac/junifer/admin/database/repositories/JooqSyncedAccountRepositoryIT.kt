package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.egress.database.repositories.getSql
import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqSyncedAccountRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val sut = JooqSyncedAccountRepository(db)

    given("there is one synced account in db") {
        val accountSql =
            SqlLoader.loadFromStream(getSql("/sql/customer/create_synced_account.sql"))
        db.execute(accountSql)

        `when`("findAll method called") {
            val response = sut.findAll()
            then("response should match expected") {
                response.size shouldBe 1
                response[0].juniferaccountid shouldBe 1
            }
        }
    }

})
