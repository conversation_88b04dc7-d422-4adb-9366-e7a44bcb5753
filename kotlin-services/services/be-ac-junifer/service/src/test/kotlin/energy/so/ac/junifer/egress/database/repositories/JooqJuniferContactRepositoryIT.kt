package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.model.tables.references.JUNIFER__ACCOUNT
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNTCONTACT
import energy.so.commons.model.tables.references.JUNIFER__CONTACT
import energy.so.commons.model.tables.references.JUNIFER__CONTACTTYPE
import energy.so.commons.model.tables.references.JUNIFER__CONTACTVERSION
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMERCONTACT
import energy.so.commons.model.tables.references.JUNIFER__TITLE
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.time.LocalDate
import java.time.OffsetDateTime
import org.jooq.DSLContext

class JooqJuniferContactRepositoryTest : BehaviorSpec({

    val db = installDatabase()
    val sut = JooqJuniferContactRepository(db)

    given("getContactVersion") {
        insertContactVersion(db)


        `when`("ContactVersion is queried") {

            val actualEntity = sut.getContactVersion(CONTACT_VERSION_ID)

            then("ContactVersion should be returned") {
                actualEntity shouldNotBe null
            }
        }

    }

    given("getContactVersionByContactId") {

        insertContactVersion(db)


        `when`("Junifer_ContactVersion is queried by contact id") {

            val actualEntity = sut.getContactVersionByContactId(CONTACT_ID)

            then("ContactVersion should be returned") {
                actualEntity shouldNotBe null
            }
        }

    }

    given("getContactByContactVersionId") {

        insertContactVersion(db)
        insertContact(db)


        `when`("Junifer_Contact is queried by contact version id") {

            val actualEntity = sut.getContactByContactVersionId(CONTACT_VERSION_ID)

            then("Junifer_Contact should be returned") {
                actualEntity shouldNotBe null
            }
        }
    }

    given("getContact") {
        insertContact(db)

        `when`("Junifer_Contact is queried by contact id") {

            val actualEntity = sut.getContact(CONTACT_ID)

            then("Junifer_Contact should be returned") {
                actualEntity shouldNotBe null
            }
        }
    }

    given("getTitle") {
        insertTitle(db)

        `when`("Junifer_Title is queried by title id") {

            val actualEntity = sut.getTitle(TITLE_ID)

            then("Junifer_Title should be returned") {
                actualEntity shouldNotBe null
                actualEntity.internalkey shouldBe TITLE_INTERNAL_KEY
            }
        }

    }

    given("getContactType") {
        inserContactType(db)

        `when`("Junifer_Contacttype is queried by  id") {

            val actualEntity = sut.getContactType(1)

            then("Junifer_Contacttype should be returned") {
                actualEntity shouldNotBe null
                actualEntity.internalkey shouldBe "Residential"
            }
        }
    }

    given("getCustomerContact") {
        insertCustomerContact(db)

        `when`("Junifer_CustomerContact is queried by  id") {

            val actualEntity = sut.getCustomerContact(1)

            then("Junifer_CustomerContact should be returned") {
                actualEntity shouldNotBe null
            }
        }
    }

    given("getBillingAccountContact") {
        insertBillingAccountContact(db)

        `when`("Junifer_AccountContact is queried by  id") {

            val actualEntity = sut.getBillingAccountContact(1)

            then("Junifer_AccountContact should be returned") {
                actualEntity shouldNotBe null
            }
        }
    }

    given("getAccountNumberByContactId") {
        insertBillingAccountContact2(db)
        insertBillingAccount(db)

        `when`("Junifer_Account is queried by contact id") {
            val accountNumber = sut.getAccountNumberByContactId(CONTACT_ID)
            then("Junifer_AccountNumber should be returned") {
                accountNumber shouldNotBe null
                accountNumber shouldBe ACCOUNT_NUMBER_2
            }
        }
    }
})


const val CONTACT_ID = 9999L
const val CONTACT_VERSION_ID = 1111L
const val CUSTOMER_ID = 3333L
const val ACCOUNT_ID = 4444L
const val ACCOUNT_ID_2 = 4666L
const val ACCOUNT_NUMBER = "**********"
const val ACCOUNT_NUMBER_2 = "**********"
const val TITLE_ID = 5555L
const val TITLE_INTERNAL_KEY = "Mr"

fun insertContactVersion(db: DSLContext) {
    db.insertInto(JUNIFER__CONTACTVERSION)
        .set(JUNIFER__CONTACTVERSION.ID, CONTACT_VERSION_ID)
        .set(JUNIFER__CONTACTVERSION.CONTACTFK, CONTACT_ID)
        .set(JUNIFER__CONTACTVERSION.ADDRESSFK, 1)
        .set(JUNIFER__CONTACTVERSION.CAREOF, "careOf")
        .set(JUNIFER__CONTACTVERSION.DATEOFBIRTHDT, LocalDate.of(1980, 1, 1))
        .set(JUNIFER__CONTACTVERSION.DELETEFL, "N")
        .set(JUNIFER__CONTACTVERSION.TITLEFK, TITLE_ID)
        .set(JUNIFER__CONTACTVERSION.EMAIL, "<EMAIL>")
        .set(JUNIFER__CONTACTVERSION.FORENAME, "Adam")
        .set(JUNIFER__CONTACTVERSION.SURNAME, "Smith")
        .set(JUNIFER__CONTACTVERSION.FROMDTTM, OffsetDateTime.of(2020, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__CONTACTVERSION.TODTTM, OffsetDateTime.of(9999, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .execute()
}

fun insertTitle(db: DSLContext) {
    db.insertInto(JUNIFER__TITLE)
        .set(JUNIFER__TITLE.ID, TITLE_ID)
        .set(JUNIFER__TITLE.INTERNALKEY, "Mr")
        .execute()
}

fun insertCustomerContact(db: DSLContext) {
    db.insertInto(JUNIFER__CUSTOMERCONTACT)
        .set(JUNIFER__CUSTOMERCONTACT.ID, 1)
        .set(JUNIFER__CUSTOMERCONTACT.CONTACTFK, 1)
        .set(JUNIFER__CUSTOMERCONTACT.CUSTOMERFK, 1)
        .set(JUNIFER__CUSTOMERCONTACT.FROMDTTM, OffsetDateTime.of(2020, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__CUSTOMERCONTACT.TODTTM, OffsetDateTime.of(2023, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__CUSTOMERCONTACT.REFERENCE, "reference")
        .set(JUNIFER__CUSTOMERCONTACT.VERSIONNO, 1)
        .execute()
}

fun insertBillingAccountContact(db: DSLContext) {
    db.insertInto(JUNIFER__ACCOUNTCONTACT)
        .set(JUNIFER__ACCOUNTCONTACT.ID, 1)
        .set(JUNIFER__ACCOUNTCONTACT.CONTACTFK, 1)
        .set(JUNIFER__ACCOUNTCONTACT.ACCOUNTFK, 1)
        .set(JUNIFER__ACCOUNTCONTACT.FROMDTTM, OffsetDateTime.of(2020, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.TODTTM, OffsetDateTime.of(2023, 1, 1, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.REFERENCE, "reference")
        .set(JUNIFER__ACCOUNTCONTACT.VERSIONNO, 1)
        .execute()
}

fun insertContact(db: DSLContext) {
    db.insertInto(JUNIFER__CONTACT)
        .set(JUNIFER__CONTACT.ID, CONTACT_ID)
        .set(JUNIFER__CONTACT.CONTACTTYPEFK, 1)
        .set(JUNIFER__CONTACT.DELETEFL, "Y")
        .set(JUNIFER__CONTACT.DESCRIPTION, "description")
        .set(JUNIFER__CONTACT.PARTITIONID, 1L)
        .set(JUNIFER__CONTACT.REFERENCE, "reference")
        .set(JUNIFER__CONTACT.VERSIONNO, 1)
        .execute()
}

fun inserContactType(db: DSLContext) {
    db.insertInto(JUNIFER__CONTACTTYPE)
        .set(JUNIFER__CONTACTTYPE.ID, 1)
        .set(JUNIFER__CONTACTTYPE.INTERNALKEY, "Residential")
        .execute()
}

fun insertBillingAccountContact2(db: DSLContext) {
    db.insertInto(JUNIFER__ACCOUNTCONTACT)
        .set(JUNIFER__ACCOUNTCONTACT.ID, 1)
        .set(JUNIFER__ACCOUNTCONTACT.CONTACTFK, CONTACT_ID)
        .set(JUNIFER__ACCOUNTCONTACT.ACCOUNTFK, ACCOUNT_ID)
        .set(JUNIFER__ACCOUNTCONTACT.FROMDTTM, OffsetDateTime.of(2015, 11, 6, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.TODTTM, OffsetDateTime.of(9999, 2, 24, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.DELETEFL, "N")
        .set(JUNIFER__ACCOUNTCONTACT.CANCELFL, "N")
        .set(JUNIFER__ACCOUNTCONTACT.PRIMARYFL, "Y")
        .set(JUNIFER__ACCOUNTCONTACT.VERSIONNO, 1)
        .execute()

    db.insertInto(JUNIFER__ACCOUNTCONTACT)
        .set(JUNIFER__ACCOUNTCONTACT.ID, 2)
        .set(JUNIFER__ACCOUNTCONTACT.CONTACTFK, CONTACT_ID)
        .set(JUNIFER__ACCOUNTCONTACT.ACCOUNTFK, ACCOUNT_ID_2)
        .set(JUNIFER__ACCOUNTCONTACT.FROMDTTM, OffsetDateTime.of(2021, 11, 6, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.TODTTM, OffsetDateTime.of(9999, 2, 24, 0, 0, 0, 0, OffsetDateTime.now().offset))
        .set(JUNIFER__ACCOUNTCONTACT.DELETEFL, "N")
        .set(JUNIFER__ACCOUNTCONTACT.CANCELFL, "N")
        .set(JUNIFER__ACCOUNTCONTACT.PRIMARYFL, "Y")
        .set(JUNIFER__ACCOUNTCONTACT.VERSIONNO, 1)
        .execute()
}

fun insertBillingAccount(db: DSLContext) {
    db.insertInto(JUNIFER__ACCOUNT)
        .set(JUNIFER__ACCOUNT.ID, ACCOUNT_ID)
        .set(JUNIFER__ACCOUNT.CUSTOMERFK, CUSTOMER_ID)
        .set(JUNIFER__ACCOUNT.NUMBER, ACCOUNT_NUMBER)
        .execute()

    db.insertInto(JUNIFER__ACCOUNT)
        .set(JUNIFER__ACCOUNT.ID, ACCOUNT_ID_2)
        .set(JUNIFER__ACCOUNT.CUSTOMERFK, CUSTOMER_ID)
        .set(JUNIFER__ACCOUNT.NUMBER, ACCOUNT_NUMBER_2)
        .execute()
}
