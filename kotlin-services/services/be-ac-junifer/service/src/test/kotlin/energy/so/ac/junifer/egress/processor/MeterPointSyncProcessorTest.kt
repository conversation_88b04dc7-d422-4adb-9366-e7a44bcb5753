package energy.so.ac.junifer.egress.processor

import com.google.protobuf.NullValue
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointSupplyStatusRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.egress.exceptions.SyncFixedDateDelayException
import energy.so.ac.junifer.fixtures.AddressPrecannedData.CORE_PROPERTY_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferProperty
import energy.so.ac.junifer.fixtures.AddressPrecannedData.propertySyncEvent
import energy.so.ac.junifer.fixtures.AssetPrecannedData.assetSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPAN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPRN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.mpanSupplyStatusEvent
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.mprnSupplyStatusEvent
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMpanSupplyStatus
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.testJuniferMprnSupplyStatus
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.DCC_SERVICE_STATUS
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.FROM_DT
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_ASSET_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_SUPPLY_PERIOD_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_MPAN_CONFIG_PERIOD_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_MPAN_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_MPRN_CONFIG_PERIOD_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_MPRN_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.MEASUREMENT_TYPE
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_SERVICE_TYPE
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_SERVICE_TYPE_FK
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.OPERATION_TYPE
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.SUPPLY_START_DT
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.TO_DT
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.UK_GSP_GROUP
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.UK_GSP_GROUP_FK
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.UK_PROFILE_CLASS
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.createMeterPointSyncWithNullProperty
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.createMeterPointWithNullSupplyStartDateSync
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.meterPointSupplyPeriodSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.meterPointSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.meterPointSyncResponse
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.mpanConfigPeriodSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.mpanSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.mprnConfigPeriodSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.mprnSyncEvent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.patchMeterPointSync
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferGasMeterPoint
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMeterPoint
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMeterPointServiceType
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMeterPointSupplyPeriod
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMpan
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMpanConfigPeriod
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMprn
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMprnConfigPeriod
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferUkGspGroup
import energy.so.ac.junifer.mapping.EntityIdentifier.METER_POINT
import energy.so.ac.junifer.mapping.EntityIdentifier.PROPERTY
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.assets.sync.v2.copy
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.nullableString
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

class MeterPointSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val mockMeterPointSupplyStatusRepository = mockk<JuniferMeterPointSupplyStatusRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val meterPointSyncProcessor = MeterPointSyncProcessor(
        mockMapper,
        mockMeterPointRepository,
        mockMeterPointSupplyStatusRepository,
        mockSyncClient
    )

    afterEach {
        clearMocks(mockMapper, mockMeterPointRepository, mockSyncClient)
    }

    given("no existing mapped meter point and an existing junifer meter point linked to mpan") {

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
        every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType

        and("mpan supply status is not active") {
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus.copy(
                supplystatus = "NotSupplied"
            )
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            `when`("a meter point event is generated") {
                coEvery { mockSyncClient.syncMeterPointEntity(createMeterPointWithNullSupplyStartDateSync) } returns meterPointSyncResponse
                justRun {
                    mockMapper.createCoreMapping(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        METER_POINT_ID.toString()
                    )
                }

                meterPointSyncProcessor.process(meterPointSyncEvent)

                then("a new meter point should be created") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }

                    verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }

                    verify {
                        mockMapper.createCoreMapping(
                            METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            METER_POINT_ID.toString()
                        )
                    }
                    coVerify { mockSyncClient.syncMeterPointEntity(createMeterPointWithNullSupplyStartDateSync) }

                    verify { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                    verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
                    verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                    verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                    verify(exactly = 0) {
                        mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(
                            JUNIFER_METER_POINT_ID
                        )
                    }
                }
            }
        }
    }

    given("no existing mapped meter point and an existing junifer meter point linked to mprn") {

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferGasMeterPoint
        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod

        every { mockMeterPointRepository.getMeterPointByMprnId(JUNIFER_MPRN_ID) } returns testJuniferGasMeterPoint

        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup

        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType

        and("current mprn supply status is inactive") {
            every { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMprnSupplyStatus.copy(
                supplystatus = "NotSupplied"
            )

            `when`("a meter point event is generated") {
                val mprnSync = createMeterPointWithNullSupplyStartDateSync.copy {
                    meterPointEntity = meterPointEntity.copy {
                        ukProfileClass = nullableString { null_ = NullValue.NULL_VALUE }
                        type = nullableString { value = "MPRN" }
                    }
                }

                coEvery {
                    mockSyncClient.syncMeterPointEntity(mprnSync)
                } returns meterPointSyncResponse
                justRun {
                    mockMapper.createCoreMapping(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        METER_POINT_ID.toString()
                    )
                }

                meterPointSyncProcessor.process(meterPointSyncEvent)

                then("a new meter point should be created") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }

                    verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }

                    verify {
                        mockMapper.createCoreMapping(
                            METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            METER_POINT_ID.toString()
                        )
                    }
                    coVerify { mockSyncClient.syncMeterPointEntity(mprnSync) }

                    verify { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) }
                    verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
                    verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                    verify { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) }
                    verify(exactly = 0) {
                        mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(
                            JUNIFER_METER_POINT_ID
                        )
                    }
                }
            }
        }
    }

    given("an existing mapped meter point and an existing junifer meter point") {

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        and("no meter point supply period but mpan supply period exists") {
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns null
            every { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) } returns SUPPLY_START_DT

            `when`("a meter point event is generated") {

                coEvery { mockSyncClient.syncMeterPointEntity(any()) } returns meterPointSyncResponse

                meterPointSyncProcessor.process(meterPointSyncEvent)

                then("meter point should be patched") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                    verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) }
                    coVerify { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) }
                }
            }
        }
    }

    given("an existing mapped meter point and an Mpan event is created") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) } returns testJuniferMpan

        and("no meter point supply period but mpan config period exists") {
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns null
            every { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) } returns SUPPLY_START_DT

            `when`("a meter point event is generated") {

                val meterPointEntityRequestWithChangeOfTenancy = patchMeterPointSync.copy {
                    meterPointEntity = this.meterPointEntity.copy {
                        changeOfTenancyFl = true.toNullableBoolean()
                        measurementType = MEASUREMENT_TYPE.toNullableString()
                    }
                }
                coEvery {
                    mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithChangeOfTenancy)
                } returns meterPointSyncResponse

                meterPointSyncProcessor.process(mpanSyncEvent)

                then("meter point should be patched") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getMpan(JUNIFER_MPAN_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                    verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                    verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) }
                    coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithChangeOfTenancy) }
                }
            }
        }
    }

    given("an existing mapped meter point and an MpanConfigPeriod without gsp group") {
        val mpanConfigPeriodWithoutGspGroup = testJuniferMpanConfigPeriod.copy(ukgspgroupfk = null)

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns mpanConfigPeriodWithoutGspGroup

        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns mpanConfigPeriodWithoutGspGroup

        every { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType


        `when`("an MpanConfigPeriod event is generated") {

            val meterPointEntityRequestWithMpanConfigPeriodData = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy {
                    ukProfileClass = UK_PROFILE_CLASS.toNullableString()
                    serviceType = METER_POINT_SERVICE_TYPE.toNullableString()
                    dccServiceStatus = DCC_SERVICE_STATUS.toNullableString()
                    operationType = OPERATION_TYPE.toNullableString()
                    toDt = mpanConfigPeriodWithoutGspGroup.todt.toNullableTimestamp()
                    fromDt = mpanConfigPeriodWithoutGspGroup.fromdt.toNullableTimestamp()
                }
            }

            coEvery {
                mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMpanConfigPeriodData)
            } returns meterPointSyncResponse
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("meter point should be patched") {

                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                verify(exactly = 0) { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) }

                coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMpanConfigPeriodData) }
            }
        }
    }

    given("an existing mapped meter point and an MpanConfigPeriod with invalid gspGroup") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod

        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod

        every { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } throws EntityNotFoundException("Could not find an Ukgspgroup in the Junifer")

        `when`("an MpanConfigPeriod event is generated") {

            val meterPointEntityRequestWithMpanConfigPeriodData = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy {
                    ukProfileClass = UK_PROFILE_CLASS.toNullableString()
                    ukGspGroup = UK_GSP_GROUP.toNullableString()
                    serviceType = METER_POINT_SERVICE_TYPE.toNullableString()
                    operationType = OPERATION_TYPE.toNullableString()
                }
            }

            coEvery {
                mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMpanConfigPeriodData)
            } returns meterPointSyncResponse

            then("meter point should not be patched") {
                shouldThrow<SyncDelayedException> {
                    meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent)
                }

                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
            }
        }
    }

    given("an existing mapped meter point and an MpanConfigPeriod event is created") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod

        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod

        every { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup

        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType


        `when`("an MpanConfigPeriod event is generated") {

            val meterPointEntityRequestWithMpanConfigPeriodData = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy {
                    ukProfileClass = UK_PROFILE_CLASS.toNullableString()
                    ukGspGroup = UK_GSP_GROUP.toNullableString()
                    serviceType = METER_POINT_SERVICE_TYPE.toNullableString()
                    dccServiceStatus = DCC_SERVICE_STATUS.toNullableString()
                    operationType = OPERATION_TYPE.toNullableString()
                    fromDt = FROM_DT.toNullableTimestamp()
                    toDt = TO_DT.toNullableTimestamp()
                }
            }

            coEvery {
                mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMpanConfigPeriodData)
            } returns meterPointSyncResponse
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("meter point should be patched") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
                verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                verify(exactly = 0) { mockMeterPointRepository.findCurrentMpanSupplyStartDate(JUNIFER_METER_POINT_ID) }

                coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMpanConfigPeriodData) }
            }
        }
    }

    given("an MpanConfigPeriod event for an non existent MpanConfigPeriod in db") {
        every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } throws EntityNotFoundException(
            ""
        )

        `when`("MpanConfigPeriod event is generated") {
            meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MpanConfigPeriod event for an non current MpanConfigPeriod in db") {
        and("is in thr past") {
            every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns null
            `when`("MpanConfigPeriod event is generated") {
                meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent)

                then("event should be ignored") {
                    verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }

                    coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
                }
            }
        }
        and("is in the future") {
            every { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) } returns testJuniferMpanConfigPeriod.copy(
                fromdt = LocalDate.now().plusDays(14)
            )
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns null
            `when`("MpanConfigPeriod event is generated") {
                then("should throw SyncFixedDateDelayException") {
                    shouldThrow<SyncFixedDateDelayException> { meterPointSyncProcessor.process(mpanConfigPeriodSyncEvent) }
                    verify { mockMeterPointRepository.getMpanConfigPeriod(JUNIFER_MPAN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }

                    coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
                }
            }
        }
    }

    given("an MpanSupplyStatus event for a non existent JuniferMpanSupplyStatus in db") {

        every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } throws EntityNotFoundException(
            "Not found"
        )

        `when`("MpanSupplyStatus event is generated") {
            meterPointSyncProcessor.process(mpanSupplyStatusEvent)

            then("event should be ignored") {
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify(exactly = 0) { mockMeterPointRepository.getMeterPointByMpanId(any()) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MpanSupplyStatus event for which there is no JuniferMeterpoint in db") {

        every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) } throws EntityNotFoundException(
            "Not found"
        )

        `when`("MpanSupplyStatus event is generated") {
            then("event should be ignored, exception thrown") {
                shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(mpanSupplyStatusEvent) }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MpanSupplyStatus event for which there is JuniferMeterpoint in db without mapping") {

        every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) } returns testJuniferMeterPoint
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
        every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
        every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus.copy(
            supplystatus = "NotSupplied"
        )
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("MpanSupplyStatus event is generated") {
            coEvery { mockSyncClient.syncMeterPointEntity(createMeterPointWithNullSupplyStartDateSync) } returns meterPointSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString(),
                    METER_POINT_ID.toString()
                )
            }

            then("event should not be processed, exception thrown") {
                shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(mpanSupplyStatusEvent) }

                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify(exactly = 0) {
                    mockMapper.createCoreMapping(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        METER_POINT_ID.toString()
                    )
                }
            }
        }
    }

    given("an MpanSupplyStatus event for which there is JuniferMeterpoint in db wit mapping") {

        every { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) } returns testJuniferMeterPoint
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()
        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
        every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
        every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("MpanSupplyStatus event is generated") {
            coEvery { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) } returns meterPointSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString(),
                    METER_POINT_ID.toString()
                )
            }

            meterPointSyncProcessor.process(mpanSupplyStatusEvent)

            then("event should be processed") {
                coVerify { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) }
                verify { mockMeterPointSupplyStatusRepository.getMpanSupplyStatus(JUNIFER_MPAN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMpanId(MeterPointHistoryPrecannedData.JUNIFER_MPAN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
            }
        }
    }

    given("an MprnSupplyStatus event for a non existent JuniferMprnSupplyStatus in db") {

        every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } throws EntityNotFoundException(
            "Not found"
        )

        `when`("MpanSupplyStatus event is generated") {
            meterPointSyncProcessor.process(mprnSupplyStatusEvent)

            then("event should be ignored") {
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify(exactly = 0) { mockMeterPointRepository.getMeterPointByMprnId(any()) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MprnSupplyStatus event for which there is no JuniferMeterpoint in db") {

        every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns testJuniferMprnSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) } throws EntityNotFoundException(
            "Not found"
        )

        `when`("MpanSupplyStatus event is generated") {
            then("event should be ignored, exception thrown") {
                shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(mprnSupplyStatusEvent) }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) }

                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MprnSupplyStatus event for which there is JuniferMeterpoint in db without mapping") {

        every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns testJuniferMprnSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) } returns testJuniferGasMeterPoint
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
        every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
        every { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMprnSupplyStatus
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("MpanSupplyStatus event is generated") {
            val patchMeterPoint = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy { type = nullableString { value = "MPRN" } }
            }

            then("event should not be processed, exception thrown") {
                shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(mprnSupplyStatusEvent) }
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(patchMeterPoint) }
            }
        }
    }

    given("an MprnSupplyStatus event for which there is JuniferMeterpoint in db with mapping") {

        every { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) } returns testJuniferMprnSupplyStatus
        every { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) } returns testJuniferGasMeterPoint
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()
        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
        every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
        every { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMprnSupplyStatus
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("MpanSupplyStatus event is generated") {
            val patchMeterPoint = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy { type = nullableString { value = "MPRN" } }
            }

            coEvery {
                mockSyncClient.syncMeterPointEntity(patchMeterPoint)
            } returns meterPointSyncResponse
            meterPointSyncProcessor.process(mprnSupplyStatusEvent)

            then("meterpoint should be patched") {
                verify { mockMeterPointSupplyStatusRepository.getMprnSupplyStatus(JUNIFER_MPRN_SUPPLY_STATUS_ID) }
                verify { mockMeterPointRepository.getMeterPointByMprnId(MeterPointHistoryPrecannedData.JUNIFER_MPRN_ID) }
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify { mockSyncClient.syncMeterPointEntity(patchMeterPoint) }
            }
        }
    }

    given("an MprnConfigPeriod event for an non existent MprnConfigPeriod in db") {
        every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } throws EntityNotFoundException(
            ""
        )

        `when`("MprnConfigPeriod event is generated") {
            meterPointSyncProcessor.process(mprnConfigPeriodSyncEvent)

            then("event should be ignored") {
                verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
            }
        }
    }

    given("an MprnConfigPeriod event for an non current MprnConfigPeriod in db") {
        and("is in the past") {
            every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod
            every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns null

            `when`("MprnConfigPeriod event is generated") {
                meterPointSyncProcessor.process(mprnConfigPeriodSyncEvent)

                then("event should be ignored") {
                    verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }

                    coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
                }
            }
        }
        and("is in the future") {
            every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod.copy(
                fromdt = LocalDate.now().plusDays(14)
            )
            every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns null

            `when`("MprnConfigPeriod event is generated") {
                then("should throw SyncFixedDateDelayException") {
                    shouldThrow<SyncFixedDateDelayException> { meterPointSyncProcessor.process(mprnConfigPeriodSyncEvent) }

                    verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }

                    coVerify(exactly = 0) { mockSyncClient.syncMeterPointEntity(any()) }
                }
            }
        }
    }

    given("an existing mapped meter point and an Mprn event is created") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint.copy(
            utilitymarketfk = 2L
        )

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) } returns testJuniferMprn

        `when`("a meter point event is generated") {

            val meterPointEntityRequestWithChangeOfTenancy = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy {
                    this.type = nullableString { value = "MPRN" }
                    changeOfTenancyFl = true.toNullableBoolean()
                }
            }
            coEvery {
                mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithChangeOfTenancy)
            } returns meterPointSyncResponse

            every { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMprnSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            meterPointSyncProcessor.process(mprnSyncEvent)

            then("meter point should be patched") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMprn(JUNIFER_MPRN_ID) }
                verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                verify(exactly = 0) { mockMeterPointRepository.findCurrentMprnSupplyStartDate(JUNIFER_METER_POINT_ID) }
                coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithChangeOfTenancy) }
            }
        }
    }

    given("an existing mapped meter point and an MprnConfigPeriod event is created") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferGasMeterPoint

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) } returns testJuniferMprnConfigPeriod

        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) } returns testJuniferMprnConfigPeriod

        every { mockMeterPointRepository.getMeterPointByMprnId(JUNIFER_MPRN_ID) } returns testJuniferGasMeterPoint

        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup

        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType

        and("no meter point supply period but mprn config period exists") {
            every { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMprnSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns null
            every { mockMeterPointRepository.findCurrentMprnSupplyStartDate(JUNIFER_METER_POINT_ID) } returns SUPPLY_START_DT

            `when`("an MprnConfigPeriod event is generated") {
                val meterPointEntityRequestWithMprnConfigPeriodData = patchMeterPointSync.copy {
                    meterPointEntity = this.meterPointEntity.copy {
                        ukGspGroup = UK_GSP_GROUP.toNullableString()
                        serviceType = METER_POINT_SERVICE_TYPE.toNullableString()
                        operationType = OPERATION_TYPE.toNullableString()
                        fromDt = FROM_DT.toNullableTimestamp()
                        toDt = TO_DT.toNullableTimestamp()
                        dccServiceStatus = DCC_SERVICE_STATUS.toNullableString()
                        type = nullableString { value = "MPRN" }
                    }
                }

                coEvery {
                    mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMprnConfigPeriodData)
                } returns meterPointSyncResponse

                meterPointSyncProcessor.process(mprnConfigPeriodSyncEvent)

                then("meter point should be patched") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                    verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                    verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriod(JUNIFER_MPRN_CONFIG_PERIOD_ID) }
                    verify { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPRN_ID) }
                    verify { mockMeterPointRepository.getMeterPointByMprnId(JUNIFER_MPRN_ID) }
                    verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
                    verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointSupplyStatusRepository.findCurrentMprnSupplyStatus(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                    verify { mockMeterPointRepository.findCurrentMprnSupplyStartDate(JUNIFER_METER_POINT_ID) }
                    coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMprnConfigPeriodData) }
                }
            }
        }
    }

    given("an existing mapped meter point and an MeterPointSupplyPeriod event is created") {
        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint

        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty

        every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.findMeterPointSupplyPeriod(JUNIFER_METER_POINT_SUPPLY_PERIOD_ID) } returns testJuniferMeterPointSupplyPeriod
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("an MeterPointSupplyPeriod event is generated") {

            val meterPointEntityRequestWithMeterPointSupplyPeriodData = patchMeterPointSync.copy {
                meterPointEntity = this.meterPointEntity.copy {
                    supplyStartDate = SUPPLY_START_DT.toNullableTimestamp()
                }
            }

            coEvery {
                mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMeterPointSupplyPeriodData)
            } returns meterPointSyncResponse

            meterPointSyncProcessor.process(meterPointSupplyPeriodSyncEvent)

            then("meter point should be patched") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findMeterPointSupplyPeriod(JUNIFER_METER_POINT_SUPPLY_PERIOD_ID) }
                verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }
                coVerify { mockSyncClient.syncMeterPointEntity(meterPointEntityRequestWithMeterPointSupplyPeriodData) }
            }
        }
    }

    given("no existing mapped meter point and an existing junifer meter point and no property") {

        every {
            mockMapper.getCoreId(
                METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns null

        every { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPoint
        every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns null
        every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
        every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
        every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
        every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
        every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
        every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
        every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
        every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

        `when`("a meter point event is generated") {

            coEvery { mockSyncClient.syncMeterPointEntity(createMeterPointSyncWithNullProperty) } returns meterPointSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString(),
                    METER_POINT_ID.toString()
                )
            }

            meterPointSyncProcessor.process(meterPointSyncEvent)

            then("a new meter point should be created with null property id") {
                verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }

                verify { mockMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        METER_POINT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncMeterPointEntity(createMeterPointSyncWithNullProperty) }

                verify { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) }
                verify { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) }
                verify { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) }
                verify { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) }
                verify { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) }
            }
        }
    }

    given("property event") {
        and("no existing mapped meter point") {

            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns null

            every { mockMeterPointRepository.getMeterPointByPropertyId(JUNIFER_PROPERTY_ID) } returns testJuniferMeterPoint

            `when`("a property event is generated") {
                meterPointSyncProcessor.process(propertySyncEvent)

                then("event is skipped") {
                    verify {
                        mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString())
                        mockMeterPointRepository.getMeterPointByPropertyId(JUNIFER_PROPERTY_ID)
                    }

                    coVerify(exactly = 0) {
                        mockMapper.createCoreMapping(
                            METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            METER_POINT_ID.toString()
                        )
                        mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockSyncClient.syncMeterPointEntity(any())
                        mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID)
                        mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK)
                        mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK)
                        mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID)
                    }
                }
            }
        }

        and("existing and mapped meter point") {

            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()

            every {
                mockMapper.getCoreId(
                    PROPERTY,
                    JUNIFER_PROPERTY_ID.toString()
                )
            } returns CORE_PROPERTY_ID.toString()

            every { mockMeterPointRepository.getMeterPointByPropertyId(JUNIFER_PROPERTY_ID) } returns testJuniferMeterPoint
            every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
            every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
            every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
            every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
            every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
            every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            `when`("a property event is generated") {

                coEvery { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) } returns meterPointSyncResponse
                meterPointSyncProcessor.process(propertySyncEvent)

                then("meter point should be patched with property id") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }

                    verify { mockMeterPointRepository.getMeterPointByPropertyId(JUNIFER_PROPERTY_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }

                    coVerify {
                        mockMapper.getCoreId(
                            PROPERTY,
                            JUNIFER_PROPERTY_ID.toString()
                        )
                    }
                    coVerify { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) }
                }
            }
        }
    }

    given("asset event") {
        and("meterpoint not found") {
            every { mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID) } returns null

            `when`("an asset event is generated") {
                then("throw SyncDelayedException") {

                    shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(assetSyncEvent) }

                    verify {
                        mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID)
                    }

                    coVerify(exactly = 0) {
                        mockMapper.createCoreMapping(
                            METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            METER_POINT_ID.toString()
                        )
                        mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockSyncClient.syncMeterPointEntity(any())
                        mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID)
                        mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK)
                        mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK)
                        mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID)
                    }
                }
            }
        }

        and("no existing mapped meter point") {

            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns null

            every { mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID) } returns testJuniferMeterPoint

            `when`("an asset event is generated") {
                then("throw SyncDelayedException") {

                    shouldThrow<SyncDelayedException> { meterPointSyncProcessor.process(assetSyncEvent) }

                    verify {
                        mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString())
                        mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID)
                    }

                    coVerify(exactly = 0) {
                        mockMapper.createCoreMapping(
                            METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            METER_POINT_ID.toString()
                        )
                        mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockSyncClient.syncMeterPointEntity(any())
                        mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID)
                        mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID)
                        mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK)
                        mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK)
                        mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID)
                    }
                }
            }
        }

        and("existing and mapped meter point") {

            every {
                mockMapper.getCoreId(
                    METER_POINT,
                    JUNIFER_METER_POINT_ID.toString()
                )
            } returns METER_POINT_ID.toString()

            every {
                mockMapper.getCoreId(
                    PROPERTY,
                    JUNIFER_PROPERTY_ID.toString()
                )
            } returns CORE_PROPERTY_ID.toString()

            every { mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID) } returns testJuniferMeterPoint
            every { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) } returns juniferProperty
            every { mockMeterPointRepository.getMpanByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMpan
            every { mockMeterPointRepository.getMpanConfigPeriodByMpanId(JUNIFER_MPAN_ID) } returns testJuniferMpanConfigPeriod
            every { mockMeterPointRepository.getMprnByMeterPointId(JUNIFER_METER_POINT_ID) } returns testJuniferMprn
            every { mockMeterPointRepository.getMprnConfigPeriodByMprnId(JUNIFER_MPAN_ID) } returns testJuniferMprnConfigPeriod
            every { mockMeterPointRepository.getUkGspGroup(UK_GSP_GROUP_FK) } returns testJuniferUkGspGroup
            every { mockMeterPointRepository.getMeterPointServiceType(METER_POINT_SERVICE_TYPE_FK) } returns testJuniferMeterPointServiceType
            every { mockMeterPointSupplyStatusRepository.findCurrentMpanSupplyStatus(JUNIFER_METER_POINT_ID) } returns testJuniferMpanSupplyStatus
            every { mockMeterPointRepository.findCurrentMeterPointSupplyPeriod(JUNIFER_METER_POINT_ID) } returns testJuniferMeterPointSupplyPeriod

            `when`("an asset event is generated") {

                coEvery { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) } returns meterPointSyncResponse
                meterPointSyncProcessor.process(assetSyncEvent)

                then("meter point should be patched with property id") {
                    verify { mockMapper.getCoreId(METER_POINT, JUNIFER_METER_POINT_ID.toString()) }

                    verify { mockMeterPointRepository.getMeterPointByAssetId(JUNIFER_ASSET_ID) }
                    verify { mockMeterPointRepository.getPropertyByMeterPointId(JUNIFER_METER_POINT_ID) }

                    coVerify {
                        mockMapper.getCoreId(
                            PROPERTY,
                            JUNIFER_PROPERTY_ID.toString()
                        )
                    }
                    coVerify { mockSyncClient.syncMeterPointEntity(patchMeterPointSync) }
                }
            }
        }
    }


})
