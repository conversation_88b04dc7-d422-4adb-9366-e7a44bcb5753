package energy.so.ac.junifer.admin.services

import energy.so.ac.junifer.admin.database.repositories.AccountRepository
import energy.so.ac.junifer.admin.database.repositories.ResyncRepository
import energy.so.ac.junifer.config.SyncOptimizationConfig
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import energy.so.ac.junifer.egress.database.repositories.envId
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.JUNIFER_ACCOUNT_CREDIT_ID_1
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.JUNIFER_ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_ADDRESS_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_ADDR_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.JUNIFER_CUSTOMER_PSR_DFN_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.JUNIFER_NOTE_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.JUNIFER_PAYMENT_SCHEDULE_SEASONAL_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.JUNIFER_PRODUCT_DFN_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.JUNIFER_TICKET_HISTORY_ID
import energy.so.ac.junifer.fixtures.AdminPrecannedData.loneReferences
import energy.so.ac.junifer.fixtures.AdminPrecannedData.resyncAccountsRequest
import energy.so.ac.junifer.fixtures.AdminPrecannedData.testAssetReferences
import energy.so.ac.junifer.fixtures.AdminPrecannedData.testCustomerReferences
import energy.so.ac.junifer.fixtures.AdminPrecannedData.testPaymentReferences
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILLING_PERIOD_ID
import energy.so.ac.junifer.fixtures.BillPrecannedData.JUNIFER_BILL_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.CORE_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_NUMBER
import energy.so.ac.junifer.fixtures.ContactPrecannedData.JUNIFER_CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.JUNIFER_CUSTOMER_CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_PSR_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOM_VULNERABILITY_ID
import energy.so.ac.junifer.fixtures.CustomerPropertyPrecannedData.JUNIFER_CUSTOMER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.JUNIFER_GO_CARDLESS_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PAYMENT_SCHEDULE_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PAYMENT_SCHEDULE_PERIOD_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_BUNDLE_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_BUNDLE_ID
import energy.so.ac.junifer.fixtures.JUNIFER_TICKET_ID
import energy.so.ac.junifer.fixtures.MeterMeterPointRelPrecannedData.JUNIFER_METER_METER_POINT_REL_ID
import energy.so.ac.junifer.fixtures.MeterPointElectricityPrecannedData.JUNIFER_MPAN_ID
import energy.so.ac.junifer.fixtures.MeterPointGasPrecannedData.JUNIFER_MPRN_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPAN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointHistoryPrecannedData.JUNIFER_MPRN_SUPPLY_STATUS_ID
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.LOCKED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.NOT_LOCKED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.mapOfAccounts
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_PLAN_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_TXN_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_GO_CARDLESS_DD_COLLECTION_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.admin.resyncAccountRequest
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.enums.OperationType
import energy.so.commons.model.tables.pojos.SyncEvent
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class ResyncServiceTest : BehaviorSpec({

    val mockResyncRepo = mockk<ResyncRepository>()
    val mockSyncEventRepo = mockk<SyncEventRepository>()
    val mockAccountRepository = mockk<AccountRepository>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val mockSyncOptimizationConfig = SyncOptimizationConfig(
        envId = envId
    )

    val sut = ResyncService(
        mockResyncRepo,
        mockSyncEventRepo,
        mockAccountRepository,
        inMemoryMapper,
        mockSyncOptimizationConfig
    )

    afterEach {
        confirmVerified(mockResyncRepo, mockSyncEventRepo)
        clearMocks(mockResyncRepo, mockSyncEventRepo)
    }

    given("existing customer data on the database") {
        and("juniferAccountId is given") {
            every { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) } returns testCustomerReferences
            every { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) } returns false
            every { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) } returns mockk()
            justRun { mockSyncEventRepo.batchInsertEvents(any()) }
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                CORE_ACCOUNT_ID.toString()
            )

            `when`("a request to resync customer data is received") {

                sut.resyncAccount(resyncAccountRequest {
                    this.juniferAccountId = JUNIFER_ACCOUNT_ID
                    this.syncCustomer = true
                })

                then("sync events for the customer should be inserted") {

                    verify { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) }
                    verify { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) }
                    verify { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) }
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_ID, EventType.ACCOUNT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_ID, EventType.CUSTOMER)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_PRODUCT_BUNDLE_ID, EventType.PRODUCT_BUNDLE)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_CONTACT_ID, EventType.CUSTOMER_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_CONTACT_ID, EventType.ACCOUNT_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CONTACT_ID, EventType.CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ADDRESS_ID, EventType.ADDRESS)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PROPERTY_ID, EventType.CUSTOMER_PROPERTY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PSR_ID, EventType.UK_VULN_CUST_PSR)
                    verifySingleEvent(
                        mockSyncEventRepo,
                        JUNIFER_CUSTOM_VULNERABILITY_ID,
                        EventType.CUSTOM_VULNERABILITY
                    )
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_ID, EventType.TICKET)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_HISTORY_ID, EventType.TICKET_HISTORY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_NOTE_ID, EventType.NOTE)
                }
            }
        }
        and("juniferAccountNumber is given") {
            every { mockAccountRepository.findIdByAccountNumber(JUNIFER_ACCOUNT_NUMBER) } returns JUNIFER_ACCOUNT_ID
            every { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) } returns testCustomerReferences
            every { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) } returns false
            every { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) } returns mockk()
            justRun { mockSyncEventRepo.batchInsertEvents(any()) }
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                CORE_ACCOUNT_ID.toString()
            )

            `when`("a request to resync customer data is received") {

                sut.resyncAccount(resyncAccountRequest {
                    this.juniferAccountNumber = JUNIFER_ACCOUNT_NUMBER
                    this.syncCustomer = true
                })

                then("sync events for the customer should be inserted") {

                    verify { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) }
                    verify { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) }
                    verify { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) }
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_ID, EventType.ACCOUNT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_ID, EventType.CUSTOMER)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_PRODUCT_BUNDLE_ID, EventType.PRODUCT_BUNDLE)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_CONTACT_ID, EventType.CUSTOMER_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_CONTACT_ID, EventType.ACCOUNT_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CONTACT_ID, EventType.CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ADDRESS_ID, EventType.ADDRESS)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PROPERTY_ID, EventType.CUSTOMER_PROPERTY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PSR_ID, EventType.UK_VULN_CUST_PSR)
                    verifySingleEvent(
                        mockSyncEventRepo,
                        JUNIFER_CUSTOM_VULNERABILITY_ID,
                        EventType.CUSTOM_VULNERABILITY
                    )
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_ID, EventType.TICKET)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_HISTORY_ID, EventType.TICKET_HISTORY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_NOTE_ID, EventType.NOTE)
                }
            }
        }
    }

    given("existing asset data on the database") {

        every { mockResyncRepo.getAssetReferences(JUNIFER_ACCOUNT_ID) } returns testAssetReferences
        every { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) } returns true
        every { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) } returns mockk()
        justRun { mockSyncEventRepo.batchInsertEvents(any()) }
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.BILLING_ACCOUNT,
            JUNIFER_ACCOUNT_ID.toString(),
            CORE_ACCOUNT_ID.toString()
        )

        `when`("a request to resync asset data is received") {

            sut.resyncAccount(resyncAccountRequest {
                this.juniferAccountId = JUNIFER_ACCOUNT_ID
                this.syncAssets = true
            })

            then("sync events for the customer should be inserted") {

                verify { mockResyncRepo.getAssetReferences(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) }
                verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_ID, EventType.ACCOUNT)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_METER_POINT_ID, EventType.METER_POINT)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_REGISTER_ID, EventType.METER_REGISTER)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_METER_ID, EventType.METER)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_MPAN_ID, EventType.MPAN)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_MPAN_SUPPLY_STATUS_ID, EventType.MPAN_SUPPLY_STATUS)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_MPRN_ID, EventType.MPRN)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_MPRN_SUPPLY_STATUS_ID, EventType.MPRN_SUPPLY_STATUS)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PROPERTY_ID, EventType.PROPERTY)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_ADDR_ID, EventType.ADDRESS)
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_METER_METER_POINT_REL_ID,
                    EventType.METER_POINT_PHY_TIME_SERIES
                )
            }
        }
    }

    given("existing payment data on the database") {

        every { mockResyncRepo.getPaymentReferences(JUNIFER_ACCOUNT_ID) } returns testPaymentReferences
        every { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) } returns true
        every { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) } returns mockk()
        justRun { mockSyncEventRepo.batchInsertEvents(any()) }
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.BILLING_ACCOUNT,
            JUNIFER_ACCOUNT_ID.toString(),
            CORE_ACCOUNT_ID.toString()
        )

        `when`("a request to resync asset data is received") {

            sut.resyncAccount(resyncAccountRequest {
                this.juniferAccountId = JUNIFER_ACCOUNT_ID
                this.syncPayments = true
            })

            then("sync events for the customer should be inserted") {

                verify { mockResyncRepo.getPaymentReferences(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepository.isAccountAddedToWhitelist(JUNIFER_ACCOUNT_ID) }
                verify { mockAccountRepository.addAccountToWhitelist(JUNIFER_ACCOUNT_ID) }
                verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_ID, EventType.ACCOUNT)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PAYMENT_METHOD_ID, EventType.PAYMENT_METHOD)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PAYMENT_PLAN_ID, EventType.PAYMENT_PLAN)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PAYMENT_PLAN_PAYMENT_ID, EventType.PAYMENT_PLAN_PAYMENT)
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_PAYMENT_PLAN_PAYMENT_TXN_ID,
                    EventType.PAYMENT_PLAN_PAYMENT_TXN
                )
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PAYMENT_REQUEST_ID, EventType.PAYMENT_REQUEST)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_TRANSACTION_ID, EventType.ACCOUNT_TRANSACTION)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_BILL_ID, EventType.BILL)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_BILLING_PERIOD_ID, EventType.BILL_PERIOD)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_GO_CARDLESS_DIRECT_DEBIT_ID, EventType.GO_CARDLESS_DD)
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_GO_CARDLESS_DD_COLLECTION_ID,
                    EventType.GO_CARDLESS_DD_COLLECTION
                )
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_PAYMENT_SCHEDULE_PERIOD_ID,
                    EventType.PAYMENT_SCHEDULE_PERIOD
                )
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PAYMENT_SCHEDULE_ID, EventType.PAYMENT_SCHEDULE_ITEM)
                verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_CREDIT_ID_1, EventType.ACCOUNT_CREDIT)
            }
        }
    }

    given("request to sync customer data for a list of accounts") {

        and("corresponding junifer account ids exist") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString(),
                CORE_ACCOUNT_ID.toString()
            )

            every { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) } returns testCustomerReferences
            justRun { mockSyncEventRepo.batchInsertEvents(any()) }

            `when`("resyncAccounts is called") {
                sut.resyncAccounts(resyncAccountsRequest)

                then("accounts sync started") {
                    verify { mockResyncRepo.getCustomerReferences(JUNIFER_ACCOUNT_ID) }

                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_ID, EventType.ACCOUNT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_ID, EventType.CUSTOMER)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_PRODUCT_BUNDLE_ID, EventType.PRODUCT_BUNDLE)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_CONTACT_ID, EventType.CUSTOMER_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ACCOUNT_CONTACT_ID, EventType.ACCOUNT_CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CONTACT_ID, EventType.CONTACT)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_ADDRESS_ID, EventType.ADDRESS)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PROPERTY_ID, EventType.CUSTOMER_PROPERTY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PSR_ID, EventType.UK_VULN_CUST_PSR)
                    verifySingleEvent(
                        mockSyncEventRepo,
                        JUNIFER_CUSTOM_VULNERABILITY_ID,
                        EventType.CUSTOM_VULNERABILITY
                    )
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_ID, EventType.TICKET)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_TICKET_HISTORY_ID, EventType.TICKET_HISTORY)
                    verifySingleEvent(mockSyncEventRepo, JUNIFER_NOTE_ID, EventType.NOTE)
                }
            }
        }

        and("corresponding junifer account ids don't exist") {
            inMemoryMapper.clear()

            `when`("resyncAccounts is called") {
                sut.resyncAccounts(resyncAccountsRequest)

                then("no sync events are generated") {
                    verify(exactly = 0) { mockSyncEventRepo.batchInsertEvents(any()) }
                }
            }
        }
    }

    given("request to sync lone references") {
        every { mockResyncRepo.getLoneRefs() } returns loneReferences
        justRun { mockSyncEventRepo.batchInsertEvents(any()) }

        `when`("a request to resync lone references data is received") {
            sut.resyncLoneReferences()

            then("sync events for the lone references should be inserted") {
                verify { mockResyncRepo.getLoneRefs() }
                verifySingleEvent(mockSyncEventRepo, JUNIFER_CUSTOMER_PSR_DFN_ID, EventType.UK_VULN_CUST_PSR_DFN)
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_PAYMENT_SCHEDULE_SEASONAL_ID,
                    EventType.PAYMENT_SCHEDULE_SEASONAL_DFN
                )
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_HALF_HOUR_RATE_DFN_ID,
                    EventType.HALF_HOUR_RATE_DFN
                )
                verifySingleEvent(
                    mockSyncEventRepo,
                    HALF_HOUR_RATE_DFN_ITEM_ID,
                    EventType.HALF_HOUR_RATE_DFN_ITEM
                )
                verifySingleEvent(mockSyncEventRepo, JUNIFER_PRODUCT_DFN_ID, EventType.PRODUCT_DEFINITION)
                verifySingleEvent(
                    mockSyncEventRepo,
                    JUNIFER_PRODUCT_BUNDLE_DFN_ID,
                    EventType.PRODUCT_BUNDLE_DEFINITION
                )
            }
        }
    }

    given("::addAccountsToWhiteList") {
        val idsList = listOf(LOCKED_ACCOUNT_ID, NOT_LOCKED_ACCOUNT_ID)
        and("no core mapping for ids") {
            inMemoryMapper.clear()

            `when`("call addAccountsToWhiteList") {
                sut.addAccountsToWhiteList(idsList)

                then("nothing is added") {
                    assertSoftly {
                        coVerify(exactly = 0) { mockAccountRepository.addAccountsToWhitelist(any()) }
                    }
                }
            }
        }
        and("core mapping exists for ids") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                LOCKED_ACCOUNT_ID.toString(),
                LOCKED_ACCOUNT_ID.toString()
            )
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.BILLING_ACCOUNT,
                NOT_LOCKED_ACCOUNT_ID.toString(),
                NOT_LOCKED_ACCOUNT_ID.toString()
            )

            coJustRun { mockAccountRepository.addAccountsToWhitelist(any()) }

            `when`("call addAccountsToWhiteList") {
                sut.addAccountsToWhiteList(idsList)

                then("2 accounts added") {
                    coVerify { mockAccountRepository.addAccountsToWhitelist(mapOfAccounts) }
                }
            }
        }
    }
})

fun verifySingleEvent(repo: SyncEventRepository, ref: Any, eventType: EventType, amount: Int = 1) {
    verify(exactly = amount) {
        repo.batchInsertEvents(
            listOf(
                SyncEvent(
                    reference = ref.toString(),
                    eventType = eventType,
                    operationType = OperationType.UPDATE,
                    processedBy = envId
                )
            )
        )
    }
}