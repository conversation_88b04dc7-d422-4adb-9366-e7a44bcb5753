package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferAddressRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.AddressPrecannedData.CORE_ADDRESS_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.CORE_PROPERTY_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_ADDRESS_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_ADDRESS_TYPE_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_COUNTRY_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_PROPERTY_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.JUNIFER_PROPERTY_TYPE_ID
import energy.so.ac.junifer.fixtures.AddressPrecannedData.createPropertySync
import energy.so.ac.junifer.fixtures.AddressPrecannedData.deletePropertySync
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferAddress
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferAddressType
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferCountry
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferProperty
import energy.so.ac.junifer.fixtures.AddressPrecannedData.juniferPropertyType
import energy.so.ac.junifer.fixtures.AddressPrecannedData.patchPropertySync
import energy.so.ac.junifer.fixtures.AddressPrecannedData.propertySyncEvent
import energy.so.ac.junifer.fixtures.AddressPrecannedData.propertySyncResponse
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityIdentifier.ASSET_ADDRESS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDateTime
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class PropertySyncProcessorTest : BehaviorSpec({

    isolationMode = IsolationMode.InstancePerTest

    val mockMapper = mockk<EntityMapper>()
    val mockRepo = mockk<JuniferAddressRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = PropertySyncProcessor(mockMapper, mockSyncClient, mockRepo, 2, 2)

    given("No existing property and property in core") {
        every { mockRepo.findProperty(JUNIFER_PROPERTY_ID) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()
        coEvery {
            mockSyncClient.syncPropertyEntity(deletePropertySync)
        } returns propertySyncResponse

        `when`("Property event registered") {
            processor.process(propertySyncEvent)

            then("Property should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                coVerify { mockSyncClient.syncPropertyEntity(deletePropertySync) }
            }
        }
    }

    given("No existing property and property not core") {
        every { mockRepo.findProperty(JUNIFER_PROPERTY_ID) } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString())
        } returns null

        `when`("Property event registered") {
            processor.process(propertySyncEvent)

            then("Event should be ignored") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Existing property and no property type") {
        every {
            mockRepo.findProperty(JUNIFER_PROPERTY_ID)
        } returns juniferProperty
        every {
            mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString())
        } returns null
        every { mockRepo.findAddress(JUNIFER_ADDRESS_ID) } returns juniferAddress
        every { mockRepo.findAddressType(JUNIFER_ADDRESS_TYPE_ID) } returns juniferAddressType
        every { mockRepo.findCountry(JUNIFER_COUNTRY_ID) } returns juniferCountry
        every { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) } returns null
        every {
            mockMapper.getCoreId(
                ASSET_ADDRESS,
                JUNIFER_ADDRESS_ID.toString()
            )
        } returns CORE_ADDRESS_ID.toString()

        `when`("Property event registered") {
            shouldThrow<SyncDelayedException> { processor.process(propertySyncEvent) }

            then("The event should be delayed") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Existing property and address not existing in Core") {
        every { mockRepo.findProperty(JUNIFER_PROPERTY_ID) } returns juniferProperty
        every { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) } returns juniferPropertyType
        every {
            mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(
                ASSET_ADDRESS,
                JUNIFER_ADDRESS_ID.toString()
            )
        } returns null

        `when`("Property event registered") {
            shouldThrow<AutoDiscardableException> { processor.process(propertySyncEvent) }

            then("The event should be delayed") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Existing property in core") {
        every { mockRepo.findAddress(JUNIFER_ADDRESS_ID) } returns juniferAddress
        every { mockRepo.findAddressType(JUNIFER_ADDRESS_TYPE_ID) } returns juniferAddressType
        every { mockRepo.findCountry(JUNIFER_COUNTRY_ID) } returns juniferCountry
        every { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) } returns juniferPropertyType
        every {
            mockMapper.getCoreId(
                ASSET_ADDRESS,
                JUNIFER_ADDRESS_ID.toString()
            )
        } returns CORE_ADDRESS_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns CORE_PROPERTY_ID.toString()
        coEvery { mockSyncClient.syncPropertyEntity(any()) } returns propertySyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString(),
                CORE_PROPERTY_ID.toString()
            )
        }

        `when`("Property event is registered") {
            every {
                mockRepo.findProperty(JUNIFER_PROPERTY_ID)
            } returns juniferProperty
            processor.process(propertySyncEvent)

            then("The property should be patched") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                coVerify { mockSyncClient.syncPropertyEntity(patchPropertySync) }
            }
        }

        `when`("Property event is registered") {
            every {
                mockRepo.findProperty(JUNIFER_PROPERTY_ID)
            } returns juniferProperty.copy(deletefl = BOOLEAN_TRUE)
            processor.process(propertySyncEvent)

            then("The property should be patched") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PROPERTY,
                        JUNIFER_PROPERTY_ID.toString(), CORE_PROPERTY_ID.toString()
                    )
                }
            }
        }
    }

    given("Existing property not in core") {
        every { mockRepo.findAddress(JUNIFER_ADDRESS_ID) } returns juniferAddress
        every { mockRepo.findAddressType(JUNIFER_ADDRESS_TYPE_ID) } returns juniferAddressType
        every { mockRepo.findCountry(JUNIFER_COUNTRY_ID) } returns juniferCountry
        every { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) } returns juniferPropertyType
        every {
            mockMapper.getCoreId(
                ASSET_ADDRESS,
                JUNIFER_ADDRESS_ID.toString()
            )
        } returns CORE_ADDRESS_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PROPERTY,
                JUNIFER_PROPERTY_ID.toString()
            )
        } returns null
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString(),
                CORE_PROPERTY_ID.toString()
            )
        }

        `when`("Property event is registered") {
            every {
                mockRepo.findProperty(JUNIFER_PROPERTY_ID)
            } returns juniferProperty
            coEvery { mockSyncClient.syncPropertyEntity(createPropertySync) } returns propertySyncResponse
            processor.process(propertySyncEvent)

            then("The property should be created") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                coVerify { mockSyncClient.syncPropertyEntity(createPropertySync) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PROPERTY,
                        JUNIFER_PROPERTY_ID.toString(), CORE_PROPERTY_ID.toString()
                    )
                }
            }
        }

        `when`("Property delete event is registered") {
            every {
                mockRepo.findProperty(JUNIFER_PROPERTY_ID)
            } returns juniferProperty.copy(deletefl = BOOLEAN_TRUE)
            coEvery { mockSyncClient.syncPropertyEntity(any()) } returns propertySyncResponse
            processor.process(propertySyncEvent)

            then("The property should be created") {
                verify { mockRepo.findProperty(JUNIFER_PROPERTY_ID) }
                verify { mockRepo.findPropertyType(JUNIFER_PROPERTY_TYPE_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PROPERTY, JUNIFER_PROPERTY_ID.toString()) }
                verify { mockMapper.getCoreId(ASSET_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                coVerify {
                    mockSyncClient.syncPropertyEntity(withArg {
                        it.propertyEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PROPERTY,
                        JUNIFER_PROPERTY_ID.toString(), CORE_PROPERTY_ID.toString()
                    )
                }
            }
        }
    }
})
