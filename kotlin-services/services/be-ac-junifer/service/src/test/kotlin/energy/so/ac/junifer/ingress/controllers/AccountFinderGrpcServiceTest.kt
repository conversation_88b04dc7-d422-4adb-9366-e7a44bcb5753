package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderRequestV2
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderResponseV2
import energy.so.ac.junifer.ingress.services.accountFinder.AccountFinderService
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk

class AccountFinderGrpcServiceTest : BehaviorSpec({

    val accountsService = mockk<AccountFinderService>()
    val service = AccountFinderGrpcService(accountsService)

    given("::accountFinder") {
        `when`("given valid request return AccountFinderResponse") {
            coEvery {
                accountsService.accountFinder(BobsData.validAccountFinderRequest)
            } returns BobsData.validAccountFinderResponse

            then("return valid response") {
                val result = service.accountFinder(BobsData.validAccountFinderRequest)

                result shouldBe BobsData.validAccountFinderResponse
            }

        }
        `when`("given invalid request throw exception") {
            then("throws exception") {
                shouldThrow<InvalidArgumentGrpcException> {
                    service.accountFinder(BobsData.invalidAccountFinderRequest)
                }
            }
        }

        and("elecTwoRate flag is TRUE when fuel type GAS is selected") {

            `when`("method is called") {

                then("InvalidArgumentGrpcException is thrown") {
                    shouldThrow<InvalidArgumentGrpcException> {
                        service.accountFinder(BobsData.requestWithGasFuelAndElecTwoRate)
                    }
                }
            }
        }
    }


    given("Account finder request") {
        coEvery { accountsService.accountFinderV2(accountFinderRequestV2) } returns accountFinderResponseV2
        `when`("given valid response from service") {
            val result = service.accountFinderV2(accountFinderRequestV2)
            then("return AccountFinderResponseV2") {
                coVerify { accountsService.accountFinderV2(accountFinderRequestV2) }
                result shouldBe accountFinderResponseV2
            }

        }
        `when`("given service throws exception") {
            coEvery { accountsService.accountFinderV2(accountFinderRequestV2) } throws RuntimeException("example")
            val exception = shouldThrow<UnknownGrpcException> { service.accountFinderV2(accountFinderRequestV2) }
            then("rethrow exception") {
                coVerify { accountsService.accountFinderV2(accountFinderRequestV2) }
                exception.message shouldBe "example"
            }
        }
    }
})