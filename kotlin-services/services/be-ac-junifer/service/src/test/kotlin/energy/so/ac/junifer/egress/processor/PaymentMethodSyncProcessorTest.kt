package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentRepository
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.CORE_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_ACCOUNT_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.PAYMENT_METHOD_TYPE_ID
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.accountPaymentMethodSyncEvent
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.createPaymentMethodSync
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.deletePaymentMethodSync
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.paymentMethodSyncEvent
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.paymentMethodSyncResponse
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.testAccountJuniferPaymentmethod
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.testJuniferPaymentmethod
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.testJuniferPaymentmethodType
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.updatePaymentMethodSync
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_METHOD
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.core.spec.style.scopes.BehaviorSpecWhenContainerScope
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentMethodSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentRepository = mockk<JuniferPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = PaymentMethodSyncProcessor(mockMapper, mockPaymentRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPaymentRepository, mockSyncClient)
        clearAllMocks()
    }

    given("existing junifer paymentMethod not mapped in core") {

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns null

        every { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) } returns testJuniferPaymentmethod
        every { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentmethodType
        every {
            mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                JUNIFER_PAYMENT_METHOD_ID,
                JUNIFER_ACCOUNT_ID
            )
        } returns (createPaymentMethodSync.paymentMethodEntity.defaultStatus.getValueOrNull().equals("DEFAULT"))


        coEvery { mockSyncClient.syncPaymentMethodEntity(createPaymentMethodSync) } returns paymentMethodSyncResponse
        coEvery {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_ACCOUNT_ID.toString()
        justRun {
            mockMapper.createCoreMapping(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString(),
                PAYMENT_METHOD_ID.toString()
            )
        }

        `when`("a CREATE payment method event is generated") {
            processor.process(paymentMethodSyncEvent)
            thenANewPaymentMethodShouldBeCreated(mockMapper, mockPaymentRepository, mockSyncClient)
        }
    }

    given("existing junifer paymentMethod not mapped in core with account payment method event") {

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                testAccountJuniferPaymentmethod.paymentmethodfk.toString()
            )
        } returns null

        every { mockPaymentRepository.getPaymentMethodById(testAccountJuniferPaymentmethod.paymentmethodfk!!) } returns testJuniferPaymentmethod
        every { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentmethodType
        every {
            mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                JUNIFER_PAYMENT_METHOD_ID,
                JUNIFER_ACCOUNT_ID
            )
        } returns (createPaymentMethodSync.paymentMethodEntity.defaultStatus.getValueOrNull().equals("DEFAULT"))


        coEvery { mockSyncClient.syncPaymentMethodEntity(createPaymentMethodSync) } returns paymentMethodSyncResponse
        coEvery {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_ACCOUNT_ID.toString()
        justRun {
            mockMapper.createCoreMapping(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString(),
                PAYMENT_METHOD_ID.toString()
            )
        }
        `when`("a CREATE account payment method event is generated") {

            every { mockPaymentRepository.getAccountPaymentMethodById(JUNIFER_ACCOUNT_PAYMENT_METHOD_ID) } returns
                    testAccountJuniferPaymentmethod
            processor.process(accountPaymentMethodSyncEvent)
            verify { mockPaymentRepository.getAccountPaymentMethodById(JUNIFER_ACCOUNT_PAYMENT_METHOD_ID) }
            thenANewPaymentMethodShouldBeCreated(mockMapper, mockPaymentRepository, mockSyncClient)
        }

        `when`("a DELETE account payment method event is generated") {
            every { mockPaymentRepository.getAccountPaymentMethodById(JUNIFER_ACCOUNT_PAYMENT_METHOD_ID) } returns
                    null
            then("sync service should not be called") {
                processor.process(accountPaymentMethodSyncEvent)
                coVerify(exactly = 0) { mockSyncClient.syncPaymentMethodEntity(createPaymentMethodSync) }
                verify { mockPaymentRepository.getAccountPaymentMethodById(JUNIFER_ACCOUNT_PAYMENT_METHOD_ID) }
            }
        }
    }


    given("existing junifer paymentMethod mapped in core is updated") {

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns PAYMENT_METHOD_ID.toString()
        coEvery {
            mockMapper.getCoreId(
                EntityIdentifier.BILLING_ACCOUNT,
                JUNIFER_ACCOUNT_ID.toString()
            )
        } returns CORE_ACCOUNT_ID.toString()

        every { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) } returns testJuniferPaymentmethod
        every { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentmethodType

        every {
            mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                JUNIFER_PAYMENT_METHOD_ID,
                JUNIFER_ACCOUNT_ID
            )
        } returns (createPaymentMethodSync.paymentMethodEntity.defaultStatus.getValueOrNull().equals("DEFAULT"))


        `when`("a PATCH payment method event is generated") {

            coEvery { mockSyncClient.syncPaymentMethodEntity(updatePaymentMethodSync) } returns paymentMethodSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_METHOD,
                    JUNIFER_PAYMENT_METHOD_ID.toString(),
                    PAYMENT_METHOD_ID.toString()
                )
            }

            processor.process(paymentMethodSyncEvent)

            then("a new payment method should be created") {
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
                verify { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) }
                verify { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) }
                verify {
                    mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                        JUNIFER_PAYMENT_METHOD_ID,
                        JUNIFER_ACCOUNT_ID
                    )
                }
                coVerify { mockSyncClient.syncPaymentMethodEntity(updatePaymentMethodSync) }
            }
        }
    }

    given("existing junifer paymentMethod mapped in core is deleted") {

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns PAYMENT_METHOD_ID.toString()

        every { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) } throws EntityNotFoundException("Payment method not found")
        every { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentmethodType
        every {
            mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                JUNIFER_PAYMENT_METHOD_ID,
                JUNIFER_ACCOUNT_ID
            )
        } returns (createPaymentMethodSync.paymentMethodEntity.defaultStatus.getValueOrNull().equals("DEFAULT"))

        `when`("a DELETE payment method event is generated") {

            coEvery { mockSyncClient.syncPaymentMethodEntity(deletePaymentMethodSync) } returns paymentMethodSyncResponse

            processor.process(paymentMethodSyncEvent)

            then("a new payment method should be created") {
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) }
                coVerify { mockSyncClient.syncPaymentMethodEntity(deletePaymentMethodSync) }
            }
        }
    }
})

private suspend fun BehaviorSpecWhenContainerScope.thenANewPaymentMethodShouldBeCreated(
    mockMapper: EntityMapper,
    mockPaymentRepository: JuniferPaymentRepository,
    mockSyncClient: SyncClient,
) {
    then("a new payment method should be created") {
        verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
        verify { mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT, JUNIFER_ACCOUNT_ID.toString()) }
        verify { mockPaymentRepository.getPaymentMethodById(JUNIFER_PAYMENT_METHOD_ID) }
        verify { mockPaymentRepository.getPaymentMethodTypeById(PAYMENT_METHOD_TYPE_ID) }
        verify {
            mockPaymentRepository.existsAccountPaymentMethodActiveNow(
                JUNIFER_PAYMENT_METHOD_ID,
                JUNIFER_ACCOUNT_ID
            )
        }

        verify {
            mockMapper.createCoreMapping(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString(),
                PAYMENT_METHOD_ID.toString()
            )
        }
        coVerify { mockSyncClient.syncPaymentMethodEntity(createPaymentMethodSync) }
    }
}