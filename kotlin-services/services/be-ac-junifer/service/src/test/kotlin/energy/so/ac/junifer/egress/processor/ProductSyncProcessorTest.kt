package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferProductRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.JUNIFER_ASSET_MPAN
import energy.so.ac.junifer.fixtures.JUNIFER_ASSET_MPRN
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_BUNDLE_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_TYPE_ID
import energy.so.ac.junifer.fixtures.PRODUCT_ID
import energy.so.ac.junifer.fixtures.ProductPrecannedData.createElecProductSync
import energy.so.ac.junifer.fixtures.ProductPrecannedData.createGasProductSync
import energy.so.ac.junifer.fixtures.ProductPrecannedData.deleteProductSync
import energy.so.ac.junifer.fixtures.ProductPrecannedData.patchElecProductSync
import energy.so.ac.junifer.fixtures.ProductPrecannedData.patchGasProductSync
import energy.so.ac.junifer.fixtures.ProductPrecannedData.productSyncEvent
import energy.so.ac.junifer.fixtures.ProductPrecannedData.productSyncResponse
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testElecJuniferProductBundleDfn
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testGasJuniferProductBundleDfn
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testJuniferAgreementType
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDateTime
import energy.so.commons.model.enums.EventType
import energy.so.products.client.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class ProductSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockProductRepo = mockk<JuniferProductRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = ProductSyncProcessor(mockMapper, mockProductRepo, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockProductRepo, mockSyncClient)
    }

    given("no existing mapped product and an existing junifer product") {

        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) } returns null
        every { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) } returns testJuniferAgreementType

        `when`("an electricity product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testElecJuniferProductBundleDfn
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPAN
            coEvery { mockSyncClient.syncProductEntity(createElecProductSync) } returns productSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT,
                    JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                    PRODUCT_ID.toString()
                )
            }

            processor.process(productSyncEvent)

            then("a new electricity product should be created") {

                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify { mockSyncClient.syncProductEntity(createElecProductSync) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                        PRODUCT_ID.toString()
                    )
                }
            }
        }

        `when`("an gas product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testGasJuniferProductBundleDfn
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPRN
            coEvery { mockSyncClient.syncProductEntity(createGasProductSync) } returns productSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT,
                    JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                    PRODUCT_ID.toString()
                )
            }

            processor.process(productSyncEvent)

            then("a new gas product should be created") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify { mockSyncClient.syncProductEntity(createGasProductSync) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                        PRODUCT_ID.toString()
                    )
                }
            }
        }
    }

    given("no existing mapped product and an existing junifer product with delete flag") {

        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) } returns null
        every { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) } returns testJuniferAgreementType

        `when`("an electricity product event is generated with delete flag") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testElecJuniferProductBundleDfn.copy(
                deletefl = BOOLEAN_TRUE
            )
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPAN
            coEvery { mockSyncClient.syncProductEntity(any()) } returns productSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT,
                    JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                    PRODUCT_ID.toString()
                )
            }

            processor.process(productSyncEvent)

            then("a new electricity product should be created with delete timestamp") {

                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify {
                    mockSyncClient.syncProductEntity(withArg {
                        it.productEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                        PRODUCT_ID.toString()
                    )
                }
            }
        }

        `when`("an gas product event is generated with delete flag") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testGasJuniferProductBundleDfn.copy(
                deletefl = BOOLEAN_TRUE
            )
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPRN
            coEvery { mockSyncClient.syncProductEntity(any()) } returns productSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT,
                    JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                    PRODUCT_ID.toString()
                )
            }

            processor.process(productSyncEvent)

            then("a new gas product should be created") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify {
                    mockSyncClient.syncProductEntity(withArg {
                        it.productEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT,
                        JUNIFER_PRODUCT_BUNDLE_ID.toString(),
                        PRODUCT_ID.toString()
                    )
                }
            }
        }

    }

    given("an existing mapped product and an existing junifer product") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString())
        } returns PRODUCT_ID.toString()
        every { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) } returns testJuniferAgreementType

        `when`("an electricity product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testElecJuniferProductBundleDfn
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPAN
            coEvery { mockSyncClient.syncProductEntity(patchElecProductSync) } returns productSyncResponse

            processor.process(productSyncEvent)

            then("the electricity product should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify { mockSyncClient.syncProductEntity(patchElecProductSync) }
            }
        }

        `when`("an gas product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testGasJuniferProductBundleDfn
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPRN
            coEvery { mockSyncClient.syncProductEntity(patchGasProductSync) } returns productSyncResponse

            processor.process(productSyncEvent)

            then("the gas product should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify { mockSyncClient.syncProductEntity(patchGasProductSync) }
            }
        }
    }

    given("an existing mapped product and an existing junifer product with delete flag") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString())
        } returns PRODUCT_ID.toString()
        every { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) } returns testJuniferAgreementType

        `when`("an electricity product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testElecJuniferProductBundleDfn.copy(
                deletefl = BOOLEAN_TRUE
            )
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPAN
            coEvery { mockSyncClient.syncProductEntity(any()) } returns productSyncResponse

            processor.process(productSyncEvent)

            then("the electricity product should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify {
                    mockSyncClient.syncProductEntity(withArg {
                        it.productEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
            }
        }

        `when`("an gas product event is generated") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testGasJuniferProductBundleDfn.copy(
                deletefl = BOOLEAN_TRUE
            )
            every { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) } returns JUNIFER_ASSET_MPRN
            coEvery { mockSyncClient.syncProductEntity(any()) } returns productSyncResponse

            processor.process(productSyncEvent)

            then("the gas product should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleDefinitionType(JUNIFER_PRODUCT_TYPE_ID) }
                coVerify {
                    mockSyncClient.syncProductEntity(withArg {
                        it.productEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
            }
        }
    }

    given("an existing mapped product and no existing junifer product") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString())
        } returns PRODUCT_ID.toString()
        every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } throws EntityNotFoundException("No product found")

        `when`("a product event is generated") {

            coEvery { mockSyncClient.syncProductEntity(deleteProductSync) } returns productSyncResponse

            processor.process(productSyncEvent)

            then("the product should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
                coVerify { mockSyncClient.syncProductEntity(deleteProductSync) }
            }
        }
    }

    given("an existing mapped product and no existing agreement type") {

        every {
            mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString())
        } returns PRODUCT_ID.toString()
        every { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) } throws EntityNotFoundException(
            "sorry not found"
        )

        `when`("sync is processed") {

            every { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) } returns testElecJuniferProductBundleDfn

            shouldThrow<SyncDelayedException> { processor.process(productSyncEvent) }

            then("an exception should be thrown") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT, JUNIFER_PRODUCT_BUNDLE_ID.toString()) }
                verify { mockProductRepo.getAgreementByProductBundleDfnId(JUNIFER_PRODUCT_BUNDLE_ID) }
                verify { mockProductRepo.getProductBundleById(JUNIFER_PRODUCT_BUNDLE_ID) }
            }
        }
    }

    given("getProcessedTypes method") {
        `when`("called") {
            then("the processed types should be correct") {
                processor.getProcessedTypes() shouldBe listOf(EventType.PRODUCT_BUNDLE_DEFINITION)
            }
        }
    }

    given("a valid product code") {
        `when`("a so energy product code is provided") {

            val shortCode = processor.resolveShortCode("E1R-SPCI-24M-C")

            then("the short code should be resolved") {
                shortCode shouldBe "SPCI"
            }
        }

        `when`("a SOOC product code is provided") {

            val shortCode = processor.resolveShortCode("E2R-SOOC-040216")

            then("the short code should be resolved") {
                shortCode shouldBe "SOOC"
            }
        }

        `when`("an E7 product code is provided") {

            val shortCode = processor.resolveShortCode("LT16-EE7-12-231120")

            then("the short code should be resolved") {
                shortCode shouldBe "2"
            }
        }

        `when`("an SR product code is provided") {

            val shortCode = processor.resolveShortCode("LT12-GSR-12-170521")

            then("the short code should be resolved") {
                shortCode shouldBe "1"
            }
        }

        `when`("an B1 product code is provided") {

            val shortCode = processor.resolveShortCode("LT12-TB1-12-170521")

            then("the short code should be resolved") {
                shortCode shouldBe "1"
            }
        }

        `when`("a PAYG product code is provided") {

            val shortCode = processor.resolveShortCode("E2R-PAYG-C")

            then("the short code should be resolved") {
                shortCode shouldBe "PAYG"
            }
        }
    }
})
