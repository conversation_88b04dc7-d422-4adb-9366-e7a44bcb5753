package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountTicketRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountTicketRequest
import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountTicketRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCreateAccountTicketRequestTest : BehaviorSpec({

    given("create account ticket request") {
        val request = createAccountTicketRequest

        `when`("map to junifer create account ticket request") {
            val juniferRequest = JuniferCreateAccountTicketRequest.fromCreateAccountTicketRequest(request)

            then("return a corresponding JuniferCreateAccountTicketRequest") {
                juniferRequest shouldBe juniferCreateAccountTicketRequest
            }
        }
    }
})
