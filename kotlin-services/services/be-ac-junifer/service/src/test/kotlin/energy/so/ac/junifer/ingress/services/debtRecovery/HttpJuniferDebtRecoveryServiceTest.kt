package energy.so.ac.junifer.ingress.services.debtRecovery

import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import java.time.LocalDate
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.Json.Default.decodeFromString
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class HttpJuniferDebtRecoveryServiceTest : BehaviorSpec({

    given("successful response from Junfier") {
        val sut = HttpJuniferDebtRecoveryService(MockJuniferClient.juniferConfig, getMockCancellationHttpClient())
        `when`("cancel debt recovery") {
            then("do nothing") {
                assertDoesNotThrow { sut.cancelDebtRecovery(1L) }
            }
        }
    }

    given("failed response from Junfier") {
        val sut = HttpJuniferDebtRecoveryService(MockJuniferClient.juniferConfig, getMockCancellationHttpClient(false))
        `when`("cancel debt recovery") {
            then("throw exception") {
                assertThrows<JuniferException> { sut.cancelDebtRecovery(1L) }
            }
        }
    }

    given("We want to create a debt recovery plan") {
        val expectedPlanId = 256L
        val accountId = 4900L
        val sut = HttpJuniferDebtRecoveryService(
            MockJuniferClient.juniferConfig,
            getMockCreationHttpClient(planId = expectedPlanId, accountId = accountId)
        )
        val amount = 100.5
        val toDate = LocalDate.now().plusMonths(4)
        `when`("Create debt recovery plan is called") {
            val result = sut.createDebtRecoveryPlan(accountId, amount, toDate)

            then("The result should match expected") {
                result.id shouldBe expectedPlanId
            }
        }
    }

    given("We want to create a debt recovery plan but junifer won't cooperate") {
        val accountId = 4901L
        val sut = HttpJuniferDebtRecoveryService(
            MockJuniferClient.juniferConfig,
            getMockCreationHttpClient(accountId = accountId)
        )
        val amount = 100.5
        val toDate = LocalDate.now().plusMonths(4)
        `when`("Create debt recovery plan is called") {
            shouldThrow<Exception> { sut.createDebtRecoveryPlan(accountId, amount, toDate) }
        }
    }
})

private fun getMockCancellationHttpClient(isSuccess: Boolean = true): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine {
        if (isSuccess) {
            respond("", HttpStatusCode.OK, headersOf())
        } else {
            respond(
                "{\n" +
                        "\"errorCode\": \"NotFound\",\n" +
                        "\"errorSeverity\": \"Error\",\n" +
                        "\"errorDescription\": \"Could not find 'debt recovery' with Id '500'\"\n" +
                        "}", HttpStatusCode.NotFound, headersOf()
            )
        }
    })
}

private fun getMockCreationHttpClient(planId: Long? = null, accountId: Long): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Post && request.url.fullPath.endsWith("/debtRecoverys")) {
            val requestBody = decodeFromString<JsonObject>(String(request.body.toByteArray()))
            assertEquals(accountId, requestBody["accountId"]?.jsonPrimitive?.longOrNull)
            if (planId != null) {
                respond(
                    """
                    {
                        "id": $planId,
                        "fromDate": "${LocalDate.now()}",
                        "toDate": ${requestBody.getValue("toDate")},
                        "currencyISO": "GBP",
                        "amount": ${requestBody.getValue("amount").jsonPrimitive},
                        "links": {
                            "self": "https://api.integration.gentrack.cloud/v1/junifer/debtRecoverys/$planId"
                        }
                    }""",
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            } else {
                respond(
                    content = ByteReadChannel(
                        Json.encodeToString(
                            JuniferError.serializer(),
                            JuniferError("", "", "")
                        )
                    ),
                    status = HttpStatusCode.BadRequest,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}