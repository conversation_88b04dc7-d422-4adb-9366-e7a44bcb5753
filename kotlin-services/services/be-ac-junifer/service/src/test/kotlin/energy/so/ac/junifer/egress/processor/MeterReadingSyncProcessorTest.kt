package energy.so.ac.junifer.egress.processor

import com.google.protobuf.util.JsonFormat
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.egress.processor.MeterReadingSyncProcessor.Companion.JUNIFER_METER_READING_TYPES
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.JUNIFER_METER_READ_ID
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.METER_READ_DATE
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.METER_READ_ID
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.acceptedReadSyncEventType
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.createReadSync
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadSyncEvent
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadSyncResponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingResult
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingsReponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.updateReadSync
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.validUkMeterRead
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.ac.junifer.ingress.models.assets.GetMeterReadingResponse
import energy.so.ac.junifer.ingress.services.assets.JuniferAssetsService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.ac.junifer.v1.events.copy
import energy.so.assets.api.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.jooq.JSON

class MeterReadingSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockAssetsService = mockk<JuniferAssetsService>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = MeterReadingSyncProcessor(mockMapper, mockAssetsService, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockAssetsService, mockSyncClient)
    }

    given("a valid create meter reading event with an unmapped meter read") {

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns null

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns JUNIFER_METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns JUNIFER_REGISTER_ID.toString()

        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER,
                MeterPrecannedData.JUNIFER_METER_ID.toString()
            )
        } returns MeterPrecannedData.JUNIFER_METER_ID.toString()

        `when`("the event is processed, and the meter reading is found in one call") {

            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.METER_READING,
                    JUNIFER_METER_READ_ID.toString(),
                    METER_READ_ID.toString()
                )
            }
            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse
            coEvery { mockSyncClient.syncMeterReadingEntity(createReadSync) } returns meterReadSyncResponse

            processor.process(meterReadSyncEvent)

            then("a meter reading should be created and a mapping should be created") {

                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.METER_READING,
                        JUNIFER_METER_READ_ID.toString(),
                        METER_READ_ID.toString(),
                    )
                }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify { mockSyncClient.syncMeterReadingEntity(createReadSync) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, MeterPrecannedData.JUNIFER_METER_ID.toString()) }
            }
        }

        `when`("the event is processed, and the meter reading is not found in one call") {

            /*
                This test will query the following date periods:
                from=[2023-03-17, to=[2023-03-19] - 3 days
                from=[2023-02-17, to=[2023-03-17] - 1 month
                from=[2022-12-17, to=[2023-02-17] - 2 months
                from=[2022-09-17, to=[2022-12-17] - 3 months
             */
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.METER_READING,
                    JUNIFER_METER_READ_ID.toString(),
                    METER_READ_ID.toString()
                )
            }
            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns GetMeterReadingResponse(
                results = listOf(
                    meterReadingResult.copy(id = 11111L), // Does not match
                    meterReadingResult.copy(id = 22222L), // Does not match
                    meterReadingResult.copy(id = 33333L), // Does not match
                )
            )
            // Second request queries even more
            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(1),
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns GetMeterReadingResponse(
                results = listOf(
                    meterReadingResult.copy(id = 11111L), // Does not match
                )
            )
            // Third request queries even more
            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(3),
                    METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns GetMeterReadingResponse(
                results = listOf(
                    meterReadingResult.copy(id = 11111L), // Does not match
                )
            )
            // Find it on the fourth
            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(6),
                    METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(3),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse
            coEvery { mockSyncClient.syncMeterReadingEntity(createReadSync) } returns meterReadSyncResponse

            processor.process(meterReadSyncEvent)

            then("the service should search for the reading, and create a meter reading") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.METER_READING,
                        JUNIFER_METER_READ_ID.toString(),
                        METER_READ_ID.toString()
                    )
                }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(1),
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(3),
                        METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(6),
                        METER_READ_DATE.toLocalDate().minusDays(1).minusMonths(3),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify { mockSyncClient.syncMeterReadingEntity(createReadSync) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, MeterPrecannedData.JUNIFER_METER_ID.toString()) }
            }
        }
    }

    given("a valid create meter reading event with a mapped meter read") {

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns METER_READ_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns JUNIFER_METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns JUNIFER_REGISTER_ID.toString()

        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER,
                MeterPrecannedData.JUNIFER_METER_ID.toString()
            )
        } returns MeterPrecannedData.JUNIFER_METER_ID.toString()

        `when`("the event is processed, and the meter reading is found in one call") {

            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse
            coEvery { mockSyncClient.syncMeterReadingEntity(updateReadSync) } returns meterReadSyncResponse

            processor.process(meterReadSyncEvent)

            then("a meter reading should be updated") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, MeterPrecannedData.JUNIFER_METER_ID.toString()) }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
                coVerify { mockSyncClient.syncMeterReadingEntity(updateReadSync) }
            }
        }
    }

    given("a valid meter reading event with no core id for meter point") {
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns METER_READ_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns null

        `when`("the event is processed, and the meter reading is found in one call") {

            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse

            then("exception thrown") {
                shouldThrow<SyncDelayedException> { processor.process(meterReadSyncEvent) }

                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
            }
        }
    }

    given("a valid create meter reading event with no core id for register") {
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns METER_READ_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns JUNIFER_METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns null

        `when`("the event is processed, and the meter reading is found in one call") {

            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse


            then("exception thrown") {
                shouldThrow<SyncDelayedException> { processor.process(meterReadSyncEvent) }

                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
            }
        }
    }

    given("a valid create meter reading event with no core id for meter") {
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns METER_READ_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns JUNIFER_METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns JUNIFER_REGISTER_ID.toString()

        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER,
                MeterPrecannedData.JUNIFER_METER_ID.toString()
            )
        } returns null

        `when`("the event is processed, and the meter reading is found in one call") {

            coEvery {
                mockAssetsService.getMeterReadings(
                    JUNIFER_METER_POINT_ID,
                    METER_READ_DATE.toLocalDate().minusDays(1),
                    METER_READ_DATE.toLocalDate().plusDays(1),
                    JUNIFER_METER_READING_TYPES,
                )
            } returns meterReadingsReponse


            then("exception thrown") {
                shouldThrow<SyncDelayedException> { processor.process(meterReadSyncEvent) }

                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, MeterPrecannedData.JUNIFER_METER_ID.toString()) }
                coVerify {
                    mockAssetsService.getMeterReadings(
                        JUNIFER_METER_POINT_ID,
                        METER_READ_DATE.toLocalDate().minusDays(1),
                        METER_READ_DATE.toLocalDate().plusDays(1),
                        JUNIFER_METER_READING_TYPES,
                    )
                }
            }
        }
    }

    given("an invalid meter reading") {
        `when`("the event is processed and the content JSON is invalid") {

            shouldThrow<Exception> {
                processor.process(
                    meterReadSyncEvent.copy(
                        additionalData = JSON.json(
                            JsonFormat.printer().print(validUkMeterRead.copy {
                                eventContent = "crap json!"
                            })
                        )
                    )
                )
            }

            then("an error should be thrown") {}
        }

        `when`("the event is processed and reference is null") {

            shouldThrow<IllegalStateException> {
                processor.process(
                    meterReadSyncEvent.copy(
                        reference = null
                    )
                )
            }

            then("an IllegalStateException should be thrown") {}
        }

        `when`("the event is processed and additionalData is missing") {

            shouldThrow<IllegalStateException> {
                processor.process(
                    meterReadSyncEvent.copy(
                        additionalData = null
                    )
                )
            }

            then("an IllegalStateException should be thrown") {}
        }
    }

    given("a valid accepted read event with a mapped meter reading") {

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString())
        } returns METER_READ_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns JUNIFER_METER_POINT_ID.toString()

        every {
            mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString())
        } returns JUNIFER_REGISTER_ID.toString()

        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER,
                MeterPrecannedData.JUNIFER_METER_ID.toString()
            )
        } returns MeterPrecannedData.JUNIFER_METER_ID.toString()

        `when`("the event is processed") {

            coEvery { mockSyncClient.syncMeterReadingEntity(updateReadSync) } returns meterReadSyncResponse

            processor.process(acceptedReadSyncEventType)

            then("the event should be synchronized") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER_READING, JUNIFER_METER_READ_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.REGISTER, JUNIFER_REGISTER_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, MeterPrecannedData.JUNIFER_METER_ID.toString()) }
                coVerify { mockSyncClient.syncMeterReadingEntity(updateReadSync) }
            }
        }
    }
})
