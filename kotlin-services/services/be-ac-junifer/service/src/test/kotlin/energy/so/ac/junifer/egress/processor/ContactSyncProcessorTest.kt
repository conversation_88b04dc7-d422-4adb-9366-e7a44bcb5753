package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferContactRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.JUNIFER_ACCOUNT_NUMBER
import energy.so.ac.junifer.fixtures.ContactPrecannedData.ADDRESS_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.CONTACT_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.JUNIFER_ADDRESS_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.JUNIFER_CONTACT_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.JUNIFER_CONTACT_TYPE_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.JUNIFER_CONTACT_VERSION_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.TITLE_ID
import energy.so.ac.junifer.fixtures.ContactPrecannedData.contactSyncEvent
import energy.so.ac.junifer.fixtures.ContactPrecannedData.contactSyncResponse
import energy.so.ac.junifer.fixtures.ContactPrecannedData.contactVersionSyncEvent
import energy.so.ac.junifer.fixtures.ContactPrecannedData.createContactSync
import energy.so.ac.junifer.fixtures.ContactPrecannedData.testJuniferContact
import energy.so.ac.junifer.fixtures.ContactPrecannedData.testJuniferContactType
import energy.so.ac.junifer.fixtures.ContactPrecannedData.testJuniferContactVersion
import energy.so.ac.junifer.fixtures.ContactPrecannedData.testJuniferContactVersionRedactedEmail
import energy.so.ac.junifer.fixtures.ContactPrecannedData.testTitle
import energy.so.ac.junifer.fixtures.ContactPrecannedData.updateContactSync
import energy.so.ac.junifer.fixtures.ContactPrecannedData.updateContactSyncRedactedMail
import energy.so.ac.junifer.mapping.EntityIdentifier.CONTACT
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER_ADDRESS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.assertEquals

class ContactSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockContactRepository = mockk<JuniferContactRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val contactProcessor =
        ContactSyncProcessor(mockMapper, mockContactRepository, mockSyncClient, 2, 2, false, "production")
    val contactProcessorOptimizedSyncEnabled =
        ContactSyncProcessor(mockMapper, mockContactRepository, mockSyncClient, 2, 2, true, "any")

    given("no existing mapped contact and an existing junifer contact  and a contact sync event") {

        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns null
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle


        `when`("a contact event is generated") {

            coEvery { mockSyncClient.syncContactEntity(createContactSync) } returns contactSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }

            contactProcessor.process(contactSyncEvent)

            then("a new contact should be created") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        CONTACT,
                        JUNIFER_CONTACT_ID.toString(),
                        CONTACT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncContactEntity(createContactSync) }
            }
        }
    }

    given("no existing mapped contact and an existing junifer contact and a contact version sync event") {

        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns null
        every { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion

        `when`("a contact version event is generated") {

            shouldThrow<SyncDelayedException> { contactProcessor.process(contactVersionSyncEvent) }
        }
    }

    given("No existing junifer contact and a contact version sync event") {

        every { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } throws EntityNotFoundException("No entity")
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion

        `when`("a contact version event is generated") {

            shouldThrow<AutoDiscardableException> { contactProcessor.process(contactVersionSyncEvent) }
        }
    }

    given("an existing mapped contact and an existing junifer contact  and a contact sync event") {


        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle

        `when`("a contact event is generated") {

            coEvery { mockSyncClient.syncContactEntity(updateContactSync) } returns contactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }

            contactProcessor.process(contactSyncEvent)

            then("contact should be patched") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }
                coVerify { mockSyncClient.syncContactEntity(updateContactSync) }
            }
        }

    }
    given("an existing mapped contact and an existing junifer contact  and a contact version sync event") {


        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle


        `when`("a contact version event is generated") {

            every { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) } returns testJuniferContactVersion

            coEvery { mockSyncClient.syncContactEntity(updateContactSync) } returns contactSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }
            every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion

            contactProcessor.process(contactVersionSyncEvent)

            then("contact should be patched") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }
                coVerify { mockSyncClient.syncContactEntity(updateContactSync) }
            }
        }
    }

    given("an existing mapped contact and existing junifer contact to be deleted  and a contact sync event") {

        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact.copy(deletefl = BOOLEAN_TRUE)

        `when`("a contact event is generated") {

            every { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) } returns testJuniferContactVersion

            coEvery { mockSyncClient.syncContactEntity(any()) } returns contactSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }
            every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion

            contactProcessor.process(contactVersionSyncEvent)

            then("contact should be deleted") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }
                coVerify { mockSyncClient.syncContactEntity(any()) }
            }
        }
    }

    given("a valid contact on the database, but no contact version") {

        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns null
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact

        `when`("a contact event is generated, not at max retries") {

            shouldThrow<AutoDiscardableException> { contactProcessor.process(contactSyncEvent) }

            then("an exception should be thrown and the processor should back off") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
            }
        }

        `when`("a contact event is generated at max retries") {
            val exception = shouldThrow<AutoDiscardableException> {
                contactProcessor.process(contactSyncEvent.copy(retryCount = 2))
            }

            then("the event should do nothing and complete") {
                assertEquals(true, exception.canDiscard)
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
            }
        }
    }

    given("an existing mapped contact and an existing junifer contact and a contact version sync event nonprod") {

        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersionRedactedEmail
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle
        every { mockContactRepository.getAccountNumberByContactId(JUNIFER_CONTACT_ID) } returns JUNIFER_ACCOUNT_NUMBER

        `when`("a contact version event is generated") {

            every { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) } returns testJuniferContactVersionRedactedEmail

            coEvery { mockSyncClient.syncContactEntity(updateContactSyncRedactedMail) } returns contactSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }
            every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion

            contactProcessorOptimizedSyncEnabled.process(contactVersionSyncEvent)

            then("contact should be patched") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersion(JUNIFER_CONTACT_VERSION_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }
                verify { mockContactRepository.getAccountNumberByContactId(JUNIFER_CONTACT_ID) }
                coVerify { mockSyncClient.syncContactEntity(updateContactSyncRedactedMail) }
            }
        }
    }

    given("create contact without redacting email on nonprod env") {
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns null
        every { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) } returns ADDRESS_ID.toString()
        every { mockContactRepository.getContact(JUNIFER_CONTACT_ID) } returns testJuniferContact
        every { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) } returns testJuniferContactVersion
        every { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) } returns testJuniferContactType
        every { mockContactRepository.getTitle(TITLE_ID) } returns testTitle
        every { mockContactRepository.getAccountNumberByContactId(JUNIFER_CONTACT_ID) } returns JUNIFER_ACCOUNT_NUMBER

        `when`("a contact event is generated") {

            coEvery { mockSyncClient.syncContactEntity(createContactSync) } returns contactSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    CONTACT,
                    JUNIFER_CONTACT_ID.toString(),
                    CONTACT_ID.toString()
                )
            }

            contactProcessorOptimizedSyncEnabled.process(contactSyncEvent)

            then("a new contact should be created") {
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER_ADDRESS, JUNIFER_ADDRESS_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getContact(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactVersionByContactId(JUNIFER_CONTACT_ID) }
                verify { mockContactRepository.getContactType(JUNIFER_CONTACT_TYPE_ID) }
                verify { mockContactRepository.getTitle(TITLE_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        CONTACT,
                        JUNIFER_CONTACT_ID.toString(),
                        CONTACT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncContactEntity(createContactSync) }
            }
        }
    }
})
