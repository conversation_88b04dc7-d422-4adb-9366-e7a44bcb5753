package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentRepository
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_GO_CARDLESS_DD_COLLECTION_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_METHOD_TYPE_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.createPaymentSync
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.deletePaymentSync
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.paymentRequestSyncEvent
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.paymentSyncResponse
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.testJuniferPaymentMethodType
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.testJuniferPaymentRequest
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.updatePaymentSync
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_METHOD
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentRepository = mockk<JuniferPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val paymentProcessor = PaymentSyncProcessor(mockMapper, mockPaymentRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPaymentRepository, mockSyncClient)
    }

    given("no existing mapped payment and an existing junifer payment") {

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PAYMENT_METHOD,
                JUNIFER_PAYMENT_METHOD_ID.toString()
            )
        } returns PAYMENT_METHOD_ID.toString()


        every { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) } returns testJuniferPaymentRequest
        every { mockPaymentRepository.getPaymentMethodTypeById(JUNIFER_PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentMethodType

        `when`("a payment event is generated") {

            coEvery { mockSyncClient.syncPaymentEntity(any()) } returns paymentSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT,
                    JUNIFER_PAYMENT_REQUEST_ID.toString(),
                    PAYMENT_ID.toString()
                )
            }

            paymentProcessor.process(paymentRequestSyncEvent)

            then("a new payment should be created") {
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) }
                verify { mockPaymentRepository.getPaymentMethodTypeById(JUNIFER_PAYMENT_METHOD_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        PAYMENT,
                        JUNIFER_PAYMENT_REQUEST_ID.toString(),
                        PAYMENT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncPaymentEntity(createPaymentSync) }
            }
        }
    }

    given("an existing mapped payment and an existing junifer payment") {

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()

        every { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) } returns testJuniferPaymentRequest
        every { mockPaymentRepository.getPaymentMethodTypeById(JUNIFER_PAYMENT_METHOD_TYPE_ID) } returns testJuniferPaymentMethodType

        `when`("a payment event is generated") {

            coEvery { mockSyncClient.syncPaymentEntity(any()) } returns paymentSyncResponse

            paymentProcessor.process(paymentRequestSyncEvent)

            then("payment should be patched") {
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) }
                verify { mockPaymentRepository.getPaymentMethodTypeById(JUNIFER_PAYMENT_METHOD_TYPE_ID) }

                coVerify { mockSyncClient.syncPaymentEntity(updatePaymentSync) }
            }
        }
    }

    given("an existing mapped payment and existing junifer payment to be deleted") {

        every { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) } throws EntityNotFoundException("Payment not found")

        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_GO_CARDLESS_DD_COLLECTION_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        `when`("a payment event is generated") {

            coEvery { mockSyncClient.syncPaymentEntity(any()) } returns paymentSyncResponse

            paymentProcessor.process(paymentRequestSyncEvent)

            then("payment should be deleted") {
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentRepository.getPaymentRequest(JUNIFER_PAYMENT_REQUEST_ID) }
                coVerify { mockSyncClient.syncPaymentEntity(deletePaymentSync) }
            }
        }
    }
})
