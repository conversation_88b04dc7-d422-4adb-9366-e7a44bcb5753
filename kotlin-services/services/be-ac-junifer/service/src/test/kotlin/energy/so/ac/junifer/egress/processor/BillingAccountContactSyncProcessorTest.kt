package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferContactRepository
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.BILLING_ACCOUNT_CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.JUNIFER_BILLING_ACCOUNT_CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.JUNIFER_CONTACT_ID
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.billingAccountContactSyncEvent
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.billingAccountContactSyncResponse
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.createBillingAccountContactSync
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.testJuniferBillingAccountContact
import energy.so.ac.junifer.fixtures.BillingAccountContactPrecannedData.updateBillingAccountContactSync
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT_CONTACT
import energy.so.ac.junifer.mapping.EntityIdentifier.CONTACT
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class BillingAccountContactSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockContactRepository = mockk<JuniferContactRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val billingAccountContactProcessor =
        BillingAccountContactSyncProcessor(mockMapper, mockContactRepository, mockSyncClient, 2, 2)

    afterEach {
        confirmVerified(mockMapper, mockContactRepository, mockSyncClient)
    }

    given("no existing mapped billingAccountContact and an existing junifer billingAccountContact") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_CONTACT,
                JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) } returns testJuniferBillingAccountContact

        `when`("a billingAccountContact event is generated") {

            coEvery { mockSyncClient.syncBillingAccountContactEntity(createBillingAccountContactSync) } returns billingAccountContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILLING_ACCOUNT_CONTACT,
                    JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString(),
                    BILLING_ACCOUNT_CONTACT_ID.toString()
                )
            }

            billingAccountContactProcessor.process(billingAccountContactSyncEvent)

            then("a new billingAccountContact should be created") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_CONTACT, JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        BILLING_ACCOUNT_CONTACT,
                        JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString(),
                        BILLING_ACCOUNT_CONTACT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillingAccountContactEntity(createBillingAccountContactSync) }
            }
        }
    }

    given("an existing mapped billingAccountContact and an existing junifer billingAccountContact") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_CONTACT,
                JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()
            )
        } returns BILLING_ACCOUNT_CONTACT_ID.toString()
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) } returns testJuniferBillingAccountContact

        `when`("a billingAccountContact event is generated") {

            coEvery { mockSyncClient.syncBillingAccountContactEntity(updateBillingAccountContactSync) } returns billingAccountContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILLING_ACCOUNT_CONTACT,
                    JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString(),
                    BILLING_ACCOUNT_CONTACT_ID.toString()
                )
            }

            billingAccountContactProcessor.process(billingAccountContactSyncEvent)

            then("billingAccountContact should be patched") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_CONTACT, JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) }
                coVerify { mockSyncClient.syncBillingAccountContactEntity(updateBillingAccountContactSync) }
            }
        }
    }

    given("an existing mapped billingAccountContact and existing junifer billingAccountContact to be deleted") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_CONTACT,
                JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()
            )
        } returns BILLING_ACCOUNT_CONTACT_ID.toString()
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) } returns testJuniferBillingAccountContact.copy(
            deletefl = BOOLEAN_TRUE,
            cancelfl = BOOLEAN_TRUE
        )

        `when`("a billingAccountContact event is generated") {

            coEvery { mockSyncClient.syncBillingAccountContactEntity(any()) } returns billingAccountContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    BILLING_ACCOUNT_CONTACT,
                    JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString(),
                    BILLING_ACCOUNT_CONTACT_ID.toString()
                )
            }

            then("billingAccountContact should be deleted") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_CONTACT, JUNIFER_BILLING_ACCOUNT_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getBillingAccountContact(JUNIFER_BILLING_ACCOUNT_CONTACT_ID) }
                coVerify { mockSyncClient.syncBillingAccountContactEntity(any()) }
            }
        }
    }
})
