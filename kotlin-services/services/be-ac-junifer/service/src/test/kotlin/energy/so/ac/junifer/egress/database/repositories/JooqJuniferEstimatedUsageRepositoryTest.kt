package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset

const val CREATE_MPAN_ESTIMATED_USAGE_SQL = "/sql/assets/create_mpan_eac.sql"
const val CREATE_MPRN_ESTIMATED_USAGE_SQL = "/sql/assets/create_mprn_eaq.sql"

class JooqJuniferEstimatedUsageRepositoryTest : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferEstimatedUsageRepository(db)

    given("existing MPAN estimated usage data") {
        val mpanEacSql = SqlLoader.loadFromStream(getSql(CREATE_MPAN_ESTIMATED_USAGE_SQL))
        val existingMpanEacId = 13655412L
        val existingMpanEacSetId = 12431832L

        `when`("the eac is loaded from the DB") {
            db.execute(mpanEacSql)
            val eac = repo.getMpanEstimatedUsage(existingMpanEacId)

            then("the eac should be returned") {
                eac shouldNotBe null
                eac.meterPointId shouldBe 123L
                eac.consumptionFromDt shouldBe LocalDate.of(2023, 1, 24)
                eac.consumptionToDt shouldBe LocalDate.of(9999, 1, 1)
                eac.source shouldBe "Industry"
                eac.rateName shouldBe "Standard"
                eac.usage shouldBe 1451.2
                eac.cancelFl shouldBe "N"
                eac.deleteFl shouldBe "N"
                eac.createdDttm shouldBe OffsetDateTime.of(2023, 1, 31, 23, 16, 34, 0, ZoneOffset.UTC)
            }
        }

        `when`("the eac set is loaded from the DB") {
            db.execute(mpanEacSql)
            val eacList = repo.getMpanEstimatedUsagesByMpanEacSetId(existingMpanEacSetId)

            then("the eac should be returned") {
                eacList shouldNotBe null
                eacList.size shouldBe 1
                eacList[0].meterPointId shouldBe 123L
                eacList[0].consumptionFromDt shouldBe LocalDate.of(2023, 1, 24)
                eacList[0].consumptionToDt shouldBe LocalDate.of(9999, 1, 1)
                eacList[0].source shouldBe "Industry"
                eacList[0].rateName shouldBe "Standard"
                eacList[0].usage shouldBe 1451.2
                eacList[0].cancelFl shouldBe "N"
                eacList[0].deleteFl shouldBe "N"
                eacList[0].createdDttm shouldBe OffsetDateTime.of(2023, 1, 31, 23, 16, 34, 0, ZoneOffset.UTC)
            }
        }
    }

    given("existing MPRN estimated usage data") {
        val mprnEstimatedUsageSql = SqlLoader.loadFromStream(getSql(CREATE_MPRN_ESTIMATED_USAGE_SQL))
        val existingMprnQuantityId = 12609249L

        `when`("the annual quantity is loaded from the DB") {
            db.execute(mprnEstimatedUsageSql)
            val quantity = repo.getMprnEstimatedUsage(existingMprnQuantityId)

            then("the annual quantity should be returned") {
                quantity shouldNotBe null
                quantity.meterPointId shouldBe 123L
                quantity.consumptionFromDt shouldBe LocalDate.of(2023, 2, 16)
                quantity.consumptionToDt shouldBe LocalDate.of(9999, 1, 1)
                quantity.source shouldBe "Industry"
                quantity.rateName shouldBe "Standard"
                quantity.usage shouldBe 4346.0
                quantity.cancelFl shouldBe "N"
                quantity.deleteFl shouldBe "N"
                quantity.createdDttm shouldBe OffsetDateTime.of(2023, 2, 8, 13, 58, 33, 0, ZoneOffset.UTC)
            }
        }
    }
})
