package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.admin.database.repositories.dto.FoundAccountV2
import energy.so.ac.junifer.admin.database.repositories.dto.FuelEnum.DUAL_ONLY
import energy.so.ac.junifer.admin.database.repositories.dto.FuelEnum.ELECTRICITY_ONLY
import energy.so.ac.junifer.admin.database.repositories.dto.FuelEnum.GAS_ONLY
import energy.so.ac.junifer.admin.database.repositories.dto.RequestAccount
import energy.so.commons.model.tables.AccountFinder
import energy.so.commons.model.tables.records.AccountFinderRecord
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import org.jooq.DSLContext
import org.jooq.SelectWhereStep

class JooqAccountFinderV2RepositoryTest : BehaviorSpec({

    val mockDb = mockk<DSLContext>()
    val selectStep = mockk<SelectWhereStep<AccountFinderRecord>>()
//    val mockAccountFinder = mockk<AccountFinder>()
    val sut = JooqAccountFinderV2Repository(mockDb)

    xgiven("request for account finder with gas only") {
        val actualSlot = slot<AccountFinder>()
//        val slotIsSeasonalFl = slot<Boolean>()

        beforeTest {
            mockkStatic("energy.so.commons.model.tables.AccountFinderKt")
            every { mockDb.selectFrom(capture(actualSlot)) } returns selectStep
//            every {
//                anyConstructed<AccountFinder>().call(
//                    capture(slotIsSeasonalFl),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Boolean>(),
//                    any<Long>(),
//                    any<Long>()
//                )
//            } returns mockAccountFinder
            every {
                any<AccountFinder>().call(
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Boolean>(),
                    any<Long>(),
                    any<Long>()
                )
            } returns mockk()
            every { selectStep.fetchInto(FoundAccountV2::class.java) } returns emptyList()
        }

        `when`("search accounts") {
            sut.searchAccounts(
                request = RequestAccount(
                    isSeasonalFlag = false,
                    isDirectDebit = false,
                    isSmartMeter = false,
                    fuel = GAS_ONLY,
                    isSingleRate = false,
                    pageNumber = 0L,
                    limit = 0L
                )
            )
            then("then return gas only entity") {
//                slotIsSeasonalFl.captured shouldBe false
//                actualSlot.captured.let { actual ->
//                    actual.IS_SEASONAL shouldBe true
//                }
            }
        }
    }

    xgiven("request for account finder with electricity only") {
        `when`("search accounts") {
            sut.searchAccounts(
                request = RequestAccount(
                    isSeasonalFlag = false,
                    isDirectDebit = false,
                    isSmartMeter = false,
                    fuel = ELECTRICITY_ONLY,
                    isSingleRate = false,
                    pageNumber = 0L,
                    limit = 0L
                )
            )
            then("then return gas only entity") {

            }
        }
    }

    xgiven("request for account finder with duel fuel only") {
        `when`("search accounts") {
            sut.searchAccounts(
                request = RequestAccount(
                    isSeasonalFlag = false,
                    isDirectDebit = false,
                    isSmartMeter = false,
                    fuel = DUAL_ONLY,
                    isSingleRate = false,
                    pageNumber = 0L,
                    limit = 0L
                )
            )
            then("then return gas only entity") {

            }
        }
    }

})
