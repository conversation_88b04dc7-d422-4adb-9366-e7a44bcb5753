package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferAccountResponse
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.getCustomerData
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.NEW_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.PROPERTY_CORE_ID
import energy.so.ac.junifer.fixtures.successChangeOfTenancyRequest
import energy.so.ac.junifer.fixtures.successChangeOfTenancyResponse
import energy.so.ac.junifer.fixtures.successCreateChangeOfTenancyRequest
import energy.so.ac.junifer.fixtures.successCreateChangeOfTenancyResponse
import energy.so.ac.junifer.fixtures.successCreateMoveOutCotRequestWithMultipleProducts
import energy.so.ac.junifer.fixtures.successCreateMoveOutCotRequestWithOneProductEach
import energy.so.ac.junifer.fixtures.successMoveOutCotRequestWithMultipleProducts
import energy.so.ac.junifer.fixtures.successMoveOutCotRequestWithOneProductEach
import energy.so.ac.junifer.fixtures.successMoveOutCotResponse
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.changeOfTenancy.ChangeOfTenancyRequest
import energy.so.ac.junifer.ingress.services.JuniferAccountService
import energy.so.ac.junifer.ingress.services.JuniferCustomerService
import energy.so.ac.junifer.ingress.services.changeOfTenancy.ChangeOfTenancyService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.changeoftenancy.copy
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.session.SessionManager
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic

class ChangeOfTenancyGrpcServiceTest : BehaviorSpec({

    val mockChangeOfTenancyService = mockk<ChangeOfTenancyService>()
    val mockCustomerService = mockk<JuniferCustomerService>()
    val mockAccountService = mockk<JuniferAccountService>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()

    val sut = ChangeOfTenancyGrpcService(
        changeOfTenancyService = mockChangeOfTenancyService,
        customerService = mockCustomerService,
        accountService = mockAccountService,
        idMapper = inMemoryMapper,
    )
    mockkObject(SessionManager)


    afterTest {
        confirmVerified(mockChangeOfTenancyService, mockCustomerService)
        unmockkObject(SessionManager::class)
        clearAllMocks()
    }

    given("createChangeOfTenancy") {

        inMemoryMapper.createCoreMapping(
            EntityIdentifier.BILLING_ACCOUNT,
            JUNIFER_BILLING_ACCOUNT_ID.toString(),
            ACCOUNT_ID.toString()
        )

        `when`("send a valid create change of tenancy request") {
            coEvery {
                mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                    accountId = JUNIFER_BILLING_ACCOUNT_ID,
                    request = successMoveOutCotRequestWithOneProductEach,
                )
            } returns successMoveOutCotResponse
            coEvery { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString()) } returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString()) } returns juniferAccountResponse

            val response = sut.createMoveOutChangeOfTenancy(successCreateMoveOutCotRequestWithOneProductEach)

            then("returns a success change of tenancy response") {
                response shouldBe successCreateChangeOfTenancyResponse

                coVerify {
                    mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                        accountId = JUNIFER_BILLING_ACCOUNT_ID,
                        request = successMoveOutCotRequestWithOneProductEach,
                    )
                }
                coVerify { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString()) }
            }
        }

        `when`("send a valid request but junifer returns error") {

            coEvery {
                mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                    accountId = JUNIFER_BILLING_ACCOUNT_ID,
                    request = successMoveOutCotRequestWithOneProductEach,
                )
            } throws JuniferException(
                errorCode = "MissingValue",
                errorSeverity = "Error",
                errorDescription = "effectiveDt is mandatory",
            )

            shouldThrow<UnknownGrpcException> {
                sut.createMoveOutChangeOfTenancy(
                    successCreateMoveOutCotRequestWithOneProductEach
                )
            }

            then("throws UnknownGrpcException") {
                coVerify {
                    mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                        accountId = JUNIFER_BILLING_ACCOUNT_ID,
                        request = successMoveOutCotRequestWithOneProductEach,
                    )
                }
            }
        }

        `when`("no existing mapping for an account") {
            shouldThrow<FailedPreconditionGrpcException> {
                sut.createMoveOutChangeOfTenancy(
                    successCreateMoveOutCotRequestWithOneProductEach.copy {
                        accountId = 99999L
                    }
                )
            }
            then("an exception should be thrown") {}
        }

        `when`("send a valid create change of tenancy request with multiple product refs") {
            coEvery {
                mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                    accountId = JUNIFER_BILLING_ACCOUNT_ID,
                    request = successMoveOutCotRequestWithMultipleProducts,
                )
            } returns successMoveOutCotResponse
            coEvery { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString()) } returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString()) } returns juniferAccountResponse

            val response = sut.createMoveOutChangeOfTenancy(successCreateMoveOutCotRequestWithMultipleProducts)

            then("returns a success change of tenancy response") {
                response shouldBe successCreateChangeOfTenancyResponse

                coVerify {
                    mockChangeOfTenancyService.createMoveOutChangeOfTenancy(
                        accountId = JUNIFER_BILLING_ACCOUNT_ID,
                        request = successMoveOutCotRequestWithMultipleProducts,
                    )
                }
                coVerify { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString()) }
            }
        }

    }

    given("Cot MI") {
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.PROPERTY,
            "321",
            "444"
        )

        val grpcRequest = successCreateChangeOfTenancyRequest
        val httpRequest = ChangeOfTenancyRequest.fromProto(successCreateChangeOfTenancyRequest)

        `when`("cot mi request without mock flag") {
            every { SessionManager.shouldMockJunifer() } returns false
            coEvery {
                mockChangeOfTenancyService.createChangeOfTenancy(
                    321L,
                    httpRequest
                )
            } returns successChangeOfTenancyResponse
            coEvery { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString())} returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString())
            } returns juniferAccountResponse


            val result = sut.createChangeOfTenancy(grpcRequest)

            then("succeeds using non-mocked property id") {
                result shouldBe successCreateChangeOfTenancyResponse
                coVerify(exactly = 1) {
                    mockChangeOfTenancyService.createChangeOfTenancy(321L, httpRequest)
                }
                coVerify(exactly = 1) {
                    mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString())
                }
                coVerify(exactly = 1) {
                    mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString())
                }

            }
        }

        `when`("cot mi request with mock flag") {
            every { SessionManager.shouldMockJunifer() } returns true
            coEvery {
                mockChangeOfTenancyService.createChangeOfTenancy(
                    12345L,
                    httpRequest
                )
            } returns successChangeOfTenancyResponse
            coEvery { mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString())} returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString())
            } returns juniferAccountResponse


            val result = sut.createChangeOfTenancy(grpcRequest)

            then("succeeds using non-mocked property id") {
                result shouldBe successCreateChangeOfTenancyResponse
                coVerify(exactly = 1) {
                    mockChangeOfTenancyService.createChangeOfTenancy(12345L, httpRequest)
                }
                coVerify(exactly = 1) {
                    mockCustomerService.getCustomer(NEW_CUSTOMER_ID.toString())
                }
                coVerify(exactly = 1) {
                    mockAccountService.getAccountsForCustomerId(NEW_CUSTOMER_ID.toString())
                }

            }
        }
    }
})