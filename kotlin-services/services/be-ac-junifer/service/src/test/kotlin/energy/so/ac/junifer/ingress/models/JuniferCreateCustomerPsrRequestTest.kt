package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.customers.JuniferCreateCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.createCustomerPsrRequest
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toNullableTimestamp
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate

class JuniferCreateCustomerPsrRequestTest : BehaviorSpec({

    given("create customer psr request with consent false") {
        val request = createCustomerPsrRequest {
            customerId = "1"
            value = "value"
            propertyTblId = 1
            utilityMarket = "market"
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
            note = "note".toNullableString()
            consentFl = false.toNullableBoolean()
        }

        `when`("map to junifer create customer psr request") {
            val juniferRequest = JuniferCreateCustomerPsrRequest.fromCreateCustomerPsrRequest(
                request
            )

            then("return a corresponding JuniferCreateCustomerPsrRequest") {
                assertSoftly {
                    juniferRequest.value shouldBe request.value
                    juniferRequest.propertyTblId shouldBe request.propertyTblId
                    juniferRequest.utilityMarket shouldBe request.utilityMarket
                    juniferRequest.fromDate shouldBe request.fromDt.getValueOrNull()?.toLocalDate()
                    juniferRequest.toDate shouldBe request.toDt.getValueOrNull()?.toLocalDate()
                    juniferRequest.note.toNullableString() shouldBe request.note
                    juniferRequest.consentFl shouldBe request.consentFl.value
                }
            }
        }
    }

    given("create customer psr request with no consent") {
        val request = createCustomerPsrRequest {
            customerId = "1"
            value = "value"
            propertyTblId = 1
            utilityMarket = "market"
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
            note = "note".toNullableString()
        }

        `when`("map to junifer create customer psr request") {
            val juniferRequest = JuniferCreateCustomerPsrRequest.fromCreateCustomerPsrRequest(
                request
            )

            then("return a corresponding JuniferCreateCustomerPsrRequest") {
                assertSoftly {
                    juniferRequest.value shouldBe request.value
                    juniferRequest.propertyTblId shouldBe request.propertyTblId
                    juniferRequest.utilityMarket shouldBe request.utilityMarket
                    juniferRequest.fromDate shouldBe request.fromDt.getValueOrNull()?.toLocalDate()
                    juniferRequest.toDate shouldBe request.toDt.getValueOrNull()?.toLocalDate()
                    juniferRequest.note.toNullableString() shouldBe request.note
                    juniferRequest.consentFl shouldBe null
                }
            }
        }
    }
})
