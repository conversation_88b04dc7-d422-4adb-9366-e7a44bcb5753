package energy.so.ac.junifer.admin.database.repositories.dto

import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderFoundAccountV2Dto
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderFoundAccountV2DtoList
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe

class FoundAccountDtoV2KtTest : BehaviorSpec({

    given("dto request") {
        `when`("map to grpc response") {
            val actual = accountFinderFoundAccountV2DtoList.toGrpcResponse()
            then("then return list of mapped values") {
                assertSoftly {
                    actual.accountsList shouldHaveSize 1
                    actual.accountsList[0].accountId shouldBe accountFinderFoundAccountV2Dto.accountId
                    actual.accountsList[0].accountNumber shouldBe accountFinderFoundAccountV2Dto.accountNumber
                    actual.accountsList[0].pspId shouldBe accountFinderFoundAccountV2Dto.pspId
                    actual.accountsList[0].pspAmount shouldBe accountFinderFoundAccountV2Dto.pspAmount
                    actual.accountsList[0].typeName shouldBe accountFinderFoundAccountV2Dto.typeName
                    actual.accountsList[0].isSeasonal shouldBe accountFinderFoundAccountV2Dto.isSeasonal
                    actual.accountsList[0].isSmartMeter shouldBe accountFinderFoundAccountV2Dto.isSmartMeter
                    actual.accountsList[0].isSingleRate shouldBe accountFinderFoundAccountV2Dto.isSingleRate
                    actual.accountsList[0].isTwoRate shouldBe accountFinderFoundAccountV2Dto.isTwoRate
                    actual.accountsList[0].isDirectDebit shouldBe accountFinderFoundAccountV2Dto.isDirectDebit
                    actual.accountsList[0].isDuelFuel shouldBe accountFinderFoundAccountV2Dto.isDuelFuel
                }
            }
        }
    }
})
