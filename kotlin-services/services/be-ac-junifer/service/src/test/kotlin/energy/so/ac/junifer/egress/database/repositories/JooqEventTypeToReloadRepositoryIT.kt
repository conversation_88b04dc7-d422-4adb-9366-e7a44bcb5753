package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.BillPrecannedData
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.EventTypesToReload
import energy.so.commons.model.tables.references.EVENT_TYPES_TO_RELOAD
import energy.so.commons.model.tables.references.JUNIFER__BILL
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext


class JooqEventTypeToReloadRepositoryIT : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqEventTypeToReloadRepository(db)

    // need this because an sql script creates static records in the test DB, causing undesired consequences in the test env
    truncateEventTypes(db)

    given("the getOneEventTypeToProcess() function is called") {
        `when`("eligible event types are present in the DB") {
            insertEventType(0L, EventType.ADDRESS, db)
            db.fetchCount(EVENT_TYPES_TO_RELOAD) shouldBe 1
            val eventType = repo.getOneEventTypeToProcess(10L)
            then("an EventType is returned") {
                eventType shouldBe EventType.ADDRESS
            }
        }

        `when`("multiple eligible event types are present in the DB") {
            insertEventType(0L, EventType.ADDRESS, db)
            insertEventType(0L, EventType.MPAN_EAC, db)
            insertEventType(0L, EventType.CUSTOMER, db)
            db.fetchCount(EVENT_TYPES_TO_RELOAD) shouldBe 3

            val eventType = repo.getOneEventTypeToProcess(10L)
            then("only one record is returned and updated") {
                eventType shouldNotBe null
                fetchAllEventTypes(db).map { it.readyForReloadFrom }.distinct().size shouldBe 2
            }
        }

        `when`("eligible event types are present in the DB but they are not ready to be reloaded") {
            insertEventType(System.currentTimeMillis() + 60000, EventType.ADDRESS, db)
            db.fetchCount(EVENT_TYPES_TO_RELOAD) shouldBe 1
            val eventType = repo.getOneEventTypeToProcess(10L)
            then("null is returned") {
                eventType shouldBe null
            }
        }

        `when`("no eligible event types are present in the DB") {
            db.fetchCount(EVENT_TYPES_TO_RELOAD) shouldBe 0
            val eventType = repo.getOneEventTypeToProcess(10L)
            then("null is returned") {
                eventType shouldBe null
            }
        }
    }

    given("the fetchIdsForBatch() function is called") {

        `when`("records present in the DB") {
            for (i in 21..32) {
                insertJuniferBill(i.toLong(), db)
            }
            db.fetchCount(JUNIFER__BILL) shouldBe 12

            val idList = repo.fetchIdsForBatch(Pair(23L, 27L), "junifer__Bill")
            then("expected range of Ids is returned") {
                idList.size shouldBe 5
                idList.toSet() shouldBe setOf(23L, 24L, 25L, 26L, 27L)
            }
        }

        `when`("records not present in the DB") {
            db.fetchCount(JUNIFER__BILL) shouldBe 0

            val idList = repo.fetchIdsForBatch(Pair(23L, 27L), "junifer__Bill")
            then("emptyList is returned") {
                idList shouldBe emptyList()
            }
        }

    }

    given("the getPaginationForTable() function is called") {

        `when`("number of records in the DB is divisible with batch size") {
            for (i in 21..32) {
                insertJuniferBill(i.toLong(), db)
            }
            db.fetchCount(JUNIFER__BILL) shouldBe 12

            val idPairs = repo.getPaginationForTable("junifer__Bill", 3)
            then("expected pairs of ids are") {
                idPairs.size shouldBe 4
                idPairs.toSet() shouldBe setOf(Pair(21L, 23L), Pair(24L, 26L), Pair(27L, 29L), Pair(30L, 32L))
            }
        }

        `when`("number of records in the DB is NOT divisible with batch size") {
            for (i in 21..31) {
                insertJuniferBill(i.toLong(), db)
            }
            db.fetchCount(JUNIFER__BILL) shouldBe 11

            val idPairs = repo.getPaginationForTable("junifer__Bill", 3)
            then("expected pairs of ids are") {
                idPairs.size shouldBe 4
                idPairs.toSet() shouldBe setOf(Pair(21L, 23L), Pair(24L, 26L), Pair(27L, 29L), Pair(30L, 31L))
            }
        }

        `when`("number of records in the DB = (N * batch_size + 1)") {
            for (i in 21..30) {
                insertJuniferBill(i.toLong(), db)
            }
            db.fetchCount(JUNIFER__BILL) shouldBe 10

            val idPairs = repo.getPaginationForTable("junifer__Bill", 3)
            then("last batch includes (batch_size + 1) records") {
                idPairs.size shouldBe 3
                idPairs.toSet() shouldBe setOf(Pair(21L, 23L), Pair(24L, 26L), Pair(27L, 30L))
            }
        }

        `when`("number of records in the DB is smaller than batch size") {
            for (i in 21..24) {
                insertJuniferBill(i.toLong(), db)
            }
            db.fetchCount(JUNIFER__BILL) shouldBe 4

            val idPairs = repo.getPaginationForTable("junifer__Bill", 5)
            then("expected pairs of ids are") {
                idPairs.size shouldBe 1
                idPairs.toSet() shouldBe setOf(Pair(21L, 24L))
            }
        }

        `when`("no records are present in the DB") {
            db.fetchCount(JUNIFER__BILL) shouldBe 0

            val idPairs = repo.getPaginationForTable("junifer__Bill", 5)
            then("an empty list is returned") {
                idPairs shouldBe emptyList()
            }
        }
    }
})

private fun insertEventType(readyToReload: Long, eventType: EventType, db: DSLContext) {
    db.insertInto(EVENT_TYPES_TO_RELOAD).columns(
        EVENT_TYPES_TO_RELOAD.EVENT_TYPE,
        EVENT_TYPES_TO_RELOAD.ENABLED,
        EVENT_TYPES_TO_RELOAD.LAST_UPDATED,
        EVENT_TYPES_TO_RELOAD.READY_FOR_RELOAD_FROM,
    ).values(eventType, true, System.currentTimeMillis(), readyToReload).execute()
}

private fun insertJuniferBill(billId: Long, db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__BILL, BillPrecannedData.testJuniferBill.copy(id = billId)))
}

private fun fetchAllEventTypes(db: DSLContext): List<EventTypesToReload> {
    return db.select().from(EVENT_TYPES_TO_RELOAD).fetchInto(EventTypesToReload::class.java)
}

private fun truncateEventTypes(context: DSLContext) = context.truncateTable(EVENT_TYPES_TO_RELOAD).execute()

