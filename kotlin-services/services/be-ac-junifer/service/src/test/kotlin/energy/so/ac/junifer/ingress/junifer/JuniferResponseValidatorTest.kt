package energy.so.ac.junifer.ingress.junifer

import energy.so.ac.junifer.fixtures.CustomerPrecannedData
import energy.so.ac.junifer.ingress.models.customers.JuniferEnrollCustomerRequest
import energy.so.ac.junifer.ingress.services.ENROL_CUSTOMER_URL
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.request
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.encodedPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class JuniferResponseValidatorTest : BehaviorSpec({

    val juniferApiVersion = "1"
    val validator = JuniferResponseValidator

    given("failed enrolment request") {
        `when`("junifer api throws exception ") {

            val path = "/rest/v1/$ENROL_CUSTOMER_URL"
            val exception = shouldThrow<JuniferException> {
                validator.validate(getMockHttpClient().post {
                    url {
                        encodedPath = path
                        setBody(JuniferEnrollCustomerRequest.toRequest(CustomerPrecannedData.enrollCustomerProto))
                    }
                }, juniferApiVersion)
            }

            then("validator should map response to a JuniferException") {
                exception.errorCode shouldBe "junifer errorCode"
                exception.errorSeverity shouldBe "junifer errorSeverity"
                exception.errorDescription shouldBe "junifer errorDescription"
                exception.errorResponse shouldNotBe null
                exception.errorResponse!!.request.method shouldBe HttpMethod.Post
                exception.errorResponse!!.call.request.url.encodedPath shouldBe path
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Post && request.url.encodedPath.contains("enrolCustomer".toRegex())) {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("junifer errorCode", "junifer errorSeverity", "junifer errorDescription")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}
