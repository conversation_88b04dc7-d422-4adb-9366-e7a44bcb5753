package energy.so.ac.junifer.migration.processors

import energy.so.ac.junifer.egress.processor.MeterReadingSyncProcessor
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadMigrationEvent
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingResult
import energy.so.ac.junifer.ingress.models.assets.GetMeterReadingResponse
import energy.so.ac.junifer.ingress.services.assets.JuniferAssetsService
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

class MeterReadingMigrationProcessorTest : BehaviorSpec({

    val mockJuniferAssetsService = mockk<JuniferAssetsService>()
    val mockMeterReadingSyncProcessor = mockk<MeterReadingSyncProcessor>()
    val sut = MeterReadingMigrationProcessor(mockJuniferAssetsService, mockMeterReadingSyncProcessor, 5, 6)


    given("A list of meter reading results returns from junifer service") {
        coEvery {
            mockJuniferAssetsService.getMeterReadings(
                any(),
                any(),
                any(),
                null
            )
        } returns GetMeterReadingResponse(
            results = listOf(
                meterReadingResult.copy(id = 11111L),
                meterReadingResult.copy(id = 22222L)
            )
        )
        coEvery { mockMeterReadingSyncProcessor.syncMeterReading(any(), any()) } returns Unit
        mockkStatic(Clock::class)
        val fixedInstant = Instant.parse("2023-05-25T00:00:00Z")
        val fixedClock = Clock.fixed(fixedInstant, ZoneId.systemDefault())
        every { Clock.systemDefaultZone() } returns fixedClock
        `when`("call process") {
            sut.process(meterReadMigrationEvent)

            then("should be able to call correct number of sync meter reading") {
                coVerify { mockMeterReadingSyncProcessor.syncMeterReading(any(), 123L) }
            }
        }
        unmockkStatic(Clock::class)
    }

})

