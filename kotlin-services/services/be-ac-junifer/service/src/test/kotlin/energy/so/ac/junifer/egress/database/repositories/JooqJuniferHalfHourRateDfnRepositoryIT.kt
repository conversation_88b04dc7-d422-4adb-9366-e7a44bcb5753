package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ID
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testHalfHourRateDfn
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testHalfHourRateDfnItemPeak
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testRateNamePeak
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.references.JUNIFER__HALFHOURRATEDFN
import energy.so.commons.model.tables.references.JUNIFER__HALFHOURRATEDFNITEM
import energy.so.commons.model.tables.references.JUNIFER__RATENAME
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext
import org.junit.jupiter.api.assertThrows

class JooqJuniferHalfHourRateDfnRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferHalfHourRateDfnRepository(db)

    given("A valid setup for a half hour rate dfn") {
        insertHalfHourRateDfn(db)
        `when`("fetch existent half hour rate dfn") {
            val hhRate = repo.getHalfHourRateDfn(JUNIFER_HALF_HOUR_RATE_DFN_ID)

            then("half hour rate dfn retrieved") {
                hhRate.id shouldBe JUNIFER_HALF_HOUR_RATE_DFN_ID
                hhRate.name shouldBe testHalfHourRateDfn.name
                hhRate.status shouldBe testHalfHourRateDfn.status
                hhRate.matchingtype shouldBe testHalfHourRateDfn.matchingtype
                hhRate.ratenamegroupfk shouldBe testHalfHourRateDfn.ratenamegroupfk
                hhRate.createddttm shouldBe testHalfHourRateDfn.createddttm
                hhRate.activatedusertblfk shouldBe testHalfHourRateDfn.activatedusertblfk
                hhRate.activateddttm shouldBe testHalfHourRateDfn.activateddttm
                hhRate.withdrawnusertblfk shouldBe testHalfHourRateDfn.withdrawnusertblfk
                hhRate.withdrawndttm shouldBe testHalfHourRateDfn.withdrawndttm
                hhRate.reference shouldBe testHalfHourRateDfn.reference
                hhRate.deletefl shouldBe testHalfHourRateDfn.deletefl
                hhRate.versionno shouldBe testHalfHourRateDfn.versionno
                hhRate.partitionid shouldBe testHalfHourRateDfn.partitionid
            }
        }
        `when`("fetch non-existent half hour rate dfn") {

            then("error thrown") {
                assertThrows<EntityNotFoundException> { repo.getHalfHourRateDfn(ID_1) }
            }
        }
    }

    given("A valid setup for a half hour rate dfn item") {
        insertHalfHourRateDfnItem(db)
        `when`("fetch existent half hour rate dfn item") {
            val hhRateItem = repo.getHalfHourRateDfnItem(HALF_HOUR_RATE_DFN_ITEM_ID)

            then("half hour rate dfn retrieved") {
                hhRateItem.id shouldBe HALF_HOUR_RATE_DFN_ITEM_ID
                hhRateItem.name shouldBe testRateNamePeak.name
                hhRateItem.halfHourRateDfnFk shouldBe JUNIFER_HALF_HOUR_RATE_DFN_ID
                hhRateItem.fromHHMM shouldBe testHalfHourRateDfnItemPeak.fromhhmm
                hhRateItem.toHHMM shouldBe testHalfHourRateDfnItemPeak.tohhmm
                hhRateItem.deleteFl shouldBe testHalfHourRateDfnItemPeak.deletefl
                hhRateItem.rateNameFk shouldBe testRateNamePeak.id
            }
        }
        `when`("fetch non-existent half hour rate dfn item") {

            then("error thrown") {
                assertThrows<EntityNotFoundException> { repo.getHalfHourRateDfnItem(ID_1) }
            }
        }
    }
})

private fun insertHalfHourRateDfn(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__HALFHOURRATEDFN,
            testHalfHourRateDfn
        )
    )
}

private fun insertHalfHourRateDfnItem(db: DSLContext) {
    db.executeInsert(
        db.newRecord(
            JUNIFER__HALFHOURRATEDFN,
            testHalfHourRateDfn
        )
    )

    db.executeInsert(
        db.newRecord(
            JUNIFER__RATENAME,
            testRateNamePeak
        )
    )

    db.executeInsert(
        db.newRecord(
            JUNIFER__HALFHOURRATEDFNITEM,
            testHalfHourRateDfnItemPeak
        )
    )
}
