package energy.so.ac.junifer.egress.broadcast

import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kubernetes.client.openapi.apis.CoreV1Api
import io.kubernetes.client.openapi.models.V1EndpointAddress
import io.kubernetes.client.openapi.models.V1EndpointSubset
import io.kubernetes.client.openapi.models.V1Endpoints
import io.kubernetes.client.openapi.models.V1EndpointsList
import io.kubernetes.client.openapi.models.V1ObjectMeta
import io.mockk.every
import io.mockk.mockk

class ServiceVariantsResolverTest : BehaviorSpec({

    val k8sApi = mockk<CoreV1Api>()
    val subject = ServiceVariantsResolver(k8sApi)

    given("no service variants exists in k8s") {
        val mainService = "be-assets.preprod.svc.cluster.local:50051"
        every {
            k8sApi.listNamespacedEndpoints(
                "preprod",
                any(),
                any(),
                any(),
                any(),
                "app.kubernetes.io/name=be-assets",
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns V1EndpointsList().items(
            listOf(
                k8sEndpoint(name = "be-assets", instanceLabel = "be-assets"),
                k8sEndpoint(name = "be-assets", instanceLabel = "be-assets-headless")
            )
        )

        `when`("retrieve service variants") {
            val result = subject.findAllVariants(mainService)
            then("returned service variants is empty") {
                result.shouldBeEmpty()
            }
        }

    }

    given("service variants exists in k8s") {
        val mainService = "be-tickets.staging.svc.cluster.local:50051"
        every {
            k8sApi.listNamespacedEndpoints(
                "staging",
                any(),
                any(),
                any(),
                any(),
                "app.kubernetes.io/name=be-tickets",
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns V1EndpointsList().items(
            listOf(
                k8sEndpoint(name = "be-tickets-so-123", instanceLabel = "be-tickets-so-123"),
                k8sEndpoint(name = "be-tickets-so-124", instanceLabel = "be-tickets-so-124"),
                k8sEndpoint(name = "be-tickets-so-124-headless", instanceLabel = "be-tickets-so-124"),
                k8sEndpoint(name = "be-tickets-so-125", instanceLabel = "be-tickets-so-125", addresses = emptyList()),
                k8sEndpoint(name = "be-tickets", instanceLabel = "be-tickets")
            )
        )
        `when`("retrieve service variants") {
            val result = subject.findAllVariants(mainService)

            then("returned service variants list is correct") {
                result.size shouldBe 2
                result shouldContainExactlyInAnyOrder listOf("so-123", "so-124")
            }
        }
    }
})

private fun k8sEndpoint(
    name: String,
    instanceLabel: String,
    addresses: List<String> = listOf("127.0.0.1"),
) = V1Endpoints()
    .metadata(
        V1ObjectMeta()
            .name(name)
            .labels(mapOf("app.kubernetes.io/instance" to instanceLabel))
    ).subsets(
        addresses.takeIf { it.isNotEmpty() }?.map { V1EndpointSubset().addAddressesItem(V1EndpointAddress().ip(it)) }
    )
