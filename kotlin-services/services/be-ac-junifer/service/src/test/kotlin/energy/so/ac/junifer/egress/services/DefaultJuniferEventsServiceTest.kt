package energy.so.ac.junifer.egress.services

import com.google.protobuf.util.JsonFormat
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import energy.so.ac.junifer.fixtures.EventsPrecannedData.billUkEvent
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.enums.OperationType
import energy.so.commons.model.tables.pojos.SyncEvent
import io.kotest.core.spec.style.FunSpec
import io.kotest.datatest.withData
import io.mockk.confirmVerified
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.jooq.JSON

class DefaultJuniferEventsServiceTest : FunSpec({

    val mockRepo = mockk<SyncEventRepository>()

    val sut = DefaultJuniferEventsService(mockRepo)

    afterEach {
        confirmVerified(mockRepo)
    }

    context("a valid UK Bill event") {
        context("a bill updated event is received") {
            justRun { mockRepo.insertEvent(any()) }

            withData(
                "BillAccepted",
                "BillUpdate",
                "BillTransactionAccepted",
                "BillTransactionCancelled",
                "BillPaymentReminder",
                "FinalBillAccepted"
            ) {
                val event = billUkEvent.setEventType(it).build()
                sut.handleUkEvent(event)

                verify {
                    mockRepo.insertEvent(
                        SyncEvent(
                            reference = event.deliveryIdentifier.toString(),
                            eventType = EventType.BILL,
                            operationType = OperationType.UPDATE,
                            additionalData = JSON.json(JsonFormat.printer().print(event)),
                        )
                    )
                }
            }
        }
    }

    context("a valid UK meter reading event") {
        context("a meter reading updated event is received") {
            justRun { mockRepo.insertEvent(any()) }

            withData(
                "MeterReadingUpdatedEvent",
                "MeterReadingAccepted"
            ) {
                val event = billUkEvent.setEventType(it).build()
                sut.handleUkEvent(event)

                verify {
                    mockRepo.insertEvent(
                        SyncEvent(
                            reference = event.deliveryIdentifier.toString(),
                            eventType = EventType.METER_READING,
                            operationType = OperationType.UPDATE,
                            additionalData = JSON.json(JsonFormat.printer().print(event)),
                        )
                    )
                }
            }
        }

        context("a meter reading created event is received") {
            justRun { mockRepo.insertEvent(any()) }

            val event = billUkEvent.setEventType("MeterReadingCreationEvent").build()
            sut.handleUkEvent(event)

            verify {
                mockRepo.insertEvent(
                    SyncEvent(
                        reference = event.deliveryIdentifier.toString(),
                        eventType = EventType.METER_READING,
                        operationType = OperationType.INSERT,
                        additionalData = JSON.json(JsonFormat.printer().print(event)),
                    )
                )
            }
        }
    }

    context("The event is not supported") {
        val event = billUkEvent.setEventType("Unsupported").build()
        sut.handleUkEvent(event)
        context("nothing should happen") {}
    }
})
