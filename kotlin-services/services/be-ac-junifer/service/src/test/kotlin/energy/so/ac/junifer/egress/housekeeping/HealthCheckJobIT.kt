package energy.so.ac.junifer.egress.housekeeping

import energy.so.commons.model.tables.pojos.SyncSourceHealthCheck
import energy.so.commons.model.tables.references.SYNC_SOURCE_HEALTH_CHECK
import energy.so.database.test.installDatabase
import io.kotest.assertions.fail
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDateTime
import org.jooq.DSLContext

class HealthCheckJobIT : BehaviorSpec({

    val db = installDatabase()
    val sut = HealthCheckJob(db)

    given("old rows in the health check table") {
        val table1 = "test_1"
        val table2 = "test_2"

        insertRow(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), table1, db)
        insertRow(LocalDateTime.of(2020, 2, 1, 0, 0, 0, 0), table1, db)
        insertRow(LocalDateTime.of(2020, 3, 1, 0, 0, 0, 0), table1, db)
        insertRow(LocalDateTime.of(2021, 2, 1, 0, 0, 0, 0), table2, db)
        insertRow(LocalDateTime.of(2022, 2, 1, 0, 0, 0, 0), table2, db)
        insertRow(LocalDateTime.of(2023, 2, 1, 0, 0, 0, 0), table2, db)
        insertRow(LocalDateTime.of(2024, 2, 1, 0, 0, 0, 0), table2, db)
        insertRow(LocalDateTime.of(2025, 2, 1, 0, 0, 0, 0), table2, db)

        `when`("the cleanup job is run") {
            sut.runCleanup()

            val returned = db.selectFrom(SYNC_SOURCE_HEALTH_CHECK).fetchInto(SyncSourceHealthCheck::class.java)

            then("only the most recent rows per table should be returned") {

                returned.size shouldBe 2
                returned.forEach {
                    when (it.tableName) {
                        table1 -> {
                            it.lastModified shouldBe LocalDateTime.of(2020, 3, 1, 0, 0, 0, 0)
                        }

                        table2 -> {
                            it.lastModified shouldBe LocalDateTime.of(2025, 2, 1, 0, 0, 0, 0)
                        }

                        else -> fail("Unexpected table name returned")
                    }
                }
            }
        }
    }

    given("existing rows in the health check table") {
        insertRow(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "test", db)
        insertRow(LocalDateTime.of(2020, 2, 1, 0, 0, 0, 0), "test1", db)
        insertRow(LocalDateTime.of(2020, 3, 1, 0, 0, 0, 0), "test2", db)
        `when`("the health check job is run") {
            sut.runHealthCheck()
            then("return the most recent update") {
                // Not the most verifiable test here! This just checks the query and timestamp formatting does not error.
            }
        }
    }
})

private fun insertRow(time: LocalDateTime, table: String, dslContext: DSLContext) =
    dslContext.insertInto(
        SYNC_SOURCE_HEALTH_CHECK,
        SYNC_SOURCE_HEALTH_CHECK.LAST_MODIFIED,
        SYNC_SOURCE_HEALTH_CHECK.TABLE_NAME
    )
        .values(time, table)
        .execute()
