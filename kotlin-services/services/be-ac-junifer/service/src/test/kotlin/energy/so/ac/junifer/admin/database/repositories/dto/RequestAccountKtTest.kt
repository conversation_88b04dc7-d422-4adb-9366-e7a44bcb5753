package energy.so.ac.junifer.admin.database.repositories.dto

import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderRequestV2
import energy.so.commons.grpc.extensions.getValueOrNull
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class RequestAccountKtTest : BehaviorSpec({

    given("grpc request") {
        `when`("map to dto") {
            val actual = accountFinderRequestV2.fromGrpcRequest()
            then("then return mapped object") {
                assertSoftly {
                    actual.isSeasonalFlag shouldBe accountFinderRequestV2.isSeasonalFlag
                    actual.isDirectDebit shouldBe accountFinderRequestV2.isDirectDebit
                    actual.isSmartMeter shouldBe accountFinderRequestV2.isSmartMeter
                    actual.fuel shouldBe FuelEnum.GAS_ONLY
                    actual.isSingleRate shouldBe accountFinderRequestV2.isSingleRate.getValueOrNull()
                    actual.pageNumber shouldBe accountFinderRequestV2.pageNumber.getValueOrNull()
                    actual.limit shouldBe accountFinderRequestV2.limit.getValueOrNull()
                }
            }
        }
    }
})
