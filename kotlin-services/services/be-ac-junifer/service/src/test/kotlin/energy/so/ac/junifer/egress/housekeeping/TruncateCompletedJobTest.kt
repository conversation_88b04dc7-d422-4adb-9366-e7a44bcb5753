package energy.so.ac.junifer.egress.housekeeping

import energy.so.ac.junifer.config.JobTruncateCompletedConfig
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class TruncateCompletedJobTest : BehaviorSpec({
    isolationMode = IsolationMode.InstancePerTest

    val mockSyncRepository = mockk<SyncEventRepository>()
    val deletedSize = 50
    val noOfHours = 720L
    val config = JobTruncateCompletedConfig(noOfHours)

    val sut = TruncateCompletedJob(mockSyncRepository, config)

    given("TruncateCompletedJob::run") {
        and("truncate process failed") {
            coEvery { mockSyncRepository.truncateCompletedEventsOlderThan(noOfHours) } throws IllegalStateException()

            `when`("job run") {
                then("job fails") {
                    assertThrows<IllegalStateException> { sut.run() }
                    coVerify { mockSyncRepository.truncateCompletedEventsOlderThan(noOfHours) }
                }
            }
        }

        and("truncate process succeeded") {
            coEvery { mockSyncRepository.truncateCompletedEventsOlderThan(noOfHours) } returns deletedSize

            `when`("job run") {
                sut.run()

                then("job succeeded") {
                    coVerify { mockSyncRepository.truncateCompletedEventsOlderThan(noOfHours) }
                }
            }
        }
    }
})
