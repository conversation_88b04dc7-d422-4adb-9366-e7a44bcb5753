package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.BillPrecannedData.billLine
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLineProto
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLineProtoWithoutLinkage
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLineWithoutLinkage
import energy.so.ac.junifer.fixtures.BillPrecannedData.billLinesResponse
import energy.so.ac.junifer.ingress.models.bills.toResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class BillLineTest : BehaviorSpec({
    given("bill line model") {
        and("single model") {
            val model = billLine

            `when`("call BillLine toResponse") {
                val result = model.toResponse()

                then("result should match") {
                    result shouldBe billLineProto
                }
            }
        }

        and("single model without linkage") {
            val model = billLineWithoutLinkage

            `when`("call BillLine toResponse") {
                val result = model.toResponse()

                then("result should match") {
                    result shouldBe billLineProtoWithoutLinkage
                }
            }
        }

        and("bill lines model list") {
            val modelList = listOf(billLine)

            `when`("call BillLine toResponse") {
                val result = modelList.toResponse()

                then("result should match") {
                    result shouldBe billLinesResponse
                }
            }
        }
    }
})
