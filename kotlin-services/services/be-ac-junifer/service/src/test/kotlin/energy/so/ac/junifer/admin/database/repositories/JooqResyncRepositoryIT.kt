package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.egress.database.repositories.getSql
import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.assertThrows
import kotlin.test.fail

const val CREATE_CUSTOMER_SQL = "/sql/customer/create_customer.sql"
const val CREATE_ASSET_SQL = "/sql/assets/create_customer_asset.sql"
const val CREATE_PAYMENT_SQL = "/sql/payments/create_customer_payments.sql"
const val CREATE_LONE_REFERENCES_SQL = "/sql/loneRefs/create_lone_refs.sql"

class JooqResyncRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val sut = JooqResyncRepository(db, "test")

    given("an existing customer on the database") {
        val customerSql = SqlLoader.loadFromStream(getSql(CREATE_CUSTOMER_SQL))
        val existingAccountId = 691791L

        `when`("customer reference IDs are requested") {
            db.execute(customerSql)

            val returnedReferences = sut.getCustomerReferences(existingAccountId)

            then("valid references should be returned") {
                returnedReferences shouldNotBe null
                returnedReferences.juniferAccountId shouldBe existingAccountId
                returnedReferences.customerIds shouldBe "740972"
                returnedReferences.agreementIds shouldBe "456"
                returnedReferences.customerPropertyIds shouldBe "343"
                returnedReferences.customerPsrIds shouldBe "505722,505723,505822,506422"
                returnedReferences.customerContactIds shouldBe "891483"
                returnedReferences.accountContactIds shouldBe "818336,854298"
                returnedReferences.contactIdsCustomer shouldBe "1097587"
                returnedReferences.contactIdsAccount shouldBe "1024275,982487"
                returnedReferences.addressIdsCustomer shouldBe "1719032"
                returnedReferences.addressIdsAccount shouldBe "1538607,1592555"
                returnedReferences.customVulnerabilityIds shouldBe "5057221,5057231,5058221,5064221"
                returnedReferences.ticketIdsCustomer shouldBe "********"
                returnedReferences.ticketHistoryIdsCustomer shouldBe "20"
                returnedReferences.noteIdsCustomer shouldBe "1000"
            }
        }
    }

    given("existing assets linked to an account on the database") {
        val assetSql = SqlLoader.loadFromStream(getSql(CREATE_ASSET_SQL))
        val existingAccountId = 691791L

        `when`("asset reference IDs are requested") {
            db.execute(assetSql)

            val returnedReferences = sut.getAssetReferences(existingAccountId)

            then("valid references should be returned") {
                returnedReferences shouldNotBe null
                returnedReferences.juniferAccountId shouldBe existingAccountId
                returnedReferences.meterPointIds shouldBe "123,124"
                returnedReferences.meterRegisterIds shouldBe "456"
                returnedReferences.meterPointMeterRelIds shouldBe "761823"
                returnedReferences.meterIds shouldBe "123"
                returnedReferences.mpanIds shouldBe "456"
                returnedReferences.mpanHistIds shouldBe "1009197,1029619,369035,369042,373306,383654,408204,992706"
                returnedReferences.mprnIds shouldBe "456"
                returnedReferences.mprnHistIds shouldBe "297742,297803,297950,304029,304799"
                returnedReferences.propIds shouldBe "456"
            }
        }
    }

    given("existing payments linked to an account in db") {
        val paymentSql = SqlLoader.loadFromStream(getSql(CREATE_PAYMENT_SQL))
        val existingAccountId = 344452L

        `when`("asset reference IDs are requested") {
            db.execute(paymentSql)

            val returnedReferences = sut.getPaymentReferences(existingAccountId)

            then("valid references should be returned") {
                returnedReferences shouldNotBe null
                returnedReferences.juniferAccountId shouldBe existingAccountId
                returnedReferences.paymentMethodIds shouldBe setOf("341354")
                returnedReferences.paymentPlanIds shouldBe setOf(
                    "15056",
                    "15058",
                    "15059",
                    "15057",
                    "15060",
                    "15062",
                    "15063"
                )
                returnedReferences.paymentPlanPaymentIds shouldBe setOf(
                    "298136",
                    "328845",
                    "319409",
                    "224676",
                    "103019",
                    "306248"
                )
                returnedReferences.paymentPlanPaymentTxnIds shouldBe setOf("364935")
                returnedReferences.paymentRequestIds shouldBe setOf("********")
                returnedReferences.accountTransactionIds shouldBe setOf("********")
                returnedReferences.billPeriodIds shouldBe setOf("********", "********", "********")
                returnedReferences.billIds shouldBe setOf("********")
                returnedReferences.directDebitIds shouldBe setOf("1471390")
                returnedReferences.directDebitCollectionIds shouldBe setOf("********")
                returnedReferences.paySchedPeriodIds shouldBe setOf(
                    "1117384",
                    "1597982",
                    "1892921",
                    "2070039",
                    "2134005",
                    "2353205",
                    "2353410",
                    "2982493",
                    "3182454",
                    "703756",
                    "996324"
                )
                returnedReferences.paySchedItemIds shouldBe setOf(
                    "********",
                    "********",
                    "********",
                    "********",
                    "11188499",
                    "11444099",
                    "11697325",
                    "11950654",
                    "3108623",
                    "3294728",
                    "3476343",
                    "3654766",
                    "3829897",
                    "4006080",
                    "4207617",
                    "4399782",
                    "4603820",
                    "4816744",
                    "5040019",
                    "5283748",
                    "5503407",
                    "5715182",
                    "5967605",
                    "6220283",
                    "6448234",
                    "6688981",
                    "6929605",
                    "7154749",
                    "7376631",
                    "7595769",
                    "7811408",
                    "8070949",
                    "8341692",
                    "8632112",
                    "8923952",
                    "9247591",
                    "9566764",
                    "9842338"
                )
                returnedReferences.accountCreditIds shouldBe setOf("4400678")
            }
        }
    }

    given("createEventTriggerFunction") {
        val query = SqlLoader.loadFromStream(getSql(CREATE_EVENT_TRIGGER_FUNCTION_SQL))

        `when`("method called") {
            try {
                db.execute(query)
            } catch (ex: Exception) {
                fail("execution failed: ${ex.message}")
            }
        }
    }

    given("createMockEventTriggerFunction") {
        val query = SqlLoader.loadFromStream(getSql(CREATE_MOCK_EVENT_TRIGGER_FUNCTION_SQL))

        `when`("method called") {
            try {
                db.execute(query)
            } catch (ex: Exception) {
                fail("execution failed: ${ex.message}")
            }
        }

        `when`("on production environment") {
            assertThrows<IllegalStateException> {
                JooqResyncRepository(
                    db,
                    "production"
                ).createMockEventTriggerFunction()
            }.message shouldBe "Illegal call of method in production environment"
        }
    }

    given("existing lone references on the database") {
        val loneReferencesSql = SqlLoader.loadFromStream(getSql(CREATE_LONE_REFERENCES_SQL))

        `when`("methods called") {
            db.execute(loneReferencesSql)
            val returnedReferences = sut.getLoneRefs()

            then("valid references should be returned") {
                returnedReferences shouldNotBe null
                returnedReferences.customerPsrDfnIds shouldBe setOf("511", "106")
                returnedReferences.paySchedSeasonalIds shouldBe setOf("3")
                returnedReferences.productDfnIds shouldBe setOf("3701", "3712")
                returnedReferences.productBundleDfnIds shouldBe setOf("123")
                returnedReferences.halfHourRateDfnIds shouldBe setOf("1")
                returnedReferences.halfHourRateDfnItemIds shouldBe setOf("2", "3")
            }
        }
    }
})
