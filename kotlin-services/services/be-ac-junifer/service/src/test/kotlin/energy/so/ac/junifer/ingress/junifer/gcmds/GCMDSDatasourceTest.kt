package energy.so.ac.junifer.ingress.junifer.gcmds

import energy.so.ac.junifer.config.GCMDSConfig
import energy.so.ac.junifer.config.GCMDS_AUTH_TEMPLATE_BEAN
import energy.so.ac.junifer.config.GCMDS_TEMPLATE_BEAN
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSConstants.ACTIVE_IMPORT_STREAM
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSConstants.AUTH_GRANT_TYPE_PARAM
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSConstants.AUTH_GRANT_TYPE_VALUE
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSConstants.TRACE_ID_HEADER
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSMarketType
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSOrder
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSResolution
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSUnitOfMeasure
import energy.so.ac.junifer.ingress.models.gcmds.GetTimeSeriesRequest
import energy.so.ac.junifer.ingress.models.gcmds.ListMeterReadingsRequest
import energy.so.ac.junifer.ingress.models.gcmds.Reading
import energy.so.ac.junifer.ingress.models.gcmds.ReadingSource
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EndpointIdentifier
import energy.so.ac.junifer.mapping.JuniferCache
import energy.so.users.v2.FeatureName
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.io.BufferedReader
import java.io.InputStream
import java.net.URI
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import org.hamcrest.Matchers.notNullValue
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders.AUTHORIZATION
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit4.SpringRunner
import org.springframework.test.web.client.ExpectedCount
import org.springframework.test.web.client.MockRestServiceServer
import org.springframework.test.web.client.match.MockRestRequestMatchers.content
import org.springframework.test.web.client.match.MockRestRequestMatchers.header
import org.springframework.test.web.client.match.MockRestRequestMatchers.method
import org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo
import org.springframework.test.web.client.response.MockRestResponseCreators.withStatus
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate


const val AUTH_RESPONSE_JSON = "/json/gcmds_auth_response.json"
const val LIST_READINGS_RESPONSE_JSON = "/json/gcmds_list_readings_response.json"
const val TIME_SERIES_RESPONSE_JSON = "/json/gcmds_time_series_response.json"
const val ENCODED_TIME_SERIES_RESPONSE_JSON = "/json/encoded_time_series_response.json"
const val PAGINATED_RESPONSE_ASC_1_JSON = "/json/gcmds_paginated_response_asc_1.json"
const val PAGINATED_RESPONSE_ASC_2_JSON = "/json/gcmds_paginated_response_asc_2.json"
const val PAGINATED_RESPONSE_DESC_1_JSON = "/json/gcmds_paginated_response_desc_1.json"
const val PAGINATED_RESPONSE_DESC_2_JSON = "/json/gcmds_paginated_response_desc_2.json"


@SpringBootTest
@RunWith(SpringRunner::class)
@ContextConfiguration(classes = [RestConfig::class])
class GCMDSDatasourceTest(
    @Autowired config: GCMDSConfig,
    @Qualifier(GCMDS_TEMPLATE_BEAN) @Autowired val gcmdsTemplate: RestTemplate,
    @Qualifier(GCMDS_AUTH_TEMPLATE_BEAN) @Autowired val authTemplate: RestTemplate,
) : BehaviorSpec({

    val mockCache = mockk<JuniferCache>()
    val mockFeatureService = mockk<FeatureService>()

    val sut = GCMDSDatasource(
        config, mockCache, mockFeatureService, gcmdsTemplate, authTemplate,
    )

    val testToken =
        "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    fun mockTokenRequest(server: MockRestServiceServer) {

        val expectedFormData = LinkedMultiValueMap<String, String>()
        expectedFormData.add(AUTH_GRANT_TYPE_PARAM, AUTH_GRANT_TYPE_VALUE)

        server.expect(
            ExpectedCount.once(),
            requestTo(URI("https://uk.id.gentrackcloud.com/token"))
        )
            .andExpect(method(HttpMethod.POST))
            .andExpect(header(AUTHORIZATION, "Basic Y2xpZW50LWlkOnNlY3JldA=="))
            .andExpect(content().contentType("application/x-www-form-urlencoded;charset=UTF-8"))
            .andExpect(content().formData(expectedFormData))
            .andRespond(
                withStatus(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(loadTestJson(getResource(AUTH_RESPONSE_JSON)))
            )
    }

    beforeTest {
        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns false
    }

    afterEach {
        confirmVerified(mockCache, mockFeatureService)
    }

    given("a valid meter reading request") {
        `when`("the auth token is not cached") {

            val mockAuthServer = MockRestServiceServer.createServer(authTemplate)
            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)
            val timeNow = OffsetDateTime.now()
            val formattedEndDate = timeNow.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)

            // Mock the auth call
            mockTokenRequest(mockAuthServer)

            // Mock the meter readings call
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/1591052994825/readings?start=2025-01-01T12:13:34Z&end=$formattedEndDate&order=asc&limit=500&meterSerialNumber=1234&registerId=1235"))
            )
                .andExpect(method(HttpMethod.GET))
                .andExpect(header(TRACE_ID_HEADER, notNullValue()))
                .andExpect(header(AUTHORIZATION, "Bearer $testToken"))
                .andRespond(
                    withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(loadTestJson(getResource(LIST_READINGS_RESPONSE_JSON)))
                )

            // Mock the junifer cache call
            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns null
            justRun {
                mockCache.cacheResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY,
                    testToken,
                    3600 - 5,
                )
            }

            val readingsList = sut.getMeterReadings(
                ListMeterReadingsRequest(
                    GCMDSMarketType.MPAN,
                    "1591052994825",
                    OffsetDateTime.of(2025, 1, 1, 12, 13, 34, 0, ZoneOffset.UTC),
                    end = timeNow,
                    meterIdentifier = "1234",
                    registerIdentifier = "1235",
                )
            )

            then("the client should authenticate and return meter readings") {
                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }
                verify {
                    mockCache.cacheResponse(
                        EndpointIdentifier.GCMDS_TOKEN_CACHE,
                        GCMDS_TOKEN_CACHE_KEY,
                        testToken,
                        3600 - 5,
                    )
                }

                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }

                mockAuthServer.verify()
                mockGcmdsServer.verify()

                val body = readingsList
                body shouldNotBe null

                validateReadings(readingsList)
            }
        }

        `when`("the auth token is cached") {

            // The auth server should not be called in this test
            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)
            val timeNow = OffsetDateTime.now()
            val formattedEndDate = timeNow.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)

            // Mock the meter readings call
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/1591052994825/readings?start=2025-01-01T12:13:34Z&end=$formattedEndDate&order=asc&limit=500&meterSerialNumber=1234&registerId=1235"))
            )
                .andExpect(method(HttpMethod.GET))
                .andExpect(header(TRACE_ID_HEADER, notNullValue()))
                .andExpect(header(AUTHORIZATION, "Bearer $testToken"))
                .andRespond(
                    withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(loadTestJson(getResource(LIST_READINGS_RESPONSE_JSON)))
                )

            // Mock the junifer cache call
            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns testToken

            val readingsList = sut.getMeterReadings(
                ListMeterReadingsRequest(
                    GCMDSMarketType.MPAN,
                    "1591052994825",
                    OffsetDateTime.of(2025, 1, 1, 12, 13, 34, 0, ZoneOffset.UTC),
                    end = timeNow,
                    meterIdentifier = "1234",
                    registerIdentifier = "1235",
                )
            )

            then("the client should not re-authenticate and return meter readings") {
                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }

                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }

                mockGcmdsServer.verify()

                val body = readingsList
                body shouldNotBe null

                validateReadings(readingsList)
            }
        }
    }

    given("a valid time series request") {
        `when`("the auth token is not cached") {

            val mockAuthServer = MockRestServiceServer.createServer(authTemplate)
            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)

            mockTokenRequest(mockAuthServer)

            // Mock the time series call
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/1591052994825/charts/time-series?start=2025-01-01T00:00:00Z&end=2025-01-02T00:00:00Z&uom=kWh&resolution=30m"))
            )
                .andExpect(method(HttpMethod.GET))
                .andExpect(header(TRACE_ID_HEADER, notNullValue()))
                .andExpect(header(AUTHORIZATION, "Bearer $testToken"))
                .andRespond(
                    withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(loadTestJson(getResource(TIME_SERIES_RESPONSE_JSON)))
                )

            // Mock the junifer cache call
            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns null

            justRun {
                mockCache.cacheResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY,
                    testToken,
                    3600 - 5,
                )
            }

            val timeSeriesResponse = sut.getTimeSeries(
                GetTimeSeriesRequest(
                    GCMDSMarketType.MPAN,
                    "1591052994825",
                    OffsetDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    OffsetDateTime.of(2025, 1, 2, 0, 0, 0, 0, ZoneOffset.UTC),
                    GCMDSUnitOfMeasure.KWH,
                    GCMDSResolution.HALF_HOUR,
                )
            )

            then("the client should re-authenticate and return time series") {
                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }
                verify {
                    mockCache.cacheResponse(
                        EndpointIdentifier.GCMDS_TOKEN_CACHE,
                        GCMDS_TOKEN_CACHE_KEY,
                        testToken,
                        3600 - 5,
                    )
                }

                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }

                mockAuthServer.verify()
                mockGcmdsServer.verify()

                val body = timeSeriesResponse.body
                body shouldNotBe null
                body!!.streams.size shouldBe 1

                val stream = body.streams.first()
                stream.id shouldBe ACTIVE_IMPORT_STREAM
                stream.uom shouldBe GCMDSUnitOfMeasure.KWH
                stream.data.size shouldBe 48 // 1 day of hh
                stream.data[0] shouldBe 0.052
                stream.data[15] shouldBe null
            }
        }

        `when`("the auth token is cached") {

            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)

            // Mock the time series call
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/1591052994825/charts/time-series?start=2025-01-01T00:00:00Z&end=2025-01-02T00:00:00Z&uom=kWh&resolution=30m"))
            )
                .andExpect(method(HttpMethod.GET))
                .andExpect(header(TRACE_ID_HEADER, notNullValue()))
                .andExpect(header(AUTHORIZATION, "Bearer $testToken"))
                .andRespond(
                    withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(loadTestJson(getResource(TIME_SERIES_RESPONSE_JSON)))
                )

            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns testToken

            val timeSeriesResponse = sut.getTimeSeries(
                GetTimeSeriesRequest(
                    GCMDSMarketType.MPAN,
                    "1591052994825",
                    OffsetDateTime.of(2025, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    OffsetDateTime.of(2025, 1, 2, 0, 0, 0, 0, ZoneOffset.UTC),
                    GCMDSUnitOfMeasure.KWH,
                    GCMDSResolution.HALF_HOUR,
                )
            )

            then("the client should not re-authenticate and return time series") {

                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }

                mockGcmdsServer.verify()

                val body = timeSeriesResponse.body
                body shouldNotBe null
                body!!.streams.size shouldBe 1

                val stream = body.streams.first()
                stream.id shouldBe ACTIVE_IMPORT_STREAM
                stream.uom shouldBe GCMDSUnitOfMeasure.KWH
                stream.data.size shouldBe 48 // 1 day of hh
                stream.data[0] shouldBe 0.052
                stream.data[15] shouldBe null
            }
        }
    }

    given("a paginated meter reading request") {

        `when`("the request returns more than 500 readings in ascending order") {
            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)

            // First page
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(
                    URI(
                        "https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/123456/readings?start=2024-07-02T00:00:00Z&end=2025-06-11T00:00:00Z&order=asc&limit=500&meterSerialNumber=SN-1&registerId=REG-1"
                    )
                )
            ).andRespond(
                withStatus(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(loadTestJson(getResource(PAGINATED_RESPONSE_ASC_1_JSON)))
            )

            // Second page
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(
                    URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/123456/readings?start=2025-03-08T00:00:00Z&end=2025-06-11T00:00:00Z&order=asc&limit=500&meterSerialNumber=SN-1&registerId=REG-1")
                )
            ).andRespond(
                withStatus(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(loadTestJson(getResource(PAGINATED_RESPONSE_ASC_2_JSON)))
            )

            coEvery {
                mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION)
            } returns true

            // Mock the junifer cache call
            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns testToken

            val request = ListMeterReadingsRequest(
                marketType = GCMDSMarketType.MPAN,
                meterPointIdentifier = "123456",
                start = OffsetDateTime.parse("2024-07-02T00:00:00Z"),
                end = OffsetDateTime.parse("2025-06-11T00:00:00Z"),
                order = GCMDSOrder.ASC,
                limit = 500,
                meterIdentifier = "SN-1",
                registerIdentifier = "REG-1"
            )

            val result = sut.getMeterReadings(request)


            then("the number of meter readings should be over 500 and match GCMDS response") {
                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }
                result.size shouldBe 690
                result.first().currentRead.date shouldBe OffsetDateTime.parse("2024-07-02T00:00:00Z")
                result.last().currentRead.date shouldBe OffsetDateTime.parse("2025-06-11T00:00:00Z")
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                mockGcmdsServer.verify()
            }

        }

        `when`("the request returns more than 500 readings in descending order") {
            val mockGcmdsServer = MockRestServiceServer.createServer(gcmdsTemplate)

            // First page
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(
                    URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/123456/readings?start=2024-07-02T00:00:00Z&end=2025-06-11T00:00:00Z&order=desc&limit=500&meterSerialNumber=SN-1&registerId=REG-1")
                )
            ).andRespond(
                withStatus(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(loadTestJson(getResource(PAGINATED_RESPONSE_DESC_1_JSON)))
            )

            // Second page
            mockGcmdsServer.expect(
                ExpectedCount.once(),
                requestTo(
                    URI("https://api.meter-data.uk1.gentrack.cloud/service-points/MPAN/123456/readings?start=2024-07-02T00:00:00Z&end=2024-10-04T00:00:00Z&order=desc&limit=500&meterSerialNumber=SN-1&registerId=REG-1")
                )
            ).andRespond(
                withStatus(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(loadTestJson(getResource(PAGINATED_RESPONSE_DESC_2_JSON)))
            )

            coEvery {
                mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION)
            } returns true

            // Mock the junifer cache call
            every {
                mockCache.getCachedResponse(
                    EndpointIdentifier.GCMDS_TOKEN_CACHE,
                    GCMDS_TOKEN_CACHE_KEY
                )
            } returns testToken

            val request = ListMeterReadingsRequest(
                marketType = GCMDSMarketType.MPAN,
                meterPointIdentifier = "123456",
                start = OffsetDateTime.parse("2024-07-02T00:00:00Z"),
                end = OffsetDateTime.parse("2025-06-11T00:00:00Z"),
                order = GCMDSOrder.DESC,
                limit = 500,
                meterIdentifier = "SN-1",
                registerIdentifier = "REG-1"
            )

            val result = sut.getMeterReadings(request)

            then("the number of meter readings should be over 500 and match GCMDS response") {
                verify { mockCache.getCachedResponse(EndpointIdentifier.GCMDS_TOKEN_CACHE, GCMDS_TOKEN_CACHE_KEY) }
                result.size shouldBe 688
                result.first().currentRead.date shouldBe OffsetDateTime.parse("2025-06-10T00:00:00Z")
                result.last().currentRead.date shouldBe OffsetDateTime.parse("2024-07-02T00:00:00Z")
                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                mockGcmdsServer.verify()
            }
        }
    }
})

/**
 * Ensure our rest templates we use for testing are set up with the same deserialization config as they would be when
 * running the service.
 */
@Configuration
@ConfigurationPropertiesScan(basePackages = ["energy.so.ac.junifer.config"])
@ComponentScan(basePackages = ["energy.so.ac.junifer.config"])
class RestConfig

fun validateReadings(readings: List<Reading>) {

    readings shouldNotBe null
    readings.size shouldBe 3

    readings.forEach {
        when (it.externalId + " - " + it.registerId) {
            "a86baf9e-5569-489f-9e53-77dc319e7829 - 1" -> {
                it.meterSerialNumber shouldBe "23L3006044"
                it.uom shouldBe "Wh"
                it.source shouldBe ReadingSource.Direct
                it.version shouldBe OffsetDateTime.of(2024, 6, 24, 1, 11, 55, 0, ZoneOffset.UTC)

                it.currentRead shouldNotBe null
                it.currentRead.reading shouldBe "2347096"
                it.currentRead.date shouldBe OffsetDateTime.of(2024, 6, 23, 0, 0, 0, 0, ZoneOffset.UTC)
                it.currentRead.quality shouldBe "A"

                // We don't care about what is in additionalInformation currently
                it.additionalInformation shouldNotBe null
            }

            "a86baf9e-5569-489f-9e53-77dc319e7829 - AI" -> {
                it.meterSerialNumber shouldBe "23L3006044"
                it.uom shouldBe "Wh"
                it.source shouldBe ReadingSource.Direct
                it.version shouldBe OffsetDateTime.of(2024, 6, 24, 1, 11, 55, 0, ZoneOffset.UTC)

                it.currentRead shouldNotBe null
                it.currentRead.reading shouldBe "2347096"
                it.currentRead.date shouldBe OffsetDateTime.of(2024, 6, 23, 0, 0, 0, 0, ZoneOffset.UTC)
                it.currentRead.quality shouldBe "A"

                // We don't care about what is in additionalInformation currently
                it.additionalInformation shouldNotBe null
            }

            "52cdcc05-f581-4d48-9415-5f95dc75e598 - 1" -> {
                it.meterSerialNumber shouldBe "23L3006044"
                it.uom shouldBe "Wh"
                it.quantity shouldBe 12251
                it.source shouldBe ReadingSource.Direct
                it.version shouldBe OffsetDateTime.of(2024, 6, 25, 1, 12, 2, 0, ZoneOffset.UTC)

                it.currentRead shouldNotBe null
                it.currentRead.reading shouldBe "2359347"
                it.currentRead.date shouldBe OffsetDateTime.of(2024, 6, 24, 0, 0, 0, 0, ZoneOffset.UTC)
                it.currentRead.quality shouldBe "A"

                it.previousRead shouldNotBe null
                it.previousRead!!.reading shouldBe "2347096"
                it.previousRead!!.date shouldBe OffsetDateTime.of(2024, 6, 23, 0, 0, 0, 0, ZoneOffset.UTC)
                it.previousRead!!.quality shouldBe "A"

                // We don't care about what is in additionalInformation currently
                it.additionalInformation shouldNotBe null
            }
        }
    }
}

fun getResource(resource: String) = GCMDSDatasourceTest::class.java.getResourceAsStream(resource)
    ?: throw IllegalStateException("Could not find test resource $resource")

fun loadTestJson(stream: InputStream) = stream.use {
    it.bufferedReader().use(BufferedReader::readText)
}
