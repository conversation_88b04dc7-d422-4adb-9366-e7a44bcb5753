package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.CustomerPrecannedData
import energy.so.ac.junifer.ingress.models.customers.toResponse
import energy.so.commons.grpc.utils.toTimestamp
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferGetCustomerResponseTest : BehaviorSpec({

    given("JuniferGetCustomerResponse") {
        val juniferResponse = CustomerPrecannedData.getCustomerData

        `when`("call toResponse method") {
            val result = juniferResponse.toResponse()

            then("result match") {
                result.id shouldBe juniferResponse.id
                result.name shouldBe juniferResponse.name
                result.number shouldBe juniferResponse.number
                result.customerClass shouldBe juniferResponse.customerClass
                result.customerType shouldBe juniferResponse.customerType
                result.state shouldBe juniferResponse.state
                result.title shouldBe juniferResponse.title
                result.forename shouldBe juniferResponse.forename
                result.surname shouldBe juniferResponse.surname
                result.companyName shouldBe juniferResponse.companyName
                result.companyNumber shouldBe juniferResponse.companyNumber
                result.companyAddress?.addressType shouldBe juniferResponse.companyAddress?.addressType
                result.companyAddress?.address1 shouldBe juniferResponse.companyAddress?.address1
                result.companyAddress?.address2 shouldBe juniferResponse.companyAddress?.address2
                result.companyAddress?.address3 shouldBe juniferResponse.companyAddress?.address3
                result.companyAddress?.address4 shouldBe juniferResponse.companyAddress?.address4
                result.companyAddress?.address5 shouldBe juniferResponse.companyAddress?.address5
                result.companyAddress?.postcode shouldBe juniferResponse.companyAddress?.postcode
                result.companyAddress?.countryCode shouldBe juniferResponse.companyAddress?.countryCode
                result.companyAddress?.country shouldBe juniferResponse.companyAddress?.country
                result.bereavementFl shouldBe juniferResponse.bereavementFl
                result.marketingOptOutFl shouldBe juniferResponse.marketingOptOutFl
                result.creditScore shouldBe juniferResponse.creditScore
                result.taxExemptReason shouldBe juniferResponse.taxExemptReason
                result.primaryContact.id shouldBe juniferResponse.primaryContact.id
                result.primaryContact.contactType shouldBe juniferResponse.primaryContact.contactType
                result.primaryContact.primary shouldBe juniferResponse.primaryContact.primary
                result.primaryContact.title shouldBe juniferResponse.primaryContact.title
                result.primaryContact.forename shouldBe juniferResponse.primaryContact.forename
                result.primaryContact.surname shouldBe juniferResponse.primaryContact.surname
                result.primaryContact.initials shouldBe juniferResponse.primaryContact.initials
                result.primaryContact.jobTitle shouldBe juniferResponse.primaryContact.jobTitle
                result.primaryContact.address.addressType shouldBe juniferResponse.primaryContact.address?.addressType
                result.primaryContact.address.address1 shouldBe juniferResponse.primaryContact.address?.address1
                result.primaryContact.address.address2 shouldBe juniferResponse.primaryContact.address?.address2
                result.primaryContact.address.address3 shouldBe juniferResponse.primaryContact.address?.address3
                result.primaryContact.address.address4 shouldBe juniferResponse.primaryContact.address?.address4
                result.primaryContact.address.address5 shouldBe juniferResponse.primaryContact.address?.address5
                result.primaryContact.address.postcode shouldBe juniferResponse.primaryContact.address?.postcode
                result.primaryContact.address.countryCode shouldBe juniferResponse.primaryContact.address?.countryCode
                result.primaryContact.address.country shouldBe juniferResponse.primaryContact.address?.country
                result.primaryContact.email shouldBe juniferResponse.primaryContact.email
                result.primaryContact.dateOfBirth shouldBe juniferResponse.primaryContact.dateOfBirth?.toTimestamp()
                result.primaryContact.phoneNumber1 shouldBe juniferResponse.primaryContact.phoneNumber1
                result.primaryContact.phoneNumber2 shouldBe juniferResponse.primaryContact.phoneNumber2
                result.primaryContact.phoneNumber3 shouldBe juniferResponse.primaryContact.phoneNumber3
                result.primaryContact.customerContactId shouldBe juniferResponse.primaryContact.customerContactId
                result.primaryContact.cancelled shouldBe juniferResponse.primaryContact.cancelled
                result.primaryContact.fromDttm shouldBe juniferResponse.primaryContact.fromDttm?.toTimestamp()
                result.primaryContact.toDttm shouldBe juniferResponse.primaryContact.toDttm?.toTimestamp()
                result.primaryContact.links.self shouldBe juniferResponse.primaryContact.links?.self
                result.links.self shouldBe juniferResponse.links?.self
                result.links.accounts shouldBe juniferResponse.links?.accounts
                result.links.securityQuestions shouldBe juniferResponse.links?.securityQuestions
                result.links.billingEntities shouldBe juniferResponse.links?.billingEntities
                result.links.prospects shouldBe juniferResponse.links?.prospects
            }
        }
    }
})
