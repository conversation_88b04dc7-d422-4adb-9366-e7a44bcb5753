package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountRepaymentRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountRepaymentRequest
import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountRepaymentRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCreateAccountRepaymentRequestTest : BehaviorSpec({

    given("create account repayment request") {
        val request = createAccountRepaymentRequest

        `when`("map to junifer create account repayment request") {
            val juniferRequest = JuniferCreateAccountRepaymentRequest.fromCreateAccountRepaymentRequest(request)

            then("return a corresponding JuniferCreateAccountRepaymentRequest") {
                juniferRequest shouldBe juniferCreateAccountRepaymentRequest
            }
        }
    }
})
