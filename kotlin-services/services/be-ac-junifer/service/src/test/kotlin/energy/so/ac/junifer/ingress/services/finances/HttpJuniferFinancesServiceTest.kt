package energy.so.ac.junifer.ingress.services.finances

import energy.so.ac.junifer.fixtures.activeSeasonalDefinitionJsonResponse
import energy.so.ac.junifer.fixtures.activeSeasonalDefinitionResponse
import energy.so.ac.junifer.fixtures.createPaymentSchedulePeriod
import energy.so.ac.junifer.fixtures.paymentMethodResponse
import energy.so.ac.junifer.fixtures.stopPaymentSchedulePeriod
import energy.so.ac.junifer.fixtures.stopPaymentSchedulePeriodRequest
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class HttpJuniferFinancesServiceTest : BehaviorSpec({

    val sut = HttpJuniferFinancesService(
        MockJuniferClient.juniferConfig,
        getMockHttpClient(),
    )


    given("GetActiveSeasonalDefinition") {
        `when`("call getActiveSeasonalDefinition") {
            val actualResponse = sut.getActiveSeasonalDefinition()
            then("should be able to active seasonal definition") {
                actualResponse shouldBe activeSeasonalDefinitionResponse
            }
        }
    }

    given("valid payment method id") {
        val juniferId = paymentMethodResponse.id.toString()

        `when`("get payment method") {
            val response = sut.getPaymentMethod(juniferId)

            then("response should contain data from junifer") {
                response shouldBe paymentMethodResponse
            }
        }
    }

    given("valid junifer id and stopPaymentSchedulePeriodRequest") {
        val juniferId = stopPaymentSchedulePeriodRequest.id.toString()

        `when`("stop payment schedule period method") {
            val response = sut.stopPaymentSchedulePeriod(juniferId, stopPaymentSchedulePeriod)
            then("response should be Unit") {
                response shouldBe Unit
            }
        }
    }

    given("CreatePaymentSchedulePeriodRequest") {

        `when`("create payment schedule period") {
            val response = sut.createPaymentSchedulePeriod(createPaymentSchedulePeriod)

            then("payment schedule created") {
                response.id shouldBe 12
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Get && request.url.fullPath.endsWith("paymentMethods/${paymentMethodResponse.id}")) {
            respond(
                "{\n" +
                        "  \"id\": ${paymentMethodResponse.id},\n" +
                        "  \"paymentMethodType\": \"${paymentMethodResponse.paymentMethodType}\",\n" +
                        "  \"status\": \"${paymentMethodResponse.status}\",\n" +
                        "  \"createdDttm\": \"${paymentMethodResponse.createdDttm}\",\n" +
                        "  \"fromDttm\": \"${paymentMethodResponse.fromDttm}\",\n" +
                        "  \"toDttm\": \"${paymentMethodResponse.toDttm}\",\n" +
                        "  \"defaultStatus\": \"${paymentMethodResponse.defaultStatus}\",\n" +
                        "  \"links\": {\n" +
                        "    \"self\": \"http://127.0.0.1:43002/rest/v1/paymentMethods/${paymentMethodResponse.id}\",\n" +
                        "    \"directDebit\": \"http://127.0.0.1:43002/rest/v1/directDebits/1\",\n" +
                        "    \"account\": \"http://127.0.0.1:43002/rest/v1/accounts/1\"\n" +
                        "  }\n" +
                        "}",
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("activeSeasonalDefinition")) {
            respond(
                ByteReadChannel(activeSeasonalDefinitionJsonResponse.toByteArray(Charsets.UTF_8)),
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Put && request.url.fullPath.endsWith("/paymentSchedulePeriods/${stopPaymentSchedulePeriodRequest.id}")) {
            respond(
                ByteReadChannel(
                    """
                    {
                    }
                """.trimIndent()
                ),
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )

        } else if (request.method == HttpMethod.Post && request.url.fullPath.contains("paymentSchedulePeriods")) {
            respond(
                "{\"id\": \"12\", \"amount\": \"100.00\", \"createdDttm\": \"2023-05-19T14:00:00\"," +
                        "\"frequency\": \"Monthly\", \"frequencyAlignmentDt\": \"2023-05-19\"," +
                        "\"frequencyMultiple\": \"1\",  \"nextPaymentDt\": \"2023-05-19\"," +
                        "\"fromDt\": \"2023-05-19\", \"seasonalPaymentFl\": false, \"links\": { \"self\": \"link\" }}",
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}