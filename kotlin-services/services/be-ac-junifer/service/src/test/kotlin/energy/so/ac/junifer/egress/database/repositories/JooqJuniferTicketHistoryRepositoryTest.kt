package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.juniferTicketHistory
import energy.so.commons.extension.save
import energy.so.commons.model.tables.references.JUNIFER__TICKETHISTORY
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JooqJuniferTicketHistoryRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val sut = JooqJuniferTicketHistoryRepository(db)

    given("TicketHistory exists in DB") {
        db.newRecord(JUNIFER__TICKETHISTORY, juniferTicketHistory).apply { save() }

        `when`("TicketHistory is queried") {
            val actualTickethistory = sut.findById(1L)
            then("TicketHistory is returned") {
                actualTickethistory shouldBe juniferTicketHistory
            }
        }
    }
    given("TicketHistory doesn't exist in DB") {
        `when`("TicketHistory is queried") {
            then("return null") {
                sut.findById(2L) shouldBe null
            }
        }
    }
})
