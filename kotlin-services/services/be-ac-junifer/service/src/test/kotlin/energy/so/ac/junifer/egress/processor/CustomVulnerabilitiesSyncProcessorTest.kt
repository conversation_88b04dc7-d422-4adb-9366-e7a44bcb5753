package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferCustomVulnerabilityRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.CUSTOM_VULN_ID
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.createCustomVulnerabilityEntityRequest
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.customVulnerabilitySyncEvent
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.deleteCustomVulnerabilityEntityRequest
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.juniferCustomVulnerability
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.patchCustomVulnerabilityEntityRequest
import energy.so.ac.junifer.fixtures.CustomVulnerabilityPrecannedData.syncResponse
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class CustomVulnerabilitiesSyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockCustomVulnerabilityRepo = mockk<JuniferCustomVulnerabilityRepository>()
    val mockCustomersClient = mockk<SyncClient>()

    val customVulnerabilityProcessor =
        CustomVulnerabilitiesSyncProcessor(mockMapper, mockCustomVulnerabilityRepo, mockCustomersClient)

    afterEach {
        confirmVerified(mockMapper, mockCustomVulnerabilityRepo, mockCustomersClient)
        clearMocks(mockMapper, mockCustomVulnerabilityRepo, mockCustomersClient)
    }

    given("another type of sync event than CUSTOM_VULNERABILITY") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    customVulnerabilityProcessor.process(CustomerAddressPrecannedData.customerAddressSyncEvent)
                }.let {
                    it.message shouldBe "Unsupported EventType ${CustomerAddressPrecannedData.customerAddressSyncEvent.eventType}"
                }
            }
        }
    }

    given("sync event with null reference") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    customVulnerabilityProcessor.process(
                        customVulnerabilitySyncEvent.copy(
                            reference = null
                        )
                    )
                }.let {
                    it.message shouldBe "Cannot process sync event without a reference"
                }
            }
        }
    }

    given("correct custom vulnerability sync event") {
        and("no custom vulnerability core mapping") {
            and("no custom vulnerability found with reference as id") {
                coEvery {
                    mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                } returns null
                coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } throws
                        EntityNotFoundException("Not Found")

                `when`("call process") {
                    customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)

                    then("nothing happens") {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                            mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                        }
                    }
                }
            }

            and("existing custom vulnerability found with reference as id") {
                and("customer mapping not found") {
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                    } returns null
                    coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } returns juniferCustomVulnerability
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                    } returns null

                    `when`("call process") {
                        then("throw exception, no sync")
                        assertThrows<SyncDelayedException> {
                            customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)
                        }
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                            mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                        }
                    }
                }
                and("property mapping not found") {
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                    } returns null
                    coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } returns juniferCustomVulnerability
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                    } returns ID_1.toString()
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                    } returns null

                    `when`("call process") {
                        then("throw exception, no sync") {
                            assertThrows<SyncDelayedException> {
                                customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)
                            }
                            coVerify {
                                mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                                mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                                mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                                mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                            }
                        }
                    }
                }
                and("customer and property mappings found") {
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                    } returns null
                    coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } returns juniferCustomVulnerability
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                    } returns ID_1.toString()
                    coEvery {
                        mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                    } returns ID_1.toString()
                    coEvery { mockCustomersClient.syncCustomVulnerabilityEntity(createCustomVulnerabilityEntityRequest) } returns syncResponse
                    coJustRun {
                        mockMapper.createCoreMapping(
                            EntityIdentifier.CUSTOM_VULNERABILITY,
                            CUSTOM_VULN_ID.toString(),
                            CUSTOM_VULN_ID.toString()
                        )
                    }

                    `when`("call process") {

                        customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)

                        then("sync to create") {
                            coVerify {
                                mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                                mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                                mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                                mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                                mockCustomersClient.syncCustomVulnerabilityEntity(createCustomVulnerabilityEntityRequest)
                                mockMapper.createCoreMapping(
                                    EntityIdentifier.CUSTOM_VULNERABILITY,
                                    CUSTOM_VULN_ID.toString(),
                                    CUSTOM_VULN_ID.toString()
                                )
                            }
                        }
                    }
                }
            }
        }
        and("existing custom vulnerability core mapping") {
            and("no custom vulnerability found with reference as id") {
                coEvery {
                    mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                } returns CUSTOM_VULN_ID.toString()
                coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } throws
                        EntityNotFoundException("Not Found")
                coEvery { mockCustomersClient.syncCustomVulnerabilityEntity(deleteCustomVulnerabilityEntityRequest) } returns syncResponse

                `when`("call process") {
                    customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)

                    then("sync to delete is called") {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                            mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                            mockCustomersClient.syncCustomVulnerabilityEntity(deleteCustomVulnerabilityEntityRequest)
                        }
                    }
                }
            }
            and("existing custom vulnerability found with reference as id") {
                coEvery {
                    mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                } returns CUSTOM_VULN_ID.toString()
                coEvery { mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID) } returns juniferCustomVulnerability
                coEvery {
                    mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                } returns ID_1.toString()
                coEvery {
                    mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                } returns ID_1.toString()
                coEvery { mockCustomersClient.syncCustomVulnerabilityEntity(patchCustomVulnerabilityEntityRequest) } returns syncResponse
                `when`("call process") {
                    customVulnerabilityProcessor.process(customVulnerabilitySyncEvent)
                    then("sync to patch is called, but no new mapping created") {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.CUSTOM_VULNERABILITY, CUSTOM_VULN_ID.toString())
                            mockCustomVulnerabilityRepo.findJuniferCustomVulnerability(CUSTOM_VULN_ID)
                            mockMapper.getCoreId(EntityIdentifier.CUSTOMER, ID_1.toString())
                            mockMapper.getCoreId(EntityIdentifier.PROPERTY, ID_1.toString())
                            mockCustomersClient.syncCustomVulnerabilityEntity(patchCustomVulnerabilityEntityRequest)
                        }
                    }
                }
            }
        }
    }
})
