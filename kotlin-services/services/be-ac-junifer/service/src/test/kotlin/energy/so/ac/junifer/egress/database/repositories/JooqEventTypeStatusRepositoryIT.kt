package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.EventTypeStatu
import energy.so.commons.model.tables.references.EVENT_TYPE_STATUS
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext


class JooqEventTypeStatusRepositoryIT : BehaviorSpec({

    val db = installDatabase(truncateOnRootOnly = true)
    val sut = JooqEventTypeStatusRepository(db)

    given("an existing event type status with enabled = true") {

        sut.save(EventTypeStatu(eventType = EventType.METER, enabled = true))

        `when`("the event type status is queried") {

            val eventTypeStatus = sut.getEventTypeStatus(EventType.METER)

            then("the event type status is returned") {
                eventTypeStatus shouldNotBe null
                eventTypeStatus!!.eventType shouldBe EventType.METER
                eventTypeStatus.enabled shouldBe true
            }
        }
    }

    given("an existing event type status with enabled = false") {

        sut.save(EventTypeStatu(eventType = EventType.METER, enabled = false))

        `when`("the event type status is set to enable = false and the event type status is queried") {

            val eventTypeStatus = getEventTypeStatus(db, EventType.METER)

            then("the event type status is returned") {
                eventTypeStatus shouldNotBe null
                eventTypeStatus!!.eventType shouldBe EventType.METER
                eventTypeStatus.enabled shouldBe false
            }
        }
    }
})

fun getEventTypeStatus(context: DSLContext, eventType: EventType): EventTypeStatu? =
    context.selectFrom(EVENT_TYPE_STATUS)
        .where(EVENT_TYPE_STATUS.EVENT_TYPE.eq(eventType))
        .fetchOneInto(EventTypeStatu::class.java)
