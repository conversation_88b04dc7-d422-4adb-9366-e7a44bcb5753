@file:Suppress("SameParameterValue")

package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.references.JUNIFER__ADDRESS
import energy.so.commons.model.tables.references.JUNIFER__ADDRESSTYPE
import energy.so.commons.model.tables.references.JUNIFER__COUNTRY
import energy.so.commons.model.tables.references.JUNIFER__PROPERTYTBL
import energy.so.commons.model.tables.references.JUNIFER__PROPERTYTYPE
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext
import org.junit.jupiter.api.assertThrows

class JooqJuniferAddressRepositoryIT : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferAddressRepository(db)

    given("Address type exists in DB") {
        insertAddressType(1, db, "type1")

        `when`("Address type is queried") {
            val type = repo.findAddressType(1)

            then("Address type is returned") {
                type shouldNotBe null
                type!!
                type.id shouldBe 1
                type.name shouldBe "type1"
            }
        }
    }

    given("Address type doesn't exist in DB") {

        `when`("Address type is queried") {
            val type = repo.findAddressType(1)

            then("Address type should not be returned") {
                type shouldBe null
            }
        }
    }

    given("Property type exists in DB") {
        insertPropertyType(1, db, "type1")

        `when`("Property type is queried") {
            val type = repo.findPropertyType(1)

            then("Property type is returned") {
                type shouldNotBe null
                type!!
                type.id shouldBe 1
                type.name shouldBe "type1"
            }
        }
    }

    given("Property type doesn't exist in DB") {

        `when`("Property type is queried") {
            val type = repo.findPropertyType(1)

            then("Property type should not be returned") {
                type shouldBe null
            }
        }
    }

    given("Country exists in DB") {
        insertCountry(1, db, "US")

        `when`("Country is queried") {
            val country = repo.findCountry(1)

            then("Country is returned") {
                country shouldNotBe null
                country!!
                country.id shouldBe 1
                country.isocode shouldBe "US"
            }
        }
    }

    given("Country doesn't exist in DB") {

        `when`("Country is queried") {
            val country = repo.findCountry(1)

            then("Country should not be returned") {
                country shouldBe null
            }
        }
    }

    given("Address exists in DB") {
        insertAddressType(1, db, "type")
        insertCountry(1, db, "US")
        insertAddress(1, db, 1, "line", 1)

        `when`("Address is queried") {
            val address = repo.findAddress(1)

            then("Address is returned") {
                address shouldNotBe null
                address!!
                address.id shouldBe 1
                address.addresstypefk shouldBe 1
                address.address3 shouldBe "line"
                address.countryfk shouldBe 1
            }
        }
    }

    given("Address doesn't exist in DB") {

        `when`("Address is queried") {
            val address = repo.findAddress(1)

            then("Address should not be returned") {
                address shouldBe null
            }
        }
    }

    given("Address exists in DB, get or throw exception") {
        insertAddressType(1, db, "type")
        insertCountry(1, db, "US")
        insertAddress(1, db, 1, "line", 1)

        `when`("Address is queried") {
            val address = repo.getAddress(1)

            then("Address is returned") {
                address shouldNotBe null
                address
                address.id shouldBe 1
                address.addresstypefk shouldBe 1
                address.address3 shouldBe "line"
                address.countryfk shouldBe 1
            }
        }
    }

    given("Address doesn't exist in DB, get or throw exception") {

        `when`("Address is queried") {

            then("Should throw EntityNotFoundException") {
                assertThrows<EntityNotFoundException> {
                    repo.getAddress(1)
                }
            }
        }
    }


    given("Property exists in DB") {
        insertAddressType(1, db, "type")
        insertCountry(1, db, "US")
        insertAddress(1, db, 1, "line", 1)
        insertPropertyType(1, db, "type")
        insertProperty(1, db, 1, 1)

        `when`("Property is queried") {
            val property = repo.findProperty(1)

            then("Property is returned") {
                property shouldNotBe null
                property!!
                property.id shouldBe 1
                property.addressfk shouldBe 1
                property.propertytypefk shouldBe 1
            }
        }
    }

    given("Property doesn't exist in DB") {

        `when`("Property is queried") {
            val property = repo.findProperty(1)

            then("Property should not be returned") {
                property shouldBe null
            }
        }
    }
})

private fun insertCountry(id: Long, db: DSLContext, code: String) {
    db.insertInto(JUNIFER__COUNTRY).columns(
        JUNIFER__COUNTRY.ID,
        JUNIFER__COUNTRY.INTERNALKEY,
        JUNIFER__COUNTRY.LANGUAGEKEY,
        JUNIFER__COUNTRY.ISOCODE,
        JUNIFER__COUNTRY.DIALCODE,
        JUNIFER__COUNTRY.REFERENCE,
        JUNIFER__COUNTRY.DELETEFL,
        JUNIFER__COUNTRY.VERSIONNO,
        JUNIFER__COUNTRY.PARTITIONID
    ).values(
        id, "", "", code, "", "", "N", 1, 1
    ).execute()
}

private fun insertAddressType(id: Long, db: DSLContext, name: String) {
    db.insertInto(JUNIFER__ADDRESSTYPE).columns(
        JUNIFER__ADDRESSTYPE.ID,
        JUNIFER__ADDRESSTYPE.NAME,
        JUNIFER__ADDRESSTYPE.ADDRESSFORMATCOMPONENTFK,
        JUNIFER__ADDRESSTYPE.POSTCODEAPIKEY,
        JUNIFER__ADDRESSTYPE.DELETEFL,
        JUNIFER__ADDRESSTYPE.VERSIONNO,
        JUNIFER__ADDRESSTYPE.PARTITIONID
    ).values(id, name, 2, "", "N", 1, 1).execute()
}

private fun insertPropertyType(id: Long, db: DSLContext, type: String) {
    db.insertInto(JUNIFER__PROPERTYTYPE).columns(
        JUNIFER__PROPERTYTYPE.ID, JUNIFER__PROPERTYTYPE.NAME
    ).values(id, type).execute()
}

private fun insertAddress(id: Long, db: DSLContext, typeFk: Long, line: String, countryFk: Long) {
    db.insertInto(JUNIFER__ADDRESS).columns(
        JUNIFER__ADDRESS.ID, JUNIFER__ADDRESS.ADDRESSTYPEFK, JUNIFER__ADDRESS.ADDRESS3, JUNIFER__ADDRESS.COUNTRYFK
    ).values(id, typeFk, line, countryFk).execute()
}

private fun insertProperty(id: Long, db: DSLContext, addressFk: Long, typeFk: Long) {
    db.insertInto(JUNIFER__PROPERTYTBL).columns(
        JUNIFER__PROPERTYTBL.ID, JUNIFER__PROPERTYTBL.ADDRESSFK, JUNIFER__PROPERTYTBL.PROPERTYTYPEFK
    ).values(id, addressFk, typeFk).execute()
}
