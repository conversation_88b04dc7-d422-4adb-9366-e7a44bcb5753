package energy.so.ac.junifer.ingress.services.accountFinder

import energy.so.ac.junifer.admin.database.repositories.AccountFinderRepository
import energy.so.ac.junifer.admin.database.repositories.AccountFinderV2Repository
import energy.so.ac.junifer.admin.database.repositories.dto.FoundAccountV2
import energy.so.ac.junifer.admin.database.repositories.dto.fromGrpcRequest
import energy.so.ac.junifer.admin.database.repositories.dto.toGrpcResponse
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderFoundAccountV2DtoList
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderRequestV2
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.accountFinderResponseV2
import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData.BobsData.requestAccount
import energy.so.ac.junifer.v1.accounts.AccountFinderRequestV2
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic

class AccountFinderServiceImplTest : BehaviorSpec({

    val accountFinderRepository = mockk<AccountFinderRepository>()
    val accountFinderV2Repository = mockk<AccountFinderV2Repository>()
    val sut = AccountFinderServiceImpl(accountFinderRepository, accountFinderV2Repository)

    given("a valid request") {
        `when`("::accountFinder") {
            every {
                accountFinderRepository.generateAndRunAccountFinderQuery(any())
            } returns BobsData.accountFinderFoundAccountDtoList

            then("return found accounts") {
                val result = sut.accountFinder(BobsData.validAccountFinderRequest)

                result shouldBe BobsData.validAccountFinderResponse
            }
        }
    }

    given("account finder request") {
        mockkStatic("energy.so.ac.junifer.admin.database.repositories.dto.RequestAccountKt")
        mockkStatic("energy.so.ac.junifer.admin.database.repositories.dto.FoundAccountV2Kt")
        every { any<AccountFinderRequestV2>().fromGrpcRequest() } returns requestAccount
        every { any<List<FoundAccountV2>>().toGrpcResponse() } returns accountFinderResponseV2
        every { accountFinderV2Repository.searchAccounts(any()) } returns accountFinderFoundAccountV2DtoList
        `when`("search accounts") {
            val actual = sut.accountFinderV2(accountFinderRequestV2)
            then("return expected response") {
                actual shouldBe accountFinderResponseV2
            }
        }
    }
})
