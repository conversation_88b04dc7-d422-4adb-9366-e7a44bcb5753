package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.JUNIFER_DD_INSTRUCTION_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.JUNIFER_DIRECT_DEBIT_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.PAYMENT_METHOD_ID
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.createDirectDebitSync
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.ddInstructionSyncEvent
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.deleteDirectDebitSync
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.directDebitSyncEvent
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.directDebitSyncResponse
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.markDirectDebitAsNotDeletedSync
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.testJuniferDirectDDInstruction
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.testJuniferDirectDDInstructionWithoutDDFk
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.testJuniferDirectDebit
import energy.so.ac.junifer.fixtures.DirectDebitPrecannedData.updateDirectDebitSync
import energy.so.ac.junifer.fixtures.PaymentMethodPrecannedData.JUNIFER_PAYMENT_METHOD_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.DIRECT_DEBIT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_METHOD
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.payments.api.v2.sync.copy
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class DirectDebitSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentRepository = mockk<JuniferPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val directDebitProcessor = DirectDebitSyncProcessor(
        mockMapper,
        mockPaymentRepository,
        mockSyncClient,
    )

    afterEach {
        confirmVerified(mockMapper, mockPaymentRepository, mockSyncClient)
        clearMocks(mockMapper, mockPaymentRepository, mockSyncClient)
    }

    given("no existing mapped directDebit and an existing junifer directDebit") {

        every { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) } returns null
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction

        `when`("associated payment method core id present in mapping") {
            every {
                mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
            } returns PAYMENT_METHOD_ID.toString()
            coEvery { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) } returns directDebitSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    DIRECT_DEBIT,
                    JUNIFER_DIRECT_DEBIT_ID.toString(),
                    PAYMENT_METHOD_ID.toString()
                )
            }

            directDebitProcessor.process(directDebitSyncEvent)

            then("a new directDebit should be created") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }

                verify {
                    mockMapper.createCoreMapping(
                        DIRECT_DEBIT,
                        JUNIFER_DIRECT_DEBIT_ID.toString(),
                        PAYMENT_METHOD_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) }
            }
        }
    }

    given("no existing mapped directDebit and an existing junifer directDebit with no payment method") {

        every { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) } returns null
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction

        `when`("associated payment method core id NOT present in mapping") {

            every { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) } returns null

            shouldThrow<SyncDelayedException> { directDebitProcessor.process(directDebitSyncEvent) }

            then("a SyncDelayedException should be thrown") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }
            }
        }
    }

    given("an existing mapped directDebit and an existing junifer directDebit") {
        every {
            mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every {
            mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction

        `when`("a directDebit event is generated") {
            coEvery { mockSyncClient.syncDirectDebitEntity(updateDirectDebitSync) } returns directDebitSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    DIRECT_DEBIT,
                    JUNIFER_DIRECT_DEBIT_ID.toString(),
                    PAYMENT_METHOD_ID.toString()
                )
            }

            directDebitProcessor.process(directDebitSyncEvent)

            then("directDebit should be patched") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }
                coVerify { mockSyncClient.syncDirectDebitEntity(updateDirectDebitSync) }
            }
        }
    }

    given("an existing mapped directDebit and existing junifer directDebit to be deleted") {

        every {
            mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        justRun { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }

        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } throws EntityNotFoundException(
            "direct debit not found"
        )

        `when`("a directDebit event is generated") {

            coEvery { mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync) } returns directDebitSyncResponse

            directDebitProcessor.process(directDebitSyncEvent)

            then("directDebit should be deleted") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                coVerify { mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync) }
            }
        }
    }

    given("goCardlessDDInstruction not found in db") {
        every { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) } throws EntityNotFoundException(
            "direct debit instruction not found"
        )

        `when`("a direct debit instruction event is generated") {

            shouldThrow<EntityNotFoundException> { directDebitProcessor.process(ddInstructionSyncEvent) }

            then("should throw EntityNotFoundException") {
                verify { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) }
            }
        }
    }

    given("goCardlessDDInstruction exists but no corresponding goCardlessDD") {
        every { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) } returns testJuniferDirectDDInstructionWithoutDDFk

        `when`("a direct debit instruction event is generated") {

            shouldThrow<SyncDelayedException> { directDebitProcessor.process(ddInstructionSyncEvent) }

            then("event should be delayed") {
                verify { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) }
            }
        }
    }

    given("goCardlessDDInstruction exists but goCardlessDD not in core") {
        every { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) } returns testJuniferDirectDDInstruction
        every {
            mockMapper.getCoreId(
                DIRECT_DEBIT,
                JUNIFER_DIRECT_DEBIT_ID.toString()
            )
        } returns null

        `when`("a direct debit instruction event is generated") {

            shouldThrow<SyncDelayedException> { directDebitProcessor.process(ddInstructionSyncEvent) }

            then("event should be delayed") {
                verify { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) }
                verify {
                    mockMapper.getCoreId(
                        DIRECT_DEBIT,
                        JUNIFER_DIRECT_DEBIT_ID.toString()
                    )
                }
            }
        }
    }

    given("goCardlessDDInstruction exists but goCardlessDD in core") {
        every { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) } returns testJuniferDirectDDInstruction
        every {
            mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every {
            mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction
        coEvery { mockSyncClient.syncDirectDebitEntity(updateDirectDebitSync) } returns directDebitSyncResponse

        `when`("a direct debit instruction event is generated") {
            directDebitProcessor.process(ddInstructionSyncEvent)

            then("corresponding direct debit should be updated") {
                verify { mockPaymentRepository.getGoCardlessDDInstruction(JUNIFER_DD_INSTRUCTION_ID) }
                verify {
                    mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
                }
                verify {
                    mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
                }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }
                coVerify { mockSyncClient.syncDirectDebitEntity(updateDirectDebitSync) }
            }
        }
    }

    given("an existing mapped direct debit, whose payment method has changed") {

        // This is the ID of the current direct debit. As the payment method has changed, the DD will be remapped.
        val oldPaymentMethodFK = 887766L

        every {
            mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
        } returns oldPaymentMethodFK.toString()
        every {
            mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction

        `when`("a direct debit event is generated") {

            // The old DD record gets unmapped, and its delete flag set
            coEvery {
                mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync.copy {
                    directDebitEntity = deleteDirectDebitSync.directDebitEntity.copy {
                        id = oldPaymentMethodFK.toNullableInt64()
                    }
                })
            } returns directDebitSyncResponse

            // The existing mapping removed
            justRun { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }

            // Now the new update / mapping are created
            coEvery { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) } returns directDebitSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    DIRECT_DEBIT,
                    JUNIFER_DIRECT_DEBIT_ID.toString(),
                    PAYMENT_METHOD_ID.toString()
                )
            }

            directDebitProcessor.process(directDebitSyncEvent)

            then("the old record should be deleted and the mapping removed") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        DIRECT_DEBIT,
                        JUNIFER_DIRECT_DEBIT_ID.toString(),
                        PAYMENT_METHOD_ID.toString()
                    )
                }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }
                coVerify { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) }
                coVerify {
                    mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync.copy {
                        directDebitEntity = deleteDirectDebitSync.directDebitEntity.copy {
                            id = oldPaymentMethodFK.toNullableInt64()
                        }
                    })
                }
            }
        }
    }

    given("no existing mapping for the junifer ID, but an existing mapping for the payment method") {

        val oldJuniferId = 554433L

        // No mapping for the current direct debit event
        every {
            mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString())
        } returns null

        // But there is a mapping already for the core ID
        every {
            mockMapper.getJuniferId(DIRECT_DEBIT, PAYMENT_METHOD_ID.toString())
        } returns oldJuniferId.toString()
        every {
            mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString())
        } returns PAYMENT_METHOD_ID.toString()
        every { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDebit
        every { mockPaymentRepository.getGoCardlessDirectDebit(oldJuniferId) } throws EntityNotFoundException("Not found")
        every { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) } returns testJuniferDirectDDInstruction

        `when`("a direct debit event is generated") {

            // The old DD record gets unmapped, and its delete flag set
            coEvery {
                mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync.copy {
                    directDebitEntity = deleteDirectDebitSync.directDebitEntity.copy {
                        id = PAYMENT_METHOD_ID.toNullableInt64()
                    }
                })
            } returns directDebitSyncResponse

            // The existing mapping removed
            justRun { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, oldJuniferId.toString()) }

            // Now the new update / mapping are created
            coEvery { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) } returns directDebitSyncResponse
            every {
                mockMapper.createCoreMapping(
                    DIRECT_DEBIT,
                    JUNIFER_DIRECT_DEBIT_ID.toString(),
                    PAYMENT_METHOD_ID.toString()
                )
            } throws IllegalStateException("Mapping exists") andThenAnswer { } // First mapping fails, second succeeds
            coEvery { mockSyncClient.syncDirectDebitEntity(markDirectDebitAsNotDeletedSync) } returns directDebitSyncResponse

            directDebitProcessor.process(directDebitSyncEvent)

            then("the old mapping should be deleted, and a new mapping created") {
                verify { mockMapper.getCoreId(DIRECT_DEBIT, JUNIFER_DIRECT_DEBIT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT_METHOD, JUNIFER_PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.getJuniferId(DIRECT_DEBIT, PAYMENT_METHOD_ID.toString()) }
                verify { mockMapper.deleteMappingByJuniferId(DIRECT_DEBIT, oldJuniferId.toString()) }
                verify(exactly = 2) {
                    mockMapper.createCoreMapping(
                        DIRECT_DEBIT,
                        JUNIFER_DIRECT_DEBIT_ID.toString(),
                        PAYMENT_METHOD_ID.toString()
                    )
                }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(JUNIFER_DIRECT_DEBIT_ID) }
                verify { mockPaymentRepository.getGoCardlessDirectDebit(oldJuniferId) }
                verify { mockPaymentRepository.getGoCardlessDDInstructionByGoCardlessDDId(JUNIFER_DIRECT_DEBIT_ID) }
                coVerify { mockSyncClient.syncDirectDebitEntity(createDirectDebitSync) }
                coVerify {
                    mockSyncClient.syncDirectDebitEntity(deleteDirectDebitSync.copy {
                        directDebitEntity = deleteDirectDebitSync.directDebitEntity.copy {
                            id = PAYMENT_METHOD_ID.toNullableInt64()
                        }
                    })
                }
                coVerify { mockSyncClient.syncDirectDebitEntity(markDirectDebitAsNotDeletedSync) }
            }
        }
    }
})
