package energy.so.ac.junifer.mapping

import energy.so.commons.redis.JedisRedisClient
import energy.so.commons.redis.RedisConnectionConfig
import energy.so.database.test.installRedis
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class RedisEntityMapperTest : BehaviorSpec({

    val redis = installRedis()
    val redisClient = JedisRedisClient(
        RedisConnectionConfig(
            host = redis.host,
            port = redis.firstMappedPort
        )
    )
    val entityMapper = RedisEntityMapper(redisClient)

    val coreId = "666"
    val juniferId = "6969"
    val entityIdentifier = EntityIdentifier.CUSTOMER

    given("core ID and junifer ID") {

        `when`("create mapping") {
            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

            then("mappings created") {
                entityMapper.getCoreId(entityIdentifier, juniferId) shouldBe coreId
                entityMapper.getJuniferId(entityIdentifier, coreId) shouldBe juniferId
            }
        }
    }

    given("existing mapping") {
        entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)

        `when`("get core id for matching junifer id") {
            val returnedCoreId = entityMapper.getCoreId(entityIdentifier, juniferId)

            then("core id returned") {
                returnedCoreId shouldBe coreId
            }
        }

        `when`("get core id for non matching junifer id") {
            val returnedCoreId = entityMapper.getCoreId(entityIdentifier, "")

            then("null returned") {
                returnedCoreId shouldBe null
            }
        }

        `when`("get junifer id for matching core id") {
            val returnedJuniferId = entityMapper.getJuniferId(entityIdentifier, coreId)

            then("junifer id returned") {
                returnedJuniferId shouldBe juniferId
            }
        }

        `when`("get core id for non matching core id") {
            val returnedJuniferId = entityMapper.getJuniferId(entityIdentifier, "")

            then("null returned") {
                returnedJuniferId shouldBe null
            }
        }
    }

    given("a mapping to delete") {
        `when`("an existing mapping is deleted by its coreId") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            entityMapper.deleteMappingByCoreId(entityIdentifier, coreId)

            val returnedJuniferId = entityMapper.getJuniferId(entityIdentifier, coreId)
            val returnedCoreId = entityMapper.getCoreId(entityIdentifier, juniferId)

            then("the mapping should be deleted") {
                returnedJuniferId shouldBe null
                returnedCoreId shouldBe null
            }
        }

        `when`("an existing mapping is deleted by its juniferId") {

            entityMapper.createCoreMapping(entityIdentifier, juniferId, coreId)
            entityMapper.deleteMappingByJuniferId(entityIdentifier, juniferId)

            then("the mapping should be deleted") {
                entityMapper.getJuniferId(entityIdentifier, coreId) shouldBe null
                entityMapper.getCoreId(entityIdentifier, juniferId) shouldBe null
            }
        }
    }
})
