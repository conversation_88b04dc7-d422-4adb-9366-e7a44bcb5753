package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JooqJuniferTicketRepository
import energy.so.ac.junifer.fixtures.JUNIFER_TICKET_ID
import energy.so.ac.junifer.fixtures.TICKET_ENTITY_ID
import energy.so.ac.junifer.fixtures.createTicketEntityRequest
import energy.so.ac.junifer.fixtures.deleteTicketEntityRequest
import energy.so.ac.junifer.fixtures.juniferTicket
import energy.so.ac.junifer.fixtures.ticketSyncResponse
import energy.so.ac.junifer.fixtures.updateTicketEntityRequest
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.SyncEvent
import energy.so.tickets.client.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify

class TicketSyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockJooqJuniferTicketRepository = mockk<JooqJuniferTicketRepository>()
    val mockTicketSyncClient = mockk<SyncClient>()

    val sut = TicketSyncProcessor(
        mockMapper,
        mockTicketSyncClient,
        mockJooqJuniferTicketRepository
    )

    given("Invalid Ticket Sync Event") {
        `when`("reference is null") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent())
                }.message shouldBe "Cannot process sync event without a reference"
            }
        }
        `when`("event is unsupported") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent(reference = "123", eventType = EventType.ACCOUNT))
                }.message shouldBe "Unsupported EventType ${EventType.ACCOUNT}"
            }
        }
    }
    given("a valid Ticket Sync Event") {
        `when`("junifer ticket is created") {
            every { mockMapper.getCoreId(EntityIdentifier.TICKET, JUNIFER_TICKET_ID.toString()) } returns null
            every { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) } returns juniferTicket
            coEvery {
                mockTicketSyncClient.syncTicketEntity(
                    createTicketEntityRequest
                )
            } returns ticketSyncResponse
            every {
                mockMapper.createCoreMapping(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString(),
                    TICKET_ENTITY_ID.toString()
                )
            } just Runs

            then("sync ticket entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_TICKET_ID.toString(), eventType = EventType.TICKET))

                verify { mockMapper.getCoreId(EntityIdentifier.TICKET, JUNIFER_TICKET_ID.toString()) }
                verify { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketEntity(
                        createTicketEntityRequest
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString(),
                        TICKET_ENTITY_ID.toString()
                    )
                }
            }
        }
        `when`("junifer ticket is updated") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) } returns juniferTicket
            coEvery {
                mockTicketSyncClient.syncTicketEntity(
                    updateTicketEntityRequest
                )
            } returns ticketSyncResponse

            then("sync ticket entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_TICKET_ID.toString(), eventType = EventType.TICKET))

                verify { mockMapper.getCoreId(EntityIdentifier.TICKET, JUNIFER_TICKET_ID.toString()) }
                verify { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketEntity(
                        updateTicketEntityRequest
                    )
                }
            }
        }
        `when`("junifer ticket is deleted") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) } returns null
            coEvery {
                mockTicketSyncClient.syncTicketEntity(
                    deleteTicketEntityRequest
                )
            } returns ticketSyncResponse
            every { mockMapper.deleteMappingByCoreId(EntityIdentifier.TICKET, TICKET_ENTITY_ID.toString()) } just Runs

            then("sync ticket entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_TICKET_ID.toString(), eventType = EventType.TICKET))

                verify { mockMapper.getCoreId(EntityIdentifier.TICKET, JUNIFER_TICKET_ID.toString()) }
                verify { mockJooqJuniferTicketRepository.findById(JUNIFER_TICKET_ID) }
                coVerify {
                    mockTicketSyncClient.syncTicketEntity(
                        deleteTicketEntityRequest
                    )
                }
                every { mockMapper.deleteMappingByCoreId(EntityIdentifier.TICKET, TICKET_ENTITY_ID.toString()) }
            }
        }
    }

})
