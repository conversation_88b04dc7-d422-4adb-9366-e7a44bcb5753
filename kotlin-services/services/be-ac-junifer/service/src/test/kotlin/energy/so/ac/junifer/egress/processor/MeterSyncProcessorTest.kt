package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.AssetPrecannedData.assetSyncResponse
import energy.so.ac.junifer.fixtures.AssetPrecannedData.createAssetSync
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_TYPE_DESCRIPTION
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_TYPE_CODE
import energy.so.ac.junifer.fixtures.MeterPrecannedData.JUNIFER_METER_TYPE_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData.METER_ID
import energy.so.ac.junifer.fixtures.MeterPrecannedData.createMeterSync
import energy.so.ac.junifer.fixtures.MeterPrecannedData.deleteMeterSync
import energy.so.ac.junifer.fixtures.MeterPrecannedData.meterSyncEvent
import energy.so.ac.junifer.fixtures.MeterPrecannedData.meterSyncResponse
import energy.so.ac.junifer.fixtures.MeterPrecannedData.patchMeterSync
import energy.so.ac.junifer.fixtures.MeterPrecannedData.patchMeterSyncWithUnknownMeterType
import energy.so.ac.junifer.fixtures.MeterPrecannedData.testJuniferMeter
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDateTime
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class MeterSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockMeterRepo = mockk<JuniferMeterRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val meterProcessor = MeterSyncProcessor(mockMapper, mockMeterRepo, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockMeterRepo, mockSyncClient)
    }

    given("Missing reference") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    meterProcessor.process(meterSyncEvent.copy(reference = null))
                }
            }
        }
    }

    given("null meter type") {
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()

        `when`("a meter event is generated") {
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter.copy(metertypefk = null)
            coEvery { mockSyncClient.syncMeterEntity(patchMeterSyncWithUnknownMeterType) } returns meterSyncResponse

            meterProcessor.process(meterSyncEvent)

            then("the meter should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify(exactly = 0) { mockMeterRepo.getMeterTypeCode(any()) }
                verify(exactly = 0) { mockMeterRepo.getMeterTypeDescription(any()) }
                coVerify { mockSyncClient.syncMeterEntity(patchMeterSyncWithUnknownMeterType) }
            }
        }
    }

    given("no meter type found") {
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()

        `when`("a meter event is generated") {
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter
            every { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) } throws EntityNotFoundException("Meter type not found")

            then("throw SyncDelayedException") {
                shouldThrow<SyncDelayedException> {
                    meterProcessor.process(meterSyncEvent)
                }

                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify { mockMeterRepo.getMeterTypeCode(any()) }
            }
        }
    }

    given("No existing mapped meter and an existing junifer meter") {

        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns null
        every { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_CODE
        every { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_DESCRIPTION

        `when`("a meter event is generated") {
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter
            coEvery { mockSyncClient.syncAssetEntity(createAssetSync) } returns assetSyncResponse
            coEvery { mockSyncClient.syncMeterEntity(createMeterSync) } returns meterSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.METER,
                    JUNIFER_METER_ID.toString(),
                    METER_ID.toString()
                )
            }

            meterProcessor.process(meterSyncEvent)

            then("a new asset and a new meter should be created") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) }
                verify { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.METER,
                        JUNIFER_METER_ID.toString(),
                        METER_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncAssetEntity(createAssetSync) }
                coVerify { mockSyncClient.syncMeterEntity(createMeterSync) }
            }
        }

        `when`("a deleted meter event is generated") {

            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_DESCRIPTION
            coEvery { mockSyncClient.syncAssetEntity(createAssetSync) } returns assetSyncResponse
            coEvery { mockSyncClient.syncMeterEntity(any()) } returns meterSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.METER,
                    JUNIFER_METER_ID.toString(),
                    METER_ID.toString()
                )
            }

            meterProcessor.process(meterSyncEvent)

            then("a new asset and a new meter should be created") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) }
                verify { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.METER,
                        JUNIFER_METER_ID.toString(),
                        METER_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncAssetEntity(createAssetSync) }
                coVerify {
                    mockSyncClient.syncMeterEntity(withArg {
                        it.meterEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
            }
        }
    }

    given("an existing mapped meter and an existing junifer meter") {

        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()
        every { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_CODE
        every { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_DESCRIPTION

        `when`("a meter event is generated") {
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter
            coEvery { mockSyncClient.syncMeterEntity(patchMeterSync) } returns meterSyncResponse

            meterProcessor.process(meterSyncEvent)

            then("the meter should be patched") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) }
                verify { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) }
                coVerify { mockSyncClient.syncMeterEntity(patchMeterSync) }
            }
        }

        `when`("a meter event is generated") {
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter.copy(deletefl = BOOLEAN_TRUE)
            every { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) } returns JUNIFER_METER_TYPE_DESCRIPTION
            coEvery { mockSyncClient.syncMeterEntity(any()) } returns meterSyncResponse

            meterProcessor.process(meterSyncEvent)

            then("a deleted meter event is generated") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify { mockMeterRepo.getMeterTypeCode(JUNIFER_METER_TYPE_ID) }
                verify { mockMeterRepo.getMeterTypeDescription(JUNIFER_METER_TYPE_ID) }
                coVerify {
                    mockSyncClient.syncMeterEntity(withArg {
                        it.meterEntity.deleted.getValueOrNull()?.toLocalDateTime()
                            ?.truncatedTo(ChronoUnit.MINUTES) shouldBe LocalDateTime.now()
                            .truncatedTo(ChronoUnit.MINUTES)
                    })
                }
            }
        }
    }

    given("an existing mapped meter and no existing junifer meter") {

        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER,
                JUNIFER_METER_ID.toString()
            )
        } returns METER_ID.toString()
        every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } throws EntityNotFoundException("Meter not found")

        `when`("a meter event is generated and the meter no longer exists") {

            coEvery { mockSyncClient.syncMeterEntity(deleteMeterSync) } returns meterSyncResponse

            meterProcessor.process(meterSyncEvent)

            then("the meter should be deleted") {
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                coVerify { mockSyncClient.syncMeterEntity(deleteMeterSync) }
            }
        }
    }

    given("a meter") {
        every { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) } returns METER_ID.toString()

        `when`("a meter event is generated") {
            clearMocks(mockMeterRepo)
            every { mockMeterRepo.getMeter(JUNIFER_METER_ID) } returns testJuniferMeter.copy(metertypefk = null)
            coEvery { mockSyncClient.syncMeterEntity(patchMeterSyncWithUnknownMeterType) } throws Exception("Exception")

            then("throws Exception") {
                shouldThrow<Exception> {
                    meterProcessor.process(meterSyncEvent)
                }
                verify { mockMapper.getCoreId(EntityIdentifier.METER, JUNIFER_METER_ID.toString()) }
                verify { mockMeterRepo.getMeter(JUNIFER_METER_ID) }
                verify(exactly = 0) { mockMeterRepo.getMeterTypeCode(any()) }
                coVerify { mockSyncClient.syncMeterEntity(patchMeterSyncWithUnknownMeterType) }
            }
        }
    }
})
