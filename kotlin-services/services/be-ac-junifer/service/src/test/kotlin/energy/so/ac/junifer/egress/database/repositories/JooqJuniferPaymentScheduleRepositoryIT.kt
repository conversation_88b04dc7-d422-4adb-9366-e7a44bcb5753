package energy.so.ac.junifer.egress.database.repositories

import energy.so.commons.database.SqlLoader
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.LocalDate

const val CREATE_PAYMENT_SCHEDULE_SQL = "/sql/payments/create_payment_schedule.sql"

class JooqJuniferPaymentScheduleRepositoryTest : BehaviorSpec({

    val db = installDatabase()
    val repo = JooqJuniferPaymentScheduleRepository(db)

    given("an existing payment schedule") {

        val payScheduleSql = SqlLoader.loadFromStream(getSql(CREATE_PAYMENT_SCHEDULE_SQL))
        val existingFrequencyInstId = 568787L
        val existingScheduleId = 3182454L
        val existingScheduledPaymentId = 11697325L
        val existingSeasonalDfnId = 3L

        `when`("retrieving the payment schedule frequency") {

            db.execute(payScheduleSql)
            val frequency = repo.getPaymentScheduleFrequency(existingFrequencyInstId)

            then("the payment schedule frequency should be returned") {
                frequency.frequency shouldBe "Monthly"
                frequency.frequencyInst.id shouldBe existingFrequencyInstId
                frequency.frequencyInst.frequencymultiple shouldBe 1
            }
        }

        `when`("retrieving a scheduled payment") {
            db.execute(payScheduleSql)
            val scheduledPayment = repo.getPaymentScheduleItemById(existingScheduledPaymentId)

            then("the scheduled payment should be returned") {
                scheduledPayment.id shouldBe existingScheduledPaymentId
                scheduledPayment.amount shouldBe BigDecimal("219.00000000")
                scheduledPayment.duedt shouldBe LocalDate.of(2023, 2, 8)
            }
        }

        `when`("retrieving the seasonal definition") {
            db.execute(payScheduleSql)
            val seasonalDfn = repo.getPaymentScheduleSeasonalDfnById(existingSeasonalDfnId)

            then("the seasonal dfn should be returned") {
                seasonalDfn.id shouldBe existingSeasonalDfnId
                seasonalDfn.highpercentage shouldBe BigDecimal("25.00000000")
                seasonalDfn.lowpercentage shouldBe BigDecimal("25.00000000")
            }
        }

        `when`("retrieving the payment schedule") {
            db.execute(payScheduleSql)
            val paymentSchedule = repo.getPaymentSchedulePeriodById(existingScheduleId)

            then("the payment schedule should be returned") {
                paymentSchedule.id shouldBe existingScheduleId
            }
        }
    }
})
