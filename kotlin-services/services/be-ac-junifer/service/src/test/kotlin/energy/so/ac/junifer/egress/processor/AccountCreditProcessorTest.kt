package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferAccountCreditRepository
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.GENERIC_STRING
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCredit
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditCreateSyncRequest
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditDeleteSyncRequest
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditPatchSyncRequest
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditReason
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditSyncEvent
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditSyncResponse
import energy.so.ac.junifer.fixtures.CustomerAddressPrecannedData.customerAddressSyncEvent
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class AccountCreditProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockAccountCreditRepository = mockk<JuniferAccountCreditRepository>()
    val mockPaymentsSyncClient = mockk<SyncClient>()

    val accountCreditProcessor =
        AccountCreditSyncProcessor(mockMapper, mockAccountCreditRepository, mockPaymentsSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockAccountCreditRepository, mockPaymentsSyncClient)
        clearMocks(mockMapper, mockAccountCreditRepository, mockPaymentsSyncClient)
    }

    given("sync event of other type than ACCOUNT_CREDIT") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    accountCreditProcessor.process(customerAddressSyncEvent)
                }.let {
                    it.message shouldBe "Unsupported EventType ${customerAddressSyncEvent.eventType}"
                }
            }
        }
    }

    given("sync event with null reference") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    accountCreditProcessor.process(accountCreditSyncEvent.copy(reference = null))
                }.let {
                    it.message shouldBe "Cannot process sync event without a reference"
                }
            }
        }
    }

    given("correct account credit sync event") {
        and("no account credit core mapping") {
            and("account credit corresponding to reference not found in db") {
                coEvery { mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString()) } returns null
                coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } throws EntityNotFoundException("Not Found")

                `when`("call process") {
                    accountCreditProcessor.process(accountCreditSyncEvent)

                    then("nothing happens") {
                        coVerify { mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString()) }
                        coVerify { mockAccountCreditRepository.getAccountCredit(ID_1) }
                    }
                }
            }
            and("account credit corresponding to reference found in db") {
                and("account credit reason not found in db") {
                    coEvery { mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString()) } returns null
                    coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } returns accountCredit
                    coEvery { mockAccountCreditRepository.getAccountCreditReason(ID_1) } throws EntityNotFoundException(
                        "Not Found"
                    )

                    `when`("call process") {
                        then("throw exception, no sync") {
                            assertThrows<EntityNotFoundException> {
                                accountCreditProcessor.process(accountCreditSyncEvent)
                            }
                            coVerify {
                                mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString())
                                mockAccountCreditRepository.getAccountCredit(ID_1)
                                mockAccountCreditRepository.getAccountCreditReason(ID_1)
                            }

                        }
                    }
                }
                and("account credit reason found in db") {
                    coEvery { mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString()) } returns null
                    coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } returns accountCredit
                    coEvery { mockAccountCreditRepository.getAccountCreditReason(ID_1) } returns accountCreditReason
                    coEvery { mockAccountCreditRepository.getSalesTaxName(ID_1) } returns GENERIC_STRING
                    coEvery { mockPaymentsSyncClient.syncAccountCredit(accountCreditCreateSyncRequest) } returns accountCreditSyncResponse
                    coEvery {
                        mockMapper.getCoreId(
                            EntityIdentifier.BILLING_ACCOUNT_TRANSACTION,
                            ID_1.toString()
                        )
                    } returns ID_1.toString()
                    coJustRun {
                        mockMapper.createCoreMapping(
                            EntityIdentifier.ACCOUNT_CREDIT,
                            ID_1.toString(),
                            ID_1.toString()
                        )
                    }
                    `when`("call process") {
                        accountCreditProcessor.process(accountCreditSyncEvent)

                        then("sync new account credit entity") {
                            coVerify {
                                mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString())
                                mockAccountCreditRepository.getAccountCredit(ID_1)
                                mockAccountCreditRepository.getAccountCreditReason(ID_1)
                                mockAccountCreditRepository.getSalesTaxName(ID_1)
                                mockPaymentsSyncClient.syncAccountCredit(accountCreditCreateSyncRequest)
                                mockMapper.createCoreMapping(
                                    EntityIdentifier.ACCOUNT_CREDIT,
                                    ID_1.toString(),
                                    ID_1.toString()
                                )
                                mockMapper.getCoreId(EntityIdentifier.BILLING_ACCOUNT_TRANSACTION, ID_1.toString())
                            }
                        }
                    }
                }
            }
        }
        and("existing account credit core mapping") {
            and("account credit corresponding to reference not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.ACCOUNT_CREDIT,
                        ID_1.toString()
                    )
                } returns ID_1.toString()
                coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } throws EntityNotFoundException("Not Found")
                coEvery { mockPaymentsSyncClient.syncAccountCredit(accountCreditDeleteSyncRequest) } returns accountCreditSyncResponse

                `when`("call process") {
                    accountCreditProcessor.process(accountCreditSyncEvent)
                    then("delete entity") {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString())
                            mockAccountCreditRepository.getAccountCredit(ID_1)
                            mockPaymentsSyncClient.syncAccountCredit(accountCreditDeleteSyncRequest)
                        }
                    }
                }
            }
        }
        and("account credit corresponding to reference found in db") {
            and("account credit reason not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.ACCOUNT_CREDIT,
                        ID_1.toString()
                    )
                } returns ID_1.toString()
                coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } returns accountCredit
                coEvery { mockAccountCreditRepository.getAccountCreditReason(ID_1) } throws EntityNotFoundException(
                    "Not Found"
                )

                `when`("call process") {
                    then("throw exception, no sync") {
                        assertThrows<EntityNotFoundException> {
                            accountCreditProcessor.process(accountCreditSyncEvent)
                        }
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString())
                            mockAccountCreditRepository.getAccountCredit(ID_1)
                            mockAccountCreditRepository.getAccountCreditReason(ID_1)
                        }

                    }
                }
            }
            and("account credit reason found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.ACCOUNT_CREDIT,
                        ID_1.toString()
                    )
                } returns ID_1.toString()
                coEvery { mockAccountCreditRepository.getAccountCredit(ID_1) } returns accountCredit
                coEvery { mockAccountCreditRepository.getAccountCreditReason(ID_1) } returns accountCreditReason
                coEvery { mockAccountCreditRepository.getSalesTaxName(ID_1) } returns null
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.BILLING_ACCOUNT_TRANSACTION,
                        ID_1.toString()
                    )
                } returns ID_1.toString()
                coEvery { mockPaymentsSyncClient.syncAccountCredit(accountCreditPatchSyncRequest) } returns accountCreditSyncResponse
                coJustRun {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.ACCOUNT_CREDIT,
                        ID_1.toString(),
                        ID_1.toString()
                    )
                }
                `when`("call process") {
                    accountCreditProcessor.process(accountCreditSyncEvent)

                    then("sync to patch account credit entity, no new mapping created") {
                        coVerify {
                            mockMapper.getCoreId(EntityIdentifier.ACCOUNT_CREDIT, ID_1.toString())
                            mockAccountCreditRepository.getAccountCredit(ID_1)
                            mockAccountCreditRepository.getAccountCreditReason(ID_1)
                            mockAccountCreditRepository.getSalesTaxName(ID_1)
                            mockPaymentsSyncClient.syncAccountCredit(accountCreditPatchSyncRequest)
                            mockMapper.getCoreId(
                                EntityIdentifier.BILLING_ACCOUNT_TRANSACTION,
                                ID_1.toString()
                            )
                        }
                    }
                }
            }
        }
    }
})
