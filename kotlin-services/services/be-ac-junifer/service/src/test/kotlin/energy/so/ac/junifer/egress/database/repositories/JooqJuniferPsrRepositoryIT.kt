package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.config.BOOLEAN_FALSE
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.FROM_DATE
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.TO_DATE
import energy.so.ac.junifer.fixtures.PsrPrecannedData.UK_VULN_CUSTTOMER_FK
import energy.so.ac.junifer.fixtures.PsrPrecannedData.UK_VULN_CUST_PSR_DFN_ID
import energy.so.ac.junifer.fixtures.PsrPrecannedData.UK_VULN_CUST_PSR_ID
import energy.so.commons.model.tables.references.JUNIFER__CUSTOMER
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTOMER
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTPSR
import energy.so.commons.model.tables.references.JUNIFER__UKVULNCUSTPSRDFN
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferPsrRepositoryTest : BehaviorSpec({


    val db = installDatabase()
    val repo = JooqJuniferPsrRepository(db)

    given("UkVulnCustPsrDfn and UkVulnCustPsr records are inserted") {

        insertUkVulnCustPsrDfn(db)

        `when`("the UkVulnCustPsrDfn is queried") {

            val returnedPsrDfn = repo.getUkVulnCustPsrDfn(UK_VULN_CUST_PSR_DFN_ID)

            then("the UkVulnCustPsrDfn should be returned") {
                returnedPsrDfn shouldNotBe null
            }
        }
    }

    given("UkVulnCustPsr record is inserted") {

        insertUkVulnCustPsr(db)

        `when`("the UkVulnCustPsr is queried") {

            val returnedPsr = repo.getUkVulnCustPsr(UK_VULN_CUST_PSR_ID)

            then("the UkVulnCustPsr should be returned") {
                returnedPsr shouldNotBe null
            }
        }
    }

    given("UkVulnCustPsr and UkVulnCustomer and Customer records are inserted") {

        inserCustomer(db)
        insertUkVulnCustomer(db)
        insertUkVulnCustPsr(db)

        `when`("the Customer is queried") {

            val returnedCustomer = repo.getCustomerByUkVulnCustPsrId(UK_VULN_CUST_PSR_ID)

            then("the Customer should be returned") {
                returnedCustomer shouldNotBe null
            }
        }
    }

})

fun insertUkVulnCustPsr(db: DSLContext) {
    db.insertInto(JUNIFER__UKVULNCUSTPSR).columns(
        JUNIFER__UKVULNCUSTPSR.ID,
        JUNIFER__UKVULNCUSTPSR.UKVULNCUSTOMERFK,
        JUNIFER__UKVULNCUSTPSR.UKVULNCUSTPSRDFNFK,
        JUNIFER__UKVULNCUSTPSR.FROMDT,
        JUNIFER__UKVULNCUSTPSR.TODT,
        JUNIFER__UKVULNCUSTPSR.DELETEFL,
        JUNIFER__UKVULNCUSTPSR.VERSIONNO,
    ).values(
        UK_VULN_CUST_PSR_ID,
        UK_VULN_CUSTTOMER_FK,
        UK_VULN_CUST_PSR_DFN_ID,
        FROM_DATE,
        TO_DATE,
        BOOLEAN_FALSE,
        1
    ).execute()
}

fun insertUkVulnCustPsrDfn(db: DSLContext) {
    db.insertInto(JUNIFER__UKVULNCUSTPSRDFN).columns(
        JUNIFER__UKVULNCUSTPSRDFN.ID,
        JUNIFER__UKVULNCUSTPSRDFN.DESCRIPTION
    ).values(
        UK_VULN_CUST_PSR_DFN_ID,
        "test"
    ).execute()
}

fun insertUkVulnCustomer(db: DSLContext) {
    db.insertInto(JUNIFER__UKVULNCUSTOMER).columns(
        JUNIFER__UKVULNCUSTOMER.ID,
        JUNIFER__UKVULNCUSTOMER.CUSTOMERFK
    ).values(
        UK_VULN_CUSTTOMER_FK,
        JUNIFER_CUSTOMER_ID
    ).execute()
}

fun inserCustomer(db: DSLContext) {
    db.insertInto(JUNIFER__CUSTOMER).columns(
        JUNIFER__CUSTOMER.ID,
        JUNIFER__CUSTOMER.NAME
    ).values(
        JUNIFER_CUSTOMER_ID,
        "test"
    ).execute()
}

