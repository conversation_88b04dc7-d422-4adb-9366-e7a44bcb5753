package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.ProductPrecannedData.testRateNamePeak
import energy.so.ac.junifer.fixtures.JUNIFER_RATE_NAME_ID
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.references.JUNIFER__RATENAME
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.assertThrows

class JooqJuniferRateNameRepositoryTest  : BehaviorSpec({
    val db = installDatabase()
    val repository = JooqJuniferRateNameRepository(db)
    given("a valid rate name id") {
        db.executeInsert(
            db.newRecord(
                JUNIFER__RATENAME,
                testRateNamePeak
            )
        )
        `when`("queried") {
            val rateName = repository.getRateNameById(JUNIFER_RATE_NAME_ID)
            then("rate name is returned") {
                rateName shouldBe testRateNamePeak
            }
        }
    }
    given("a invalid rate name id") {
        `when`("queried") {
            then("throw exception") {
                assertThrows<EntityNotFoundException> { repository.getRateNameById(1L) }
            }
        }
    }
})