package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferEstimatedUsageRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.ESTIMATED_USAGE_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_MPAN_EAC_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_MPAN_EAC_SET_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_MPRN_ANNUAL_QUANTITY_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.estimatedUsageSyncResponse
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.mpanEacEvent
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.mpanEacSetEvent
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.mprnAnnualQuantityEvent
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferAqUsageCreateRequest
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferAqUsageUpdateRequest
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferEacUsageCreateRequest
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferEacUsageUpdateRequest
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferEstimatedUsageForElec
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.testJuniferEstimatedUsageForGas
import energy.so.ac.junifer.fixtures.METER_POINT_CORE_JUNIFER_ID
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityIdentifier.ESTIMATED_USAGE_ELECTRICITY
import energy.so.ac.junifer.mapping.EntityIdentifier.ESTIMATED_USAGE_GAS
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.assets.api.v2.SyncClient
import energy.so.assets.sync.v2.copy
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.grpc.extensions.toNullableLongString
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.enums.OperationType
import energy.so.commons.model.tables.pojos.Junifer_Mpaneac
import energy.so.commons.model.tables.pojos.Junifer_Mpaneacset
import energy.so.commons.model.tables.pojos.Junifer_Mprnannualquantity
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class EstimatedUsageSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockUsageRepo = mockk<JuniferEstimatedUsageRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val estimatedUsageSyncProcessor = EstimatedUsageSyncProcessor(
        mockMapper,
        mockUsageRepo,
        mockSyncClient,
        10,
        10,
    )

    afterEach {
        confirmVerified(mockMapper, mockUsageRepo, mockSyncClient)
    }

    given("Invalid sync event type") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    estimatedUsageSyncProcessor.process(
                        mpanEacEvent.copy(eventType = EventType.SERVICE_RATING_RATE)
                    )
                }
            }
        }
    }

    given("Missing reference") {
        `when`("process event") {
            then("throw IllegalStateException exception") {
                shouldThrow<IllegalStateException> {
                    estimatedUsageSyncProcessor.process(
                        mpanEacEvent.copy(reference = null)
                    )
                }
            }
        }
    }

    given("no existing mapped estimated usage and an existing junifer Mpan Eac") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every {
            mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns METER_POINT_CORE_JUNIFER_ID
        every { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) } returns Junifer_Mpaneac(id = JUNIFER_MPAN_EAC_ID)

        `when`("a mpan eac event is generated") {
            every { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) } returns testJuniferEstimatedUsageForElec
            coEvery { mockSyncClient.syncEstimatedUsageEntity(any()) } returns estimatedUsageSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    ESTIMATED_USAGE_ELECTRICITY,
                    JUNIFER_MPAN_EAC_ID.toString(),
                    ESTIMATED_USAGE_ID.toString()
                )
            }

            estimatedUsageSyncProcessor.process(mpanEacEvent)

            then("a new estimated usage should be created") {
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                }
                verify { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) }
                verify { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        ESTIMATED_USAGE_ELECTRICITY,
                        JUNIFER_MPAN_EAC_ID.toString(),
                        ESTIMATED_USAGE_ID.toString()
                    )
                }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                coVerify {
                    mockSyncClient.syncEstimatedUsageEntity(testJuniferEacUsageCreateRequest)
                }
            }
        }
    }

    given("Existing mapped estimated usage and an existing junifer Mpan Eac") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every {
            mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
        } returns ESTIMATED_USAGE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns METER_POINT_CORE_JUNIFER_ID
        every { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) } returns Junifer_Mpaneac(id = JUNIFER_MPAN_EAC_ID)

        `when`("a mpan eac event is generated") {

            every { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) } returns
                    testJuniferEstimatedUsageForElec
            coEvery { mockSyncClient.syncEstimatedUsageEntity(any()) } returns
                    estimatedUsageSyncResponse

            estimatedUsageSyncProcessor.process(mpanEacEvent)

            then("estimated usage should be patched") {
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                }
                verify { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) }
                coVerify {
                    mockSyncClient.syncEstimatedUsageEntity(withArg {
                        it shouldBe testJuniferEacUsageUpdateRequest.copy {
                            estimatedUsageEntity = testJuniferEacUsageUpdateRequest.estimatedUsageEntity.copy {
                                id = ESTIMATED_USAGE_ID.toNullableInt64()
                                meterPointId = METER_POINT_CORE_JUNIFER_ID.toLong().toNullableInt64()
                            }
                        }
                    })
                }
            }
        }
    }

    given("no existing junifer Mpan Eac") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every {
            mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
        } returns ESTIMATED_USAGE_ID.toString()
        every { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) } returns Junifer_Mpaneac(id = JUNIFER_MPAN_EAC_ID)
        every { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) } throws
                EntityNotFoundException("")

        `when`("a mpan eac event is generated and retry count less than max retry count") {

            shouldThrow<AutoDiscardableException> {
                estimatedUsageSyncProcessor.process(mpanEacEvent.copy(retryCount = 2))
            }

            then("event should be delayed") {
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                }
                verify { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) }
                verify { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) }
                verify(exactly = 0) {
                    mockMapper.getCoreId(
                        EntityIdentifier.METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                coVerify(exactly = 0) { mockSyncClient.syncEstimatedUsageEntity(any()) }
            }
        }

        `when`("a mpan eac event is generated and retry count reached max limit") {

            val exception = shouldThrow<AutoDiscardableException> {
                estimatedUsageSyncProcessor.process(
                    mpanEacEvent.copy(retryCount = 10)
                )
            }

            then("event should be completed, no more operations") {
                exception.canDiscard shouldBe true
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                }
                verify { mockUsageRepo.getMpanEstimatedUsage(mpanEacId = JUNIFER_MPAN_EAC_ID) }
                verify { mockUsageRepo.getMpanEacById(JUNIFER_MPAN_EAC_ID) }
                verify(exactly = 0) {
                    mockMapper.getCoreId(
                        EntityIdentifier.METER_POINT,
                        JUNIFER_METER_POINT_ID.toString()
                    )
                }
                coVerify(exactly = 0) { mockSyncClient.syncEstimatedUsageEntity(any()) }
            }
        }
    }

    given("no existing mapped estimated usage and an existing junifer Mprn annual Quantity") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every {
            mockMapper.getCoreId(ESTIMATED_USAGE_GAS, JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString())
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString()
            )
        } returns METER_POINT_CORE_JUNIFER_ID
        every { mockUsageRepo.getMprnEstimatedUsage(mprnAnnualQuantityId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID) } returns
                testJuniferEstimatedUsageForGas
        every { mockUsageRepo.getMprnEstimatedUsageById(JUNIFER_MPRN_ANNUAL_QUANTITY_ID) } returns
                Junifer_Mprnannualquantity(id = JUNIFER_MPRN_ANNUAL_QUANTITY_ID)

        `when`("a mprn annual quantity event is generated") {

            coEvery { mockSyncClient.syncEstimatedUsageEntity(any()) } returns estimatedUsageSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    ESTIMATED_USAGE_GAS,
                    JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString(),
                    ESTIMATED_USAGE_ID.toString()
                )
            }

            estimatedUsageSyncProcessor.process(mprnAnnualQuantityEvent)

            then("a new estimated usage should be created") {
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_GAS, JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString())
                }
                verify { mockUsageRepo.getMprnEstimatedUsage(mprnAnnualQuantityId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        ESTIMATED_USAGE_GAS,
                        JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString(),
                        ESTIMATED_USAGE_ID.toString()
                    )
                }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockUsageRepo.getMprnEstimatedUsageById(JUNIFER_MPRN_ANNUAL_QUANTITY_ID) }
                coVerify {
                    mockSyncClient.syncEstimatedUsageEntity(withArg {
                        it shouldBe testJuniferAqUsageCreateRequest.copy {
                            estimatedUsageEntity = testJuniferAqUsageCreateRequest.estimatedUsageEntity.copy {
                                meterPointId = METER_POINT_CORE_JUNIFER_ID.toLong().toNullableInt64()
                                syncTransactionId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toNullableLongString()
                            }
                        }
                    })
                }
            }
        }
    }

    given("Existing mapped estimated usage and an existing junifer Mprn annual Quantity") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every {
            mockMapper.getCoreId(ESTIMATED_USAGE_GAS, JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString())
        } returns ESTIMATED_USAGE_ID.toString()
        every {
            mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
        } returns METER_POINT_CORE_JUNIFER_ID
        every { mockUsageRepo.getMprnEstimatedUsage(mprnAnnualQuantityId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID) } returns
                testJuniferEstimatedUsageForGas
        every { mockUsageRepo.getMprnEstimatedUsageById(JUNIFER_MPRN_ANNUAL_QUANTITY_ID) } returns
                Junifer_Mprnannualquantity(id = JUNIFER_MPRN_ANNUAL_QUANTITY_ID)

        `when`("a mprn annual quantity event is generated") {

            coEvery { mockSyncClient.syncEstimatedUsageEntity(any()) } returns estimatedUsageSyncResponse

            estimatedUsageSyncProcessor.process(mprnAnnualQuantityEvent)

            then("estimated usage should be patched") {
                verify {
                    mockMapper.getCoreId(ESTIMATED_USAGE_GAS, JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toString())
                }
                verify { mockUsageRepo.getMprnEstimatedUsage(mprnAnnualQuantityId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString()) }
                verify { mockUsageRepo.getMprnEstimatedUsageById(JUNIFER_MPRN_ANNUAL_QUANTITY_ID) }
                coVerify {
                    mockSyncClient.syncEstimatedUsageEntity(withArg {
                        it shouldBe testJuniferAqUsageUpdateRequest.copy {
                            estimatedUsageEntity = testJuniferAqUsageUpdateRequest.estimatedUsageEntity.copy {
                                id = ESTIMATED_USAGE_ID.toNullableInt64()
                                meterPointId = METER_POINT_CORE_JUNIFER_ID.toLong().toNullableInt64()
                                syncTransactionId = JUNIFER_MPRN_ANNUAL_QUANTITY_ID.toNullableLongString()
                            }
                        }
                    })
                }
            }
        }
    }

    given("event type is MPAN_EAC_SET") {
        // Something in this file is leaving behind mocks between tests
        clearMocks(mockMapper, mockUsageRepo, mockSyncClient)

        every { mockUsageRepo.getMpanEacSetById(JUNIFER_MPAN_EAC_SET_ID) } returns
                Junifer_Mpaneacset(id = JUNIFER_MPAN_EAC_SET_ID)

        and("operation type is UPDATE") {

            val event = mpanEacSetEvent

            and("there are estimated usages for given reference") {
                coEvery { mockUsageRepo.getMpanEstimatedUsagesByMpanEacSetId(JUNIFER_MPAN_EAC_SET_ID) } returns listOf(
                    testJuniferEstimatedUsageForElec
                )
                and("the estimated usages are mapped") {

                    every {
                        mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                    } returns ESTIMATED_USAGE_ID.toString()
                    every {
                        mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
                    } returns METER_POINT_CORE_JUNIFER_ID

                    `when`("a mpan eac set update event is generated") {

                        coEvery { mockSyncClient.syncEstimatedUsageEntity(testJuniferEacUsageUpdateRequest) } returns
                                estimatedUsageSyncResponse

                        estimatedUsageSyncProcessor.process(event)

                        then("should proccess event") {
                            verify {
                                mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                            }
                            verify {
                                mockUsageRepo.getMpanEstimatedUsagesByMpanEacSetId(JUNIFER_MPAN_EAC_SET_ID)
                            }
                            verify { mockUsageRepo.getMpanEacSetById(JUNIFER_MPAN_EAC_SET_ID) }
                            verify {
                                mockMapper.getCoreId(EntityIdentifier.METER_POINT, JUNIFER_METER_POINT_ID.toString())
                            }
                            coVerify {
                                mockSyncClient.syncEstimatedUsageEntity(testJuniferEacUsageUpdateRequest)
                            }
                        }
                    }
                }

                and("the estimated usages are not mapped") {
                    every {
                        mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                    } returns null
                    `when`("a mpan eac set update event is generated") {
                        then("should throw SyncDelayedException") {
                            shouldThrow<SyncDelayedException> { estimatedUsageSyncProcessor.process(event) }
                            verify {
                                mockUsageRepo.getMpanEstimatedUsagesByMpanEacSetId(JUNIFER_MPAN_EAC_SET_ID)
                            }
                            verify { mockUsageRepo.getMpanEacSetById(JUNIFER_MPAN_EAC_SET_ID) }
                            verify {
                                mockMapper.getCoreId(ESTIMATED_USAGE_ELECTRICITY, JUNIFER_MPAN_EAC_ID.toString())
                            }
                        }
                    }
                }
            }

            and("there are no estimated usages for given reference") {
                coEvery { mockUsageRepo.getMpanEstimatedUsagesByMpanEacSetId(JUNIFER_MPAN_EAC_SET_ID) } returns listOf()
                `when`("a mpan eac set update event is generated") {
                    estimatedUsageSyncProcessor.process(event)
                    then("there is nothing processed") {
                        verify {
                            mockUsageRepo.getMpanEstimatedUsagesByMpanEacSetId(JUNIFER_MPAN_EAC_SET_ID)
                        }
                        verify { mockUsageRepo.getMpanEacSetById(JUNIFER_MPAN_EAC_SET_ID) }
                    }
                }
            }
        }

        and("operation type is not UPDATE") {
            val event = mpanEacSetEvent.copy(operationType = OperationType.INSERT)
            `when`("a mpan eac set update event is generated") {
                estimatedUsageSyncProcessor.process(event)
                then("nothing happens") {}
            }
        }
    }
})
