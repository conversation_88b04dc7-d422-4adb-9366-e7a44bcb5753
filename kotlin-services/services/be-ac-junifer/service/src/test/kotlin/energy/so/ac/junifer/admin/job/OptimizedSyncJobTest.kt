package energy.so.ac.junifer.admin.job

import energy.so.ac.junifer.admin.database.repositories.AccountRepository
import energy.so.ac.junifer.admin.database.repositories.SyncedAccountRepository
import energy.so.ac.junifer.admin.services.ResyncService
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.LOCKED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.NEWLY_CREATED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.NOT_LOCKED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.listOfAccounts
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.listOfAccountsIds
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.lockedAccount
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.notLockedAccount
import energy.so.ac.junifer.v1.admin.resyncAccountRequest
import energy.so.commons.model.tables.pojos.SynchronizedAccount
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class OptimizedSyncJobTest : BehaviorSpec({
    val mockResyncService = mockk<ResyncService>()
    val mockSyncedAccountRepository = mockk<SyncedAccountRepository>()
    val mockAccountRepository = mockk<AccountRepository>()

    val sut = OptimizedSyncJob(mockSyncedAccountRepository, mockAccountRepository, mockResyncService, 2, 60)

    afterEach {
        clearMocks(mockResyncService, mockSyncedAccountRepository, mockAccountRepository)
        confirmVerified(mockResyncService, mockSyncedAccountRepository, mockAccountRepository)
    }

    given("no accounts to sync") {
        every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
        every { mockSyncedAccountRepository.findAll() } returns emptyList()
        every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns emptyList()

        `when`("cron job is run") {
            sut.runAccountsSync()

            then("nothing happens") {
                verify(exactly = 0) { mockResyncService.resyncAccount(any()) }
                verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                verify {
                    mockSyncedAccountRepository.findAll()
                    mockAccountRepository.findIdsByCreatedAtAfter(any())
                }
            }
        }
    }

    given("one account to sync") {
        every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
        every { mockSyncedAccountRepository.findAll() } returns listOf(
            SynchronizedAccount(
                id = 1L,
                juniferaccountid = 2L,
            )
        )
        every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns emptyList()

        `when`("cron job is run") {
            sut.runAccountsSync()

            then("account is synced") {
                verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                verify(exactly = 1) {
                    mockAccountRepository.findIdsByCreatedAtAfter(any())
                    mockSyncedAccountRepository.findAll()
                    mockResyncService.resyncAccount(eq(resyncAccountRequest {
                        juniferAccountId = 2L
                        syncCustomer = true
                        syncPayments = true
                        syncAssets = true
                    }))
                }
            }
        }
    }

    given("new accounts created today") {
        and("there is free capacity in synchronized table") {
            every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
            every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns listOf(NEWLY_CREATED_ACCOUNT_ID)
            every { mockSyncedAccountRepository.findAll() } returns listOf(lockedAccount)
            justRun { mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID)) }
            justRun { mockResyncService.resyncAccount(any()) }

            `when`("cron job is run") {
                sut.runAccountsSync()

                then("accounts added to the synchronized accounts table") {
                    assertSoftly {
                        verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                        verify(exactly = 1) {
                            mockAccountRepository.findIdsByCreatedAtAfter(any())
                            mockSyncedAccountRepository.findAll()
                            mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID))
                        }
                        verify { mockResyncService.resyncAccount(any()) }
                    }
                }
            }
        }

        and("today`s accounts already in synchronized accounts table") {
            every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
            every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns listOf(NEWLY_CREATED_ACCOUNT_ID)
            every { mockSyncedAccountRepository.findAll() } returns listOf(
                lockedAccount,
                notLockedAccount.copy(juniferaccountid = NEWLY_CREATED_ACCOUNT_ID)
            )
            justRun { mockResyncService.resyncAccount(any()) }

            `when`("cron job is run") {
                sut.runAccountsSync()

                then("accounts added to the synchronized accounts table") {
                    assertSoftly {
                        verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                        verify(exactly = 1) {
                            mockAccountRepository.findIdsByCreatedAtAfter(any())
                            mockSyncedAccountRepository.findAll()
                        }
                        verify { mockResyncService.resyncAccount(any()) }
                    }
                }
            }
        }
        and("no free capacity, closed account present in table >= new created accounts") {
            every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
            every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns listOf(NEWLY_CREATED_ACCOUNT_ID)
            every { mockSyncedAccountRepository.findAll() } returns listOfAccounts
            every {
                mockAccountRepository.findClosedAccounts(listOfAccountsIds)
            } returns listOf(NOT_LOCKED_ACCOUNT_ID)
            every { mockAccountRepository.deleteWhitelistedAccounts(1, listOf(NOT_LOCKED_ACCOUNT_ID)) } returns 1
            justRun { mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID)) }
            justRun { mockResyncService.resyncAccount(any()) }

            `when`("cron job is run") {
                sut.runAccountsSync()

                then("delete closed account, add new account") {
                    assertSoftly {
                        verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                        verify(exactly = 1) {
                            mockAccountRepository.findIdsByCreatedAtAfter(any())
                            mockSyncedAccountRepository.findAll()
                            mockAccountRepository.findClosedAccounts(
                                listOf(LOCKED_ACCOUNT_ID, NOT_LOCKED_ACCOUNT_ID)
                            )
                            mockAccountRepository.deleteWhitelistedAccounts(1, listOf(NOT_LOCKED_ACCOUNT_ID))
                            mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID))
                        }
                        verify { mockResyncService.resyncAccount(any()) }
                    }
                }
            }
        }

        and("closed account present in table < new created accounts") {
            every { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
            every { mockAccountRepository.findIdsByCreatedAtAfter(any()) } returns listOf(NEWLY_CREATED_ACCOUNT_ID)
            every { mockSyncedAccountRepository.findAll() } returns listOfAccounts
            every { mockAccountRepository.findClosedAccounts(listOfAccountsIds) } returns emptyList()
            every { mockAccountRepository.deleteWhitelistedAccounts(1, emptyList()) } returns 0
            every { mockAccountRepository.deleteWhitelistedAccounts(1, null) } returns 1
            justRun { mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID)) }
            justRun { mockResyncService.resyncAccount(any()) }

            `when`("cron job is run") {
                sut.runAccountsSync()

                then("delete closed account, add new account") {
                    assertSoftly {
                        verify(exactly = 2) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                        verify(exactly = 1) {
                            mockAccountRepository.findIdsByCreatedAtAfter(any())
                            mockSyncedAccountRepository.findAll()
                            mockAccountRepository.findClosedAccounts(listOfAccountsIds)
                            mockAccountRepository.deleteWhitelistedAccounts(1, emptyList())
                            mockResyncService.addAccountsToWhiteList(listOf(NEWLY_CREATED_ACCOUNT_ID))
                        }
                        verify { mockAccountRepository.deleteWhitelistedAccounts(1, null) }
                        verify { mockResyncService.resyncAccount(any()) }
                    }
                }
            }
        }
    }

    given("a pod already running the re-sync job") {
        coEvery { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 0

        `when`("cron job is run") {
            sut.runAccountsSync()

            then("job is skipped") {
                assertSoftly {
                    coVerify(exactly = 1) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                    coVerify(exactly = 0) {
                        mockAccountRepository.findIdsByCreatedAtAfter(any())
                        mockSyncedAccountRepository.findAll()
                        mockAccountRepository.findClosedAccounts(any())
                        mockResyncService.addAccountsToWhiteList(any())
                        mockAccountRepository.deleteWhitelistedAccounts(any(), any())
                        mockResyncService.resyncAccount(any())
                    }
                }
            }
        }
    }

    given("sync refs") {
        and("another pod is already running") {
            coEvery { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 0
            justRun { mockResyncService.resyncLoneReferences() }
            `when`("cron job runs") {
                sut.runLoneReferencesSync()

                then("method called") {
                    coVerify(exactly = 1) { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) }
                    verify(exactly = 0) { mockResyncService.resyncLoneReferences() }
                }
            }
        }
        and("no other pod is running") {
            coEvery { mockAccountRepository.setOptimizedSyncRunningFlag(any(), any(), any()) } returns 1
            justRun { mockResyncService.resyncLoneReferences() }
            `when`("cron job runs") {
                sut.runLoneReferencesSync()

                then("method called") {
                    coVerify(exactly = 1) {
                        mockAccountRepository.setOptimizedSyncRunningFlag(any(), true, any())
                        mockAccountRepository.setOptimizedSyncRunningFlag(any(), false, any())
                        mockResyncService.resyncLoneReferences()
                    }
                }
            }
        }
    }
})
