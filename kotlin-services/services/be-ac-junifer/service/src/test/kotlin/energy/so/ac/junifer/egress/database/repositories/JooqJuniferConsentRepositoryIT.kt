package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.config.BOOLEAN_FALSE
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.testJuniferConsent
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.METER_POINT_ID
import energy.so.commons.extension.save
import energy.so.commons.model.tables.references.JUNIFER__CONSENT
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.collections.shouldBeIn
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

class JooqJuniferConsentRepositoryIT : BehaviorSpec({
    val id = 123L
    val db = installDatabase()
    val repo = JooqJuniferConsentRepository(db)

    given("::findById") {
        and("no data saved in db") {
            `when`("call findById") {
                val consent = repo.findById(id)

                then("result is null") {
                    consent shouldBe null
                }
            }
        }

        and("no consent with given id in db") {
            db.newRecord(JUNIFER__CONSENT, testJuniferConsent).apply { save() }

            `when`("call findById") {
                val consent = repo.findById(id)

                then("result is null") {
                    consent shouldBe null
                }
            }
        }

        and("consent with given id in db") {
            db.newRecord(JUNIFER__CONSENT, testJuniferConsent).apply { save() }

            `when`("call findById") {
                val consent = repo.findById(testJuniferConsent.id!!)

                then("result is not null") {
                    consent shouldNotBe null
                    consent!!.setting shouldBe testJuniferConsent.setting
                    consent.entityid shouldBe testJuniferConsent.entityid
                }
            }
        }
    }

    given("::findByUkVulnCustPsrId") {
        and("no data saved in db") {
            `when`("call findByUkVulnCustPsrId") {
                val consent = repo.findByUkVulnCustPsrId(id)

                then("result is null") {
                    consent shouldBe null
                }
            }
        }

        and("no consent with given id in db") {
            db.newRecord(JUNIFER__CONSENT, testJuniferConsent).apply { save() }

            `when`("call findByUkVulnCustPsrId") {
                val consent = repo.findByUkVulnCustPsrId(id)

                then("result is null") {
                    consent shouldBe null
                }
            }
        }

        and("consent with given id in db") {
            and("now() outside interval from-to") {
                db.newRecord(JUNIFER__CONSENT, testJuniferConsent.copy(fromdttm = testJuniferConsent.todttm))
                    .apply { save() }

                `when`("call findByUkVulnCustPsrId") {
                    val consent = repo.findByUkVulnCustPsrId(testJuniferConsent.entityid!!)

                    then("result is null") {
                        consent shouldBe null
                    }
                }
            }
            and("consent cancelled") {
                db.newRecord(JUNIFER__CONSENT, testJuniferConsent.copy(cancelfl = "Y"))
                    .apply { save() }

                `when`("call findByUkVulnCustPsrId") {
                    val consent = repo.findByUkVulnCustPsrId(testJuniferConsent.entityid!!)

                    then("result is null") {
                        consent shouldBe null
                    }
                }
            }
            and("consent valid") {
                db.newRecord(JUNIFER__CONSENT, testJuniferConsent)
                    .apply { save() }

                `when`("call findByUkVulnCustPsrId") {
                    val consent = repo.findByUkVulnCustPsrId(testJuniferConsent.entityid!!)

                    then("result is not null") {
                        consent shouldNotBe null
                        consent!!.setting shouldBe testJuniferConsent.setting
                        consent.entityid shouldBe testJuniferConsent.entityid
                    }
                }
            }
        }
    }

    given("::findReadyFrequencyConsentHistory") {

        db.newRecord(
            JUNIFER__CONSENT, testJuniferConsent.copy(
                id = 1,
                setting = "Half Hourly",
                entityid = METER_POINT_ID,
                consentdefinitionfk = MHHS_BILLING_DEFINITION,
                entitytblfk = METERPOINT_ENTITY_ENUM,
            )
        ).apply { save() }

        db.newRecord(
            JUNIFER__CONSENT, testJuniferConsent.copy(
                id = 2,
                setting = "Daily",
                entityid = METER_POINT_ID,
                consentdefinitionfk = MHHS_BILLING_DEFINITION,
                entitytblfk = METERPOINT_ENTITY_ENUM,
            )
        ).apply { save() }

        db.newRecord(
            JUNIFER__CONSENT, testJuniferConsent.copy(
                id = 3,
                setting = "Half Hourly",
                entityid = METER_POINT_ID,
                consentdefinitionfk = MPRN_BILLING_DEFINITION,
                entitytblfk = METERPOINT_ENTITY_ENUM,
            )
        ).apply { save() }

        `when`("consent history is queried") {

            val consents = repo.findReadFrequencyConsentHistory(METER_POINT_ID)

            then("all non deleted consent entries should be returned for the given meterpoint") {
                consents.size shouldBe 3
                consents.forEach {
                    it.entityid shouldBe METER_POINT_ID
                    it.entitytblfk shouldBe METERPOINT_ENTITY_ENUM
                    it.consentdefinitionfk shouldBeIn listOf(MHHS_BILLING_DEFINITION, MPRN_BILLING_DEFINITION)
                    it.deletefl shouldBe BOOLEAN_FALSE
                }
            }
        }
    }

    given("::findReadyFrequencyConsent") {

        db.newRecord(
            JUNIFER__CONSENT, testJuniferConsent.copy(
                id = 1,
                setting = "Half Hourly",
                entityid = METER_POINT_ID,
                consentdefinitionfk = MHHS_BILLING_DEFINITION,
                entitytblfk = METERPOINT_ENTITY_ENUM,
            )
        ).apply { save() }

        `when`("consent is queried and a consent exists ") {

            val consent = repo.findReadFrequencyConsent(METER_POINT_ID)

            then("the current consent should be returned") {
                consent shouldNotBe null
                consent!!.setting shouldBe "Half Hourly"
            }
        }

        `when`("consent is queried and no consent exists") {

            val consent = repo.findReadFrequencyConsent(6969)

            then("no consent should be returned") {
                consent shouldBe null
            }
        }
    }
})
