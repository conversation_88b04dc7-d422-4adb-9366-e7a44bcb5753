package energy.so.ac.junifer.egress.housekeeping

import energy.so.ac.junifer.config.JobFailedEventsConfig
import energy.so.ac.junifer.egress.database.repositories.SyncEventRepository
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class FailedEvensJobTest : BehaviorSpec({

    isolationMode = IsolationMode.InstancePerTest

    val mockRepository = mockk<SyncEventRepository>()

    val job =
        FailedEventsJob(mockRepository, JobFailedEventsConfig(limit = 10, batch = 5))

    given("Reset of failed events fails") {
        every { mockRepository.resetFailedEvents(5) } throws IllegalStateException()

        `when`("Job runs") {
            shouldThrow<IllegalStateException> { job.run() }

            then("Job fails") {
                verify { mockRepository.resetFailedEvents(5) }
            }
        }
    }

    given("Reset of failed events succeeds") {
        every { mockRepository.resetFailedEvents(5) } returns 5

        `when`("Job runs") {
            job.run()

            then("Job succeeds") {
                verify(exactly = 2) { mockRepository.resetFailedEvents(5) }
            }
        }
    }
})
