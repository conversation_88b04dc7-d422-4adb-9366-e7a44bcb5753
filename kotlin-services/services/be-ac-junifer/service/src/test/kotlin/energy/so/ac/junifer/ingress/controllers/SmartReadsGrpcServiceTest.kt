package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aBulkXRITMErrorResult
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aBulkXRITMGrpcRequest
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aBulkXRITMGrpcResponse
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aSecondXRITMMeterDto
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.anXRITMMeterDto
import energy.so.ac.junifer.ingress.services.smartreads.HttpJuniferSmartReadsService
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.assertDoesNotThrow

class SmartReadsGrpcServiceTest : BehaviorSpec({

    val smartReadsService = mockk<HttpJuniferSmartReadsService>()
    val sut = SmartReadsGrpcService(smartReadsService)

    given("Valid request to BulkXRITM") {
        val grpcRequest = aBulkXRITMGrpcRequest
        val expectedMappedRequest = listOf(anXRITMMeterDto, aSecondXRITMMeterDto)
        coEvery { smartReadsService.bulkXRITMReads(expectedMappedRequest) } returns listOf(aBulkXRITMErrorResult)
        `when`("Service is called") {
            val result = assertDoesNotThrow { sut.bulkXRITM(grpcRequest) }
            then("Correct response returned")
            result shouldBe aBulkXRITMGrpcResponse
        }
    }
})