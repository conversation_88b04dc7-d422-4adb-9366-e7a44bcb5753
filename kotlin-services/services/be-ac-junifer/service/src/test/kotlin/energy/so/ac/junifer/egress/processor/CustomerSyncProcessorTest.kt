package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferContactRepository
import energy.so.ac.junifer.egress.database.repositories.JuniferCustomerRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CORE_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_CLASS_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_TYPE_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.createCustomerSync
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.customerSyncEvent
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.customerSyncResponse
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.deleteCustomerSync
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.juniferContactVersion
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.juniferCustomer
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.juniferCustomerClass
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.juniferCustomerType
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.patchCustomerSync
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.Called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class CustomerSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockRepo = mockk<JuniferCustomerRepository>()
    val mockContactRepo = mockk<JuniferContactRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = CustomerSyncProcessor(mockMapper, mockSyncClient, mockRepo, mockContactRepo)

    afterEach {
        clearMocks(mockMapper, mockRepo, mockContactRepo, mockSyncClient)
    }

    given("No existing customer but customer in core") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns CORE_CUSTOMER_ID.toString()
        coEvery { mockSyncClient.syncCustomerEntity(deleteCustomerSync) } returns customerSyncResponse

        `when`("Customer event") {
            processor.process(customerSyncEvent)

            then("Customer should be deleted") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                coVerify { mockSyncClient.syncCustomerEntity(deleteCustomerSync) }
            }
        }
    }

    given("No existing customer and customer not core") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns null

        `when`("Customer event") {
            processor.process(customerSyncEvent)

            then("Should ignore event") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Customer exists but not customer type") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns juniferCustomer
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns CORE_CUSTOMER_ID.toString()
        every { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) } returns null
        every { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) } returns juniferCustomerClass

        `when`("Customer event") {
            shouldThrow<SyncDelayedException> { processor.process(customerSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.CUSTOMER,
                        JUNIFER_CUSTOMER_ID.toString()
                    )
                }
                verify { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) }
                verify { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Customer exists but not customer class") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns juniferCustomer
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns CORE_CUSTOMER_ID.toString()
        every { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) } returns juniferCustomerType
        every { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) } returns null

        `when`("Customer event") {
            shouldThrow<SyncDelayedException> { processor.process(customerSyncEvent) }

            then("Event should be delayed") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.CUSTOMER,
                        JUNIFER_CUSTOMER_ID.toString()
                    )
                }
                verify { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) }
                coVerify { mockSyncClient wasNot Called }
            }
        }
    }

    given("Customer exists but not in core") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns juniferCustomer
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns null
        every { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) } returns juniferCustomerType
        every { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) } returns juniferCustomerClass
        every { mockContactRepo.findPrimaryCustomerContactVersion(JUNIFER_CUSTOMER_ID) } returns juniferContactVersion
        coEvery { mockSyncClient.syncCustomerEntity(createCustomerSync) } returns customerSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString(), CORE_CUSTOMER_ID.toString()
            )
        }

        `when`("Customer event") {
            processor.process(customerSyncEvent)

            then("Customer should be created") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) }
                verify { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) }
                coVerify { mockSyncClient.syncCustomerEntity(createCustomerSync) }

                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.CUSTOMER,
                        JUNIFER_CUSTOMER_ID.toString(), CORE_CUSTOMER_ID.toString()
                    )
                }
            }
        }
    }

    given("Customer exists and in core") {
        every { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) } returns juniferCustomer
        every {
            mockMapper.getCoreId(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString()
            )
        } returns CORE_CUSTOMER_ID.toString()
        every { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) } returns juniferCustomerType
        every { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) } returns juniferCustomerClass
        coEvery { mockSyncClient.syncCustomerEntity(patchCustomerSync) } returns customerSyncResponse
        justRun {
            mockMapper.createCoreMapping(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString(), CORE_CUSTOMER_ID.toString()
            )
        }

        `when`("Customer event") {
            processor.process(customerSyncEvent)

            then("Customer should be updated") {
                verify { mockRepo.findCustomer(JUNIFER_CUSTOMER_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockRepo.findCustomerType(JUNIFER_CUSTOMER_TYPE_ID) }
                verify { mockRepo.findCustomerClass(JUNIFER_CUSTOMER_CLASS_ID) }
                coVerify { mockSyncClient.syncCustomerEntity(patchCustomerSync) }

                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.CUSTOMER,
                        JUNIFER_CUSTOMER_ID.toString(), CORE_CUSTOMER_ID.toString()
                    )
                }
            }
        }
    }
})
