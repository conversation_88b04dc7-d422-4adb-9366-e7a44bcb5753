package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.fixtures.AccountFinderPrecannedData
import energy.so.ac.junifer.v1.accounts.AccountFuel
import energy.so.ac.junifer.v1.accounts.copy
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.database.test.installDatabase
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.string.shouldContain

class JooqAccountFinderRepositoryTest : BehaviorSpec({

    val db = installDatabase()
    val sut = JooqAccountFinderRepository(db)

    given("a valid request") {
        `when`("::generateAndRunAccountFinderQuery") {
            var resultQuery: String

            then("request with dual fuel query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest)

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.dualFuelCTE)
                    .shouldContain(JooqAccountFinderRepository.dualFuelWhere)
            }
            then("request with elec only query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.ELECTRICITY
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.elecOnlyWhere)
            }
            then("request with gas only query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.GAS
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.gasOnlyWhere)
            }
            then("request with multiple gas query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.GAS
                    multipleMeters = true
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.multipleQuery1)
                    .shouldContain(JooqAccountFinderRepository.multipleQuery2)
                    .shouldContain(JooqAccountFinderRepository.gasOnlyWhere)
            }
            then("request with multiple elec query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.ELECTRICITY
                    multipleMeters = true
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.multipleQuery1)
                    .shouldContain(JooqAccountFinderRepository.multipleQuery2)
                    .shouldContain(JooqAccountFinderRepository.elecOnlyWhere)

            }
            then("request with smart meter dual query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    smartMeter = true.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.smartMeterWhere)
                    .shouldContain(JooqAccountFinderRepository.smartMeterCTE)
                    .shouldContain(JooqAccountFinderRepository.dualFuelCTE)
                    .shouldContain(JooqAccountFinderRepository.dualFuelWhere)
            }
            then("request with smart meter gas query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.GAS
                    multipleMeters = false
                    smartMeter = true.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.smartMeterWhere)
                    .shouldContain(JooqAccountFinderRepository.smartMeterCTE)
                    .shouldContain(JooqAccountFinderRepository.gasOnlyWhere)
            }
            then("request with basic meter elec query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.ELECTRICITY
                    multipleMeters = false
                    smartMeter = false.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.smartMeterCTE)
                    .shouldContain(JooqAccountFinderRepository.basicMeterWhere)
                    .shouldContain(JooqAccountFinderRepository.elecOnlyWhere)
            }
            then("request with true seasonal flag query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    seasonalFlag = true.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.seasonalFlagJoin)
                    .shouldContain(JooqAccountFinderRepository.seasonalFlagWhere)
                    .shouldContain(JooqAccountFinderRepository.dualFuelCTE)
                    .shouldContain(JooqAccountFinderRepository.dualFuelWhere)
            }
            then("request with false seasonal flag query") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    seasonalFlag = false.toNullableBoolean()
                })

                resultQuery.shouldContain(JooqAccountFinderRepository.seasonalFlagJoin)
                    .shouldContain(JooqAccountFinderRepository.noSeasonalFlagWhere)
                    .shouldContain(JooqAccountFinderRepository.dualFuelCTE)
                    .shouldContain(JooqAccountFinderRepository.dualFuelWhere)
            }
            then("request with limit 100") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    limit = 100
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain("LIMIT 100")
                    .shouldContain(JooqAccountFinderRepository.dualFuelCTE)
            }
            then("request with true direct debit") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    directDebit = true.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.directDebitJoin)
                    .shouldContain(JooqAccountFinderRepository.directDebitWhere)
            }
            then("request with false direct debit") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    directDebit = false.toNullableBoolean()
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.directDebitJoin)
                    .shouldContain(JooqAccountFinderRepository.noDirectDebitWhere)
            }
            then("request with null account fuel") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    fuel = AccountFuel.NULL
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain(JooqAccountFinderRepository.genericQuery)
            }
            then("request with outcode check") {
                resultQuery = sut.generateQuery(AccountFinderPrecannedData.BobsData.validAccountFinderRequest.copy {
                    outcode = "ME"
                })

                shouldNotThrow<Exception> {
                    db.execute(resultQuery)
                }

                resultQuery.shouldContain("postcode LIKE 'ME%'")
            }
        }
    }
})