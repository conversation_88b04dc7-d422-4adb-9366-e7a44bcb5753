package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlan
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlanPayment
import energy.so.commons.model.tables.references.JUNIFER__PAYMENTPLANPAYMENT
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferPaymentPlanPaymentRepositoryTest : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferPaymentPlanPaymentRepository(db)

    given("PaymentPlan exists in DB") {
        insertPaymentPlan(db)
        insertPaymentPlanPayment(db)

        `when`("PaymentPlan is queried") {
            val paymentPlanPayment = repo.getPaymentPlanPayment(JUNIFER_PAYMENT_PLAN_PAYMENT_ID)

            then("PaymentPlan is returned") {
                with(paymentPlanPayment) {
                    this shouldNotBe null
                    id shouldBe testJuniferPaymentPlanPayment.id
                    paymentplanfk shouldBe testJuniferPaymentPlanPayment.paymentplanfk
                    scheduleddt shouldBe testJuniferPaymentPlanPayment.scheduleddt
                    scheduledamount?.toDouble() shouldBe testJuniferPaymentPlanPayment.scheduledamount?.toDouble()
                    paymentrequestfk shouldBe testJuniferPaymentPlanPayment.paymentrequestfk
                    reference shouldBe testJuniferPaymentPlan.reference
                    deletefl shouldBe testJuniferPaymentPlan.deletefl
                    versionno shouldBe testJuniferPaymentPlan.versionno
                    partitionid shouldBe testJuniferPaymentPlan.partitionid
                }
            }
        }
    }

})

fun insertPaymentPlanPayment(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__PAYMENTPLANPAYMENT, testJuniferPaymentPlanPayment))
}