package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.CustomerPrecannedData.updatePrimaryContactDto
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.updatePrimaryContactProto
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class UpdatePrimaryContactMapperTests : BehaviorSpec({

    given("updatePrimaryContact proto object") {
        val protoObject = updatePrimaryContactProto
        `when`("call toUpdatePrimaryContactDto method") {
            val result = protoObject.toUpdatePrimaryContact()
            then("the result should have same fields") {
                result shouldBe updatePrimaryContactDto
            }
        }
    }
})
