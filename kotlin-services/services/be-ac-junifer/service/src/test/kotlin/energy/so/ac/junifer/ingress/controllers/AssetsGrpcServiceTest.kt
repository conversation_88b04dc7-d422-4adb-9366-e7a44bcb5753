package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.config.JuniferCacheConfig
import energy.so.ac.junifer.egress.database.repositories.JuniferMeterPointRepository
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.cachedJuniferEstimatedUsageResponse
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.encodedEstimatedUsageRequestParams
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.estimatedUsageRequestData
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.juniferEstimatedUsageResponse
import energy.so.ac.junifer.fixtures.INVALID_QUALITY
import energy.so.ac.junifer.fixtures.INVALID_SOURCE
import energy.so.ac.junifer.fixtures.InMemoryJuniferCache
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.fixtures.METER_POINT_CORE_JUNIFER_ID
import energy.so.ac.junifer.fixtures.MTDS_HAVE_ARRIVED
import energy.so.ac.junifer.fixtures.MeterPointPrecannedData.testJuniferMeterPoint
import energy.so.ac.junifer.fixtures.MeterPointStructureData
import energy.so.ac.junifer.fixtures.MeterPointStructureData.ID
import energy.so.ac.junifer.fixtures.MeterPointStructureData.JUNIFER_METER_POINT
import energy.so.ac.junifer.fixtures.MeterPointStructureData.cachedJuniferMeterPointStructureResponse
import energy.so.ac.junifer.fixtures.MeterPointStructureData.encodedMeterPointStructureRequestParams
import energy.so.ac.junifer.fixtures.MeterPointStructureData.meterPointStructure
import energy.so.ac.junifer.fixtures.MeterPointStructureData.meterPointStructureRegister
import energy.so.ac.junifer.fixtures.MeterPointStructureData.meterPointStructureRequest
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMpanRequest
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMpanResponseDto
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMpanResponseProto
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMprnResponseDto
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMprnResponseProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.ONE_LONG
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.TEST_STRING
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.encodedDailyMeterReadingsRequestParams
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.encodedGCMDSMeterReadingsResponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.encodedGetMeterReadingsResponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.encodedHHMeterReadingsRequestParams
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.encodedMeterReadingsRequestParams
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.getMeterReadingsRequest
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingsGCMDSReponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingsReponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsValidateOnlyProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithoutDetailsProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationError
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorInvalidQuality
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorInvalidSource
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorMTDsHaveArrived
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorNotFound
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorReadingDtBeforeSSD
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrorUnknownCode
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrors
import energy.so.ac.junifer.fixtures.NOT_FOUND
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testJuniferMpanAsset
import energy.so.ac.junifer.fixtures.READING_DATE_BEFORE_SSD
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.JUNIFER_REGISTER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.REGISTER_ID
import energy.so.ac.junifer.fixtures.RegisterPrecannedData.testJuniferRegister
import energy.so.ac.junifer.fixtures.multipleGspResponse
import energy.so.ac.junifer.fixtures.multipleGspResponseWithNullGspGroupId
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.junifer.JuniferSubmitMeterReadingError
import energy.so.ac.junifer.ingress.junifer.gcmds.ENCODED_TIME_SERIES_RESPONSE_JSON
import energy.so.ac.junifer.ingress.junifer.gcmds.GCMDSDatasource
import energy.so.ac.junifer.ingress.junifer.gcmds.LIST_READINGS_RESPONSE_JSON
import energy.so.ac.junifer.ingress.junifer.gcmds.TIME_SERIES_RESPONSE_JSON
import energy.so.ac.junifer.ingress.junifer.gcmds.getResource
import energy.so.ac.junifer.ingress.junifer.gcmds.loadTestJson
import energy.so.ac.junifer.ingress.models.assets.JuniferEstimatedUsageResponse
import energy.so.ac.junifer.ingress.models.assets.toResponse
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSListMeterReadingsResponse
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSMarketType
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSOrder
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSResolution
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSTimeSeriesResponse
import energy.so.ac.junifer.ingress.models.gcmds.GCMDSUnitOfMeasure
import energy.so.ac.junifer.ingress.models.gcmds.GetTimeSeriesRequest
import energy.so.ac.junifer.ingress.models.gcmds.ListMeterReadingsRequest
import energy.so.ac.junifer.ingress.services.assets.JuniferAssetsService
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EndpointIdentifier
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.assets.ReadFrequency
import energy.so.ac.junifer.v1.assets.cancelMeterPointRegistrationRequest
import energy.so.ac.junifer.v1.assets.copy
import energy.so.ac.junifer.v1.assets.estimatedUsage
import energy.so.ac.junifer.v1.assets.gspGroupIdsByPostcodeRequest
import energy.so.ac.junifer.v1.assets.gspGroupIdsByPostcodeResponse
import energy.so.ac.junifer.v1.assets.submitMeterReadingsRequest
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toOffsetDateTime
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.Optional
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.assertThrows
import org.springframework.http.ResponseEntity

class AssetsGrpcServiceTest : BehaviorSpec({

    val mockJuniferAssetsService = mockk<JuniferAssetsService>("JnonDB")
    val mockJuniferMeterPointRepository = mockk<JuniferMeterPointRepository>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val inMemoryJuniferCache = InMemoryJuniferCache()
    val juniferCacheConfig = JuniferCacheConfig(
        enabled = true,
        getEstimatedUsageExpirationTime = 10,
        getMeterPointStructureExpirationTime = 10,
        getMeterReadingsExpirationTime = 10,
        getMeterIdToRegistersExpirationTime = 10,
        getGcmdsListingsExpirationTime = 10,
    )
    val mockFeatureService = mockk<FeatureService>()
    val mockGCMDSDatasource = mockk<GCMDSDatasource>()
    val assetsGrpcService =
        AssetsGrpcService(
            mockJuniferAssetsService,
            mockGCMDSDatasource,
            inMemoryMapper,
            false,
            mockFeatureService,
            mockJuniferMeterPointRepository,
            inMemoryJuniferCache,
            juniferCacheConfig,
        )

    afterEach {
        confirmVerified(
            mockJuniferAssetsService,
            mockJuniferMeterPointRepository,
            mockFeatureService,
            mockGCMDSDatasource
        )
        clearMocks(
            mockJuniferAssetsService,
            mockJuniferMeterPointRepository,
            mockFeatureService,
            mockGCMDSDatasource
        )
        inMemoryJuniferCache.clear()
    }

    given("valid postcode") {
        val validPostcode = "N8 9TJ"
        val request = gspGroupIdsByPostcodeRequest {
            postcode = validPostcode
            formatAddress = false
        }
        and("none of the gspGroupIds are null") {
            coEvery { mockJuniferAssetsService.getGspGroupIdsByPostcode(request) } returns multipleGspResponse
            val expectedResponse = gspGroupIdsByPostcodeResponse {
                gspGroupIds.addAll(listOf("_C", "_A"))
            }

            `when`("call getGspGroupIdsByPostcode") {
                val actualResponse = assetsGrpcService.getGspGroupIdsByPostcode(request)

                then("should be able to call getGspsByPostcode on JuniferAssestsService") {
                    actualResponse shouldBe expectedResponse
                    coVerify { mockJuniferAssetsService.getGspGroupIdsByPostcode(request) }
                }
            }
        }
        and("one of the gspGroupIds is null") {
            coEvery { mockJuniferAssetsService.getGspGroupIdsByPostcode(request) } returns multipleGspResponseWithNullGspGroupId
            val expectedResponse = gspGroupIdsByPostcodeResponse {
                gspGroupIds.addAll(listOf("_A"))
            }

            `when`("call getGspGroupIdsByPostcode") {
                val actualResponse = assetsGrpcService.getGspGroupIdsByPostcode(request)

                then("should be able to call getGspsByPostcode on JuniferAssestsService") {
                    actualResponse shouldBe expectedResponse
                    coVerify { mockJuniferAssetsService.getGspGroupIdsByPostcode(request) }
                }
            }
        }
    }

    given("no junifer mapping for meterPointId ") {
        val request = submitMeterReadingsRequest { meterPointId = ONE_LONG }

        `when`("call submitMeterReadings") {
            then("throws IllegalStateException") {
                assertThrows<IllegalStateException> { assetsGrpcService.submitMeterReadings(request) }
            }
        }
    }

    given("meter reading with technical details") {
        val request = submitMeterReadingsRequest {
            meterPointId = ONE_LONG
            meterReadingWithTechnicalDetails = readingWithDetailsProto
        }

        and("and corresponding junifer mapping") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, METER_POINT_CORE_JUNIFER_ID, "1")

            and("call to junifer succeed") {
                coJustRun {
                    mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithTechnicalDetails
                    )
                }

                `when`("call submitMeterReadings") {
                    val result = assetsGrpcService.submitMeterReadings(request)

                    then("reading is submitted to Junifer") {
                        result.validationErrorsCount shouldBe 0
                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithTechnicalDetails
                            )
                        }
                    }
                }
            }

            and("call is validate only") {
                val validateOnlyRequest = submitMeterReadingsRequest {
                    meterPointId = ONE_LONG
                    meterReadingWithTechnicalDetails = readingWithDetailsValidateOnlyProto
                }

                coJustRun {
                    mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        validateOnlyRequest.meterReadingWithTechnicalDetails
                    )
                }


                `when`("call submitMeterReadings") {
                    val result = assetsGrpcService.submitMeterReadings(validateOnlyRequest)

                    then("reading is validated only") {
                        result.validationErrorsCount shouldBe 0
                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                eq(readingWithDetailsValidateOnlyProto)
                            )
                        }
                    }
                }
            }

            and("call to junifer returns 400 BadRequest") {
                coEvery {
                    mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithTechnicalDetails
                    )
                } throws JuniferSubmitMeterReadingError(submitMeterReadingValidationErrors)

                `when`("call submitMeterReadings") {
                    val result = assetsGrpcService.submitMeterReadings(request)

                    then("return response with validation errors") {
                        result shouldNotBe null
                        result.validationErrorsCount shouldBe 1

                        val validationError = result.validationErrorsList[0]
                        validationError.meterIdentifier shouldBe submitMeterReadingValidationError.meterIdentifier
                        validationError.registerIdentifier shouldBe submitMeterReadingValidationError.registerIdentifier
                        validationError.errorDetail.errorCode shouldBe submitMeterReadingValidationError.errorDetail.errorCode
                        validationError.errorDetail.errorSeverity shouldBe submitMeterReadingValidationError.errorDetail.errorSeverity
                        validationError.errorDetail.errorDescription.value shouldBe submitMeterReadingValidationError.errorDetail.errorDescription

                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithTechnicalDetails
                            )
                        }
                    }
                }
            }

            and("call fails with internal error") {
                coEvery {
                    mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithTechnicalDetails
                    )
                } throws RuntimeException()

                `when`("call submitMeterReadings") {
                    then("throw InternalGrpcException") {
                        assertThrows<UnknownGrpcException> { assetsGrpcService.submitMeterReadings(request) }
                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithTechnicalDetails
                            )
                        }
                    }
                }
            }
        }
    }

    given("meter reading without technical details") {
        val request = submitMeterReadingsRequest {
            meterPointId = ONE_LONG
            meterReadingWithoutTechnicalDetails = readingWithoutDetailsProto
        }

        and("and corresponding junifer mapping") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, METER_POINT_CORE_JUNIFER_ID, "1")

            and("call to junifer succeed") {
                coEvery {
                    mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithoutTechnicalDetails
                    )
                } returns Unit

                `when`("call submitMeterReadings") {
                    val result = assetsGrpcService.submitMeterReadings(request)
                    then("reading is submitted to Junifer") {
                        result.validationErrorsCount shouldBe 0

                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithoutTechnicalDetails
                            )
                        }
                    }
                }
            }

            and("call to junifer fails with junifer exception") {
                and("error code is NotFound") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        NOT_FOUND,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {

                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return notFound on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorNotFound
                        }
                    }
                }

                and("error code is InvalidSource") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        INVALID_SOURCE,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {
                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return invalid source on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorInvalidSource
                        }
                    }
                }

                and("error code is unknown") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        TEST_STRING,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {
                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return corresponding code on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorUnknownCode
                        }
                    }
                }

                and("error code is InvalidQuality") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        INVALID_QUALITY,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {
                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return invalid quality on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorInvalidQuality
                        }
                    }
                }

                and("error code is MTDsHaveArrived") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        MTDS_HAVE_ARRIVED,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {
                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return MTDsHaveArrived on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorMTDsHaveArrived
                        }
                    }
                }

                and("error code is ReadingDtBeforeSSD") {
                    coEvery {
                        mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                            METER_POINT_CORE_JUNIFER_ID,
                            request.meterReadingWithoutTechnicalDetails
                        )
                    } throws JuniferException(
                        READING_DATE_BEFORE_SSD,
                        TEST_STRING,
                        TEST_STRING,
                    )

                    `when`("call submitMeterReadings") {
                        val submitMeterReadingsResponse = assetsGrpcService.submitMeterReadings(request)

                        then("return ReadingDtBeforeSSD on error response") {
                            coVerify {
                                mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                    METER_POINT_CORE_JUNIFER_ID,
                                    request.meterReadingWithoutTechnicalDetails
                                )
                            }
                            submitMeterReadingsResponse.validationErrorsCount shouldBe 1
                            submitMeterReadingsResponse.validationErrorsList[0] shouldBe submitMeterReadingValidationErrorReadingDtBeforeSSD
                        }
                    }
                }
            }

            and("call to junifer fails with IllegalArgumentException") {
                coEvery {
                    mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithoutTechnicalDetails
                    )
                } throws IllegalArgumentException()

                `when`("call submitMeterReadings") {
                    then("throw InvalidArgumentGrpcException") {
                        assertThrows<InvalidArgumentGrpcException> { assetsGrpcService.submitMeterReadings(request) }
                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithoutTechnicalDetails
                            )
                        }
                    }
                }
            }

            and("call to junifer fails with unhandled exception") {
                coEvery {
                    mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                        METER_POINT_CORE_JUNIFER_ID,
                        request.meterReadingWithoutTechnicalDetails
                    )
                } throws Exception()

                `when`("call submitMeterReadings") {
                    then("throw UnknownGrpcException") {
                        assertThrows<UnknownGrpcException> { assetsGrpcService.submitMeterReadings(request) }
                        coVerify {
                            mockJuniferAssetsService.submitMeterReadingsWithoutTechnicalDetails(
                                METER_POINT_CORE_JUNIFER_ID,
                                request.meterReadingWithoutTechnicalDetails
                            )
                        }
                    }
                }
            }
        }
    }

    given("cancel meter reading registration request") {
        val id = "1"
        val request = cancelMeterPointRegistrationRequest {
            meterPointId = id
            reason = "reason"
            communicationFl = false
        }

        and("and corresponding junifer mapping") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, METER_POINT_CORE_JUNIFER_ID, id)

            and("call to junifer succeed") {
                coEvery {
                    mockJuniferAssetsService.cancelMeterPointRegistration(
                        METER_POINT_CORE_JUNIFER_ID,
                        request
                    )
                } returns Unit

                `when`("cancel meter point registration") {
                    assetsGrpcService.cancelMeterPointRegistration(request)

                    then("meter point registration is cancelled") {
                        coVerify {
                            mockJuniferAssetsService.cancelMeterPointRegistration(
                                METER_POINT_CORE_JUNIFER_ID,
                                request
                            )
                        }
                    }
                }
            }

            and("call to junifer fails with junifer exception") {
                coEvery {
                    mockJuniferAssetsService.cancelMeterPointRegistration(
                        METER_POINT_CORE_JUNIFER_ID,
                        request
                    )
                } throws JuniferException("InvalidQuality", TEST_STRING, TEST_STRING)

                `when`("cancel meter point registration") {

                    then("throw InvalidArgumentGrpcException") {
                        assertThrows<InvalidArgumentGrpcException> {
                            assetsGrpcService.cancelMeterPointRegistration(request)
                        }
                        coVerify {
                            mockJuniferAssetsService.cancelMeterPointRegistration(
                                METER_POINT_CORE_JUNIFER_ID,
                                request
                            )
                        }
                    }
                }
            }

            and("call fails with internal error") {
                coEvery {
                    mockJuniferAssetsService.cancelMeterPointRegistration(
                        METER_POINT_CORE_JUNIFER_ID,
                        request
                    )
                } throws Exception()

                `when`("cancel meter point registration") {

                    then("throw UnknownGrpcException") {
                        assertThrows<UnknownGrpcException> { assetsGrpcService.cancelMeterPointRegistration(request) }
                        coVerify {
                            mockJuniferAssetsService.cancelMeterPointRegistration(
                                METER_POINT_CORE_JUNIFER_ID,
                                request
                            )
                        }
                    }
                }
            }
        }
        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("cancel meter point registration") {

                then("throw FailedPreconditionGrpcException") {
                    assertThrows<FailedPreconditionGrpcException> {
                        assetsGrpcService.cancelMeterPointRegistration(request)
                    }

                    coVerify(exactly = 0) {
                        mockJuniferAssetsService.cancelMeterPointRegistration(
                            METER_POINT_CORE_JUNIFER_ID, request
                        )
                    }
                }
            }
        }
    }

    given("get meter point structure request") {
        and("no meterpoint mapping exists") {
            `when`("get meterpoint structure") {
                then("throw IllegalStateException") {
                    shouldThrow<IllegalStateException> {
                        assetsGrpcService.getMeterPointStructure(
                            meterPointStructureRequest
                        )
                    }
                }
            }
        }

        and("meterpoint structure has internal mapping") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT.toString(),
                meterPointStructureRequest.id.toString()
            )
            and("corresponding junifer response not found in cache") {
                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                coEvery {
                    mockJuniferAssetsService.getMeterPointStructure(
                        JUNIFER_METER_POINT.toString(),
                        meterPointStructureRequest
                    )
                } returns meterPointStructure
                `when`("get meter point structure ") {
                    assetsGrpcService.getMeterPointStructure(meterPointStructureRequest)
                    then("meter point structure retrieved from Junifer and saved in cache") {
                        inMemoryJuniferCache.getCachedResponse(
                            EndpointIdentifier.GET_METER_POINT_STRUCTURE,
                            encodedMeterPointStructureRequestParams
                        ) shouldBe cachedJuniferMeterPointStructureResponse
                        coVerify {
                            mockJuniferAssetsService.getMeterPointStructure(
                                JUNIFER_METER_POINT.toString(),
                                meterPointStructureRequest
                            )
                        }
                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                    }
                }
            }

            and("corresponding junifer response found in cache") {
                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                inMemoryJuniferCache.cacheResponse(
                    EndpointIdentifier.GET_METER_POINT_STRUCTURE,
                    encodedMeterPointStructureRequestParams,
                    cachedJuniferMeterPointStructureResponse,
                    juniferCacheConfig.getMeterPointStructureExpirationTime
                )
                `when`("get meter point structure") {
                    assetsGrpcService.getMeterPointStructure(meterPointStructureRequest)
                    then("meter point retrieved from cache") {
                        coVerify(exactly = 0) {
                            mockJuniferAssetsService.getMeterPointStructure(
                                JUNIFER_METER_POINT.toString(),
                                meterPointStructureRequest
                            )
                        }
                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                    }
                }
            }
        }
    }

    given("::searchMpan") {
        and("request is valid and there is a mpan for the given parameters") {
            val request = searchMpanRequest
            coEvery { mockJuniferAssetsService.searchMpan(request) } returns searchMpanResponseDto
            `when`("search mpan") {
                val response = assetsGrpcService.searchMpan(request)
                then("response should match expectations") {
                    response shouldBe searchMpanResponseProto
                    coVerify { mockJuniferAssetsService.searchMpan(request) }
                }
            }
        }
    }

    given("::searchMprn") {
        and("request is valid and there is a mprn for the given parameters") {
            val request = MeterPointStructureData.searchMprnRequest
            coEvery { mockJuniferAssetsService.searchMprn(request) } returns searchMprnResponseDto
            `when`("search mprn") {
                val response = assetsGrpcService.searchMprn(request)
                then("response should match expectations") {
                    response shouldBe searchMprnResponseProto
                    coVerify { mockJuniferAssetsService.searchMprn(request) }
                }
            }
        }
    }

    given("::getEstimatedUsage") {
        and("corresponding junifer response not found in cache") {
            and("request is valid and estimated usage is fetched") {

                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true

                inMemoryMapper.createCoreMapping(
                    EntityIdentifier.METER_POINT,
                    JUNIFER_METER_POINT_ID.toString(),
                    estimatedUsageRequestData.meterPointId.toString()
                )

                coEvery {
                    mockJuniferAssetsService.getEstimatedUsage(
                        JUNIFER_METER_POINT_ID,
                        estimatedUsageRequestData
                    )
                } returns juniferEstimatedUsageResponse

                `when`("get estimated usage") {
                    val response = assetsGrpcService.getEstimatedUsage(estimatedUsageRequestData)
                    then("response should match and should be saved to cache") {
                        response shouldBe juniferEstimatedUsageResponse.toResponse()
                        inMemoryJuniferCache.getCachedResponse(
                            EndpointIdentifier.GET_ESTIMATED_USAGE,
                            encodedEstimatedUsageRequestParams
                        ) shouldBe cachedJuniferEstimatedUsageResponse
                        coVerify {
                            mockJuniferAssetsService.getEstimatedUsage(
                                JUNIFER_METER_POINT_ID,
                                estimatedUsageRequestData
                            )
                        }
                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                    }
                }
            }

            and("request is valid but no estimated usage is fetched") {
                inMemoryMapper.createCoreMapping(
                    EntityIdentifier.METER_POINT,
                    JUNIFER_METER_POINT_ID.toString(),
                    estimatedUsageRequestData.meterPointId.toString()
                )

                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                coEvery {
                    mockJuniferAssetsService.getEstimatedUsage(
                        JUNIFER_METER_POINT_ID,
                        estimatedUsageRequestData
                    )
                } returns JuniferEstimatedUsageResponse()

                `when`("get estimated usage") {
                    val response = assetsGrpcService.getEstimatedUsage(estimatedUsageRequestData)
                    then("response should match and should be saved to cache") {
                        response shouldBe estimatedUsage { }
                        inMemoryJuniferCache.getCachedResponse(
                            EndpointIdentifier.GET_ESTIMATED_USAGE,
                            encodedEstimatedUsageRequestParams
                        ) shouldBe "{}"
                        coVerify {
                            mockJuniferAssetsService.getEstimatedUsage(
                                JUNIFER_METER_POINT_ID,
                                estimatedUsageRequestData
                            )
                        }
                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                    }
                }
            }
        }

        and("corresponding junifer response found in cache") {

            coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true

            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT_ID.toString(),
                estimatedUsageRequestData.meterPointId.toString()
            )
            inMemoryJuniferCache.cacheResponse(
                EndpointIdentifier.GET_ESTIMATED_USAGE,
                encodedEstimatedUsageRequestParams,
                cachedJuniferEstimatedUsageResponse,
                juniferCacheConfig.getEstimatedUsageExpirationTime
            )
            `when`("get estimated usage") {
                val response = assetsGrpcService.getEstimatedUsage(estimatedUsageRequestData)
                then("response should be retrieved from cache") {
                    response shouldBe juniferEstimatedUsageResponse.toResponse()
                    coVerify(exactly = 0) {
                        mockJuniferAssetsService.getEstimatedUsage(
                            JUNIFER_METER_POINT_ID,
                            estimatedUsageRequestData
                        )
                    }
                    coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                }
            }
        }
    }

    given("::getMeterReadings") {
        and("meterpoint has internal mapping") {
            and("feature TMP_SO_22516_USE_GCMDS_API is disabled") {
                and("corresponding junifer response not found in cache") {
                    inMemoryMapper.createCoreMapping(
                        EntityIdentifier.METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        getMeterReadingsRequest.meterpointId.toString()
                    )
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns false
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns false

                    coEvery {
                        mockJuniferAssetsService.getMeterReadings(
                            JUNIFER_METER_POINT_ID,
                            getMeterReadingsRequest.fromDt.toLocalDate(),
                            getMeterReadingsRequest.toDt.toLocalDate(),
                            getMeterReadingsRequest.statusList
                        )
                    } returns meterReadingsReponse

                    `when`("get meter readings") {
                        val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequest)

                        then("meter readings retrieved from Junifer and saved to cache") {
                            response shouldBe meterReadingsReponse.toResponse(getMeterReadingsRequest.meterpointId)
                            inMemoryJuniferCache.getCachedResponse(
                                EndpointIdentifier.GET_METER_READINGS,
                                encodedMeterReadingsRequestParams
                            ) shouldBe encodedGetMeterReadingsResponse
                            coVerify {
                                mockJuniferAssetsService.getMeterReadings(
                                    JUNIFER_METER_POINT_ID,
                                    getMeterReadingsRequest.fromDt.toLocalDate(),
                                    getMeterReadingsRequest.toDt.toLocalDate(),
                                    getMeterReadingsRequest.statusList
                                )
                            }
                            coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                            coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                        }
                    }
                }

                and("corresponding junifer response found in cache") {

                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                    coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns false

                    inMemoryMapper.createCoreMapping(
                        EntityIdentifier.METER_POINT,
                        JUNIFER_METER_POINT_ID.toString(),
                        getMeterReadingsRequest.meterpointId.toString()
                    )
                    inMemoryJuniferCache.cacheResponse(
                        EndpointIdentifier.GET_METER_READINGS,
                        encodedMeterReadingsRequestParams,
                        encodedGetMeterReadingsResponse,
                        juniferCacheConfig.getMeterReadingsExpirationTime
                    )

                    `when`("get meter readings") {
                        val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequest)

                        then("meter readings retrieved from cache") {
                            response shouldBe meterReadingsReponse.toResponse(getMeterReadingsRequest.meterpointId)
                            coVerify(exactly = 0) {
                                mockJuniferAssetsService.getMeterReadings(
                                    JUNIFER_METER_POINT_ID,
                                    getMeterReadingsRequest.fromDt.toLocalDate(),
                                    getMeterReadingsRequest.toDt.toLocalDate(),
                                    getMeterReadingsRequest.statusList
                                )
                            }
                            coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                            coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                        }
                    }
                }
            }

            and("feature TMP_SO_22516_USE_GCMDS_API is enabled") {
                and("reading frequency is not HH or DAILY") {
                    and("corresponding junifer response not found in cache") {

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequest.meterpointId.toString()
                        )
                        coEvery {
                            mockJuniferAssetsService.getMeterReadings(
                                JUNIFER_METER_POINT_ID,
                                getMeterReadingsRequest.fromDt.toLocalDate(),
                                getMeterReadingsRequest.toDt.toLocalDate(),
                                getMeterReadingsRequest.statusList
                            )
                        } returns meterReadingsReponse

                        `when`("get meter readings") {
                            val response = assetsGrpcService.getMeterReadings(
                                getMeterReadingsRequest.copy {
                                    readingFrequency = ReadFrequency.GREATER_THAN_DAILY
                                }
                            )

                            then("meter readings retrieved from Junifer and saved to cache") {
                                response shouldBe meterReadingsReponse.toResponse(getMeterReadingsRequest.meterpointId)
                                inMemoryJuniferCache.getCachedResponse(
                                    EndpointIdentifier.GET_METER_READINGS,
                                    encodedMeterReadingsRequestParams
                                ) shouldBe encodedGetMeterReadingsResponse
                                coVerify {
                                    mockJuniferAssetsService.getMeterReadings(
                                        JUNIFER_METER_POINT_ID,
                                        getMeterReadingsRequest.fromDt.toLocalDate(),
                                        getMeterReadingsRequest.toDt.toLocalDate(),
                                        getMeterReadingsRequest.statusList
                                    )
                                }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                            }
                        }
                    }

                    and("corresponding junifer response found in cache") {

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequest.meterpointId.toString()
                        )
                        inMemoryJuniferCache.cacheResponse(
                            EndpointIdentifier.GET_METER_READINGS,
                            encodedMeterReadingsRequestParams,
                            encodedGetMeterReadingsResponse,
                            juniferCacheConfig.getMeterReadingsExpirationTime
                        )

                        `when`("get meter readings") {
                            val response = assetsGrpcService.getMeterReadings(
                                getMeterReadingsRequest.copy {
                                    readingFrequency = ReadFrequency.GREATER_THAN_DAILY
                                }
                            )

                            then("meter readings retrieved from cache") {
                                response shouldBe meterReadingsReponse.toResponse(getMeterReadingsRequest.meterpointId)
                                coVerify(exactly = 0) {
                                    mockJuniferAssetsService.getMeterReadings(
                                        JUNIFER_METER_POINT_ID,
                                        getMeterReadingsRequest.fromDt.toLocalDate(),
                                        getMeterReadingsRequest.toDt.toLocalDate(),
                                        getMeterReadingsRequest.statusList
                                    )
                                }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                            }
                        }
                    }
                }

                and("reading frequency is DAILY") {
                    and("corresponding junifer response not found in cache") {

                        val getMeterReadingsRequestDaily = getMeterReadingsRequest.copy {
                            readingFrequency = ReadFrequency.DAILY
                        }

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequestDaily.meterpointId.toString()
                        )

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.REGISTER,
                            JUNIFER_REGISTER_ID.toString(),
                            REGISTER_ID.toString(),
                        )

                        every {
                            mockJuniferMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID)
                        } returns testJuniferMeterPoint

                        every {
                            mockJuniferMeterPointRepository.getAssetType(JUNIFER_METER_POINT_ID)
                        } returns testJuniferMpanAsset

                        every {
                            mockJuniferMeterPointRepository.findRegister(
                                testJuniferMeterPoint.id!!,
                                "1",
                            )
                        } returns testJuniferRegister

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns true

                        val request = ListMeterReadingsRequest(
                            marketType = GCMDSMarketType.MPAN,
                            meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                            start = getMeterReadingsRequestDaily.fromDt.toOffsetDateTime(),
                            end = getMeterReadingsRequestDaily.toDt.toOffsetDateTime(),
                            order = GCMDSOrder.DESC,
                            limit = 100
                        )

                        coEvery {
                            mockGCMDSDatasource.getMeterReadings(request)
                        } returns Json.decodeFromString<GCMDSListMeterReadingsResponse>(
                            loadTestJson(
                                getResource(
                                    LIST_READINGS_RESPONSE_JSON
                                )
                            )
                        ).readings


                        `when`("get meter readings") {
                            val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequestDaily)

                            then("meter readings retrieved from Junifer and saved to cache") {
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                                coVerify { mockGCMDSDatasource.getMeterReadings(request) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                                verify { mockJuniferMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                                verify { mockJuniferMeterPointRepository.getAssetType(JUNIFER_METER_POINT_ID) }
                                verify {
                                    mockJuniferMeterPointRepository.findRegister(
                                        testJuniferMeterPoint.id!!,
                                        "1",
                                    )
                                }
                                response shouldBe meterReadingsGCMDSReponse.toResponse(getMeterReadingsRequestDaily.meterpointId)
                            }
                        }
                    }

                    and("corresponding junifer response found in cache") {

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns true

                        val getMeterReadingsRequestDaily = getMeterReadingsRequest.copy {
                            readingFrequency = ReadFrequency.DAILY
                        }

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequestDaily.meterpointId.toString()
                        )
                        inMemoryJuniferCache.cacheResponse(
                            EndpointIdentifier.GET_GCMDS_METER_READINGS,
                            encodedDailyMeterReadingsRequestParams,
                            encodedGCMDSMeterReadingsResponse,
                            juniferCacheConfig.getMeterReadingsExpirationTime
                        )

                        `when`("get meter readings") {
                            val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequestDaily)

                            then("meter readings retrieved from cache") {
                                response shouldBe meterReadingsGCMDSReponse.toResponse(getMeterReadingsRequestDaily.meterpointId)
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                                coVerify(exactly = 0) {
                                    mockJuniferAssetsService.getMeterReadings(
                                        JUNIFER_METER_POINT_ID,
                                        getMeterReadingsRequestDaily.fromDt.toLocalDate(),
                                        getMeterReadingsRequestDaily.toDt.toLocalDate(),
                                        getMeterReadingsRequestDaily.statusList
                                    )
                                }
                            }
                        }
                    }
                }

                and("reading frequency is HALF_HOURLY") {

                    val from = OffsetDateTime.of(2025, 2, 1, 0, 0, 0, 0, ZoneOffset.UTC)
                    val to = OffsetDateTime.of(2025, 2, 2, 0, 0, 0, 0, ZoneOffset.UTC)

                    and("corresponding junifer response not found in cache") {

                        inMemoryJuniferCache.clear()

                        val getMeterReadingsRequestHH = getMeterReadingsRequest.copy {
                            readingFrequency = ReadFrequency.HALF_HOURLY
                            fromDt = from.toTimestamp()
                            toDt = to.toTimestamp()
                            order = energy.so.ac.junifer.v1.assets.GCMDSOrder.ASC
                        }

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequestHH.meterpointId.toString()
                        )

                        every {
                            mockJuniferMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID)
                        } returns testJuniferMeterPoint

                        every {
                            mockJuniferMeterPointRepository.getAssetType(JUNIFER_METER_POINT_ID)
                        } returns testJuniferMpanAsset

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns true

                        val request = GetTimeSeriesRequest(
                            marketType = GCMDSMarketType.MPAN,
                            meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                            start = from,
                            end = to,
                            resolution = GCMDSResolution.HALF_HOUR,
                            uom = GCMDSUnitOfMeasure.KWH,
                        )

                        every {
                            mockGCMDSDatasource.getTimeSeries(request)
                        } returns ResponseEntity.of(
                            Optional.of(
                                Json.decodeFromString<GCMDSTimeSeriesResponse>(
                                    loadTestJson(getResource(TIME_SERIES_RESPONSE_JSON))
                                )
                            )
                        )

                        `when`("get meter readings") {

                            val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequestHH)

                            then("meter readings retrieved from Junifer and saved to cache") {

                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                                verify { mockJuniferMeterPointRepository.getMeterPoint(JUNIFER_METER_POINT_ID) }
                                verify { mockJuniferMeterPointRepository.getAssetType(JUNIFER_METER_POINT_ID) }
                                verify { mockGCMDSDatasource.getTimeSeries(request) }

                                response.readingsCount shouldBe 47 // As one reading is null, this should be 48 - 1
                                val reading0 = response.readingsList[0]
                                reading0.id shouldBe -1
                                reading0.readingDttm shouldBe from.plusMinutes(30).toTimestamp()
                                reading0.status shouldBe "Accepted"
                                reading0.sequenceType shouldBe "Normal"
                                reading0.source shouldBe "GCMDS"
                                reading0.quality shouldBe "Normal"
                                reading0.consumption shouldBe 0.052
                                reading0.cumulative shouldBe 0.0
                                reading0.unit shouldBe "kWh"
                                reading0.receivedDttm shouldBe from.plusMinutes(30).toTimestamp()
                                reading0.fromDttm shouldBe from.plusMinutes(30).toTimestamp()

                                val reading47 = response.readingsList[46]
                                reading47.id shouldBe -1
                                reading47.readingDttm shouldBe to.toTimestamp()
                                reading47.status shouldBe "Accepted"
                                reading47.sequenceType shouldBe "Normal"
                                reading47.source shouldBe "GCMDS"
                                reading47.quality shouldBe "Normal"
                                reading47.consumption shouldBe 0.05
                                reading47.cumulative shouldBe 0.0
                                reading47.unit shouldBe "kWh"
                                reading47.receivedDttm shouldBe to.toTimestamp()
                                reading47.fromDttm shouldBe to.toTimestamp()
                            }
                        }
                    }

                    and("corresponding junifer response found in cache") {

                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) } returns true
                        coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) } returns true

                        val getMeterReadingsRequestHH = getMeterReadingsRequest.copy {
                            readingFrequency = ReadFrequency.HALF_HOURLY
                            fromDt = from.toTimestamp()
                            toDt = to.toTimestamp()
                            order = energy.so.ac.junifer.v1.assets.GCMDSOrder.ASC
                        }

                        inMemoryMapper.createCoreMapping(
                            EntityIdentifier.METER_POINT,
                            JUNIFER_METER_POINT_ID.toString(),
                            getMeterReadingsRequestHH.meterpointId.toString()
                        )

                        inMemoryJuniferCache.cacheResponse(
                            EndpointIdentifier.GET_GCMDS_METER_READINGS,
                            encodedHHMeterReadingsRequestParams,
                            loadTestJson(getResource(ENCODED_TIME_SERIES_RESPONSE_JSON)),
                            juniferCacheConfig.getMeterReadingsExpirationTime
                        )

                        `when`("get meter readings") {
                            val response = assetsGrpcService.getMeterReadings(getMeterReadingsRequestHH)

                            then("meter readings retrieved from cache") {
                                response.readingsCount shouldBe 47 // As one reading is null, this should be 48 - 1
                                val reading0 = response.readingsList[0]
                                reading0.id shouldBe -1
                                reading0.readingDttm shouldBe from.plusMinutes(30).toTimestamp()
                                reading0.status shouldBe "Accepted"
                                reading0.sequenceType shouldBe "Normal"
                                reading0.source shouldBe "GCMDS"
                                reading0.quality shouldBe "Normal"
                                reading0.consumption shouldBe 0.052
                                reading0.cumulative shouldBe 0.0
                                reading0.unit shouldBe "kWh"
                                reading0.receivedDttm shouldBe from.plusMinutes(30).toTimestamp()
                                reading0.fromDttm shouldBe from.plusMinutes(30).toTimestamp()

                                val reading47 = response.readingsList[46]
                                reading47.id shouldBe -1
                                reading47.readingDttm shouldBe to.toTimestamp()
                                reading47.status shouldBe "Accepted"
                                reading47.sequenceType shouldBe "Normal"
                                reading47.source shouldBe "GCMDS"
                                reading47.quality shouldBe "Normal"
                                reading47.consumption shouldBe 0.05
                                reading47.cumulative shouldBe 0.0
                                reading47.unit shouldBe "kWh"
                                reading47.receivedDttm shouldBe to.toTimestamp()
                                reading47.fromDttm shouldBe to.toTimestamp()

                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_GCMDS_CACHE) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_22516_USE_GCMDS_API) }
                                coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_24488_METERS_ENDPOINT_PAGINATION) }
                                verify(exactly = 0) {
                                    mockGCMDSDatasource.getTimeSeries(
                                        GetTimeSeriesRequest(
                                            marketType = GCMDSMarketType.MPAN,
                                            meterPointIdentifier = testJuniferMeterPoint.identifier!!,
                                            start = from,
                                            end = to,
                                            resolution = GCMDSResolution.HALF_HOUR,
                                            uom = GCMDSUnitOfMeasure.KWH,
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    given("::getMeterIdToRegisters") {
        and("meterpoint structure has internal mapping for all elements") {

            val coreMeterId = 2023
            val coreRegisterId = 2024

            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                JUNIFER_METER_POINT.toString(),
                meterPointStructureRequest.id.toString()
            )
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER,
                ID.toString(),
                coreMeterId.toString()
            )
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.REGISTER,
                ID.toString(),
                coreRegisterId.toString()
            )

            coEvery {
                mockJuniferAssetsService.getMeterPointStructure(
                    JUNIFER_METER_POINT.toString(),
                    meterPointStructureRequest
                )
            } returns meterPointStructure

            and("corresponding junifer response not found in cache") {

                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true

                `when`("get meter id to registers") {
                    val response = assetsGrpcService.getMeterIdToRegisters(idRequest { id = JUNIFER_METER_POINT })

                    then("response should be retrieved from junifer and saved to cache") {

                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }

                        response shouldNotBe null
                        response.responseCount shouldBe 1

                        val meterToRegisters = response.responseList[0]
                        meterToRegisters.meterId shouldBe coreMeterId
                        meterToRegisters.registersCount shouldBe 1

                        val register = meterToRegisters.registersList[0]
                        register.id shouldBe coreRegisterId
                        register.identifier.value shouldBe meterPointStructureRegister.identifier
                        register.digits shouldBe meterPointStructureRegister.digits
                        register.decimalPlaces shouldBe meterPointStructureRegister.decimalPlaces
                        register.registerType.value shouldBe meterPointStructureRegister.registerType
                        register.rateName.value shouldBe meterPointStructureRegister.rateName

                        coVerify {
                            mockJuniferAssetsService.getMeterPointStructure(
                                JUNIFER_METER_POINT.toString(),
                                meterPointStructureRequest
                            )
                        }
                        inMemoryJuniferCache.getCachedResponse(
                            EndpointIdentifier.GET_METER_ID_TO_REGISTERS,
                            encodedMeterPointStructureRequestParams
                        ) shouldBe cachedJuniferMeterPointStructureResponse
                    }
                }
            }

            and("corresponding junifer response found in cache") {

                coEvery { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) } returns true

                inMemoryJuniferCache.cacheResponse(
                    EndpointIdentifier.GET_METER_ID_TO_REGISTERS,
                    encodedMeterPointStructureRequestParams,
                    cachedJuniferMeterPointStructureResponse,
                    juniferCacheConfig.getMeterIdToRegistersExpirationTime
                )
                `when`("get meter id to registers") {
                    val response = assetsGrpcService.getMeterIdToRegisters(idRequest { id = JUNIFER_METER_POINT })
                    then("response should be retrieved from cache") {

                        coVerify { mockFeatureService.isFeatureEnabled(FeatureName.TMP_SO_15966_JUNIFER_CACHE) }

                        response shouldNotBe null
                        response.responseCount shouldBe 1

                        val meterToRegisters = response.responseList[0]
                        meterToRegisters.meterId shouldBe coreMeterId
                        meterToRegisters.registersCount shouldBe 1

                        val register = meterToRegisters.registersList[0]
                        register.id shouldBe coreRegisterId
                        register.identifier.value shouldBe meterPointStructureRegister.identifier
                        register.digits shouldBe meterPointStructureRegister.digits
                        register.decimalPlaces shouldBe meterPointStructureRegister.decimalPlaces
                        register.registerType.value shouldBe meterPointStructureRegister.registerType
                        register.rateName.value shouldBe meterPointStructureRegister.rateName

                        coVerify(exactly = 0) {
                            mockJuniferAssetsService.getMeterPointStructure(
                                JUNIFER_METER_POINT.toString(),
                                meterPointStructureRequest
                            )
                        }
                    }
                }
            }
        }
    }

    given("::filterReads") {

        val read1 = meterReadingsGCMDSReponse.results?.get(0)
        val read2 = meterReadingsGCMDSReponse.results?.get(1)

        `when`("ordered in ascending order") {

            val filteredReads = assetsGrpcService.filterReads(
                meterReadingsGCMDSReponse.results!!.toList(),
                energy.so.ac.junifer.v1.assets.GCMDSOrder.ASC,
                null,
            )

            then("the reads should be ordered appropriately") {
                filteredReads.size shouldBe 2
                filteredReads[0] shouldBe read1
                filteredReads[1] shouldBe read2
            }
        }

        `when`("ordered in descending order") {

            val filteredReads = assetsGrpcService.filterReads(
                meterReadingsGCMDSReponse.results!!.toList(),
                energy.so.ac.junifer.v1.assets.GCMDSOrder.DESC,
                null,
            )

            then("the reads should be ordered appropriately") {
                filteredReads.size shouldBe 2
                filteredReads[0] shouldBe read2
                filteredReads[1] shouldBe read1
            }
        }

        `when`("limited") {

            val filteredReads = assetsGrpcService.filterReads(
                meterReadingsGCMDSReponse.results!!.toList(),
                null,
                1,
            )

            then("the reads should be limited appropriately") {
                filteredReads.size shouldBe 1
            }
        }

        `when`("ordered and limited") {

            val filteredReads = assetsGrpcService.filterReads(
                meterReadingsGCMDSReponse.results!!.toList(),
                energy.so.ac.junifer.v1.assets.GCMDSOrder.DESC,
                1,
            )

            then("the reads should be ordered and limited appropriately") {
                filteredReads.size shouldBe 1
                filteredReads[0] shouldBe read2
            }
        }
    }
})
