package energy.so.ac.junifer.ingress.services.accountAgreements

import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.getAccountAgreementsJuniferMockResponse
import energy.so.ac.junifer.fixtures.AccountAgreementsPrecannedData.juniferGetAccountAgreementsResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class HttpJuniferAccountAgreementsServiceTest : BehaviorSpec({

    val sut = HttpJuniferAccountAgreementsService(MockJuniferClient.juniferConfig, getMockHttpClient())

    given("junifer account id") {
        val juniferAccountId = "100"

        `when`("get account agreements") {
            val juniferResponse = sut.getAccountAgreements(juniferAccountId)

            then("agreements are returned") {
                juniferResponse.results shouldBe juniferGetAccountAgreementsResponse.results
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Get && request.url.fullPath.contains("agreements")) {
            respond(
                getAccountAgreementsJuniferMockResponse,
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}
