package energy.so.ac.junifer.ingress.services.changeoftenancy

import energy.so.ac.junifer.fixtures.ACCOUNT_ID
import energy.so.ac.junifer.fixtures.PRODUCT_ID
import energy.so.ac.junifer.fixtures.successChangeOfTenancyRequest
import energy.so.ac.junifer.fixtures.successChangeOfTenancyResponse
import energy.so.ac.junifer.fixtures.successJuniferChangeOfTenancyResponse
import energy.so.ac.junifer.fixtures.successJuniferMoveOutChangeOfTenancyResponse
import energy.so.ac.junifer.fixtures.successMoveOutCotRequestWithMultipleProducts
import energy.so.ac.junifer.fixtures.successMoveOutCotRequestWithOneProductEach
import energy.so.ac.junifer.fixtures.successMoveOutCotResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import energy.so.ac.junifer.ingress.services.changeOfTenancy.HttpJuniferChangeOfTenancyService
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class HttpJuniferChangeOfTenancyServiceTest : BehaviorSpec({
    val sut = HttpJuniferChangeOfTenancyService(MockJuniferClient.juniferConfig, getMockHttpClient())

    given("createChangeOfTenancy") {
        `when`("receive a valid create change of tenancy request") {
            val response = sut.createChangeOfTenancy(
                PRODUCT_ID,
                successChangeOfTenancyRequest
            )
            then("return a valid change of tenancy response") {
                response shouldBe successChangeOfTenancyResponse
            }
        }
    }

    given("moveOutChangeOfTenancyV2") {
        `when`("receive a valid move out change of tenancy request") {
            val response = sut.createMoveOutChangeOfTenancy(
                ACCOUNT_ID,
                successMoveOutCotRequestWithOneProductEach
            )
            then("return a valid move out change of tenancy response") {
                response shouldBe successMoveOutCotResponse
            }
        }

        `when`("receive a valid move out change of tenancy request with a list of productRefs") {
            val response = sut.createMoveOutChangeOfTenancy(
                ACCOUNT_ID,
                successMoveOutCotRequestWithMultipleProducts
            )
            then("return a valid move out change of tenancy response") {
                response shouldBe successMoveOutCotResponse
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Post && request.url.fullPath.contains("changeOfTenancys")) {
            respond(
                successJuniferChangeOfTenancyResponse,
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Post && request.url.fullPath.contains("moveOutChangeOfTenancy")) {
            respond(
                successJuniferMoveOutChangeOfTenancyResponse,
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}
