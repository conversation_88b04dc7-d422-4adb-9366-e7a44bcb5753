package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCreateAccountNoteRequestTest : BehaviorSpec({

    given("create account note request") {
        val request: CreateAccountNoteRequest = createAccountNoteRequest {
            accountId = "1"
            subject = "subject"
            type = NoteType.Note
            summary = "summary"
            content = "content"
        }

        `when`("map to junifer create account note request") {
            val juniferRequest = JuniferCreateAccountNoteRequest.fromCreateAccountNoteRequest(
                request
            )

            then("return a corresponding JuniferCreateAccountNoteRequest") {
                assertSoftly {
                    juniferRequest.subject shouldBe request.subject
                    juniferRequest.type shouldBe request.type.name
                    juniferRequest.summary shouldBe request.summary
                    juniferRequest.content shouldBe request.content
                }
            }
        }
    }
})
