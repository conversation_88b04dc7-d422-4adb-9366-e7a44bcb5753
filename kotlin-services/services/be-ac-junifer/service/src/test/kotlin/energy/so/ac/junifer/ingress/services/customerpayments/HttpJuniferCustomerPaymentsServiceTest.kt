package energy.so.ac.junifer.ingress.services.customerpayments

import energy.so.ac.junifer.fixtures.directDebitResponse
import energy.so.ac.junifer.fixtures.paymentMethodResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import energy.so.ac.junifer.v1.finances.createGoCardlessDirectDebitRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json

class HttpJuniferCustomerPaymentsServiceTest : BehaviorSpec({

    val sut = HttpJuniferCustomerPaymentsService(MockJuniferClient.juniferConfig, getMockHttpClient())

    given("valid go cardless direct debit id") {
        val juniferId = directDebitResponse.id.toString()

        `when`("get go cardless direct debit") {
            val response = sut.getGoCardlessDirectDebit(juniferId)

            then("response should contain data from junifer") {
                response shouldBe directDebitResponse
            }
        }
    }

    given("valid create go cardless direct debit data") {
        `when`("create go cardless direct debit") {
            val response = sut.createGoCardlessDirectDebit(createGoCardlessDirectDebitRequest {
                accountName = directDebitResponse.accountName
                accountNumber = directDebitResponse.accountNumber
                sortCode = directDebitResponse.sortCode
                accountId = "1"
                coreDirectDebitId = 12
                corePaymentMethodId = 12
            })

            then("response should contain data from junifer") {
                response shouldBe directDebitResponse.copy(mandateReference = null, bankReference = null)
            }
        }
    }

})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Post && request.url.fullPath.endsWith("goCardlessDirectDebits")) {
            respond(
                "{\n" +
                        "    \"id\": ${directDebitResponse.id},\n" +
                        "    \"accountName\": \"${directDebitResponse.accountName}\",\n" +
                        "    \"accountNumber\": \"${directDebitResponse.accountNumber}\",\n" +
                        "    \"sortCode\": \"${directDebitResponse.sortCode}\",\n" +
                        "    \"authRequestedDttm\": \"${directDebitResponse.authRequestedDttm}\",\n" +
                        "    \"authorisedDttm\": \"${directDebitResponse.authorisedDttm}\",\n" +
                        "    \"terminatedDttm\": \"${directDebitResponse.terminatedDttm}\",\n" +
                        "    \"links\": {\n" +
                        "       \"self\": \"http://127.0.0.1:43002/rest/v1/goCardlessDirectDebits/${directDebitResponse.id}\",\n" +
                        "       \"paymentMethod\": \"http://127.0.0.1:43002/rest/v1/paymentMethods/${paymentMethodResponse.id}\"\n" +
                        "    }\n" +
                        "}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.endsWith("/goCardlessDirectDebits/${directDebitResponse.id}")) {
            respond(
                "{\n" +
                        "    \"id\": ${directDebitResponse.id},\n" +
                        "    \"accountName\": \"${directDebitResponse.accountName}\",\n" +
                        "    \"accountNumber\": \"${directDebitResponse.accountNumber}\",\n" +
                        "    \"sortCode\": \"${directDebitResponse.sortCode}\",\n" +
                        "    \"bankReference\": \"${directDebitResponse.bankReference}\",\n" +
                        "    \"mandateReference\": \"${directDebitResponse.mandateReference}\",\n" +
                        "    \"authRequestedDttm\": \"${directDebitResponse.authRequestedDttm}\",\n" +
                        "    \"authorisedDttm\": \"${directDebitResponse.authorisedDttm}\",\n" +
                        "    \"terminatedDttm\": \"${directDebitResponse.terminatedDttm}\",\n" +
                        "    \"links\": {\n" +
                        "       \"self\": \"http://127.0.0.1:43002/rest/v1/goCardlessDirectDebits/${directDebitResponse.id}\",\n" +
                        "       \"paymentMethod\": \"http://127.0.0.1:43002/rest/v1/paymentMethods/${paymentMethodResponse.id}\"\n" +
                        "    }\n" +
                        "}",
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })

}
