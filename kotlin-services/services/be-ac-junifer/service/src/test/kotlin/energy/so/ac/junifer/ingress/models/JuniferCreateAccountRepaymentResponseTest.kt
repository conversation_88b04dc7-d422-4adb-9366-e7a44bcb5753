package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountRepaymentResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountRepaymentResponse
import energy.so.ac.junifer.ingress.models.accounts.toResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferCreateAccountRepaymentResponseTest : BehaviorSpec({

    given("junifer create account repayment response") {
        val juniferResponse = juniferCreateAccountRepaymentResponse

        `when`("map to create account repayment response") {
            val response = juniferResponse.toResponse()

            then("return a corresponding CreateAccountRepaymentResponse") {
                response shouldBe createAccountRepaymentResponse
            }
        }
    }
})
