package energy.so.ac.junifer.admin.database.repositories

import energy.so.ac.junifer.admin.job.atStartOfDay
import energy.so.ac.junifer.egress.database.repositories.getSql
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.LOCKED_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.OptimizedJobPrecannedData.NOT_LOCKED_ACCOUNT_ID
import energy.so.commons.database.SqlLoader
import energy.so.commons.extension.save
import energy.so.commons.model.tables.pojos.SynchronizedAccount
import energy.so.commons.model.tables.references.ATOMIC_FLAG
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNT
import energy.so.commons.model.tables.references.SYNCHRONIZED_ACCOUNTS
import energy.so.database.test.installDatabase
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDateTime
import java.time.OffsetDateTime
import org.jooq.DSLContext

const val CREATE_ACCOUNT_CONTACT_SQL = "/sql/customer/create_account_contact.sql"
const val CREATE_TODAY_ACCOUNT_SQL = "/sql/customer/create_today_account.sql"
const val CREATE_ACCOUNT_SQL = "/sql/customer/create_account.sql"
const val CREATE_SYNCHRONIZED_ACCOUNT_SQL = "/sql/account/create_synchronized_accounts.sql"

class JooqAccountRepositoryTest : BehaviorSpec({

    val processName = "PROCESS_NAME"
    val db = installDatabase()
    val sut = JooqAccountRepository(db)

    afterEach {
        db.truncateTable("junifer__account")
        db.truncateTable("synchronized_accounts")
        db.truncateTable("atomic_flag")
    }
    given("there is no account with given number in db") {
        val accountNumber = "********"
        `when`("get id by account number") {
            val response = sut.findIdByAccountNumber(accountNumber)
            then("response should match expected") {
                response shouldBe null
            }
        }
    }

    given("there is an account with given number in db") {
        val accountSql =
            SqlLoader.loadFromStream(getSql(CREATE_ACCOUNT_CONTACT_SQL))
        db.execute(accountSql)
        val expectedAccountId = 691791L
        val accountNumber = "********"
        `when`("get id by account number") {
            val response = sut.findIdByAccountNumber(accountNumber)
            then("response should match expected") {
                response shouldBe expectedAccountId
            }
        }
    }

    given("there is an account created today in db") {
        val expectedAccountId = 691792L
        val oldAccountSql =
            SqlLoader.loadFromStream(getSql(CREATE_ACCOUNT_SQL))
        db.execute(oldAccountSql)

        val todayAccountSql =
            SqlLoader.loadFromStream(getSql(CREATE_TODAY_ACCOUNT_SQL))
        db.execute(todayAccountSql)

        `when`("get id by created after") {
            val response = sut.findIdsByCreatedAtAfter(OffsetDateTime.now().atStartOfDay())
            then("response should match expected") {
                response.size shouldBe 1
                response[0] shouldBe expectedAccountId
            }
        }
    }

    given("::findClosedAccounts") {
        val accountsList = listOf(691792L)
        and("empty db") {
            `when`("call findClosedAccounts") {
                val closedAccounts = sut.findClosedAccounts(accountsList)

                then("return empty list") {
                    closedAccounts shouldBe emptyList()
                }
            }
        }

        and("no closed account in db") {
            val accountSql = SqlLoader.loadFromStream(getSql(CREATE_TODAY_ACCOUNT_SQL))
            db.execute(accountSql)

            `when`("call findClosedAccounts") {
                val closedAccounts = sut.findClosedAccounts(accountsList)

                then("return empty list") {
                    closedAccounts shouldBe emptyList()
                }
            }
        }

        and("closed account in db") {
            val accountSql = SqlLoader.loadFromStream(getSql(CREATE_TODAY_ACCOUNT_SQL))
            db.execute(accountSql)
            db.update(JUNIFER__ACCOUNT).set(JUNIFER__ACCOUNT.CLOSEDDTTM, OffsetDateTime.now()).execute()

            `when`("call findClosedAccounts") {
                val closedAccounts = sut.findClosedAccounts(accountsList)

                then("return list with that account") {
                    closedAccounts shouldBe accountsList
                }
            }
        }

        and("cancelled account in db") {
            val accountSql = SqlLoader.loadFromStream(getSql(CREATE_TODAY_ACCOUNT_SQL))
            db.execute(accountSql)
            db.update(JUNIFER__ACCOUNT).set(JUNIFER__ACCOUNT.CANCELLEDDTTM, OffsetDateTime.now()).execute()

            `when`("call findClosedAccounts") {
                val closedAccounts = sut.findClosedAccounts(accountsList)

                then("return list with that account") {
                    closedAccounts shouldBe accountsList
                }
            }
        }

        and("expired account in db") {
            val accountSql = SqlLoader.loadFromStream(getSql(CREATE_TODAY_ACCOUNT_SQL))
            db.execute(accountSql)
            db.update(JUNIFER__ACCOUNT).set(JUNIFER__ACCOUNT.TODTTM, OffsetDateTime.now()).execute()

            `when`("call findClosedAccounts") {
                val closedAccounts = sut.findClosedAccounts(accountsList)

                then("return list with that account") {
                    closedAccounts shouldBe accountsList
                }
            }
        }
    }

    given("::addAccountsToWhitelist") {
        val junifeToCoreIdsMap = mapOf(LOCKED_ACCOUNT_ID to LOCKED_ACCOUNT_ID)
        and("empty synchronized_accounts table") {
            val initialCount = countWhitelistedAccounts(db)
            `when`("call addAccountsToWhitelist") {
                sut.addAccountsToWhitelist(junifeToCoreIdsMap)

                then("account added to whitelist") {
                    assertSoftly {
                        initialCount shouldBe 0
                        countWhitelistedAccounts(db) shouldBe 1
                    }
                }
            }
        }

        and("entries in synchronized_accounts table") {
            insertSynchronizedAccount(db)
            val initialCount = countWhitelistedAccounts(db)

            `when`("call addAccountsToWhitelist") {
                sut.addAccountsToWhitelist(junifeToCoreIdsMap)

                then("account added to whitelist") {
                    assertSoftly {
                        initialCount shouldBe 1
                        countWhitelistedAccounts(db) shouldBe 2
                    }
                }
            }
        }
    }

    given("::deleteWhitelistedAccounts") {
        val idsList = listOf(NOT_LOCKED_ACCOUNT_ID)
        and("empty synchronized_accounts table") {
            val initialCount = countWhitelistedAccounts(db)

            `when`("call deleteWhitelistedAccounts") {
                val deletedCount = sut.deleteWhitelistedAccounts(1)

                then("no row deleted") {
                    initialCount shouldBe 0
                    deletedCount shouldBe 0
                }
            }
        }

        and("no given id in synchronized_accounts table") {
            insertSynchronizedAccount(db, LOCKED_ACCOUNT_ID)
            val initialCount = countWhitelistedAccounts(db)

            `when`("call deleteWhitelistedAccounts") {
                val deletedCount = sut.deleteWhitelistedAccounts(1, idsList)

                then("no row deleted") {
                    initialCount shouldBe 1
                    deletedCount shouldBe 0
                    countWhitelistedAccounts(db) shouldBe 1
                }
            }
        }

        and("matching given id in synchronized_accounts table") {
            insertSynchronizedAccount(db)
            val initialCount = countWhitelistedAccounts(db)

            `when`("call deleteWhitelistedAccounts") {
                val deletedCount = sut.deleteWhitelistedAccounts(1, idsList)

                then("no row deleted") {
                    initialCount shouldBe 1
                    deletedCount shouldBe 1
                    countWhitelistedAccounts(db) shouldBe 0
                }
            }
        }
    }

    given("::setOptimizedSyncRunningFlag") {
        and("no matching entry in db") {
            `when`("call setOptimizedSyncRunningFlag") {
                val updatedNo = sut.setOptimizedSyncRunningFlag(processName, true, 60)

                then("return 0") {
                    updatedNo shouldBe 0
                }
            }
        }

        and("no matching entry in db but configured period expired") {
            `when`("call setOptimizedSyncRunningFlag") {
                insertAtomicFlag(db, processName, true, LocalDateTime.now().minusDays(1))
                val updatedNo = sut.setOptimizedSyncRunningFlag(processName, true, 60)

                then("return 1") {
                    updatedNo shouldBe 1
                }
            }
        }

        and("matching entry in db within period") {
            insertAtomicFlag(db, processName, true)

            `when`("call setOptimizedSyncRunningFlag") {
                val updatedNo = sut.setOptimizedSyncRunningFlag(processName, false, 60)

                then("return 1") {
                    updatedNo shouldBe 1
                }
            }
        }
    }

    given("::isAccountAddedToWhitelist") {
        val juniferAccountId = 10L
        and("empty synchronized_accounts table") {
            `when`("call isAccountAddedToWhitelist") {
                val exists = sut.isAccountAddedToWhitelist(juniferAccountId)

                then("account does not exist to whitelist") {
                    assertSoftly {
                        exists shouldBe false
                    }
                }
            }
        }

        and("account exists in table") {
            val synchronizedAccountSql = SqlLoader.loadFromStream(getSql(CREATE_SYNCHRONIZED_ACCOUNT_SQL))
            db.execute(synchronizedAccountSql)
            `when`("call isAccountAddedToWhitelist") {
                val exists = sut.isAccountAddedToWhitelist(juniferAccountId)

                then("account added to whitelist") {
                    assertSoftly {
                        exists shouldBe true
                    }
                }
            }
        }
    }

    given("::addAccountToWhitelist") {
        val juniferAccountId = 1L
        and("empty synchronized_accounts table") {
            val initialCount = countWhitelistedAccounts(db)
            `when`("call addAccountToWhitelist") {
                sut.addAccountToWhitelist(juniferAccountId)

                then("account added to whitelist") {
                    assertSoftly {
                        initialCount shouldBe 0
                        countWhitelistedAccounts(db) shouldBe 1
                    }
                }
            }
        }

        and("entries in synchronized_accounts table") {
            insertSynchronizedAccount(db)
            val initialCount = countWhitelistedAccounts(db)

            `when`("call addAccountToWhitelist") {
                sut.addAccountToWhitelist(juniferAccountId)

                then("account added to whitelist") {
                    assertSoftly {
                        initialCount shouldBe 1
                        countWhitelistedAccounts(db) shouldBe 2
                    }
                }
            }
        }
    }

    given("::updateAccountId") {
        and("accountId is null") {
            val juniferAccountId = 30L
            val accountId = 100L
            val synchronizedAccountSql = SqlLoader.loadFromStream(getSql(CREATE_SYNCHRONIZED_ACCOUNT_SQL))
            db.execute(synchronizedAccountSql)
            `when`("call updateAccountId") {
                sut.updateAccountId(juniferAccountId, accountId)

                val result = db.selectFrom(SYNCHRONIZED_ACCOUNTS)
                    .where(SYNCHRONIZED_ACCOUNTS.JUNIFERACCOUNTID.eq(juniferAccountId))
                    .fetchInto(SynchronizedAccount::class.java)

                then("account should be updated") {
                    assertSoftly {
                        result.find { it.juniferaccountid == juniferAccountId }!!.accountid shouldBe accountId
                    }
                }
            }
        }

        and("accountId already exists and is not null") {
            val juniferAccountId = 20L
            val accountId = 2L
            val newAccountId = 100L
            val synchronizedAccountSql = SqlLoader.loadFromStream(getSql(CREATE_SYNCHRONIZED_ACCOUNT_SQL))
            db.execute(synchronizedAccountSql)
            `when`("call updateAccountId") {
                sut.updateAccountId(juniferAccountId, newAccountId)

                val result = db.selectFrom(SYNCHRONIZED_ACCOUNTS)
                    .where(SYNCHRONIZED_ACCOUNTS.JUNIFERACCOUNTID.eq(juniferAccountId))
                    .fetchInto(SynchronizedAccount::class.java)

                then("accountId is not updated") {
                    assertSoftly {
                        result.find { it.juniferaccountid == juniferAccountId }!!.accountid shouldBe accountId
                    }
                }
            }
        }
    }
})

private fun insertSynchronizedAccount(db: DSLContext, juniferId: Long? = null) {
    db.newRecord(
        SYNCHRONIZED_ACCOUNTS,
        SynchronizedAccount(null, NOT_LOCKED_ACCOUNT_ID, juniferId ?: NOT_LOCKED_ACCOUNT_ID, false)
    ).apply { save() }
}

private fun insertAtomicFlag(
    db: DSLContext,
    processName: String,
    running: Boolean,
    updatedAt: LocalDateTime? = LocalDateTime.now(),
) {
    db.insertInto(ATOMIC_FLAG, ATOMIC_FLAG.PROCESS_NAME, ATOMIC_FLAG.RUNNING, ATOMIC_FLAG.UPDATED_AT)
        .values(processName, running, updatedAt).execute()
}

private fun countWhitelistedAccounts(db: DSLContext) = db.selectCount()
    .from(SYNCHRONIZED_ACCOUNTS)
    .fetchOneInto(Long::class.java)

