package energy.so.ac.junifer.ingress.services

import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequest
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CONSENT_FROM_DATE
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_METERPOINT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.METERPOINT
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.SEOND_JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.enrollCustomerProto
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.updatePrimaryContactProto
import energy.so.ac.junifer.fixtures.EnrolAdditionalAccountPrecannedData.enrolNewAccountRequest
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient.createRetryingMockClient
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient.juniferConfig
import energy.so.ac.junifer.ingress.models.customers.UpdateCustomerDto
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.v1.customers.createCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.createCustomerVulnerabilityRequest
import energy.so.ac.junifer.v1.customers.deleteCustomerPsrRequest
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.ints.shouldBeExactly
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondOk
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.request.HttpRequest
import io.ktor.client.request.HttpRequestData
import io.ktor.client.request.HttpResponseData
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDate
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.serialization.json.Json

class HttpJuniferCustomerServiceTest : BehaviorSpec({

    val mockFeatureService = mockk<FeatureService> {
        coEvery { isFeatureEnabled(FeatureName.TMP_SO_24805_RETRYABLE_METERPOINT_CLIENT) } returns true
    }

    // mpClient uses existing mockHttpClient in most cases.
    // A new SUT and mpClient should be created for retry testing, as we must pass stateful variables to test retries.
    val sut = HttpJuniferCustomerService(
        config = juniferConfig,
        httpClient = getMockHttpClient(),
        mpClient = getMockHttpClient(),
        featureService = mockFeatureService
    )

    given("update customer request") {
        val updateCustomerDto = UpdateCustomerDto(id = "888", firstName = "Steve")

        `when`("update customer") {
            sut.updateCustomer(JUNIFER_CUSTOMER_ID.toString(), updateCustomerDto)
        }
        then("PUT request to junifer ok")
    }

    given("create customer psr") {
        val createCustomerPsr = createCustomerPsrRequest {
            customerId = "1"
            value = "value"
            utilityMarket = "utility"
            propertyTblId = 1
        }

        `when`("create customer psr") {
            val resp = sut.createCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), createCustomerPsr)

            then("POST request to junifer ok") {
                resp.id shouldBe 12L
            }
        }
    }

    given("delete customer psr") {
        val psrId = 2L
        `when`("deleteCustomerPsr is invoked") {
            val resp = sut.deleteCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), psrId)

            then("DELETE request to junifer ok") {
                resp shouldBe Unit
            }
        }
    }

    given("an update contact request") {
        val request = updatePrimaryContactProto
        `when`("client called") {
            then("issued a PUT request to junifer") {
                sut.updatePrimaryContact(JUNIFER_CUSTOMER_ID.toString(), request)
            }
        }
    }

    given("enroll customer request") {
        `when`("enroll customer") {
            sut.enrollCustomer(enrollCustomerProto)
            then("POST request OK")
        }
    }

    given("get customer request") {
        `when`("get customer") {
            sut.getCustomer(JUNIFER_CUSTOMER_ID.toString())
            then("GET request OK")
        }
    }

    given("customer id") {
        val customerId = SEOND_JUNIFER_CUSTOMER_ID

        `when`("get customer consents") {
            val resp = sut.getCustomerConsent(customerId.toString())

            then("GET request to junifer ok") {
                resp.results[0].id shouldBe 123L
            }
        }
    }

    given("meterPoint") {
        val meterPoint = METERPOINT

        `when`("get customer consents") {
            val resp = sut.getCustomerConsentV2(meterPoint)

            then("GET request to junifer ok") {
                resp.results[0].id shouldBe 123L
            }
        }
    }

    given("meterPoint and validDt") {
        val meterPoint = METERPOINT
        val validDt = CONSENT_FROM_DATE.toString()

        `when`("get customer consents") {
            val resp = sut.getCustomerConsentV2(meterPoint, validDt)

            then("GET request to junifer ok") {
                resp.results[0].id shouldBe 124L
            }
        }
    }

    given("customer id and update customer consent request") {
        val customerId = SEOND_JUNIFER_CUSTOMER_ID
        val request = updateCustomerConsentRequest

        `when`("update customer consents") {

            then("PUT request to junifer ok") {
                sut.updateCustomerConsent(customerId.toString(), request)
            }
        }
    }

    given("customer id and update customer consent request via v2") {
        `when`("update customer consents") {
            then("PUT request to junifer ok") {
                sut.updateCustomerConsent(JUNIFER_METERPOINT_ID.toString(), "freq", "def", LocalDate.now())
            }
        }
        `when`("a uSmartCoC ticket is processing for this meterpoint") {
            val httpClientCallCount = AtomicInteger(0)
            val successfulCallCount = 3

            // build your SUT with our retrying client
            val sut =
                HttpJuniferCustomerService(
                    config = juniferConfig,
                    httpClient = getMockHttpClient(),
                    mpClient =
                        getRetryingMockClient(
                            handler = {
                                if (httpClientCallCount.incrementAndGet() < successfulCallCount)
                                    mock422ChangeOfConsentAlreadyOpenForMeterPoint()
                                else mock204NoContent()
                            },
                            retryRule = meterPointConsentRetry()
                        ),
                    featureService = mockFeatureService,
                )

            shouldNotThrowAny {
                sut.updateCustomerConsent(
                    juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                    freq = "freq",
                    consentDefinition = "def",
                    fromDt = LocalDate.now()
                )
            }

            then("The request should be retried, succeed on 5th attempt") {
                httpClientCallCount.get() shouldBeExactly successfulCallCount
            }
        }
    }

    given("a junifer customer") {
        `when`("an additional account is enrolled") {

            val response = sut.enrolAdditionalAccount(JUNIFER_CUSTOMER_ID.toString(), enrolNewAccountRequest)

            then("submit an enrol additional account request") {
                response.accountId shouldBe JUNIFER_BILLING_ACCOUNT_ID
            }
        }
    }

    given("create customer vulnerability") {
        val request = createCustomerVulnerabilityRequest {
            customerId = "1"
            id = 1
            propertyTblId = 1
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
        }

        `when`("create customer vulnerability") {
            val resp = sut.createCustomerVulnerability(JUNIFER_CUSTOMER_ID.toString(), request)

            then("POST request to junifer ok") {
                resp.id shouldBe 12L
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->

        if (request.method == HttpMethod.Post && request.url.fullPath.contains("enrolAdditionalAccount")) {
            respond(
                "{ \"accountId\": $JUNIFER_BILLING_ACCOUNT_ID }",
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Put && request.url.fullPath.contains(JUNIFER_CUSTOMER_ID.toString())) {
            respond(
                "",
                HttpStatusCode.NoContent,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Delete && request.url.fullPath.contains(JUNIFER_CUSTOMER_ID.toString())) {
            respond(
                "",
                HttpStatusCode.NoContent,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
        else if (request.method == HttpMethod.Get && request.url.fullPath.contains("customers/$JUNIFER_CUSTOMER_ID")) {
            respond(
                """{
                            "id": 1,
                            "name": "Mr Joe Bloggs",
                            "number": "00000001",
                            "customerClass": "Residential Customer Class",
                            "customerType": "Domestic",
                            "state": "Prospect",
                            "title": "Mr",
                            "forename": "Joe",
                            "surname": "Bloggs",
                            "companyName": "Energy Ltd",
                            "companyNumber": "3100000004",
                            "companyAddress": {
                                "addressType": "Standard address",
                                "address1": "1 High Street",
                                "address2": "Highton",
                                "address3": "Highville",
                                "address4": "Hightown",
                                "address5": "Highborough",
                                "postcode": "TE1 2ST",
                                "countryCode": "GB",
                                "country": "Great Britain"
                            },
                            "bereavementFl": false,
                            "marketingOptOutFl": false,
                            "creditScore": 1,
                            "taxExemptReason": "1",
                            "primaryContact": {
                                "id": "123",
                                "contactType": "Residential",
                                "primary": true,
                                "title": "Ms",
                                "forename": "Test",
                                "surname": "Gas",
                                "initials": "TG",
                                "jobTitle": "Inspector",
                                "address": {
                                    "addressType": "Standard Address",
                                    "careOf": "Ms Test Gas",
                                    "address1": "123 The Street",
                                    "address2": "Central Town",
                                    "address3": "London",
                                    "address4": "Highville",
                                    "address5": "Hightown",
                                    "postcode": "E20 1AB",
                                    "countryCode": "GB",
                                    "country": "Great Britain"
                                },
                                "email": "<EMAIL>",
                                "dateOfBirth": "2020-01-01",
                                "phoneNumber1": "0000000000",
                                "phoneNumber2": "0000000000",
                                "phoneNumber3": "0000000000",
                                "customerContactId": 456,
                                "cancelled": false,
                                "fromDttm": "2019-03-27T10:15:30",
                                "toDttm": "2019-03-27T10:15:30",
                                "links": {
                                    "self": "http://127.0.0.1:43002/rest/v1/contacts/123"
                                }
                            },
                            "links": {
                                "self": "http://127.0.0.1:43002/rest/v1/contacts/123",
                                "accounts": "http://127.0.0.1:43002/rest/v1/customers/123/accounts",
                                "securityQuestions": "http://127.0.0.1:43002/rest/v1/customers/123/securityQuestions",
                                "billingEntities": "http://127.0.0.1:43001/rest/v1/customers/123/billingEntities",
                                "prospects": "http://127.0.0.1:43002/rest/v1/customers/123/prospects"
                            }
                        }""", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Post && request.url.fullPath.contains("enrolCustomer")) {
            respond(
                "{ \"customerId\": \"12\"}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Post) {
            respond(
                "{ \"id\": \"12\"}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("junifer/uk/meterpoints")) {
            respond(
                "{ \"results\": [{\"id\":456, \"consentDefinition\":\"def\", \"setting\":false, \"fromDt\":\"2023-02-02\"," +
                        "\"toDt\":\"2024-02-02\"}]}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get &&
            request.url.fullPath.contains("consents") &&
            !request.url.parameters.contains("validDt")
        ) {
            respond(
                "{ \"results\": [{\"id\":123, \"consentDefinition\":\"def\", \"setting\":false, \"fromDt\":\"2023-02-02\"," +
                        "\"toDt\":\"2024-02-02\"}]}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get &&
            request.url.fullPath.contains("consents") &&
            request.url.parameters.contains("validDt")
        ) {
            respond(
                "{ \"results\": [{\"id\":124, \"consentDefinition\":\"def\", \"setting\":false, \"fromDt\":\"2023-02-02\"," +
                        "\"toDt\":\"2024-02-02\"}]}", HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Put && request.url.fullPath.contains("consents")) {
            respondOk()
        } else {
            mock400BadRequest()
        }
    })
}

private fun MockRequestHandleScope.mock400BadRequest(): HttpResponseData = respond(
    content = ByteReadChannel(
        Json.encodeToString(
            JuniferError.serializer(), JuniferError("", "", "")
        )
    ), status = HttpStatusCode.BadRequest, headers = headersOf(HttpHeaders.ContentType, "application/json")
)

private fun MockRequestHandleScope.mock422ChangeOfConsentAlreadyOpenForMeterPoint(): HttpResponseData = respond(
    content = ByteReadChannel(
        Json.encodeToString(
            JuniferError.serializer(),
            JuniferError(
                "422",
                "",
                "Unknown error when submitting request to Junifer: There is already a 'uSmart Change of Consent' ticket open for this meter point"
            )
        )
    ),
    status = HttpStatusCode.UnprocessableEntity,
    headers = headersOf(HttpHeaders.ContentType, "application/json")
)

private fun MockRequestHandleScope.mock204NoContent(): HttpResponseData = respond(
    content = ByteReadChannel.Empty,
    status = HttpStatusCode.NoContent,
    headers = headersOf(HttpHeaders.ContentType, "application/json")
)

fun getRetryingMockClient(
    handler: suspend MockRequestHandleScope.(HttpRequestData) -> HttpResponseData,
    retryRule: HttpRequestRetry.ShouldRetryContext.(HttpRequest, HttpResponse) -> Boolean
): HttpClient = createRetryingMockClient(
    mockEngine = MockEngine { handler(it) },
    shouldRetry = retryRule
)

fun meterPointConsentRetry():
        HttpRequestRetry.ShouldRetryContext.(
            HttpRequest,
            HttpResponse,
        ) -> Boolean = { req, res ->
    res.status == HttpStatusCode.UnprocessableEntity &&
            req.method == HttpMethod.Put &&
            req.url.fullPath.contains("/uk/meterPoints/$JUNIFER_METERPOINT_ID/consents") &&
            req.url.fullPath.endsWith("/consents")
}
