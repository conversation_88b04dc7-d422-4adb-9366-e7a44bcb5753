package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.createAccountCreditRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe


class JuniferCreateAccountCreditRequestTest : BehaviorSpec({

    given("create account credit request") {
        val request: CreateAccountCreditRequest = createAccountCreditRequest {
            grossAmountInPounds = 100.0
            salesTaxName = "tax name"
            accountCreditReasonName = "reason"
            reference = "ref"
            description = "description"
        }

        `when`("map to junifer create account credit request") {
            val juniferRequest = JuniferCreateAccountCreditRequest.fromCreateAccountCreditRequest(
                request
            )

            then("return a corresponding JuniferCreateAccountCreditRequest") {
                assertSoftly {
                    juniferRequest.accountCreditReasonName shouldBe request.accountCreditReasonName
                    juniferRequest.description shouldBe request.description
                    juniferRequest.reference shouldBe request.reference
                    juniferRequest.salesTaxName shouldBe request.salesTaxName
                    juniferRequest.grossAmount shouldBe request.grossAmountInPounds
                }
            }
        }
    }

})
