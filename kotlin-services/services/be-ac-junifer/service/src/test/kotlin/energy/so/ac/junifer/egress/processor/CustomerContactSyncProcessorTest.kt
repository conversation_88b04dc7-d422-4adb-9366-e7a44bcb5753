package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.config.BOOLEAN_TRUE
import energy.so.ac.junifer.egress.database.repositories.JuniferContactRepository
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.CUSTOMER_CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.JUNIFER_CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.JUNIFER_CUSTOMER_CONTACT_ID
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.createCustomerContactSync
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.customerContactSyncEvent
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.customerContactSyncResponse
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.testJuniferCustomerContact
import energy.so.ac.junifer.fixtures.CustomerContactPrecannedData.updateCustomerContactSync
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.mapping.EntityIdentifier.CONTACT
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER
import energy.so.ac.junifer.mapping.EntityIdentifier.CUSTOMER_CONTACT
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.customers.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class CustomerContactSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockContactRepository = mockk<JuniferContactRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val customerContactProcessor = CustomerContactSyncProcessor(mockMapper, mockContactRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockContactRepository, mockSyncClient)
    }

    given("no existing mapped customerContact and an existing junifer customerContact") {

        every { mockMapper.getCoreId(CUSTOMER_CONTACT, JUNIFER_CUSTOMER_CONTACT_ID.toString()) } returns null
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) } returns testJuniferCustomerContact

        `when`("a customerContact event is generated") {

            coEvery { mockSyncClient.syncCustomerContactEntity(createCustomerContactSync) } returns customerContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_CONTACT,
                    JUNIFER_CUSTOMER_CONTACT_ID.toString(),
                    CUSTOMER_CONTACT_ID.toString()
                )
            }

            customerContactProcessor.process(customerContactSyncEvent)

            then("a new customerContact should be created") {
                verify { mockMapper.getCoreId(CUSTOMER_CONTACT, JUNIFER_CUSTOMER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        CUSTOMER_CONTACT,
                        JUNIFER_CUSTOMER_CONTACT_ID.toString(),
                        CUSTOMER_CONTACT_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncCustomerContactEntity(createCustomerContactSync) }
            }
        }
    }

    given("an existing mapped customerContact and an existing junifer customerContact") {

        every {
            mockMapper.getCoreId(
                CUSTOMER_CONTACT,
                JUNIFER_CUSTOMER_CONTACT_ID.toString()
            )
        } returns CUSTOMER_CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) } returns testJuniferCustomerContact

        `when`("a customerContact event is generated") {

            coEvery { mockSyncClient.syncCustomerContactEntity(updateCustomerContactSync) } returns customerContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_CONTACT,
                    JUNIFER_CUSTOMER_CONTACT_ID.toString(),
                    CUSTOMER_CONTACT_ID.toString()
                )
            }

            customerContactProcessor.process(customerContactSyncEvent)

            then("customerContact should be patched") {
                verify { mockMapper.getCoreId(CUSTOMER_CONTACT, JUNIFER_CUSTOMER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) }
                coVerify { mockSyncClient.syncCustomerContactEntity(updateCustomerContactSync) }
            }
        }
    }

    given("an existing mapped customerContact and existing junifer customerContact to be deleted") {

        every {
            mockMapper.getCoreId(
                CUSTOMER_CONTACT,
                JUNIFER_CUSTOMER_CONTACT_ID.toString()
            )
        } returns CUSTOMER_CONTACT_ID.toString()
        every { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) } returns CUSTOMER_ID.toString()
        every { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) } returns CONTACT_ID.toString()
        every { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) } returns testJuniferCustomerContact.copy(
            deletefl = BOOLEAN_TRUE,
            cancelfl = BOOLEAN_TRUE
        )

        `when`("a customerContact event is generated") {

            coEvery { mockSyncClient.syncCustomerContactEntity(any()) } returns customerContactSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    CUSTOMER_CONTACT,
                    JUNIFER_CUSTOMER_CONTACT_ID.toString(),
                    CUSTOMER_CONTACT_ID.toString()
                )
            }

            customerContactProcessor.process(customerContactSyncEvent)

            then("customerContact should be deleted") {
                verify { mockMapper.getCoreId(CUSTOMER_CONTACT, JUNIFER_CUSTOMER_CONTACT_ID.toString()) }
                verify { mockMapper.getCoreId(CUSTOMER, JUNIFER_CUSTOMER_ID.toString()) }
                verify { mockMapper.getCoreId(CONTACT, JUNIFER_CONTACT_ID.toString()) }
                verify { mockContactRepository.getCustomerContact(JUNIFER_CUSTOMER_CONTACT_ID) }
                coVerify { mockSyncClient.syncCustomerContactEntity(any()) }
            }
        }
    }

})
