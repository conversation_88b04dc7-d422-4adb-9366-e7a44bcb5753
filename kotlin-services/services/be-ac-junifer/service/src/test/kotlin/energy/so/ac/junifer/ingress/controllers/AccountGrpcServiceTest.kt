package energy.so.ac.junifer.ingress.controllers

import com.google.protobuf.Empty
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.BILLING_ACCOUNT_NUMBER
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditRequest
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditRequest_2
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.juniferAccountCreditResponse
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.juniferResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData
import energy.so.ac.junifer.fixtures.AccountPrecannedData.ACCOUNT_NUMBER
import energy.so.ac.junifer.fixtures.AccountPrecannedData.ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
import energy.so.ac.junifer.fixtures.AccountPrecannedData.FREQUENCY
import energy.so.ac.junifer.fixtures.AccountPrecannedData.FREQUENCY_MULTIPLE
import energy.so.ac.junifer.fixtures.AccountPrecannedData.SUGGESTED_PAYMENT_AMOUNT
import energy.so.ac.junifer.fixtures.AccountPrecannedData.applyExitFeesRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.billingAccountId
import energy.so.ac.junifer.fixtures.AccountPrecannedData.cancelAccountReviewPeriodRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.contactId
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountRepaymentRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountRepaymentResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountReviewPeriodRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountReviewPeriodResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountTicketRequest
import energy.so.ac.junifer.fixtures.AccountPrecannedData.createAccountTicketResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferAccountId
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferContactId
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountRepaymentResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountReviewPeriodResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferCreateAccountTicketResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.updateAccountContactRequest
import energy.so.ac.junifer.fixtures.BillingAccountPrecannedData.juniferPaymentSchedulePeriod
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.accounts.JuniferAccountCredit
import energy.so.ac.junifer.ingress.models.accounts.JuniferAccountResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferAccountSuggestedPaymentAmountResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferCancelAccountRegistrationResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferCreateAccountNoteResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferGetAccountCreditsResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferGetAccountTicketsResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferPaymentSchedulePeriodResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferRenewAccount
import energy.so.ac.junifer.ingress.models.accounts.JuniferRenewAccountResponse
import energy.so.ac.junifer.ingress.models.accounts.SuccessfulCancellation
import energy.so.ac.junifer.ingress.models.accounts.UnsuccessfulCancellation
import energy.so.ac.junifer.ingress.services.JuniferAccountService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.accounts.CancelAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.CancelAccountRegistrationRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.CreateAccountTicketRequest
import energy.so.ac.junifer.v1.accounts.GetAccountCreditsRequest
import energy.so.ac.junifer.v1.accounts.NoteType
import energy.so.ac.junifer.v1.accounts.Status
import energy.so.ac.junifer.v1.accounts.accountSuggestedPaymentAmountRequest
import energy.so.ac.junifer.v1.accounts.cancelAccountCreditRequest
import energy.so.ac.junifer.v1.accounts.cancelAccountRegistrationRequest
import energy.so.ac.junifer.v1.accounts.copy
import energy.so.ac.junifer.v1.accounts.createAccountNoteRequest
import energy.so.ac.junifer.v1.accounts.getAccountByNumberRequest
import energy.so.ac.junifer.v1.accounts.getAccountCreditsRequest
import energy.so.ac.junifer.v1.accounts.getTicketsByJuniferAccountNumberRequest
import energy.so.ac.junifer.v1.accounts.getTicketsForAccountNumberRequest
import energy.so.ac.junifer.v1.accounts.meterPointChangeOfModeRequest
import energy.so.ac.junifer.v1.accounts.meterPointChangeOfModeResponse
import energy.so.ac.junifer.v1.accounts.renewAccountRequest
import energy.so.ac.junifer.v1.accounts.updateAccountToSmartPayAsYouGoRequest
import energy.so.ac.junifer.v1.accounts.updateAccountToSmartPayAsYouGoResponse
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.GrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.extensions.toNullableBoolean
import energy.so.commons.grpc.extensions.toNullableInt32
import energy.so.commons.grpc.extensions.toNullableString
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.v2.dtos.idRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDateTime
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertFailsWith

class AccountGrpcServiceTest : BehaviorSpec({

    val mockAccountService = mockk<JuniferAccountService>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()

    val sut = AccountGrpcService(mockAccountService, inMemoryMapper)

    afterTest {
        confirmVerified(mockAccountService)
        clearMocks(mockAccountService)
    }

    given("create account credit request") {
        val juniferAccountId = AccountCreditPrecannedData.ACCOUNT_ID
        val billingAccountId = BILLING_ACCOUNT_ID

        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns 204") {

                coJustRun { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } returns juniferResponse

                `when`("create account credit") {
                    val result = sut.createAccountCredit(accountCreditRequest)

                    then("credit account created") {
                        result shouldBe juniferAccountCreditResponse
                        coVerify { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }

            and("reference does not match") {
                coJustRun {
                    mockAccountService.createAccountCredit(
                        juniferAccountId, accountCreditRequest_2
                    )
                }
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } returns juniferResponse

                `when`("create account credit") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountCredit(accountCreditRequest_2)
                    }

                    then("credit account created") {
                        coVerify {
                            mockAccountService.createAccountCredit(
                                juniferAccountId, accountCreditRequest_2
                            )
                        }
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest)
                } throws JuniferException("InvalidQuality", "", "")

                `when`("create account credit") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountCredit(accountCreditRequest)
                    }

                    then("no credit account created") {
                        coVerify { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                    }
                }
            }

            and("junifer exception when getting credit accounts") {
                coJustRun { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } throws JuniferException(
                    "Exception", "", ""
                )

                `when`("create account credit") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountCredit(accountCreditRequest)
                    }

                    then("no credit account created") {
                        coVerify { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }

            and("internal service exception when creating account credit") {
                coEvery {
                    mockAccountService.createAccountCredit(
                        juniferAccountId, accountCreditRequest
                    )
                } throws RuntimeException()

                `when`("create account credit") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountCredit(accountCreditRequest)
                    }

                    then("no credit account created") {
                        coVerify { mockAccountService.createAccountCredit(juniferAccountId, accountCreditRequest) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("create account credit") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createAccountCredit(accountCreditRequest)
                }

                then("no credit account created") {
                    coVerify(exactly = 0) {
                        mockAccountService.createAccountCredit(
                            juniferAccountId, accountCreditRequest
                        )
                    }
                }
            }
        }
    }

    given("get account credit requests") {
        val juniferAccountId = "1"
        val coreId = 100L
        val request: GetAccountCreditsRequest = getAccountCreditsRequest { billingAccountId = coreId }

        and("entity mapping present") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, coreId.toString())

            and("call to junifer returns 200") {
                val juniferResponse = JuniferGetAccountCreditsResponse(
                    results = listOf(
                        JuniferAccountCredit(
                            id = 1,
                            createdDttm = LocalDateTime.now().minusDays(10),
                            acceptedDttm = LocalDateTime.now().minusDays(10),
                            cancelledDttm = null,
                            currencyISO = "EUR",
                            grossAmount = 5.0,
                            netAmount = 4.0,
                            salesTaxAmount = 1.0,
                            salesTax = "VAT",
                            reason = "reason",
                            reference = "reference"
                        ),
                        JuniferAccountCredit(
                            id = 2,
                            createdDttm = LocalDateTime.now(),
                            acceptedDttm = LocalDateTime.now(),
                            cancelledDttm = null,
                            currencyISO = "GBP",
                            grossAmount = 10.0,
                            netAmount = 8.0,
                            salesTaxAmount = 2.0,
                            salesTax = "VAT",
                            reason = "reason",
                            reference = "reference"
                        ),
                    )
                )
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } returns juniferResponse
                `when`("cancel account credit") {

                    val result = sut.getAccountCredits(request)
                    then(" account credits returned") {
                        result.accountCreditsList.size shouldBe 2
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }

            and("junifer exception") {
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } throws JuniferException(
                    errorCode = "NotFound", "", ""
                )
                `when`("get account credits") {

                    assertThrows<EntityNotFoundGrpcException> { sut.getAccountCredits(request) }
                    then("no account credits returned") {
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }

            and("internal service exception") {
                coEvery { mockAccountService.getAccountCredits(juniferAccountId) } throws RuntimeException()
                `when`("get account credits") {
                    assertThrows<UnknownGrpcException> { sut.getAccountCredits(request) }
                    then("no account credits returned") {
                        coVerify { mockAccountService.getAccountCredits(juniferAccountId) }
                    }
                }
            }
        }

        and("no entity mapping present") {
            inMemoryMapper.clear()

            `when`("get account credits") {

                assertThrows<FailedPreconditionGrpcException> { sut.getAccountCredits(request) }
                then("no account credits returned") {
                    coVerify(exactly = 0) { mockAccountService.getAccountCredits(any()) }
                }
            }
        }
    }

    given("cancel account credit request") {
        val request: CancelAccountCreditRequest = cancelAccountCreditRequest { accountCreditId = 1 }

        and("call to junifer returns 204") {
            coEvery { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) } returns Unit
            `when`("cancel account credit") {

                val result = sut.cancelAccountCredit(request)
                then("credit account cancelled") {
                    result shouldBe Empty.getDefaultInstance()
                    coVerify { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) }
                }
            }
        }

        and("junifer exception") {
            coEvery { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) } throws JuniferException(
                errorCode = "NotFound", "", ""
            )
            `when`("cancel account credit") {

                assertThrows<EntityNotFoundGrpcException> { sut.cancelAccountCredit(request) }
                then("no credit account cancelled") {
                    coVerify { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) }
                }
            }
        }

        and("internal service exception") {
            coEvery { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) } throws RuntimeException()
            `when`("cancel account credit") {

                assertThrows<UnknownGrpcException> { sut.cancelAccountCredit(request) }
                then("no credit account cancelled") {
                    coVerify { mockAccountService.cancelAccountCredit(request.accountCreditId.toString()) }
                }
            }
        }
    }

    given("cancel account registration request") {
        val juniferAccountId = "1"
        val billingAccountId = "100"
        val request: CancelAccountRegistrationRequest = cancelAccountRegistrationRequest {
            accountId = billingAccountId
            reason = "reason"
            communicationFl = false
        }

        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                coEvery {
                    mockAccountService.cancelAccountRegistration(juniferAccountId, request)
                } returns JuniferCancelAccountRegistrationResponse(
                    unsuccessfulCancellations = listOf(
                        UnsuccessfulCancellation(reason = "reason", meterPoints = listOf())
                    ), successfulCancellations = listOf(
                        SuccessfulCancellation(meterPoints = listOf()),
                    )
                )

                `when`("cancel account registration") {
                    val result = sut.cancelAccountRegistration(request)

                    then("account registration cancelled") {
                        result.successfulCancellationsCount shouldBe 1
                        coVerify { mockAccountService.cancelAccountRegistration(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.cancelAccountRegistration(juniferAccountId, request)
                } throws JuniferException("InvalidQuality", "", "")

                `when`("cancel account registration") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.cancelAccountRegistration(request)
                    }

                    then("account registration not cancelled") {
                        coVerify { mockAccountService.cancelAccountRegistration(juniferAccountId, request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.cancelAccountRegistration(
                        juniferAccountId, request
                    )
                } throws RuntimeException()

                `when`("cancel account registration") {
                    assertThrows<UnknownGrpcException> {
                        sut.cancelAccountRegistration(request)
                    }

                    then("account registration not cancelled") {
                        coVerify { mockAccountService.cancelAccountRegistration(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("cancel account registration") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.cancelAccountRegistration(request)
                }

                then("account registration not cancelled") {
                    coVerify(exactly = 0) { mockAccountService.cancelAccountRegistration(juniferAccountId, request) }
                }
            }
        }
    }

    given("create account note request") {
        val juniferAccountId = "1"
        val billingAccountId = "100"
        val request: CreateAccountNoteRequest = createAccountNoteRequest {
            accountId = billingAccountId
            subject = "subject"
            type = NoteType.Note
            summary = "summary"
            content = "content"
        }
        val juniferCreateAccountNoteResponse = JuniferCreateAccountNoteResponse(
            1L,
            LocalDateTime.now(),
            "subject",
            energy.so.ac.junifer.ingress.models.accounts.NoteType.NOTE,
            "summary",
            "content"
        )

        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)
            and("call to junifer returns response") {

                coEvery {
                    mockAccountService.createAccountNote(juniferAccountId, request)
                } returns juniferCreateAccountNoteResponse

                `when`("create account note") {
                    val result = sut.createAccountNote(request)

                    then("note account created") {
                        result.id shouldBe juniferCreateAccountNoteResponse.id
                        result.content.value shouldBe juniferCreateAccountNoteResponse.content
                        result.subject shouldBe juniferCreateAccountNoteResponse.subject
                        result.summary shouldBe juniferCreateAccountNoteResponse.summary

                        coVerify { mockAccountService.createAccountNote(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountNote(juniferAccountId, request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("create account note") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountNote(request)
                    }

                    then("no note account created") {
                        coVerify { mockAccountService.createAccountNote(juniferAccountId, request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery { mockAccountService.createAccountNote(juniferAccountId, request) } throws RuntimeException()

                `when`("create account not") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountNote(request)
                    }

                    then("no note account created") {
                        coVerify { mockAccountService.createAccountNote(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("create account note") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createAccountNote(request)
                }

                then("no note account created") {
                    coVerify(exactly = 0) { mockAccountService.createAccountNote(juniferAccountId, request) }
                }
            }
        }
        and("useJuniferId is true") {
            val juniferIdRequest: CreateAccountNoteRequest = createAccountNoteRequest {
                accountId = billingAccountId
                subject = "subject"
                type = NoteType.Note
                summary = "summary"
                content = "content"
                useJuniferId = true
            }
            coEvery {
                mockAccountService.createAccountNote(billingAccountId, juniferIdRequest)
            } returns juniferCreateAccountNoteResponse

            `when`("create account note") {
                val result = sut.createAccountNote(juniferIdRequest)

                then("note account created") {
                    result.id shouldBe juniferCreateAccountNoteResponse.id
                    result.content.value shouldBe juniferCreateAccountNoteResponse.content
                    result.subject shouldBe juniferCreateAccountNoteResponse.subject
                    result.summary shouldBe juniferCreateAccountNoteResponse.summary

                    coVerify { mockAccountService.createAccountNote(billingAccountId, juniferIdRequest) }
                }
            }
        }
    }

    given("create account ticket request") {

        val request: CreateAccountTicketRequest = createAccountTicketRequest
        val requestUseJunifedId: CreateAccountTicketRequest = createAccountTicketRequest.copy {
            useJuniferId = true
            accountId = juniferAccountId
        }

        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                coEvery {
                    mockAccountService.createAccountTicket(juniferAccountId, request)
                } returns juniferCreateAccountTicketResponse

                `when`("create account ticket") {
                    val result = sut.createAccountTicket(request)

                    then("account ticket created") {
                        result shouldBe createAccountTicketResponse

                        coVerify { mockAccountService.createAccountTicket(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountTicket(juniferAccountId, request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("create account ticket") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountTicket(request)
                    }

                    then("no account ticket created") {
                        coVerify { mockAccountService.createAccountTicket(juniferAccountId, request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery { mockAccountService.createAccountTicket(juniferAccountId, request) } throws RuntimeException()

                `when`("create account ticket") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountTicket(request)
                    }

                    then("no account ticket created") {
                        coVerify { mockAccountService.createAccountTicket(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("create account ticket") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createAccountTicket(request)
                }

                then("no account ticket created") {
                    coVerify(exactly = 0) { mockAccountService.createAccountTicket(juniferAccountId, request) }
                }
            }
        }

        `when`("useJuniferId is informed") {
            coEvery {
                mockAccountService.createAccountTicket(juniferAccountId, requestUseJunifedId)
            } returns juniferCreateAccountTicketResponse

            val result = sut.createAccountTicket(requestUseJunifedId)
            then("create a ticket in junifer") {
                result shouldBe createAccountTicketResponse

                coVerify { mockAccountService.createAccountTicket(juniferAccountId, requestUseJunifedId) }
            }
        }
    }

    given("create account ticket for account number request") {

        val request: CreateAccountTicketRequest = createAccountTicketRequest.copy { accountId = ACCOUNT_NUMBER }

        and("call to junifer returns response") {
            coEvery {
                mockAccountService.createAccountTicketForAccountNumber(ACCOUNT_NUMBER, request)
            } returns juniferCreateAccountTicketResponse

            `when`("create account ticket") {
                val result = sut.createAccountTicketForAccountNumber(request)

                then("account ticket created") {
                    result shouldBe createAccountTicketResponse

                    coVerify { mockAccountService.createAccountTicketForAccountNumber(ACCOUNT_NUMBER, request) }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountTicketForAccountNumber(ACCOUNT_NUMBER, request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("create account ticket") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountTicketForAccountNumber(request)
                    }

                    then("no account ticket created") {
                        coVerify { mockAccountService.createAccountTicketForAccountNumber(ACCOUNT_NUMBER, request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.createAccountTicketForAccountNumber(
                        ACCOUNT_NUMBER, request
                    )
                } throws RuntimeException()

                `when`("create account ticket") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountTicketForAccountNumber(request)
                    }

                    then("no account ticket created") {
                        coVerify { mockAccountService.createAccountTicketForAccountNumber(ACCOUNT_NUMBER, request) }
                    }
                }
            }
        }
    }


    given("junifer account number") {
        val accountNumber = "304556"

        and("entity mapping exists") {
            val juniferAccountId = "12"
            val billingAccountId = "1"
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                val juniferAccountResponse = JuniferAccountResponse(
                    id = 12, type = "CB-INVOICE", number = "304556", name = "Account304556", currency = "GBP"
                )
                coEvery {
                    mockAccountService.getAccountByNumber(accountNumber)
                } returns juniferAccountResponse

                `when`("create account note") {
                    val result = sut.getAccountByJuniferAccountNumber(getAccountByNumberRequest {
                        juniferAccountNumber = accountNumber
                    })

                    then("account returned") {
                        result.id shouldBe billingAccountId

                        coVerify { mockAccountService.getAccountByNumber(accountNumber) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.getAccountByNumber(accountNumber)
                } throws JuniferException("NotFound", "", "")

                `when`("get account by number") {

                    then("no account is returned") {
                        assertThrows<EntityNotFoundGrpcException> {
                            sut.getAccountByJuniferAccountNumber(getAccountByNumberRequest {
                                juniferAccountNumber = accountNumber
                            })
                        }

                        coVerify { mockAccountService.getAccountByNumber(accountNumber) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            and("call to junifer returns response") {
                val juniferAccountResponse = JuniferAccountResponse(
                    id = 12, type = "CB-INVOICE", number = "304556", name = "Account304556", currency = "GBP"
                )
                coEvery {
                    mockAccountService.getAccountByNumber(accountNumber)
                } returns juniferAccountResponse

                `when`("get account by number") {
                    assertThrows<FailedPreconditionGrpcException> {
                        sut.getAccountByJuniferAccountNumber(getAccountByNumberRequest {
                            juniferAccountNumber = accountNumber
                        })
                    }

                    then("no account returned") {
                        coVerify { mockAccountService.getAccountByNumber(accountNumber) }
                    }
                }
            }
        }
    }

    given("junifer account id") {
        val juniferAccountId = "12"
        val juniferResponse = JuniferPaymentSchedulePeriodResponse(
            results = listOf(juniferPaymentSchedulePeriod)
        )
        and("get psp using billing account id") {

            and("entity mapping exists") {
                val billingAccountId = "1"
                inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

                and("call to junifer returns response") {
                    coEvery {
                        mockAccountService.getPaymentSchedulePeriods(juniferAccountId)
                    } returns juniferResponse

                    `when`("get payment schedule periods") {
                        val result =
                            sut.getPaymentSchedulePeriodsByAccountId(idRequest { id = billingAccountId.toLong() })

                        then("payment schedule periods returned") {
                            result.paymentSchedulePeriodCount shouldBe 1

                            coVerify { mockAccountService.getPaymentSchedulePeriods(juniferAccountId) }
                        }
                    }
                }

                and("call to junifer throws exception") {
                    coEvery {
                        mockAccountService.getPaymentSchedulePeriods(juniferAccountId)
                    } throws JuniferException("NotFound", "", "")

                    `when`("get payment schedule periods") {

                        then("no payment schedule period is returned") {
                            assertThrows<EntityNotFoundGrpcException> {
                                sut.getPaymentSchedulePeriodsByAccountId(idRequest { id = billingAccountId.toLong() })
                            }

                            coVerify { mockAccountService.getPaymentSchedulePeriods(juniferAccountId) }
                        }
                    }
                }
            }

            and("no mapping exists") {
                inMemoryMapper.clear()

                and("call to junifer returns response") {
                    coEvery {
                        mockAccountService.getPaymentSchedulePeriods(juniferAccountId)
                    } throws Exception("example")

                    `when`("get payment schedule periods") {
                        assertThrows<FailedPreconditionGrpcException> {
                            sut.getPaymentSchedulePeriodsByAccountId(idRequest { id = 1L })
                        }

                        then("no payment schedule period is returned") {
                            coVerify(exactly = 0) { mockAccountService.getPaymentSchedulePeriods(juniferAccountId) }
                        }
                    }
                }
            }
        }
        and("get psp using junifer account id") {
            and("response successful") {
                coEvery {
                    mockAccountService.getPaymentSchedulePeriods(juniferAccountId)
                } returns juniferResponse
                `when`("get psp by junifer account id") {
                    val actual =
                        sut.getPaymentSchedulePeriodsByJuniferAccountId(idRequest { id = juniferAccountId.toLong() })
                    then("confirm expected") {
                        actual.paymentSchedulePeriodCount shouldBe 1

                        coVerify { mockAccountService.getPaymentSchedulePeriods(juniferAccountId) }
                    }

                }
            }
            and("response failure") {
                `when`("get psp by junifer account id") {
                    then("confirm exception") {
                        assertThrows<UnknownGrpcException> {
                            sut.getPaymentSchedulePeriodsByJuniferAccountId(idRequest {
                                id = juniferAccountId.toLong()
                            })
                        }

                        coVerify { mockAccountService.getPaymentSchedulePeriods(juniferAccountId) }
                    }
                }
            }
        }
    }

    given("renew account request") {
        val billingAccountId = "1"
        val request = renewAccountRequest {
            id = billingAccountId
            electricityProductCode = "123"
            electricitySupplyProductSubType = "type"
            electricityStartDate = "2023-05-12"
        }

        and("entity mapping exists") {
            val juniferAccountId = "12"
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                val juniferResponse = JuniferRenewAccountResponse(
                    listOf(JuniferRenewAccount("123", 1065L, 1066L))
                )
                coEvery {
                    mockAccountService.renewAccount(juniferAccountId, request)
                } returns juniferResponse

                `when`("renew account") {
                    val result = sut.renewAccount(request)

                    then("account is renewed") {
                        result.renewalsList[0].accountNumber shouldBe "123"

                        coVerify { mockAccountService.renewAccount(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.renewAccount(juniferAccountId, request)
                } throws JuniferException("NotFound", "", "")

                `when`("renew account") {

                    then("account is not renewed") {
                        assertThrows<EntityNotFoundGrpcException> {
                            sut.renewAccount(request)
                        }

                        coVerify { mockAccountService.renewAccount(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("renew account") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.renewAccount(request)
                }

                then("account is not renewed") {
                    coVerify(exactly = 0) { mockAccountService.renewAccount(any(), any()) }
                }
            }
        }
    }

    given("get account's suggested payment amount request") {
        val billingAccountId = "1"
        val request = accountSuggestedPaymentAmountRequest {
            accountId = billingAccountId
            frequency = FREQUENCY.toNullableString()
            frequencyMultiple = FREQUENCY_MULTIPLE.toNullableInt32()
            ignoreWarnings = true.toNullableBoolean()
        }

        and("entity mapping exists") {
            val juniferAccountId = "12"
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                val juniferResponse = JuniferAccountSuggestedPaymentAmountResponse(
                    frequency = FREQUENCY, suggestedPaymentAmount = SUGGESTED_PAYMENT_AMOUNT.toDouble()
                )
                coEvery {
                    mockAccountService.getAccountSuggestedPaymentAmount(juniferAccountId, request)
                } returns juniferResponse

                `when`("get account's suggested payment amount") {
                    val result = sut.getAccountSuggestedPaymentAmount(request)

                    then("account's suggested payment amount is retrieved") {
                        result.frequency shouldBe FREQUENCY
                        result.suggestedPaymentAmount shouldBe SUGGESTED_PAYMENT_AMOUNT.toDouble()

                        coVerify { mockAccountService.getAccountSuggestedPaymentAmount(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.getAccountSuggestedPaymentAmount(juniferAccountId, request)
                } throws JuniferException("NotFound", "", "")

                `when`("get account's suggested payment amount") {

                    then("account's suggested payment amount is not retrieved") {
                        assertThrows<EntityNotFoundGrpcException> {
                            sut.getAccountSuggestedPaymentAmount(request)
                        }

                        coVerify { mockAccountService.getAccountSuggestedPaymentAmount(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("get account's suggested payment amount") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.getAccountSuggestedPaymentAmount(request)
                }

                then("account's suggested payment amount is not retrieved") {
                    coVerify(exactly = 0) { mockAccountService.getAccountSuggestedPaymentAmount(any(), any()) }
                }
            }
        }
    }

    given("create account repayment request") {

        val request = createAccountRepaymentRequest
        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns response") {
                coEvery {
                    mockAccountService.createAccountRepayment(juniferAccountId, request)
                } returns juniferCreateAccountRepaymentResponse

                `when`("create account repayment") {
                    val result = sut.createAccountRepayment(request)

                    then("account repayment created") {
                        result shouldBe createAccountRepaymentResponse

                        coVerify { mockAccountService.createAccountRepayment(juniferAccountId, request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountRepayment(juniferAccountId, request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("create account repayment") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountRepayment(request)
                    }

                    then("no account repayment created") {
                        coVerify { mockAccountService.createAccountRepayment(juniferAccountId, request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.createAccountRepayment(
                        juniferAccountId, request
                    )
                } throws RuntimeException()

                `when`("create account repayment") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountRepayment(request)
                    }

                    then("no account repayment created") {
                        coVerify { mockAccountService.createAccountRepayment(juniferAccountId, request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("create account repayment") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createAccountRepayment(request)
                }

                then("no account repayment created") {
                    coVerify(exactly = 0) { mockAccountService.createAccountRepayment(juniferAccountId, request) }
                }
            }
        }

    }

    given("update account contact request") {

        val request = updateAccountContactRequest
        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.CONTACT, juniferContactId, contactId)

            and("call to junifer returns no content") {
                coJustRun { mockAccountService.updateAccountContact(juniferAccountId, juniferContactId, request) }

                `when`("update account contact") {
                    val result = sut.updateAccountContact(request)

                    then("account contact updated") {
                        result shouldBe Empty.getDefaultInstance()

                        coVerify {
                            mockAccountService.updateAccountContact(
                                juniferAccountId, juniferContactId, request
                            )
                        }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.updateAccountContact(juniferAccountId, juniferContactId, request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("update account contact") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.updateAccountContact(request)
                    }

                    then("account contact not updated") {
                        coVerify {
                            mockAccountService.updateAccountContact(
                                juniferAccountId, juniferContactId, request
                            )
                        }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.updateAccountContact(juniferAccountId, juniferContactId, request)
                } throws RuntimeException()

                `when`("update account contact") {
                    assertThrows<UnknownGrpcException> {
                        sut.updateAccountContact(request)
                    }

                    then("account contact not updated") {
                        coVerify {
                            mockAccountService.updateAccountContact(
                                juniferAccountId, juniferContactId, request
                            )
                        }
                    }
                }
            }
        }

        and("no account id mapping exists") {
            inMemoryMapper.clear()
            inMemoryMapper.createCoreMapping(EntityIdentifier.CONTACT, juniferContactId, contactId)
            `when`("update account contact") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.updateAccountContact(request)
                }

                then("account contact not updated") {
                    coVerify(exactly = 0) {
                        mockAccountService.updateAccountContact(
                            juniferAccountId, juniferContactId, request
                        )
                    }
                }
            }
        }
        and("no contact id mapping exists") {
            inMemoryMapper.clear()
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)
            `when`("update account contact") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.updateAccountContact(request)
                }

                then("account contact not updated") {
                    coVerify(exactly = 0) {
                        mockAccountService.updateAccountContact(
                            juniferAccountId, juniferContactId, request
                        )
                    }
                }
            }
        }
    }

    given("create account review period request") {

        val request = createAccountReviewPeriodRequest
        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer is successful") {
                coEvery {
                    mockAccountService.createAccountReviewPeriod(
                        juniferAccountId.toLong(),
                        request
                    )
                } returns juniferCreateAccountReviewPeriodResponse

                `when`("create account review period") {
                    val result = sut.createAccountReviewPeriod(request)

                    then("account review period created") {
                        result shouldBe createAccountReviewPeriodResponse

                        coVerify {
                            mockAccountService.createAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                request
                            )
                        }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.createAccountReviewPeriod(juniferAccountId.toLong(), request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("create account review period") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createAccountReviewPeriod(request)
                    }

                    then("account review period not created") {
                        coVerify {
                            mockAccountService.createAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                request
                            )
                        }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.createAccountReviewPeriod(juniferAccountId.toLong(), request)
                } throws RuntimeException()

                `when`("create account review period") {
                    assertThrows<UnknownGrpcException> {
                        sut.createAccountReviewPeriod(request)
                    }

                    then("account review period not created") {
                        coVerify {
                            mockAccountService.createAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                request
                            )
                        }
                    }
                }
            }
        }

        and("no account id mapping exists") {
            inMemoryMapper.clear()

            `when`("create account review period") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createAccountReviewPeriod(request)
                }

                then("account review period not created") {
                    coVerify(exactly = 0) {
                        mockAccountService.createAccountReviewPeriod(
                            juniferAccountId.toLong(),
                            request
                        )
                    }
                }
            }
        }
    }

    given("cancel account review period request") {

        val request = cancelAccountReviewPeriodRequest
        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer is successful") {
                coJustRun {
                    mockAccountService.cancelAccountReviewPeriod(
                        juniferAccountId.toLong(),
                        ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                    )
                }

                `when`("cancel account review period") {
                    val result = sut.cancelAccountReviewPeriod(request)

                    then("account review period cancelled") {
                        result shouldBe Empty.getDefaultInstance()

                        coVerify {
                            mockAccountService.cancelAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                            )
                        }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.cancelAccountReviewPeriod(
                        juniferAccountId.toLong(),
                        ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                    )
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("cancel account review period") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.cancelAccountReviewPeriod(request)
                    }

                    then("account review period not cancelled") {
                        coVerify {
                            mockAccountService.cancelAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                            )
                        }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.cancelAccountReviewPeriod(
                        juniferAccountId.toLong(),
                        ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                    )
                } throws RuntimeException()

                `when`("cancel account review period") {
                    assertThrows<UnknownGrpcException> {
                        sut.cancelAccountReviewPeriod(request)
                    }

                    then("account review period not cancelled") {
                        coVerify {
                            mockAccountService.cancelAccountReviewPeriod(
                                juniferAccountId.toLong(),
                                ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                            )
                        }
                    }
                }
            }
        }

        and("no account id mapping exists") {
            inMemoryMapper.clear()

            `when`("cancel account review period") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.cancelAccountReviewPeriod(request)
                }

                then("account review period not cancelled") {
                    coVerify(exactly = 0) {
                        mockAccountService.cancelAccountReviewPeriod(
                            juniferAccountId.toLong(),
                            ACCOUNT_REVIEW_PERIOD_JUNIFER_ID
                        )
                    }
                }
            }
        }
    }

    given("apply change of tenancy exit fees request") {

        val request = applyExitFeesRequest
        and("entity mapping exists") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            and("call to junifer returns no content") {
                coJustRun { mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request) }

                `when`("apply change of tenancy exit fees") {
                    val result = sut.applyChangeOfTenancyExitFees(request)

                    then("exit fees applied") {
                        result shouldBe Empty.getDefaultInstance()

                        coVerify { mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request) }
                    }
                }
            }

            and("call to junifer throws exception") {
                coEvery {
                    mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request)
                } throws JuniferException("ReadingDtBeforeSSD", "", "")

                `when`("apply change of tenancy exit fees") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.applyChangeOfTenancyExitFees(request)
                    }

                    then("exit fees not applied") {
                        coVerify { mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request) }
                    }
                }
            }

            and("internal service exception") {
                coEvery {
                    mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request)
                } throws RuntimeException()

                `when`("apply change of tenancy exit fees") {
                    assertThrows<UnknownGrpcException> {
                        sut.applyChangeOfTenancyExitFees(request)
                    }

                    then("exit fees not applied") {
                        coVerify { mockAccountService.applyChangeOfTenancyExitFees(juniferAccountId.toLong(), request) }
                    }
                }
            }
        }

        and("no mapping exists") {
            inMemoryMapper.clear()

            `when`("apply change of tenancy exit fees") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.applyChangeOfTenancyExitFees(request)
                }

                then("exit fees not applied") {
                    coVerify(exactly = 0) {
                        mockAccountService.applyChangeOfTenancyExitFees(
                            juniferAccountId.toLong(),
                            request
                        )
                    }
                }
            }
        }
    }

    given("get tickets for an account request") {
        val billingAccountId = "1"
        val request = getTicketsForAccountNumberRequest {
            accountId = billingAccountId.toLong()
            status = Status.Open
            includeIndirect = false
        }

        and("entity mapping exists ") {
            val juniferAccountId = "12"
            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, billingAccountId)

            `when`("the call to junifer returns a response") {
                val juniferResponse = JuniferGetAccountTicketsResponse(
                    results = listOf(
                        AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 1L),
                        AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 2L),
                        AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 3L),
                    )
                )
                coEvery {
                    mockAccountService.getTicketsForAccountNumber(juniferAccountId.toLong(), request)
                } returns juniferResponse

                val result = sut.getTicketsForAccountNumber(request)

                then("the tickets are retrieved") {
                    coVerify {
                        mockAccountService.getTicketsForAccountNumber(juniferAccountId.toLong(), request)
                    }
                    result.ticketsCount shouldBe 3

                    with(result.ticketsList) {
                        this[0].id shouldBe 1L
                        this[1].id shouldBe 2L
                        this[2].id shouldBe 3L
                    }
                }
            }
            `when`("the call to junifer throws an exception") { // no tickets found
                coEvery {
                    mockAccountService.getTicketsForAccountNumber(juniferAccountId.toLong(), request)
                } throws JuniferException("TicketStatusNotRecognised", "", "")

                val exception = assertThrows<InvalidArgumentGrpcException> {
                    sut.getTicketsForAccountNumber(request)
                }

                then("no tickets are retrieved ") {
                    coVerify { mockAccountService.getTicketsForAccountNumber(juniferAccountId.toLong(), request) }
                    exception.errorCode shouldBe "TicketStatusNotRecognised"
                }
            }
        }
        and("no mapping exists for this ID") {
            inMemoryMapper.clear()

            `when`("the getTicketsForAccountNumber method is invoked") { // no account
                assertThrows<FailedPreconditionGrpcException> {
                    sut.getTicketsForAccountNumber(request)
                }

                then("no tickets are retrieved") {
                    coVerify(exactly = 0) {
                        mockAccountService.getTicketsForAccountNumber(juniferAccountId.toLong(), request)
                    }
                }
            }
        }
    }

    given("get tickets by junifer account number request") {
        val juniferAccNumber = "********"
        val request = getTicketsByJuniferAccountNumberRequest {
            juniferAccountNumber = juniferAccNumber
            status = Status.Open
            includeIndirect = false
        }

        `when`("getTicketsByJuniferAccountNumber is invoked") {
            val juniferResponse = JuniferGetAccountTicketsResponse(
                results = listOf(
                    AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 1L),
                    AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 2L),
                    AccountPrecannedData.juniferGetAccountTicketResponse.copy(id = 3L),
                )
            )
            coEvery {
                mockAccountService.getTicketsByJuniferAccountNumber(juniferAccNumber, request)
            } returns juniferResponse

            val result = sut.getTicketsByJuniferAccountNumber(request)

            then("the tickets are retrieved") {
                coVerify {
                    mockAccountService.getTicketsByJuniferAccountNumber(juniferAccNumber, request)
                }
                result.ticketsCount shouldBe 3

                with(result.ticketsList) {
                    this[0].id shouldBe 1L
                    this[1].id shouldBe 2L
                    this[2].id shouldBe 3L
                }
            }
        }
        `when`("the call to junifer throws an exception") {
            coEvery {
                mockAccountService.getTicketsByJuniferAccountNumber(juniferAccNumber, request)
            } throws JuniferException("TicketStatusNotRecognised", "", "")

            val exception = assertThrows<InvalidArgumentGrpcException> {
                sut.getTicketsByJuniferAccountNumber(request)
            }

            then("no tickets are retrieved ") {
                coVerify { mockAccountService.getTicketsByJuniferAccountNumber(juniferAccNumber, request) }
                exception.errorCode shouldBe "TicketStatusNotRecognised"
            }
        }
    }


    given("a customer wishes to wish change mode to smart pay as you go (PAYG)") {
        val now = LocalDateTime.now().toTimestamp()
        val juniferAccountId = AccountCreditPrecannedData.ACCOUNT_ID

        and("entity mapping exists") {

            inMemoryMapper.createCoreMapping(EntityIdentifier.BILLING_ACCOUNT, juniferAccountId, BILLING_ACCOUNT_ID)

            `when`("submitting to junifer with one meterpoint") {
                val request = updateAccountToSmartPayAsYouGoRequest {
                    billingAccountNumber = BILLING_ACCOUNT_NUMBER
                    requests.addAll(
                        listOf(
                            meterPointChangeOfModeRequest {
                                meterPointIdentifier = "********"
                                executionDttm = now
                                productReference = "SPAYG"
                            }
                        )
                    )
                }

                val juniferResponse = updateAccountToSmartPayAsYouGoResponse {
                    meterPoints.addAll(
                        listOf(
                            meterPointChangeOfModeResponse {
                                meterPointIdentifier = "********"
                                creditToPaygOrchestrationTicketId = "********"
                                configurePanTicketId = "********"
                                productBundleCreatedId = ********

                            },
                        )
                    )
                }

                coEvery {
                    mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
                } returns juniferResponse

                val response = sut.updateAccountToSmartPayAsYouGo(request)
                then("should have a response") {
                    coVerify(exactly = 1) { mockAccountService.updateModeToSmartPayAsYouGo(any(), any()) }
                    response.meterPointsList.size shouldBe 1
                    val meterPointRequest = response.meterPointsList.first()
                    meterPointRequest.meterPointIdentifier shouldBe "********"
                    meterPointRequest.productBundleCreatedId shouldBe ********
                }
            }

            `when`("submitting to junifer with two meterpoint") {
                val request = updateAccountToSmartPayAsYouGoRequest {
                    billingAccountNumber = BILLING_ACCOUNT_NUMBER
                    requests.addAll(
                        listOf(meterPointChangeOfModeRequest {
                            meterPointIdentifier = "********"
                            executionDttm = now
                            productReference = "SPAYG"
                        }, meterPointChangeOfModeRequest {
                            meterPointIdentifier = "********"
                            executionDttm = now
                            productReference = "SPAYG"
                        })
                    )
                }
                val juniferResponse = updateAccountToSmartPayAsYouGoResponse {
                    meterPoints.addAll(
                        listOf(
                            meterPointChangeOfModeResponse {
                                meterPointIdentifier = "********"
                                creditToPaygOrchestrationTicketId = "********"
                                configurePanTicketId = "********"
                                productBundleCreatedId = ********

                            },
                            meterPointChangeOfModeResponse {
                                meterPointIdentifier = "********"
                                creditToPaygOrchestrationTicketId = "********"
                                configurePanTicketId = "********"
                                productBundleCreatedId = ********

                            },
                        )
                    )
                }
                coEvery {
                    mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
                } returns juniferResponse

                val response = sut.updateAccountToSmartPayAsYouGo(request)

                then("should have a response") {
                    coVerify(exactly = 1) { mockAccountService.updateModeToSmartPayAsYouGo(any(), any()) }
                    response.meterPointsList.size shouldBe 2
                }
            }

            `when`("submitting to junifer with no request") {
                val request = updateAccountToSmartPayAsYouGoRequest {
                    billingAccountNumber = BILLING_ACCOUNT_NUMBER
                }

                val exception = assertFailsWith<GrpcException> { sut.updateAccountToSmartPayAsYouGo(request) }

                then("should throw an IllegalArgumentException") {
                    coVerify(exactly = 0) { mockAccountService.updateModeToSmartPayAsYouGo(any(), any()) }
                    exception.message shouldBe "[updateCustomerAccountToPayAsYouGo] Customer's meterPoints were not provided for account number: 100"
                }
            }
        }
        and("call to junifer throws exception") {
            val request = updateAccountToSmartPayAsYouGoRequest {
                billingAccountNumber = BILLING_ACCOUNT_NUMBER
                requests.addAll(
                    listOf(meterPointChangeOfModeRequest {
                        meterPointIdentifier = "********"
                        executionDttm = now
                        productReference = "SPAYG"
                    })
                )
            }
            coEvery {
                mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
            } throws JuniferException("409", "", "accountId already requested")

            `when`("registering an account for smart payas you go") {
                assertThrows<UnknownGrpcException> {
                    sut.updateAccountToSmartPayAsYouGo(request)
                }

                then("already requested") {
                    coVerify {
                        mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
                    }
                }
            }
        }
        and("internal service exception") {
            val request = updateAccountToSmartPayAsYouGoRequest {
                billingAccountNumber = BILLING_ACCOUNT_NUMBER
                requests.addAll(
                    listOf(meterPointChangeOfModeRequest {
                        meterPointIdentifier = "********"
                        executionDttm = now
                        productReference = "SPAYG"
                    })
                )
            }
            coEvery {
                mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
            } throws RuntimeException()

            `when`("PAYG request") {
                assertThrows<UnknownGrpcException> {
                    sut.updateAccountToSmartPayAsYouGo(request)
                }

                then("request not created") {
                    coVerify {
                        mockAccountService.updateModeToSmartPayAsYouGo(any(), any())
                    }
                }
            }
        }
    }

})
