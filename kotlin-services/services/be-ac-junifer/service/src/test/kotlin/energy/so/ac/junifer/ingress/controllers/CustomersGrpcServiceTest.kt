package energy.so.ac.junifer.ingress.controllers

import com.google.protobuf.NullValue.NULL_VALUE
import energy.so.ac.junifer.egress.database.repositories.JuniferConsentRepository
import energy.so.ac.junifer.fixtures.AccountPrecannedData.individualJuniferAccount
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferAccountResponse
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.getCustomerConsentResponse
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequest
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequestV2Elec
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequestV2ForElec
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequestV2ForGas
import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData.updateCustomerConsentRequestV2Gas
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CONSENT_FROM_DATE
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.FIRST_NAME
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOMER_PSR_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_CUSTOM_VULNERABILITY_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.JUNIFER_METERPOINT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.METERPOINT
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.METERPOINT_ID
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.enrollCustomerProto
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.getCustomerData
import energy.so.ac.junifer.fixtures.CustomerPrecannedData.updatePrimaryContactProto
import energy.so.ac.junifer.fixtures.CustomerPsrPrecannedData.FROM_DATE
import energy.so.ac.junifer.fixtures.EnrolAdditionalAccountPrecannedData.enrolNewAccountRequest
import energy.so.ac.junifer.fixtures.EnrolAdditionalAccountPrecannedData.enrolNewAccountResponse
import energy.so.ac.junifer.fixtures.InMemoryJuniferCoreIdMapper
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.models.accounts.JuniferAccountResponse
import energy.so.ac.junifer.ingress.models.accounts.JuniferAccountsResponse
import energy.so.ac.junifer.ingress.models.customers.JuniferCreateCustomerPsrResponse
import energy.so.ac.junifer.ingress.models.customers.JuniferCreateCustomerVulnerabilityResponse
import energy.so.ac.junifer.ingress.models.customers.JuniferEnrollCustomerResponse
import energy.so.ac.junifer.ingress.models.customers.UpdateCustomerDto
import energy.so.ac.junifer.ingress.models.customers.toUpdateCustomerDto
import energy.so.ac.junifer.ingress.services.JuniferAccountService
import energy.so.ac.junifer.ingress.services.JuniferCustomerService
import energy.so.ac.junifer.ingress.services.feature.FeatureService
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.v1.customers.createCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.createCustomerVulnerabilityRequest
import energy.so.ac.junifer.v1.customers.deleteCustomerPsrRequest
import energy.so.ac.junifer.v1.customers.getCustomerConsentsRequest
import energy.so.ac.junifer.v1.customers.getCustomerRequest
import energy.so.ac.junifer.v1.customers.updateCustomerRequest
import energy.so.ac.junifer.v1.customers.updatePrimaryContactRequest
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.grpc.utils.toNullableTimestamp
import energy.so.commons.grpc.utils.toTimestamp
import energy.so.commons.model.tables.pojos.Junifer_Consent
import energy.so.commons.nullableTimestamp
import energy.so.commons.v2.dtos.idRequest
import energy.so.commons.v2.dtos.idRequestStr
import energy.so.users.v2.FeatureName
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.OffsetDateTime
import org.junit.jupiter.api.assertThrows
import kotlin.test.fail

private const val CORE_ID = "999"
private const val PROPERTY_ID = "1"

class CustomersGrpcServiceTest : BehaviorSpec({

    val mockCustomerService = mockk<JuniferCustomerService>()
    val mockAccountService = mockk<JuniferAccountService>()
    val mockConsentRepo = mockk<JuniferConsentRepository>()
    val inMemoryMapper = InMemoryJuniferCoreIdMapper()
    val mockFeatureService = mockk<FeatureService> {
        coEvery { isFeatureEnabled(FeatureName.TMP_SO_24805_RETRY_SETTLEMENT_CONSENT_ORDER) } returns true
    }

    val sut = CustomersGrpcService(
        mockCustomerService,
        mockAccountService,
        mockConsentRepo,
        inMemoryMapper,
        mockFeatureService
    )

    afterTest {
        confirmVerified(mockCustomerService, mockAccountService, mockConsentRepo)
        clearMocks(mockCustomerService, mockAccountService, mockConsentRepo)
    }

    given("customer with an existing mapping") {
        val coreId = CORE_ID
        val updateRequest = updateCustomerRequest {
            id = coreId
            firstName = FIRST_NAME
        }
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
        coJustRun {
            mockCustomerService.updateCustomer(
                JUNIFER_CUSTOMER_ID.toString(),
                updateRequest.toUpdateCustomerDto()
            )
        }

        val requestSlot = slot<UpdateCustomerDto>()

        coJustRun {
            mockCustomerService.updateCustomer(
                JUNIFER_CUSTOMER_ID.toString(),
                capture(requestSlot)
            )
        }
        `when`("call updateCustomer method") {
            sut.updateCustomer(updateRequest)
            then("update the customer") {
                coVerify {
                    mockCustomerService.updateCustomer(
                        JUNIFER_CUSTOMER_ID.toString(),
                        updateRequest.toUpdateCustomerDto()
                    )
                }

                val request = requestSlot.captured
                request.id shouldBe coreId
                request.firstName shouldBe FIRST_NAME
                request.title shouldBe null
                request.lastName shouldBe null
            }
        }

        `when`("call updateCustomer method when has no update") {
            sut.updateCustomer(updateCustomerRequest {
                id = coreId
            })
            then("update the customer") {
                coVerify(exactly = 0) {
                    mockCustomerService.updateCustomer(
                        JUNIFER_CUSTOMER_ID.toString(),
                        updateRequest.toUpdateCustomerDto()
                    )
                }

                val request = requestSlot.captured
                request.id shouldBe coreId
            }
        }
    }

    given("a customer with no existing mapping") {
        inMemoryMapper.clear()
        val coreId = CORE_ID
        val updateRequest = updateCustomerRequest {
            id = coreId
            firstName = FIRST_NAME
        }
        `when`("update the customer") {
            then("throw an IllegalStateException") {
                shouldThrow<IllegalStateException> { sut.updateCustomer(updateRequest) }
            }
        }
    }

    given("a contact corresponding to a customer with an existing mapping") {
        val updatePrimaryContactRequest = updatePrimaryContactProto
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.CUSTOMER,
            updatePrimaryContactRequest.customerId,
            CUSTOMER_ID.toString()
        )
        coJustRun {
            mockCustomerService.updatePrimaryContact(
                updatePrimaryContactRequest.customerId,
                updatePrimaryContactRequest
            )
        }
        `when`("update the primary contact") {
            sut.updatePrimaryContact(updatePrimaryContactRequest)
            then("update the contact") {
                coVerify {
                    mockCustomerService.updatePrimaryContact(
                        updatePrimaryContactRequest.customerId,
                        updatePrimaryContactRequest
                    )
                }
            }
        }
    }

    given("a contact corresponding to a customer without an existing mapping") {
        inMemoryMapper.clear()
        val updatePrimaryContactRequest = updatePrimaryContactProto
        `when`("update the primary contact") {
            then("throw an IllegalStateException") {
                shouldThrow<IllegalStateException> { sut.updatePrimaryContact(updatePrimaryContactRequest) }
            }
        }
    }

    given("a contact which has no data to update") {
        val coreId = CORE_ID
        val updatePrimaryContactRequest = updatePrimaryContactRequest {
            customerId = coreId
        }
        `when`("update the primary contact") {
            sut.updatePrimaryContact(updatePrimaryContactRequest)

            then("do nothing/no error thrown") {
                coVerify(exactly = 0) {
                    mockCustomerService.updatePrimaryContact(any(), any())
                }
            }
        }
    }

    given("given a customer psr with an existing mapping") {
        val coreId = "999"
        val createCustomerPrsRequest = createCustomerPsrRequest {
            customerId = "999"
            value = "value"
            utilityMarket = "utility"
            propertyTblId = 1
        }

        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(), coreId)
        inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)
        coEvery {
            mockCustomerService.createCustomerPsr(
                JUNIFER_CUSTOMER_ID.toString(),
                createCustomerPrsRequest
            )
        } returns JuniferCreateCustomerPsrResponse(id = JUNIFER_CUSTOMER_PSR_ID)

        `when`("create customer psr") {
            sut.createCustomerPsr(createCustomerPrsRequest)

            then("customer psr created") {
                coVerify {
                    mockCustomerService.createCustomerPsr(
                        JUNIFER_CUSTOMER_ID.toString(),
                        createCustomerPrsRequest
                    )
                }
            }
        }

        and("call to junifer throws exception") {
            coEvery {
                mockCustomerService.createCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), createCustomerPrsRequest)
            } throws JuniferException("ReadingDtBeforeSSD", "", "")
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)

            `when`("create customer psr") {

                then("no customer psr created") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createCustomerPsr(createCustomerPrsRequest)
                    }

                    coVerify {
                        mockCustomerService.createCustomerPsr(
                            JUNIFER_CUSTOMER_ID.toString(),
                            createCustomerPrsRequest
                        )
                    }
                }
            }
        }

        and("internal service exception") {
            coEvery {
                mockCustomerService.createCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), createCustomerPrsRequest)
            } throws RuntimeException()
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)

            `when`("create customer psr") {
                assertThrows<UnknownGrpcException> {
                    sut.createCustomerPsr(createCustomerPrsRequest)
                }

                then("no customer psr created") {
                    coVerify {
                        mockCustomerService.createCustomerPsr(
                            JUNIFER_CUSTOMER_ID.toString(),
                            createCustomerPrsRequest
                        )
                    }
                }
            }
        }
    }

    given("given a customer psr with no existing mapping") {
        val createCustomerPrsRequest = createCustomerPsrRequest {
            customerId = "999"
            value = "value"
            utilityMarket = "utility"
            propertyTblId = 1
        }
        inMemoryMapper.clear()

        `when`("update customer") {

            then("throw exception") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createCustomerPsr(createCustomerPrsRequest)
                }
                coVerify(exactly = 0) { mockCustomerService.createCustomerPsr(any(), any()) }

            }
        }
    }

    given("given a customer psr with customer but no property mapping") {
        val createCustomerPrsRequest = createCustomerPsrRequest {
            customerId = "999"
            value = "value"
            utilityMarket = "utility"
            propertyTblId = 1
        }
        inMemoryMapper.clear()
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), CORE_ID)

        `when`("update customer") {

            then("throw exception") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.createCustomerPsr(createCustomerPrsRequest)
                }
                coVerify(exactly = 0) { mockCustomerService.createCustomerPsr(any(), any()) }

            }
        }
    }

    given("delete a customer psr with an existing mapping") {
        val coreId = "999"
        val psrCoreId = "88"
        val deleteCustomerPrsRequest = deleteCustomerPsrRequest {
            customerId = "999"
            psrId = psrCoreId.toLong()
        }
        coEvery {
            mockCustomerService.deleteCustomerPsr(
                JUNIFER_CUSTOMER_ID.toString(),
                JUNIFER_CUSTOMER_PSR_ID
            )
        } returns Unit
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(), psrCoreId)

        `when`("delete customer psr") {
            sut.deleteCustomerPsr(deleteCustomerPrsRequest)

            then("customer psr created") {
                coVerify {
                    mockCustomerService.deleteCustomerPsr(
                        JUNIFER_CUSTOMER_ID.toString(),
                        JUNIFER_CUSTOMER_PSR_ID
                    )
                }
            }
        }

        and("call to junifer throws exception") {
            coEvery {
                mockCustomerService.deleteCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), JUNIFER_CUSTOMER_PSR_ID)
            } throws JuniferException("ReadingDtBeforeSSD", "", "")
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(),  psrCoreId)

            `when`("delete customer psr") {

                then("no customer psr deleted") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.deleteCustomerPsr(deleteCustomerPsrRequest {
                            customerId = coreId
                            psrId = psrCoreId.toLong() })
                    }

                    coVerify {
                        mockCustomerService.deleteCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), JUNIFER_CUSTOMER_PSR_ID)
                    }
                }
            }
        }

        and("internal service exception") {
            coEvery {
                mockCustomerService.deleteCustomerPsr(JUNIFER_CUSTOMER_ID.toString(), JUNIFER_CUSTOMER_PSR_ID)
            } throws RuntimeException()
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), coreId)
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER_PSR, JUNIFER_CUSTOMER_PSR_ID.toString(), psrCoreId)

            `when`("delete customer psr") {
                assertThrows<UnknownGrpcException> {
                    sut.deleteCustomerPsr(deleteCustomerPsrRequest {
                        customerId = coreId
                        psrId = psrCoreId.toLong() })
                }

                then("no customer psr deleted") {
                    coVerify {
                        mockCustomerService.deleteCustomerPsr(
                            JUNIFER_CUSTOMER_ID.toString(),
                            JUNIFER_CUSTOMER_PSR_ID
                        )
                    }
                }
            }
        }
    }

    given("delete a customer psr with no existing mapping") {
        val deleteCustomerPrsRequest = deleteCustomerPsrRequest {
            customerId = "999"
            psrId = 88
        }
        inMemoryMapper.clear()

        `when`("delete customer psr") {

            then("throw exception") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.deleteCustomerPsr(deleteCustomerPrsRequest)
                }
                coVerify(exactly = 0) { mockCustomerService.deleteCustomerPsr(any(), any()) }

            }
        }
    }

    given("customer to enroll") {
        val enrollCustomer = enrollCustomerProto
        coEvery { mockCustomerService.enrollCustomer(enrollCustomer) } returns
                JuniferEnrollCustomerResponse(JUNIFER_CUSTOMER_ID)
        coEvery { mockCustomerService.getCustomer(JUNIFER_CUSTOMER_ID.toString()) } returns getCustomerData
        coEvery { mockAccountService.getAccountsForCustomerId(JUNIFER_CUSTOMER_ID.toString()) } returns juniferAccountResponse

        `when`("enroll customer") {
            val enrolledCustomer = sut.enrollCustomer(enrollCustomer)

            then("customer enrolled") {
                enrolledCustomer.customer shouldNotBe null
                enrolledCustomer.account shouldNotBe null

                coVerify { mockCustomerService.enrollCustomer(enrollCustomer) }
                coVerify { mockCustomerService.getCustomer(JUNIFER_CUSTOMER_ID.toString()) }
                coVerify { mockAccountService.getAccountsForCustomerId(JUNIFER_CUSTOMER_ID.toString()) }
            }
        }

        and("internal service exception") {
            coEvery { mockCustomerService.getCustomer(JUNIFER_CUSTOMER_ID.toString()) } returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(JUNIFER_CUSTOMER_ID.toString()) } returns
                    JuniferAccountsResponse(
                        listOf(
                            JuniferAccountResponse(
                                id = JUNIFER_BILLING_ACCOUNT_ID,
                                currency = "GBP",
                                type = "type",
                                name = "name",
                                number = "123"
                            )
                        )
                    )
            coEvery {
                mockCustomerService.enrollCustomer(enrollCustomer)
            } throws RuntimeException()
            `when`("enroll customer") {
                assertThrows<UnknownGrpcException> {
                    sut.enrollCustomer(enrollCustomer)
                }
                then("no customer enrolled") {
                    coVerify { mockCustomerService.enrollCustomer(enrollCustomer) }
                }
            }
        }

        and("no result accounts") {
            coEvery { mockCustomerService.getCustomer(JUNIFER_CUSTOMER_ID.toString()) } returns getCustomerData
            coEvery { mockAccountService.getAccountsForCustomerId(JUNIFER_CUSTOMER_ID.toString()) } returns JuniferAccountsResponse()
            coEvery { mockCustomerService.enrollCustomer(enrollCustomer) } returns
                    JuniferEnrollCustomerResponse(JUNIFER_CUSTOMER_ID)

            `when`("enroll customer") {
                assertThrows<FailedPreconditionGrpcException> {
                    sut.enrollCustomer(enrollCustomer)
                }

                then("no customer enrolled") {
                    coVerify { mockCustomerService.enrollCustomer(enrollCustomer) }
                    coVerify { mockCustomerService.getCustomer(JUNIFER_CUSTOMER_ID.toString()) }
                    coVerify { mockAccountService.getAccountsForCustomerId(JUNIFER_CUSTOMER_ID.toString()) }
                }
            }
        }
    }

    given("customer to get") {
        val customerResponse = getCustomerData
        val id = JUNIFER_CUSTOMER_ID.toString()

        `when`("get customer") {
            then("throw FailedPreconditionGrpcException") {
                shouldThrow<FailedPreconditionGrpcException> { sut.getCustomer(getCustomerRequest { customerId = id }) }
            }
        }

        and("customer has internal mapping") {
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), id)
            coEvery { mockCustomerService.getCustomer(id) } returns customerResponse
            `when`("get customer") {
                sut.getCustomer(getCustomerRequest { customerId = id })
                then("customer retrieved") {
                    coVerify { mockCustomerService.getCustomer(id) }
                }
            }
        }
    }

    given("customer consents to get") {
        val customerConsentResponse = getCustomerConsentResponse
        val customerId = JUNIFER_CUSTOMER_ID
        inMemoryMapper.clear()

        `when`("get customer consents") {
            then("throw FailedPreconditionGrpcException") {
                shouldThrow<FailedPreconditionGrpcException> { sut.getCustomerConsents(idRequest { id = customerId }) }
                coVerify(exactly = 0) { mockCustomerService.getCustomerConsent(customerId.toString()) }
            }
        }

        and("customer has internal mapping") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString(),
                customerId.toString()
            )
            coEvery { mockCustomerService.getCustomerConsent(customerId.toString()) } returns customerConsentResponse
            `when`("get customer's consents") {
                sut.getCustomerConsents(idRequest { id = customerId })
                then("customer's consents retrieved") {
                    coVerify { mockCustomerService.getCustomerConsent(customerId.toString()) }
                }
            }
        }
    }

    given("customer consents to get by meterPoint") {
        val customerConsentResponse = getCustomerConsentResponse
        val meterPoint = METERPOINT
        inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, "22", meterPoint)
        coEvery { mockCustomerService.getCustomerConsentV2(meterPoint) } returns customerConsentResponse
        `when`("get customer's consents") {
            sut.getCustomerConsentsV2(getCustomerConsentsRequest {
                meterPointId = meterPoint.toLong()
                validDt = nullableTimestamp { null_ = NULL_VALUE }
            })
            then("customer's consents retrieved") {
                coVerify { mockCustomerService.getCustomerConsentV2(meterPoint) }
            }
        }
    }

    given("customer consents to get by meterPoint and validDt") {
        val customerConsentResponse = getCustomerConsentResponse
        val meterPoint = METERPOINT
        val validDt = LocalDate.now()

        inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, "22", meterPoint)
        coEvery {
            mockCustomerService.getCustomerConsentV2(meterPoint, validDt.toString())
        } returns customerConsentResponse
        `when`("get customer's consents") {
            sut.getCustomerConsentsV2(getCustomerConsentsRequest {
                meterPointId = meterPoint.toLong()
                this.validDt = validDt.toNullableTimestamp()
            })
            then("customer's consents retrieved") {
                coVerify { mockCustomerService.getCustomerConsentV2(meterPoint, validDt.toString()) }
            }
        }
    }

    given("customer consents to get by junifer meterPoint id") {
        val customerConsentResponse = getCustomerConsentResponse
        val meterPoint = METERPOINT
        inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, "22", meterPoint)
        `when`("get customer's consents") {
            coEvery { mockCustomerService.getCustomerConsentV2(meterPoint) } returns customerConsentResponse
            sut.getCustomerConsentsV2ByJuniferId(idRequestStr { id = meterPoint })
            then("customer's consents retrieved") {
                coVerify { mockCustomerService.getCustomerConsentV2(meterPoint) }
            }
        }
        `when`("get customer's consents throws exception") {
            coEvery { mockCustomerService.getCustomerConsentV2("23") } throws RuntimeException("DummyException")
            then("customer's consents retrieved") {
                shouldThrow<UnknownGrpcException> { sut.getCustomerConsentsV2ByJuniferId(idRequestStr { id = "23" }) }
                coVerify { mockCustomerService.getCustomerConsentV2("23") }
            }
        }
    }

    given("a request to retrieve a history of consents across a meterpoint") {

        inMemoryMapper.createCoreMapping(EntityIdentifier.METER_POINT, "22", METERPOINT)

        `when`("queried") {

            val from = OffsetDateTime.now().minusDays(30)
            val to = OffsetDateTime.now().plusDays(30)

            every { mockConsentRepo.findReadFrequencyConsentHistory(22L) } returns listOf(
                Junifer_Consent(
                    id = 1L,
                    consentdefinitionfk = 1L,
                    setting = "Half Hourly",
                    fromdttm = from,
                    todttm = to,
                ),
                Junifer_Consent(
                    id = 2L,
                    consentdefinitionfk = 1L,
                    setting = "Daily",
                    fromdttm = from,
                    todttm = to,
                )
            )

            val response = sut.getMeterPointConsentHistory(getCustomerConsentsRequest {
                meterPointId = METERPOINT.toLong()
                validDt = nullableTimestamp { null_ = NULL_VALUE }
            })

            then("return the consent history") {

                verify { mockConsentRepo.findReadFrequencyConsentHistory(22L) }

                response.consentsCount shouldBe 2
                response.consentsList.forEach {
                    when (it.id) {
                        "1" -> {
                            it.setting shouldBe "Half Hourly"
                            it.consentDefinition shouldBe "1"
                            it.fromDt shouldBe from.toTimestamp()
                            it.toDt shouldBe to.toNullableTimestamp()
                        }

                        "2" -> {
                            it.setting shouldBe "Daily"
                            it.consentDefinition shouldBe "1"
                            it.fromDt shouldBe from.toTimestamp()
                            it.toDt shouldBe to.toNullableTimestamp()
                        }

                        else -> fail("Unexpected setting ID found")
                    }
                }
            }
        }
    }

    given("customer consent to update") {
        val customerId = JUNIFER_CUSTOMER_ID
        inMemoryMapper.clear()

        `when`("update customer consent") {
            then("throw FailedPreconditionGrpcException") {
                shouldThrow<FailedPreconditionGrpcException> { sut.updateCustomerConsents(updateCustomerConsentRequest) }
                coVerify(exactly = 0) {
                    mockCustomerService.updateCustomerConsent(
                        customerId.toString(),
                        updateCustomerConsentRequest
                    )
                }
            }
        }

        and("customer has internal mapping") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.CUSTOMER,
                JUNIFER_CUSTOMER_ID.toString(),
                customerId.toString()
            )
            coJustRun { mockCustomerService.updateCustomerConsent(customerId.toString(), updateCustomerConsentRequest) }
            `when`("update customer's consents") {
                sut.updateCustomerConsents(updateCustomerConsentRequest)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            customerId.toString(),
                            updateCustomerConsentRequest
                        )
                    }
                }
            }
        }
    }

    given("customer consent  to update - v2") {
        inMemoryMapper.clear()

        `when`("update customer consent") {
            then("throw FailedPreconditionGrpcException") {
                shouldThrow<FailedPreconditionGrpcException> {
                    sut.updateCustomerConsentsV2(
                        updateCustomerConsentRequestV2ForGas
                    )
                }
                coVerify(exactly = 0) {
                    mockCustomerService.updateCustomerConsent(
                        juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                        freq = "setting",
                        consentDefinition = "MHHS Billing",
                        fromDt = CONSENT_FROM_DATE
                    )
                }
            }
        }

        and("customer has internal mapping for gas") {

            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                JUNIFER_METERPOINT_ID.toString(),
                METERPOINT_ID.toString()
            )
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                    freq = "setting",
                    consentDefinition = "MPRN Billing",
                    fromDt = CONSENT_FROM_DATE
                )
            }
            `when`("update customer's consents") {
                sut.updateCustomerConsentsV2(updateCustomerConsentRequestV2ForGas)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                            freq = "setting",
                            consentDefinition = "MPRN Billing",
                            fromDt = CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }
        and("customer has internal mapping for elec") {

            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                JUNIFER_METERPOINT_ID.toString(),
                METERPOINT_ID.toString()
            )
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                    freq = "setting",
                    consentDefinition = "MHHS Billing",
                    fromDt = CONSENT_FROM_DATE
                )
            }

            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                    freq = "setting",
                    consentDefinition = "MHHS Settlement",
                    fromDt = CONSENT_FROM_DATE
                )
            }

            `when`("update customer's consents") {
                sut.updateCustomerConsentsV2(updateCustomerConsentRequestV2ForElec)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                            freq = "setting",
                            consentDefinition = "MHHS Billing",
                            fromDt = CONSENT_FROM_DATE
                        )
                    }
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            juniferMeterPt = JUNIFER_METERPOINT_ID.toString(),
                            freq = "setting",
                            consentDefinition = "MHHS Settlement",
                            fromDt = CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }

    }

    given("customer consent to update by meterpoint") {
        val customerId = JUNIFER_CUSTOMER_ID
        inMemoryMapper.clear()

        `when`("update customer consent") {
            then("throw FailedPreconditionGrpcException") {
                shouldThrow<FailedPreconditionGrpcException> {
                    sut.updateCustomerConsentsV2(
                        updateCustomerConsentRequestV2Elec
                    )
                }
                coVerify(exactly = 0) {
                    mockCustomerService.updateCustomerConsent(
                        updateCustomerConsentRequestV2Elec.meterPoint,
                        "Daily",
                        "MHHS Billing",
                        fromDt = FROM_DATE
                    )
                }
            }
        }

        and("customer has internal mapping - electric meter") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                "98797",
                updateCustomerConsentRequestV2Elec.meterPoint
            )
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    "98797",
                    updateCustomerConsentRequestV2Elec.setting,
                    "MHHS Billing",
                    CONSENT_FROM_DATE
                )
            }
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    "98797",
                    updateCustomerConsentRequestV2Elec.setting,
                    "MHHS Settlement",
                    CONSENT_FROM_DATE
                )
            }
            `when`("update customer's consents") {
                sut.updateCustomerConsentsV2(updateCustomerConsentRequestV2Elec)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            "98797",
                            updateCustomerConsentRequestV2Elec.setting,
                            "MHHS Billing",
                            CONSENT_FROM_DATE
                        )
                        mockCustomerService.updateCustomerConsent(
                            "98797",
                            updateCustomerConsentRequestV2Elec.setting,
                            "MHHS Settlement",
                            CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }

        and("customer has internal mapping - gas meter") {
            inMemoryMapper.createCoreMapping(
                EntityIdentifier.METER_POINT,
                "98797",
                updateCustomerConsentRequestV2Gas.meterPoint
            )
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    "98797",
                    updateCustomerConsentRequestV2Gas.setting,
                    "MPRN Billing",
                    CONSENT_FROM_DATE
                )
            }
            `when`("update customer's consents") {
                sut.updateCustomerConsentsV2(updateCustomerConsentRequestV2Gas)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            "98797",
                            updateCustomerConsentRequestV2Gas.setting,
                            "MPRN Billing",
                            CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }
    }

    given("customer consent to update by meterpoint without mapping") {
        and("customer has electric meter") {
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    "65488",
                    updateCustomerConsentRequestV2Elec.setting,
                    "MHHS Billing",
                    CONSENT_FROM_DATE
                )
            }
            coJustRun {
                mockCustomerService.updateCustomerConsent(
                    "65488",
                    updateCustomerConsentRequestV2Elec.setting,
                    "MHHS Settlement",
                    CONSENT_FROM_DATE
                )
            }
            `when`("update customer's consents") {
                sut.updateCustomerConsentsV2ByJuniferId(updateCustomerConsentRequestV2Elec)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            "65488",
                            updateCustomerConsentRequestV2Elec.setting,
                            "MHHS Billing",
                            CONSENT_FROM_DATE
                        )
                        mockCustomerService.updateCustomerConsent(
                            "65488",
                            updateCustomerConsentRequestV2Elec.setting,
                            "MHHS Settlement",
                            CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }

        and("customer has gas meter") {
            `when`("update customer's consents") {
                coJustRun {
                    mockCustomerService.updateCustomerConsent(
                        "65489",
                        updateCustomerConsentRequestV2Gas.setting,
                        "MPRN Billing",
                        CONSENT_FROM_DATE
                    )
                }
                sut.updateCustomerConsentsV2ByJuniferId(updateCustomerConsentRequestV2Gas)
                then("customer's consents updated") {
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            "65489",
                            updateCustomerConsentRequestV2Gas.setting,
                            "MPRN Billing",
                            CONSENT_FROM_DATE
                        )
                    }
                }
            }
            `when`("update customer's consents throws exception") {
                coEvery {
                    mockCustomerService.updateCustomerConsent(
                        "65489",
                        updateCustomerConsentRequestV2Gas.setting,
                        "MPRN Billing",
                        CONSENT_FROM_DATE
                    )
                } throws Exception("DummyException")
                then("customer's consents is not updated") {
                    shouldThrow<UnknownGrpcException> {
                        sut.updateCustomerConsentsV2ByJuniferId(
                            updateCustomerConsentRequestV2Gas
                        )
                    }
                    coVerify {
                        mockCustomerService.updateCustomerConsent(
                            "65489",
                            updateCustomerConsentRequestV2Gas.setting,
                            "MPRN Billing",
                            CONSENT_FROM_DATE
                        )
                    }
                }
            }
        }
    }

    given("a existing enrolled junifer customer") {

        val coreCustomerId = CUSTOMER_ID

        inMemoryMapper.clear()
        inMemoryMapper.createCoreMapping(
            EntityIdentifier.CUSTOMER,
            JUNIFER_CUSTOMER_ID.toString(),
            coreCustomerId.toString()
        )

        `when`("an additional account is enrolled") {

            coEvery {
                mockCustomerService.enrolAdditionalAccount(
                    JUNIFER_CUSTOMER_ID.toString(),
                    enrolNewAccountRequest
                )
            } returns enrolNewAccountResponse

            coEvery { mockAccountService.getAccount(JUNIFER_BILLING_ACCOUNT_ID) } returns individualJuniferAccount

            sut.enrolAdditionalAccount(enrolNewAccountRequest)

            then("the account should be enrolled") {
                coVerify {
                    mockCustomerService.enrolAdditionalAccount(
                        JUNIFER_CUSTOMER_ID.toString(),
                        enrolNewAccountRequest
                    )
                }
                coVerify { mockAccountService.getAccount(JUNIFER_BILLING_ACCOUNT_ID) }
            }
        }
    }

    given("given a customer vulnerability with an existing mapping") {
        val request = createCustomerVulnerabilityRequest {
            customerId = CORE_ID
            id = 1
            propertyTblId = 1
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
        }

        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), CORE_ID)
        inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)
        coEvery {
            mockCustomerService.createCustomerVulnerability(
                JUNIFER_CUSTOMER_ID.toString(),
                request
            )
        } returns JuniferCreateCustomerVulnerabilityResponse(id = JUNIFER_CUSTOM_VULNERABILITY_ID)

        `when`("create customer vulnerability") {
            sut.createCustomerVulnerability(request)

            then("customer vulnerability created") {
                coVerify {
                    mockCustomerService.createCustomerVulnerability(
                        JUNIFER_CUSTOMER_ID.toString(),
                        request
                    )
                }
            }
        }

        and("call to junifer throws exception") {
            coEvery {
                mockCustomerService.createCustomerVulnerability(JUNIFER_CUSTOMER_ID.toString(), request)
            } throws JuniferException("ReadingDtBeforeSSD", "", "")
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), CORE_ID)
            inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)

            `when`("create customer vulnerability") {

                then("no customer vulnerability created") {
                    assertThrows<InvalidArgumentGrpcException> {
                        sut.createCustomerVulnerability(request)
                    }

                    coVerify {
                        mockCustomerService.createCustomerVulnerability(
                            JUNIFER_CUSTOMER_ID.toString(),
                            request
                        )
                    }
                }
            }
        }

        and("internal service exception") {
            coEvery {
                mockCustomerService.createCustomerVulnerability(JUNIFER_CUSTOMER_ID.toString(), request)
            } throws RuntimeException()
            inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), CORE_ID)
            inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)

            `when`("create customer vulnerability") {
                assertThrows<UnknownGrpcException> {
                    sut.createCustomerVulnerability(request)
                }

                then("no customer vulnerability created") {
                    coVerify {
                        mockCustomerService.createCustomerVulnerability(
                            JUNIFER_CUSTOMER_ID.toString(),
                            request
                        )
                    }
                }
            }
        }
    }

    given("given a customer vulnerability with no customer mapping") {
        val request = createCustomerVulnerabilityRequest {
            customerId = "999"
            id = 1
            propertyTblId = 1
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
        }
        coEvery {
            mockCustomerService.createCustomerVulnerability(
                JUNIFER_CUSTOMER_ID.toString(),
                request
            )
        } returns JuniferCreateCustomerVulnerabilityResponse(id = JUNIFER_CUSTOM_VULNERABILITY_ID)

        inMemoryMapper.clear()
        inMemoryMapper.createCoreMapping(EntityIdentifier.PROPERTY, PROPERTY_ID, PROPERTY_ID)

        `when`("create customer vulnerability") {
            assertThrows<FailedPreconditionGrpcException> {
                sut.createCustomerVulnerability(request)
            }

            then("FailedPreconditionGrpcException is thrown") {
                coVerify(exactly = 0) {
                    mockCustomerService.createCustomerVulnerability(
                        JUNIFER_CUSTOMER_ID.toString(),
                        request
                    )
                }
            }
        }
    }

    given("given a customer vulnerability with no property mapping") {
        val request = createCustomerVulnerabilityRequest {
            customerId = CORE_ID
            id = 1
            propertyTblId = 1
            fromDt = LocalDate.now().toNullableTimestamp()
            fromDt = LocalDate.now().toNullableTimestamp()
        }
        coEvery {
            mockCustomerService.createCustomerVulnerability(
                JUNIFER_CUSTOMER_ID.toString(),
                request
            )
        } returns JuniferCreateCustomerVulnerabilityResponse(id = JUNIFER_CUSTOM_VULNERABILITY_ID)

        inMemoryMapper.clear()
        inMemoryMapper.createCoreMapping(EntityIdentifier.CUSTOMER, JUNIFER_CUSTOMER_ID.toString(), CORE_ID)

        `when`("create customer vulnerability") {
            assertThrows<FailedPreconditionGrpcException> {
                sut.createCustomerVulnerability(request)

            }
            then("FailedPreconditionGrpcException is thrown") {
                coVerify(exactly = 0) {
                    mockCustomerService.createCustomerVulnerability(
                        JUNIFER_CUSTOMER_ID.toString(),
                        request
                    )
                }
            }
        }
    }

})
