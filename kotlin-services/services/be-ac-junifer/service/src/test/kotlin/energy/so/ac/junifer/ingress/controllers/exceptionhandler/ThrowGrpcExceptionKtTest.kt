package energy.so.ac.junifer.ingress.controllers.exceptionhandler

import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.services.ENROL_ADDITIONAL_PROPERTY_URL
import energy.so.ac.junifer.ingress.services.ENROL_CUSTOMER_URL
import energy.so.ac.junifer.ingress.services.changeOfTenancy.COT_MOVE_OUT_ACCOUNT_URL
import energy.so.ac.junifer.ingress.services.changeOfTenancy.COT_PROPERTY_URL
import energy.so.commons.exceptions.dto.ErrorCategories
import energy.so.commons.exceptions.dto.ErrorCodes
import energy.so.commons.exceptions.grpc.EntityNotFoundGrpcException
import energy.so.commons.exceptions.grpc.FailedPreconditionGrpcException
import energy.so.commons.exceptions.grpc.InternalGrpcException
import energy.so.commons.exceptions.grpc.InvalidArgumentGrpcException
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.call.HttpClientCall
import io.ktor.client.request.HttpRequest
import io.ktor.client.statement.HttpResponse
import io.ktor.http.Headers
import io.ktor.http.HttpMethod
import io.ktor.http.HttpProtocolVersion
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.http.content.OutgoingContent
import io.ktor.http.encodedPath
import io.ktor.util.Attributes
import io.ktor.util.InternalAPI
import io.ktor.util.cio.ChannelIOException
import io.ktor.util.date.GMTDate
import io.ktor.utils.io.ByteReadChannel
import io.mockk.every
import io.mockk.mockk
import kotlin.coroutines.CoroutineContext

class ThrowGrpcExceptionKtTest : BehaviorSpec({

    val mockCall = mockk<HttpClientCall>()

    given("JuniferException is handled") {
        `when`("error category can be deduced from request url") {
            val exception = buildJuniferException(mockCall, "NotFound")
            and("url contains ENROL_CUSTOMER_URL") {
                every { mockCall.request } returns getDummyRequest(HttpMethod.Post, "/rest/v1$ENROL_CUSTOMER_URL")

                then("error category should be deduced from path") {
                    val handledException = shouldThrow<InternalGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "NotFound"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER_ENROLMENT
                }
            }
            and("url contains ENROL_ADDITIONAL_PROPERTY_URL") {
                every { mockCall.request } returns getDummyRequest(
                    HttpMethod.Post,
                    "/rest/v1$ENROL_ADDITIONAL_PROPERTY_URL"
                )

                then("error category should be deduced from path") {
                    val handledException = shouldThrow<InternalGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "NotFound"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER_ENROLMENT
                }
            }
            and("url contains COT_MOVE_OUT_PROPERTY_URL") {
                every { mockCall.request } returns getDummyRequest(
                    HttpMethod.Post,
                    "/rest/v1$COT_PROPERTY_URL"
                )

                then("error category should be deduced from path") {
                    val handledException = shouldThrow<InternalGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "NotFound"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER_COT_MOVE_OUT
                }
            }
            and("url contains COT_MOVE_OUT_ACCOUNT_URL") {
                every { mockCall.request } returns getDummyRequest(HttpMethod.Post, "/rest/v1$COT_MOVE_OUT_ACCOUNT_URL")

                then("error category should be deduced from path") {
                    val handledException = shouldThrow<InternalGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "NotFound"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER_COT_MOVE_OUT
                }
            }
        }

        `when`("error category can NOT be deduced from request url") {
            every { mockCall.request } returns getDummyRequest(HttpMethod.Post, "/some/random/url")
            and("junifer error code is NotFound") {
                val exception = buildJuniferException(mockCall, "NotFound")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<EntityNotFoundGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "110"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is ReadingDtBeforeSSD") {
                val exception = buildJuniferException(mockCall, "ReadingDtBeforeSSD")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "111"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is MTDsHaveArrived") {
                val exception = buildJuniferException(mockCall, "MTDsHaveArrived")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "112"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is InvalidQuality") {
                val exception = buildJuniferException(mockCall, "InvalidQuality")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "113"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is InvalidSource") {
                val exception = buildJuniferException(mockCall, "InvalidSource")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "114"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is RequiredParameterMissing") {
                val exception = buildJuniferException(mockCall, "RequiredParameterMissing")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "115"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is AccountNotFound") {
                val exception = buildJuniferException(mockCall, "AccountNotFound")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<EntityNotFoundGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "116"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is SortCodeNotCorrectlyFormatted") {
                val exception = buildJuniferException(mockCall, "SortCodeNotCorrectlyFormatted")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "117"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("junifer error code is WhateverElse") {
                val exception = buildJuniferException(mockCall, "WhateverElse")

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<UnknownGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "Unknown error when submitting request to Junifer: junifer errorDescription"
                    handledException.errorCode shouldBe "WhateverElse"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
            and("response dto is null") {
                val exception = JuniferException(
                    "InvalidSource",
                    "junifer errorSeverity",
                    "junifer errorDescription",
                    null
                )

                then("error type should be deduced from junifer error code") {
                    val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }

                    handledException.message shouldBe "junifer errorDescription"
                    handledException.errorCode shouldBe "114"
                    handledException.errorCategory shouldBe ErrorCategories.JUNIFER
                }
            }
        }
    }

    given("exception handled is NOT JuniferException") {
        `when`("IllegalArgumentException is handled") {
            val exception = IllegalArgumentException("errorMessage")
            then("an InvalidArgumentGrpcException is thrown") {
                val handledException = shouldThrow<InvalidArgumentGrpcException> { throwGrpcException(exception) }
                handledException.message shouldBe "errorMessage"
                handledException.errorCode shouldBe "109"
                handledException.errorCategory shouldBe ErrorCategories.JUNIFER
            }
        }
        `when`("IllegalStateException is handled") {
            val exception = IllegalStateException("errorMessage")
            then("an FailedPreconditionGrpcException is thrown") {
                val handledException = shouldThrow<FailedPreconditionGrpcException> { throwGrpcException(exception) }
                handledException.message shouldBe "errorMessage"
                handledException.errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                handledException.errorCategory shouldBe ErrorCategories.COMMONS
            }
        }
        `when`("some other exception is handled") {
            val exception = ChannelIOException("errorMessage", Exception(""))
            then("an UnknownGrpcException is thrown") {
                val handledException = shouldThrow<UnknownGrpcException> { throwGrpcException(exception) }
                handledException.message shouldBe "errorMessage"
                handledException.errorCode shouldBe ErrorCodes.COMMONS_UNHANDLED_ERROR
                handledException.errorCategory shouldBe ErrorCategories.COMMONS
            }
        }
    }
})

private fun getDummyRequest(method: HttpMethod, url: String) = DummyRequest(
    method,
    URLBuilder().apply {
        protocol = URLProtocol.HTTPS
        host = "localhost"
        encodedPath = url
    }.build()
)

private fun buildJuniferException(mockCall: HttpClientCall, juniferErrorCode: String) = JuniferException(
    errorCode = juniferErrorCode,
    errorSeverity = "junifer errorSeverity",
    errorDescription = "junifer errorDescription",
    errorResponse = DummyResponse(mockCall)
)

// Some quick and dirty empty implementations of some Ktor stuff
class DummyResponse(override val call: HttpClientCall) : HttpResponse() {

    @InternalAPI
    override val content: ByteReadChannel
        get() = TODO("Not yet implemented")
    override val coroutineContext: CoroutineContext
        get() = TODO("Not yet implemented")
    override val headers: Headers
        get() = TODO("Not yet implemented")
    override val requestTime: GMTDate
        get() = TODO("Not yet implemented")
    override val responseTime: GMTDate
        get() = TODO("Not yet implemented")
    override val status: HttpStatusCode
        get() = TODO("Not yet implemented")
    override val version: HttpProtocolVersion
        get() = TODO("Not yet implemented")
}

class DummyRequest(
    override val method: HttpMethod,
    override val url: Url,
) : HttpRequest {
    override val attributes: Attributes
        get() = TODO("Not yet implemented")
    override val call: HttpClientCall
        get() = TODO("Not yet implemented")
    override val content: OutgoingContent
        get() = TODO("Not yet implemented")
    override val headers: Headers
        get() = TODO("Not yet implemented")
}