package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentPlanPaymentTransactionRepository
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_PLAN_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.createPaymentPlanPaymentTransactionSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.deletePaymentPlanPaymentTransactionSync
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentTransactionCreateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentTransactionDeleteSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentTransactionSyncResponse
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.paymentPlanPaymentTransactionUpdateSyncEvent
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.testJuniferPaymentPlanPaymentTransaction
import energy.so.ac.junifer.fixtures.PaymentPlanPrecannedData.updatePaymentPlanPaymentTransactionSync
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_PLAN_PAYMENT
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT_PLAN_PAYMENT_TRANSACTION
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.billings.client.v2.SyncClient
import energy.so.commons.exceptions.services.EntityNotFoundException
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class PaymentPlanPaymentTransactionSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentPlanPaymentTransactionRepository = mockk<JuniferPaymentPlanPaymentTransactionRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val paymentPlanPaymentTransactionProcessor = PaymentPlanPaymentTransactionSyncProcessor(
        mockMapper,
        mockPaymentPlanPaymentTransactionRepository,
        mockSyncClient
    )

    afterEach {
        confirmVerified(mockMapper, mockPaymentPlanPaymentTransactionRepository, mockSyncClient)
    }

    given("no existing mapped paymentPlanPaymentTransaction and an existing junifer paymentPlanPaymentTransaction") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT_TRANSACTION,
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
            )
        } returns null

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT,
                JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_ID.toString()



        every {
            mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
            )
        } returns testJuniferPaymentPlanPaymentTransaction

        `when`("a paymentPlanPaymentTransaction event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentTransactionEntity(createPaymentPlanPaymentTransactionSync) } returns paymentPlanPaymentTransactionSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN_PAYMENT_TRANSACTION,
                    JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString(),
                    PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
                )
            }

            paymentPlanPaymentTransactionProcessor.process(paymentPlanPaymentTransactionCreateSyncEvent)

            then("a new paymentPlanPaymentTransaction should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN_PAYMENT, JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        PAYMENT_PLAN_PAYMENT_TRANSACTION,
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
                    )
                }
                verify {
                    mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        PAYMENT_PLAN_PAYMENT_TRANSACTION,
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString(),
                        PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
                    )
                }
                coVerify {
                    mockSyncClient.syncPaymentPlanPaymentTransactionEntity(
                        createPaymentPlanPaymentTransactionSync
                    )
                }
            }
        }
    }

    given("an existing mapped paymentPlanPaymentTransaction and an existing junifer paymentPlanPaymentTransaction") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT_TRANSACTION,
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT,
                JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_ID.toString()

        every {
            mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
            )
        } returns testJuniferPaymentPlanPaymentTransaction

        `when`("a paymentPlanPaymentTransaction event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentTransactionEntity(updatePaymentPlanPaymentTransactionSync) } returns paymentPlanPaymentTransactionSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    PAYMENT_PLAN_PAYMENT,
                    JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString(),
                    PAYMENT_PLAN_PAYMENT_ID.toString()
                )
            }

            paymentPlanPaymentTransactionProcessor.process(paymentPlanPaymentTransactionUpdateSyncEvent)

            then("a new paymentPlanPaymentTransaction should be created") {
                verify { mockMapper.getCoreId(PAYMENT_PLAN_PAYMENT, JUNIFER_PAYMENT_PLAN_PAYMENT_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        PAYMENT_PLAN_PAYMENT_TRANSACTION,
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
                    )
                }
                verify {
                    mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
                    )
                }
                coVerify {
                    mockSyncClient.syncPaymentPlanPaymentTransactionEntity(
                        updatePaymentPlanPaymentTransactionSync
                    )
                }
            }
        }
    }

    given("an existing mapped paymentPlanPaymentTransaction and existing junifer paymentPlanPaymentTransaction to be deleted") {

        every {
            mockMapper.getCoreId(
                PAYMENT_PLAN_PAYMENT_TRANSACTION,
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
            )
        } returns PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()

        every {
            mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
            )
        } throws EntityNotFoundException(
            "PaymentPlanPaymentTransaction not found"
        )

        `when`("a paymentPlanPaymentTransaction event is generated") {

            coEvery { mockSyncClient.syncPaymentPlanPaymentTransactionEntity(deletePaymentPlanPaymentTransactionSync) } returns paymentPlanPaymentTransactionSyncResponse

            paymentPlanPaymentTransactionProcessor.process(paymentPlanPaymentTransactionDeleteSyncEvent)

            then("a new paymentPlanPaymentTransaction should be created") {
                verify {
                    mockMapper.getCoreId(
                        PAYMENT_PLAN_PAYMENT_TRANSACTION,
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID.toString()
                    )
                }
                verify {
                    mockPaymentPlanPaymentTransactionRepository.getPaymentPlanPaymentTransaction(
                        JUNIFER_PAYMENT_PLAN_PAYMENT_TRANSACTION_ID
                    )
                }
                coVerify {
                    mockSyncClient.syncPaymentPlanPaymentTransactionEntity(
                        deletePaymentPlanPaymentTransactionSync
                    )
                }
            }
        }
    }


})
