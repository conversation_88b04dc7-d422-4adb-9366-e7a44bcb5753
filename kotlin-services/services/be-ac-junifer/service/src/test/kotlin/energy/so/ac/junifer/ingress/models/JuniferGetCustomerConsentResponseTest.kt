package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.CustomerConsentPrecannedData
import energy.so.ac.junifer.ingress.models.customers.toResponse
import energy.so.commons.grpc.extensions.getValueOrNull
import energy.so.commons.grpc.utils.toLocalDate
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

class JuniferGetCustomerConsentResponseTest : BehaviorSpec({

    given("JuniferGetCustomerConsentsResponse") {
        val juniferResponse = CustomerConsentPrecannedData.getCustomerConsentResponse

        `when`("call toResponse method") {
            val result = juniferResponse.toResponse()

            then("result match") {
                result.getConsents(0) shouldNotBe null
                result.getConsents(0).id shouldBe juniferResponse.results[0].id.toString()
                result.getConsents(0).consentDefinition shouldBe juniferResponse.results[0].consentDefinition
                result.getConsents(0).setting shouldBe juniferResponse.results[0].setting
                result.getConsents(0).fromDt.toLocalDate() shouldBe juniferResponse.results[0].fromDt
                result.getConsents(0).toDt.getValueOrNull()?.toLocalDate() shouldBe juniferResponse.results[0].toDt
                result.getConsents(0).links.customer shouldBe juniferResponse.results[0].links?.customer
                result.getConsents(0).links.self shouldBe juniferResponse.results[0].links?.self
            }
        }
    }
})
