package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferRateNameRepository
import energy.so.ac.junifer.fixtures.JUNIFER_RATE_NAME_ID
import energy.so.ac.junifer.fixtures.PRODUCT_RATE_ID
import energy.so.ac.junifer.fixtures.ProductPrecannedData.productSyncEvent
import energy.so.ac.junifer.fixtures.ProductPrecannedData.testRateNamePeak
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createProductRateEntityRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.patchProductRateEntityRequest
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.productRateSyncResponse
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.rateNameSyncEvent
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.v2.sync.OperationType
import energy.so.products.client.v2.SyncClient
import energy.so.products.sync.v2.productRateEntity
import energy.so.products.sync.v2.productRateEntityRequest
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

class ProductRateSyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockJuniferRateNameRepository = mockk<JuniferRateNameRepository>()
    val mockProductsSyncClient = mockk<SyncClient>()

    val productRateSyncProcessor =
        ProductRateSyncProcessor(mockMapper, mockJuniferRateNameRepository, mockProductsSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockJuniferRateNameRepository, mockProductsSyncClient)
        clearMocks(mockMapper, mockJuniferRateNameRepository, mockProductsSyncClient)
    }

    given("sync event with null reference") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    productRateSyncProcessor.process(rateNameSyncEvent.copy(reference = null))
                }.let {
                    it.message shouldBe "Cannot process sync event without a reference"
                }
            }
        }
    }

    given("invalid sync event type") {
        `when`("call process") {
            then("throw IllegalStateException") {
                assertThrows<IllegalStateException> {
                    productRateSyncProcessor.process(productSyncEvent)
                }.let {
                    it.message shouldBe "Unsupported EventType PRODUCT_BUNDLE_DEFINITION"
                }
            }
        }
    }

    given("correct RATE_NAME sync event") {
        and("no RATE_NAME core mapping") {
            and("RATE_NAME corresponding to reference not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString()
                    )
                } returns null
                coEvery { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) } throws EntityNotFoundException(
                    "Not Found"
                )

                `when`("call process") {
                    productRateSyncProcessor.process(rateNameSyncEvent)

                    then("sync not called") {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString()
                            )
                        }
                        coVerify { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) }
                        coVerify(exactly = 0) { mockProductsSyncClient.syncProductRateEntityRequest(any()) }
                    }
                }
            }

            and("RATE_NAME corresponding to reference found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString()
                    )
                } returns null
                coEvery { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) } returns testRateNamePeak
                coEvery { mockProductsSyncClient.syncProductRateEntityRequest(createProductRateEntityRequest) } returns productRateSyncResponse
                coJustRun {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString(),
                        PRODUCT_RATE_ID.toString()
                    )
                }

                `when`("call process") {
                    productRateSyncProcessor.process(rateNameSyncEvent)

                    then("CREATED sync event") {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString()
                            )
                        }
                        coVerify { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) }
                        coVerify {
                            mockProductsSyncClient.syncProductRateEntityRequest(
                                createProductRateEntityRequest
                            )
                        }

                        coVerify {
                            mockMapper.createCoreMapping(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString(),
                                PRODUCT_RATE_ID.toString()
                            )
                        }
                    }
                }
            }
        }

        and("RATE_NAME core mapping") {
            and("RATE_NAME corresponding to reference not found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString()
                    )
                } returns PRODUCT_RATE_ID.toString()
                coEvery { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) } throws EntityNotFoundException(
                    "Not Found"
                )
                coEvery { mockProductsSyncClient.syncProductRateEntityRequest(any()) } returns productRateSyncResponse

                `when`("call process") {
                    productRateSyncProcessor.process(rateNameSyncEvent)

                    then("DELETE sync event, mapping deleted") {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString()
                            )
                        }
                        coVerify { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) }
                        coVerify {
                            mockProductsSyncClient.syncProductRateEntityRequest(
                                productRateEntityRequest {
                                    productRateEntity = productRateEntity {
                                        id = PRODUCT_RATE_ID.toNullableInt64()
                                    }
                                    operationType = OperationType.DELETE
                                }
                            )
                        }

                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString(),
                                PRODUCT_RATE_ID.toString()
                            )
                        }
                    }
                }
            }

            and("RATE_NAME corresponding to reference found in db") {
                coEvery {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString()
                    )
                } returns PRODUCT_RATE_ID.toString()
                coEvery { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) } returns testRateNamePeak
                coEvery { mockProductsSyncClient.syncProductRateEntityRequest(patchProductRateEntityRequest) } returns productRateSyncResponse

                `when`("call process") {
                    productRateSyncProcessor.process(rateNameSyncEvent)

                    then("UPDATED sync event, no new mapping created") {
                        coVerify {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString()
                            )
                        }
                        coVerify { mockJuniferRateNameRepository.getRateNameById(JUNIFER_RATE_NAME_ID) }
                        coVerify {
                            mockProductsSyncClient.syncProductRateEntityRequest(
                                patchProductRateEntityRequest
                            )
                        }

                        coVerify(exactly = 0) {
                            mockMapper.createCoreMapping(
                                EntityIdentifier.PRODUCT_RATE,
                                JUNIFER_RATE_NAME_ID.toString(),
                                PRODUCT_RATE_ID.toString()
                            )
                        }
                    }
                }
            }
        }
    }
})
