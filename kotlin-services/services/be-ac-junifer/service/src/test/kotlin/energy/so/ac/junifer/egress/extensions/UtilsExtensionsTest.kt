package energy.so.ac.junifer.egress.extensions

import energy.so.commons.grpc.utils.toLocalDate
import energy.so.commons.grpc.utils.toOffsetDateTime
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

class UtilsExtensionsTest : BehaviorSpec({

    val offsetDateTimeMax = OffsetDateTime.of(9999, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)
    val offsetDateTime = OffsetDateTime.of(2023, 1, 1, 23, 0, 0, 0, ZoneOffset.UTC)
    val offsetDateTimeSummer = OffsetDateTime.of(2023, 7, 1, 23, 0, 0, 0, ZoneOffset.UTC)
    val localDateMax = offsetDateTimeMax.toLocalDate()
    val localDate = offsetDateTime.toLocalDate()
    val summerLocalDateTime = LocalDateTime.of(2023, 7, 1, 23, 0, 0, 0)
    val winterLocalDateTime = LocalDateTime.of(2023, 1, 1, 23, 0, 0, 0)
    given("::extractNullableTimestampIgnoringMaxValue for offset") {
        and("offsetDate is null") {
            val date: OffsetDateTime? = null

            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(date)

                then("return nullable with null set") {
                    result.hasNull() shouldBe true
                }
            }
        }

        and("offsetDate is gt 9000") {
            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(offsetDateTimeMax)

                then("return nullable with null set") {
                    result.hasNull() shouldBe true
                }
            }
        }

        and("offsetDate is lt 9000") {
            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(offsetDateTime)

                then("return nullable with date set") {
                    result.hasNull() shouldBe false
                    result.value.toOffsetDateTime() shouldBe offsetDateTime
                }
            }
        }
    }
    given("::extractNullableTimestampIgnoringMaxValue for local date") {
        and("local date is null") {
            val date: LocalDate? = null

            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(date)

                then("return nullable with null set") {
                    result.hasNull() shouldBe true
                }
            }
        }

        and("local date is gt 9000") {
            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(localDateMax)

                then("return nullable with null set") {
                    result.hasNull() shouldBe true
                }
            }
        }

        and("local date is lt 9000") {
            `when`("call extractNullableTimestampIgnoringMaxValue") {
                val result = extractNullableTimestampIgnoringMaxValue(localDate)

                then("return nullable with date set") {
                    result.hasNull() shouldBe false
                    result.value.toLocalDate() shouldBe localDate
                }
            }
        }
    }

    given("LocalDateTime::toUkTime") {
        and("for summer time at 11 PM") {
            `when`("call toUkTime") {
                val result = summerLocalDateTime.toUkTime()

                then("return corresponding timestamp from next day") {
                    result shouldBe summerLocalDateTime.plusHours(1)
                }
            }
        }

        and("for winter time at 11 PM") {
            `when`("call toUkTime") {
                val result = winterLocalDateTime.toUkTime()

                then("return same timestamp") {
                    result shouldBe winterLocalDateTime
                }
            }
        }
    }

    given("OffsetDateTime::toUkTime") {
        and("for summer time at 11 PM") {
            `when`("call toUkTime") {
                val result = offsetDateTimeSummer.toUkTime()

                then("return corresponding timestamp from next day") {
                    result shouldBe summerLocalDateTime.plusHours(1)
                }
            }
        }

        and("for winter time at 11 PM") {
            `when`("call toUkTime") {
                val result = offsetDateTime.toUkTime()

                then("return same timestamp") {
                    result shouldBe winterLocalDateTime
                }
            }
        }
    }
})
