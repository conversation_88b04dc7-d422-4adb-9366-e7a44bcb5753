package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferPaymentRepository
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.JUNIFER_ACCOUNT_TRANSACTION_ID
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.JUNIFER_BILLING_ACCOUNT_ID
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.accountTransactionSyncEvent
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.accountTransactionSyncResponse
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.createBillingAccountTransactionSync
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.deleteBillingAccountTransactionSync
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.testJuniferAccount
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.testJuniferAccountTransaction
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.testJuniferAccountTransactionType
import energy.so.ac.junifer.fixtures.AccountTransactionPrecannedData.updateBillingAccountTransactionSync
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.JUNIFER_PAYMENT_REQUEST_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.PAYMENT_ID
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.paymentSyncEvent
import energy.so.ac.junifer.fixtures.PaymentsPrecannedData.testJuniferPayment
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT
import energy.so.ac.junifer.mapping.EntityIdentifier.BILLING_ACCOUNT_TRANSACTION
import energy.so.ac.junifer.mapping.EntityIdentifier.PAYMENT
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.payments.client.v2.sync.SyncClient
import io.kotest.core.spec.style.BehaviorSpec
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify

class BillingAccountTransactionSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockPaymentRepository = mockk<JuniferPaymentRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val accountTransactionProcessor =
        BillingAccountTransactionSyncProcessor(mockMapper, mockPaymentRepository, mockSyncClient)

    afterEach {
        confirmVerified(mockMapper, mockPaymentRepository, mockSyncClient)
    }

    given("no existing mapped accountTransaction and an existing junifer accountTransaction") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every { mockPaymentRepository.getPaymentRequestFromTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferPayment.paymentrequestfk
        every { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferAccountTransaction
        every { mockPaymentRepository.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferAccount
        every { mockPaymentRepository.getAccountTransactionType(JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID) } returns testJuniferAccountTransactionType

        `when`("an accountTransaction event is generated") {

            coEvery { mockSyncClient.syncBillingAccountTransactionEntity(createBillingAccountTransactionSync) } returns accountTransactionSyncResponse

            justRun {
                mockMapper.createCoreMapping(
                    BILLING_ACCOUNT_TRANSACTION,
                    JUNIFER_ACCOUNT_TRANSACTION_ID.toString(),
                    ACCOUNT_TRANSACTION_ID.toString()
                )
            }

            accountTransactionProcessor.process(accountTransactionSyncEvent)

            then("a new accountTransaction should be created") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_TRANSACTION, JUNIFER_ACCOUNT_TRANSACTION_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentRepository.getPaymentRequestFromTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransactionType(JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID) }
                verify {
                    mockMapper.createCoreMapping(
                        BILLING_ACCOUNT_TRANSACTION,
                        JUNIFER_ACCOUNT_TRANSACTION_ID.toString(),
                        ACCOUNT_TRANSACTION_ID.toString()
                    )
                }
                coVerify { mockSyncClient.syncBillingAccountTransactionEntity(createBillingAccountTransactionSync) }
            }
        }
    }

    given("an existing mapped accountTransaction and an existing junifer accountTransaction") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns ACCOUNT_TRANSACTION_ID.toString()
        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT,
                JUNIFER_BILLING_ACCOUNT_ID.toString()
            )
        } returns BILLING_ACCOUNT_ID.toString()
        every {
            mockMapper.getCoreId(
                PAYMENT,
                JUNIFER_PAYMENT_REQUEST_ID.toString()
            )
        } returns PAYMENT_ID.toString()

        every { mockPaymentRepository.getPaymentRequestFromTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferPayment.paymentrequestfk
        every { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferAccountTransaction
        every { mockPaymentRepository.getAccountTransactionType(JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID) } returns testJuniferAccountTransactionType
        every { mockPaymentRepository.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID) } returns testJuniferAccount

        `when`("an accountTransaction event is generated") {

            coEvery { mockSyncClient.syncBillingAccountTransactionEntity(updateBillingAccountTransactionSync) } returns accountTransactionSyncResponse

            accountTransactionProcessor.process(accountTransactionSyncEvent)

            then("accountTransaction should be patched") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_TRANSACTION, JUNIFER_ACCOUNT_TRANSACTION_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentRepository.getPaymentRequestFromTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransactionType(JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID) }
                coVerify { mockSyncClient.syncBillingAccountTransactionEntity(updateBillingAccountTransactionSync) }
            }
        }

        `when`("a payment event is generated") {

            every { mockPaymentRepository.getPayment(JUNIFER_PAYMENT_ID) } returns testJuniferPayment
            coEvery { mockSyncClient.syncBillingAccountTransactionEntity(updateBillingAccountTransactionSync) } returns accountTransactionSyncResponse

            accountTransactionProcessor.process(paymentSyncEvent)

            then("accountTransaction should be patched") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_TRANSACTION, JUNIFER_ACCOUNT_TRANSACTION_ID.toString()) }
                verify { mockMapper.getCoreId(BILLING_ACCOUNT, JUNIFER_BILLING_ACCOUNT_ID.toString()) }
                verify { mockMapper.getCoreId(PAYMENT, JUNIFER_PAYMENT_REQUEST_ID.toString()) }
                verify { mockPaymentRepository.getPayment(JUNIFER_PAYMENT_ID) }
                verify { mockPaymentRepository.getPaymentRequestFromTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountByAccountTransactionId(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                verify { mockPaymentRepository.getAccountTransactionType(JUNIFER_ACCOUNT_TRANSACTION_TYPE_ID) }
                coVerify { mockSyncClient.syncBillingAccountTransactionEntity(updateBillingAccountTransactionSync) }
            }
        }
    }

    given("an existing mapped accountTransaction and existing junifer accountTransaction to be deleted") {

        every {
            mockMapper.getCoreId(
                BILLING_ACCOUNT_TRANSACTION,
                JUNIFER_ACCOUNT_TRANSACTION_ID.toString()
            )
        } returns ACCOUNT_TRANSACTION_ID.toString()

        every { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) } throws EntityNotFoundException(
            "account transaction not found"
        )

        `when`("an accountTransaction event is generated") {

            coEvery { mockSyncClient.syncBillingAccountTransactionEntity(deleteBillingAccountTransactionSync) } returns accountTransactionSyncResponse

            accountTransactionProcessor.process(accountTransactionSyncEvent)

            then("accountTransaction should be deleted") {
                verify { mockMapper.getCoreId(BILLING_ACCOUNT_TRANSACTION, JUNIFER_ACCOUNT_TRANSACTION_ID.toString()) }
                verify { mockPaymentRepository.getAccountTransaction(JUNIFER_ACCOUNT_TRANSACTION_ID) }
                coVerify { mockSyncClient.syncBillingAccountTransactionEntity(deleteBillingAccountTransactionSync) }
            }
        }
    }
})
