package energy.so.ac.junifer.ingress.services.smartreads

import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aBulkXRITMErrorResult
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.aSecondXRITMMeterDto
import energy.so.ac.junifer.fixtures.SmartReadsCannedData.anXRITMMeterDto
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.assertDoesNotThrow

class HttpJuniferSmartReadsServiceTest : BehaviorSpec({
    val sut = HttpJuniferSmartReadsService(MockJuniferClient.juniferConfig, getMockHttpClient())

    given("::oneOffRead") {
        `when`("meterpoint id provided to endpoint") {
            val meterpointId = "1234"
            then("no error thrown") {
                assertDoesNotThrow { sut.initiateOneOffRead(meterpointId) }
            }
        }
    }

    given("::bulkXRITM") {
        `when`("List of meters provided") {
            val meters = listOf(anXRITMMeterDto, aSecondXRITMMeterDto)
            val result = assertDoesNotThrow { sut.bulkXRITMReads(meters) }
            then("Error list contains only the one errored meterpointId") {
                result shouldBe listOf(aBulkXRITMErrorResult)
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Post && request.url.fullPath.endsWith("1234/dcc/initiateOneOffReading")) {
            respond(
                "",
                HttpStatusCode.NoContent,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "Error error error")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}