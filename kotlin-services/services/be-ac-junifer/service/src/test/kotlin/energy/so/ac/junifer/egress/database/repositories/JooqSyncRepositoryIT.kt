package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.config.JobFailedEventsConfig
import energy.so.ac.junifer.config.SyncConfig
import energy.so.ac.junifer.config.SyncOptimizationConfig
import energy.so.ac.junifer.fixtures.CompletedSyncEventPrecannedData.ID_2
import energy.so.ac.junifer.fixtures.CompletedSyncEventPrecannedData.POTENTIALLY_CORRUPT
import energy.so.ac.junifer.fixtures.CompletedSyncEventPrecannedData.completedSyncEvent
import energy.so.ac.junifer.fixtures.CompletedSyncEventPrecannedData.completedSyncEventOlderThan1Month
import energy.so.commons.extension.save
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.enums.OperationType
import energy.so.commons.model.tables.pojos.SyncEvent
import energy.so.commons.model.tables.references.COMPLETED_SYNC_EVENT
import energy.so.commons.model.tables.references.SYNC_EVENT
import energy.so.commons.model.tables.references.SYNC_SOURCE_HEALTH_CHECK
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.ShouldSpec
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.lang.Thread.sleep
import java.time.LocalDateTime
import java.time.OffsetDateTime
import org.jooq.DSLContext

const val maxEvents = 3
const val maxLockTimeInSeconds = 60L
const val exponentialBackoffMultiplierInSeconds = 60L
const val pid = "pid"
const val maxRetryCount = 3
const val minCreateDelayInSeconds = 2L
const val envId = "79dfa8a8-edfc-43ad-8e48-12727fb6f3d9"
const val envId2 = "a1a968fb-d612-4b04-a286-60b60922a9b8"

private const val ONE_MONTH_IN_HOURS = 720L

class JooqSyncRepositoryTest : ShouldSpec({

    val db = installDatabase(truncateOnRootOnly = true)
    val syncRepository = JooqSyncEventRepository(
        db,
        SyncConfig(
            pid,
            maxEvents,
            maxLockTimeInSeconds,
            exponentialBackoffMultiplierInSeconds,
            maxRetryCount,
            minCreateDelayInSeconds
        ),
        JobFailedEventsConfig(
            completeCorruptedData = false
        ),
        SyncOptimizationConfig()
    )

    val syncRepositoryPauseOnErrorEnabled = JooqSyncEventRepository(
        db,
        SyncConfig(
            pid,
            maxEvents,
            maxLockTimeInSeconds,
            exponentialBackoffMultiplierInSeconds,
            maxRetryCount,
            minCreateDelayInSeconds
        ),
        JobFailedEventsConfig(
            completeCorruptedData = false
        ),
        SyncOptimizationConfig()
    )

    val syncRepositoryCompleteCorruptedDataEnabled = JooqSyncEventRepository(
        db,
        SyncConfig(
            pid = pid,
            maxEvents = maxEvents,
            maxLockTimeInSeconds = maxLockTimeInSeconds,
            exponentialBackoffMultiplierInSeconds = exponentialBackoffMultiplierInSeconds,
            maxRetryCount = maxRetryCount,
            minInsertDelayInSeconds = minCreateDelayInSeconds,
        ),
        JobFailedEventsConfig(
            completeCorruptedData = true
        ),
        SyncOptimizationConfig()
    )

    val syncRepositoryNullProcessedByEnabled = JooqSyncEventRepository(
        db,
        SyncConfig(
            pid,
            maxEvents,
            maxLockTimeInSeconds,
            exponentialBackoffMultiplierInSeconds,
            maxRetryCount,
            minCreateDelayInSeconds,
        ),
        JobFailedEventsConfig(
            completeCorruptedData = false
        ),
        SyncOptimizationConfig(
            optimizedDataSync = true
        )
    )

    val syncRepositoryNullProcessedByDisabled = JooqSyncEventRepository(
        db,
        SyncConfig(
            pid,
            maxEvents,
            maxLockTimeInSeconds,
            exponentialBackoffMultiplierInSeconds,
            maxRetryCount,
            minCreateDelayInSeconds
        ),
        JobFailedEventsConfig(
            completeCorruptedData = false
        ),
        SyncOptimizationConfig(
            optimizedDataSync = true,
            enableNullProcessedBySyncEvents = false,
            envId = envId
        )
    )

    context("::getSyncEvents") {

        afterTest {
            truncateEvents(db)
        }

        should("given sync events when queried then return the max number of events") {
            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER, "2", OperationType.UPDATE)
            createEvent(db, EventType.METER, "3", OperationType.UPDATE)
            createEvent(db, EventType.METER, "4", OperationType.UPDATE)

            // WHEN
            val events = syncRepository.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 3
            validateDb(db, 0, 1, 3, 0)
        }

        should("given sync events with operation type INSERT when getSyncEvents called then these are only returned after configured delay") {
            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.INSERT)
            createEvent(db, EventType.METER, "2", OperationType.INSERT)
            createEvent(db, EventType.METER, "3", OperationType.INSERT)

            // WHEN
            var events = syncRepository.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 0

            //AND WHEN
            sleep(minCreateDelayInSeconds * 1000)
            events = syncRepository.getSyncEvents(EventType.METER)

            //THEN
            events.size shouldBe 3

            validateDb(db, 0, 0, 3, 0)
        }

        should("given sync event which exceeds retry count when queried then dont return them") {
            val claimedAt = OffsetDateTime.now().minusDays(1)

            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE, retryCount = 1, claimedAt = claimedAt)
            createEvent(
                db,
                EventType.METER,
                "1",
                OperationType.UPDATE,
                retryCount = 3,
                errorCode = "oops",
                claimedAt = claimedAt
            )
            createEvent(
                db,
                EventType.METER,
                "1",
                OperationType.UPDATE,
                retryCount = 3,
                errorCode = "oops",
                claimedAt = claimedAt
            )

            // WHEN
            val events = syncRepository.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 1
            validateDb(db, 0, 0, 1, 2)
        }

        should("given sync events in some states when getSyncEvents called then check the events counts for each state") {
            // GIVEN

            //unclaimed event
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)

            // completed event
            createEvent(
                db,
                EventType.METER,
                "2",
                OperationType.UPDATE,
                completedAt = OffsetDateTime.now(),
                claimedAt = OffsetDateTime.now().minusSeconds(30),
                completionReason = "completion_reason",
            )

            //  ready to be retried
            createEvent(db, EventType.METER, "3", OperationType.UPDATE, retryCount = 1)

            // exceeded max retries
            createEvent(
                db,
                EventType.METER,
                "4",
                OperationType.UPDATE,
                retryCount = 3,
                errorCode = "100",
                errorDescription = "An error occurred"
            )

            // timed out and should be retried
            createEvent(
                db,
                EventType.METER,
                "5",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(maxLockTimeInSeconds + 30)
            )

            // WHEN
            val events = syncRepository.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 3
            validateDb(db, 1, 0, 3, 1)
        }

        should("given an event type is specified when queried then only events of that event type should be returned") {

            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER, "2", OperationType.UPDATE)
            createEvent(db, EventType.METER_REGISTER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER_REGISTER, "2", OperationType.UPDATE)

            // WHEN
            val events = syncRepositoryPauseOnErrorEnabled.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 2
            events.forEach {
                it.eventType shouldBe EventType.METER
            }
        }

        should("given enableNullProcessedBySyncEvents true and sync events, when queried then return the max number of all events") {
            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER, "2", OperationType.UPDATE)
            createEvent(db, EventType.METER, "3", OperationType.UPDATE, processedBy = envId)
            createEvent(db, EventType.METER, "4", OperationType.UPDATE)

            // WHEN
            val events = syncRepositoryNullProcessedByEnabled.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 3
            validateDb(db, 0, 1, 3, 0)

        }

        should("given enableNullProcessedBySyncEvents false and sync events when queried then return only events with envId") {
            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER, "2", OperationType.UPDATE, processedBy = envId)
            createEvent(db, EventType.METER, "3", OperationType.UPDATE, processedBy = envId2)

            // WHEN
            val events = syncRepositoryNullProcessedByDisabled.getSyncEvents(EventType.METER)

            // THEN
            events.size shouldBe 1
            validateDb(db, 0, 2, 1, 0)
        }
    }

    context("::releaseEvent") {

        afterTest {
            truncateEvents(db)
        }

        should("given a claimed event when released the event should be updated") {
            // GIVEN
            createEvent(db, EventType.METER, "1", OperationType.UPDATE)
            createEvent(db, EventType.METER, "2", OperationType.UPDATE)
            createEvent(db, EventType.METER, "3", OperationType.UPDATE)

            // WHEN
            val events = syncRepository.getSyncEvents(EventType.METER)
            // Here we have two events claimed, and one unclaimed on the DB, we release one event
            syncRepository.update(events[0])

            // THEN
            validateDb(db, 0, 0, 2, 0)
        }
    }

    context("::insertEvents") {

        afterTest {
            truncateEvents(db)
        }

        should("given references to be inserted are not present in the sync events table") {
            db.fetchCount(SYNC_EVENT) shouldBe 0
            syncRepository.insertEvents(listOf(1L, 2L, 3L), EventType.ADDRESS)

            // THEN all should be inserted into the table at the same time
            db.fetchCount(SYNC_EVENT) shouldBe 3
            getAllSyncEvents(db).map { it.reference!!.toLong() } shouldBe listOf(1L, 2L, 3L)
            getAllSyncEvents(db).map { it.eventType }.distinct() shouldBe listOf(EventType.ADDRESS)
            getAllSyncEvents(db).map { it.createdAt }.distinct().size shouldBe 1

        }

        should("some references are already present in the sync events table for a given event type") {
            createEvent(db, EventType.ADDRESS, "1", OperationType.UPDATE)
            db.fetchCount(SYNC_EVENT) shouldBe 1
            syncRepository.insertEvents(listOf(1L, 2L, 3L), EventType.ADDRESS)

            // THEN only those ids should be inserted which are not yet present"
            db.fetchCount(SYNC_EVENT) shouldBe 3
            getAllSyncEvents(db).map { it.reference!!.toLong() } shouldBe listOf(1L, 2L, 3L)
            getAllSyncEvents(db).map { it.createdAt }.distinct().size shouldBe 2

        }

        should("function called with an empty list") {
            db.fetchCount(SYNC_EVENT) shouldBe 0
            syncRepository.insertEvents(emptyList(), EventType.ADDRESS)

            // THEN nothing inserted but no exception thrown
            db.fetchCount(SYNC_EVENT) shouldBe 0
        }
    }

    context("::resetFailedEvents") {

        afterTest {
            truncateEvents(db)
        }

        should("given failed sync events present in the DB, they should be reinserted for reprocessing and reset as completed") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )

            // two failed sync events present in the DB
            getAllSyncEvents(db).size shouldBe 2
            getFailedEvents(db).size shouldBe 2
            getOutstandingEvents(db).size shouldBe 0

            syncRepository.resetFailedEvents(10L)

            // THEN new SyncEvent records should be inserted for reprocessing and the old records reset
            val allRecordsInDb = getAllSyncEvents(db)
            allRecordsInDb.size shouldBe 4

            getFailedEvents(db).size shouldBe 0

            val outStandingRecords = getOutstandingEvents(db)
            outStandingRecords.size shouldBe 2

            val completedNotFailedRecords = getCompletedNotFailedEvents(db)
            completedNotFailedRecords.size shouldBe 2

            // AND the fields on the new and reset records should be populated as expected
            outStandingRecords.map { it.reference!!.toLong() }.toSet() shouldBe setOf(1L, 2L)
            outStandingRecords.map { it.reference!!.toLong() }.toSet() shouldBe setOf(1L, 2L)
            outStandingRecords.map { it.completedAt }.distinct() shouldBe listOf(null)
            outStandingRecords.map { it.retryCount }.toSet() shouldBe setOf(0)

            completedNotFailedRecords.map { it.reference!!.toLong() }.toSet() shouldBe setOf(1L, 2L)
            completedNotFailedRecords.map { it.eventType }.toSet() shouldBe setOf(EventType.ADDRESS, EventType.ACCOUNT)
            completedNotFailedRecords.map { it.completedAt } shouldNotContain null
            completedNotFailedRecords.map { it.retryCount }.toSet() shouldBe setOf(null)

            allRecordsInDb.map { it.syncPid }.distinct() shouldBe listOf(null)
            allRecordsInDb.map { it.errorCode }.distinct() shouldBe listOf(null)
            allRecordsInDb.map { it.errorDescription }.distinct() shouldBe listOf(null)
        }

        should("given number of failed events larger than the batch size, only some of the failed records should be processed") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.MPAN,
                "3",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.PAYMENT_METHOD,
                "4",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )

            getAllSyncEvents(db).size shouldBe 4
            getFailedEvents(db).size shouldBe 4
            getOutstandingEvents(db).size shouldBe 0
            getCompletedNotFailedEvents(db).size shouldBe 0

            syncRepository.resetFailedEvents(2L)

            // THEN record number equal to the batch size should be processed only
            db.fetchCount(SYNC_EVENT) shouldBe 6
            getFailedEvents(db).size shouldBe 2
            getOutstandingEvents(db).size shouldBe 2
            getCompletedNotFailedEvents(db).size shouldBe 2
        }

        should("given failed and unprocessed records present with the same 'reference - event type' combination, no new records should be inserted but failed records should be reset") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(db, EventType.ADDRESS, "1", OperationType.UPDATE)
            createEvent(db, EventType.ACCOUNT, "2", OperationType.UPDATE)

            getAllSyncEvents(db).size shouldBe 4
            getFailedEvents(db).size shouldBe 2
            getOutstandingEvents(db).size shouldBe 2
            getCompletedNotFailedEvents(db).size shouldBe 0

            syncRepository.resetFailedEvents(10L)

            getAllSyncEvents(db).size shouldBe 4
            getFailedEvents(db).size shouldBe 0
            getOutstandingEvents(db).size shouldBe 2
            getCompletedNotFailedEvents(db).size shouldBe 2
        }

        should("given multiple failed records present with the same 'ref-eventype' combination, only one new is inserted but all old ones are reset") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )

            getAllSyncEvents(db).size shouldBe 3
            getFailedEvents(db).size shouldBe 3
            getOutstandingEvents(db).size shouldBe 0
            getCompletedNotFailedEvents(db).size shouldBe 0

            syncRepository.resetFailedEvents(10L)

            getAllSyncEvents(db).size shouldBe 4
            getFailedEvents(db).size shouldBe 0
            getOutstandingEvents(db).size shouldBe 1
            getCompletedNotFailedEvents(db).size shouldBe 3
        }

        should("given no failed records present in the DB, resetFailedEvents() should not alter the DB") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                completionReason = "completion_reason",
            )
            createEvent(db, EventType.ADDRESS, "1", OperationType.UPDATE)
            createEvent(db, EventType.ACCOUNT, "2", OperationType.UPDATE)

            getAllSyncEvents(db).size shouldBe 4
            getFailedEvents(db).size shouldBe 0
            getOutstandingEvents(db).size shouldBe 2
            getCompletedNotFailedEvents(db).size shouldBe 2

            val recordsBeforeFailedEventsJob = getAllSyncEvents(db)

            syncRepository.resetFailedEvents(10L)
            val recordsAfterFailedEventsJob = getAllSyncEvents(db)

            recordsAfterFailedEventsJob shouldBe recordsBeforeFailedEventsJob
        }

        should("given failed sync events present in the DB, the generated reinserted records are inserted in one batch") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.MPAN,
                "3",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusSeconds(800),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )

            // two failed sync events present in the DB
            getAllSyncEvents(db).size shouldBe 3
            getFailedEvents(db).size shouldBe 3
            getOutstandingEvents(db).size shouldBe 0

            syncRepository.resetFailedEvents(10L)

            // THEN new SyncEvent records should be inserted for reprocessing and the old records reset
            getOutstandingEvents(db).size shouldBe 3

            // AND the created at field should have the same value on all new records
            getOutstandingEvents(db).map { it.createdAt }.distinct().size shouldBe 1
        }

        should("given completeCorruptedData enables and sync events with expected errors, then events should be mark as corrupted") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "EntityNotFound",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "SyncDelayed",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.MPAN,
                "3",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "SyncDelayed",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createSyncSourceHealthCheck(db, LocalDateTime.now())

            syncRepositoryCompleteCorruptedDataEnabled.resetFailedEvents(10L)

            // THEN corrupted events should be mark as corrupted and completed
            getCompletedEvents(db).size shouldBe 3

            // AND the completion reason field should be mark as POTENTIALLY_CORRUPTED
            getCompletedEvents(db).map { it.completionReason }.distinct()[0] shouldBe "POTENTIALLY_CORRUPT"
        }

        should("given completeCorruptedData enables, sync events with expected errors not in given threshold , then events should not be mark as corrupted") {

            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "EntityNotFound",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "SyncDelayed",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.MPAN,
                "3",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "SyncDelayed",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createSyncSourceHealthCheck(db, LocalDateTime.now().minusHours(15))

            syncRepositoryCompleteCorruptedDataEnabled.resetFailedEvents(10L)

            // THEN new SyncEvent records should be inserted for reprocessing and the old records reset
            getOutstandingEvents(db).size shouldBe 3

            // AND the created at field should have the same value on all new records
            getOutstandingEvents(db).map { it.createdAt }.distinct().size shouldBe 1
        }

        should("given completeCorruptedData enables, sync events with other errors, then events should not be mark as corrupted") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.MPAN,
                "3",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusHours(3),
                completedAt = OffsetDateTime.now().minusSeconds(600),
                errorCode = "100",
                errorDescription = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createSyncSourceHealthCheck(db, LocalDateTime.now())

            syncRepositoryCompleteCorruptedDataEnabled.resetFailedEvents(10L)

            // THEN new SyncEvent records should be inserted for reprocessing and the old records reset
            getOutstandingEvents(db).size shouldBe 3

            // AND the created at field should have the same value on all new records
            getOutstandingEvents(db).map { it.createdAt }.distinct().size shouldBe 1
        }

    }

    context("::archiveCompletedEventsOlderThan") {
        afterTest {
            truncateEvents(db)
        }

        should("given old completed sync events present in the DB, they should be moved to completed_sync_events table") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusDays(11),
                completedAt = OffsetDateTime.now().minusDays(11),
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusDays(11),
                completedAt = OffsetDateTime.now().minusDays(11),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )

            // two completed old sync event present in the DB
            getAllSyncEvents(db).size shouldBe 2
            getCompletedEvents(db).size shouldBe 2
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 0

            syncRepository.archiveCompletedEventsOlderThan(10L, 10)

            // THEN SyncEvent records should be deleted, and completed_sync_event records should be inserted
            db.fetchCount(SYNC_EVENT) shouldBe 0
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 2
        }

        should("given old and new completed and uncompleted sync events present in the DB, only the old completed ones are moved to completed_sync_events table") {
            createEvent(
                db,
                EventType.ADDRESS,
                "1",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusDays(1),
                completedAt = OffsetDateTime.now().minusDays(1),
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.ACCOUNT,
                "2",
                OperationType.UPDATE,
                claimedAt = OffsetDateTime.now().minusDays(11),
                completedAt = OffsetDateTime.now().minusDays(11),
                errorCode = "error",
                retryCount = maxRetryCount,
                completionReason = "completion_reason",
            )
            createEvent(
                db,
                EventType.METER,
                "3",
                OperationType.UPDATE,
                claimedAt = null,
                completedAt = null
            )

            // two completed old sync event present in the DB
            getAllSyncEvents(db).size shouldBe 3
            getCompletedEvents(db).size shouldBe 2
            getOutstandingEvents(db).size shouldBe 1
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 0

            syncRepository.archiveCompletedEventsOlderThan(10L, 10)

            // THEN SyncEvent records should be deleted, and completed_sync_event records should be inserted
            db.fetchCount(SYNC_EVENT) shouldBe 2
            getOutstandingEvents(db).size shouldBe 1
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 1
        }

    }

    context("::truncateCompletedEventsOlderThan") {
        afterTest { truncateEvents(db) }

        should("given no old completed events in db, they should all be ignored") {
            // GIVEN
            db.newRecord(COMPLETED_SYNC_EVENT, completedSyncEvent).apply { save() }
            db.newRecord(COMPLETED_SYNC_EVENT, completedSyncEvent.copy(id = ID_2)).apply { save() }
            val initialInDb = db.fetchCount(COMPLETED_SYNC_EVENT)

            // WHEN
            syncRepository.truncateCompletedEventsOlderThan(ONE_MONTH_IN_HOURS)

            // THEN
            initialInDb shouldBe 2
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 2
        }

        should("given old completed events but none POTENTIALLY_CORRUPTED, they should all be deleted") {
            // GIVEN
            db.newRecord(COMPLETED_SYNC_EVENT, completedSyncEventOlderThan1Month).apply { save() }
            db.newRecord(COMPLETED_SYNC_EVENT, completedSyncEventOlderThan1Month.copy(id = ID_2)).apply { save() }
            val initialInDb = db.fetchCount(COMPLETED_SYNC_EVENT)

            // WHEN
            syncRepository.truncateCompletedEventsOlderThan(ONE_MONTH_IN_HOURS)

            // THEN
            initialInDb shouldBe 2
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 0
        }

        should("given 2 old completed events but one POTENTIALLY_CORRUPTED, only one should be ignored") {
            // GIVEN
            db.newRecord(COMPLETED_SYNC_EVENT, completedSyncEventOlderThan1Month).apply { save() }
            db.newRecord(
                COMPLETED_SYNC_EVENT,
                completedSyncEventOlderThan1Month.copy(id = ID_2, completionReason = POTENTIALLY_CORRUPT)
            ).apply { save() }
            val initialInDb = db.fetchCount(COMPLETED_SYNC_EVENT)

            // WHEN
            syncRepository.truncateCompletedEventsOlderThan(ONE_MONTH_IN_HOURS)

            // THEN
            initialInDb shouldBe 2
            db.fetchCount(COMPLETED_SYNC_EVENT) shouldBe 1
        }
    }

})


private fun validateDb(
    context: DSLContext,
    completedCount: Int,
    outstandingCount: Int,
    claimedCount: Int,
    failedCount: Int,
) {
    val outstandingEvents = getOutstandingEvents(context)
    val completedEvents = getCompletedEvents(context)
    val claimedEvents = getClaimedEvents(context)
    val failedEvents = getFailedEvents(context)

    outstandingEvents.size shouldBe outstandingCount
    completedEvents.size shouldBe completedCount
    claimedEvents.size shouldBe claimedCount
    failedEvents.size shouldBe failedCount

    outstandingEvents.forEach {
        it.claimedAt shouldBe null
        it.completedAt shouldBe null
        it.syncPid shouldBe null
    }

    completedEvents.forEach {
        it.claimedAt shouldNotBe null
        it.completedAt shouldNotBe null
        it.syncPid shouldBe null
    }

    claimedEvents.forEach {
        it.claimedAt shouldNotBe null
        it.completedAt shouldBe null
        it.syncPid shouldNotBe null // The pid will still be in place until the event is released
    }
}

private fun createEvent(
    context: DSLContext,
    eventType: EventType,
    reference: String,
    operationType: OperationType,
    retryCount: Int? = 0,
    completedAt: OffsetDateTime? = null,
    errorCode: String? = null,
    errorDescription: String? = null,
    claimedAt: OffsetDateTime? = null,
    completionReason: String? = null,
    processedBy: String? = null,
) =
    context.insertInto(
        SYNC_EVENT,
        SYNC_EVENT.EVENT_TYPE,
        SYNC_EVENT.REFERENCE,
        SYNC_EVENT.OPERATION_TYPE,
        SYNC_EVENT.RETRY_COUNT,
        SYNC_EVENT.COMPLETED_AT,
        SYNC_EVENT.ERROR_CODE,
        SYNC_EVENT.ERROR_DESCRIPTION,
        SYNC_EVENT.CLAIMED_AT,
        SYNC_EVENT.COMPLETION_REASON,
        SYNC_EVENT.PROCESSED_BY,
    )
        .values(
            eventType,
            reference,
            operationType,
            retryCount,
            completedAt,
            errorCode,
            errorDescription,
            claimedAt,
            completionReason,
            processedBy,
        )
        .execute()

private fun createSyncSourceHealthCheck(
    context: DSLContext,
    lastModifiedAt: LocalDateTime,
) =
    context.insertInto(
        SYNC_SOURCE_HEALTH_CHECK,
        SYNC_SOURCE_HEALTH_CHECK.LAST_MODIFIED,
    )
        .values(lastModifiedAt)
        .execute()


private fun truncateEvents(context: DSLContext) {
    context.truncateTable(SYNC_EVENT).execute()
    context.truncateTable(COMPLETED_SYNC_EVENT).execute()
    context.truncateTable(SYNC_SOURCE_HEALTH_CHECK).execute()
}

private fun getOutstandingEvents(context: DSLContext) =
    context.selectFrom(SYNC_EVENT)
        .where(SYNC_EVENT.CLAIMED_AT.isNull)
        .and(SYNC_EVENT.COMPLETED_AT.isNull)
        .and(SYNC_EVENT.RETRY_COUNT.lt(maxRetryCount))
        .fetchInto(SyncEvent::class.java)

private fun getCompletedEvents(context: DSLContext) =
    context.selectFrom(SYNC_EVENT)
        .where(SYNC_EVENT.COMPLETED_AT.isNotNull)
        .and(SYNC_EVENT.CLAIMED_AT.isNotNull)
        .fetchInto(SyncEvent::class.java)

private fun getCompletedNotFailedEvents(context: DSLContext) =
    context.selectFrom(SYNC_EVENT)
        .where(SYNC_EVENT.COMPLETED_AT.isNotNull)
        .and(SYNC_EVENT.CLAIMED_AT.isNotNull)
        .and((SYNC_EVENT.RETRY_COUNT.isNull).or(SYNC_EVENT.RETRY_COUNT.eq(0)))
        .fetchInto(SyncEvent::class.java)

private fun getClaimedEvents(context: DSLContext) =
    context.selectFrom(SYNC_EVENT)
        .where(SYNC_EVENT.COMPLETED_AT.isNull)
        .and(SYNC_EVENT.CLAIMED_AT.isNotNull)
        .and(SYNC_EVENT.SYNC_PID.isNotNull)
        .fetchInto(SyncEvent::class.java)

private fun getFailedEvents(context: DSLContext) =
    context.selectFrom(SYNC_EVENT)
        .where(SYNC_EVENT.RETRY_COUNT.eq(maxRetryCount))
        .fetchInto(SyncEvent::class.java)

private fun getAllSyncEvents(db: DSLContext): List<SyncEvent> {
    return db.select().from(SYNC_EVENT).orderBy(SYNC_EVENT.ID).fetchInto(SyncEvent::class.java)
}
