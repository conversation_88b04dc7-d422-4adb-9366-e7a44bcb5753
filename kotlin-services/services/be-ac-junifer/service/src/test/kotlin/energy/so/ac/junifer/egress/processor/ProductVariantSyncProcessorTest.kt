package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JuniferProductVariantRepository
import energy.so.ac.junifer.egress.exceptions.AutoDiscardableException
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.egress.exceptions.SyncFixedDateDelayException
import energy.so.ac.junifer.fixtures.ELEC_RATE_ID
import energy.so.ac.junifer.fixtures.ELEC_VARIANT_ID
import energy.so.ac.junifer.fixtures.GAS_RATE_ID
import energy.so.ac.junifer.fixtures.GAS_VARIANT_ID
import energy.so.ac.junifer.fixtures.HALF_HOUR_RATE_DFN_ID
import energy.so.ac.junifer.fixtures.HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID
import energy.so.ac.junifer.fixtures.JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_BUNDLE_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_PRODUCT_DFN_ID
import energy.so.ac.junifer.fixtures.JUNIFER_RATE_NAME_ID
import energy.so.ac.junifer.fixtures.PRODUCT_ID
import energy.so.ac.junifer.fixtures.PRODUCT_RATE_ID
import energy.so.ac.junifer.fixtures.PRODUCT_VARIANT_ID
import energy.so.ac.junifer.fixtures.ProductPrecannedData.baseJuniferProductBndlPojo
import energy.so.ac.junifer.fixtures.ProductPrecannedData.productSyncEvent
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createElecTouVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createElecVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createGasVariantEntitySync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createGasVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.createProductVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.deleteProductVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.elecSingleDfnType
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.elecSyncResponse
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.gasDfnType
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.gasSyncResponse
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.patchElecProductVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.patchElecVariantSync
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.testElecJuniferRate
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.testElecSingleProductDfn
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.testElecTouJuniferRate
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.testGasJuniferRate
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.testGasProductDfn
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.variantCreateSyncEvent
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.variantSyncEvent
import energy.so.ac.junifer.fixtures.ProductVariantPrecannedData.variantSyncResponse
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.grpc.extensions.toNullableInt64
import energy.so.commons.model.enums.EventType
import energy.so.products.client.v2.SyncClient
import energy.so.products.sync.v2.copy
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime

class ProductVariantSyncProcessorTest : BehaviorSpec({

    val mockMapper = mockk<EntityMapper>()
    val mockVariantRepo = mockk<JuniferProductVariantRepository>()
    val mockSyncClient = mockk<SyncClient>()

    val processor = ProductVariantSyncProcessor(mockMapper, mockVariantRepo, mockSyncClient, 2, 1)

    afterEach {
        confirmVerified(mockMapper, mockVariantRepo, mockSyncClient)
    }

    given("an existing mapped elec product and no mapped variant or elec variant, and an existing junifer product definition, a hh rate and product rate id") {
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID.toString()
            )
        } returns HALF_HOUR_RATE_DFN_ITEM_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_RATE,
                JUNIFER_RATE_NAME_ID.toString()
            )
        } returns PRODUCT_RATE_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN,
                JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()

            )
        } returns HALF_HOUR_RATE_DFN_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk!!.toString(),
            )
        } returns PRODUCT_ID.toString()
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(
            testElecTouJuniferRate.copy(
                rateNameId = JUNIFER_RATE_NAME_ID
            )
        )
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType

        `when`("a supported event is generated") {
            val elecVariantEntityRequest = createElecTouVariantSync.copy {
                elecVariantEntity = createElecTouVariantSync.elecVariantEntity.copy {
                    productRateId = PRODUCT_RATE_ID.toNullableInt64()
                }
            }
            coEvery { mockSyncClient.syncProductVariantEntity(createProductVariantSync) } returns variantSyncResponse
            coEvery { mockSyncClient.syncElecVariantEntityRequest(elecVariantEntityRequest) } returns elecSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT,
                    JUNIFER_PRODUCT_DFN_ID.toString(),
                    PRODUCT_VARIANT_ID.toString()
                )
            }
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT_ELEC,
                    ELEC_RATE_ID.toString(),
                    ELEC_VARIANT_ID.toString()
                )
            }

            processor.process(variantCreateSyncEvent)

            then("a product variant should be created") {
                coVerify { mockSyncClient.syncProductVariantEntity(createProductVariantSync) }
                coVerify { mockSyncClient.syncElecVariantEntityRequest(elecVariantEntityRequest) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk!!.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                        JUNIFER_HALF_HOUR_RATE_DFN_ITEM_ID.toString(),
                    )
                }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                        PRODUCT_VARIANT_ID.toString(),
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT_ELEC,
                        ELEC_RATE_ID.toString(),
                        ELEC_VARIANT_ID.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        JUNIFER_HALF_HOUR_RATE_DFN_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        JUNIFER_RATE_NAME_ID.toString()
                    )
                }
            }
        }
    }

    given("an existing mapped elec product and no mapped variant or elec variant, and an existing junifer product definition, and no hh rate") {
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                null.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN,
                null.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk!!.toString(),
            )
        } returns PRODUCT_ID.toString()
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(testElecJuniferRate)
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_RATE,
                "null"
            )
        } returns null
        `when`("a supported event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(createProductVariantSync) } returns variantSyncResponse
            coEvery { mockSyncClient.syncElecVariantEntityRequest(createElecVariantSync) } returns elecSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT,
                    JUNIFER_PRODUCT_DFN_ID.toString(),
                    PRODUCT_VARIANT_ID.toString()
                )
            }
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT_ELEC,
                    ELEC_RATE_ID.toString(),
                    ELEC_VARIANT_ID.toString()
                )
            }

            processor.process(variantCreateSyncEvent)

            then("a product variant should be created") {
                coVerify { mockSyncClient.syncProductVariantEntity(createProductVariantSync) }
                coVerify { mockSyncClient.syncElecVariantEntityRequest(createElecVariantSync) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk!!.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                        null.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        null.toString(),
                    )
                }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                        PRODUCT_VARIANT_ID.toString(),
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT_ELEC,
                        ELEC_RATE_ID.toString(),
                        ELEC_VARIANT_ID.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        null.toString()
                    )
                }
            }
        }
    }

    given("an existing mapped gas product and no mapped variant or gas variant, and an existing junifer product definition") {

        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testGasProductDfn.productbundledfnfk!!.toString()
            )
        } returns PRODUCT_ID.toString()
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_GAS, GAS_RATE_ID.toString()) } returns null
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(testGasJuniferRate)
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testGasProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testGasProductDfn.productbundledfnfk!!) } returns gasDfnType

        `when`("a supported event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(createGasVariantSync) } returns variantSyncResponse
            coEvery { mockSyncClient.syncGasVariantEntityRequest(createGasVariantEntitySync) } returns gasSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT,
                    JUNIFER_PRODUCT_DFN_ID.toString(),
                    PRODUCT_VARIANT_ID.toString()
                )
            }
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT_GAS,
                    GAS_RATE_ID.toString(),
                    GAS_VARIANT_ID.toString()
                )
            }

            processor.process(variantCreateSyncEvent)

            then("a product variant should be created") {
                coVerify { mockSyncClient.syncProductVariantEntity(createGasVariantSync) }
                coVerify { mockSyncClient.syncGasVariantEntityRequest(createGasVariantEntitySync) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testGasProductDfn.productbundledfnfk!!) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_GAS, GAS_RATE_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testGasProductDfn.productbundledfnfk!!.toString()
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                        PRODUCT_VARIANT_ID.toString()
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT_GAS,
                        GAS_RATE_ID.toString(),
                        GAS_VARIANT_ID.toString()
                    )
                }
            }
        }
    }

    given("no exising mapped product, and an existing junifer product definition") {

        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns PRODUCT_VARIANT_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk.toString()
            )
        } returns null
        every {
            mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID)
        } returns JUNIFER_PRODUCT_DFN_ID
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType

        `when`("a handled event is generated") {

            shouldThrow<SyncDelayedException> { processor.process(variantSyncEvent) }

            then("an exception should be thrown") {
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(
                        JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString()
                    )
                }
            }
        }
    }

    given("an existing mapped product and variant and elec variant, and an existing junifer product definition") {

        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns PRODUCT_VARIANT_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT_ELEC,
                ELEC_RATE_ID.toString()
            )
        } returns ELEC_VARIANT_ID.toString()
        every { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, null.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, null.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_RATE, null.toString()) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk.toString()
            )
        } returns PRODUCT_ID.toString()
        every { mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID) } returns JUNIFER_PRODUCT_DFN_ID
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(testElecJuniferRate)
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType

        `when`("a handled event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(patchElecVariantSync) } returns variantSyncResponse
            coEvery { mockSyncClient.syncElecVariantEntityRequest(patchElecProductVariantSync) } returns elecSyncResponse

            processor.process(variantSyncEvent)

            then("a product variant should be patched") {
                coVerify { mockSyncClient.syncProductVariantEntity(patchElecVariantSync) }
                coVerify { mockSyncClient.syncElecVariantEntityRequest(patchElecProductVariantSync) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID)
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                        null.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                        null.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_RATE,
                        null.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString()
                    )
                }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) }
            }
        }
    }

    given("an existing mapped product and variant, and no existing junifer product definition") {
        every {
            mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString())
        } returns PRODUCT_VARIANT_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT_ELEC,
                ELEC_RATE_ID.toString()
            )
        } returns ELEC_VARIANT_ID.toString()
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                null.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.HALF_HOUR_RATE_DFN,
                null.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_RATE,
                null.toString()
            )
        } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk.toString()
            )
        } returns PRODUCT_ID.toString()
        every { mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID) } returns JUNIFER_PRODUCT_DFN_ID
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(testElecJuniferRate)
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType

        `when`("a handled event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(any()) } returns variantSyncResponse
            coEvery { mockSyncClient.syncElecVariantEntityRequest(any()) } returns elecSyncResponse

            processor.process(variantSyncEvent)

            then("a product variant should be patched") {
                coVerify { mockSyncClient.syncProductVariantEntity(patchElecVariantSync) }
                coVerify { mockSyncClient.syncElecVariantEntityRequest(any()) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID)
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString())
                }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM, null.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.HALF_HOUR_RATE_DFN, null.toString()) }
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_RATE, null.toString()) }
            }
        }
    }

    // i.e. the case where we do not wish to do any processing on an event
    given("an invalid event") {

        every {
            mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID)
        } returns null

        `when`("a handled event is generated") {

            // Nothing should happen with this event
            processor.process(variantSyncEvent)

            then("nothing should happen") {
                verify {
                    mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID)
                }
            }
        }
    }

    given("getProcessedTypes method") {

        `when`("called") {
            val expectedProcessedEventTypes = listOf(
                EventType.PRODUCT_DEFINITION,
                EventType.RECURRING_ITEM_DEFINITION,
                EventType.RECURRING_ITEM_DEFINITION_AMOUNT,
                EventType.PRICE_PLAN_ROW_DIMENSION,
                EventType.PRODUCT_BUNDLE_DEFINITION,
                EventType.SERVICE_RATING_RATE,
                EventType.METER_SERVICE_DEFINITION,
                EventType.MPAN_PRODUCT_ITEM_DEFINITION,
                EventType.MPAN_PRODUCT_ITEM_DEFINITION_RATE,
                EventType.MPAN_PRODUCT_ITEM_DEFINITION_PERIOD
            )

            processor.getProcessedTypes() shouldBe expectedProcessedEventTypes
        }
    }

    given("no existing mapped variants and no rates are present in the junifer data") {

        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT_ELEC, ELEC_RATE_ID.toString()) } returns null
        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT,
                testElecSingleProductDfn.productbundledfnfk!!.toString(),
            )
        } returns PRODUCT_ID.toString()
        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns emptyList()
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType


        `when`("a handled event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(createProductVariantSync) } returns variantSyncResponse
            justRun {
                mockMapper.createCoreMapping(
                    EntityIdentifier.PRODUCT_VARIANT,
                    JUNIFER_PRODUCT_DFN_ID.toString(),
                    PRODUCT_VARIANT_ID.toString()
                )
            }

            processor.process(variantCreateSyncEvent)

            then("only the product variant should be created") {
                coVerify { mockSyncClient.syncProductVariantEntity(createProductVariantSync) }
                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT,
                        testElecSingleProductDfn.productbundledfnfk!!.toString(),
                    )
                }
                verify {
                    mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString())
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString(),
                        PRODUCT_VARIANT_ID.toString(),
                    )
                }
            }
        }
    }

    given("a deleted product dfn and no mapped product variant") {

        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } throws EntityNotFoundException("Oops")

        `when`("a handled event is generated") {

            processor.process(variantCreateSyncEvent)

            then("nothing should happen") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
            }
        }
    }

    given("a deleted product dfn and an existing mapped product variant") {

        every {
            mockMapper.getCoreId(
                EntityIdentifier.PRODUCT_VARIANT,
                JUNIFER_PRODUCT_DFN_ID.toString()
            )
        } returns PRODUCT_VARIANT_ID.toString()
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } throws EntityNotFoundException("Oops")

        `when`("a handled event is generated") {

            coEvery { mockSyncClient.syncProductVariantEntity(deleteProductVariantSync) } returns variantSyncResponse

            processor.process(variantCreateSyncEvent)

            then("the product variant should be deleted") {
                coVerify { mockSyncClient.syncProductVariantEntity(deleteProductVariantSync) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.PRODUCT_VARIANT,
                        JUNIFER_PRODUCT_DFN_ID.toString()
                    )
                }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
            }
        }
    }

    given("a junifer product bundle without a type") {
        every { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) } returns null
        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } throws EntityNotFoundException(
            "Oops"
        )

        `when`("a handled event is generated") {

            shouldThrow<AutoDiscardableException> { processor.process(variantCreateSyncEvent) }

            then("the event should be discarded") {
                verify { mockMapper.getCoreId(EntityIdentifier.PRODUCT_VARIANT, JUNIFER_PRODUCT_DFN_ID.toString()) }
                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
            }
        }
    }

    given("a PRODUCT_BUNDLE_DEFINITION event") {
        and("the corresponding ProductBundleDfn found in db") {

            and("product bundle dfn has status ACTIVE") {
                val productBundleDfnWithActiveStatus = baseJuniferProductBndlPojo.copy(status = "Active")
                every { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns productBundleDfnWithActiveStatus

                and("event previously delayed") {
                    val previouslyDelayedProductBundleDfnEvent =
                        productSyncEvent.copy(nextRetryOn = LocalDateTime.now())

                    and("productDfnIds list corresponding to productBundleDfn is not empty") {
                        every { mockVariantRepo.getProductDfnIdsForProductBundleDfn(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns listOf(
                            JUNIFER_PRODUCT_DFN_ID
                        )

                        every {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_VARIANT,
                                JUNIFER_PRODUCT_DFN_ID.toString()
                            )
                        } returns PRODUCT_VARIANT_ID.toString()
                        every {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT_VARIANT_ELEC,
                                ELEC_RATE_ID.toString()
                            )
                        } returns ELEC_VARIANT_ID.toString()
                        every {
                            mockMapper.getCoreId(
                                EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                                null.toString()
                            )
                        } returns null
                        every {
                            mockMapper.getCoreId(
                                EntityIdentifier.HALF_HOUR_RATE_DFN,
                                null.toString()
                            )
                        } returns null
                        every {
                            mockMapper.getCoreId(EntityIdentifier.PRODUCT_RATE, null.toString())
                        } returns null
                        every {
                            mockMapper.getCoreId(
                                EntityIdentifier.PRODUCT,
                                testElecSingleProductDfn.productbundledfnfk.toString()
                            )
                        } returns PRODUCT_ID.toString()
                        every {
                            mockVariantRepo.getProductDfnIdFromMpanProductItemDfnPeriod(
                                JUNIFER_MPAN_PRODUCT_ITEM_DFN_PERIOD_ID
                            )
                        } returns JUNIFER_PRODUCT_DFN_ID
                        every { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) } returns listOf(
                            testElecJuniferRate
                        )
                        every { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) } returns testElecSingleProductDfn
                        every { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) } returns elecSingleDfnType


                        `when`("process event") {

                            coEvery { mockSyncClient.syncProductVariantEntity(patchElecVariantSync) } returns variantSyncResponse
                            coEvery { mockSyncClient.syncElecVariantEntityRequest(patchElecProductVariantSync) } returns elecSyncResponse

                            processor.process(previouslyDelayedProductBundleDfnEvent)

                            then("resync product variants for all product dnf ids") {
                                verify { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                                verify {
                                    mockVariantRepo.getProductDfnIdsForProductBundleDfn(
                                        JUNIFER_PRODUCT_BUNDLE_DFN_ID
                                    )
                                }
                                coVerify { mockSyncClient.syncProductVariantEntity(patchElecVariantSync) }
                                coVerify { mockSyncClient.syncElecVariantEntityRequest(patchElecProductVariantSync) }
                                verify { mockVariantRepo.getProductDfnsById(JUNIFER_PRODUCT_DFN_ID) }
                                verify { mockVariantRepo.getProductDfnById(JUNIFER_PRODUCT_DFN_ID) }
                                verify { mockVariantRepo.getProductBundleDfnType(testElecSingleProductDfn.productbundledfnfk!!) }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.PRODUCT,
                                        testElecSingleProductDfn.productbundledfnfk.toString()
                                    )
                                }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.PRODUCT_VARIANT,
                                        JUNIFER_PRODUCT_DFN_ID.toString()
                                    )
                                }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.PRODUCT_VARIANT_ELEC,
                                        ELEC_RATE_ID.toString()
                                    )
                                }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.HALF_HOUR_RATE_DFN_ITEM,
                                        null.toString()
                                    )
                                }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.HALF_HOUR_RATE_DFN,
                                        null.toString()
                                    )
                                }
                                verify {
                                    mockMapper.getCoreId(
                                        EntityIdentifier.PRODUCT_RATE,
                                        null.toString()
                                    )
                                }
                            }
                        }
                    }

                    and("productDfnIds list corresponding to productBundleDfn is empty") {
                        every { mockVariantRepo.getProductDfnIdsForProductBundleDfn(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns listOf()

                        `when`("process event") {
                            processor.process(previouslyDelayedProductBundleDfnEvent)

                            then("nothing happens") {
                                verify { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                                verify {
                                    mockVariantRepo.getProductDfnIdsForProductBundleDfn(
                                        JUNIFER_PRODUCT_BUNDLE_DFN_ID
                                    )
                                }
                            }
                        }
                    }
                }

                and("event not previously delayed") {

                    `when`("process event") {
                        then("delay event for 1 minute") {
                            shouldThrow<SyncFixedDateDelayException> { processor.process(productSyncEvent) }
                            verify { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                        }
                    }
                }
            }

            and("product bundle dfn does not have status ACTIVE") {
                every { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } returns baseJuniferProductBndlPojo

                `when`("process event") {
                    processor.process(productSyncEvent)

                    then("nothing happens") {
                        verify { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                    }
                }
            }
        }

        and("the corresponding ProductBundleDfn not found in db") {
            every { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) } throws EntityNotFoundException(
                "Oops"
            )

            `when`("process event") {
                then("should throw SyncDelayedException") {
                    shouldThrow<SyncDelayedException> { processor.process(productSyncEvent) }
                    verify { mockVariantRepo.getProductBundleDfnById(JUNIFER_PRODUCT_BUNDLE_DFN_ID) }
                }
            }
        }
    }
})
