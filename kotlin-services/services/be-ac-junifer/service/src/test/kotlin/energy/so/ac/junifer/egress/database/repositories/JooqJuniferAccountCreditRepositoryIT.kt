package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.GENERIC_STRING
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.ID_1
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCredit
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.accountCreditReason
import energy.so.ac.junifer.fixtures.AccountCreditPrecannedData.salesTax
import energy.so.commons.exceptions.services.EntityNotFoundException
import energy.so.commons.model.tables.pojos.Junifer_Accountcredit
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNTCREDIT
import energy.so.commons.model.tables.references.JUNIFER__ACCOUNTCREDITREASON
import energy.so.commons.model.tables.references.JUNIFER__SALESTAX
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.assertThrows

private const val RANDOM_ID = 123456L

class JooqJuniferAccountCreditRepositoryIT : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferAccountCreditRepository(db)

    given("::getAccountCredit") {
        and("no account credit saved in db") {
            `when`("call getAccountCredit") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.getAccountCredit(ID_1)
                    }
                }
            }
        }
        and("no account credit saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__ACCOUNTCREDIT, accountCredit))

            `when`("call getAccountCredit") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.getAccountCredit(RANDOM_ID)
                    }
                }
            }
        }
        and("account credit saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__ACCOUNTCREDIT, accountCredit))

            `when`("call getAccountCredit") {
                val result = repo.getAccountCredit(ID_1)

                then("return the corresponding account credit") {
                    result shouldMatch accountCredit
                }
            }
        }
    }

    given("::getAccountCreditReason") {
        and("no account credit reason saved in db") {
            `when`("call getAccountCreditReason") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.getAccountCreditReason(ID_1)
                    }
                }
            }
        }
        and("no account credit reason saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__ACCOUNTCREDITREASON, accountCreditReason))

            `when`("call getAccountCreditReason") {
                then("throw EntityNotFoundException") {
                    assertThrows<EntityNotFoundException> {
                        repo.getAccountCreditReason(RANDOM_ID)
                    }
                }
            }
        }
        and("account credit reason saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__ACCOUNTCREDITREASON, accountCreditReason))

            `when`("call getAccountCreditReason") {
                val result = repo.getAccountCreditReason(ID_1)

                then("return the corresponding account credit reason") {
                    result shouldBe accountCreditReason
                }
            }
        }
    }

    given("::getSalesTaxName") {
        and("no salesTax saved in db") {
            `when`("call getSalesTaxName") {
                val result = repo.getSalesTaxName(ID_1)

                then("result is null") {
                    result shouldBe null
                }
            }
        }
        and("no salesTax saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__SALESTAX, salesTax))

            `when`("call getSalesTaxName") {
                val result = repo.getSalesTaxName(RANDOM_ID)

                then("result is null") {
                    result shouldBe null
                }
            }
        }
        and("salesTax saved with the corresponding id") {
            db.executeInsert(db.newRecord(JUNIFER__SALESTAX, salesTax))

            `when`("call getSalesTaxName") {
                val result = repo.getSalesTaxName(ID_1)

                then("return the corresponding salesTax") {
                    result shouldBe GENERIC_STRING
                }
            }
        }
    }
})

private infix fun Junifer_Accountcredit.shouldMatch(other: Junifer_Accountcredit) {
    id shouldBe other.id
    grossamount?.toDouble() shouldBe other.grossamount?.toDouble()
    netamount?.toDouble() shouldBe other.netamount?.toDouble()
    salestaxamount?.toDouble() shouldBe other.salestaxamount?.toDouble()
    salestaxfk shouldBe other.salestaxfk
    accountcreditreasonfk shouldBe other.accountcreditreasonfk
    accounttransactionfk shouldBe other.accounttransactionfk
    reference shouldBe other.reference
    deletefl shouldBe other.deletefl
    versionno shouldBe other.versionno
    partitionid shouldBe other.partitionid
}
