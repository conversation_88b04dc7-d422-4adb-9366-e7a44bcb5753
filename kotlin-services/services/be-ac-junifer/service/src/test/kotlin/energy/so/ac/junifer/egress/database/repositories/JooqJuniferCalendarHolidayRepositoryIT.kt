package energy.so.ac.junifer.egress.database.repositories

import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.JUNIFER_CALENDAR_HOLIDAY_ID
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.testCalendarholiday
import energy.so.ac.junifer.fixtures.CalendarHolidayPrecannedData.testCalendarholiday2
import energy.so.commons.model.tables.references.JUNIFER__CALENDARHOLIDAY
import energy.so.database.test.installDatabase
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.jooq.DSLContext

class JooqJuniferCalendarHolidayRepositoryIT : BehaviorSpec({
    val db = installDatabase()
    val repo = JooqJuniferCalendarHolidayRepository(db)

    given("Calendar holiday exists in DB") {
        insertCalendarHoliday(db)

        `when`("Calendar holiday is queried") {
            val calendarHoliday = repo.getCalendarHoliday(JUNIFER_CALENDAR_HOLIDAY_ID)

            then("Calendar holiday is returned") {

                with(calendarHoliday) {
                    this shouldNotBe null
                    id shouldBe testCalendarholiday.id
                    holidaydt shouldBe testCalendarholiday.holidaydt
                    calendartblfk shouldBe testCalendarholiday.calendartblfk
                    reference shouldBe testCalendarholiday.reference
                    deletefl shouldBe testCalendarholiday.deletefl
                    versionno shouldBe testCalendarholiday.versionno
                    partitionid shouldBe testCalendarholiday.partitionid
                }
            }
        }
    }

    given("get all calendar holidays in DB") {
        insertCalendarHoliday(db)
        insertExtraCalendarHoliday(db)

        `when`("Calendar holidays are queried") {
            val calendarHolidays = repo.getCalendarHolidays()

            then("Calendar holiday is returned") {

                with(calendarHolidays) {
                    this shouldNotBe null
                    this.size shouldBe 2
                }
            }
        }
    }

})

fun insertCalendarHoliday(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__CALENDARHOLIDAY, testCalendarholiday))
}

fun insertExtraCalendarHoliday(db: DSLContext) {
    db.executeInsert(db.newRecord(JUNIFER__CALENDARHOLIDAY, testCalendarholiday2))
}
