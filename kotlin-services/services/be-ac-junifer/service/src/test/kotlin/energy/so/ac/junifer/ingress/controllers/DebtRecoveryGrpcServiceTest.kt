package energy.so.ac.junifer.ingress.controllers

import energy.so.ac.junifer.ingress.models.finances.CreateDebtRecoveryPlanResponse
import energy.so.ac.junifer.ingress.services.debtRecovery.JuniferDebtRecoveryService
import energy.so.ac.junifer.v1.debtRecovery.createDebtRecoveryPlanRequest
import energy.so.commons.exceptions.grpc.UnknownGrpcException
import energy.so.commons.v2.dtos.idRequest
import io.kotest.assertions.assertSoftly
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDate
import org.junit.jupiter.api.assertThrows

class DebtRecoveryGrpcServiceTest : BehaviorSpec({

    val service = mockk<JuniferDebtRecoveryService>()
    val sut = DebtRecoveryGrpcService(service = service)

    given("debt recovery id") {
        and("successful") {
            coEvery { service.cancelDebtRecovery(any()) } returns Unit
            `when`("when cancel debt recovery") {
                sut.cancelDebtRecovery(idRequest { id = 1 })
                then("then invoke service") {
                    coVerify { service.cancelDebtRecovery(1L) }
                }
            }
        }
        and("fails") {
            coEvery { service.cancelDebtRecovery(any()) } throws RuntimeException("example")
            `when`("when cancel debt recovery") {
                then("then rethrow exception") {
                    assertThrows<UnknownGrpcException> { sut.cancelDebtRecovery(idRequest { id = 1 }) }
                    coVerify { service.cancelDebtRecovery(1L) }
                }
            }
        }
    }

    given("debt recovery request") {
        and("successful") {
            val testAccountId = 5000L
            val testMonthlyAmount = 5064.6
            val testToDate = LocalDate.parse("2024-11-06")
            val planId = 5124L
            val fromDate = LocalDate.now()
            coEvery {
                service.createDebtRecoveryPlan(
                    testAccountId,
                    testMonthlyAmount,
                    testToDate
                )
            } returns CreateDebtRecoveryPlanResponse(
                id = planId,
                fromDate = fromDate.toString(),
                toDate = testToDate.toString(),
                currencyISO = "GBP",
                amount = testMonthlyAmount,
                emptyMap()
            )
            `when`("when create debt recovery") {
                val actual = sut.createDebtRecovery(createDebtRecoveryPlanRequest {
                    accountId = testAccountId
                    monthlyAmount = testMonthlyAmount
                    toDate = testToDate.toString()
                })
                then("then invoke service") {
                    coVerify(exactly = 1) {
                        service.createDebtRecoveryPlan(
                            testAccountId,
                            testMonthlyAmount,
                            testToDate
                        )
                    }
                }
                then("rethrow exception") {
                    assertSoftly {
                        actual.planId shouldBe planId
                        actual.fromDate shouldBe fromDate.toString()
                        actual.toDate shouldBe testToDate.toString()
                        actual.currencyIso shouldBe "GBP"
                        actual.monthlyAmount shouldBe testMonthlyAmount
                    }
                }
            }
        }
        and("fails") {
            coEvery { service.createDebtRecoveryPlan(any(), any(), any()) } throws RuntimeException("example")
            `when`("when create debt recovery") {
                then("then rethrow exception") {
                    assertThrows<UnknownGrpcException> {
                        sut.createDebtRecovery(
                            createDebtRecoveryPlanRequest {
                                accountId = 1
                                monthlyAmount = 605.5
                                toDate = LocalDate.now().toString()
                            }
                        )
                    }
                }
            }
        }
    }

})
