package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.AccountPrecannedData.getTicketsForAccountNumberResponse
import energy.so.ac.junifer.fixtures.AccountPrecannedData.juniferGetAccountTicketsResponse
import energy.so.ac.junifer.ingress.models.accounts.toProtoResponse
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe

class JuniferGetAccountTicketsResponseTest : BehaviorSpec({

    given("junifer get account's ticket response") {
        val juniferResponse = juniferGetAccountTicketsResponse

        `when`("map to get account's ticket response") {
            val response = juniferResponse.toProtoResponse()

            then("return a corresponding GetTicketsForAccountNumberResponse") {
                response shouldBe getTicketsForAccountNumberResponse
            }
        }
    }
})