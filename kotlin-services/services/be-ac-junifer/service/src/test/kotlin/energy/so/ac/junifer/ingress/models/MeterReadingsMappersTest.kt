package energy.so.ac.junifer.ingress.models

import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsDto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithoutDetailsDto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithoutDetailsProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationError
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.submitMeterReadingValidationErrors
import energy.so.ac.junifer.ingress.models.assets.MeterReadingsWithTechnicalDetailsDto
import energy.so.ac.junifer.ingress.models.assets.MeterReadingsWithoutTechnicalDetailsDto
import energy.so.ac.junifer.ingress.models.assets.toProto
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe

class MeterReadingsMappersTest : BehaviorSpec({
    given("meterReadingsWithTechnicalDetails proto") {
        val proto = readingWithDetailsProto
        `when`("call MeterReadingsWithTechnicalDetailsDto fromProto") {
            val result = MeterReadingsWithTechnicalDetailsDto.fromProto(proto)
            then("result should match") {
                result shouldBe readingWithDetailsDto
            }
        }
    }

    given("meterReadingsWithoutTechnicalDetails proto") {
        val proto = readingWithoutDetailsProto
        `when`("call MeterReadingsWithoutTechnicalDetailsDto fromProto") {
            val result = MeterReadingsWithoutTechnicalDetailsDto.fromProto(proto)
            then("result should match") {
                result shouldBe readingWithoutDetailsDto
            }
        }
    }

    given("list of validation errors dto") {
        val dto = submitMeterReadingValidationErrors

        `when`("call List<ValidationError>::toProto") {
            val result = dto.toProto()
            then("result should not have errors") {
                result shouldNotBe null
                result.validationErrorsCount shouldBe 1

                val error = result.validationErrorsList[0]
                error.meterIdentifier shouldBe submitMeterReadingValidationError.meterIdentifier
                error.registerIdentifier shouldBe submitMeterReadingValidationError.registerIdentifier
                error.errorDetail.errorCode shouldBe submitMeterReadingValidationError.errorDetail.errorCode
                error.errorDetail.errorSeverity shouldBe submitMeterReadingValidationError.errorDetail.errorSeverity
                error.errorDetail.errorDescription.value shouldBe submitMeterReadingValidationError.errorDetail.errorDescription
            }
        }
    }
})
