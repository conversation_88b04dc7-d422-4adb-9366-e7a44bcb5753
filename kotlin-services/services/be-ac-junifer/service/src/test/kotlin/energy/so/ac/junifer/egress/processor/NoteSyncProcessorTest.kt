package energy.so.ac.junifer.egress.processor

import energy.so.ac.junifer.egress.database.repositories.JooqJuniferNoteRepository
import energy.so.ac.junifer.egress.exceptions.SyncDelayedException
import energy.so.ac.junifer.fixtures.JUNIFER_NOTE_ID
import energy.so.ac.junifer.fixtures.JUNIFER_TICKET_ID
import energy.so.ac.junifer.fixtures.NOTE_ENTITY_ID
import energy.so.ac.junifer.fixtures.TICKET_ENTITY_ID
import energy.so.ac.junifer.fixtures.createNoteEntityRequest
import energy.so.ac.junifer.fixtures.deleteNoteEntityRequest
import energy.so.ac.junifer.fixtures.juniferNote
import energy.so.ac.junifer.fixtures.noteSyncResponse
import energy.so.ac.junifer.fixtures.updateNoteEntityRequest
import energy.so.ac.junifer.mapping.EntityIdentifier
import energy.so.ac.junifer.mapping.EntityMapper
import energy.so.commons.model.enums.EventType
import energy.so.commons.model.tables.pojos.SyncEvent
import energy.so.tickets.client.v2.SyncClient
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify

class NoteSyncProcessorTest : BehaviorSpec({
    val mockMapper = mockk<EntityMapper>()
    val mockJooqJuniferNoteRepository = mockk<JooqJuniferNoteRepository>()
    val mockTicketSyncClient = mockk<SyncClient>()

    val sut = NoteSyncProcessor(
        mockMapper,
        mockTicketSyncClient,
        mockJooqJuniferNoteRepository
    )

    given("invalid Note Sync Event") {
        `when`("reference is null") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent())
                }.message shouldBe "Cannot process sync event without a reference"
            }
        }
        `when`("event is unsupported") {
            then("throw IllegalStateException") {
                shouldThrow<IllegalStateException> {
                    sut.process(SyncEvent(reference = "123", eventType = EventType.ACCOUNT))
                }.message shouldBe "Unsupported EventType ${EventType.ACCOUNT}"
            }
        }
    }
    given("a valid Note Sync Event") {
        `when`("junifer note is created") {
            every { mockMapper.getCoreId(EntityIdentifier.NOTE, JUNIFER_NOTE_ID.toString()) } returns null
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) } returns juniferNote
            coEvery {
                mockTicketSyncClient.syncNoteEntity(
                    createNoteEntityRequest
                )
            } returns noteSyncResponse
            every {
                mockMapper.createCoreMapping(
                    EntityIdentifier.NOTE,
                    JUNIFER_NOTE_ID.toString(),
                    NOTE_ENTITY_ID.toString()
                )
            } just Runs

            then("sync note entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_NOTE_ID.toString(), eventType = EventType.NOTE))
                verify { mockMapper.getCoreId(EntityIdentifier.NOTE, JUNIFER_NOTE_ID.toString()) }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString()
                    )
                }
                verify { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) }
                coVerify {
                    mockTicketSyncClient.syncNoteEntity(
                        createNoteEntityRequest
                    )
                }
                verify {
                    mockMapper.createCoreMapping(
                        EntityIdentifier.NOTE,
                        JUNIFER_NOTE_ID.toString(),
                        NOTE_ENTITY_ID.toString()
                    )
                }
            }
        }
        `when`("junifer ticket is not syncronized ") {
            every { mockMapper.getCoreId(EntityIdentifier.NOTE, JUNIFER_NOTE_ID.toString()) } returns null
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns null
            every { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) } returns juniferNote

            then("throw SyncDelayedException") {
                shouldThrow<SyncDelayedException> {
                    sut.process(SyncEvent(reference = JUNIFER_NOTE_ID.toString(), eventType = EventType.NOTE))
                }
            }
        }
        `when`("junifer note is updated") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.NOTE,
                    JUNIFER_NOTE_ID.toString()
                )
            } returns NOTE_ENTITY_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) } returns juniferNote
            coEvery {
                mockTicketSyncClient.syncNoteEntity(
                    updateNoteEntityRequest
                )
            } returns noteSyncResponse

            then("sync note entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_NOTE_ID.toString(), eventType = EventType.NOTE))

                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.NOTE,
                        JUNIFER_NOTE_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString()
                    )
                }
                verify { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) }
                coVerify {
                    mockTicketSyncClient.syncNoteEntity(
                        updateNoteEntityRequest
                    )
                }
            }
        }
        `when`("junifer note is deleted") {
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.NOTE,
                    JUNIFER_NOTE_ID.toString()
                )
            } returns NOTE_ENTITY_ID.toString()
            every {
                mockMapper.getCoreId(
                    EntityIdentifier.TICKET,
                    JUNIFER_TICKET_ID.toString()
                )
            } returns TICKET_ENTITY_ID.toString()
            every { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) } returns null
            coEvery {
                mockTicketSyncClient.syncNoteEntity(
                    deleteNoteEntityRequest
                )
            } returns noteSyncResponse
            every { mockMapper.deleteMappingByCoreId(EntityIdentifier.NOTE, NOTE_ENTITY_ID.toString()) } just Runs

            then("sync note entity successfully") {
                sut.process(SyncEvent(reference = JUNIFER_NOTE_ID.toString(), eventType = EventType.NOTE))
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.NOTE,
                        JUNIFER_NOTE_ID.toString()
                    )
                }
                verify {
                    mockMapper.getCoreId(
                        EntityIdentifier.TICKET,
                        JUNIFER_TICKET_ID.toString()
                    )
                }
                verify { mockJooqJuniferNoteRepository.findById(JUNIFER_NOTE_ID) }
                coVerify {
                    mockTicketSyncClient.syncNoteEntity(
                        deleteNoteEntityRequest
                    )
                }
                verify { mockMapper.deleteMappingByCoreId(EntityIdentifier.NOTE, NOTE_ENTITY_ID.toString()) }


            }
        }
    }

})
