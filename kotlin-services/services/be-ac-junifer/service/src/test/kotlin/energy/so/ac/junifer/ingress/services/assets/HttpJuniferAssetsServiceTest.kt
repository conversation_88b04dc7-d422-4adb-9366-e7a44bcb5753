package energy.so.ac.junifer.ingress.services.assets

import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.JUNIFER_METER_POINT_ID
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.estimatedUsageRequestData
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.juniferEstimatedUsageEmptyResponseJson
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.juniferEstimatedUsageResponse
import energy.so.ac.junifer.fixtures.EstimatedUsagePrecannedData.juniferEstimatedUsageResponseJson
import energy.so.ac.junifer.fixtures.MeterPointStructureData
import energy.so.ac.junifer.fixtures.MeterPointStructureData.JUNIFER_METER_POINT
import energy.so.ac.junifer.fixtures.MeterPointStructureData.POSTCODE
import energy.so.ac.junifer.fixtures.MeterPointStructureData.TEST_STRING
import energy.so.ac.junifer.fixtures.MeterPointStructureData.meterPointStructureRequest
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMpanRequest
import energy.so.ac.junifer.fixtures.MeterPointStructureData.searchMprnRequest
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.ONE_LONG
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.getMeterReadingsEmptyResponseJson
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.getMeterReadingsResponseJson
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingsEmptyReponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.meterReadingsReponse
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithDetailsValidateOnlyProto
import energy.so.ac.junifer.fixtures.MeterReadingsPrecannedData.readingWithoutDetailsProto
import energy.so.ac.junifer.fixtures.gspByPostcodeResponse
import energy.so.ac.junifer.fixtures.gspByPostcodeResponseJson
import energy.so.ac.junifer.fixtures.mpanDto
import energy.so.ac.junifer.fixtures.mprnDto
import energy.so.ac.junifer.fixtures.mprnsResponse
import energy.so.ac.junifer.ingress.junifer.JuniferError
import energy.so.ac.junifer.ingress.junifer.JuniferException
import energy.so.ac.junifer.ingress.junifer.JuniferSubmitMeterReadingError
import energy.so.ac.junifer.ingress.junifer.MockJuniferClient
import energy.so.ac.junifer.ingress.models.assets.JuniferEstimatedUsageResponse
import energy.so.ac.junifer.ingress.models.assets.SubmitMeterReadingsWithTechDetailsResponse
import energy.so.ac.junifer.ingress.models.assets.ValidationError
import energy.so.ac.junifer.v1.assets.copy
import energy.so.ac.junifer.v1.assets.gspGroupIdsByPostcodeRequest
import energy.so.ac.junifer.v1.assets.searchMpanRequest
import energy.so.ac.junifer.v1.assets.searchMprnRequest
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.matchers.shouldBe
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import java.time.LocalDate
import kotlinx.serialization.json.Json

class HttpJuniferAssetsServiceTest : BehaviorSpec({

    val sut = HttpJuniferAssetsService(
        MockJuniferClient.juniferConfig,
        getMockHttpClient()
    )

    given("valid postcode") {
        val request = gspGroupIdsByPostcodeRequest {
            postcode = POSTCODE
            formatAddress = false
        }

        `when`("call getGspGroupIdsByPostcode") {
            val actualResponse = sut.getGspGroupIdsByPostcode(request)

            then("should be able to retrieve gsp") {
                actualResponse shouldBe gspByPostcodeResponse
            }
        }
    }

    given("meter reading with technical details") {
        `when`("call submitMeterReadingsWithTechnicalDetails") {
            then("POST Request to junifer ok") {
                sut.submitMeterReadingsWithTechnicalDetails(ONE_LONG.toString(), readingWithDetailsProto)
            }
        }

        `when`("call submitMeterReadingsWithTechnicalDetails returns 400 Bad Request with custom payload") {
            then("POST Request to junifer throws exception") {
                shouldThrow<JuniferSubmitMeterReadingError> {
                    sut.submitMeterReadingsWithTechnicalDetails(
                        2L.toString(),
                        readingWithDetailsProto
                    )
                }
            }
        }

        `when`("call submitMeterReadingsWithTechnicalDetails with validatateOnly") {
            then("POST request to junifer ok") {
                sut.submitMeterReadingsWithTechnicalDetails(ONE_LONG.toString(), readingWithDetailsValidateOnlyProto)
            }
        }

        `when`("call submitMeterReadingsWithTechnicalDetails returns 400 Bad Request with default payload") {
            then("POST Request to junifer throws exception") {
                shouldThrow<JuniferException> {
                    sut.submitMeterReadingsWithTechnicalDetails(
                        3L.toString(),
                        readingWithDetailsProto
                    )
                }
            }
        }
    }

    given("meter reading without technical details") {
        `when`("call submitMeterReadingsWithTechnicalDetails") {
            then("POST Request to junifer ok") {
                sut.submitMeterReadingsWithoutTechnicalDetails(ONE_LONG.toString(), readingWithoutDetailsProto)
            }
        }
    }

    given("a request to get meter readings") {
        and("request returns readings") {

            `when`("getMeterReadings is called") {

                val response = sut.getMeterReadings(1, LocalDate.now(), LocalDate.now(), null)

                then("a GET request to junifer should be made and return ok") {
                    response shouldBe meterReadingsReponse
                }
            }
        }

        and("request returns no readings") {

            `when`("getMeterReadings is called") {

                val response = sut.getMeterReadings(2, LocalDate.now(), LocalDate.now(), null)

                then("a GET request to junifer should be made and return ok") {
                    response shouldBe meterReadingsEmptyReponse
                }
            }
        }
    }

    given("get meter point structure request") {
        `when`("get meter point structure") {
            then("GET request OK") {
                sut.getMeterPointStructure(JUNIFER_METER_POINT.toString(), meterPointStructureRequest)
            }
        }
    }

    given("valid search mpan request") {
        and("the request contains postcode") {
            val request = searchMpanRequest
            `when`("call searchMpans") {
                val actualResponse = sut.searchMpan(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mpanDto
                }
            }
            and("the request contains limit") {
                val newRequest = request.copy { limit = 1L }
                `when`("call searchMpans") {
                    val actualResponse = sut.searchMpan(newRequest)

                    then("should be able to retrieve gsp") {
                        actualResponse shouldBe mpanDto
                    }
                }
            }
            and("the request contains formatAddressFl") {
                val newRequest = request.copy { formatAddressFl = true }
                `when`("call searchMpans") {
                    val actualResponse = sut.searchMpan(newRequest)

                    then("should be able to retrieve gsp") {
                        actualResponse shouldBe mpanDto
                    }
                }
            }
        }
        and("the request contains meter serial number") {
            val request = searchMpanRequest {
                this.meterSerialNumber = TEST_STRING
            }
            `when`("call searchMpans") {
                val actualResponse = sut.searchMpan(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mpanDto
                }
            }
        }
        and("the request contains mpanCore") {
            val request = searchMpanRequest {
                this.mpanCore = TEST_STRING
            }
            `when`("call searchMpans") {
                val actualResponse = sut.searchMpan(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mpanDto
                }
            }
        }
    }

    given("valid search mprn request") {
        and("the request has postcode") {
            val request = searchMprnRequest
            `when`("call searchMpans") {
                val actualResponse = sut.searchMprn(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mprnDto
                }
            }
            and("the request contains limit") {
                val newRequest = request.copy { limit = 1L }
                `when`("call searchMpans") {
                    val actualResponse = sut.searchMprn(newRequest)

                    then("should be able to retrieve gsp") {
                        actualResponse shouldBe mprnDto
                    }
                }
            }
            and("the request contains formatAddressFl") {
                val newRequest = request.copy { formatAddressFl = true }
                `when`("call searchMpans") {
                    val actualResponse = sut.searchMprn(newRequest)

                    then("should be able to retrieve gsp") {
                        actualResponse shouldBe mprnDto
                    }
                }
            }
        }
        and("the request contains meter serial number") {
            val request = searchMprnRequest {
                this.meterSerialNumber = TEST_STRING
            }
            `when`("call searchMpans") {
                val actualResponse = sut.searchMprn(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mprnDto
                }
            }
        }
        and("the request contains mpanCore") {
            val request = searchMprnRequest {
                this.mprn = TEST_STRING
            }
            `when`("call searchMpans") {
                val actualResponse = sut.searchMprn(request)

                then("should be able to retrieve gsp") {
                    actualResponse shouldBe mprnDto
                }
            }
        }
    }

    given("get estimated usage request") {
        and("get estimated usage is found") {
            `when`("::getEstimatedUsage") {
                val actualResponse = sut.getEstimatedUsage(JUNIFER_METER_POINT_ID, estimatedUsageRequestData)
                then("GET request OK") {
                    actualResponse shouldBe juniferEstimatedUsageResponse
                }
            }
        }

        and("get estimated usage is not found") {
            `when`("::getEstimatedUsage") {
                val actualResponse = sut.getEstimatedUsage(1, estimatedUsageRequestData)
                then("GET request OK") {
                    actualResponse shouldBe JuniferEstimatedUsageResponse()
                }
            }
        }
    }
})

private fun getMockHttpClient(): HttpClient {
    return MockJuniferClient.createMockHttpClient(MockEngine { request ->
        if (request.method == HttpMethod.Get && request.url.fullPath.contains("estimatedUsage")) {
            if (request.url.fullPath.contains("${JUNIFER_METER_POINT_ID}/estimatedUsage")) {
                respond(
                    ByteReadChannel(juniferEstimatedUsageResponseJson.toByteArray(Charsets.UTF_8)),
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            } else {
                respond(
                    ByteReadChannel(juniferEstimatedUsageEmptyResponseJson.toByteArray(Charsets.UTF_8)),
                    HttpStatusCode.OK,
                    headersOf(HttpHeaders.ContentType, "application/json")
                )
            }

        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("mpans?")) {
            respond(
                ByteReadChannel(gspByPostcodeResponseJson.toByteArray(Charsets.UTF_8)),
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("mprns?")) {
            respond(
                ByteReadChannel(mprnsResponse.toByteArray(Charsets.UTF_8)),
                HttpStatusCode.OK,
                headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.method == HttpMethod.Get && request.url.fullPath.contains("meterPoints/${JUNIFER_METER_POINT}/meterStructure")) {
            respond(
                content = MeterPointStructureData.meterPointStructureJson,
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        } else if (request.url.fullPath.contains("meterPoints/1")) {
            when (request.method) {
                HttpMethod.Post -> {
                    respond(
                        "",
                        HttpStatusCode.NoContent,
                        headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }

                HttpMethod.Get -> {
                    respond(
                        ByteReadChannel(getMeterReadingsResponseJson.toByteArray(Charsets.UTF_8)),
                        HttpStatusCode.OK,
                        headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }

                else -> {
                    respondError(HttpStatusCode.InternalServerError)
                }
            }
        }
        //mock custom error response submit meter reading with tech details
        else if (request.url.fullPath.contains("meterPoints/2")) {
            when (request.method) {
                HttpMethod.Post -> {
                    respond(
                        content = ByteReadChannel(
                            Json.encodeToString(
                                SubmitMeterReadingsWithTechDetailsResponse.serializer(),
                                SubmitMeterReadingsWithTechDetailsResponse(
                                    listOf(
                                        ValidationError(
                                            "",
                                            "",
                                            JuniferError("", "", "")
                                        )
                                    )
                                )
                            )
                        ),
                        status = HttpStatusCode.BadRequest,
                        headers = headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }

                HttpMethod.Get -> {
                    respond(
                        ByteReadChannel(getMeterReadingsEmptyResponseJson.toByteArray(Charsets.UTF_8)),
                        HttpStatusCode.OK,
                        headersOf(HttpHeaders.ContentType, "application/json")
                    )
                }

                else -> {
                    respondError(HttpStatusCode.InternalServerError)
                }
            }
        } else {
            respond(
                content = ByteReadChannel(
                    Json.encodeToString(
                        JuniferError.serializer(),
                        JuniferError("", "", "")
                    )
                ),
                status = HttpStatusCode.BadRequest,
                headers = headersOf(HttpHeaders.ContentType, "application/json")
            )
        }
    })
}