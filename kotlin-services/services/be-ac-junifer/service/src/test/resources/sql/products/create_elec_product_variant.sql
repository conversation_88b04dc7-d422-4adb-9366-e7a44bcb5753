-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__ProductType";
TRUNCATE TABLE "junifer__PricePlan";
TRUNCATE TABLE "junifer__ProductDfn";
TRUNCATE TABLE "junifer__ProductBundleDfn";
TRUNCATE TABLE "junifer__ProductItemDfn";
TRUNCATE TABLE "junifer__PricePlanRow";
TRUNCATE TABLE "junifer__PricePlanRowItem";
TRUNCATE TABLE "junifer__SalesTax";
TRUNCATE TABLE "junifer__SalesTaxRate";
TRUNCATE TABLE "junifer__PricePlanRowDimension";
TRUNCATE TABLE "junifer__PricePlanDimension";
TRUNCATE TABLE "junifer__PricePlanDimFlag";
TRUNCATE TABLE "junifer__PricePlanDimensionClass";
TRUNCATE TABLE "junifer__PricePlanDimUkGspGroup";
TRUNCATE TABLE "junifer__UkGspGroup";
TRUNCATE TABLE "junifer__MpanProductItemDfn";
TRUNCATE TABLE "junifer__MpanProductItemDfnPeriod";
TRUNCATE TABLE "junifer__MpanProductItemDfnRate";
TRUNCATE TABLE "junifer__RateName";
TRUNCATE TABLE "junifer__RecurringItemDfn";
TRUNCATE TABLE "junifer__FrequencyInst";
TRUNCATE TABLE "junifer__RecurringItemDfnAmount";
TRUNCATE TABLE "junifer__Component";
TRUNCATE TABLE "junifer__EarlyTermChargeDfn";
TRUNCATE TABLE "junifer__EarlyTermChargeDfnAmount";

INSERT INTO "junifer__ProductType" (
    id,
    "name",
    "code",
    "productServiceTypeFk",
    "billingEntityFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'Electricity Supply (Res)', N'PT-RES-ELE-SUPPLY', 2, 1, N'RES-ELE-SUPPLY', N'N', 1, 1);

INSERT INTO "junifer__PricePlan" (
    id,
    "productDfnFk",
    "productTypeFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (49, 55, 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__ProductDfn" (
    id,
    "productBundleDfnFk",
    "productClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "optionalLinkageFl",
    "multipleLinkageFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (55, 54, 2, 0, N'Electricity', N'Y', 1, 1, 3, N'N', N'Y', NULL, N'N', 1, 1);

INSERT INTO "junifer__ProductBundleDfn" (
    id,
    "name",
    "billDisplay",
    "status",
    "fromDttm",
    "toDttm",
    "endDttm",
    "followOnProductBundleDfnFk",
    "productBundleDfnGroupFk",
    "productBundleDfnTypeFk",
    "productFlexClassFk",
    "billingEntityFk",
    "currencyFk",
    "billingCycleFk",
    "countryFk",
    "useDfnNameFl",
    "deemedDefault",
    "availablePublicFl",
    "offSupplyBillFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (54, N'Electricity 1-Rate | SOAR | 151116', N'So Armadillo - Electricity - Single Rate', N'Active', CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('2016-07-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 69, 102, 2, NULL, 1, 1, NULL, 1, N'Y', N'Default', N'N', N'N', N'E1R-SOAR-151116', N'N', 7, 1);

INSERT INTO "junifer__ProductItemDfn" (
    id,
    "productDfnFk",
    "productItemClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "productItemTypeFk",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "overrideCode",
    "allowInactiveFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (11874, 55, 17, 0, N'Electricity Supply', N'Y', 2, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11875, 55, 3, 1, N'Standing Charge', N'Y', 2, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11876, 55, 4, 2, N'Online Discount', N'Y', 4, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11877, 55, 4, 3, N'Dual Fuel Discount', N'Y', 4, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11878, 55, 5, 4, N'Early Termination Charge', N'Y', 3, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRow" (
    id,
    "pricePlanFk",
    "productSubTypeFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2380, 49, 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRowItem" (
    id,
    "pricePlanRowFk",
    "productItemDfnFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (11864, 2380, 11874, NULL, N'N', 1, 1),
    (11865, 2380, 11875, NULL, N'N', 1, 1),
    (11866, 2380, 11876, NULL, N'N', 1, 1),
    (11867, 2380, 11877, NULL, N'N', 1, 1),
    (11868, 2380, 11878, NULL, N'N', 1, 1);


INSERT INTO "junifer__SalesTax" (
    id,
    "countryFk",
    "name",
    "componentFk",
    "isAppliedDynamicallyFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (3, 1, N'Reduced VAT', NULL, N'N', N'UK-RED', N'N', 1, 1);

INSERT INTO "junifer__SalesTaxRate" (
    id,
    "salesTaxFk",
    "fromDttm",
    "toDttm",
    "rate",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, 3, CAST('2000-01-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.05000000, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRowDimension" (
    id,
    "pricePlanRowFk",
    "pricePlanDimensionFk",
    "pricePlanDimValueId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2400, 2379, 96, 483, NULL, N'N', 1, 1),
    (2401, 2379, 97, 97, NULL, N'N', 1, 1),
    (2402, 2380, 96, 483, NULL, N'N', 1, 1),
    (2403, 2380, 97, 96, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimension" (
    id,
    "pricePlanFk",
    "pricePlanDimensionClassFk",
    "orderNo",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (96, 49, 9, 1, N'GSP Group', NULL, N'N', 1, 1),
    (97, 49, 2, 2, N'Direct Debit', NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimFlag" (
    id,
    "pricePlanDimensionFk",
    "valueFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (96, 97, N'Y', NULL, N'N', 1, 1),
    (97, 97, N'N', NULL, N'N', 1, 1);


INSERT INTO "junifer__PricePlanDimensionClass" (
    id,
    "internalKey",
    "languageKey",
    "iconTblFk",
    "componentFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, N'Direct Debit', N'PricePlanDimensionClass.DirectDebit', 1308, 1259, NULL, N'N', 1, 1),
    (9, N'GSP Group', N'PricePlanDimensionClass.GspGroup', 1634, 1934, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimUkGspGroup" (
    id,
    "pricePlanDimensionFk",
    "ukGspGroupFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (483, 96, 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__UkGspGroup" (
    id,
    "code",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'_A', N'Eastern', NULL, N'N', 1, 1);

INSERT INTO "junifer__MpanProductItemDfn" (
    id,
    "productItemDfnFk",
    "meterMeasurementTypeFk",
    "metricDp",
    "rateMetricTypeFk",
    "dp",
    "ukTprMappingSetFk",
    "partialRateNameMatchFl",
    "halfHourRateDfnFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (471, 11874, 1, 1, 9, 4, 1, N'Y', NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__MpanProductItemDfnPeriod" (
    id,
    "mpanProductItemDfnFk",
    "fromDttm",
    "toDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (470, 471, CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), NULL, N'N', 1, 1);

INSERT INTO "junifer__MpanProductItemDfnRate" (
    id,
    "mpanProductItemDfnPeriodFk",
    "rateNameFk",
    "rate",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2345, 470, 14, 9.71900000, NULL, N'N', 1, 1);

INSERT INTO "junifer__RateName" (
    id,
    "rateNameGroupFk",
    "name",
    "code",
    "colour",
    "orderNo",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (14, 4, N'Standard', N'S', N'255,128,255', 3, N'STANDARD', N'N', 1, 1);

INSERT INTO "junifer__RecurringItemDfn" (
    id,
    "productItemDfnFk",
    "creditFl",
    "pricingFrequencyInstFk",
    "proRataPricingFl",
    "applyCode",
    "alignCode",
    "alignFrequencyInstFk",
    "proRataFl",
    "minUnits",
    "maxUnits",
    "dp",
    "proRataComponentFk",
    "introPeriodFl",
    "introPdFrequencyInstFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2437, 11875, N'N', 504372, N'Y', N'InArrears', N'BillingPeriod', NULL, N'Y', 1, 1, 6, NULL, N'N', NULL, NULL, N'N', 1, 1),
    (2438, 11876, N'Y', 504373, N'Y', N'InArrears', N'StartDate', 504374, N'Y', 1, 1, 2, NULL, N'N', NULL, NULL, N'N', 1, 1),
    (2439, 11877, N'Y', 504380, N'Y', N'InArrears', N'StartDate', 504381, N'Y', 1, 1, 2, NULL, N'N', NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__FrequencyInst" (
    id,
    "frequencyComponentFk",
    "frequencyMultiple",
    "manualFreqFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (504380, 203, 1, NULL, NULL, N'N', 1, 1),
    (504373, 203, 1, NULL, NULL, N'N', 1, 1),
    (504372, 200, 1, NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__RecurringItemDfnAmount" (
    id,
    "recurringItemDfnFk",
    "fromDttm",
    "toDttm",
    "amount",
    "introAmount",
    "minAmount",
    "maxAmount",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2431, 2437, CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.20000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1),
    (2432, 2438, CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.00000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1),
    (2433, 2439, CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.00000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__Component" (
    id,
    "componentGroupFk",
    "propertyDfnGroupFk",
    "internalKey",
    "languageKey",
    "clientType",
    "serverBean",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (200, 23, NULL, N'Daily', N'Component.DailyFrequency', N'Junifer.Thor.Components.Frequency.DailyFrequency,Junifer.Thor.Components', N'frequencyDaily', NULL, N'N', 1, 1),
    (203, 23, NULL, N'Yearly', N'Component.YearlyFrequency', N'Junifer.Thor.Components.Frequency.YearlyFrequency,Junifer.Thor.Components', N'frequencyYearly', NULL, N'N', 1, 1);

INSERT INTO "junifer__EarlyTermChargeDfn" (
    id,
    "productItemDfnFk",
    "dp",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (7520, 11878, 2, null, N'N', 1, 1);

INSERT INTO "junifer__EarlyTermChargeDfnAmount" (
    id,
    "earlyTermChargeDfnFk",
    "fromDttm",
    "toDttm",
    "amount",
    "minAmount",
    "maxAmount",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (8330, 7520, CAST('2015-10-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 4.76000000, null, null, null, N'N', 1, 1);
