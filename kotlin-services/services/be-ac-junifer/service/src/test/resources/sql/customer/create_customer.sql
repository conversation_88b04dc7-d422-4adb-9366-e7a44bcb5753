-- WARNING: Running this script will clear out a number of tables
TRUNCATE TABLE "junifer__Customer";
TRUNCATE TABLE "junifer__CustomerClass";
TRUNCATE TABLE "junifer__CustomerType";
TRUNCATE TABLE "junifer__CustomerContact";
TRUNCATE TABLE "junifer__Contact";
TRUNCATE TABLE "junifer__ContactVersion";
TRUNCATE TABLE "junifer__Address";
TRUNCATE TABLE "junifer__ContactType";
TRUNCATE TABLE "junifer__Title";
TRUNCATE TABLE "junifer__Account";
TRUNCATE TABLE "junifer__AccountType";
TRUNCATE TABLE "junifer__Currency";
TRUNCATE TABLE "junifer__AccountClass";
TRUNCATE TABLE "junifer__BillingCycle";
TRUNCATE TABLE "junifer__AccountContact";
TRUNCATE TABLE "junifer__ProductBundle";
TRUNCATE TABLE "junifer__UkVulnCustomer";
TRUNCATE TABLE "junifer__UkVulnCustPsr";
TRUNCATE TABLE "junifer__CustomerProperty";
TRUNCATE TABLE "junifer__Ticket";
TRUNCATE TABLE "junifer__TicketHistory";
TRUNCATE TABLE "junifer__Note";

INSERT INTO "junifer__Customer" (id,
                                 "billingEntityFk",
                                 "customerClassFk",
                                 "customerTypeFk",
                                 "number",
                                 "name",
                                 "companyName",
                                 "companyNo",
                                 "companyRegAddressFk",
                                 "titleFk",
                                 "forename",
                                 "surname",
                                 "state",
                                 "managerUserTblFk",
                                 "marketingOptOutFl",
                                 "creditScore",
                                 "deceasedFl",
                                 "taxExemptReasonFk",
                                 "txnAllocatorComponentFk",
                                 "dataErasedFl",
                                 "reference",
                                 "deleteFl",
                                 "versionNo",
                                 "partitionId")
VALUES (740972, 1, 2, 2, N'01540943', N'*Customer740972*', null, null, null, null, N'*REDACTED*', N'*REDACTED*',
        N'Active', null, N'Y', null, N'N', null, null, N'N', null, N'N', 1, 1);

INSERT INTO "junifer__CustomerClass" (id,
                                      "code",
                                      "internalKey",
                                      "languageKey",
                                      "dunningBasis",
                                      "iconTblFk",
                                      "showPricesInclSalesTaxFl",
                                      "componentFk",
                                      "contactTypeFk",
                                      "reference",
                                      "deleteFl",
                                      "versionNo",
                                      "partitionId")
VALUES (2, N'CR', N'Consumer - Residential', N'CustomerClass.ConsumerResidential', N'Account', 1067, N'Y', 1307, 1,
        N'RESI', N'N', 1, 1);

INSERT INTO "junifer__CustomerType" (id,
                                     "customerClassFk",
                                     "category",
                                     "internalKey",
                                     "languageKey",
                                     "reference",
                                     "deleteFl",
                                     "versionNo",
                                     "partitionId")
VALUES (2, 2, N'Residential', N'Residential', N'CustomerType.Residential', N'RESI', N'N', 2, 1);

INSERT INTO "junifer__CustomerContact" (id,
                                        "customerFk",
                                        "fromDttm",
                                        "toDttm",
                                        "primaryFl",
                                        "contactFk",
                                        "cancelFl",
                                        "reference",
                                        "deleteFl",
                                        "versionNo",
                                        "partitionId")
VALUES (891483, 740972, CAST('2021-12-16 00:00:00.0000000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), N'Y', 1097587, N'N', null, N'N', 1, 1);

INSERT INTO "junifer__Contact" (id,
                                "contactTypeFk",
                                "description",
                                "reference",
                                "deleteFl",
                                "versionNo",
                                "partitionId")
VALUES (1097587, 1, null, null, N'N', 1, 1),
       (982487, 1, NULL, NULL, N'N', 1, 1),
       (1024275, 1, NULL, NULL, N'N', 1, 1);


INSERT INTO "junifer__ContactVersion" (id,
                                       "contactFk",
                                       "fromDttm",
                                       "toDttm",
                                       "titleFk",
                                       "initials",
                                       "forename",
                                       "surname",
                                       "jobTitle",
                                       "number1",
                                       "number2",
                                       "number3",
                                       "email",
                                       "web",
                                       "socialMedia1",
                                       "socialMedia2",
                                       "socialMedia3",
                                       "securityNo",
                                       "dateOfBirthDt",
                                       "careOf",
                                       "addressFk",
                                       "reference",
                                       "deleteFl",
                                       "versionNo",
                                       "partitionId")
VALUES (3735029, 1097587, CAST('2021-12-16 00:00:00.0000000' AS TIMESTAMP),
        CAST('2022-01-21 10:33:23.0000000' AS TIMESTAMP), 1, null, N'*REDACTED*', N'*REDACTED*', null, null, null, null,
        N'<EMAIL>', null, null, null, null, null, CAST('2000-01-01' AS DATE), null, 1719032, null, N'N', 2,
        1),
       (3931936, 1097587, CAST('2022-01-21 10:33:23.0000000' AS TIMESTAMP),
        CAST('2022-01-27 08:37:32.0000000' AS TIMESTAMP), 1, null, N'*REDACTED*', N'*REDACTED*', null, null, null, null,
        N'<EMAIL>', null, null, null, null, null, CAST('2000-01-01' AS DATE), null, 1719032, null, N'N', 2,
        1),
       (4001832, 1097587, CAST('2022-01-27 08:37:32.0000000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, null, N'*REDACTED*', N'*REDACTED*', null, N'0000000000',
        null, null, N'<EMAIL>', null, null, null, null, null, CAST('2000-01-01' AS DATE), null, 1719032,
        null, N'N', 2, 1),
       (3415643, 982487, CAST('2021-08-09 14:09:07.0000000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, NULL, N'*REDACTED*', N'*REDACTED*', NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, CAST('2000-01-01' AS DATE), NULL, 1538607, NULL, N'N', 2, 1),
       (3563767, 1024275, CAST('2021-10-14 10:24:56.0000000' AS TIMESTAMP),
        CAST('2021-10-15 12:21:51.0000000' AS TIMESTAMP), 1, NULL, N'*REDACTED*', N'*REDACTED*', NULL, N'0000000000',
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST('2000-01-01' AS DATE), N'*REDACTED*', 1592555, NULL, N'N',
        3, 1),
       (3567045, 1024275, CAST('2021-10-15 12:21:51.0000000' AS TIMESTAMP),
        CAST('2021-10-15 12:22:45.0000000' AS TIMESTAMP), 1, NULL, N'*REDACTED*', N'*REDACTED*', NULL, N'0000000000',
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST('2000-01-01' AS DATE), N'*REDACTED*', 1592555, NULL, N'N',
        2, 1),
       (3567046, 1024275, CAST('2021-10-15 12:22:45.0000000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, NULL, N'*REDACTED*', N'*REDACTED*', NULL, N'0000000000',
        N'0000000000', NULL, N'<EMAIL>', NULL, NULL, NULL, NULL, NULL, CAST('2000-01-01' AS DATE),
        N'*REDACTED*', 1592555, NULL, N'N', 1, 1);


INSERT INTO "junifer__Address" (id,
                                "parentAddressFk",
                                "addressTypeFk",
                                "address1",
                                "address2",
                                "address3",
                                "address4",
                                "address5",
                                "address6",
                                "address7",
                                "address8",
                                "address9",
                                "address10",
                                "address11",
                                "address12",
                                "address13",
                                "address14",
                                "address15",
                                "address16",
                                "address17",
                                "address18",
                                "address19",
                                "address20",
                                "address21",
                                "address22",
                                "address23",
                                "address24",
                                "address25",
                                "address26",
                                "address27",
                                "address28",
                                "address29",
                                "address30",
                                "postCode",
                                "countryFk",
                                "reference",
                                "deleteFl",
                                "versionNo",
                                "partitionId")
VALUES (1719032, null, 1, N'*Address1719032*', N'Address2_1719032', N'Address3_1719032', null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, null, null, N'NE6 2AW', 1, null, N'N', 1, 1),
       (1538607, NULL, 1, N'*Address1538607*', NULL, NULL, N'Address4_1538607', N'Address5_1538607', NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, N'LN1 3NF', 1, NULL, N'N', 1, 1),
       (1592555, NULL, 1, N'*Address1592555*', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        N'LN1 3NF', 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__ContactType" (id,
                                    "internalKey",
                                    "languageKey",
                                    "iconTblFk",
                                    "addressMandatoryFl",
                                    "number1Label",
                                    "number1Regex",
                                    "number2Label",
                                    "number2Regex",
                                    "number3Label",
                                    "number3Regex",
                                    "smsNumber",
                                    "socialMedia1Label",
                                    "socialMedia1Regex",
                                    "socialMedia2Label",
                                    "socialMedia2Regex",
                                    "socialMedia3Label",
                                    "socialMedia3Regex",
                                    "emergencyContactFl",
                                    "reference",
                                    "deleteFl",
                                    "versionNo",
                                    "partitionId")
VALUES (1, N'Residential', N'ContactType.Customer', 2033, N'Y', N'Home Phone',
        N'(0|\+44)(\d{9,11})(\s?((ext|x)\.?\s?)(\d{1,6}?))?', N'Mobile Phone', N'(0|\+44)(\d{9,11})', N'Work Phone',
        N'(0|\+44)(\d{9,11})(\s?((ext|x)\.?\s?)(\d{1,6}?))?', N'Number2', null, null, null, null, null, null, N'N',
        null, N'N', 1, 1);

INSERT INTO "junifer__Title" (id,
                              "internalKey",
                              "languageKey",
                              "reference",
                              "deleteFl",
                              "versionNo",
                              "partitionId")
VALUES (1, N'Dr', N'Title.Dr', null, N'N', 1, 1);

INSERT INTO "junifer__Account" (id,
                                "billingEntityFk",
                                "parentAccountFk",
                                "customerFk",
                                "accountTypeFk",
                                "number",
                                "name",
                                "createdDttm",
                                "createdUserTblFk",
                                "fromDttm",
                                "toDttm",
                                "terminatedUserTblFk",
                                "closedDttm",
                                "currencyFk",
                                "languageTblFk",
                                "dunningPackageFk",
                                "billPackageFk",
                                "billingCycleFk",
                                "billingAlignDttm",
                                "initialBillStatus",
                                "paymentTermFk",
                                "payPointLocation",
                                "salesTaxExemptFl",
                                "lastAccountTransactionFk",
                                "cancelFl",
                                "cancelledDttm",
                                "cancelledUserTblFk",
                                "terminatedDttm",
                                "parentOverridesChildFl",
                                "parentReconciliationBillingFl",
                                "overrideAllocateCreditOnRebill",
                                "autoRebillBehaviour",
                                "txnAllocatorComponentFk",
                                "reference",
                                "paymentDataErasedFl",
                                "deleteFl",
                                "versionNo",
                                "partitionId")
VALUES (691791, 1, null, 740972, 3, N'********', N'*Account691791*', CAST('2021-08-09 14:09:07.0000000' AS TIMESTAMP),
        302, CAST('2021-08-09 00:00:00.0000000' AS TIMESTAMP), CAST('2021-10-11 00:00:00.0000000' AS TIMESTAMP), 302,
        CAST('2021-11-25 14:40:07.0000000' AS TIMESTAMP), 1, 1, null, 1, 1,
        CAST('2021-08-09 00:00:00.0000000' AS TIMESTAMP), N'Accepted', 1, N'This', N'N', ********, N'N', null, null,
        CAST('2021-10-14 10:24:56.0000000' AS TIMESTAMP), N'N', N'N', null, null, null, null, N'N', N'N', 7, 1);

INSERT INTO "junifer__AccountType" (id,
                                    "internalKey",
                                    "languageKey",
                                    "code",
                                    "customerClassFk",
                                    "accountClassFk",
                                    "dunningBasis",
                                    "billPointFl",
                                    "payPointFl",
                                    "broughtForwardBillingFl",
                                    "landlordFl",
                                    "propertyManagerFl",
                                    "promptBeforeTerminationFl",
                                    "requiresFinalBillToCloseFl",
                                    "reference",
                                    "deleteFl",
                                    "versionNo",
                                    "partitionId")
VALUES (3, N'Invoice', N'AccountType.ResidentialInvoice', N'CR-INVOICE', 2, 1, N'Account', N'Y', N'Y', N'Y', N'N', N'N',
        N'N', N'N', null, N'N', 1, 1);

INSERT INTO "junifer__Currency" (id,
                                 "internalKey",
                                 "languageKey",
                                 "isoCode",
                                 "reference",
                                 "deleteFl",
                                 "versionNo",
                                 "partitionId")
VALUES (1, N'British Pound', N'Currency.BritishPound', N'GBP', null, N'N', 1, 1);

INSERT INTO "junifer__AccountClass" (id,
                                     "internalKey",
                                     "languageKey",
                                     "cashflowCode",
                                     "reference",
                                     "deleteFl",
                                     "versionNo",
                                     "partitionId")
VALUES (1, N'Invoice', N'AccountClass.Invoice', N'Income', null, N'N', 1, 1);

INSERT INTO "junifer__BillingCycle" (id,
                                     "billingEntityFk",
                                     "name",
                                     "frequencyInstFk",
                                     "alignmentType",
                                     "accountAlignDttmComponentFk",
                                     "pivotDttm",
                                     "openPriorDayDfnFk",
                                     "closePostDayDfnFk",
                                     "minPeriodFrequencyInstFk",
                                     "issueDateCode",
                                     "issueLagDayDfnFk",
                                     "reference",
                                     "deleteFl",
                                     "versionNo",
                                     "partitionId")
VALUES (1, 1, N'Monthly - Anniversary', 1, N'SpecifiedOnAccount', 5403, null, 35, 36, 1348109, N'CreationDate', 37,
        N'RES-MON-ANN', N'N', 12, 1);

INSERT INTO "junifer__AccountContact" (id,
                                       "accountFk",
                                       "fromDttm",
                                       "toDttm",
                                       "primaryFl",
                                       "contactFk",
                                       "billDelivery",
                                       "receivePostFl",
                                       "receiveEmailFl",
                                       "receiveSmsFl",
                                       "cancelFl",
                                       "reference",
                                       "deleteFl",
                                       "versionNo",
                                       "partitionId")
VALUES (818336, 691791, CAST('2021-08-09 00:00:00.0000000' AS TIMESTAMP),
        CAST('2021-10-11 00:00:00.0000000' AS TIMESTAMP), N'Y', 982487, N'Mail', N'Y', N'N', N'N', N'N', NULL, N'N', 2,
        1),
       (854298, 691791, CAST('2021-10-11 00:00:00.0000000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), N'Y', 1024275, N'Email', N'N', N'Y', N'N', N'N', NULL, N'N',
        2, 1);

INSERT INTO "junifer__ProductBundle" (id,
                                      "productBundleDfnFk",
                                      "agreementTypeFk",
                                      "accountFk",
                                      "purchasingGroupFk",
                                      "brokerAgentFk",
                                      "name",
                                      "number",
                                      "createdDttm",
                                      "createdUserTblFk",
                                      "orderDttm",
                                      "fromDttm",
                                      "toDttm",
                                      "contractedToDttm",
                                      "followOnFl",
                                      "followOnDttm",
                                      "followOnProductBundleDfnFk",
                                      "terminationReceivedDt",
                                      "productTerminationReasonFk",
                                      "currencyFk",
                                      "cancelFl",
                                      "cancelledDttm",
                                      "cancelledUserTblFk",
                                      "billableFl",
                                      "reference",
                                      "deleteFl",
                                      "versionNo",
                                      "partitionId")
VALUES (456, 123, 101, 691791, null, 1, null, N'00001004', CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), 1,
        CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), CAST('2015-12-16 00:00:00.0000000' AS TIMESTAMP),
        CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), N'N', null,
        null, null, null, 1, N'N', null, null, N'Y', N'*REDACTED*', N'N', 1, 1);

INSERT INTO "junifer__UkVulnCustomer" (id,
                                       "customerFk",
                                       "reference",
                                       "deleteFl",
                                       "versionNo",
                                       "partitionId")
VALUES (519762, 740972, null, 'N', 1, 1);


INSERT INTO "junifer__UkVulnCustPsr" (id,
                                      "ukVulnCustomerFk",
                                      "ukVulnCustPsrDfnFk",
                                      "propertyTblFk",
                                      "fromDt",
                                      "toDt",
                                      "note",
                                      "reference",
                                      "deleteFl",
                                      "versionNo",
                                      "partitionId")
VALUES (505723, 519762, 3, 69186, '2023-07-31', '9999-01-01', null, null, 'N', 1, 1),
       (505822, 519762, 9, 1, '2023-08-02', '9999-01-01', null, null, 'N', 1, 1),
       (505722, 519762, 1, 69186, '2023-07-31', '2023-08-16', null, null, 'N', 2, 1),
       (506422, 519762, 108, 69186, '2023-08-16', '2026-10-09', null, null, 'N', 1, 1);

INSERT INTO "junifer__UkVulnCustTypeDfn" (id,
                                          name,
                                          "reference",
                                          "deleteFl",
                                          "versionNo",
                                          "partitionId")
VALUES (50572, 'NAME1', null, 'N', 1, 1),
       (50582, 'NAME2', null, 'N', 1, 1),
       (50571, 'NAME3', null, 'N', 2, 1),
       (50642, 'NAME4', null, 'N', 1, 1);

INSERT INTO "junifer__UkVulnCustomerTypeInst" (id,
                                               "ukVulnCustomerFk",
                                               "ukVulnCustTypeDfnFk",
                                               "propertyTblFk",
                                               "fromDttm",
                                               "toDttm",
                                               "reference",
                                               "deleteFl",
                                               "versionNo",
                                               "partitionId")
VALUES (5057231, 519762, 50572, 69186, '2023-07-31', '9999-01-01', null, 'N', 1, 1),
       (5058221, 519762, 50582, 1, '2023-08-02', '9999-01-01', null, 'N', 1, 1),
       (5057221, 519762, 50571, 69186, '2023-07-31', '2023-08-16', null, 'N', 2, 1),
       (5064221, 519762, 50642, 69186, '2023-08-16', '2026-10-09', null, 'N', 1, 1);

INSERT INTO "junifer__CustomerProperty" (id,
                                         "customerFk",
                                         "propertyTblFk",
                                         "siteDescription",
                                         "specialAccess",
                                         "reference",
                                         "deleteFl",
                                         "versionNo",
                                         "partitionId")
VALUES (343, 740972, 805517, null, null, null, 'N', 1, 1);

INSERT INTO "junifer__Ticket" (id,
                               "parentTicketFk",
                               "ticketDefinitionFk",
                               "ticketPriorityFk",
                               "ticketStepFk",
                               "ticketDeadlineInstFk",
                               "status",
                               "keyIdentifier",
                               "summary",
                               "description",
                               "raisedDttm",
                               "createdDttm",
                               "dueDttm",
                               "closeDttm",
                               "cancelDttm",
                               "ticketCancelReasonFk",
                               "createdUserTblFk",
                               "assignedTeamFk",
                               "assignedUserTblFk",
                               "lastModifiedUserTblFk",
                               "lastModifiedDttm",
                               "stepStartDttm",
                               "stepScheduledDttm",
                               "stepDueDttm",
                               "mapTblFk",
                               "taskFk",
                               "serverExceptionFk",
                               "quietFl",
                               "quietUntilDttm",
                               "escalatedFl",
                               "customerContactFk",
                               "reference",
                               "deleteFl",
                               "versionNo",
                               "partitionId")
VALUES (42920000, null, 300, 400, 500, null, N'Closed', N'VAR-PRICE-CHG-1372536', N'*REDACTED*', N'*REDACTED*',
        CAST('2023-09-15 07:00:00.000000' AS TIMESTAMP), CAST('2023-09-15 07:20:00.000000' AS TIMESTAMP), null,
        CAST('2023-09-15 07:20:00.000000' AS TIMESTAMP), null, null, 1, null, null, 1,
        CAST('2023-09-15 07:20:00.000000' AS TIMESTAMP), null, null, null, null, null, null, N'N', null, N'N', 891483,
        null, N'N', 1, 1);

INSERT INTO public."junifer__TicketHistory"
(id, "ticketFk", dttm, "userTblFk", "propertyKey", "previousValue", "newValue", "previousValueNText", "newValueNText",
 reference, "deleteFl", "versionNo", "partitionId")
VALUES (20, 42920000, CAST('2023-12-22 04:52:16.444' AS TIMESTAMP), 1, 'Created', null, 'Send Renewal Letter',
        null, null, null, 'N', 1, 1);

INSERT INTO public."junifer__Note"
(id, "noteSubjectFk", "noteType", "noteStyle", summary, "content", "entityTblFk", "entityId", "createdDttm",
 "createdUserTblFk", "lastModifiedDttm", "lastModifiedUserTblFk", "pinnedFl", "pinnedDttm", reference, "deleteFl",
 "versionNo", "partitionId")
VALUES (1000, 7, 'Note', 'None', 'Meter Reading submitted via So Watt',
        'Meter Reading on So <NAME_EMAIL> at 2023-12-20 12:03:05 UTC', 155, 42920000,
        CAST('2023-12-20 12:03:05.686' AS TIMESTAMP), 302, CAST('2023-12-20 12:03:05.686' AS TIMESTAMP), 302, 'N',
        null, null, 'N', 1, 1);