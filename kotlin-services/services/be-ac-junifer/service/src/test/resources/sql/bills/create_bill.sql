-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__Bill";
TRUNCATE TABLE "junifer__BillPeriod";

INSERT INTO "junifer__Bill" (
    id,
    "accountFk",
    "accountTransactionFk",
    "billPeriodFk",
    "billedFromDttm",
    "billedToDttm",
    "billRequestFk",
    "number",
    "description",
    "versionNumber",
    "createdDttm",
    "createdUserTblFk",
    "issueDt",
    "dueDt",
    "netAmount",
    "grossAmount",
    "currencyFk",
    "salesTaxAmount",
    "status",
    "draftReason",
    "acceptedDttm",
    "acceptedByUserTblFk",
    "lessThanMinimumFl",
    "supersededFl",
    "supersededByBillFk",
    "finalFl",
    "disallowReversionFl",
    "lastShownAccountTransactionFk",
    "comment",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (********, 2906, ********, ********, CAST('2023-03-19 00:00:00.0000000' AS TIMESTAMP), CAST('2023-03-21 00:00:00.0000000' AS TIMESTAMP), ********, N'********', NULL, 1, CAST('2023-03-21 11:51:27.0000000' AS TIMESTAMP), 2258, CAST('2023-03-21' AS DATE), CAST('2023-04-03' AS DATE), -47.********, -50.********, 1, -2.********, N'Draft', N'Low Electricity Total Consumption Value. The bill has electricity total consumption of -126.0kWh which is below the lower total consumption estimate of -122.0kWh', NULL, NULL, N'N', N'N', NULL, N'N', N'N', ********, N'', NULL, N'N', 3, 1);

INSERT INTO "junifer__BillPeriod" (
    id,
    "accountFk",
    "statusCode",
    "fromDttm",
    "toDttm",
    "consolidatedBillPeriodFk",
    "washUpFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (********, 2906, N'Open', CAST('2023-03-21 00:00:00.0000000' AS TIMESTAMP), CAST('2023-04-19 00:00:00.0000000' AS TIMESTAMP), NULL, N'N', NULL, N'N', 1, 1),
    (********, 2906, N'Closed', CAST('2023-03-19 00:00:00.0000000' AS TIMESTAMP), CAST('2023-03-21 00:00:00.0000000' AS TIMESTAMP), ********, N'N', NULL, N'N', 3, 1),
    (********, 2906, N'Closed', CAST('2023-02-19 00:00:00.0000000' AS TIMESTAMP), CAST('2023-03-19 00:00:00.0000000' AS TIMESTAMP), ********, N'N', NULL, N'N', 3, 1);
