-- WARNING: Running this script will clear out a number of tables

-- Meter point ID: 123
-- Mpan ID: 456

TRUNCATE TABLE "junifer__Mpan";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__MpanSupplyStatus";

INSERT INTO "junifer__Mpan" (
    id,
    "meterPointFk",
    "mpanClass",
    "msid",
    "ukDistributionAreaFk",
    "specialNeedsInformation",
    "changeOfTenancyFl",
    "measurementType",
    "balancingMechanismUnitId",
    "balancingMechanismUnitDfnFk",
    "soleTradingUnitFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, 'SVA', null, 16, null, 'N', 'Import', null, null, 'N', 'test MPAN reference', 'N', 1, 1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1, 1, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

INSERT INTO "junifer__MpanSupplyStatus" (
    id,
    "mpanFk",
    "eventType",
    "supplyStatus",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (369035, 456, N'Created', N'NotSupplied', CAST('2018-06-07 06:30:48.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (369042, 456, N'RegistrationStarted', N'RegistrationRequested', CAST('2018-06-07 06:34:15.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (373306, 456, N'RegistrationAccepted', N'RegistrationAccepted', CAST('2018-06-07 22:26:38.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (383654, 456, N'RegistrationConfirmed', N'RegistrationConfirmed', CAST('2018-06-15 00:04:10.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (408204, 456, N'RegistrationCompleted', N'Registered', CAST('2018-07-10 16:38:52.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (992706, 456, N'LossInitiated', N'LossNotified', CAST('2019-05-28 22:38:10.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (1009197, 456, N'LossConfirmed', N'LossConfirmed', CAST('2019-06-12 00:38:43.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (1029619, 456, N'LossCompleted', N'NotSupplied', CAST('2019-06-26 16:41:06.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);