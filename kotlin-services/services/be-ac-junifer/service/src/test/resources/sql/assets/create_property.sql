-- WARNING: Running this script will clear out a number of tables

-- Address ID: 123
-- Property ID: 456
TRUNCATE TABLE "junifer__Country";
TRUNCATE TABLE "junifer__AddressType";
TRUNCATE TABLE "junifer__Address";
TRUNCATE TABLE "junifer__PropertyTbl";

INSERT INTO "junifer__Country" (
    id,
    "internalKey",
    "languageKey",
    "isoCode",
    "dialCode",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    1,
    'Great Britain',
    'Country.GreatBritain',
    'GB',
    44,
    'Great Britain',
    'N',
    1,
    1);

INSERT INTO "junifer__AddressType" (
    id,
    "name",
    "description",
    "addressFormatComponentFk",
    "postcodeApiKey",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, 'Standard address', 'Standard address', 999, 'postcode', 'Standard address', 'N', 1, 1),
    (101, 'UK MRA Standard Address', 'UK MRA Standard Address', 999, 'postcode', 'UK MRA Standard Address', 'N', 1, 1);


INSERT INTO "junifer__Address" (
    id,
    "parentAddressFk",
    "addressTypeFk",
    "address1",
    "address2",
    "address3",
    "address4",
    "address5", -- There are actually 30 address lines, I can only be bothered to populate 5
    "postCode",
    "countryFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    123,
    null,
    1,
    '107 Power Rd',
    'Chiswick',
    'London',
    'United Kingdom',
    null,
    'W4 5PY',
    1,
    'So energy office',
    'N',
    1,
    1);

INSERT INTO "junifer__PropertyTbl" (
    id,
    "addressFk",
    "identifier",
    "rootPropertyTblFk",
    "parentPropertyTblFk",
    "isProductionFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    456,
    123,
    'Test property 456',
    456,
    null,
    'N',
    'Test property 456 reference',
    'N',
    1,
    1);