INSERT INTO "junifer__Account" (id, "billingEntityFk", "parentAccountFk", "customerFk", "accountTypeFk", "number",
                                "name",
                                "createdDttm", "createdUserTblFk", "fromDttm", "toDttm", "terminatedUserTblFk",
                                "closedDttm",
                                "currencyFk", "languageTblFk", "dunningPackageFk", "billPackageFk", "billingCycleFk",
                                "billingAlignDttm", "initialBillStatus", "paymentTermFk", "payPointLocation",
                                "salesTaxExemptFl",
                                "lastAccountTransactionFk", "cancelFl", "cancelledDttm", "cancelledUserTblFk",
                                "terminatedDttm",
                                "parentOverridesChildFl", "parentReconciliationBillingFl",
                                "overrideAllocateCreditOnRebill",
                                "autoRebillBehaviour", "txnAllocatorComponentFk", "reference", "paymentDataErasedFl",
                                "deleteFl",
                                "versionNo", "partitionId")
VALUES (691792, 1, null, 659172, 3, N'********', N'*Account691791*', now()::timestamptz, 302,
        (now() - interval '1 day')::timestamptz, (now() + interval '1 day')::timestamptz, 302,
        (now() + interval '1 day')::timestamptz, 1, 1, null, 1, 1, now()::timestamptz, N'Accepted', 1,
        N'This', N'N', ********, N'N', null, null, (now() + interval '1 day')::timestamptz, N'N', N'N', null, null,
        null, null, N'N', N'N', 7, 1);
