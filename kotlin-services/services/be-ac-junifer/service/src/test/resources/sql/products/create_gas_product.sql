-- WARNING: Running this script will clear out a number of tables

-- Product Bundle ID: 456
-- Product Bundle Dfn ID: 123
TRUNCATE TABLE "junifer__ProductBundle";
TRUNCATE TABLE "junifer__ProductBundleDfn";
TRUNCATE TABLE "junifer__ProductBundleDfnGroup";
TRUNCATE TABLE "junifer__AgreementType";
TRUNCATE TABLE "junifer__ProductDfn";
TRUNCATE TABLE "junifer__ProductItemDfn";

INSERT INTO "junifer__ProductBundle" (
    id,
    "productBundleDfnFk",
    "agreementTypeFk",
    "accountFk",
    "purchasingGroupFk",
    "brokerAgentFk",
    "name",
    "number",
    "createdDttm",
    "createdUserTblFk",
    "orderDttm",
    "fromDttm",
    "toDttm",
    "contractedToDttm",
    "followOnFl",
    "followOnDttm",
    "followOnProductBundleDfnFk",
    "terminationReceivedDt",
    "productTerminationReasonFk",
    "currencyFk",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "billableFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    456,
    123,
    101,
    934,
    null,
    1,
    null,
    N'********',
    N'2015-11-27 17:12:34.0000000',
    1,
    N'2015-11-27 17:12:34.0000000',
    N'2015-12-16 00:00:00.0000000',
    N'2016-12-16 00:00:00.0000000',
    N'2016-12-16 00:00:00.0000000',
    N'N',
    null,
    null,
    null,
    null,
    1,
    N'N',
    null,
    null,
    N'Y',
    N'*REDACTED*',
    N'N',
    1,
    1);

INSERT INTO "junifer__ProductBundleDfn" (
    id,
    "name",
    "billDisplay",
    "status",
    "fromDttm",
    "toDttm",
    "endDttm",
    "followOnProductBundleDfnFk",
    "productBundleDfnGroupFk",
    "productBundleDfnTypeFk",
    "productFlexClassFk",
    "billingEntityFk",
    "currencyFk",
    "billingCycleFk",
    "countryFk",
    "useDfnNameFl",
    "deemedDefault",
    "availablePublicFl",
    "offSupplyBillFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    123,
    N'Gas | SOAR | 151116',
    N'So Armadillo - Gas',
    N'Active',
    N'2015-10-26 00:00:00.0000000',
    N'2016-07-01 00:00:00.0000000',
    N'9999-01-01 00:00:00.0000000',
    68,
    102,
    5,
    null,
    1,
    1,
    null,
    1,
    N'Y',
    N'Default',
    N'N',
    N'N',
    N'G1R-SOAR-151116',
    N'N',
    5,
    1);

INSERT INTO "junifer__ProductBundleDfnGroup" (
    id,
    "code",
    "notes",
    "billDisplay",
    "agreementTypeFk",
    "status",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (102,
        N'FIXED',
        null,
        N'Fixed - 12 Months product',
        101,
        N'Active',
        null,
        N'N',
        1,
        1);

INSERT INTO "junifer__AgreementType" (
    id,
    "name",
    "months",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'Open ended', null, null, N'N', 1, 1),
    (101, N'Fixed - 12 Months', 12, null, N'N', 1, 1),
    (108, N'Fixed - 24 Months', 24, null, N'N', 1, 1),
    (109, N'Fixed - 18 Months', 18, null, N'N', 1, 1),
    (110, N'Fixed - 30 Months', 30, null, N'N', 1, 1),
    (111, N'Fixed - 36 Months', 36, null, N'N', 1, 1);

INSERT INTO "junifer__ProductBundleDfnType" (
    id,
    "name",
    "description",
    "status",
    "assetTypeFk",
    "numberOfRates",
    "altProductBundleDfnTypeFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, N'E1R', N'Electricity 1-rate', N'Active', 3, 1, null, null, N'N', 1, 1),
    (3, N'E2R', N'Electricity 2-rate', N'Active', 3, 2, null, null, N'N', 1, 1),
    (4, N'E3R', N'Electricity ''1-rate'' for 2-rate meters (2 matched rates)', N'Active', 3, 2, 3, null, N'N', 1, 1),
    (5, N'G1R', N'Gas 1-rate', N'Active', 2, 1, null, null, N'N', 1, 1);

INSERT INTO "junifer__ProductDfn" (
    id,
    "productBundleDfnFk",
    "productClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "optionalLinkageFl",
    "multipleLinkageFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (54,
        123,
        2,
        0,
        N'Gas',
        N'Y',
        1,
        1,
        3,
        N'N',
        N'Y',
        null,
        N'N',
        1,
        1);

INSERT INTO "junifer__ProductItemDfn" (
    id,
    "productDfnFk",
    "productItemClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "productItemTypeFk",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "overrideCode",
    "allowInactiveFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (11719,
        54,
        15,
        0,
        N'Gas Supply',
        N'Y',
        2,
        1,
        1,
        null,
        N'NoOverride',
        N'N',
        null,
        N'N',
        1,
        1);



