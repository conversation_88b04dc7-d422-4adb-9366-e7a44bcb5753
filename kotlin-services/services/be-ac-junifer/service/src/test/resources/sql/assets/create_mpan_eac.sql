-- WARNING: Running this script will clear out a number of tables

-- Meter Point ID: 123
-- MPAN ID: 456

TRUNCATE TABLE "junifer__Mpan";
TRUNCATE TABLE "junifer__MpanConfigPeriod";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__MpanEacSet";
TRUNCATE TABLE "junifer__MpanEac";
TRUNCATE TABLE "junifer__ProductPropertyAsset";
TRUNCATE TABLE "junifer__Product";
TRUNCATE TABLE "junifer__ProductItem";
TRUNCATE TABLE "junifer__MpanProductItem";
TRUNCATE TABLE "junifer__UkTprMapping";
TRUNCATE TABLE "junifer__RateName";

INSERT INTO "junifer__Mpan" (
    id,
    "meterPointFk",
    "mpanClass",
    "msid",
    "ukDistributionAreaFk",
    "specialNeedsInformation",
    "changeOfTenancyFl",
    "measurementType",
    "balancingMechanismUnitId",
    "balancingMechanismUnitDfnFk",
    "soleTradingUnitFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, 'SVA', null, 16, null, 'N', 'Import', null, null, 'N', 'test MPAN reference', 'N', 1, 1);

insert into "junifer__MpanConfigPeriod" (
    id,
    "mpanFk",
    "fromDt",
    "toDt",
    "ukLineLossFactorClassFk",
    "ukMeterTimeswitchClassFk",
    "ukProfileClassFk",
    "ukGspGroupFk",
    "ukMeasurementClassFk",
    "ukStdSettlementConfigFk",
    "dcMarketParticipantFk",
    "dcAppointedBy",
    "daMarketParticipantFk",
    "daAppointedBy",
    "mopMarketParticipantFk",
    "mopAppointedBy",
    "mapContractTypeFk",
    "supMarketParticipantFk",
    "mpanReadingFrequencyFk",
    "energisedFl",
    "operationType",
    "erroneousTransferFl",
    "amrFl",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "dccSupplyStatus",
    "mpanCodeOfPracticeFk",
    "mpanCommunicationMethodFk",
    "meterInstallRequiredFl",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (4184544, 456, CAST('2020-02-19' AS DATE), CAST('9999-01-01' AS DATE), 6052, 495, 1, 3, 1, 366, 2, N'Supplier', 2, N'Supplier', 1601, N'Supplier', null, 565, 8, N'Y', N'Credit', N'N', N'N', 1, null, N'NotApplicable', null, null, N'N', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1127831, 1, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

INSERT INTO "junifer__MpanEacSet" (
    id,
    "mpanFk",
    "fromDt",
    "toDt",
    "source",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (12431832, 456, CAST('2023-01-24' AS DATE), CAST('9999-01-01' AS DATE), N'Industry', N'N', null, null, CAST('2023-01-31 23:16:34.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);

INSERT INTO "junifer__MpanEac" (
    id,
    "mpanEacSetFk",
    "rateNameFk",
    "ukTimePatternRegimeFk",
    "eac",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (13655412, 12431832, null, 2, 1451.20000000, null, N'N', 1, 1);

INSERT INTO "junifer__ProductPropertyAsset" (
    id,
    "productFk",
    "createdDttm",
    "createdUserTblFk",
    "fromDttm",
    "toDttm",
    "propertyTblFk",
    "assetFk",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (3245513, 3297046, CAST('2022-09-02 08:47:15.0000000' AS TIMESTAMP), 1, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), null, 1127831, N'N', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__Product" (
    id,
    "productBundleFk",
    "productDfnFk",
    "productSubTypeFk",
    "name",
    "fromDttm",
    "toDttm",
    "salesTaxFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (3297046, 3298798, 94, 1, null, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), null, null, N'N', 1, 1);

INSERT INTO "junifer__ProductItem" (
    id,
    "productFk",
    "productItemDfnFk",
    "name",
    "salesTaxFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (13797418, 3297046, 14207, null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MpanProductItem" (
    id,
    "productItemFk",
    "mpanProductItemDfnFk",
    "fromDttm",
    "toDttm",
    "ukTprMappingSetFk",
    "halfHourRateDfnFk",
    "pricingPoint",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2013947, 13797418, 937, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, null, N'MSP', null, N'N', 1, 1);

INSERT INTO "junifer__UkTprMapping" (
    id,
    "ukTimePatternRegimeFk",
    "ukStdSettlementConfigFk",
    "ukTprMappingSetFk",
    "fromDt",
    "toDt",
    "rateNameFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, 2, 366, 1, CAST('1996-04-01' AS DATE), CAST('2023-12-01' AS DATE), 14, null, N'N', 8, 1);

INSERT INTO "junifer__RateName" (
    id,
    "rateNameGroupFk",
    "name",
    "code",
    "colour",
    "orderNo",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (14, 4, N'Standard', N'S', N'255,128,255', 3, N'STANDARD', N'N', 1, 1);


