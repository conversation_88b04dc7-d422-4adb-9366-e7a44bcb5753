-- WARNING: Running this script will clear out a number of tables

-- Meter point ID: 123
-- Mpan ID: 456

TRUNCATE TABLE "junifer__Mpan";
TRUNCATE TABLE "junifer__MpanConfigPeriod";
TRUNCATE TABLE "junifer__MeterPointSupplyPeriod";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__UkProfileClass";
TRUNCATE TABLE "junifer__UkGspGroup";
TRUNCATE TABLE "junifer__MeterPointServiceType";
TRUNCATE TABLE "junifer__UkLineLossFactorClass";
TRUNCATE TABLE "junifer__UkMeasurementClass";
TRUNCATE TABLE "junifer__UkMeterTimeswitchClass";
TRUNCATE TABLE "junifer__UkStdSettlementConfig";
TRUNCATE TABLE "junifer__UkDistributionArea";

INSERT INTO "junifer__Mpan" (
    id,
    "meterPointFk",
    "mpanClass",
    "msid",
    "ukDistributionAreaFk",
    "specialNeedsInformation",
    "changeOfTenancyFl",
    "measurementType",
    "balancingMechanismUnitId",
    "balancingMechanismUnitDfnFk",
    "soleTradingUnitFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    456,
    123,
    'SVA',
    null,
    16,
    null,
    'N',
    'Import',
    null,
    null,
    'N',
    'test MPAN reference',
    'N',
    1,
    1);

insert into "junifer__MpanConfigPeriod" (
    id,
    "mpanFk",
    "fromDt",
    "toDt",
    "ukLineLossFactorClassFk",
    "ukMeterTimeswitchClassFk",
    "ukProfileClassFk",
    "ukGspGroupFk",
    "ukMeasurementClassFk",
    "ukStdSettlementConfigFk",
    "dcMarketParticipantFk",
    "dcAppointedBy",
    "daMarketParticipantFk",
    "daAppointedBy",
    "mopMarketParticipantFk",
    "mopAppointedBy",
    "mapContractTypeFk",
    "supMarketParticipantFk",
    "mpanReadingFrequencyFk",
    "energisedFl",
    "operationType",
    "erroneousTransferFl",
    "amrFl",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "dccSupplyStatus",
    "mpanCodeOfPracticeFk",
    "mpanCommunicationMethodFk",
    "meterInstallRequiredFl",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (4184544, 456, CAST('2020-02-19' AS DATE), CAST('9999-01-01' AS DATE), 6052, 495, 1, 3, 1, 366, 2, N'Supplier', 2, N'Supplier', 1601, N'Supplier', null, 565, 8, N'Y', N'Credit', N'N', N'N', 1, null, N'NotApplicable', null, null, N'N', null, null, null, N'N', 1, 1);

insert into "junifer__MeterPointSupplyPeriod" (
    id,
    "meterPointFk",
    "supplyStartDt",
    "supplyEndDt",
    "status",
    "createdDttm",
    "gainFromMarketParticipantFk",
    "lostToMarketParticipantFk",
    "erroneousTransferStatus",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (214030, 123, CAST('2023-01-01' AS DATE), CAST('9999-01-01' AS DATE), N'Active', CAST('2023-01-01 11:56:39.0000000' AS TIMESTAMP), 24, null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1, 1, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

insert into "junifer__UkProfileClass" (
    id,
    "code",
    "fromDt",
    "toDt",
    "description",
    "switchedLoadFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (1, 1, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Domestic Unrestricted', N'N', null, N'N', 1, 1),
    (2, 2, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Domestic Economy 7', N'Y', null, N'N', 1, 1),
    (3, 3, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic Unrestricted', N'N', null, N'N', 1, 1),
    (4, 4, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic Economy 7', N'Y', null, N'N', 1, 1),
    (5, 5, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic,MD,load factor 0-20%', N'N', null, N'N', 1, 1),
    (6, 6, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic,MD,load factor 20-30%', N'N', null, N'N', 1, 1),
    (7, 7, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic,MD,load factor 30-40%', N'N', null, N'N', 1, 1),
    (8, 8, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Non-domestic,MD,load factor 40%+', N'N', null, N'N', 1, 1),
    (9, 0, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Auto-generated half-hourly profile class', N'N', null, N'N', 1, 1);

INSERT INTO "junifer__UkGspGroup" (
    "id",
    "code",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'_A', N'Eastern', null, N'N', 1, 1),
    (2, N'_B', N'East Midlands', null, N'N', 1, 1),
    (3, N'_C', N'London', null, N'N', 1, 1),
    (4, N'_D', N'Merseyside and North Wales', null, N'N', 1, 1),
    (5, N'_E', N'Midlands', null, N'N', 1, 1),
    (6, N'_F', N'Northern', null, N'N', 1, 1),
    (7, N'_G', N'North Western', null, N'N', 1, 1),
    (8, N'_H', N'Southern', null, N'N', 1, 1),
    (9, N'_J', N'South Eastern', null, N'N', 1, 1),
    (10, N'_K', N'South Wales', null, N'N', 1, 1);

INSERT INTO "junifer__MeterPointServiceType" (
    "id",
    "name",
    "automatedReadFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'Legacy', N'N', null, N'N', 1, 1),
    (101, N'DCC', N'Y', null, N'N', 1, 1);

INSERT INTO "junifer__UkLineLossFactorClass" (
    "id",
    "code",
    "ukDistributionAreaFk",
    "fromDt",
    "toDt",
    "meteringSystemSpecific",
    "description",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    6052,
    N'902',
    12,
    CAST('1996-04-01' AS DATE),
    CAST('9999-01-01' AS DATE),
    N'GeneralLlfClassImport',
    N'Domestic Aggregated w/Residual',
    null,
    N'N',
    1,
    1);

INSERT INTO "junifer__UkMeasurementClass" (
    id,
    "code",
    "name",
    "fromDt",
    "toDt",
    "aggregationType",
    "meteredFl",
    "consumptionLevelType",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'A', N'Non Half Hourly Metered', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'NonHalfHourly', N'Y', null, null, N'N', 1, 1),
    (2, N'B', N'Non Half Hourly Unmetered', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'NonHalfHourly', N'N', null, null, N'N', 1, 1),
    (3, N'C', N'HH metered in 100kW Premises', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'HalfHourly', N'Y', N'Is100KW', null, N'N', 1, 1),
    (4, N'D', N'Half Hourly Unmetered', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'HalfHourly', N'N', null, null, N'N', 1, 1),
    (5, N'E', N'HH metered sub 100kW CT', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'HalfHourly', N'Y', N'Not100KW', null, N'N', 2, 1),
    (6, N'F', N'HH metered sub 100kW domestic', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'HalfHourly', N'Y', N'Not100KW', null, N'N', 2, 1),
    (7, N'G', N'HH metered sub 100kW nondom wc', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'HalfHourly', N'Y', N'Not100KW', null, N'N', 2, 1);

INSERT INTO "junifer__UkMeterTimeswitchClass" (
    id,
    "code",
    "fromDt",
    "toDt",
    "description",
    "commonCodeFl",
    "relatedFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (495, 801, CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'NHH Unrestricted 1-rate Non-Prog Credit Meter', N'Y', N'N', null, N'N', 1, 1);

INSERT INTO "junifer__UkStdSettlementConfig" (
    id,
    "code",
    "description",
    "fromDt",
    "toDt",
    "configType",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (366, N'0393', N'Unrestricted', CAST('1996-04-01' AS DATE), CAST('9999-01-01' AS DATE), N'Import', null, N'N', 1, 1);

INSERT INTO "junifer__UkDistributionArea" (
    id,
    "areaId",
    "area",
    "name",
    "operator",
    "emergencyNo",
    "generalEnquiriesNo",
    "contactAddress",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (16, 16, N'North West England', null, N'Electricity North West', N'0800 195 4141', N'0800 048 1820', null, null, N'N', 1, 1);
