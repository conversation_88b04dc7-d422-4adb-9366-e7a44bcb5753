-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__PaymentRequest";
TRUNCATE TABLE "junifer__PaymentMethod";
TRUNCATE TABLE "junifer__PaymentType";

INSERT INTO "junifer__PaymentRequest" (
    id,
    "paymentMethodTypeFk",
    "requestId",
    "transactionId",
    "paymentMethodFk",
    "paymentImportFileRowFk",
    "createdUserTblFk",
    "createdDttm",
    "postedDt",
    "collectionDt",
    "collectionRequestDt",
    "status",
    "currencyFk",
    "paymentCancellationReasonFk",
    "cancelledDttm",
    "cancelledByUserTblFk",
    "repaymentF",
    "amount",
    "description",
    "taskFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (6186347, 8, N'6124420', NULL, 513825, NULL, 1, CAST('2021-03-03 01:42:14.0000000' AS TIMESTAMP), CAST('2021-03-08' AS DATE), NULL, NULL, N'Successful', 1, NULL, NULL, NULL, N'N', 65.********, NULL, ********, NULL, N'N', 5, 1);

INSERT INTO "junifer__PaymentMethod" (
    id,
    "paymentMethodTypeFk",
    "accountFk",
    "status",
    "pendingDefaultFl",
    "fromDttm",
    "toDttm",
    "createdUserTblFk",
    "createdDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (513825, 8, 512806, N'Active', N'N', CAST('2020-11-16 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, CAST('2020-11-16 02:47:26.0000000' AS TIMESTAMP), NULL, N'N', 4, 1);

INSERT INTO "junifer__PaymentMethodType" (
    id,
    "internalKey",
    "languageKey",
    "iconTblFk",
    "componentFk",
    "requestComponentFk",
    "orderNo",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (5, N'Direct Debit', N'PaymentMethodType.DirectDebit', 356, 580, 583, NULL, NULL, N'Y', 2, 1),
    (6, N'BACS', N'PaymentMethodType.BACS', 123, 582, NULL, NULL, NULL, N'N', 1, 1),
    (8, N'Go Cardless Direct Debit', N'PaymentMethodType.GoCardlessDirectDebit', 356, 2751, 2756, NULL, NULL, N'N', 1, 1),
    (101, N'Cheque', N'None', 917, 579, NULL, NULL, NULL, N'N', 1, 1),
    (102, N'Electronic Payment', N'PaymentMethodType.ElectronicPayment', 1308, 3284, NULL, NULL, NULL, N'N', 1, 1),
    (208, N'WorldPay Payment', N'None', 551, 915, NULL, NULL, NULL, N'N', 4, 1),
    (209, N'Bank Transfer', N'None', 553, 915, NULL, NULL, NULL, N'N', 1, 1);