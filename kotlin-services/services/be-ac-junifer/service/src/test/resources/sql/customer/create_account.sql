INSERT INTO "junifer__Account" (id, "billingEntityFk", "parentAccountFk", "customerFk", "accountTypeFk", "number",
                                "name",
                                "createdDttm", "createdUserTblFk", "fromDttm", "toDttm", "terminatedUserTblFk",
                                "closedDttm",
                                "currencyFk", "languageTblFk", "dunningPackageFk", "billPackageFk", "billingCycleFk",
                                "billingAlignDttm", "initialBillStatus", "paymentTermFk", "payPointLocation",
                                "salesTaxExemptFl",
                                "lastAccountTransactionFk", "cancelFl", "cancelledDttm", "cancelledUserTblFk",
                                "terminatedDttm",
                                "parentOverridesChildFl", "parentReconciliationBillingFl",
                                "overrideAllocateCreditOnRebill",
                                "autoRebillBehaviour", "txnAllocatorComponentFk", "reference", "paymentDataErasedFl",
                                "deleteFl",
                                "versionNo", "partitionId")
VALUES (691791, 1, null, 659172, 3, N'********', N'*Account691791*', (now() - interval '1 day')::timestamptz, 302,
        (now() - interval '1 day')::timestamptz, (now() + interval '1 day')::timestamptz, 302,
        (now() + interval '1 day')::timestamptz, 1, 1, null, 1, 1, now()::timestamptz, N'Accepted', 1,
        N'This', N'N', ********, N'N', null, null, (now() + interval '1 day')::timestamptz, N'N', N'N', null, null,
        null, null, N'N', N'N', 7, 1);

INSERT INTO "junifer__AccountType" (id, "internalKey", "languageKey", "code", "customerClassFk", "accountClassFk",
                                    "dunningBasis",
                                    "billPointFl", "payPointFl", "broughtForwardBillingFl", "landlordFl",
                                    "propertyManagerFl",
                                    "promptBeforeTerminationFl", "requiresFinalBillToCloseFl", "reference", "deleteFl",
                                    "versionNo", "partitionId")
VALUES (3, N'Invoice', N'AccountType.ResidentialInvoice', N'CR-INVOICE', 2, 1, N'Account', N'Y', N'Y', N'Y', N'N', N'N',
        N'N', N'N', null, N'N', 1, 1);

INSERT INTO "junifer__Currency" (id, "internalKey", "languageKey", "isoCode", "reference", "deleteFl", "versionNo",
                                 "partitionId")
VALUES (1, N'British Pound', N'Currency.BritishPound', N'GBP', null, N'N', 1, 1);

INSERT INTO "junifer__AccountClass" (id, "internalKey", "languageKey", "cashflowCode", "reference", "deleteFl",
                                     "versionNo",
                                     "partitionId")
VALUES (1, N'Invoice', N'AccountClass.Invoice', N'Income', null, N'N', 1, 1);

INSERT INTO "junifer__BillingCycle" (id, "billingEntityFk", "name", "frequencyInstFk", "alignmentType",
                                     "accountAlignDttmComponentFk", "pivotDttm", "openPriorDayDfnFk",
                                     "closePostDayDfnFk",
                                     "minPeriodFrequencyInstFk", "issueDateCode", "issueLagDayDfnFk", "reference",
                                     "deleteFl",
                                     "versionNo", "partitionId")
VALUES (1, 1, N'Monthly - Anniversary', 1, N'SpecifiedOnAccount', 5403, null, 35, 36, 1348109, N'CreationDate', 37,
        N'RES-MON-ANN', N'N', 12, 1);
