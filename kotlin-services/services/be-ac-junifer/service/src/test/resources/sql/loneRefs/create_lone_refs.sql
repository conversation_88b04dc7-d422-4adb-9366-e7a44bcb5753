TRUNCATE TABLE "junifer__UkVulnCustPsrDfn";
TRUNCATE TABLE "junifer__PaySchedSeasonalDfn";
TRUNCATE TABLE "junifer__ProductDfn";
TRUNCATE TABLE "junifer__ProductBundleDfn";
TRUNCATE TABLE "junifer__HalfHourRateDfn";

INSERT INTO public."junifer__UkVulnCustPsrDfn"
(id, description, "gasDataItemValidSetValue", "elecDataItemValidSetValue", reference, "deleteFl", "versionNo",
 "partitionId")
VALUES (511, 'Water impairment', null, null, null, 'N', 1, 1),
       (106, '(Not Used) Oxygen Use', 26, 26, null, 'N', 2, 1);

INSERT INTO public."junifer__PaySchedSeasonalDfn"
(id, status, "lowPercentage", "highPercentage", "createdDttm", "createdUserTblFk", "activeDttm", "archivedDttm",
 reference, "deleteFl", "versionNo", "partitionId")
VALUES (3, 'Active', 25.00000000, 25.00000000, CAST('2016-11-02 10:50:30.000 +0000' AS TIMESTAMP), 115,
        CAST('2016-12-20 21:42:50.000 +0000' AS TIMESTAMP), null, null, 'N', 2, 1);

INSERT INTO public."junifer__ProductDfn"
(id, "productBundleDfnFk", "productClassFk", "orderNo", "name", "useDfnNameFl", "minCount", "maxCount", "salesTaxFk",
 "optionalLinkageFl", "multipleLinkageFl", reference, "deleteFl", "versionNo", "partitionId")
VALUES (3701, 3756, 2, 0, 'Gas', 'Y', 1, 1, 3, 'N', 'Y', null, 'N', 1, 1),
       (3712, 3793, 2, 0, 'Price Plan', 'Y', 1, 1, 101, 'N', 'N', 'PRICE-PLAN', 'N', 1, 1);

INSERT INTO "junifer__ProductBundle" (id,
                                      "productBundleDfnFk",
                                      "agreementTypeFk",
                                      "accountFk",
                                      "purchasingGroupFk",
                                      "brokerAgentFk",
                                      "name",
                                      "number",
                                      "createdDttm",
                                      "createdUserTblFk",
                                      "orderDttm",
                                      "fromDttm",
                                      "toDttm",
                                      "contractedToDttm",
                                      "followOnFl",
                                      "followOnDttm",
                                      "followOnProductBundleDfnFk",
                                      "terminationReceivedDt",
                                      "productTerminationReasonFk",
                                      "currencyFk",
                                      "cancelFl",
                                      "cancelledDttm",
                                      "cancelledUserTblFk",
                                      "billableFl",
                                      "reference",
                                      "deleteFl",
                                      "versionNo",
                                      "partitionId")
VALUES (456, 123, 101, 691791, null, 1, null, N'00001004', CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), 1,
        CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), CAST('2015-12-16 00:00:00.0000000' AS TIMESTAMP),
        CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), N'N', null,
        null, null, null, 1, N'N', null, null, N'Y', N'*REDACTED*', N'N', 1, 1);

INSERT INTO "junifer__ProductBundleDfn"
(id, "name", "billDisplay", status, "fromDttm", "toDttm", "endDttm", "followOnProductBundleDfnFk",
 "productBundleDfnGroupFk", "productBundleDfnTypeFk", "productFlexClassFk", "billingEntityFk", "currencyFk",
 "billingCycleFk", "countryFk", "useDfnNameFl", "deemedDefault", "availablePublicFl", "offSupplyBillFl", reference,
 "deleteFl", "versionNo", "partitionId")
VALUES (123, 'Gas | SPBI | 12M-X-V3', 'So Birch Essential v3 - Gas', 'Active',
        CAST('2020-10-09 00:00:00.000' AS TIMESTAMP), CAST('2020-12-31 00:00:00.000' AS TIMESTAMP),
        CAST('9999-01-01 00:00:00.000' AS TIMESTAMP), 68, 102, 5, NULL, 1, 1, NULL, 1, 'Y', 'Default', 'N', 'N',
        'G1R-SPBI-12M-X-V3', 'N', 3, 1);

insert into public."junifer__HalfHourRateDfn" (id, name, "matchingType", status, "rateNameGroupFk",
                                                             "createdUserTblFk", "createdDttm", "activatedUserTblFk",
                                                             "activatedDttm", "withdrawnUserTblFk", "withdrawnDttm",
                                                             reference, "deleteFl", "versionNo", "partitionId")
VALUES (1, 'EV', '', 'Active', 1, 1, CAST('2020-10-09 00:00:00.000' AS TIMESTAMP), 1,
        CAST('2020-10-09 00:00:00.000' AS TIMESTAMP), null, null, '', 'N', 1, 1);

insert into public."junifer__HalfHourRateDfnItem"(id, "halfHourRateDfnFk", "matchingCriteria", "orderNo", "fromDDMM", "toDDMM", "fromHHMM", "toHHMM", "offset", duration, "rateNameFk", "rateNameMappingComponentFk", "propertyInstGroupFk", reference, "deleteFl", "versionNo", "partitionId")
VALUES (2, 1, '',1,null,null,'0000','0500',null,null,2,null,null,'','N',1,1),
       (3, 1, '',2,null,null,'0500','0000',null,null,3,null,null,'','N',1,1);
