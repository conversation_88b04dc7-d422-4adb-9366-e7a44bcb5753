-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__ProductType";
TRUNCATE TABLE "junifer__PricePlan";
TRUNCATE TABLE "junifer__ProductDfn";
TRUNCATE TABLE "junifer__ProductBundleDfn";
TRUNCATE TABLE "junifer__ProductItemDfn";
TRUNCATE TABLE "junifer__PricePlanRow";
TRUNCATE TABLE "junifer__PricePlanRowItem";
TRUNCATE TABLE "junifer__SalesTax";
TRUNCATE TABLE "junifer__SalesTaxRate";
TRUNCATE TABLE "junifer__MeterServiceDfn";
TRUNCATE TABLE "junifer__MeterServiceStandardDfn";
TRUNCATE TABLE "junifer__ServiceRatingRate";
TRUNCATE TABLE "junifer__PricePlanRowDimension";
TRUNCATE TABLE "junifer__PricePlanDimension";
TRUNCATE TABLE "junifer__PricePlanDimFlag";
TRUNCATE TABLE "junifer__PricePlanDimensionClass";
TRUNCATE TABLE "junifer__PricePlanDimUkGspGroup";
TRUNCATE TABLE "junifer__PricePlanDimensionClass";
TRUNCATE TABLE "junifer__UkGspGroup";
TRUNCATE TABLE "junifer__RecurringItemDfn";
TRUNCATE TABLE "junifer__FrequencyInst";
TRUNCATE TABLE "junifer__RecurringItemDfnAmount";
TRUNCATE TABLE "junifer__Component";
TRUNCATE TABLE "junifer__EarlyTermChargeDfn";
TRUNCATE TABLE "junifer__EarlyTermChargeDfnAmount";

INSERT INTO "junifer__ProductType" (
    id,
    "name",
    "code",
    "productServiceTypeFk",
    "billingEntityFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, N'Gas Supply (Res)', N'PT-RES-GAS-SUPPLY', 3, 1, N'RES-GAS-SUPPLY', N'N', 1, 1);

INSERT INTO "junifer__PricePlan" (
    id,
    "productDfnFk",
    "productTypeFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (48, 54, 2, NULL, N'N', 1, 1);

INSERT INTO "junifer__ProductDfn" (
    id,
    "productBundleDfnFk",
    "productClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "optionalLinkageFl",
    "multipleLinkageFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (54, 53, 2, 0, N'Gas', N'Y', 1, 1, 3, N'N', N'Y', NULL, N'N', 1, 1);

INSERT INTO "junifer__ProductBundleDfn" (
    id,
    "name",
    "billDisplay",
    "status",
    "fromDttm",
    "toDttm",
    "endDttm",
    "followOnProductBundleDfnFk",
    "productBundleDfnGroupFk",
    "productBundleDfnTypeFk",
    "productFlexClassFk",
    "billingEntityFk",
    "currencyFk",
    "billingCycleFk",
    "countryFk",
    "useDfnNameFl",
    "deemedDefault",
    "availablePublicFl",
    "offSupplyBillFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (
    53,
    N'Gas | SOAR | 151116',
    N'So Armadillo - Gas',
    N'Active',
    CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP),
    CAST('2016-07-01 00:00:00.0000000' AS TIMESTAMP),
    CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP),
    68,
    102,
    5,
    NULL,
    1,
    1,
    NULL,
    1,
    N'Y',
    N'Default',
    N'N',
    N'N',
    N'G1R-SOAR-151116',
    N'N',
    5,
    1);

INSERT INTO "junifer__ProductItemDfn" (
    id,
    "productDfnFk",
    "productItemClassFk",
    "orderNo",
    "name",
    "useDfnNameFl",
    "productItemTypeFk",
    "minCount",
    "maxCount",
    "salesTaxFk",
    "overrideCode",
    "allowInactiveFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (11729, 54, 15, 0, N'Gas Supply', N'Y', 2, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11730, 54, 3, 1, N'Standing Charge', N'Y', 2, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11731, 54, 4, 2, N'Online Discount', N'Y', 4, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11732, 54, 4, 3, N'Dual Fuel Discount', N'Y', 4, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1),
    (11733, 54, 5, 4, N'Early Termination Charge', N'Y', 3, 1, 1, NULL, N'NoOverride', N'N', NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRow" (
    id,
    "pricePlanFk",
    "productSubTypeFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2352, 48, 4, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRowItem" (
    id,
    "pricePlanRowFk",
    "productItemDfnFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (11724, 2352, 11729, NULL, N'N', 1, 1),
    (11725, 2352, 11730, NULL, N'N', 1, 1),
    (11726, 2352, 11731, NULL, N'N', 1, 1),
    (11727, 2352, 11732, NULL, N'N', 1, 1),
    (11728, 2352, 11733, NULL, N'N', 1, 1);

INSERT INTO "junifer__SalesTax" (
    id,
    "countryFk",
    "name",
    "componentFk",
    "isAppliedDynamicallyFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (3, 1, N'Reduced VAT', NULL, N'N', N'UK-RED', N'N', 1, 1);

INSERT INTO "junifer__SalesTaxRate" (
    id,
    "salesTaxFk",
    "fromDttm",
    "toDttm",
    "rate",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, 3, CAST('2000-01-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.05000000, NULL, N'N', 1, 1);

INSERT INTO "junifer__MeterServiceDfn" (
    id,
    "productItemDfnFk",
    "meterMeasurementTypeFk",
    "ignoreMeterMeasureTypeChecksFl",
    "metricDp",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (471, 11729, 4, N'N', 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__MeterServiceStandardDfn" (
    id,
    "meterServiceDfnFk",
    "serviceRatingFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (471, 471, 471, NULL, N'N', 1, 1);

INSERT INTO "junifer__ServiceRatingRate" (
    id,
    "serviceRatingFk",
    "fromDttm",
    "toDttm",
    "rate",
    "introRate",
    "minRate",
    "maxRate",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (470, 471, CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 2.49410000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanRowDimension" (
    id,
    "pricePlanRowFk",
    "pricePlanDimensionFk",
    "pricePlanDimValueId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2347, 2352, 95, 94, NULL, N'N', 1, 1),
    (2346, 2352, 94, 469, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimension" (
    id,
    "pricePlanFk",
    "pricePlanDimensionClassFk",
    "orderNo",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (95, 48, 2, 2, N'Direct Debit', NULL, N'N', 1, 1),
    (94, 48, 9, 1, N'GSP Group', NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimFlag" (
    id,
    "pricePlanDimensionFk",
    "valueFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (94, 95, N'Y', NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimensionClass" (
    id,
    "internalKey",
    "languageKey",
    "iconTblFk",
    "componentFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, N'Direct Debit', N'PricePlanDimensionClass.DirectDebit', 1308, 1259, NULL, N'N', 1, 1),
    (9, N'GSP Group', N'PricePlanDimensionClass.GspGroup', 1634, 1934, NULL, N'N', 1, 1);

INSERT INTO "junifer__PricePlanDimUkGspGroup" (
    id,
    "pricePlanDimensionFk",
    "ukGspGroupFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (469, 94, 1, NULL, N'N', 1, 1);

INSERT INTO "junifer__UkGspGroup" (
    id,
    "code",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'_A', N'Eastern', NULL, N'N', 1, 1);

INSERT INTO "junifer__RecurringItemDfn" (
    id,
    "productItemDfnFk",
    "creditFl",
    "pricingFrequencyInstFk",
    "proRataPricingFl",
    "applyCode",
    "alignCode",
    "alignFrequencyInstFk",
    "proRataFl",
    "minUnits",
    "maxUnits",
    "dp",
    "proRataComponentFk",
    "introPeriodFl",
    "introPdFrequencyInstFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2350, 11730, N'N', 503980, N'Y', N'InArrears', N'BillingPeriod', NULL, N'Y', 1, 1, 6, NULL, N'N', NULL, NULL, N'N', 1, 1),
    (2351, 11731, N'Y', 503981, N'Y', N'InArrears', N'StartDate', 503982, N'Y', 1, 1, 2, NULL, N'N', NULL, NULL, N'N', 1, 1),
    (2352, 11732, N'Y', 503983, N'Y', N'InArrears', N'StartDate', 503984, N'Y', 1, 1, 2, NULL, N'N', NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__FrequencyInst" (
    id,
    "frequencyComponentFk",
    "frequencyMultiple",
    "manualFreqFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (503980, 200, 1, NULL, NULL, N'N', 1, 1),
    (503981, 203, 1, NULL, NULL, N'N', 1, 1),
    (503983, 203, 1, NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__RecurringItemDfnAmount" (
    id,
    "recurringItemDfnFk",
    "fromDttm",
    "toDttm",
    "amount",
    "introAmount",
    "minAmount",
    "maxAmount",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2347, 2350, CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.20000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1),
    (2348, 2351, CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.00000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1),
    (2349, 2352, CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 0.00000000, 0.00000000, NULL, NULL, NULL, N'N', 1, 1);

INSERT INTO "junifer__Component" (
    id,
    "componentGroupFk",
    "propertyDfnGroupFk",
    "internalKey",
    "languageKey",
    "clientType",
    "serverBean",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (200, 23, NULL, N'Daily', N'Component.DailyFrequency', N'Junifer.Thor.Components.Frequency.DailyFrequency,Junifer.Thor.Components', N'frequencyDaily', NULL, N'N', 1, 1),
    (203, 23, NULL, N'Yearly', N'Component.YearlyFrequency', N'Junifer.Thor.Components.Frequency.YearlyFrequency,Junifer.Thor.Components', N'frequencyYearly', NULL, N'N', 1, 1);

INSERT INTO "junifer__EarlyTermChargeDfn" (
    id,
    "productItemDfnFk",
    "dp",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (7520, 11733, 2, null, N'N', 1, 1);

INSERT INTO "junifer__EarlyTermChargeDfnAmount" (
    id,
    "earlyTermChargeDfnFk",
    "fromDttm",
    "toDttm",
    "amount",
    "minAmount",
    "maxAmount",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (8330, 7520, CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 4.76000000, null, null, null, N'N', 1, 1);
