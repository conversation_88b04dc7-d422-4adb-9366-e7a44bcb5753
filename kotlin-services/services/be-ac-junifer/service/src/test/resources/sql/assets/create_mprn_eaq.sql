-- WARNING: Running this script will clear out a number of tables

-- Meter Point ID: 123
-- MPRN ID: 456

TRUNCATE TABLE "junifer__Mprn";
TRUNCATE TABLE "junifer__MprnConfigPeriod";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__MprnAnnualQuantity";

INSERT INTO "junifer__Mprn" (
    id,
    "meterPointFk",
    "lastSubmittedReadEventDt",
    "changeOfTenancyFl",
    "lastUpdatedSmartCVDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, CAST('2021-02-10' AS DATE), N'N', null, null, N'N', 40, 1);

INSERT INTO "junifer__MprnConfigPeriod" (
    id,
    "mprnFk",
    "fromDt",
    "toDt",
    "ukGasExitZoneFk",
    "ukGspGroupFk",
    "mamMarketParticipantFk",
    "mamAppointedBy",
    "mraMarketParticipantFk",
    "mraAppointedBy",
    "shipperMarketParticipantFk",
    "supMarketParticipantFk",
    "sicCodeFk",
    "readingType",
    "mprnReadingFrequencyFk",
    "connectionStatus",
    "marketSector",
    "nominationShipperReference",
    "xoserveConfirmationReference",
    "spManned24HoursFl",
    "operationType",
    "erroneousTransferFl",
    "distanceToNtsExit",
    "distanceToNtsEntry",
    "directlyNtsConnectedFl",
    "ldzOptionalTariffFl",
    "ntsOptionalTariffFl",
    "amrFl",
    "amrAppointedBy",
    "ukGasSettlementClassFk",
    "ukGasCsepFk",
    "dniFl",
    "meterFaultStatusFl",
    "amrMarketParticipantFk",
    "lastCheckReadDt",
    "mprnSupplyPointCategoryFk",
    "class3BatchFreq",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "priorityConsumerCategory",
    "gasActOwner",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (10907772, 456, CAST('2021-06-10' AS DATE), CAST('9999-01-01' AS DATE), 4, 1, 671, null, null, null, 567, 567, null, null, 5, N'Connected', N'Domestic', N'374561', N'240422341', N'N', N'Credit', N'N', null, null, N'N', N'N', N'N', N'N', null, 4, null, N'N', N'N', null, null, 7, null, 1, null, null, N'Transporter', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MprnAnnualQuantity" (
    id,
    "mprnFk",
    "fromDt",
    "toDt",
    "consumption",
    "source",
    "createdDttm",
    "createdUserTblFk",
    "aqClass",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (12609249, 456, CAST('2023-02-16' AS DATE), CAST('9999-01-01' AS DATE), 4346.00000000, N'Industry', CAST('2023-02-08 13:58:33.0000000' AS TIMESTAMP), 1, N'Rolling', N'N', null, null, null, N'N', 1, 1);
