-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__AccountTransaction";

INSERT INTO "junifer__AccountTransaction" (
    id,
    "accountFk",
    "orderNo",
    "accountTransactionTypeFk",
    "status",
    "description",
    "dueDt",
    "currencyFk",
    "creditAmount",
    "debitAmount",
    "creditBalance",
    "debitBalance",
    "accountBalance",
    "createdDttm",
    "createdUserTblFk",
    "acceptedDttm",
    "acceptedByUserTblFk",
    "collectionRequestDt",
    "collectionDt",
    "collectionScheduledFl",
    "maxCollectionAmount",
    "cancelledDttm",
    "cancelledByUserTblFk",
    "cancelAccountTransactionFk",
    "rejectedDttm",
    "rejectedByUserTblFk",
    "dunningFl",
    "authorisedByUserTblFk",
    "authorisedDttm",
    "collectionCancelledByUserTblFk",
    "collectionCancelledDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (********, 713739, 39, 8, N'Accepted', N'CardPayment UID-111111-AC-********-TR-**********', NULL, 1, 150.********, NULL, 0.********, NULL, -6.********, CAST('2023-02-01 15:22:19.0000000' AS TIMESTAMP), 1, CAST('2023-02-01 15:22:19.0000000' AS TIMESTAMP), 1, NULL, NULL, N'N', NULL, NULL, NULL, NULL, NULL, NULL, N'N', NULL, NULL, NULL, NULL, N'CardPayment UID-111111-AC-********-TR-**********', N'N', 5, 1);