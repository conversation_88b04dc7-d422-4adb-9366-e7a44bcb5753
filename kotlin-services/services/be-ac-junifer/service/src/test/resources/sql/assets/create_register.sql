-- WARNING: Running this script will clear out a number of tables

-- Meter ID: 123
-- Register ID: 456
TRUNCATE TABLE "junifer__Meter";
TRUNCATE TABLE "junifer__MeterType";
TRUNCATE TABLE "junifer__MeterRegister";
TRUNCATE TABLE "junifer__MeterRegisterConfigPeriod";
TRUNCATE TABLE "junifer__MeterRegisterType";

INSERT INTO "junifer__Meter" (
    id,
    "utilityMarketFk",
    "identifier",
    "timeZoneTblFk",
    "location",
    "locationCode",
    "meterTypeFk",
    "manufacturer",
    "model",
    "manufacturedYear",
    "lastInspectionDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 2, N'G4W00345080501', 1, null, N'32', 16, null, null, null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MeterType" (
    id,
    "code",
    "description",
    "utilityMarketFk",
    "prepayFl",
    "deviceClass",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (16, N'CR', N'Credit', 2, N'N', N'Meter', null, N'N', 1, 1);

INSERT INTO "junifer__MeterRegister" (
    id,
    "meterFk",
    "identifier",
    "externalIdentifier",
    "fromDttm",
    "toDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, null, null, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), null, N'N', 1, 1);

INSERT INTO "junifer__MeterRegisterConfigPeriod" (
    id,
    "meterRegisterFk",
    "fromDttm",
    "toDttm",
    "meterReadingConfigFk",
    "meterReadingType",
    "meterMeasurementTypeFk",
    "meterRegisterTypeFk",
    "metricUnitFk",
    "coefficient",
    "multiplier",
    "digits",
    "decimalPlaces",
    "correctionFactor",
    "readingMode",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (137506, 456, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, N'Accumulating', 4, 1081, 8, N'Positive', 1.00000000, 5, 0, 1.02264000, N'Billable', null, N'N', 2, 1);

INSERT INTO "junifer__MeterRegisterType" (
    "id",
    "utilityMarketFk",
    "name",
    "code",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1081, 2, N'Gas Volume', N'G', null, N'N', 1, 1);


