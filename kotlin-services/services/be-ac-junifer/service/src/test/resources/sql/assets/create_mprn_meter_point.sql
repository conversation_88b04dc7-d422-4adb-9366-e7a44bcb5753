-- WARNING: Running this script will clear out a number of tables

-- Meter point ID: 123
-- Mprn ID: 456

TRUNCATE TABLE "junifer__Mprn";
TRUNCATE TABLE "junifer__MprnConfigPeriod";
TRUNCATE TABLE "junifer__MeterPointSupplyPeriod";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__UkGspGroup";
TRUNCATE TABLE "junifer__MeterPointServiceType";
TRUNCATE TABLE "junifer__MarketParticipant";

INSERT INTO "junifer__Mprn" (
    id,
    "meterPointFk",
    "lastSubmittedReadEventDt",
    "changeOfTenancyFl",
    "lastUpdatedSmartCVDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, CAST('2021-02-10' AS DATE), N'N', null, null, N'N', 40, 1);

INSERT INTO "junifer__MprnConfigPeriod" (
    id,
    "mprnFk",
    "fromDt",
    "toDt",
    "ukGasExitZoneFk",
    "ukGspGroupFk",
    "mamMarketParticipantFk",
    "mamAppointedBy",
    "mraMarketParticipantFk",
    "mraAppointedBy",
    "shipperMarketParticipantFk",
    "supMarketParticipantFk",
    "sicCodeFk",
    "readingType",
    "mprnReadingFrequencyFk",
    "connectionStatus",
    "marketSector",
    "nominationShipperReference",
    "xoserveConfirmationReference",
    "spManned24HoursFl",
    "operationType",
    "erroneousTransferFl",
    "distanceToNtsExit",
    "distanceToNtsEntry",
    "directlyNtsConnectedFl",
    "ldzOptionalTariffFl",
    "ntsOptionalTariffFl",
    "amrFl",
    "amrAppointedBy",
    "ukGasSettlementClassFk",
    "ukGasCsepFk",
    "dniFl",
    "meterFaultStatusFl",
    "amrMarketParticipantFk",
    "lastCheckReadDt",
    "mprnSupplyPointCategoryFk",
    "class3BatchFreq",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "priorityConsumerCategory",
    "gasActOwner",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (10907772, 456, CAST('2021-06-10' AS DATE), CAST('9999-01-01' AS DATE), 4, 1, 671, null, null, null, 567, 567, null, null, 5, N'Connected', N'Domestic', N'374561', N'240422341', N'N', N'Credit', N'N', null, null, N'N', N'N', N'N', N'N', null, 4, null, N'N', N'N', null, null, 7, null, 1, null, null, N'Transporter', null, null, null, N'N', 1, 1);

insert into "junifer__MeterPointSupplyPeriod" (
    id,
    "meterPointFk",
    "supplyStartDt",
    "supplyEndDt",
    "status",
    "createdDttm",
    "gainFromMarketParticipantFk",
    "lostToMarketParticipantFk",
    "erroneousTransferStatus",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (
    214030,
    123,
    CAST('2023-01-01' AS DATE),
    CAST('9999-01-01' AS DATE),
    N'Active',
    CAST('2023-01-01 11:56:39.0000000' AS TIMESTAMP),
    24,
    null,
    null,
    null,
    N'N',
    1,
    1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1, 2, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

INSERT INTO "junifer__UkGspGroup" (
    "id",
    "code",
    "name",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'_A', N'Eastern', null, N'N', 1, 1),
    (2, N'_B', N'East Midlands', null, N'N', 1, 1),
    (3, N'_C', N'London', null, N'N', 1, 1),
    (4, N'_D', N'Merseyside and North Wales', null, N'N', 1, 1),
    (5, N'_E', N'Midlands', null, N'N', 1, 1),
    (6, N'_F', N'Northern', null, N'N', 1, 1),
    (7, N'_G', N'North Western', null, N'N', 1, 1),
    (8, N'_H', N'Southern', null, N'N', 1, 1),
    (9, N'_J', N'South Eastern', null, N'N', 1, 1),
    (10, N'_K', N'South Wales', null, N'N', 1, 1);

INSERT INTO "junifer__MeterPointServiceType" (
    "id",
    "name",
    "automatedReadFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'Legacy', N'N', null, N'N', 1, 1),
    (101, N'DCC', N'Y', null, N'N', 1, 1);

INSERT INTO "junifer__MarketParticipant" (
    id,
    "code",
    "name",
    "utilityMarketFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (671, N'GTM', N'National Grid Metering', 2, null, N'N', 9, 1);
