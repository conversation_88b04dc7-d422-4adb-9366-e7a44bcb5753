-- WARNING: Running this script will clear out a number of tables

-- Meter point ID: 123
-- Mprn ID: 456

TRUNCATE TABLE "junifer__Mprn";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__MprnSupplyStatus";

INSERT INTO "junifer__Mprn" (
    id,
    "meterPointFk",
    "lastSubmittedReadEventDt",
    "changeOfTenancyFl",
    "lastUpdatedSmartCVDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, CAST('2021-02-10' AS DATE), N'N', null, null, N'N', 40, 1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1, 2, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

INSERT INTO "junifer__MprnSupplyStatus" (
    id,
    "mprnFk",
    "eventType",
    "supplyStatus",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (297742, 456, N'Created', N'NotSupplied', CAST('2018-05-16 03:26:21.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (297803, 456, N'RegistrationStarted', N'RegistrationRequested', CAST('2018-05-16 03:28:05.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (297950, 456, N'RegistrationAccepted', N'RegistrationAccepted', CAST('2018-05-16 13:23:57.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (304029, 456, N'RegistrationConfirmed', N'RegistrationConfirmed', CAST('2018-06-04 15:46:06.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (304799, 456, N'RegistrationCompleted', N'Registered', CAST('2018-06-07 06:39:56.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);