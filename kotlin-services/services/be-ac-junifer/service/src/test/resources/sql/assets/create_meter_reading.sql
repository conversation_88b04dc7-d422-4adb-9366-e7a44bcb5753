-- WARNING: Running this script will clear out a number of tables

-- Meter point ID: 123
-- Meter ID: 456
-- Register ID: 789

TRUNCATE TABLE "junifer__UtilityTimeSeriesEvent";
TRUNCATE TABLE "junifer__TimeSeriesSource";
TRUNCATE TABLE "junifer__TimeSeriesQuality";
TRUNCATE TABLE "junifer__MetricUnit";
TRUNCATE TABLE "junifer__ReadingWorkflowStatus";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__Meter";
TRUNCATE TABLE "junifer__MeterType";
TRUNCATE TABLE "junifer__MeterRegister";
TRUNCATE TABLE "junifer__MeterRegisterType";
TRUNCATE TABLE "junifer__MeterRegisterConfigPeriod";

-- 6 time series events for a meter point, 2 removed, 4 accepted
INSERT INTO "junifer__UtilityTimeSeriesEvent" (
    id,
    "taskDfnGroupFk",
    "batchId",
    "eventSourceFk",
    "eventSourceIdentifier",
    "eventId",
    "receivedDttm",
    "meterPointFk",
    "meterPointTimeSeriesFk",
    "meterFk",
    "meterRegisterFk",
    "meterMeasurementTypeFk",
    "meterReadingManualFk",
    "utilityMarketFk",
    "timeSeriesSourceFk",
    "timeSeriesQualityFk",
    "sequenceType",
    "rawFromDttm",
    "rawEventDttm",
    "rawTimeZoneTblFk",
    "rawConsumption",
    "rawCumulative",
    "rawMetricUnitFk",
    "fromDttm",
    "eventDttm",
    "timeZoneTblFk",
    "consumption",
    "cumulative",
    "metricUnitFk",
    "reportConsumption",
    "reportCumulative",
    "reportMetricUnitFk",
    "status",
    "timeSeriesEventReasonFk",
    "validationFailureInfo",
    "readingWorkflowStatusFk",
    "industryInvalidityReason",
    "suppressPublishToIndustryFl",
    "dataPartitionId",
    "lastStatusUpdateDttm")
VALUES (64101332, 0, 0, 1, N'36196102', 6866327109, CAST('2023-01-04 01:42:00.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 101, 2, N'Normal', null, CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 1, null, 9397.80000000, 1, null, CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 2, null, 9397.80000000, 1, null, 9397.80000000, 1, N'Removed', 3, null, null, null, N'N', 9, CAST('2023-01-04 05:00:22.0000000' AS TIMESTAMP)),
    (64150691, 0, 0, 1, N'36214031', 6871729309, CAST('2023-01-04 04:10:06.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 101, 2, N'Normal', CAST('2022-12-05 00:00:00.0000000' AS TIMESTAMP), CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 1, null, 9397.20000000, 1, CAST('2022-12-05 00:00:00.0000000' AS TIMESTAMP), CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 2, 710.00000000, 9397.20000000, 1, 710.00000000, 9397.20000000, 1, N'Accepted', null, null, null, null, N'N', 9, CAST('2023-01-09 00:26:59.0000000' AS TIMESTAMP)),
    (64495289, 0, 0, 0, N'0', 6911628909, CAST('2023-01-09 00:26:59.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 3, 3, N'Normal', null, CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 2, 714.10000000, 9401.30000000, 1, null, CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), 2, 714.10000000, 9401.30000000, 1, 714.10000000, 9401.30000000, 1, N'Removed', 3, null, null, null, N'N', 9, CAST('2023-01-09 00:26:59.0000000' AS TIMESTAMP)),
    (64259663, 0, 0, 1, N'36294309', 6883400609, CAST('2023-01-05 01:40:05.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 101, 2, N'Normal', CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), CAST('2023-01-05 00:00:00.0000000' AS TIMESTAMP), 1, null, 9422.50000000, 1, CAST('2023-01-04 00:00:00.0000000' AS TIMESTAMP), CAST('2023-01-05 00:00:00.0000000' AS TIMESTAMP), 2, 25.30000000, 9422.50000000, 1, 25.30000000, 9422.50000000, 1, N'Accepted', null, null, null, null, N'N', 9, CAST('2023-01-05 05:00:23.0000000' AS TIMESTAMP)),
    (66279725, 0, 0, 1, N'38809400', 7109915409, CAST('2023-02-04 02:07:50.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 101, 2, N'Normal', CAST('2023-01-05 00:00:00.0000000' AS TIMESTAMP), CAST('2023-02-04 00:00:00.0000000' AS TIMESTAMP), 1, null, 10106.20000000, 1, CAST('2023-01-05 00:00:00.0000000' AS TIMESTAMP), CAST('2023-02-04 00:00:00.0000000' AS TIMESTAMP), 2, 683.70000000, 10106.20000000, 1, 683.70000000, 10106.20000000, 1, N'Accepted', null, null, null, null, N'N', 9, CAST('2023-02-07 21:21:04.0000000' AS TIMESTAMP)),
    (66362998, 0, 0, 1, N'38881362', 7118594909, CAST('2023-02-05 01:34:51.0000000' AS TIMESTAMP), 123, 1000049, 456, 789, 1, null, 1, 101, 2, N'Normal', CAST('2023-02-04 00:00:00.0000000' AS TIMESTAMP), CAST('2023-02-05 00:00:00.0000000' AS TIMESTAMP), 1, null, 10126.70000000, 1, CAST('2023-02-04 00:00:00.0000000' AS TIMESTAMP), CAST('2023-02-05 00:00:00.0000000' AS TIMESTAMP), 2, 20.50000000, 10126.70000000, 1, 20.50000000, 10126.70000000, 1, N'Accepted', null, null, null, null, N'N', 9, CAST('2023-02-05 05:00:13.0000000' AS TIMESTAMP));

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 1, 1, 'test meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test meter point reference', 'N', 1, 1);

INSERT INTO "junifer__Meter" (
    id,
    "utilityMarketFk",
    "identifier",
    "timeZoneTblFk",
    "location",
    "locationCode",
    "meterTypeFk",
    "manufacturer",
    "model",
    "manufacturedYear",
    "lastInspectionDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 2, N'G4W00345080501', 1, null, N'32', 16, null, null, null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MeterType" (
    id,
    "code",
    "description",
    "utilityMarketFk",
    "prepayFl",
    "deviceClass",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (16, N'CR', N'Credit', 2, N'N', N'Meter', null, N'N', 1, 1);

INSERT INTO "junifer__MeterRegister" (
    id,
    "meterFk",
    "identifier",
    "externalIdentifier",
    "fromDttm",
    "toDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (789, 456, null, null, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), null, N'N', 1, 1);

INSERT INTO "junifer__MeterRegisterConfigPeriod" (
    id,
    "meterRegisterFk",
    "fromDttm",
    "toDttm",
    "meterReadingConfigFk",
    "meterReadingType",
    "meterMeasurementTypeFk",
    "meterRegisterTypeFk",
    "metricUnitFk",
    "coefficient",
    "multiplier",
    "digits",
    "decimalPlaces",
    "correctionFactor",
    "readingMode",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (137506, 789, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, N'Accumulating', 4, 1, 8, N'Positive', 1.00000000, 5, 0, 1.02264000, N'Billable', null, N'N', 2, 1);

INSERT INTO "junifer__MeterRegisterType" (
    "id",
    "utilityMarketFk",
    "name",
    "code",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, 1, '00001', '00001', null, N'N', 1, 1),
    (1081, 2, N'Gas Volume', N'G', null, N'N', 1, 1);

INSERT INTO "junifer__TimeSeriesSource" (
    id,
    "code",
    "internalKey",
    "languageKey",
    "iconTblFk",
    "userEnterableFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'AMR', N'AMR', N'TimeSeriesSource.AMR', 530, N'N', null, N'N', 1, 1),
    (2, N'MMR', N'MMR', N'TimeSeriesSource.MMR', 2046, N'N', null, N'N', 1, 1),
    (3, N'System', N'System', N'TimeSeriesSource.System', 1723, N'N', null, N'N', 1, 1),
    (4, N'Customer', N'Customer', N'TimeSeriesSource.Customer', 328, N'Y', null, N'N', 1, 1),
    (5, N'User', N'User', N'TimeSeriesSource.User', 2033, N'Y', null, N'N', 1, 1),
    (6, N'Virtual', N'Virtual', N'TimeSeriesSource.Virtual', 348, N'N', null, N'N', 1, 1),
    (101, N'SMR', N'SMR', N'TimeSeriesSource.SMR', 1660, N'N', null, N'N', 1, 1),
    (201, N'Industry', N'Industry', N'TimeSeriesSource.Industry', 805, N'N', null, N'N', 1, 1),
    (301, N'EDMI', N'EDMI', N'TimeSeriesSource.EDMI', 1660, N'N', null, N'N', 1, 1),
    (302, N'Secure', N'Secure', N'TimeSeriesSource.Secure', 1660, N'N', null, N'N', 1, 1);

INSERT INTO "junifer__TimeSeriesQuality" (
    id,
    "orderNo",
    "internalKey",
    "languageKey",
    "code",
    "qualityType",
    "defaultFl",
    "colour",
    "iconTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, 1, N'Manual', N'TimeSeriesQuality.Manual', N'Manual', N'Manual', N'Y', N'192, 255, 192', 750, null, N'N', 1, 1),
    (2, 2, N'Normal', N'TimeSeriesQuality.Normal', N'Normal', N'Normal', N'Y', N'255, 255, 255', 423, null, N'N', 1, 1),
    (3, 3, N'Estimated', N'TimeSeriesQuality.Estimated', N'Estimated', N'Estimated', N'Y', N'255, 255, 192', 348, null, N'N', 1, 1);

INSERT INTO "junifer__MetricUnit" (
    id,
    "metricUnitTypeFk",
    "code",
    "display",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, 1, N'kWh', N'kWh', null, N'N', 1, 1),
    (8, 2, N'm3', N'm³', null, N'N', 1, 1);

INSERT INTO "junifer__ReadingWorkflowStatus" (
    id,
    "internalKey",
    "languageKey",
    "code",
    "iconTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1, N'Flagged', N'ReadingWorkflowStatus.Flagged', N'Flagged', 1813, null, N'N', 1, 1),
    (2, N'Validation Scheduled', N'ReadingWorkflowStatus.ValidationScheduled', N'Validation Scheduled', 349, null, N'N', 1, 1),
    (3, N'Validation Scheduled With Override', N'ReadingWorkflowStatus.ValidationScheduledWithOverride', N'Validation Scheduled With Override', 359, null, N'N', 1, 1),
    (4, N'Awaiting Industry Response', N'ReadingWorkflowStatus.AwaitingIndustryResponse', N'Awaiting Industry Response', 2210, null, N'N', 1, 1),
    (5, N'Validation Success', N'ReadingWorkflowStatus.ValidationSuccess', N'Validation Success', 410, null, N'N', 1, 1),
    (6, N'Validation Failure', N'ReadingWorkflowStatus.ValidationFailure', N'Validation Failure', 243, null, N'N', 1, 1),
    (7, N'Accepted', N'ReadingWorkflowStatus.Accepted', N'Accepted', 423, null, N'N', 1, 1),
    (8, N'Error', N'ReadingWorkflowStatus.Error', N'Error', 244, null, N'N', 1, 1),
    (203, N'Scheduled For Publishing', N'ReadingWorkflowStatus.ScheduledForPublishing', N'ScheduledForPublishing', 2210, null, N'N', 1, 1);

