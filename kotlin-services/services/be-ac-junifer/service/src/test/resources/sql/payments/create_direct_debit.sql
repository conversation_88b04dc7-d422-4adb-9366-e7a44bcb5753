-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__GoCardlessDDInstruction";
TRUNCATE TABLE "junifer__GoCardlessDirectDebit";
TRUNCATE TABLE "junifer__PaymentMethod";
TRUNCATE TABLE "junifer__GoCardlessDDCollection";

INSERT INTO "junifer__GoCardlessDDInstruction" (
    id,
    "goCardlessDirectDebitFk",
    "status",
    "requestType",
    "requestedDttm",
    "referralCode",
    "reference",
    "deleteFl",
    "versionNo",
    "partition")
VALUES (1162835, 1471390, N'Successful', N'Setup', CAST('2023-03-16 11:39:29.0000000' AS TIMESTAMP), NULL, NULL, N'N', 2, 1);

INSERT INTO "junifer__GoCardlessDirectDebit" (
    id,
    paymentMethodFk,
    sortCode,
    accountNo,
    accountName,
    authRequestDttm,
    authorisedDttm,
    terminatedDttm,
    bankReference,
    mandateReference,
    reference,
    deleteFl,
    versionNo,
    partitionId)
VALUES (1471390, 1471514, N'encrypted-sort-code', N'encrypted-account-number', N'Bobs account', CAST('2023-03-16 11:39:28.0000000' AS TIMESTAMP), CAST('2023-03-21 12:30:04.0000000' AS TIMESTAMP), NULL, N'BA1111F11E1HSF', N'MD1111RV1AYGPX', NULL, N'N', 3, 1);

INSERT INTO "junifer__PaymentMethod" (
    id,
    "paymentMethodTypeFk",
    "accountFk",
    "status",
    "pendingDefaultFl",
    "fromDttm",
    "toDttm",
    "createdUserTblFk",
    "createdDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (1471514, 8, 1190978, N'Active', N'N', CAST('2023-03-16 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, CAST('2023-03-16 11:06:42.0000000' AS TIMESTAMP), NULL, N'N', 3, 1);

INSERT INTO "junifer__GoCardlessDDCollection" (
    id,
    "paymentRequestAttemptFk",
    "goCardlessDirectDebitFk",
    "bacsProcessingDt",
    "referralCode",
    "successPlusFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (********, ********, 1471390, CAST('2023-03-23' AS DATE), NULL, N'N', NULL, N'N', 1, 1);

