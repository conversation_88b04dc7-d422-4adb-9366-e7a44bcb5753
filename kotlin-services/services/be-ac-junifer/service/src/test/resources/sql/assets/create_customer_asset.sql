-- WARNING: Running this script will clear out a number of tables

TRUNCATE TABLE "junifer__Account";
TRUNCATE TABLE "junifer__Mpan";
TRUNCATE TABLE "junifer__MpanConfigPeriod";
TRUNCATE TABLE "junifer__MeterPoint";
TRUNCATE TABLE "junifer__MpanEacSet";
TRUNCATE TABLE "junifer__MpanEac";
TRUNCATE TABLE "junifer__ProductPropertyAsset";
TRUNCATE TABLE "junifer__Product";
TRUNCATE TABLE "junifer__MpanProductItem";
TRUNCATE TABLE "junifer__UkTprMapping";
TRUNCATE TABLE "junifer__RateName";
TRUNCATE TABLE "junifer__Mprn";
TRUNCATE TABLE "junifer__MprnConfigPeriod";
TRUNCATE TABLE "junifer__MprnAnnualQuantity";
TRUNCATE TABLE "junifer__Meter";
TRUNCATE TABLE "junifer__MeterRegister";
TRUNCATE TABLE "junifer__MeterRegisterConfigPeriod";
TRUNCATE TABLE "junifer__ProductBundle";
TRUNCATE TABLE "junifer__ProductBundleDfn";
TRUNCATE TABLE "junifer__Asset";
TRUNCATE TABLE "junifer__MprnSupplyStatus";
TRUNCATE TABLE "junifer__MpanSupplyStatus";
TRUNCATE TABLE "junifer__PropertyTbl";
TRUNCATE TABLE "junifer__MeterPointPhyTimeSeries";
TRUNCATE TABLE "junifer__MeterPointTimeSeries";

INSERT INTO "junifer__Account" (
    id,
    "billingEntityFk",
    "parentAccountFk",
    "customerFk",
    "accountTypeFk",
    "number",
    "name",
    "createdDttm",
    "createdUserTblFk",
    "fromDttm",
    "toDttm",
    "terminatedUserTblFk",
    "closedDttm",
    "currencyFk",
    "languageTblFk",
    "dunningPackageFk",
    "billPackageFk",
    "billingCycleFk",
    "billingAlignDttm",
    "initialBillStatus",
    "paymentTermFk",
    "payPointLocation",
    "salesTaxExemptFl",
    "lastAccountTransactionFk",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "terminatedDttm",
    "parentOverridesChildFl",
    "parentReconciliationBillingFl",
    "overrideAllocateCreditOnRebill",
    "autoRebillBehaviour",
    "txnAllocatorComponentFk",
    "reference",
    "paymentDataErasedFl",
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(691791, 1, null, 740972, 3, N'********', N'*Account691791*', CAST('2021-08-09 14:09:07.0000000' AS TIMESTAMP), 302, CAST('2021-08-09 00:00:00.0000000' AS TIMESTAMP), CAST('2021-10-11 00:00:00.0000000' AS TIMESTAMP), 302, CAST('2021-11-25 14:40:07.0000000' AS TIMESTAMP), 1, 1, null, 1, 1, CAST('2021-08-09 00:00:00.0000000' AS TIMESTAMP), N'Accepted', 1, N'This', N'N', ********, N'N', null, null, CAST('2021-10-14 10:24:56.0000000' AS TIMESTAMP), N'N', N'N', null, null, null, null, N'N', N'N', 7, 1);

INSERT INTO "junifer__Mpan" (
    id,
    "meterPointFk",
    "mpanClass",
    "msid",
    "ukDistributionAreaFk",
    "specialNeedsInformation",
    "changeOfTenancyFl",
    "measurementType",
    "balancingMechanismUnitId",
    "balancingMechanismUnitDfnFk",
    "soleTradingUnitFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 124, 'SVA', null, 16, null, 'N', 'Import', null, null, 'N', 'test MPAN reference', 'N', 1, 1);

insert into "junifer__MpanConfigPeriod" (
    id,
    "mpanFk",
    "fromDt",
    "toDt",
    "ukLineLossFactorClassFk",
    "ukMeterTimeswitchClassFk",
    "ukProfileClassFk",
    "ukGspGroupFk",
    "ukMeasurementClassFk",
    "ukStdSettlementConfigFk",
    "dcMarketParticipantFk",
    "dcAppointedBy",
    "daMarketParticipantFk",
    "daAppointedBy",
    "mopMarketParticipantFk",
    "mopAppointedBy",
    "mapContractTypeFk",
    "supMarketParticipantFk",
    "mpanReadingFrequencyFk",
    "energisedFl",
    "operationType",
    "erroneousTransferFl",
    "amrFl",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "dccSupplyStatus",
    "mpanCodeOfPracticeFk",
    "mpanCommunicationMethodFk",
    "meterInstallRequiredFl",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (4184544, 456, CAST('2020-02-19' AS DATE), CAST('9999-01-01' AS DATE), 6052, 495, 1, 3, 1, 366, 2, N'Supplier', 2, N'Supplier', 1601, N'Supplier', null, 565, 8, N'Y', N'Credit', N'N', N'N', 1, null, N'NotApplicable', null, null, N'N', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MeterPoint" (
    id,
    "assetFk",
    "utilityMarketFk", -- 1 = MPAN, 2 = MPRN
    "identifier",
    "timeZoneTblFk",
    "billableFl",
    "parentMeterPointFk",
    "meterPointPublishFk",
    "lastPublishDttm",
    "nextPublishDttm",
    "newConnectionFl",
    "primaryRelatedMpanFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(123, 418863, 1, 'test gas meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test gas meter point reference', 'N', 1, 1),
(124, 418864, 1, 'test elec meter point identifier', 2, 'Y', null, null, null, null, 'N', 'N', 'test elec point reference', 'N', 1, 1);

INSERT INTO "junifer__MpanEacSet" (
    id,
    "mpanFk",
    "fromDt",
    "toDt",
    "source",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (12431832, 456, CAST('2023-01-24' AS DATE), CAST('9999-01-01' AS DATE), N'Industry', N'N', null, null, CAST('2023-01-31 23:16:34.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);

INSERT INTO "junifer__MpanEac" (
    id,
    "mpanEacSetFk",
    "rateNameFk",
    "ukTimePatternRegimeFk",
    "eac",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (13655412, 12431832, null, 2, 1451.20000000, null, N'N', 1, 1);

INSERT INTO "junifer__ProductPropertyAsset" (
    id,
    "productFk",
    "createdDttm",
    "createdUserTblFk",
    "fromDttm",
    "toDttm",
    "propertyTblFk",
    "assetFk",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(3245513, 1838195, CAST('2022-09-02 08:47:15.0000000' AS TIMESTAMP), 1, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 456, 418863, N'N', null, null, null, N'N', 1, 1),
(3245514, 1838194, CAST('2022-09-02 08:47:15.0000000' AS TIMESTAMP), 1, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 456, 418864, N'N', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MpanProductItem" (
    id,
    "productItemFk",
    "mpanProductItemDfnFk",
    "fromDttm",
    "toDttm",
    "ukTprMappingSetFk",
    "halfHourRateDfnFk",
    "pricingPoint",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2013947, 13797418, 937, CAST('2022-09-02 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, null, N'MSP', null, N'N', 1, 1);

INSERT INTO "junifer__UkTprMapping" (
    id,
    "ukTimePatternRegimeFk",
    "ukStdSettlementConfigFk",
    "ukTprMappingSetFk",
    "fromDt",
    "toDt",
    "rateNameFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (2, 2, 366, 1, CAST('1996-04-01' AS DATE), CAST('2023-12-01' AS DATE), 14, null, N'N', 8, 1);

INSERT INTO "junifer__RateName" (
    id,
    "rateNameGroupFk",
    "name",
    "code",
    "colour",
    "orderNo",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
values (14, 4, N'Standard', N'S', N'255,128,255', 3, N'STANDARD', N'N', 1, 1);

INSERT INTO "junifer__Mprn" (
    id,
    "meterPointFk",
    "lastSubmittedReadEventDt",
    "changeOfTenancyFl",
    "lastUpdatedSmartCVDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, CAST('2021-02-10' AS DATE), N'N', null, null, N'N', 40, 1);

INSERT INTO "junifer__MprnConfigPeriod" (
    id,
    "mprnFk",
    "fromDt",
    "toDt",
    "ukGasExitZoneFk",
    "ukGspGroupFk",
    "mamMarketParticipantFk",
    "mamAppointedBy",
    "mraMarketParticipantFk",
    "mraAppointedBy",
    "shipperMarketParticipantFk",
    "supMarketParticipantFk",
    "sicCodeFk",
    "readingType",
    "mprnReadingFrequencyFk",
    "connectionStatus",
    "marketSector",
    "nominationShipperReference",
    "xoserveConfirmationReference",
    "spManned24HoursFl",
    "operationType",
    "erroneousTransferFl",
    "distanceToNtsExit",
    "distanceToNtsEntry",
    "directlyNtsConnectedFl",
    "ldzOptionalTariffFl",
    "ntsOptionalTariffFl",
    "amrFl",
    "amrAppointedBy",
    "ukGasSettlementClassFk",
    "ukGasCsepFk",
    "dniFl",
    "meterFaultStatusFl",
    "amrMarketParticipantFk",
    "lastCheckReadDt",
    "mprnSupplyPointCategoryFk",
    "class3BatchFreq",
    "meterPointServiceTypeFk",
    "dccServiceStatus",
    "priorityConsumerCategory",
    "gasActOwner",
    "businessSectorFk",
    "cssRegistrationId",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (10907772, 456, CAST('2021-06-10' AS DATE), CAST('9999-01-01' AS DATE), 4, 1, 671, null, null, null, 567, 567, null, null, 5, N'Connected', N'Domestic', N'374561', N'240422341', N'N', N'Credit', N'N', null, null, N'N', N'N', N'N', N'N', null, 4, null, N'N', N'N', null, null, 7, null, 1, null, null, N'Transporter', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__MprnAnnualQuantity" (
    id,
    "mprnFk",
    "fromDt",
    "toDt",
    "consumption",
    "source",
    "createdDttm",
    "createdUserTblFk",
    "aqClass",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (12609249, 456, CAST('2023-02-16' AS DATE), CAST('9999-01-01' AS DATE), 4346.00000000, N'Industry', CAST('2023-02-08 13:58:33.0000000' AS TIMESTAMP), 1, N'Rolling', N'N', null, null, null, N'N', 1, 1);

INSERT INTO "junifer__PropertyTbl" (
    id,
    "addressFk",
    "identifier",
    "rootPropertyTblFk",
    "parentPropertyTblFk",
    "isProductionFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, 'Test property 456', 456, null, 'N', 'Test property 456 reference', 'N', 1, 1);

INSERT INTO "junifer__MeterRegister" (
    id,
    "meterFk",
    "identifier",
    "externalIdentifier",
    "fromDttm",
    "toDttm",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (456, 123, null, null, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), null, N'N', 1, 1);

INSERT INTO "junifer__MeterRegisterConfigPeriod" (
    id,
    "meterRegisterFk",
    "fromDttm",
    "toDttm",
    "meterReadingConfigFk",
    "meterReadingType",
    "meterMeasurementTypeFk",
    "meterRegisterTypeFk",
    "metricUnitFk",
    "coefficient",
    "multiplier",
    "digits",
    "decimalPlaces",
    "correctionFactor",
    "readingMode",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (137506, 456, CAST('2018-06-28 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 1, N'Accumulating', 4, 1081, 8, N'Positive', 1.00000000, 5, 0, 1.02264000, N'Billable', null, N'N', 2, 1);

INSERT INTO "junifer__MeterPointPhyTimeSeries" (
    id,
    "meterPointTimeSeriesFk",
    "meterRegisterConfigPeriodFk",
    reference,
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(761822, 761827, 137506, null, 'N', 1, 1),
(761823, 614181, 137506, null, 'N', 1, 1);

INSERT INTO "junifer__MeterPointTimeSeries" (
    id,
    "meterPointFk",
    identifier,
    "fromDttm",
    "toDttm",
    "continueMeterPointTimeSeriesFk",
    "virtualFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(614180, 123, null, CAST('2020-08-04 23:00:00.000000 +00:00' AS TIMESTAMP), CAST('9999-01-01 00:00:00.000000 +00:00' AS TIMESTAMP), null, 'N', null, 'N', 1, 1),
(614181, 124, null, CAST('2020-08-04 23:00:00.000000 +00:00' AS TIMESTAMP), CAST('9999-01-01 00:00:00.000000 +00:00' AS TIMESTAMP), null, 'N', null, 'N', 1, 1);


INSERT INTO "junifer__ProductBundle" (
    id,
    "productBundleDfnFk",
    "agreementTypeFk",
    "accountFk",
    "purchasingGroupFk",
    "brokerAgentFk",
    "name",
    "number",
    "createdDttm",
    "createdUserTblFk",
    "orderDttm",
    "fromDttm",
    "toDttm",
    "contractedToDttm",
    "followOnFl",
    "followOnDttm",
    "followOnProductBundleDfnFk",
    "terminationReceivedDt",
    "productTerminationReasonFk",
    "currencyFk",
    "cancelFl",
    "cancelledDttm",
    "cancelledUserTblFk",
    "billableFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES
(456, 123, 101, 691791, null, 1, null, N'********', CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), 1, CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), CAST('2015-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), N'N', null, null, null, null, 1, N'N', null, null, N'Y', N'*REDACTED*', N'N', 1, 1),
(457, 124, 101, 691791, null, 1, null, N'********', CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), 1, CAST('2015-11-27 17:12:34.0000000' AS TIMESTAMP), CAST('2015-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), CAST('2016-12-16 00:00:00.0000000' AS TIMESTAMP), N'N', null, null, null, null, 1, N'N', null, null, N'Y', N'*REDACTED*', N'N', 1, 1);

INSERT INTO "junifer__ProductBundleDfn" (
    id,
    "name",
    "billDisplay",
    "status",
    "fromDttm",
    "toDttm",
    "endDttm",
    "followOnProductBundleDfnFk",
    "productBundleDfnGroupFk",
    "productBundleDfnTypeFk",
    "productFlexClassFk",
    "billingEntityFk",
    "currencyFk",
    "billingCycleFk",
    "countryFk",
    "useDfnNameFl",
    "deemedDefault",
    "availablePublicFl",
    "offSupplyBillFl",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES
(123, N'Gas | SOAR | 151116', N'So Armadillo - Gas', N'Active', CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('2016-07-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 68, 102, 5, null, 1, 1, null, 1, N'Y', N'Default', N'N', N'N', N'G1R-SOAR-151116', N'N', 5, 1),
(124, N'Electricity 1-Rate | SOCH | 010317', N'So Chimpanzee - Electricity - 1 Rate', N'Active', CAST('2015-10-26 00:00:00.0000000' AS TIMESTAMP), CAST('2016-07-01 00:00:00.0000000' AS TIMESTAMP), CAST('9999-01-01 00:00:00.0000000' AS TIMESTAMP), 68, 102, 5, null, 1, 1, null, 1, N'Y', N'Default', N'N', N'N', N'E1R-SOCH-010316', N'N', 5, 1);

INSERT INTO "junifer__Product" (
    id,
    "productBundleFk",
    "productDfnFk",
    "productSubTypeFk",
    name,
    "fromDttm",
    "toDttm",
    "salesTaxFk",
    reference,
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(1838195, 456, 689, 4, null, CAST('2021-05-31 23:00:00.000000 +00:00' AS TIMESTAMP), CAST('2022-05-31 23:00:00.000000 +00:00' AS TIMESTAMP), null, null, 'N', 1, 1),
(1838194, 457, 689, 4, null, CAST('2021-05-31 23:00:00.000000 +00:00' AS TIMESTAMP), CAST('2022-05-31 23:00:00.000000 +00:00' AS TIMESTAMP), null, null, 'N', 1, 1);

INSERT INTO "junifer__Asset" (
    id,
    "propertyTblFk",
    "fromDttm",
    "toDttm",
    "assetTypeFk",
    location,
    reference,
    "deleteFl",
    "versionNo",
    "partitionId"
) VALUES
(418863, 456, '2019-09-19 23:00:00.000000 +00:00', '9999-01-01 00:00:00.000000 +00:00', 2, null, null, 'N', 1, 1),
(418864, 456, '2019-09-19 23:00:00.000000 +00:00', '9999-01-01 00:00:00.000000 +00:00', 2, null, null, 'N', 1, 1);

INSERT INTO "junifer__MprnSupplyStatus" (
    id,
    "mprnFk",
    "eventType",
    "supplyStatus",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (297742, 456, N'Created', N'NotSupplied', CAST('2018-05-16 03:26:21.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (297803, 456, N'RegistrationStarted', N'RegistrationRequested', CAST('2018-05-16 03:28:05.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (297950, 456, N'RegistrationAccepted', N'RegistrationAccepted', CAST('2018-05-16 13:23:57.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (304029, 456, N'RegistrationConfirmed', N'RegistrationConfirmed', CAST('2018-06-04 15:46:06.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (304799, 456, N'RegistrationCompleted', N'Registered', CAST('2018-06-07 06:39:56.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);

INSERT INTO "junifer__MpanSupplyStatus" (
    id,
    "mpanFk",
    "eventType",
    "supplyStatus",
    "createdDttm",
    "createdUserTblFk",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (369035, 456, N'Created', N'NotSupplied', CAST('2018-06-07 06:30:48.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (369042, 456, N'RegistrationStarted', N'RegistrationRequested', CAST('2018-06-07 06:34:15.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (373306, 456, N'RegistrationAccepted', N'RegistrationAccepted', CAST('2018-06-07 22:26:38.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (383654, 456, N'RegistrationConfirmed', N'RegistrationConfirmed', CAST('2018-06-15 00:04:10.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (408204, 456, N'RegistrationCompleted', N'Registered', CAST('2018-07-10 16:38:52.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (992706, 456, N'LossInitiated', N'LossNotified', CAST('2019-05-28 22:38:10.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (1009197, 456, N'LossConfirmed', N'LossConfirmed', CAST('2019-06-12 00:38:43.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1),
    (1029619, 456, N'LossCompleted', N'NotSupplied', CAST('2019-06-26 16:41:06.0000000' AS TIMESTAMP), 1, null, N'N', 1, 1);

INSERT INTO "junifer__Meter" (
    id,
    "utilityMarketFk",
    "identifier",
    "timeZoneTblFk",
    "location",
    "locationCode",
    "meterTypeFk",
    "manufacturer",
    "model",
    "manufacturedYear",
    "lastInspectionDt",
    "reference",
    "deleteFl",
    "versionNo",
    "partitionId")
VALUES (123, 2, N'G4W00345080501', 1, null, N'32', 16, null, null, null, null, null, N'N', 1, 1);