{"results": [{"id": -1, "readingDttm": "2025-02-01T00:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.052, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T00:30:00", "fromDttm": "2025-02-01T00:30:00"}, {"id": -1, "readingDttm": "2025-02-01T01:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.059, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T01:00:00", "fromDttm": "2025-02-01T01:00:00"}, {"id": -1, "readingDttm": "2025-02-01T01:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.057, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T01:30:00", "fromDttm": "2025-02-01T01:30:00"}, {"id": -1, "readingDttm": "2025-02-01T02:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.051, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T02:00:00", "fromDttm": "2025-02-01T02:00:00"}, {"id": -1, "readingDttm": "2025-02-01T02:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.059, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T02:30:00", "fromDttm": "2025-02-01T02:30:00"}, {"id": -1, "readingDttm": "2025-02-01T03:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.057, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T03:00:00", "fromDttm": "2025-02-01T03:00:00"}, {"id": -1, "readingDttm": "2025-02-01T03:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.052, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T03:30:00", "fromDttm": "2025-02-01T03:30:00"}, {"id": -1, "readingDttm": "2025-02-01T04:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.06, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T04:00:00", "fromDttm": "2025-02-01T04:00:00"}, {"id": -1, "readingDttm": "2025-02-01T04:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.088, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T04:30:00", "fromDttm": "2025-02-01T04:30:00"}, {"id": -1, "readingDttm": "2025-02-01T05:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.082, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T05:00:00", "fromDttm": "2025-02-01T05:00:00"}, {"id": -1, "readingDttm": "2025-02-01T05:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.081, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T05:30:00", "fromDttm": "2025-02-01T05:30:00"}, {"id": -1, "readingDttm": "2025-02-01T06:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.057, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T06:00:00", "fromDttm": "2025-02-01T06:00:00"}, {"id": -1, "readingDttm": "2025-02-01T06:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.083, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T06:30:00", "fromDttm": "2025-02-01T06:30:00"}, {"id": -1, "readingDttm": "2025-02-01T07:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.087, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T07:00:00", "fromDttm": "2025-02-01T07:00:00"}, {"id": -1, "readingDttm": "2025-02-01T07:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.084, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T07:30:00", "fromDttm": "2025-02-01T07:30:00"}, {"id": -1, "readingDttm": "2025-02-01T08:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.064, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T08:30:00", "fromDttm": "2025-02-01T08:30:00"}, {"id": -1, "readingDttm": "2025-02-01T09:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.083, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T09:00:00", "fromDttm": "2025-02-01T09:00:00"}, {"id": -1, "readingDttm": "2025-02-01T09:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.055, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T09:30:00", "fromDttm": "2025-02-01T09:30:00"}, {"id": -1, "readingDttm": "2025-02-01T10:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.068, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T10:00:00", "fromDttm": "2025-02-01T10:00:00"}, {"id": -1, "readingDttm": "2025-02-01T10:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.074, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T10:30:00", "fromDttm": "2025-02-01T10:30:00"}, {"id": -1, "readingDttm": "2025-02-01T11:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.052, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T11:00:00", "fromDttm": "2025-02-01T11:00:00"}, {"id": -1, "readingDttm": "2025-02-01T11:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.082, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T11:30:00", "fromDttm": "2025-02-01T11:30:00"}, {"id": -1, "readingDttm": "2025-02-01T12:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.059, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T12:00:00", "fromDttm": "2025-02-01T12:00:00"}, {"id": -1, "readingDttm": "2025-02-01T12:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.07, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T12:30:00", "fromDttm": "2025-02-01T12:30:00"}, {"id": -1, "readingDttm": "2025-02-01T13:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.063, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T13:00:00", "fromDttm": "2025-02-01T13:00:00"}, {"id": -1, "readingDttm": "2025-02-01T13:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.079, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T13:30:00", "fromDttm": "2025-02-01T13:30:00"}, {"id": -1, "readingDttm": "2025-02-01T14:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.051, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T14:00:00", "fromDttm": "2025-02-01T14:00:00"}, {"id": -1, "readingDttm": "2025-02-01T14:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.083, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T14:30:00", "fromDttm": "2025-02-01T14:30:00"}, {"id": -1, "readingDttm": "2025-02-01T15:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.058, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T15:00:00", "fromDttm": "2025-02-01T15:00:00"}, {"id": -1, "readingDttm": "2025-02-01T15:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.074, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T15:30:00", "fromDttm": "2025-02-01T15:30:00"}, {"id": -1, "readingDttm": "2025-02-01T16:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.064, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T16:00:00", "fromDttm": "2025-02-01T16:00:00"}, {"id": -1, "readingDttm": "2025-02-01T16:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.073, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T16:30:00", "fromDttm": "2025-02-01T16:30:00"}, {"id": -1, "readingDttm": "2025-02-01T17:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.081, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T17:00:00", "fromDttm": "2025-02-01T17:00:00"}, {"id": -1, "readingDttm": "2025-02-01T17:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.153, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T17:30:00", "fromDttm": "2025-02-01T17:30:00"}, {"id": -1, "readingDttm": "2025-02-01T18:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.101, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T18:00:00", "fromDttm": "2025-02-01T18:00:00"}, {"id": -1, "readingDttm": "2025-02-01T18:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.101, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T18:30:00", "fromDttm": "2025-02-01T18:30:00"}, {"id": -1, "readingDttm": "2025-02-01T19:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.098, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T19:00:00", "fromDttm": "2025-02-01T19:00:00"}, {"id": -1, "readingDttm": "2025-02-01T19:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.095, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T19:30:00", "fromDttm": "2025-02-01T19:30:00"}, {"id": -1, "readingDttm": "2025-02-01T20:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.116, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T20:00:00", "fromDttm": "2025-02-01T20:00:00"}, {"id": -1, "readingDttm": "2025-02-01T20:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.091, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T20:30:00", "fromDttm": "2025-02-01T20:30:00"}, {"id": -1, "readingDttm": "2025-02-01T21:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.064, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T21:00:00", "fromDttm": "2025-02-01T21:00:00"}, {"id": -1, "readingDttm": "2025-02-01T21:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.069, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T21:30:00", "fromDttm": "2025-02-01T21:30:00"}, {"id": -1, "readingDttm": "2025-02-01T22:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.086, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T22:00:00", "fromDttm": "2025-02-01T22:00:00"}, {"id": -1, "readingDttm": "2025-02-01T22:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.056, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T22:30:00", "fromDttm": "2025-02-01T22:30:00"}, {"id": -1, "readingDttm": "2025-02-01T23:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.069, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T23:00:00", "fromDttm": "2025-02-01T23:00:00"}, {"id": -1, "readingDttm": "2025-02-01T23:30:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.06, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-01T23:30:00", "fromDttm": "2025-02-01T23:30:00"}, {"id": -1, "readingDttm": "2025-02-02T00:00:00", "status": "Accepted", "sequenceType": "Normal", "source": "GCMDS", "quality": "Normal", "consumption": 0.05, "cumulative": 0.0, "unit": "kWh", "receivedDttm": "2025-02-02T00:00:00", "fromDttm": "2025-02-02T00:00:00"}]}