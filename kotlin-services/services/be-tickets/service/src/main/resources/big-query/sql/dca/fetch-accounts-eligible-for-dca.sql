with max_month_end as
         (select max(Month_end) as latest_month

          from `soe-prod-data-core-7529.soe_junifer_model.w_monthly_transaction_summary` as ts),

     contact_details as
         (select distinct a.number                                     as account_number,
                          a.id                                         as account_id,
                          if(T.internal<PERSON><PERSON> IS null, '', T.internal<PERSON>ey) as title,
                          cv.forename                                  as forename,
                          cv.surname                                   as surname,
                          cv.dateOfBirthDt                             as date_of_birth,
                          cv.email                                     as email_address,
                          if(cv.number1 IS null, '', cv.number1)       as number1,
                          if(cv.number2 IS null, '', cv.number2)       as number2,
                          if(cv.number3 IS null, '', cv.number3)       as number3,
                          badr.address1                                as billing_adr_1,
                          badr.address2                                as billing_adr_2,
                          badr.address3                                as billing_adr_3,
                          badr.address4                                as billing_adr_4,
                          badr.address5                                as billing_adr_5,
                          badr.postCode                                as billing_postcode,
                          sadr.address1                                as supply_adr_1,
                          sadr.address2                                as supply_adr_2,
                          sadr.address3                                as supply_adr_3,
                          sadr.address4                                as supply_adr_4,
                          sadr.address5                                as supply_adr_5,
                          sadr.postCode                                as supply_postcode,
                          bill_adr.vulnpsrcust_flag                    as psr_flag,
                          bill_adr.supply_end_desc                     as supply_end_desc

          from `soe_junifer_source_delta.Account` as a
                   join `soe_junifer_source_delta.ProductBundle` as pb
                        on pb.accountFk = a.id
                   join `soe_junifer_source_delta.Product` as p
                        on p.productBundleFk = pb.id
                   join `soe_junifer_source_delta.ProductBundleDfn` as pbd
                        on pb.productBundleDfnFk = pbd.id
                   join `soe_junifer_source_delta.Customer` as c
                        on a.customerFk = c.id
                   join `soe_junifer_source_delta.CustomerContact` as cc
                        on cc.customerFk = c.id
                   join `soe_junifer_source_delta.Contact` as con
                        on cc.contactFk = con.id
                   join `soe_junifer_source_delta.ContactVersion` as cv
                        on cv.contactFk = con.id
                   join `soe_junifer_source_delta.AccountContact` as ac
                        on ac.accountFk = a.id
                            and ac.contactFk = con.id
                   left join `soe_junifer_source_delta.Title` as t
                             on cv.titleFk = t.id
                   join `soe-prod-data-core-7529.soe_junifer_model.w_account_d` as bill_adr
                        on bill_adr.account_number = a.number
                   join `soe_junifer_source_delta.Address` as badr
                        on bill_adr.billing_postCode = badr.postCode
                            and cv.addressFk = badr.id
                   join `soe_junifer_source_delta.ProductPropertyAsset` as ppa
                        on ppa.productFk = p.id
                   join `soe_junifer_source_delta.Asset` as ast
                        on ppa.assetFk = ast.id
                   join `soe_junifer_source_delta.PropertyTbl` as pt
                        on ast.propertyTblFk = pt.id
                   join `soe_junifer_source_delta.Address` as sadr
                        on sadr.id = pt.addressFk

          where cast(CV.fromDttm as date) <= current_date()
            and cast(CV.toDttm as date) > current_date()
            and cast(AC.fromDttm as date) <= current_date()
            and cast(AC.toDttm as date) > current_date()
            and AC.cancelFl = 'N'
            and AC.primaryFl = 'Y'
            and lower(cv.forename) not like '%estate of%'
            and lower(cv.surname) not like '%estate of%'
            and lower(cv.forename) not like '%executor of%'
            and lower(cv.surname) not like '%executor of%'
            and lower(cv.forename) not like '%executors of%'
            and lower(cv.surname) not like '%executors of%'
            and lower(cv.forename) not like '(iva)%'
            and lower(cv.surname) not like '(iva)%'),

     payment_plans as
         (select A.number
          from `soe_junifer_source_delta.PaymentPlan` as pp
                   join `soe_junifer_source_delta.Account` as a
                        on pp.accountFk = a.id

          where pp.status = 'Active'
            and pp.deleteFl = 'N'),

     open_complaints as
         (select distinct cast(if(cast(cf.junifer_account as string) IS null, a.number,
                                  cast(cf.junifer_account as string)) as int64) as junifer_account

          from `soe-prod-data-core-7529.soe_freshdesk.w_tickets_d` as tic
                   join `soe-prod-data-core-7529.soe_freshdesk.w_groups_d` as g
                        on tic.group_id = g.group_id
                   join `soe-prod-data-core-7529.soe_freshdesk.w_contacts_d` as c
                        on tic.contact_id = c.contact_id,
               unnest(custom_fields) as cf
                   join `soe_junifer_source_delta.ContactVersion` as cv
                        on cv.email = c.email
                   join `soe_junifer_source_delta.Contact` as con
                        on cv.contactFk = con.id
                   join `soe_junifer_source_delta.AccountContact` as ac
                        on ac.contactFk = con.id
                   join `soe_junifer_source_delta.Account` as a
                        on ac.accountFk = a.id

          where tic.status not in (4, 5)
            and tic.type = 'Official Complaint'),

     et_tickets as
         (select distinct a.number
          from `soe_junifer_source_delta.Ticket` as t
                   join `soe_junifer_source_delta.TicketDefinition` as td
                        on t.ticketDefinitionFk = td.id
                   join `soe_junifer_source_delta.TicketEntity` as tea
                        on tea.ticketFk = t.id
                   join `soe_junifer_source_delta.EntityTbl` as et
                        on tea.entityTblFk = et.id
                   join `soe_junifer_source_delta.Account` as a
                        on tea.entityId = a.id
                            and tea.entityTblFk = 155

          where td.keyPrefix like '%ETSUS%'
             OR td.keyPrefix like '%ET-%'
             or td.keyPrefix like '%ERRON%'),

     dispute_tickets as
         (select distinct a.number
          from `soe_junifer_source_delta.Ticket` as t
                   join `soe_junifer_source_delta.TicketDefinition` as td
                        on t.ticketDefinitionFk = td.id
                   join `soe_junifer_source_delta.TicketEntity` as tea
                        on tea.ticketFk = t.id
                   join `soe_junifer_source_delta.EntityTbl` as et
                        on tea.entityTblFk = et.id
                   join `soe_junifer_source_delta.Account` as a
                        on tea.entityId = a.id
                            and tea.entityTblFk = 155

          where lower(td.keyPrefix) like '%dispute%'
             or lower(td.keyPrefix) like '%getmp%'),

     dca_accounts as (select distinct a.number
                      from `soe_junifer_source_delta.Ticket` as t
                               join `soe_junifer_source_delta.TicketDefinition` as td
                                    on t.ticketDefinitionFk = td.id
                               join `soe_junifer_source_delta.TicketStep` as ts
                                    on t.ticketStepFk = ts.id
                               join `soe_junifer_source_delta.TicketEntity` as te
                                    on te.ticketFk = t.id
                               join `soe_junifer_source_delta.Account` as a
                                    on te.entityId = a.id
                                        and te.entityTblFk = 155

                      where t.status in ('Open', 'Error')
                        and lower(td.keyPrefix) in (
                                                    'conexus-1',
                                                    'pdc-1',
                                                    'creditstyle-1',
                                                    'coeo',
                                                    'opos',
                                                    'creditstyle',
                                                    'digitaldra',
                                                    'firstlocate',
                                                    'conexus',
                                                    'pdc',
                                                    'digidra',
                                                    'moriarty'
                          )),

     legal_tickets as (select distinct row_number() over (partition by a.number order by t.createdDttm asc) as rn,
                                       a.number,
                                       td.keyPrefix                                                         as ticket_prefix,
                                       t.keyIdentifier                                                      as ticket_identifier,
                                       ts.name                                                              as ticket_step,
                                       t.id                                                                 as ticket_id,
                                       cast(t.createdDttm as date)                                          as ticket_created,
                                       t.status                                                             as ticket_status
                       from `soe_junifer_source_delta.Ticket` as t
                                join `soe_junifer_source_delta.TicketDefinition` as td
                                     on t.ticketDefinitionFk = td.id
                                join `soe_junifer_source_delta.TicketStep` as ts
                                     on t.ticketStepFk = ts.id
                                join `soe_junifer_source_delta.TicketEntity` as te
                                     on te.ticketFk = t.id
                                join `soe_junifer_source_delta.Account` as a
                                     on te.entityId = a.id
                                         and te.entityTblFk = 155

                       where t.status in ('Open', 'Error')
                         and lower(td.keyPrefix) in (
                                                     'legal-cot-forwardingaddressunkown(trace)',
                                                     'legal-lateliveoccunknown',
                                                     'legal-final debt-emailonly',
                                                     'legal-finaldebtchangesupplier',
                                                     'legal-lateliveoccconmd',
                                                     'legal-cot-forwardingaddressconfirmed',
                                                     'predca-new',
                                                     'predca-recall')
                           ),

     legal_dca_tickets as (select l.*
                           from legal_tickets l
                                    left join
                                dca_accounts d
                                on
                                    l.number = d.number
                           where d.number is null
                             and rn = 1),

     accounts_in_review as
         (select distinct a.number
          from `soe_junifer_source_delta.Account` as a
                   inner join `soe_junifer_source_delta.AccountReviewPeriod` as arp
                              on arp.accountFk = a.id
          where a.cancelledDttm is null
            and a.cancelFl = 'N'
            and a.deleteFl = 'N'
            and current_date() between cast(arp.fromDttm as date) and cast(arp.toDttm as date)
            and arp.cancelledDttm is null
            and arp.cancelledUserTblFk is null
            and arp.deleteFl = 'N'
            and (arp.suppressBillingFl = 'Y' or arp.suppressDunningFl = 'Y')),

     overdue_debt as
         (select distinct account_number
          from `soe-prod-data-core-7529.soe_junifer_model.w_aged_debt_breakdown`
          where month_end = last_day(current_date(), month)
            and od_61_to_90_days + od_91_to_120_days + od_121_to_180_days + od_181_to_365_days + od_over_365_days > 0),

     not_active_dd as
         (select a.number
               , pm.status                     as dd_status
               , cast(pm.createdDttm as date)  as dd_created
               , psp.amount                    as dd_amount
               , cast(psp.createdDttm as date) as dd_amount_last_change
               , cast(psp.fromDttm as date)    as dd_fromDttm

          from `soe-prod-data-gentrack.soe_junifer_source_delta.Account` as a
                   left join `soe_junifer_source_delta.PaymentSchedulePeriod` as psp --PSP = DD amount
on psp.accountFk = a.id
    left join `soe-prod-data-gentrack.soe_junifer_source_delta.PaymentMethod` as pm --PM = Shared for both DD and PP
    on pm.accountFk = a.id
    qualify row_number () over (partition by number order by dd_created desc, dd_amount_last_change desc, dd_fromDttm desc ) = 1

    ),


    last_active_date_in_month as
    (
select distinct
    cc.account_number, cc.mpxn, cc.meter_reg, max(Date_Index) as last_day_per_month

from `soe-prod-data-core-7529.soe_junifer_model.w_daily_cost_calculation` as cc

group by 1, 2, 3

    ),
    elec_annual_cost as
    (
select
    lad.account_number, lad.mpxn, lad.meter_reg, (sum(ifnull(eac, 0) * ifnull(unit_rate_pence, 0))
    + sum((Standing_Charge * 365)))
    * 1.05 as estimated_annual_cost

from last_active_date_in_month as lad
    join `soe-prod-data-core-7529.soe_junifer_model.w_daily_cost_calculation` as cc
on cc.Account_Number = lad.account_number
    and cc.Date_Index = lad.last_day_per_month
    and cc.mpxn = lad.mpxn
    and cc.meter_reg = lad.meter_reg

where cc.Fuel_Type = 'Elec'

group by 1, 2, 3
    ),
    gas_annual_cost as
    (
select
    lad.account_number, lad.mpxn, lad.meter_reg, (sum(ifnull(aq, 0) * ifnull(unit_rate_pence, 0))
    + sum((Standing_Charge * 365)))
    * 1.05 as estimated_annual_cost

from last_active_date_in_month as lad
    join `soe-prod-data-core-7529.soe_junifer_model.w_daily_cost_calculation` as cc
on cc.Account_Number = lad.account_number
    and cc.Date_Index = lad.last_day_per_month
    and cc.mpxn = lad.mpxn
    and cc.meter_reg = lad.meter_reg

where cc.Fuel_Type = 'Gas'

group by 1, 2, 3
    ),
    final_data as (
select distinct
    row_number () over (partition by a.number order by 6 DESC) as rn, a.number as account_number, a.id as account_id, det.account_status, case
    when det.Account_Status = 'Active'
    then 'Live'
    ELSE 'Final'
    end as debt_type, cast(a.fromDttm as date) as account_start, cast(a.toDttm as date) as account_to, case
    when aph.Account_end_type = 'Move Out' then 'Change of Tenancy'
    when aph.Account_end_type = 'Changed Supplier' then 'Change of Supplier'
    else 'Other'
    end as Portfolio, det.account_balance as debt_amount, cd.title, cd.forename, cd.surname, cd.date_of_birth, cd.billing_adr_1, cd.billing_adr_2, cd.billing_adr_3, cd.billing_adr_4, cd.billing_adr_5, cd.Billing_Postcode, cd.supply_adr_1, cd.supply_adr_2, cd.supply_adr_3, cd.supply_adr_4, cd.supply_adr_5, cd.Supply_Postcode, cd.number1, cd.number2, cd.number3, cd.email_address, case
    when lower(cd.Forename) like '%occupier%' then true
    when lower(cd.Surname) like '%occupier%' then true
    else false
    end as occupier_flag, ldt.ticket_id, ldt.ticket_prefix, if (cd.psr_flag = 'Y', true, false) as `psr_flag`, cd.supply_end_desc as `supply_end_desc`

from `soe_junifer_source_delta.Account` as a
    join `soe-prod-data-core-7529.soe_junifer_model.w_account_product_history_d` as aph
on aph.Account_Number = a.number
    join `soe-prod-data-core-7529.soe_junifer_model.w_monthly_transaction_summary` as det
    on det.Account_Number = a.number
    join max_month_end as mme
    on mme.Latest_Month = det.Month_end
    join contact_details as cd
    on cd.Account_Number = a.number
    left join payment_plans as pp
    on pp.number = a.number
    left join open_complaints as com
    on com.Junifer_Account = cast(a.number as int64)
    left join et_tickets as ett
    on ett.number = a.number
    left join dispute_tickets as dt
    on dt.number = a.number
    join legal_dca_tickets as ldt
    on ldt.number = a.number
    and ldt.rn = 1
    left join accounts_in_review as air
    on air.number = a.number
    left join overdue_debt as od
    on od.account_number = a.number
    left join not_active_dd as nad
    on nad.number = a.number


where a.cancelledDttm is null
  and a.cancelFl = 'N'
  and a.deleteFl = 'N'
  and (aph.Account_end_type in ('Move Out'
    , 'Changed Supplier')
   OR aph.Account_end_type = 'On Contract')
  and cd.supply_adr_1 is not null
  and pp.number is null
  and com.Junifer_Account is null
  and ett.number is null
  and dt.number is null
  and air.number is null
  and od.account_number is not null
  and (nad.dd_status is null
   or nad.dd_status <> 'Active')

  and det.Account_Balance >= 25
    )
    , latest_junifer_data as
    (
select
    max(act.acceptedDttm) as latest_datetime

from `soe_junifer_source_delta.AccountTransaction` as act
    ), active_debt_recovery as (
select a.id as account_id, a.number as account_number
from `soe_junifer_source_delta.DebtRecoveryPeriod` as drp
    join `soe_junifer_source_delta.Account` as a
on drp.accountFk = a.id
where
    current_date () between cast(drp.fromDttm as date)
  and cast(drp.toDttm as date)
  and drp.stoppedDttm is null
  and drp.stoppedUserTblFk is null)

select FD.account_number as `account_number`,
       FD.account_id     as `account_id`,
       portfolio         as `portfolio`,
       debt_amount       as `debt_amount`,
       account_start     as `supply_start_date`,
       account_to        as `supply_end_date`,
       debt_type         as `final_or_live`,
       occupier_flag     as `occupier_flag`,
       psr_flag          as `psr_flag`,
       supply_end_desc   as `supply_end_desc`
from final_data as fd
         join latest_junifer_data as ljd
              on cast(ljd.latest_datetime as date) > fd.account_start
         left join elec_annual_cost as elec
                   on elec.account_number = fd.account_number
         left join gas_annual_cost as gas
                   on gas.account_number = fd.account_number

where rn = 1
  and fd.account_id not in (select adr.account_id from active_debt_recovery adr)
group by 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
order by 1
limit (_limit_) offset(_offset_)