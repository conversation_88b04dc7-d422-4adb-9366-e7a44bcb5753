# Default values for be-assets.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

## Refer to https://soenergy.atlassian.net/wiki/x/D4AWwQ for use of this values.yaml ##

replicaCount: 2

image:
  repository: be-assets
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: be-assets

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "kubernetes-gcp-sa"

podAnnotations:
  # admission.datadoghq.com/java-lib.version: "v1.29.0"
  ad.datadoghq.com/linkerd-init.logs: >-
    [{
      "source": "linkerd",
      "service": "linkerd-init",
      "log_processing_rules": [
        {
          "type": "exclude_at_match",
          "name": "exclude_non_error_logs",
          "pattern": ".*[Ii][Nn][Ff][Oo].*|.*[Ww][Aa][Rr][Nn].*|.*[Dd][Ee][Bb][Uu][Gg].*|info|warn|debug"
        }
      ]
    }]
  ad.datadoghq.com/linkerd-proxy.logs: >-
    [{
      "source": "linkerd",
      "service": "linkerd-proxy",
      "log_processing_rules": [
        {
          "type": "exclude_at_match",
          "name": "exclude_non_error_logs",
          "pattern": ".*[Ii][Nn][Ff][Oo].*|.*[Ww][Aa][Rr][Nn].*|.*[Dd][Ee][Bb][Uu][Gg].*|info|warn|debug"
        }
      ]
    }]
  ad.datadoghq.com/be-assets.logs: >-
    [{
      "source": "kotlin",
    "service": "be-assets",
      "log_processing_rules": [
        {
          "type": "multi_line",
          "name": "log_start_with_date",
          "pattern": "INFO|DEBUG|ERROR|WARN|TRACE\\s*\\d{4}-[0-9]{2}-[0-9]{2}"
        },
        {
          "type": "exclude_at_match",
          "name": "exclude_error_logs_during_node_scaleup",
          "pattern": "ERROR\\s*\\d{4}-[0-9]{2}-[0-9]{2}\\s+0[5-6]:(00|0[1-9]|1[0-9]|2[0-9]|30):\\d{2}.*(unavailable|UNAVAILABLE).*(staging).*service\\s(unavailable|UNAVAILABLE)(\\n|.)*"
        }
      ]
    }]

podLabels:
  admission.datadoghq.com/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 50051

resources:
  limits:
    cpu: 1300m
    memory: 900Mi
  requests:
    cpu: 500m
    memory: 900Mi


autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

canary:
  enabled: true
  analysis:
    interval: 30s
    threshold: 5
    # step weights
    maxWeight: 50
    stepWeight: 20
    stepWeightPromotion: 100
    metrics:
      requestLatency:
        interval: 1m
        # in milliseconds
        threshold: 5000 
      requestSuccessRate:
        interval: 1m
        # in percent
        threshold: 80
  # canary HPA replicas
  minReplicas: 2
  maxReplicas: 2

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        topologyKey: kubernetes.io/hostname
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - "be-assets"

# healthcheck probes
startupProbe:
  grpc:
    port: 50051
  initialDelaySeconds: 60
  timeoutSeconds: 10
  periodSeconds: 20
  failureThreshold: 5

readinessProbe:
  grpc:
    port: 50051
    service: "readiness"
  initialDelaySeconds: 60
  timeoutSeconds: 10
  periodSeconds: 20

livenessProbe:
  grpc:
    port: 50051
    service: "liveness"
  initialDelaySeconds: 60
  timeoutSeconds: 10
  periodSeconds: 20

staging:
  envVars:
    plainText:
      # deployment
      - name: UPLOAD_OUTBOUND_PHOTOS_URL
        value: "https://myaccount.staging.soenergy.co/readings?openPhotoRequest=true"
      - name: GENERIC_NOVA_METER_PHOTO_HISTORY_MODAL_URL
        value: "https://nova.staging.soenergy.co/customer/{{accountNumber}}/meterpoints?openPhotoHistoryModal=true"
      # microservices
      ## be-ac-esg
      - name: BE_AC_ESG_HOST
        value: be-ac-esg-primary.staging.svc.cluster.local
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_HOST
        value: be-ac-junifer-primary.staging.svc.cluster.local
      ## be-communications
      - name: COMMUNICATIONS_HUB_HOST
        value: be-communications-primary.staging.svc.cluster.local
      ## be-customers
      - name: BE_CUSTOMERS_HOST
        value: be-customers-primary.staging.svc.cluster.local
      ## be-identity
      - name: BE_IDENTITY_HOST
        value: be-identity-primary.staging.svc.cluster.local
      ## be-junifer
      - name: BE_JUNIFER_HOST
        value: be-junifer-primary.staging.svc.cluster.local
      ## freshdesk
      - name: FRESHDESK_HOST
        value: "soenergysandbox.freshdesk.com"
      - name: FRESHDESK_GROUP_ID
        value: "100**********"
      - name: FRESHDESK_SLEEP_MILLIS
        value: "30000"
      - name: FRESHDESK_DELAY_MILLIS
        value: "1"
      - name: FRESHDESK_RATE_LIMIT
        value: "5"
      - name: FRESHDESK_MAX_RETRY
        value: "2"
      - name: FRESHDESK_REQUEST_TIMEOUT
        value: "180000"
      ## kafka
      - name: KAFKA_SCHEMA_REGISTRY_URL
        value: "https://psrc-kk5gg.europe-west3.gcp.confluent.cloud"
      ## AWS Connect
      - name: AWS_PROJECT_ID
        value: "844728591657"
      - name: AWS_CONNECT_CASES_DOMAIN
        value: "affbfa92-650d-453f-9386-471f9e81744b"
      - name: AWS_CONNECT_INSTANCES_ID
        value: "0a808e34-1797-4e32-bc98-96e0bc6a2fd2"
      - name: AWS_CONNECT_CUSTOMER_CARE_QUEUE_ARN
        value: "arn:aws:connect:eu-west-2:844728591657:instance/0a808e34-1797-4e32-bc98-96e0bc6a2fd2/queue/26d232cc-56a6-4a8c-9378-3752f1a49dce"
      - name: AWS_CONNECT_CASE_TEMPLATE_ID
        value: "deb06a0b-a97b-471d-8925-c24b78ca3c14"
      - name: AWS_CONNECT_CUSTOMER_PROFILES_DOMAIN
        value: "so-energy-dev-customer-profiles"
      - name: AWS_CONNECT_CASE_DUE_DATE_FIELD
        value: "e8a7e160-c904-4e53-bffc-7aeca0c54390"
      - name: AWS_CONNECT_CASE_DESCRIPTION_FIELD
        value: "a3f83902-0ed1-46f3-82d8-8e0e48c450e0"
      - name: AWS_CONNECT_CASE_FRESHDESK_ID_FIELD
        value: "e7ad1753-1fc6-4b56-ac97-28bf8216cfd9"
      - name: AWS_CONNECT_CASE_SOURCE_FIELD
        value: "31242fbf-ae5f-4467-b728-8993b152ecf5"
      - name: AWS_CONNECT_ATTACHMENTS_BUCKET
        value: "amazon-connect-dev-bucket-214365"
      - name: AWS_CONNECT_INSTANCE_ALIAS
        value: "soenergydev"
      - name: AWS_CONNECT_HELP_ADDRESS
        value: "<EMAIL>"

production:
  envVars:
    plainText:
      # deployment
      - name: UPLOAD_OUTBOUND_PHOTOS_URL
        value: "https://myaccount.so.energy/readings?openPhotoRequest=true"
      - name: GENERIC_NOVA_METER_PHOTO_HISTORY_MODAL_URL
        value: "https://nova.so.energy/customer/{{accountNumber}}/meterpoints?openPhotoHistoryModal=true"
      # microservices
      ## be-ac-esg
      - name: BE_AC_ESG_HOST
        value: be-ac-esg.production.svc.cluster.local
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_HOST
        value: be-ac-junifer.production.svc.cluster.local
      ## be-communications
      - name: COMMUNICATIONS_HUB_HOST
        value: be-communications.production.svc.cluster.local
      ## be-customers
      - name: BE_CUSTOMERS_HOST
        value: be-customers.production.svc.cluster.local
      ## be-identity
      - name: BE_IDENTITY_HOST
        value: be-identity.production.svc.cluster.local
      ## be-junifer
      - name: BE_JUNIFER_HOST
        value: be-junifer.production.svc.cluster.local
      ## freshdesk
      - name: FRESHDESK_HOST
        value: "soenergy.freshdesk.com"
      - name: FRESHDESK_GROUP_ID
        value: "**********"
      - name: FRESHDESK_SLEEP_MILLIS
        value: "30000"
      - name: FRESHDESK_DELAY_MILLIS
        value: "1"
      - name: FRESHDESK_RATE_LIMIT
        value: "20"
      - name: FRESHDESK_MAX_RETRY
        value: "2"
      - name: FRESHDESK_REQUEST_TIMEOUT
        value: "60000"
      ## kafka
      - name: KAFKA_SCHEMA_REGISTRY_URL
        value: "https://psrc-9zg5y.europe-west3.gcp.confluent.cloud"
      ## AWS connect
      - name: AWS_PROJECT_ID
        value: "301573230590"
      - name: AWS_CONNECT_CASES_DOMAIN
        value: "76b01de6-84c3-4e41-87f4-8e5ed08c69f9"
      - name: AWS_CONNECT_INSTANCES_ID
        value: "0d3c4c70-38e3-4fc7-a378-652fc5fd494a"
      - name: AWS_CONNECT_CUSTOMER_CARE_QUEUE_ARN
        value: "arn:aws:connect:eu-west-2:301573230590:instance/0d3c4c70-38e3-4fc7-a378-652fc5fd494a/queue/6c6a5134-8bb5-4f7d-8361-162b1ace565b"
      - name: AWS_CONNECT_CASE_TEMPLATE_ID
        value: "9be85da9-a903-424d-95e9-b463559a698c"
      - name: AWS_CONNECT_CASE_DUE_DATE_FIELD
        value: "e8a7e160-c904-4e53-bffc-7aeca0c54390"
      - name: AWS_CONNECT_CUSTOMER_PROFILES_DOMAIN
        value: "so-energy-prod-customer-profiles"
      - name: AWS_CONNECT_CASE_DESCRIPTION_FIELD
        value: "261e5cbf-c1db-4498-96f9-e79061ae4b1a"
      - name: AWS_CONNECT_CASE_FRESHDESK_ID_FIELD
        value: "eea436f0-1f65-4503-b19b-368768bafa90"
      - name: AWS_CONNECT_CASE_SOURCE_FIELD
        value: "94cfefef-f927-4624-9f4b-63055c57911e"
      - name: AWS_CONNECT_ATTACHMENTS_BUCKET
        value: "amazon-connect-prod-bucket-214365"
      - name: AWS_CONNECT_INSTANCE_ALIAS
        value: "soenergyprod"
      - name: AWS_CONNECT_HELP_ADDRESS
        value: "<EMAIL>"

global:
  image:
    tag: "default-global-tag"

  environmentName: default-environment-name
  kotlinEnvironmentName: default-kotlin-environment-name

  datadog:
    commitSha: default-commit-sha

  db:
    migrationsEnabled: false

  ondemand:
    enabled: false
    branchName: default-branch-name

  envVars:
    plainText: 
      # infra
      ## open-telemetry
      ## redis
      - name: REDIS_CLUSTER
        value: "false"
      # microservices
      ## be-ac-esg
      - name: BE_AC_ESG_PORT
        value: "50051"
      - name: BE_AC_ESG_TIMEOUT
        value: "180"
      ## database
      - name: DB_MAXIMUM_POOL_SIZE
        value: "20"
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_PORT
        value: "50051"
      - name: BE_AC_JUNIFER_TIMEOUT
        value: "120"
      ## be-assets
      - name: METER_READING_FIRST_READING_PERIOD_DAYS_ELEC
        value: "5"
      - name: METER_READING_FIRST_READING_MIN_PERIOD_DAYS_GAS
        value: "0"
      - name: METER_READING_FIRST_READING_MAX_PERIOD_DAYS_GAS
        value: "10"
      - name: METER_READING_IGNORE_METER_WARNINGS
        value: "true"
      - name: GCP_READINGS_EVIDENCE_FOLDER
        value: "readingsEvidence"
      - name: METER_READ_IMAGE_UPLOAD_EXPIRY
        value: "900"
      - name: METER_READ_IMAGE_GET_EXPIRY
        value: "3600"
      - name: METER_READ_IMAGE_UPLOAD_MAX_SIZE
        value: "20971520"
      - name: GOOGLE_STORAGE_CREDENTIALS_PATH
        value: ""
      - name: GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_MAXIMUM_DELIVERY_ATTEMPTS
        value: "10"
      ## be-communications
      - name: COMMUNICATIONS_HUB_PORT
        value: "50051"
      - name: COMMUNICATIONS_HUB_TIMEOUT
        value: "5"
      ## be-customers
      - name: BE_CUSTOMERS_PORT
        value: "50051"
      - name: BE_CUSTOMERS_TIMEOUT
        value: "10"
      ## be-identity
      - name: BE_IDENTITY_PORT
        value: "50051"
      - name: BE_IDENTITY_TIMEOUT
        value: "10"
      ## be-junifer
      - name: BE_JUNIFER_PORT
        value: "50051"
      - name: BE_JUNIFER_TIMEOUT
        value: "10"
      ## freshdesk
      - name: FRESHDESK_CONNECTION_TIMEOUT
        value: "60000"
      ## outbound photo
      - name: OUTBOUND_PHOTO_MAX_ACTIVITY_PERIOD_DAYS
        value: "7"
      ## kafka
      - name: KAFKA_BOOTSTRAP_SERVER
        value: "pkc-l6wr6.europe-west2.gcp.confluent.cloud:9092"
      - name: KAFKA_DOTDIGITAL_EMAIL_TOPIC_NAME
        value: "dotdigital-emails"
      ## AWS connect
      - name: AWS_CONNECT_TIMEOUT
        value: "30000"
      - name: AWS_SOCKET_TIMEOUT
        value: "60000"
      - name: AWS_CONNECT_MAX_RETRIES
        value: "3"
      - name: AWS_REGION
        value: "eu-west-2"
      - name: ENABLED_SHADOW_TRAFFIC
        value: "false"

    externalSecret:
      # infra
      ## open-telemetry
      - name: OTEL_EXPORTER_OTLP_HEADERS
        gcpSecret: OTEL_EXPORTER_OTLP_HEADERS
      # infra
      ## freshdesk
      - name: FRESHDESK_API_KEY
        gcpSecret: FRESHDESK_API_KEY
      - name: FRESHDESK_PASSWORD
        gcpSecret: FRESHDESK_PASSWORD
      ## kafka
      - name: KAFKA_SCHEMA_REGISTRY_CREDENTIALS
        gcpSecret: KAFKA_SCHEMA_REGISTRY_CREDENTIALS
      - name: KAFKA_JAAS_CONFIG
        gcpSecret: KAFKA_JAAS_CONFIG
      ## AWS
      - name: AWS_ACCESS_KEY
        gcpSecret: AWS_CONNECT_KEY
      - name: AWS_SECRET_ACCESS_KEY
        gcpSecret: AWS_CONNECT_SECRET

db:
  enabled: true
  name: be_assets
  overrideName: ""

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#PubSub-items
pubSub:
  enabled: true
  items:
  - topicName: be-assets-mr-subs
    topicEnvVar: GCP_METER_READINGS_SUBMISSIONS_QUEUE_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-mr-subs-subscription
        subscriptionEnvVar: GCP_METER_READINGS_SUBMISSIONS_QUEUE_SUBSCRIPTION_NAME
        subRetry:
          minimumBackoff: 60s
  - topicName: be-meter-readings-mr-events
    topicEnvVar: GCP_METER_READINGS_EVENTS_QUEUE_TOPIC_NAME
    subscriptions: {}
  - topicName: be-assets-mr-sub-reqs
    topicEnvVar: GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-mr-sub-reqs-subscription
        subscriptionEnvVar: GCP_BE_ASSETS_METER_READ_SUBMISSION_REQUESTS_QUEUE_SUBSCRIPTION_NAME
  - topicName: be-assets-queue-persisted-mr-sub-reqs
    topicEnvVar: GCP_BE_ASSETS_QUEUE_PERSISTED_METER_READ_SUBMISSION_REQUESTS_QUEUE_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-queue-persisted-mr-sub-reqs-subscription
        subscriptionEnvVar: GCP_BE_ASSETS_QUEUE_PERSISTED_METER_READ_SUBMISSION_REQUESTS_QUEUE_SUBSCRIPTION_NAME
  - topicName: be-assets-update-contacted-reads-status
    topicEnvVar: GCP_BE_ASSETS_UPDATE_CONTACTED_READS_STATUS_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-update-contacted-reads-status-subs
        subscriptionEnvVar: GCP_BE_ASSETS_UPDATE_CONTACTED_READS_STATUS_SUBSCRIPTION_NAME
  - topicName: be-assets-automate-close-outbound-req
    dlqTopicName: be-assets-automate-close-outbound-req-dlq
    topicEnvVar: GCP_BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-automate-close-outbound-req-subs
        subscriptionEnvVar: GCP_BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ_SUBSCRIPTION_NAME
  - topicName: be-assets-photo-evidence-freshdesk-ticket
    topicEnvVar: GCP_BE_ASSETS_PHOTO_EVIDENCE_FRESHDESK_TICKET_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-photo-evidence-freshdesk-ticket-subs
        subscriptionEnvVar: GCP_BE_ASSETS_PHOTO_EVIDENCE_FRESHDESK_TICKET_SUBSCRIPTION_NAME
        ackDeadline:
          seconds: 180
        subRetry:
          minimumBackoff: 60s
  - topicName: be-assets-meter-reading-status-updates
    dlqTopicName: be-assets-meter-reading-status-updates-dlq
    topicEnvVar: GCP_BE_ASSETS_QUEUE_METER_READING_STATUS_UPDATES_QUEUE_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-meter-reading-status-updates-subs
        subscriptionEnvVar: GCP_BE_ASSETS_METER_READ_STATUS_UPDATE_QUEUE_TOPIC_NAME
        ackDeadline:
          seconds: 120
        subRetry:
          minimumBackoff: 40s
  - topicName: be-assets-outbound-photo-requests
    topicEnvVar: GCP_BE_ASSETS_OUTBOUND_PHOTO_REQUESTS_TOPIC_NAME
    subscriptions:
      - subscriptionName: be-assets-outbound-photo-requests-subs
        subscriptionEnvVar: GCP_BE_ASSETS_OUTBOUND_PHOTO_REQUESTS_SUBSCRIPTION_NAME
        ackDeadline:
          seconds: 180
        subRetry:
          minimumBackoff: 60s

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#GCP-Cloud-Storage-(buckets)-items
gcpBucket:
  enabled: true
  items:
  - bucketName: be-assets-reading-photo-evidence
    bucketEnvVar: READINGS_PHOTO_EVIDENCE_BUCKET
    config:
      gcpRegion: europe-west2
      cors:
        method:
          - GET
          - DELETE
          - PUT
        origin:
          - https://www.so.energy
          - https://www.sandbox.soenergy.co
          - https://www.staging.soenergy.co
          - https://so.energy
          - https://sandbox.soenergy.co
          - https://staging.soenergy.co
          - https://*preview.soenergy.co
        responseHeader:
          - Content-Type
          - Authorization
          - Access-Control-Allow-Headers
          - Access-Control-Allow-Origin
          - Access-Control-Allow-Methods
          - X-Requested-With
          - Cache-Control

  - bucketName: be-assets-tmp-reading-photo-evidence
    bucketEnvVar: READINGS_TMP_PHOTO_EVIDENCE_BUCKET
    config:
      gcpRegion: europe-west2
      cors:
        method:
          - GET
          - DELETE
          - PUT
        origin:
          - https://www.so.energy
          - https://myaccount.so.energy
          - https://myaccount.staging.soenergy.co
          - https://myaccount.sandbox.soenergy.co
          - https://www.sandbox.soenergy.co
          - https://www.staging.soenergy.co
          - https://so.energy
          - https://sandbox.soenergy.co
          - https://staging.soenergy.co
          - https://*preview.soenergy.co
          - https://nova.so.energy
          - https://nova.sandbox.soenergy.co
          - https://nova.staging.soenergy.co
        responseHeader:
          - Content-Type
          - Authorization
          - Access-Control-Allow-Headers
          - Access-Control-Allow-Origin
          - Access-Control-Allow-Methods
          - X-Requested-With
          - Cache-Control

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#GCP-Cloud-Scheduler
gcpCloudScheduler:
  enabled: true
  items:
  - scheduleName: be-assets-send-reminders
    scheduleEnvVar: BE_ASSETS_SEND_REMINDERS_SCHEDULE
    config:
      description: "This schedule sends reminders to the pubsub topic at 08:30 and 17:30 daily."
      region: europe-west2
      schedule: "30 8,17 * * *"
      topicName: "be-assets-send-reminders"
      attributes:
        event_type: "CREATED"
        reminder_type: "METER_READ"
  - scheduleName: be-assets-update-contacted-reads-status
    scheduleEnvVar: BE_ASSETS_UPDATE_CONTACTED_READS_STATUS
    config:
      description: "This schedule updates the contacted reads status if a new read is successful to the pubsub topic at 00:30"
      region: europe-west2
      schedule: "30 00 * * *"
      topicName: "be-assets-update-contacted-reads-status"
  - scheduleName: be-assets-automate-close-outbound-req
    scheduleEnvVar: BE_ASSETS_AUTOMATE_CLOSE_OUTBOUND_REQ
    config:
      description: "This schedule automatically close outbound requests to the pubsub topic at 01:00 and 13:00 daily"
      region: europe-west2
      schedule: "00 1,13 * * *"
      topicName: "be-assets-automate-close-outbound-req"
      attributes:
        event_type: "CREATED"
#   this scheduled job seems to cause some issue as messages published by it are not processed correctly
#  - scheduleName: be-assets-update-meter-reading-status
#    scheduleEnvVar: BE_ASSETS_UPDATE_METER_READING_STATUS
#    config:
#      description: "This schedule updates the status of meter readings in the database if it has changed to a final status in Junifer"
#      region: europe-west2
#      schedule: "*/5 * * * *"
#      topicName: "be-assets-meter-reading-status-updates"
#      attributes:
#        event_type: "UPDATED"

