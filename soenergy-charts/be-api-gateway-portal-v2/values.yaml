# Default values for be-api-gateway-portal-v2.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

## Refer to https://soenergy.atlassian.net/wiki/x/D4AWwQ for use of this values.yaml ##
replicaCount: 1

image:
  repository: be-api-gateway-portal-v2
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: be-api-gateway-portal-v2

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "kubernetes-gcp-sa"

podAnnotations:
  ad.datadoghq.com/linkerd-init.logs: >-
    [{
      "source": "linkerd",
      "service": "linkerd-init",
      "log_processing_rules": [
        {
          "type": "exclude_at_match",
          "name": "exclude_non_error_logs",
          "pattern": ".*[Ii][Nn][Ff][Oo].*|.*[Ww][Aa][Rr][Nn].*|.*[Dd][Ee][Bb][Uu][Gg].*|info|warn|debug"
        }
      ]
    }]
  ad.datadoghq.com/linkerd-proxy.logs: >-
    [{
      "source": "linkerd",
      "service": "linkerd-proxy",
      "log_processing_rules": [
        {
          "type": "exclude_at_match",
          "name": "exclude_non_error_logs",
          "pattern": ".*[Ii][Nn][Ff][Oo].*|.*[Ww][Aa][Rr][Nn].*|.*[Dd][Ee][Bb][Uu][Gg].*|info|warn|debug"
        }
      ]
    }]
  ad.datadoghq.com/be-api-gateway-portal-v2.logs: >-
    [{
      "source": "kotlin",
      "service": "be-api-gateway-portal-v2",
      "log_processing_rules": [
        {
          "type": "exclude_at_match",
          "name": "exclude_probe_logs",
          "pattern": ".*\/actuator\/health\/.*"
        },
        {
          "type": "multi_line",
          "name": "log_start_with_date",
          "pattern": "INFO|DEBUG|ERROR|WARN|TRACE\\s*\\d{4}-[0-9]{2}-[0-9]{2}"
        }
      ]
    }]

podLabels:
  admission.datadoghq.com/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 50051

resources:
  limits:
    cpu: 2000m
    memory: 1000Mi
  requests:
    cpu: 1200m
    memory: 1000Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

canary:
  enabled: true
  analysis:
    interval: 30s
    threshold: 5
    # step weights
    maxWeight: 50
    stepWeight: 20
    stepWeightPromotion: 100
    metrics:
      requestLatency:
        interval: 1m
        # in milliseconds
        threshold: 5000 
      requestSuccessRate:
        interval: 1m
        # in percent
        threshold: 80
  # canary HPA replicas
  minReplicas: 5
  maxReplicas: 5

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        topologyKey: kubernetes.io/hostname
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - "be-api-gateway-portal-v2"

# healthcheck probes
startupProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 50051
  initialDelaySeconds: 60
  timeoutSeconds: 15
  periodSeconds: 10
  failureThreshold: 5

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 50051
  initialDelaySeconds: 60
  timeoutSeconds: 15
  periodSeconds: 10

livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 50051
  initialDelaySeconds: 60
  timeoutSeconds: 15
  periodSeconds: 10

ingressRoute:
  enabled: true
  entryPoints:
    - websecure
  tlsSecretName: so-energy-wildcard-cert

staging:
  envVars:
    plainText:
      # deployment
      - name: WEB_ACCOUNT_FRONTEND_HOST
        value: "https://myaccount.staging.soenergy.co"
      - name: DOMAIN
        value: "account.soenergy.co"
      - name: SMART-METER-BOOKING-PORTAL-URL
        value: "https://smbp-be.staging.soenergy.co/smart_booking/api/v1/"
      # infra
      ## freshdesk
      - name: FRESHDESK_API_HOST
        value: "soenergysandbox.freshdesk.com"
      - name: FRESHDESK_GROUP_ID
        value: "*************"
      # microservices
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_HOST
        value: be-ac-junifer-primary.staging.svc.cluster.local
      ## be-assets
      - name: BE_ASSETS_HOST
        value: be-assets-primary.staging.svc.cluster.local
      ## be-financials
      - name: BE_FINANCIALS_HOST
        value: be-financials-primary.staging.svc.cluster.local
      ## be-communications
      - name: BE_COMMUNICATIONS_HOST
        value: be-communications-primary.staging.svc.cluster.local
      ## be-customers
      - name: BE_CUSTOMERS_HOST
        value: be-customers-primary.staging.svc.cluster.local
      ## be-identity
      - name: BE_IDENTITY_HOST
        value: be-identity-primary.staging.svc.cluster.local
      ## be-junifer
      - name: BE_JUNIFER_HOST
        value: be-junifer-primary.staging.svc.cluster.local
      ## be-products
      - name: BE_PRODUCTS_HOST
        value: be-products-primary.staging.svc.cluster.local
      ## be-tickets
      - name: BE_TICKETS_HOST
        value: be-tickets-primary.staging.svc.cluster.local
      ## be-comms-hub
      - name: BE_COMMS_HUB_HOST
        value: be-comms-hub-primary.staging.svc.cluster.local
    externalSecret:
      ## Solar Recaptcha Key
      - name: RECAPTCHA_SECRET_KEY
        gcpSecret: RECAPTCHA_SECRET_KEY_STAGING
  ingressRoute:
    publicHostname: portal-api-gateway-v2.staging.soenergy.co
    corsRules:
      - "https://www.staging.soenergy.co"
      - "https://smbp.staging.soenergy.co"
      - "https://whd.staging.soenergy.co"
      - "https://solar.staging.soenergy.co"
      - "https://myaccount.staging.soenergy.co"
      - "capacitor://app.staging.soenergy.co"
      - "http://localhost:3000"
      - "http://localhost:8030"
      - "http://localhost:8040"
      - "http://localhost:8050"
      - "http://localhost:8060"
      - "http://localhost:8070"
      - "http://localhost:8080"
      - "http://localhost:8081"
      - "http://localhost:8082"
      - "http://localhost:8090"
    corsRulesRegex:
      - "https://(.*?)soenergy.co"
      - "^https://fe-myaccount-(.*?)-soenergy.vercel.app"

production:
  envVars:
    plainText:
      # deployment
      - name: WEB_ACCOUNT_FRONTEND_HOST
        value: "https://myaccount.so.energy"
      - name: DOMAIN
        value: "account.so.energy"
      - name: SMART-METER-BOOKING-PORTAL-URL
        value: "https://smbp-be.so.energy/smart_booking/api/v1/"
      # infra
      ## freshdesk
      - name: FRESHDESK_API_HOST
        value: soenergy.freshdesk.com
      - name: FRESHDESK_GROUP_ID
        value: "**********"
      # microservices
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_HOST
        value: be-ac-junifer.production.svc.cluster.local
      ## be-assets
      - name: BE_ASSETS_HOST
        value: be-assets.production.svc.cluster.local
      ## be-financials
      - name: BE_FINANCIALS_HOST
        value: be-financials.production.svc.cluster.local
      ## be-communications
      - name: BE_COMMUNICATIONS_HOST
        value: be-communications.production.svc.cluster.local
      ## be-customers
      - name: BE_CUSTOMERS_HOST
        value: be-customers.production.svc.cluster.local
      ## be-identity
      - name: BE_IDENTITY_HOST
        value: be-identity.production.svc.cluster.local
      ## be-junifer
      - name: BE_JUNIFER_HOST
        value: be-junifer.production.svc.cluster.local
      ## be-products
      - name: BE_PRODUCTS_HOST
        value: be-products.production.svc.cluster.local
      ## be-tickets
      - name: BE_TICKETS_HOST
        value: be-tickets.production.svc.cluster.local
      ## be-comms-hub
      - name: BE_COMMS_HUB_HOST
        value: be-comms-hub.production.svc.cluster.local
    externalSecret:
      ## Solar Recaptcha Key
      - name: RECAPTCHA_SECRET_KEY
        gcpSecret: RECAPTCHA_SECRET_KEY
  ingressRoute:
    publicHostname: portal-api-gateway-v2.so.energy
    corsRules:
      - "https://so.energy"
      - "https://www.so.energy"
      - "https://myaccount.so.energy"
      - "https://solar.so.energy"
      - "https://nova.so.energy"
      - "https://smbp.services.so.energy"
      - "https://whd.services.so.energy"
      - "https://soenergy.freshdesk.com"
      - "https://help.so.energy"
      - "capacitor://app.so.energy"
    corsRulesRegex:
      - "https://(.*?)so.energy"
      - "^https://.*preview.soenergy.co"

global:
  image:
    tag: "default-global-tag"

  ondemand:
    enabled: false
    branchName: default-branch-name
  
  environmentName: default-environment-name
  kotlinEnvironmentName: default-kotlin-environment-name

  datadog:
    commitSha: default-commit-sha
  
  envVars:
    plainText:
      # deployment
      - name: CAPTCHA_VERIFY_URL
        value: https://www.google.com/recaptcha/api/siteverify
      - name: MUG_PRIORITY_LISTING_IDS
        value: "2,4,7,11,15,1,18,14,16"
      - name: SHADOW_TRAFFIC_EXTERNAL_CLIENTS_ENABLED
        value: "true"
      - name: DISABLED_FOR_JUNIFER_ENTITY_MAPPER
        value: "false"
      - name: ENABLED_SHADOW_TRAFFIC
        value: "false"
      # infra
      ## open-telemetry
      ## redis
      - name: REDIS_CLUSTER
        value: "false"
      # microservices
      ## be-ac-junifer
      - name: BE_AC_JUNIFER_PORT
        value: "50051"
      - name: BE_AC_JUNIFER_TIMEOUT
        value: "120"
      ## be-assets
      - name: BE_ASSETS_PORT
        value: "50051"
      - name: BE_ASSETS_TIMEOUT
        value: "150"
      ## be-financials
      - name: BE_FINANCIALS_PORT
        value: "50051"
      - name: BE_FINANCIALS_TIMEOUT
        value: "120"
      ## be-communications
      - name: BE_COMMUNICATIONS_PORT
        value: "50051"
      - name: BE_COMMUNICATIONS_TIMEOUT
        value: "10"
      ## be-customers
      - name: BE_CUSTOMERS_PORT
        value: "50051"
      - name: BE_CUSTOMERS_TIMEOUT
        value: "120"
      ## be-identity
      - name: BE_IDENTITY_PORT
        value: "50051"
      - name: BE_IDENTITY_TIMEOUT
        value: "10"
      - name: BE_IDENTITY_PROTOCOL
        value: "http"
      - name: BE_IDENTITY_RESET_PASSWORD_ENDPOINT
        value: "/users/reset-password"
      - name: BE_IDENTITY_REDEEM_PASSWORD_TOKEN_ENDPOINT
        value: "/users/redeem-password-token"
      - name: BE_IDENTITY_REFRESH_ENDPOINT
        value: "/tokens/refresh"
      ## be-junifer
      - name: BE_JUNIFER_PORT
        value: "50051"
      ## be-products
      - name: BE_PRODUCTS_PORT
        value: "50051"
      - name: BE_PRODUCTS_TIMEOUT
        value: "10"
      ## be-tickets
      - name: BE_TICKETS_PORT
        value: "50051"
      - name: BE_TICKETS_CALL_TIMEOUT
        value: "300"
      ## be-comms-hub
      - name: BE_COMMS_HUB_PORT
        value: "50051"
      - name: BE_COMMS_HUB_TIMEOUT
        value: "10"
      ##
      - name: BE_API_GATEWAY_PORTAL_V2_MAX_REQUEST_HEADER_SIZE
        value: "12000"
      - name: ACCEPTED_PAYMENT_REDIRECT_DOMAINS
        value: "so.energy,soenergy.co"
      ## Caches
      - name: ACCOUNT_PREFERENCE_CACHE_EXPIRY
        value: "300"
    externalSecret:
      # infra
      ## bugsnag
      - name: BE_API_GATEWAY_PORTAL_BUGSNAG_KEY
        gcpSecret: BE_API_GATEWAY_PORTAL_BUGSNAG_KEY
      ## freshdesk
      - name: FRESHDESK_API_KEY
        gcpSecret: FRESHDESK_API_KEY
      ## open-telemetry
      - name: OTEL_EXPORTER_OTLP_HEADERS
        gcpSecret: OTEL_EXPORTER_OTLP_HEADERS
      ## SMBP
      - name: SMBP_API_KEY
        gcpSecret: SMBP_API_KEY

db:
  enabled: false

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#PubSub-items
pubSub:
  enabled: false

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#GCP-Cloud-Storage-(buckets)-items
gcpBucket:
  enabled: false

# Refer to https://soenergy.atlassian.net/wiki/spaces/SoTech/pages/**********/BE-MICROSERVICES+Usage+Guide#GCP-Cloud-Scheduler
gcpCloudScheduler:
  enabled: false
